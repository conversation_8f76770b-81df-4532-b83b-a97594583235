# Augment 项目规则配置文件
# 金融科技业务部后台管理系统 (bmp-mgmt.dianhua.cn)

## 基本交流规则
- 使用中文和我对话

## 项目技术栈规范
### PHP & ThinkPHP 框架规范
- 项目基于 ThinkPHP 3.2.3 框架开发
- PHP 版本要求 >= 5.3.0
- 遵循 ThinkPHP MVC 架构模式：Model-View-Controller
- 所有控制器必须继承 Common\Controller\BaseController
- 使用命名空间，遵循 PSR-4 自动加载规范

### 代码风格规范
- PHP 代码使用 <?php 开始标签，不使用短标签
- 类名使用 PascalCase（大驼峰）命名：如 BaseController、UserModel
- 方法名使用 camelCase（小驼峰）命名：如 getUserInfo()、checkConfig()
- 常量使用全大写加下划线：如 APP_DEBUG、DB_TYPE
- 变量名使用下划线命名：如 $user_info、$config_mysql
- 数组使用方括号语法：[] 而不是 array()（新代码）

### 文件组织规范
- 控制器文件放在各模块的 Controller 目录下，以 .class.php 结尾
- 模型文件放在各模块的 Model 目录下，以 .class.php 结尾
- 视图文件放在各模块的 View 目录下，使用 .html 扩展名
- 配置文件放在各模块的 Conf 目录下，使用 .php 扩展名
- 公共文件放在 Application/Common 目录下

### 数据库规范
- 主数据库：MySQL，表前缀为 crs_
- 支持 MongoDB 用于特定业务场景
- 数据库配置按环境分离：dev.php、prod.php、test.php 等
- 使用 ThinkPHP 的 ORM 进行数据库操作
- 敏感配置信息（密码、密钥）不应硬编码在代码中

### 前端技术栈规范
- 使用 Bootstrap 3.x 作为 CSS 框架
- JavaScript 库：jQuery、Vue.js、ECharts、LayUI
- 静态资源统一放在 statics 目录下
- CSS 文件放在 statics/css/
- JavaScript 文件放在 statics/js/
- 图片资源放在 statics/images/

### 模块化开发规范
- 系统按业务模块划分：Home、Api、System、Task、Account、Stat 等
- 每个模块独立配置，可单独部署
- 模块间通信通过 API 接口或公共服务
- 新增模块需在 index.php 的 MODULE_ALLOW_LIST 中注册

### 安全规范
- 所有用户输入必须进行验证和过滤
- 使用 session 进行用户认证，session 存储在数据库中
- 密码使用加盐哈希存储，盐值配置在 USER_PWD_SALT
- API 接口需要进行权限验证
- 敏感操作需要记录日志

### 性能优化规范
- 启用 ThinkPHP 缓存机制
- 数据库查询使用索引优化
- 静态资源使用 CDN 加速
- 大数据量查询使用分页处理
- 定期清理缓存和日志文件

### 日志和调试规范
- 开发环境开启 APP_DEBUG 模式
- 生产环境关闭调试模式，启用错误日志
- 使用 addBreakPoint() 函数进行性能监控
- 日志文件按日期分目录存储在 cache/ 目录下
- 重要业务操作必须记录操作日志

### API 开发规范
- API 接口统一返回 JSON 格式
- 使用 HTTP 状态码表示请求结果
- API 版本控制通过 URL 路径或请求头实现
- 接口文档使用 Markdown 格式，存放在 doc/ 目录

### 测试规范
- 编写单元测试覆盖核心业务逻辑
- 使用 PHPUnit 进行单元测试
- API 接口需要编写集成测试
- 数据库操作需要编写数据库测试

### 部署和运维规范
- 使用 Composer 管理 PHP 依赖
- 配置文件按环境分离，不提交敏感信息到版本控制
- 使用 Git 进行版本控制，遵循 Git Flow 工作流
- 生产环境部署前必须经过测试环境验证

### 代码注释规范
- 类和方法必须添加 PHPDoc 注释
- 注释使用中文，描述清晰准确
- 复杂业务逻辑添加行内注释说明
- 配置文件添加配置项说明注释

### 错误处理规范
- 使用 try-catch 处理可能的异常
- 自定义异常类继承 \Exception
- 错误信息对用户友好，对开发者详细
- 生产环境不暴露敏感的错误信息

### 代码审查规范
- 新功能开发必须经过代码审查
- 遵循单一职责原则，保持方法简洁
- 避免重复代码，提取公共方法
- 保持代码可读性和可维护性
