<?php
return [
    // 邦秒配数据库
    'DB_MATCHING' => [
        'DB_TYPE' => 'mysql',
        'DB_USER' => 'finance_backend',
        'DB_PREFIX' => '',
        'DB_PWD' => 'Yulore@2018Qaz',
        'DB_HOST' => '************',
        'DB_PORT' => '6021',
        'DB_NAME' => 'itag_apis',
    ],
    // 邦秒爬的数据库
    'DB_CRAWLER' => [
        'DB_TYPE' => 'mysql',
        'DB_HOST' => '************',
        'DB_PORT' => '6012',
        'DB_NAME' => 'crs-api',
        'DB_USER' => 'crs-api',
        'DB_PWD' => 'YulorecomCRS',
        'DB_PREFIX' => ''
    ],

    // 催收分数据库
    'DUNNING_MONGO' => [
        'DB_TYPE' => 'mongo',
        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST' => '*************,*************', // 数据库服务器地址
        'DB_NAME' => 'admin',                     // 数据库名称
        'DB_USER' => 'admin,admin',              // 数据库用户名
        'DB_PWD' => 'yulorei7,yulorei7',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
    ],

    // 客户体系数据库
    'DB_FINANCE' => [
        'DB_TYPE' => 'mysql',
        'DB_HOST' => '************',
        'DB_PORT' => '6012',
        'DB_NAME' => 'yulore_finance',
        'DB_USER' => 'finance_yulore',
        'DB_PWD' => 'finance20180510',
        'DB_PREFIX' => '',
    ],
];