<?php

return [
    // 邦秒配数据库
    'DB_MATCHING' => [
        'DB_TYPE' => 'mysql',
        'DB_USER' => 'crs',
        'DB_PREFIX' => '',
        'DB_PWD' => 'crs@2017qwe',
        'DB_HOST' => '*************',
        'DB_PORT' => '3306',
        'DB_NAME' => 'crs-api',
    ],

    // 邦秒爬的数据库
    'DB_CRAWLER' => [
        'DB_TYPE' => 'mysql',
        'DB_HOST' => '*************',
        'DB_PORT' => '6020',
        'DB_NAME' => 'crs',
        'DB_USER' => 'dev',
        'DB_PWD' => 'yulorei7',
        'DB_PREFIX' => ''
    ],

    // 催收分数据库
    'DUNNING_MONGO' => [
        'DB_TYPE' => 'mongo',
        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST' => '*************,*************', // 数据库服务器地址
        'DB_PORT' => '27017,27017',
        'DB_NAME' => 'crs',                     // 数据库名称
        'DB_USER' => 'crs_w,crs_w',              // 数据库用户名
        'DB_PWD' => 'dsddfdf,dsddfdf',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
        'DB_CHARSET' => 'utf8',
        'DB_MASTER_NUM' => 1,
    ],

    // 客户体系数据库
    'DB_FINANCE' => [
        'DB_TYPE' => 'mysql',
        'DB_HOST' => '*************',
        'DB_PORT' => '3306',
        'DB_NAME' => 'yulore_finance',
        'DB_USER' => 'dev',
        'DB_PWD' => 'Yulore@2019',
        'DB_PREFIX' => ''
    ],
];