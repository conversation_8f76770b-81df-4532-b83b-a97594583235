<?php

namespace Api\Repositories;

use Common\Common\CurlTrait;
use Common\Common\HandlerLog;
use Common\Model\WechatWarningModel;
use Think\Exception;

class BackendWechatManageRepository extends BaseRepository
{
    use CurlTrait;

    /*
     * 更新之前的微信预警配置
     * */
    private $wechat_warning_before_update;

    /*
     * 更新之后的微信预警配置
     * */
    private $wechat_warning_after_update;

    /*
     * create的时候 from要传递的必选字段
     * */
    private $list_required_create_field = [
        'name' => '名称',
        'slug' => 'Slug',
        'agent_id' => 'AgentId',
        'corp_id' => '企业ID',
        'secret' => 'Secret',
        'itag_id' => '标签ID',
        'report_line' => '配置参数',
    ];

    /*
    * update的时候 from要传递的必选字段
    * */
    private $list_required_update_field = [
        'id' => '编辑配置的ID',
        'corp_id' => '企业ID',
        'name' => '名称',
        'slug' => 'Slug',
        'agent_id' => 'AgentId',
        'secret' => 'Secret',
        'itag_id' => '标签ID',
        'report_line' => '配置参数',
    ];

    /*
     * create的时候 form需要传递的所有字段
     * */
    private $list_create_field = [
        'name',
        'slug',
        'agent_id',
        'secret',
        'itag_id',
        'report_line',
        'remark',
        'corp_id'
    ];

    /*
    * update的时候 form需要传递的所有字段
    * */
    private $list_update_field = [
        'name',
        'slug',
        'agent_id',
        'secret',
        'itag_id',
        'report_line',
        'remark',
        'corp_id'
    ];


    /**
     * 编辑模式
     * @throws \Exception
     */
    public function update()
    {
        // 限定访问方法
        $this->limitMethodPost();

        // 检查参数
        $this->validateParamsForUpdate();

        // 更新
        $this->updateDo();
    }

    /**
     * 更新
     * @throws \Exception
     */
    private function updateDo()
    {
        // 参数
        $params = $this->genParamsForUpdate();

        // 条件
        $where = $this->genConditionForUpdate();

        // 新增
        $this->justUpdate($where, $params);

        // 日志
        $this->logUpdate();
    }

    /**
     * 日志
     * @throws \Exception
     */
    protected function logUpdate()
    {
        $wechat_warning_after_update = $this->wechat_warning_after_update;
        $wechat_warning_before_update = $this->wechat_warning_before_update;

        $log_info = [
            'handle_type' => 'update',
            'description' => '更新微信预警配置',
            'content' => compact('wechat_warning_after_update', 'wechat_warning_before_update'),
            'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: 'system',
            'handle_result' => '1'
        ];
        HandlerLog::log($log_info);
    }

    /**
     * 为编辑生成条件
     * @return array
     */
    private function genConditionForUpdate()
    {
        $request_body = $this->postParams();
        $id = $request_body['id'];
        return compact('id');
    }

    /**
     * 更新
     * @param array $where
     * @param array $params
     * @throws \Exception
     */
    private function justUpdate(array $where, array $params)
    {
        // 更新之前微信预警配置
        $this->wechat_warning_before_update = $this->getOneWechatByCondition($where);

        $response = (new WechatWarningModel())->where($where)
            ->save($params);

        if ($response === false) {
            throw new \Exception('更新失败， 请重新提交');
        }

        // 更新之后的微信预警配置
        $this->wechat_warning_after_update = $this->getOneWechatByCondition($where);
    }

    /**
     * 生成参数
     * @throws \Exception
     * @return array
     */
    private function genParamsForUpdate()
    {
        // 处理form传递的参数
        $params_form = $this->genFormParamsForUpdate();

        // 附加其他参数
        $params_other = $this->genOtherParamsForUpdate();
        return array_merge($params_other, $params_form);
    }


    /**
     * 附加其他参数
     * @return array
     */
    private function genOtherParamsForUpdate()
    {
        $updated_at = time();
        $creator = session(C('LOGIN_SESSION_NAME'));
        return compact('updated_at', 'creator');
    }


    /**
     * 处理form传递的参数
     * @return array
     */
    private function genFormParamsForUpdate()
    {
        $request_body = $this->genParamsForPost();
        return array_reduce($this->list_update_field, function ($carry, $field) use ($request_body) {
            // 传递了则获取 否则‘’
            $carry[$field] = array_key_exists($field, $request_body) ? trim($request_body[$field]) : '';
            return $carry;
        }, []);
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    private function validateParamsForUpdate()
    {
        // 基本检查
        $this->validateBaseParamsForUpdate();

        // 检查配置参数是否是合法的json
        $this->validateReportLineForUpdate();

        // 检查slug的唯一性
        $this->validateTheUniqueSlugForUpdate();
    }

    /**
     * 检查slug的唯一性
     * @throws \Exception
     */
    private function validateTheUniqueSlugForUpdate()
    {
        // 条件
        $slug = $this->postKey('slug', '', 'trim');
        $id = $this->postKey('id', '', 'trim');
        $id = ['NEQ', $id];

        // 是否已经被占用
        $already_exists = $this->getWechatByCondition(compact('id', 'slug'));
        if ($already_exists) {
            throw new \Exception('抱歉，slug已经被占用,请使用其他的slug');
        }

    }

    /**
     * 是否已经被占用
     * @throws \Exception
     */
    private function validateTheUniqueSlugForCreate()
    {
        $slug = $this->postKey('slug', '', 'trim');
        $already_exists = $this->getWechatByCondition(compact('slug'));
        if ($already_exists) {
            throw new \Exception('抱歉，slug已经被占用,请使用其他的slug');
        }
    }

    /**
     * 检查配置参数是否是合法的json
     * @throws \Exception
     */
    private function validateReportLineForUpdate()
    {
        $request_body = $this->genParamsForPost();

        // 是否存在 && 是否为空 已经在基本检测中测试过了
        if (json_validate($request_body['report_line']) === false) {
            throw new \Exception('请输入合法的合法的配置参数 : JSON类型');
        }
    }

    /**
     * 基本检查
     * @throws \Exception
     */
    private function validateBaseParamsForUpdate()
    {
        $request_body = $this->genParamsForPost();

        // 检查必填参数是否合法
        array_walk($this->list_required_update_field, function ($tag, $field) use ($request_body) {
            // 不存在或者为空则弹出错误
            if (!array_key_exists($field, $request_body) || !trim($request_body[$field])) {
                throw new \Exception('请输入' . $tag);
            }
        });
    }

    /**
     * 新增微信报警配置
     * @throws \Exception
     */
    public function create()
    {
        // 限定方法
        $this->limitMethodPOST();

        // 检查参数
        $this->validateParamsForCreate();

        // 新增
        $this->createDo();
    }

    /**
     * 新增
     * @throws \Exception
     *
     */
    private function createDo()
    {
        // 参数
        $params = $this->genParamsForCreate();

        // 新增
        $insert_id = $this->justAdd($params);

        // 记录新增日志
        $this->logInsert($insert_id);
    }

    /**
     * @param int $id 新增ID
     * @throws \Exception
     */
    private function logInsert($id)
    {
        $insert_one = $this->getOneWechatByCondition(compact('id'));

        $log_info = [
            'handle_type' => 'create',
            'description' => '新增微信预警配置',
            'content' => compact('insert_one'),
            'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: 'system',
            'handle_result' => '1'
        ];
        HandlerLog::log($log_info);
    }

    /**
     * 根据条件获取微信列表
     * @param array $where
     * @return array
     */
    private function getOneWechatByCondition(array $where)
    {
        return (new WechatWarningModel())->where($where)
            ->find();
    }

    /**
     * 新增
     * @param array $params
     * @throws \Exception
     * @return integer
     */
    private function justAdd(array $params)
    {
        $insert_id = (new WechatWarningModel())->add($params);
        if ($insert_id === false) {
            throw new \Exception('mysql 入库失败，请再次提交数据');
        }
        return $insert_id;
    }

    /**
     * 生成参数
     * @throws \Exception
     * @return array
     */
    private function genParamsForCreate()
    {
        // 处理form传递的参数
        $params_form = $this->genFormParamsForCreate();

        // 附加其他参数
        $params_other = $this->genOtherParamsForCreate();
        return array_merge($params_other, $params_form);
    }

    /**
     * 附加其他参数
     * @return array
     */
    private function genOtherParamsForCreate()
    {
        $created_at = $updated_at = time();
        $creator = session(C('LOGIN_SESSION_NAME'));
        return compact('created_at', 'updated_at', 'creator');
    }

    /**
     * 处理form传递的参数
     * @return array
     */
    private function genFormParamsForCreate()
    {
        $request_body = $this->genParamsForPost();
        return array_reduce($this->list_create_field, function ($carry, $field) use ($request_body) {
            // 传递了则获取 否则‘’
            $carry[$field] = array_key_exists($field, $request_body) ? trim($request_body[$field]) : '';
            return $carry;
        }, []);
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    private function validateParamsForCreate()
    {
        // 基本检查
        $this->validateBaseParamsForCreate();

        // 检查配置参数是否是合法的json
        $this->validateReportLineForCreate();

        // slug是否已经被占用
        $this->validateTheUniqueSlugForCreate();
    }

    /**
     * 检查配置参数是否是合法的json
     * @throws \Exception
     */
    private function validateReportLineForCreate()
    {
        $request_body = $this->genParamsForPost();

        // 是否存在 && 是否为空 已经在基本检测中测试过了
        if (json_validate($request_body['report_line']) === false) {
            throw new \Exception('请输入合法的合法的配置参数 : JSON类型');
        }
    }

    /**
     * 基本检查
     * @throws \Exception
     */
    private function validateBaseParamsForCreate()
    {
        $request_body = $this->genParamsForPost();

        // 检查必填参数是否合法
        array_walk($this->list_required_create_field, function ($tag, $field) use ($request_body) {
            // 不存在或者为空则弹出错误
            if (!array_key_exists($field, $request_body) || !trim($request_body[$field])) {
                throw new \Exception('请输入' . $tag);
            }
        });
    }

    /**
     * 全量的wechat配置
     * @throws \Exception
     */
    public function listWechat()
    {
        // 限制访问方式
        $this->limitMethodGET();

        return $this->getWechatByCondition([]);
    }

    /**
     * 根据条件获取微信列表
     * @param array $where
     * @return array
     */
    private function getWechatByCondition(array $where)
    {
        return (new WechatWarningModel())->where($where)
            ->select();
    }

    /**
     * 查询微信配置
     * @throws \Exception
     */
    public function searchWechat()
    {
        //限定访问方式
        $this->limitMethodPOST();

        // 获取选中的微信配置
        return $this->getWechatListForSearch();
    }

    /**
     * 获取选中的微信配置
     * @return array
     */
    private function getWechatListForSearch()
    {
        // 条件
        $where = $this->getConditionForSearch();

        // 列表
        return $this->getWechatByCondition($where);
    }

    /**
     * 查找微信配置的条件
     * @return array
     */
    private function getConditionForSearch()
    {
        $request_body = $this->genParamsForPost();
        $slug = array_key_exists('slug', $request_body) ? trim($request_body['slug']) : '';
        if (!$slug) {
            return [];
        }
        return compact('slug');
    }
}
