<?php

namespace Api\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;
use Api\Transform\TransformerCuishouShortDetailStat;
use Common\Common\ResponseTrait;
use Common\Common\HandlerLog;

class BackendCuiShouShortStatRepository extends BaseRepository
{
    use ResponseTrait;

    /*
     * 催收分快捷版的产品ID
     * */
    private $product_id = 210;

    /*
     * 列表的统计单元
     * */
    private $item_base_list = [
        'sum_all' => 0,
        'sum_success' => 0
    ];

    /*
    * 基本的详情统计单元
    * */
    protected $base_detail_item;

    /*
     * 详情需要计算的字段列表
     * */
    private $list_detail_field = [
        'c_all', 'c_success'
    ];

    /*
     * 详情的字段列表存放session中key
     * */
    private $session_detail_field_key = 'session_cuishou_short_detail_key';

    /*
     * 详情存放slug列表的Key
     * */
    private $session_slug_key = 'session_slug_detail_key';

    /*
     * 列表统计中选中的账号
     * */
    private $list_account_selected;

    /**
     * 产看单个的客户
     * @throws \Exception
     * @return array
     */
    public function customerShow()
    {
        // 检查参数
        $this->verifyParamsForCustomerShow();

        // 生成条件
        $where = $this->genConditionForCustomerShow();
        return (new CustomerModel())->where($where)->find();
    }


    /**
     * 生成条件
     * @return array
     */
    protected function genConditionForCustomerShow()
    {
        $customer_id = I('get.customer_id', '', 'trim');
        return compact('customer_id');
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParamsForCustomerShow()
    {
        $customer_id = I('get.customer_id', '', 'trim');
        if (!$customer_id) {
            throw new \Exception('请输入customer_id');
        }
    }

    /**
     * 统计排序
     * @return array
     * @throws \Exception
     */
    public function sortBy()
    {
        $response_body = $this->genParamsForPost();

        // 传递的数据
        $filter_field = $this->getValueWithDefault($response_body, 'filter_field', '');
        $filter_order = $this->getValueWithDefault($response_body, 'filter_order', '');
        $list_data = $this->getValueWithDefault($response_body, 'list_data', []);

        // 如果缺少必要元素 则不进行操作
        if (!$filter_field || !$filter_order) {
            return $list_data;
        }

        // 排序
        return $this->sortData($list_data, $filter_field, $filter_order);
    }

    /**
     * 数据排序
     * @param array $list_data 原始数据
     * @param string $filter_field 排序字段
     * @param string $filter_order 排序方式
     * @return mixed
     */
    protected function sortData($list_data, $filter_field, $filter_order)
    {
        // 总计不参与排序
        $item_total = array_shift($list_data);

        // 统计单元 && 统计单元格式化
        $list_item = array_column($list_data, $filter_field);
        $list_item = $this->formatDataForSort($list_item, $filter_field);

        // 排序
        $sort_order = strtolower($filter_order) === 'asc' ? SORT_ASC : SORT_DESC;
        array_multisort($list_item, $sort_order, SORT_NUMERIC, $list_data);

        // 总计不参与排序
        array_unshift($list_data, $item_total);
        return $list_data;
    }


    /**
     * 格式化统计元素(转成可以直接排序的数值型元素)
     * @param array $list_item
     * @param string $filter_field
     * @return array
     */
    protected function formatDataForSort(array $list_item, $filter_field)
    {
        $delimiter = $filter_field === '"rate_sum_success"' ?  '%' : ',';
        return array_map(function ($item) use ($delimiter) {
            // 如果统计原始是需要反格式化的化
            if (strpos($item, $delimiter) !== false) {
                $item = str_replace($delimiter, '', $item);
            }
            return $item;
        }, $list_item);
    }

    /**
     * 统计详情需要展示的columns
     * @throws \Exception
     * @return array
     */
    public function detailColumns()
    {
        // 获取slug
        $list_slug = $this->getSlugList();

        // 组合需要字段
        $list_columns = $this->appendOtherFieldToColumns($list_slug);

        // 组合成需要的样式
        return $this->formatColumns($list_columns);
    }

    /**
     * 组合成需要的样式
     * @param array $list_columns
     * @return array
     */
    private function formatColumns(array $list_columns)
    {
        $source_column = [
            'field' => '',
            'title' => '',
            'width' => 400,
            'columnAlign' => 'center',
            'orderBy' => '',
            'isResize' => true,
            'titleCellClassName' => 'title_column'
        ];

        return array_map(function ($item_column) use($source_column){
            // 如果是time的话 不需要排序
            if ($item_column['name'] === 'time') {
                $source_column['orderBy'] = false;
            }

            // 动态分配计算长度
            $source_column['field'] = $item_column['name'];
            $source_column['title'] = $item_column['desc'];
            $source_column['width'] = $this->computedLengthForColumns($item_column['desc']);
            return $source_column;
        }, $list_columns);
    }

    /**
     * 计算展示字段占用的长度
     * @param string $column
     * @return integer
     */
    private function computedLengthForColumns($column)
    {
        $length_mb = mb_strlen($column, 'utf8');
        return $length_mb * 12 + 80;
    }

    /**
     * 组合需要字段
     * @param array $list_columns
     * @return array
     */
    private function appendOtherFieldToColumns(array $list_columns)
    {
        array_unshift($list_columns, ['name' => 'time', 'desc' => '时间'],
            ['name' => 'c_all', 'desc' => '总查询量'],
            ['name' => 'c_success', 'desc' => '有效查询量']);

        return $list_columns;
    }

    /**
     * 获取slug
     * @return array|mixed
     * @throws \Exception
     */
    private function getSlugList()
    {
        // session中取值
        if ($list_slug = session($this->session_slug_key)) {
            return $list_slug;
        }

        // api取值 && init session
        $list_slug = $this->getSlugFromApi([]);
        $list_slug = array_key_exists('list', $list_slug['data']) ? $list_slug['data']['list'] : [];
        session($this->session_slug_key, $list_slug);

        return $list_slug;
    }

    /**
     * 统计详情
     * @throws \Exception
     */
    public function detailStat()
    {
        // 限定访问类型
        $this->limitPostMethod();

        // 检查参数
        $this->verifyParamsForDetail();

        // 初始化详情统计需要的基本元素
        $this->iniDetailField();

        // 获取统计信息
        $list_stat_info = $this->getStatInfoForDetail();

        // 追加总计数据
        $list_stat_info = $this->appendTotalToStat($list_stat_info);

        // 格式化数据
        return $this->formatData($list_stat_info);
    }

    /**
     * 详情排序
     * @param array $list_stat_info
     * @return array
     */
    private function sortDataForDetail(array $list_stat_info)
    {
        $list_sort_base = array_column($list_stat_info, 'c_all');
        array_multisort($list_sort_base, SORT_DESC, SORT_NUMERIC, $list_stat_info);
        return $list_stat_info;
    }

    /**
     * 格式化数据
     * @param array $list_stat
     * @return array
     */
    protected function formatData($list_stat)
    {
        return array_map(function ($item) {
            // 格式化单元
            return $this->formatItem($item);
        }, $list_stat);

    }

    /**
     * 格式化单元
     * @param array $item_stat
     * @return array
     */
    protected function formatItem($item_stat)
    {
        array_walk($this->list_detail_field, function ($field) use (&$item_stat) {
            $item_stat[$field] = $this->formatNum($item_stat[$field]);
        });
        return $item_stat;
    }

    /**
     * 增加总计数据
     * @param array $list_stat_info
     * @return array
     * @throws \Exception
     */
    protected function appendTotalToStat(array $list_stat_info)
    {
        // 获取总计数据
        $list_total = $this->getTotalForDetail($list_stat_info);

        // 追加总计数据
        return $this->appendTotalToStatForDetail($list_stat_info, $list_total);
    }

    /**
     * 追加总计数据
     * @param array $list_stat_info
     * @param array $list_total
     * @return array
     */
    protected function appendTotalToStatForDetail($list_stat_info, $list_total)
    {
        array_unshift($list_stat_info, $list_total);
        return $list_stat_info;
    }

    /**
     * 获取总计数据
     * @param array $list_stat_info
     * @throws \Exception
     * @return array
     */
    protected function getTotalForDetail($list_stat_info)
    {
        // 容器
        $list_container = $this->base_detail_item;
        $list_container['time'] = '总计';
        array_walk($list_stat_info, function ($item_stat) use (&$list_container) {
            array_walk($this->list_detail_field, function ($field) use (&$list_container, $item_stat) {
                $list_container[$field] += $item_stat[$field];
            });
        });
        return $list_container;
    }

    /**
     * 初始化详情统计字段
     * @throws \Exception
     */
    private function iniDetailField()
    {
        // 获取slug列表
        $list_slug = $this->getSlugList();
        $list_slug = array_column($list_slug, 'name');

        // 组合字段列表
        $this->list_detail_field = array_merge($this->list_detail_field, $list_slug);

        // 组合基本元素
        $this->base_detail_item = array_fill_keys($this->list_detail_field, 0);

        // 组合字段列表存到session中, 方便Transfromer使用
        session($this->session_detail_field_key, $this->list_detail_field);
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function getSlugFromApi($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_short_slug_list'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['code'] === 0) {
                break;
            }
            $i++;
        }
        // 三次请求失败,做记录
        $this->logForSlug($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 连续三次请求API失败
     * @param array $result_request
     * @param string $url
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    private function logForSlug(array $result_request, $url, array $params)
    {
        if ((int)$result_request['code'] === 0) {
            return true;
        }

        $msg_log = [
            'handle_type' => 'stat',
            'description' => '邦信分快捷版SlugAPI连续三次请求失败',
            'content' => compact('result_request', 'url', 'params'),
            'handle_result' => 0
        ];
        HandlerLog::log($msg_log);
        throw new \Exception('邦信分快捷版SlugAPI连续三次请求失败');
    }

    /**
     * 获取统计信息
     * @throws \Exception
     * @return array
     */
    protected function getStatInfoForDetail()
    {
        // 条件
        $params = $this->genStatConditionForDetail();

        // 调用API
        $list_stat_info = $this->getApiDetailStatInfo($params);

        // 补全缺失的天
        $list_stat_info = $this->appendMissDayForDetail($list_stat_info);

        // transformer
        return TransformerCuishouShortDetailStat::transForms($list_stat_info);
    }

    /**
     * 补全缺失的天
     * @param array $list_stat_info
     * @return array
     */
    protected function appendMissDayForDetail(array $list_stat_info)
    {
        $list_stat_info = array_column($list_stat_info, null, 'time');

        // 时间范围
        $list_date_range = $this->dateRange();

        // 容器
        $list_container = [];
        array_walk($list_date_range, function ($date) use (&$list_container, $list_stat_info) {
            $date_format = date('Ymd', strtotime($date));

            // 如果没有这天的数据 则赋默认值
            if (!array_key_exists($date_format, $list_stat_info)) {
                $list_container[$date] = $this->base_detail_item;
            } else {
                $list_container[$date] = $list_stat_info[$date_format];
            }

            $list_container[$date]['time'] = $date;
        });

        return $list_container;
    }


    /**
     * 需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = $this->genParamsForPost();
        $day_begin = date('Ymd', strtotime($request_body['time_begin']));
        $day_end = date('Ymd', strtotime($request_body['time_end']));

        // 容器 ['2018-10-01', '2018-10-02']
        $list_date = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            $date_format = date('Y-m-d', strtotime($day_end));
            array_push($list_date, $date_format);

            $day_end = date('Ymd', strtotime($day_end . '-1 day'));
        }

        return $list_date;
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getApiDetailStatInfo($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_short_stat_detail'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['code'] === 0) {
                break;
            }
            $i++;
        }
        // 三次请求失败,做记录
        $this->logDetailForFail($result_request, $url_api, $params);
        return array_key_exists('list', $result_request['data']) ? $result_request['data']['list'] : [];
    }

    /**
     * 连续三次请求API失败
     * @param array $result_request
     * @param string $url
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    private function logDetailForFail(array $result_request, $url, array $params)
    {
        if ((int)$result_request['code'] === 0) {
            return true;
        }

        $msg_log = [
            'handle_type' => 'stat',
            'description' => '邦信分快捷版详情统计API连续三次请求失败',
            'content' => compact('result_request', 'url', 'params'),
            'handle_result' => 0
        ];
        HandlerLog::log($msg_log);
        throw new \Exception('邦信分快捷版详情统计API连续三次请求失败');
    }

    /**
     * 详情统计条件
     * @return array
     */
    protected function genStatConditionForDetail()
    {
        // apikey 限制
        $limit_apikey = $this->limitApiKeyParamsForDetail();

        // 时间限制
        $limit_time = $this->genLimitTimeForDetailApi();
        return array_merge($limit_apikey, $limit_time);
    }

    /**
     * 时间限制
     * @return array
     */
    protected function genLimitTimeForDetailApi()
    {
        $request_body = $this->genParamsForPost();
        $date_start = date('Ymd', strtotime($request_body['time_begin']));
        $date_end = date('Ymd', strtotime($request_body['time_end']));
        return compact('date_end', 'date_start');
    }

    /**
     * apikey 限制
     * @return array
     */
    protected function limitApiKeyParamsForDetail()
    {
        $request_body = $this->genParamsForPost();
        $customer_id = array_key_exists('customer_id', $request_body) ? trim($request_body['customer_id']) : '';
        $account_id = array_key_exists('account_id', $request_body) ? trim($request_body['account_id']) : '';

        // 如果只限制了 account_id
        if (!$customer_id && $account_id) {
            return $this->genParamsWhenLimitAccount($account_id);
        }

        // 如果限制了 customer_id
        if ($customer_id && !$account_id) {
            return $this->genParamsWhenLimitCustomer($customer_id);
        }

        // 同时限制了两者
        return $this->genParamsWhenLimitBoth($customer_id, $account_id);
    }

    /**
     * 同时限制了customer_id and account_id
     * @param integer $customer_id 客户ID
     * @param integer $account_id 账号ID
     * @return array
     */
    protected function genParamsWhenLimitBoth($customer_id, $account_id)
    {
        $list_account = $this->getAccountByCondition(compact('customer_id', 'account_id'));
        // 如果当前客户没有开通客户，则永远得不到数据
        if (!$list_account) {
            return ['apikey' => '当前客户没有开通账户, 客户id : ' . $customer_id];
        }

        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 生成apikey限制 (只是限制了customer_id的时候)
     * @param integer $customer_id
     * @return array
     */
    protected function genParamsWhenLimitCustomer($customer_id)
    {
        // 默认限制
        $father_id = ['NEQ', '0'];

        $list_account = $this->getAccountByCondition(compact('customer_id', 'father_id'));
        // 如果当前客户没有开通客户，则永远得不到数据
        if (!$list_account) {
            return ['apikey' => '当前客户没有开通客户, 客户id : ' . $customer_id];
        }

        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 生成apikey限制 (只是限制了account_id的时候)
     * @param integer $account_id 账号ID
     * @return array
     */
    protected function genParamsWhenLimitAccount($account_id)
    {
        $list_account = $this->getAccountByCondition(compact('account_id'));
        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParamsForDetail()
    {
        // 检查时间
        $this->verifyTime();

        // 是否选定了客户或者账号
        $this->verifyExistsSelectedApikey();
    }

    /**
     * 是否选定了客户或者账号
     * @throws \Exception
     */
    protected function verifyExistsSelectedApikey()
    {
        $request_body = $this->genParamsForPost();
        $customer_id = array_key_exists('customer_id', $request_body) ? trim($request_body['customer_id']) : '';
        $account_id = array_key_exists('account_id', $request_body) ? trim($request_body['account_id']) : '';
        if (!$customer_id && !$account_id) {
            throw new \Exception('请选定客户或者账号后再查询');
        }
    }

    /**
     * 检查时间
     * @throws \Exception
     */
    protected function verifyTime()
    {
        // 检查时间参数是否存在
        $this->verifyExistTime();

        // 时间得其他需求
        $this->verifyTimeOtherRequire();
    }

    /**
     * 时间得其他需求
     * @throws \Exception
     */
    protected function verifyTimeOtherRequire()
    {
        $request_body = $this->genParamsForPost();
        // 合法
        $time_begin = strtotime($request_body['time_begin']);
        $time_end = strtotime($request_body['time_end']) + 86399;
        if ($time_begin > $time_end) {
            throw new \Exception('开始时间必须小于结束时间');
        }

        // 365天限制
        $time_diff = $time_end - $time_begin > 365 * 86400;
        if ($time_diff) {
            throw new \Exception('时间范围不合法,单次查询最多365天');
        }
    }

    /**
     * 检查时间参数是否存在
     * @throws \Exception
     */
    protected function verifyExistTime()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('time_begin', $request_body) || !$request_body['time_begin']) {
            throw new \Exception('请选择开始时间');
        }
        if (!array_key_exists('time_end', $request_body) || !$request_body['time_end']) {
            throw new \Exception('请选择结束时间');
        }
    }

    /**
     * 限制为POST请求
     * @throws \Exception
     */
    protected function limitPostMethod()
    {
        if (!IS_POST) {
            throw new \Exception('请求必须是POST方法');
        }
    }

    /**
     * 查看单个的账号
     * @throws \Exception
     */
    public function accountShow()
    {
        // 检查参数
        $this->verifyParamsForShow();

        // 生成条件
        $where = $this->genConditionForShow();

        return (new AccountModel())->where($where)->find();
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParamsForShow()
    {
        $account_id = I('get.account_id', '', 'trim');
        if (!$account_id) {
            throw new \Exception('请输入account_id');
        }
    }


    /**
     * 生成条件
     * @return array
     */
    protected function genConditionForShow()
    {
        $account_id = I('get.account_id', '', 'trim');
        return compact('account_id');
    }


    /**
     * 生成全量的账号列表
     * @return array
     * @throws \Exception
     */
    public function accountList()
    {
        // 限制请求方法
        $this->limitGetMethod();

        // 列表
        $list_account = $this->getAccountListByCondition();

        // 过滤掉没有开通用产品的账号
        return $this->filterNotHasProduct($list_account);
    }


    /**
     * 过滤掉没有开通产品的账号
     * @param array $list_account 等待过滤的账号
     * @return array
     */
    protected function filterNotHasProduct($list_account)
    {
        // 全量账号产品
        $list_account_product = $this->getAllAccountProduct();
        $list_account_product = array_column($list_account_product, 'account_id');

        // 容器
        $list_container = [];
        array_walk($list_account, function ($item) use (&$list_container, $list_account_product) {
            $account_id = $item['account_id'];
            if (in_array($account_id, $list_account_product)) {
                array_push($list_container, $item);
            }
        });
        return $list_container;
    }


    /**
     * 获取全量的账号id
     */
    protected function getAllAccountProduct()
    {
        $product_id = $this->product_id;
        return $this->getAccountProductByCondition(compact('product_id'));
    }


    /**
     * 根据条件获取账号列表
     * @return array
     */
    protected function getAccountListByCondition()
    {
        // 条件
        $where = $this->genConditionForListAccount();
        return $this->getAccountByCondition($where);
    }

    /**
     * 为账号列表生成条件
     * @return array
     */
    protected function genConditionForListAccount()
    {
        // 默认的限制
        $father_id = ['NEQ', '0'];

        // 客户限制
        $customer_id = I('get.customer_id', '', 'trim');
        if (!$customer_id) {
            return compact('father_id');
        }

        return compact('customer_id', 'father_id');
    }

    /**
     * 限制为Get请求
     * @throws \Exception
     */
    protected function limitGetMethod()
    {
        if (!IS_GET) {
            throw new \Exception('请求必须是GET方法');
        }
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function listStat()
    {
        // 检查访问方式
        $this->validateMethodsForList();

        // 检索条件是否合法
        $this->validateParamsForList();

        // 获取接口统计数据
        $list_stat = $this->invokeApiForStatInfo();

        // 整合成客户维度
        $list_stat = $this->tidyCustomerDataForList($list_stat);

        // 排序
        $list_stat = $this->sortDataForList($list_stat);

        // 追加总计数据
        $list_stat = $this->appendTotalToList($list_stat);

        // 追加计算有效查询率
        $list_stat = $this->computedItemForList($list_stat);

        // 格式化数据
        return $this->formatDataForList($list_stat);
    }

    /**
     * 为列表数据排序
     * @param array $list_stat
     * @return array
     */
    private function sortDataForList(array $list_stat)
    {
        $list_sort_base = array_column($list_stat, 'sum_all');
        array_multisort($list_sort_base, SORT_DESC, SORT_NUMERIC, $list_stat);
        return $list_stat;
    }

    /**
     * 追加总计数据
     * @param array $list_stat
     * @return array
     */
    private function appendTotalToList(array $list_stat)
    {
        $list_total = array_reduce($list_stat, function ($carry, $item_stat) {
            $carry['sum_all'] += $item_stat['sum_all'];
            $carry['sum_success'] += $item_stat['sum_success'];
            return $carry;
        }, $this->item_base_list);

        $list_total['name'] = '总计';

        array_unshift($list_stat, $list_total);
        return $list_stat;
    }

    /**
     * 格式化数据
     * @param array $list_stat
     * @return array
     */
    private function formatDataForList(array $list_stat)
    {
        return array_map([new static(), 'formatItemForList'], $list_stat);
    }

    private function formatItemForList(array $item)
    {
        $item['sum_all'] = $this->formatNum($item['sum_all']);
        $item['sum_success'] = $this->formatNum($item['sum_success']);
        $item['rate_sum_success'] = $this->formatPercentage($item['rate_sum_success']);
        return $item;
    }

    /**
     * 计算有效查询率
     * @param array $list_stat
     * @return array
     */
    private function computedItemForList(array $list_stat)
    {
        return array_map([new static(), 'computedItem'], $list_stat);
    }

    /**
     * 计算有效查询率
     * @param array $item
     * @return array
     */
    private function computedItem(array $item)
    {
        if ($item['sum_all'] == 0) {
            $item['rate_sum_success'] = 'NA';
            return $item;
        }

        $item['rate_sum_success'] = $item['sum_success'] / $item['sum_all'];
        return $item;
    }

    /**
     * 整合成客户维度
     * @param array $list_stat
     * @return array
     */
    private function tidyCustomerDataForList(array $list_stat)
    {
        // 客户信息
        $list_customer = $this->getCustomerShowForList();

        // 将账号ID整合到客户中
        return $this->formatAccountInCustomer($list_customer, $list_stat);
    }

    /**
     * 将账号ID整合到客户中
     * @param array $list_customer
     * @param array $list_stat
     * @return array
     */
    private function formatAccountInCustomer(array $list_customer, array $list_stat)
    {
        $list_customer = array_column($list_customer, null, 'customer_id');
        $list_stat = array_column($list_stat, null, 'apikey');

        array_walk($this->list_account_selected, function ($item_account) use (&$list_customer, $list_stat) {
            $customer_id = $item_account['customer_id'];
            $apikey = $item_account['apikey'];

            // 当前账号如果部不属于选中的客户 则抛掉
            if (!array_key_exists($customer_id, $list_customer)) {
                return true;
            }
            $list_customer[$customer_id]['list_account'][] = $item_account;

            // 如果当前账号没有调用 则抛掉
            if (!array_key_exists($apikey, $list_stat)) {
                return true;
            }

            // 对应的客户进行累加
            $list_customer[$customer_id] = $this->sumStatItemForList($list_customer[$customer_id], $list_stat[$apikey]);

        });
        //过滤掉没有账号的客户数据
        $list_customer = array_filter($list_customer, function ($item) {
            if (!isset($item['list_account']) || empty($item['list_account'])) {
                return false;
            }
            return true;
        });

        return $list_customer;
    }

    /**
     * 列表进行统计信息的累加
     * @param array $item_customer
     * @param array $item_stat
     * @return array
     */
    private function sumStatItemForList(array $item_customer, array $item_stat)
    {
        $item_customer['sum_all'] += $item_stat['sum_all'];
        $item_customer['sum_success'] += $item_stat['sum_success'];
        return $item_customer;
    }

    /**
     * 客户信息
     * @return array
     */
    private function getCustomerShowForList()
    {
        $limit_customer = $this->limitCustomerForList();
        $list_customer = $this->getCustomerByCondition($limit_customer);

        // 附加初始信息
        return array_map(function ($item_customer) {
            return array_merge($item_customer, $this->item_base_list);
        }, $list_customer);
    }

    /**
     * 为列表统计的账号限定生成条件
     * @return array
     */
    private function limitCustomerForList()
    {
        $request_body = $this->genParamsForPost();
        $customer_id = array_key_exists('customer_id', $request_body) ? $request_body['customer_id'] : '';

        $id_delete = 0;
        return $customer_id ? compact('customer_id', 'is_delete') : compact('id_delete');
    }

    /**
     * 检索条件是否合法
     * @throws \Exception
     */
    private function validateParamsForList()
    {
        $request_body = $this->genParamsForPost();
        $time_begin = array_key_exists('time_begin', $request_body) ? $request_body['time_begin'] : '';
        $time_end = array_key_exists('time_end', $request_body) ? $request_body['time_end'] : '';

        // 时间是否合法
        if (!$time_end || !$time_begin) {
            throw new \Exception('开始时间和结束时间必须选择');
        }

        $time_begin = strtotime($time_begin);
        $time_end = strtotime($time_end);
        if ($time_begin > $time_end) {
            throw new \Exception('开始时间必须小于结束时间');
        }

        if ($time_end > time()) {
            throw new \Exception('请选择有效的结束时间');
        }

        if ($time_end - $time_begin > 31536000) {
            throw new \Exception('单次查询的最长时间间隔是365天');
        }
    }

    /**
     * 调用接口获取列表数据
     * @return array
     * @throws \Exception
     */
    private function invokeApiForStatInfo()
    {
        // 参数
        $params = $this->genParamsForList();

        // 调用接口获取列表数据
        return $this->invokeApiForList($params);
    }

    /**
     * 调用接口获取列表数据
     * @param array $params
     * @throws \Exception
     * @return array
     */
    protected function invokeApiForList(array $params)
    {
        // 调用接口
        $list_stat = $this->getApiStatInfo($params);

        // 整理信息
        return array_key_exists('stat', $list_stat['data']) ? $list_stat['data']['stat'] : [];
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getApiStatInfo(array $params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_short_stat_list'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ((int)$result_request['code'] === 0) {
                break;
            }
            $i++;
        }
        // 三次请求失败,做记录
        $this->logForFail($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 连续三次请求API失败
     * @param array $result_request
     * @param string $url
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    private function logForFail(array $result_request, $url, array $params)
    {
        if ((int)$result_request['code'] === 0) {
            return true;
        }

        $msg_log = [
            'handle_type' => 'stat',
            'description' => '邦信分快捷版列表统计API连续三次请求失败',
            'content' => compact('result_request', 'url', 'params'),
            'handle_result' => 0
        ];
        HandlerLog::log($msg_log);
        throw new \Exception('邦信分快捷版列表统计API连续三次请求失败');
    }

    /**
     * 列表统计参数
     * @return array
     */
    private function genParamsForList()
    {
        // 时间限定
        $limit_time = $this->limitTimeForList();

        // apikey 限定
        $limit_apikey = $this->limitApiKeyForList();

        return array_merge($limit_apikey, $limit_time);
    }

    private function limitApiKeyForList()
    {
        // 客户限定
        $list_account = $this->limitCustomer();

        // 账号签约状态限定
        $this->list_account_selected = $list_account = $this->limitContractStatusForList($list_account);

        // 整理成apikey的形式
        return $this->formatApiKeyForList($list_account);
    }

    /**
     * 整理成apikey的形式
     * @param array $list_account
     * @return array
     */
    private function formatApiKeyForList(array $list_account)
    {
        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 账号签约状态限定
     * @param array $list_account 要过滤客户
     * @return array
     */
    private function limitContractStatusForList(array $list_account)
    {
        // 满足签约状态的催收快捷版的产品列表
        $list_product = $this->getContractProductForList();

        // 过滤掉不合法的账号
        return $this->filterLegalAccountForList($list_account, $list_product);
    }

    /**
     * 过滤掉不合法的账号
     * @param array $list_account
     * @param array $list_product
     * @return array
     */
    private function filterLegalAccountForList(array $list_account, array $list_product)
    {
        $list_product = array_column($list_product, null, 'account_id');

        return array_reduce($list_account, function ($list_container, $item_account) use ($list_product) {
            // 如果账号下面没有符合的产品 则过滤掉
            $account_id = $item_account['account_id'];
            if (array_key_exists($account_id, $list_product)) {
                array_push($list_container, $item_account);
            }
            return $list_container;
        }, []);
    }

    /**
     * 满足签约状态的催收快捷版的产品列表
     * @return array
     */
    private function getContractProductForList()
    {
        // 条件
        $where = $this->genContractParamsForList();

        return $this->getAccountProductByCondition($where);
    }

    /**
     * 限定产品签约状态的条件
     * @return array
     */
    private function genContractParamsForList()
    {
        $request_body = $this->genParamsForPost();
        $contract_status = array_key_exists('contract_status', $request_body) ? $request_body['contract_status'] : '';
        $product_id = $this->product_id;

        // 没有限定签约类型
        if (!$contract_status) {
            return compact('product_id');
        }
        return compact('product_id', 'contract_status');
    }

    /**
     * 获取产品列表
     * @param array $where
     * @return array
     */
    private function getAccountProductByCondition(array $where)
    {
        return (new AccountProductModel())->where($where)
            ->select();
    }

    /**
     * 客户限定
     * @return array
     */
    private function limitCustomer()
    {
        // 条件
        $where = $this->genAccountParamsForList();

        // 账号列表
        return $this->getAccountByCondition($where);
    }

    /**
     * 为列表统计的账号限定生成条件
     * @return array
     */
    private function genAccountParamsForList()
    {
        $request_body = $this->genParamsForPost();
        $customer_id = array_key_exists('customer_id', $request_body) ? $request_body['customer_id'] : '';

        $is_delete = 0;
        return $customer_id ? compact('customer_id', 'is_delete') : compact('is_delete');
    }

    /**
     * 获取账号列表
     * @param array $where
     * @return array
     */
    private function getAccountByCondition(array $where)
    {
        return (new AccountModel())->where($where)
            ->select();
    }

    /**
     * 时间限定
     * @return array
     */
    private function limitTimeForList()
    {
        $request_body = $this->genParamsForPost();
        $time_begin = trim($request_body['time_begin']);
        $time_end = trim($request_body['time_end']);
        $date_start = date('Ymd', strtotime($time_begin));
        $date_end = date('Ymd', strtotime($time_end));

        return compact('date_end', 'date_start');
    }

    /**
     * 限定访问方法
     * @throws \Exception
     */
    private function validateMethodsForList()
    {
        if (!IS_POST) {
            throw new \Exception('本方法限定POST方式请求');
        }
    }

    /**
     * 客户全量列表
     * @return array
     */
    public function customerList()
    {
        //获取已开通此产品的账号ID
        $product_id = $this->product_id;
        $account_ids = (new AccountProductModel())->field('account_id')->where([
            compact('product_id')
        ])->select();
        $customer_id = (new AccountModel())->field('customer_id')->where([
            'account_id'    => ['in', array_column($account_ids, 'account_id')],
            'is_delete'     => 0
        ])->select();
        $is_delete = 0;
        $customer_id = ['in', array_values(array_unique(array_column($customer_id, 'customer_id')))];
        return $this->getCustomerByCondition(compact('customer_id', 'is_delete'));
    }

    /**
     * 根据条件获取客户列表
     * @param array $where
     * @return array
     */
    protected function getCustomerByCondition(array $where)
    {
        return (new CustomerModel())->where($where)
            ->select();
    }
}