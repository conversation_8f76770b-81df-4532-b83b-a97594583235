<?php

namespace Api\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\ProductModel;
use Account\Repositories\AccountRepository;
use Account\Repositories\CuiShouFenRepository;
use Account\Repositories\CuiShouFenRepository1000;
use Account\Repositories\FeeConfigRepository;
use Account\Repositories\GoldShieldRepository;
use Account\Repositories\ProductRepository;
use Common\Common\CurlTrait;
use Common\Common\HandlerLog;
use Common\Common\ResponseTrait;
use Common\Common\WechatBackendExceptionTrait;
use Home\Controller\ToolController;
use Common\Common\Approval;

class BackendProductRepository extends BaseRepository
{
    use ResponseTrait, CurlTrait, WechatBackendExceptionTrait;

    // 编辑之前的产品信息
    protected $product_before_update;

    /*
     * 运营商报告的id
     * */
    private $product_report_id = 102;

    /*
     * 邦信分详单版V1id
     * */
    private $product_cuishou_id = 101;

    // 老版本的紧急联系人限制的映射关系
    private $list_emergency_map = [
        'emergency_contact_detail_limits_spouse' => '配偶',
        'emergency_contact_detail_limits_parent' => '父母',
        'emergency_contact_detail_limits_bas' => '兄弟姐妹',
        'emergency_contact_detail_limits_children' => '子女',
        'emergency_contact_detail_limits_relative' => '亲戚',
        'emergency_contact_detail_limits_colleague' => '同事',
        'emergency_contact_detail_limits_friend' => '朋友',
        'emergency_contact_detail_limits_classmate' => '同学',
        'emergency_contact_detail_limits_other' => '其他',
    ];

    /*
    * 运营商报告的配置信息
    * */
    private $report_config;

    /*
     * 发生变化的重要参数
     * */
    private $list_updated_params = [];

    /*
     * 重要data字段
     * */
    private $list_data_import_fields;


    /**
     * 秒配的签名字符串
     * @throws \Exception
     */
    public function signs()
    {
        $this->limitMethodGet();

        // 校验参数
        $this->validateParamsForSigns();

        // 生成签名 && 实例
        return $this->genSignsAndExample();
    }

    private function genSignsAndExample()
    {
        // 生成签名
        list($sig_str, $apikey) = $this->genSigns();

        // 列子
        $api_example = $this->genExample($sig_str, $apikey);
        return compact('sig_str', 'api_example');
    }

    /**
     * 列子
     * @param $sig_str
     * @param $apikey
     * @return string
     */
    private function genExample($sig_str, $apikey)
    {
        $sig = (new ToolController())->gen_sig($sig_str, '10086', 'NMKJ', 'myApp',
            '1.0', $apikey, '86', '1');

        // gen api example
        return "https://itag.dianhua.cn/itag/?apikey=" . $apikey .
            "&app=myApp&app_ver=1.0&country=86&version=1&tel=9646f275f10ae73f70fa297fef85e62b5accd3a38284eb0a64b8203e12dd1373&uid=NMKJ&sig=" . $sig . '&encrypt=3';
    }

    /**
     * 生成签名
     * @return array
     */
    private function genSigns()
    {
        $account_id = I('get.account_id', '', 'trim');
        $account = $this->getOneAccountByCondition(compact('account_id'), ['appsecret', 'apikey']);

        // generate sig_str
        $sig_str =  (new ToolController())->gen_sig_str($account['appsecret']);
        return [$sig_str, $account['apikey']];
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    private function validateParamsForSigns()
    {
        $account_id = I('get.account_id', '', 'trim');
        $product_id = I('get.product_id', '', 'trim');
        if (!$product_id || $product_id != 601) {
            throw new \Exception('请传入正常的product_id');
        }

        if (!$account_id) {
            throw new \Exception('请传入正常的account_id');
        }
    }

    /**
     * 当更新了重要字段的时候,弹窗预警
     */
    public function warningWhenUpdateImportField()
    {
        // 格式化旧版的参数
        list($product_old_data_common, $product_old_data_custom, $product_id) = $this->formatOldDataWhenUpdate();

        // 格式化新版的参数
        list($product_new_data_common, $product_new_data_custom) = $this->formatNewDataWhenUpdate();

        // 检查通用参数是否变化
        $this->validateCommonParamsIfChanged($product_new_data_common, $product_old_data_common);

        // 检查产品的特定参数是否发生了变化
        $this->validateCustomParamsIfChanged($product_new_data_custom, $product_old_data_custom, $product_id);

        // 格式化字段
        return $this->formatDataField();
    }

    /**
     * 格式化data字段
     */
    private function formatDataField()
    {
        // 格式化需要转化的字段
        $list_wait_fields = array_reduce($this->list_data_import_fields, function ($carry, $item) {
            if (in_array($item['type'], [3, 4])) {
                $carry[$item['name']] = $item;
            }
            return $carry;
        }, []);

        // 字段转换
        return array_map(function ($item) use ($list_wait_fields) {
            return $this->formatItemWhenUpdate($item, $list_wait_fields);
        }, $this->list_updated_params);
    }

    /**
     * 字段转化
     * @param array $item
     * @param array $list_wait_fields
     * @return array
     */
    private function formatItemWhenUpdate(array $item, array $list_wait_fields)
    {
        $field = $item['field'];
        if (!array_key_exists($field, $list_wait_fields)) {
            return $item;
        }

        $type = $list_wait_fields[$field]['type'];
        $options = array_key_exists('option', $list_wait_fields[$field]) ? $list_wait_fields[$field]['option'] : [];
        $options = array_column($options, 'opt_name', 'opt_val');

        switch ($type) {
            case 3:
                return $this->formatItemWhenTypeThree($item, $options);
                break;
            case 4:
                return $this->formatItemWhenTypeFour($item, $options);
                break;
            default:
                return $item;
        }
    }

    /**
     * 格式化type数据
     * @param array $item
     * @param array $options
     * @return array
     */
    private function formatItemWhenTypeFour(array $item, array $options)
    {
        $item['old'] = implode(' , ', arrOnly($options, $item['old']));
        $item['new'] = implode(' , ', arrOnly($options, $item['new']));
        return $item;
    }

    /**
     * 格式化type 数据
     * @param array $item
     * @param array $options
     * @return array
     */
    private function formatItemWhenTypeThree(array $item, array $options)
    {
        $item['old'] = $options[$item['old']];
        $item['new'] = $options[$item['new']];
        return $item;
    }

    /**
     * 检查产品的特定参数是否发生了变化
     * @param array $product_new_data_custom
     * @param array $product_old_data_custom
     * @param int $product_id
     *
     */
    private function validateCustomParamsIfChanged(array $product_new_data_custom, array $product_old_data_custom, $product_id)
    {
        $list_important_custom_fields = C('list_important_custom_fields');

        // 如果没有指定该产品的特殊配置 则不再过问
        if (!isset($list_important_custom_fields[$product_id])) {
            return;
        }

        $list_important_fields = $list_important_custom_fields[$product_id];

        // 重要字段是否发生变化
        $this->validateImportFieldWhetherChanged($product_new_data_custom, $product_old_data_custom, $list_important_fields);
    }

    /**
     * 格式化旧版的参数
     * @return array
     */
    private function formatOldDataWhenUpdate()
    {
        $list_params = $this->postParams();

        // 容器
        $data_common = [];
        $data_custom = [];

        array_walk($list_params['product_old'], function ($item, $key) use (&$data_custom, &$data_common) {
            // 通用参数
            if ($key != 'data') {
                $data_common[$key] = $item;
                return;
            }

            // 赋值给新data字段使用
            if (!$this->list_data_import_fields) {
                $this->list_data_import_fields = $item;
            }

            // 定制参数
            array_walk($item, function ($item_product) use (&$data_custom) {
                $data_custom[$item_product['name']] = $item_product['val'];
            });
        });

        $product_id = $data_common['product_id'];

        return [$data_common, $data_custom, $product_id];
    }

    /**
     * 格式化新版的参数
     * @return array
     */
    private function formatNewDataWhenUpdate()
    {
        $list_params_wait_parse = $this->postParams();

        // 容器
        $data_common = [];
        $data_custom = [];

        array_walk($list_params_wait_parse['product_new'], function ($params_wait_parse) use (&$data_common, &$data_custom) {
            $key = $params_wait_parse['name'];

            if ($key == 'data_json') {
                return;
            }

            // 通用参数
            if (strpos($key, 'data') === false) {
                $data_common[$key] = $params_wait_parse['value'];
                return;
            }

            // 定制参数
            $data_custom = $this->parseDataPartWhenValidateImport($key, $params_wait_parse['value'], $data_custom);
        });

        return [$data_common, $data_custom];
    }

    /**
     * 解决传递的新版参数的data部分
     * @param $key
     * @param $item
     * @param array $data_custom
     * @return array
     */
    private function parseDataPartWhenValidateImport($key, $item, array $data_custom)
    {
        // 获取本身的参数
        $begin = strpos($key, '[');
        $length = strpos($key, ']') - $begin - 1;
        $key_format = substr($key, $begin + 1, $length);

        // 如果不是多维度的([])
        if (strpos($key, '[]') === false) {
            $data_custom[$key_format] = $item;
            return $data_custom;
        }

        $data_custom[$key_format][] = $item;
        return $data_custom;
    }

    /**
     * 检查通用参数是否变化
     * @param array $product_new_data_common
     * @param array $product_old_data_common
     */
    private function validateCommonParamsIfChanged(array $product_new_data_common, array $product_old_data_common)
    {
        $list_important_common_fields = C('list_important_common_fields');

        // 检查关键字是否发生变化
        $this->validateImportFieldWhetherChanged($product_new_data_common, $product_old_data_common, $list_important_common_fields);
    }

    /**
     * 检查关键字是否发生变化
     * @param array $product_new_data
     * @param array $product_old_data
     * @param array $list_important_fields
     */
    private function validateImportFieldWhetherChanged(array $product_new_data, array $product_old_data, array $list_important_fields)
    {
        array_walk($list_important_fields, function ($item, $field) use ($product_new_data, $product_old_data) {
            $old = array_key_exists($field, $product_old_data) ? $product_old_data[$field] : '';
            $new = array_key_exists($field, $product_new_data) ? $product_new_data[$field] : '';

            // 如果是字符串则 trim
            $old = $this->trimIfString($old);
            $new = $this->trimIfString($new);

            if ($old == $new) {
                return;
            }

            // array转化成string
            $meaning = $item['meaning'];
            $field = $item['field'];

            array_push($this->list_updated_params, compact('field', 'old', 'new', 'meaning'));
        });
    }

    /**
     * 如果是字符串则 trim
     * @param $val_wait
     * @return mixed
     */
    private function trimIfString($val_wait)
    {
        return is_string($val_wait) ? trim($val_wait) : $val_wait;
    }

    /**
     * @return mixed
     */
    private function getReportConfig()
    {
        if ($this->report_config) {
            return $this->report_config;
        }

        $product_id = $this->product_report_id;
        $product_report = $this->getOneProductByCondition(compact('product_id'));
        return $this->report_config = json_decode($product_report['data'], true);
    }

    /**
     * 设置更新之前的产品信息
     */
    public function setProductBeforeUpdate()
    {
        $product_id = I('post.product_id');
        $account_id = I('post.account_id');
        $product_source = $this->getOneAccountProductByCondition(compact('account_id', 'product_id'));

        // 如果是301 302
        $product_id = I('post.product_id', '', 'trim');
        if (in_array($product_id, [301, 302])) {
            $product_id = $this->product_report_id;
            $product_report = $this->getOneAccountProductByCondition(compact('product_id', 'account_id'));
            $this->product_before_update = compact('product_source', 'product_report');
        }

        $this->product_before_update = $product_source;
    }

    /**
     * 更新产品
     * @throws \Exception
     */
    public function updateProduct()
    {
        // 限定访问方式
        $this->limitMethodPost();

        // 校验参数
        $this->validateParamsForUpdate();

        // 发起对back-end的请求, 擦出用户用量预警产生的缓存
        $this->requestBackendApiForClearFlag();

        //计费配置监听开通产品编辑事件
        (new FeeConfigRepository())->listenUpdateStoreProduct();

        // 设置更新之前的数据
        $this->setProductBeforeUpdate();

        // 更新
        $this->updateProductDo();

        //监听产品状态的签约状态，
        (new AccountRepository())->listenAccountProductStatus();
    }

    /**
     * 发起对back-end的请求
     * @throws \Exception
     */
    private function requestBackendApiForClearFlag()
    {
        // 参数
        $params = $this->genParamsForFlag();

        // url
        $url = C('LIST_API_URL.backend_api_clear_flag');

        // 发起请求
        $i = 0;
        while (true) {
            $response = $this->post($url, $params);
            if ($i > 2 || $response['status'] == 0) {
                break;
            }
            $i++;
        }
        if ($response['status'] != 0) {
            $tip = '清除用户用量预警的缓存连续三次失败';
            $msg = compact('response', 'params', 'url');
            $this->wehcatException(json_encode(compact('tip', 'msg'), JSON_UNESCAPED_UNICODE));
            throw new \Exception('清除用户用量预警的缓存连续三次失败');
        }
    }

    /**
     * 参数
     */
    private function genParamsForFlag()
    {
        // before 参数
        $before = $this->getBeforeParamsForFlag();

        // after参数
        $after = $this->getAfterParamsForFlag();

        // product_id, key ,apikey
        list($product_id, $key, $apikey) = $this->getListParamsForFlag();

        return compact('product_id', 'key', 'apikey', 'before', 'after');
    }

    /**
     * list语法糖为flag获取参数
     */
    private function getListParamsForFlag()
    {
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');

        $product = $this->getOneProductByCondition(compact('product_id'));
        $account = $this->getOneAccountByCondition(compact('account_id'));
        return [$product_id, $product['product_key'], $account['apikey']];
    }

    /**
     * after参数
     */
    private function getAfterParamsForFlag()
    {
        $daily_limit = I('post.daily_limit', '', 'trim');
        $month_limit = I('post.month_limit', '', 'trim');
        $year_limit = I('post.year_limit', '', 'trim');
        $total_limit = I('post.total_limit', '', 'trim');
        return compact('daily_limit', 'month_limit', 'year_limit', 'total_limit');
    }

    /**
     * before参数
     * @return array
     */
    private function getBeforeParamsForFlag()
    {
        $product_id = I('post.product_id');
        $account_id = I('post.account_id');
        $account_product = $this->getOneAccountProductByCondition(compact('product_id', 'account_id'));
        return arrOnly($account_product, ['daily_limit', 'month_limit', 'year_limit', 'total_limit']);
    }

    /**
     * log
     * @throws \Exception
     */
    protected function logUpdate()
    {
        // 获取记录的详细内容
        $log_info = $this->genLogContentForUpdate();
        HandlerLog::log($log_info);
    }

    /**
     * 获取记录的详细内容
     */
    protected function genLogContentForUpdate()
    {
        // 记录的详细内容
        $product_after_update = $this->getDataAfterUpdate();
        $product_before_update = $this->product_before_update;
        $approval_token = I('post.approval_token');
        if($approval_token){
            $mod = new Approval();
            $username = $mod->getApplicantById($approval_token);
        }else{
            $username = session(C('LOGIN_SESSION_NAME'));
        }
        return [
            'handle_type' => 'update',
            'description' => '更新产品日志',
            'content' => compact('product_before_update', 'product_after_update'),
            'handle_user' => $username,
            'handle_result' => 1
        ];
    }

    public function getDataAfterUpdate()
    {
        $product_id = I('post.product_id');
        $account_id = I('post.account_id');
        return $this->getOneAccountProductByCondition(compact('account_id', 'product_id'));
    }

    public function getDataBeforeUpdate()
    {
        return  $this->product_before_update;
    }

    /**
     * 更新产品
     * @throws \Exception
     */
    protected function updateProductDo()
    {
        // 如果是301 或者 302需要特殊处理
        $product_id = $this->postKey('product_id', '', 'trim');
        if (in_array($product_id, [301, 302])) {
            $this->updateCrawlerProductDo();
            return;
        } elseif ($product_id == 210) {
            CuiShouFenRepository::build()->updateProduct($this->genParamsForUpdate(), $this->genDataParamsForUpdate());
            return;
        } else if($product_id == 1000) {
        	CuiShouFenRepository1000::build()->updateProduct($this->genParamsForUpdate(), $this->genDataParamsForUpdate());
        	return;
		} elseif ($product_id==615) {
            GoldShieldRepository::build()->updateProduct($this->genParamsForUpdate(), $this->genDataParamsForUpdate());
            return;
        }

        // 更新普通产品
        $this->updateCommonProductDo();

        // 检查父产品是否有更新要求，更新父产品 比如200产品的截止时间
        $this->updateFatherProduct();
    }

    
    protected function updateFatherProduct()
    {   
        $product_id = $this->postKey('product_id', '', 'trim');
        $father_id  = (new ProductModel())->where(compact('product_id'))->getField('father_id');
        if(200==$father_id){
            // 更新账号下 200这个产品的截止时间
            $product_id =  $father_id;
            $account_id = I('post.account_id',0);
            if(empty($product_id)||empty($account_id)){
                return false;
            }
            $fatherAccount = $this->getOneAccountProductByCondition(compact('account_id', 'product_id'));
            
            // 获取最大子产品的截止日期 可能延长 可能缩短
            $max_child_endtime = $this->maxChildProductEndtime($account_id,$product_id );
            if($max_child_endtime>0){
                $this->updateDo(compact('account_id', 'product_id'),['end_time'=>$max_child_endtime]);
            } else {
                //TODO 唯一可用产品被禁用 讨论暂不处理
            }
        }
    }

    public function maxChildProductEndtime($account_id,$father_id=200)
    {
        $where['father_id'] = $father_id;
        $productRepository = new ProductRepository();
		$data              = $productRepository->data($where);
        
		$childIds              = array_column($data, 'product_id');
        return (new AccountProductModel())->where([
            'account_id' => $account_id,
            'product_id' => ['IN',$childIds],
            'status'     =>1,   // 启用产品 
        ])->max('end_time');

    }

    /**
     * 更新301或者302
     * @throws \Exception
     */
    protected function updateCrawlerProductDo()
    {
        // 更新301或者302产品
        $this->updateCrawlerProductForSpecial();

        // 更新报告产品
        $this->updateReportProductForSpecial();
    }

    /**
     * 生成报告产品
     * @throws \Exception
     */
    private function updateReportProductForSpecial()
    {
        // 生成参数
        $params = $this->getReportProductParamsForSpecial();
        $params['product_id'] = $this->product_report_id;

        // 更新产品
        $this->updateOrCreateReportProduct($params);
    }

    /**
     * 更新产品
     * @param array $params
     * @throws \Exception
     */
    private function updateOrCreateReportProduct(array $params)
    {
        // 条件
        $account_id = $this->postKey('account_id', '', 'trim');
        $product_id = $this->product_report_id;
        $where = compact('product_id', 'account_id');

        // 更新
        $determine_exists = $this->getOneAccountProductByCondition($where);
        if ($determine_exists) {
            $this->updateReportProduct(['data' => $params['data']], $where);
            return;
        }

        // 新建
        $this->createReportProduct($params);
    }

    /**
     * 新建
     * @param array $params
     * @throws \Exception
     */
    private function createReportProduct(array $params)
    {
        $create_result = (new AccountProductModel())->add($params);

        if ($create_result === false) {
            $msg = '更新爬虫产品的时候，伴生运营商报告产品生成失败';
            $this->wehcatException($msg . ' msg :' . json_encode(compact('account_id', 'product_id', 'params')));
            throw new \Exception($msg);
        }

        // log
        $this->logCreate($create_result);
    }


    /**
     * 记录create log
     * @param integer $id
     * @throws \Exception
     */
    private function logCreate($id)
    {
        $product_new_create = $this->getOneAccountProductByCondition(compact('id'));

        $log_info = [
            'handle_type' => 'create',
            'description' => '新增产品日志',
            'content' => compact('product_new_create'),
            'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: 'system',
            'handle_result' => 1
        ];
        HandlerLog::log($log_info);
    }

    /**
     * 更新报告
     * @param array $params
     * @param array $where
     * @throws \Exception
     */
    private function updateReportProduct(array $params, array $where)
    {
        // 旧版数据
        $product_old = $this->getOneAccountProductByCondition($where);

        $update_result = (new AccountProductModel())->where($where)
            ->save($params);

        if ($update_result === false) {
            $msg = '更新爬虫产品的时候，伴生运营商报告产品更新时候';
            $this->wehcatException($msg . ' msg :' . json_encode(compact('account_id', 'product_id', 'params')));
            throw new \Exception($msg);
        }

        // log
        $product_new = $this->getOneAccountProductByCondition($where);
        $this->logCommon(compact('product_new', 'product_old'));
    }

    /**
     * 生成参数
     * @return array
     */
    private function getReportProductParamsForSpecial()
    {
        // 通用参数
        $data_common = $this->getCommonParamsForSpecial();

        // data 参数
        $data_report = $this->getReportParamsForSpecial();

        return array_merge($data_common, $data_report);
    }

    /**
     * 报告更新方法
     * @return array
     */
    private function getReportParamsForSpecial()
    {
        // 获取report data的配置
        $data_json = $this->getReportConfig();
        $data_post = I('post.data');

        //如果授权状态回调地址、报告状态回调地址两个地址相同时
        if (trim($data_post['authorize_notify_url']) == trim($data_post['report_notify_url']) && !empty(trim($data_post['authorize_notify_url']))) {
            $report_notify_url = trim($data_post['report_notify_url']);
            $data_post['report_notify_url'] = strstr($report_notify_url, '?')?$report_notify_url . '&yulore_flag=report' : $report_notify_url . '?yulore_flag=report';
        }
        
        // data数据可能补全, eg:  H5配置-必填字段在不选择的时候是这样的
        $data = array_reduce($data_json, function ($carry, $item) use ($data_post) {
            $name = $item['name'];
            $carry[$name] = array_key_exists($name, $data_post) ? $data_post[$name] : ($item['type'] == 4 ? [] : '');
            return $carry;
        }, []);

        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 更新报告产品
     * @throws \Exception
     */
    private function updateCrawlerProductForSpecial()
    {
        // 更新参数
        $params = $this->getCrawlerProductParamsForSpecial();
        $params['product_id'] = I('post.product_id', '', 'trim');

        // 更新产品
        $this->updateCrawlerProduct($params);
    }

    /**
     * 更新产品
     * @param array $params
     * @throws \Exception
     */
    protected function updateCrawlerProduct(array $params)
    {
        $where = arrOnly(I('post.'), ['account_id', 'product_id']);

        // 更新之前的数据
        $product_old = $this->getOneAccountProductByCondition($where);

        $update_result = (new AccountProductModel())->where($where)
            ->save($params);
        if ($update_result === false) {
            $msg = '更新邦秒爬失败';
            $this->wehcatException($msg . ' msg：' . json_encode(compact('where', 'params')));
            throw new \Exception($msg);
        }

        // log
        $product_new = $this->getOneAccountProductByCondition($where);
        $this->logCommon(compact('product_old', 'product_new'));
    }

    /**
     * 记录create log
     * @param array $content
     * @throws \Exception
     */
    private function logCommon(array $content)
    {
        $log_info = [
            'handle_type' => 'update',
            'description' => '更新产品日志',
            'content' => $content,
            'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: 'system',
            'handle_result' => 1
        ];
        HandlerLog::log($log_info);
    }

    /**
     * 为301 or 302 更新参数
     * @return array
     * @throws \Exception
     */
    private function getCrawlerProductParamsForSpecial()
    {
        // 通用参数
        $params_common = $this->getCommonParamsForSpecial();

        // data 参数
        $data_crawler = $this->getCrawlerDataForSpecial();

        return array_merge($params_common, $data_crawler);
    }

    /**
     * 为301 302更新data参数
     * @throws \Exception
     */
    private function getCrawlerDataForSpecial()
    {
        $data_post = I('post.data');
        $data_crawler = $this->getCrawlerConfig();

        //如果授权状态回调地址、报告状态回调地址两个地址相同时
        if (trim($data_post['authorize_notify_url']) == trim($data_post['report_notify_url']) && !empty(trim($data_post['authorize_notify_url']))) {
            $authorize_notify_url = trim($data_post['authorize_notify_url']);
            $data_post['authorize_notify_url'] = strstr($authorize_notify_url, '?')?$authorize_notify_url . '&yulore_flag=report' : $authorize_notify_url . '?yulore_flag=auth';
        }

        // data数据可能补全
        $data = array_reduce($data_crawler, function ($carry, $item) use ($data_post) {
            $name = $item['name'];
            $carry[$name] = array_key_exists($name, $data_post) ? $data_post[$name] : ($item['type'] == 4 ? [] : '');
            return $carry;
        }, []);


        // 不同产品,加工一波
        $data = $this->tidyDataForSpecialProduct($data);

        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }


    /**
     * 获取爬虫的配置
     * @throws \Exception
     */
    private function getCrawlerConfig()
    {
        $product_id = I('post.product_id', '', 'trim');
        $product_crawler = $this->getOneProductByCondition(compact('product_id'));
        return json_decode($product_crawler['data'], true);
    }

    /**
     * 通用参数
     * @return array
     */
    private function getCommonParamsForSpecial()
    {
        // 更新产品基本参数
        $limit_base = $this->genParamsBaseForSpecial();

        // 基础参数
        $limit_time = $this->genTimeParamsForSpecial();
        return array_merge($limit_base, $limit_time);
    }

    /**
     * 开通产品更新其他基础限制的参数
     * @return array
     */
    protected function genTimeParamsForSpecial()
    {
        $end_time = I('post.end_time', '', 'strtotime');
        $end_time += 86399;
        $update_at = time();
        return compact('end_time', 'update_at');
    }

    /**
     * 更新产品更新限制的参数
     * @return array
     */
    protected function genParamsBaseForSpecial()
    {
        $params_post = I('post.');
        $params_post['limit_start_date'] = I('post.limit_start_date')?I('post.limit_start_date'):null;
        $list_params = [
            'total_limit', 'daily_limit', 'month_limit', 'year_limit', 'concurrency',
            'account_id', 'status', 'contract_status', 'use_type', 'limit_start_date'
        ];

        return arrOnly($params_post, $list_params);
    }

    /**
     * 更新普通的产品
     * @throws \Exception
     */
    private function updateCommonProductDo()
    {
        // 条件
        $where = $this->genConditionForUpdate();

        // 更新参数
        $params = $this->genParamsForUpdate();

        // 更新
        $this->updateDo($where, $params);

        // log
        $this->logUpdate();
    }

    /**
     * 更新
     * @param array $where
     * @param array $params
     */
    protected function updateDo(array $where, array $params)
    {
        (new AccountProductModel())->where($where)
            ->save($params);
    }

    /**
     * 更新产品的条件
     * @return array
     */
    protected function genConditionForUpdate()
    {
        $product_id = I('post.product_id');
        $account_id = I('post.account_id');
        return compact('account_id', 'product_id');
    }

    /**
     * 为更新产品更新参数
     * @return array
     */
    public function genParamsForUpdate()
    {
        // 更新data参数
        $params_data = $this->genDataParamsForUpdate();

        // 更新基本参数
        $params_base = $this->genBaseParamsForUpdate();

        return array_merge($params_base, $params_data);
    }

    /**
     * 更新公用的参数
     * @return array
     */
    protected function genBaseParamsForUpdate()
    {
        // 更新产品基本参数
        $limit_base = $this->genParamsBaseForUpdate();

        // 基础参数
        $limit_time = $this->genTimeParamsForUpdate();
        return array_merge($limit_base, $limit_time);
    }

    /**
     * 更新产品更新其他基础限制的参数
     * @return array
     */
    protected function genTimeParamsForUpdate()
    {
        $end_time = I('post.end_time', '', 'strtotime');
        $end_time += 86399;
        $update_at = time();
        return compact('end_time', 'update_at');
    }

    /**
     * 更新产品更新限制的参数
     * @return array
     */
    protected function genParamsBaseForUpdate()
    {
        $params_post = I('post.');
        $params_post['limit_start_date'] = I('post.limit_start_date')?I('post.limit_start_date'):null;
        $list_params = [
            'total_limit', 'daily_limit', 'month_limit', 'year_limit', 'concurrency',
            'account_id', 'product_id', 'status', 'contract_status', 'use_type', 'limit_start_date'
        ];

        return arrOnly($params_post, $list_params);
    }

    /**
     * 更新data参数
     * @return array
     */
    protected function genDataParamsForUpdate()
    {
        // 更新通用的data
        $data = $this->genCommonDataForUpdate();

        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 各个产品的特殊处理
     * @param array $data
     * @return array
     */
    protected function tidyDataForSpecialProduct(array $data)
    {
        $product_id = I('post.product_id');
        switch ($product_id) {
            case 301:
                return $this->tidyDataForH5($data);
                break;
        }
        return $data;
    }

    /**
     * H5邦秒爬需要特殊处理的地方
     * @param array $data
     * @return array
     */
    protected function tidyDataForH5(array $data)
    {
        // 数值型转int类型
        $data = $this->tidyTypeDataForH5($data);

        // H5必填紧急联系人限制
        $data = $this->tidyEmergencyDataForH5($data);

        // 协议处理
        return $this->tidyProtocolDataForH5($data);
    }

    /**
     * H5必填紧急联系人限制
     * @param array $data
     * @return array
     */
    private function tidyEmergencyDataForH5(array $data)
    {
        // 如果没有介入联系人页面，则重置为[]
        $data_post = I('post.data');
        if ($data_post['contactor_page'] === 'N') {
            $data['emergency_contact_detail_limits'] = [];
            return $data;
        }

        // 整合各个限制
        $emergency_contact_detail_limits = [];
        array_walk($data_post, function ($item, $key) use (&$emergency_contact_detail_limits) {
            if (strpos($key, 'emergency_contact_detail_limits') !== false) {
                array_push($emergency_contact_detail_limits, $item);
            }
        });

        $data['emergency_contact_detail_limits'] = $emergency_contact_detail_limits;
        return $data;
    }

    /**
     * 301产品协议处理
     * @param array $data
     * @return array
     */
    protected function tidyProtocolDataForH5(array $data)
    {
        // 如果是默认协议 则协议内容一律为空
        $data_post = I('post.data');
        $protocol_default = $data_post['protocol_default'];
        if ($protocol_default === 1) {
            $data['protocol_content'] = '';
        }
        return $data;
    }

    /**
     * 301产品数值型转int类型
     * @param array $data
     * @return array
     */
    protected function tidyTypeDataForH5(array $data)
    {
        $list_sections = [
            'emergency_contact_max_number',
            'effective_authorization_time',
        ];

        array_walk($list_sections, function ($section) use (&$data) {
            $data[$section] = $data[$section] === '' ? '' : (int)$data[$section];
        });
        return $data;
    }

    protected function genCommonDataForUpdate()
    {
        $data = I('post.data');
        $data_json = json_decode(I('post.data_json', '', 'trim'), true);

        //如果是号码分等级区间配置，需要单独逻辑整理数据
        $data = $this->checkScoreGradeLimit($data);
        // data数据可能补全, eg:  H5配置-必填字段在不选择的时候是这样的
        $result = array_reduce($data_json, function ($carry, $item) use ($data) {
            $name = $item['name'];
            $carry[$name] = array_key_exists($name, $data) ? $data[$name] : ($item['type'] == 4 ? [] : '');
            return $carry;
        }, []);
        if(!empty($data) && is_array($data)){
            $result = $result + $data;
        }

        return $result;
    }
    /**
     * 处理号码分等级字段逻辑
     * @param $data
     * @return mixed
     */
    protected function checkScoreGradeLimit($data){
        if (!isset($data['scoreGradeLimit'])) {return $data;}
        //如果是号码分值等级，需要修改数据格式
        $sourceLimit = $data['scoreGradeLimit'];
        $gradeLimit = [];
        sort($sourceLimit);
        foreach ($sourceLimit as $key => $limit) {
            $gradeLimit[] = [
                'grade' => $key + 1,
                'min' => $key ? $sourceLimit[$key-1] + 1 : 0,
                'max' => intval($limit)
            ];
        }
        $data['scoreGradeLimit'] = $gradeLimit;
        return $data;
    }
    /**
     * 为更新产品限定访问的参数
     * @throws \Exception
     */
    protected function validateParamsForUpdate()
    {
        // 检测必选条件是否存在
        $this->verifyParamsExistsForUpdate();

        //检测产品是否下架
        $this->verifyProductStatus();

        // 检测基本的参数
        $this->verifyBaseParamsForUpdate();

        // 检测是否必选
        $this->verifyRequiredParamsForUpdate();

        // 特定产品的校验
        $this->verifyOptionForSpecialProduct();
    }

    /**
     * 特定产品的校验
     * @throws \Exception
     */
    protected function verifyOptionForSpecialProduct()
    {
        $product_id = I('post.product_id', '', 'trim');
        switch ($product_id) {
            case 301:
                // H5邦秒爬
                $this->verifyItemForH5Crawler();
                break;
            case 302:
                // api 邦秒爬
                $this->validateItemForApiCrawler();
                break;
            case 102:
                // 检查报告的参数
                $this->validateItemForReport();
                break;
            case 210 :
                CuiShouFenRepository::build()->storeProductValid();
                break;
            case 615:
                GoldShieldRepository::build()->storeProductValid();
                break;
        }
        $father_id  = (new ProductModel())->where(compact('product_id'))
            ->field(['father_id'])
            ->select();
        if ($father_id[0]['father_id'] == 1100) {
            $this->verifyItemForHmf();
        }
    }

    /**
     * 为邦秒爬API校验参数
     * @throws \Exception
     */
    private function validateItemForApiCrawler()
    {
        // 检查授权状态推送地址
        $this->validateAuthorizeUrlForApiCrawler();

        // 检查报告的参数
        $this->validateItemForReport();
    }

    /**
     * 更新报告时检查报告状态推送地址
     * @throws \Exception
     */
    private function validateReportForReport()
    {
        $data = I('post.data');
        $report_notify_url = array_key_exists('report_notify_url', $data) ? $data['report_notify_url'] : '';

        // 检查报告状态推送地址
        if ($report_notify_url && validateUrl($report_notify_url) === false) {
            throw new \Exception('报告状态推送地址不合法');
        }
    }

    /**
     * 检查返回邦信分详单版V1
     * @throws \Exception
     */
    private function validateNeedDunningForReport()
    {
        $data = I('post.data');
        $need_dunning = $data['need_dunning'];
        $apikey = array_key_exists('cuishou_apikey', $data) ? trim($data['cuishou_apikey']) : '';
        $appsecret = array_key_exists('cuishou_appsecret', $data) ? trim($data['cuishou_appsecret']) : '';

        // 如果没有返回详单版
        if ($need_dunning != 1) {
            return;
        }

        // 检查选中的apikey api secret是否有对应的邦信分详单版V1产品
        $this->determineHasOneCuishouProduct($apikey, $appsecret);
    }

    /**
     * 检索输入的apikey appsecret是否合法
     * @param string $apikey
     * @param string $appsecret
     * @throws \Exception
     */
    private function determineHasOneCuishouProduct($apikey, $appsecret)
    {
        // 是否存在账号
        $account = $this->getOneAccountByCondition(compact('apikey', 'appsecret'));
        if (!$account) {
            throw new \Exception('催收分析APPKEY && 催收分析APPsercert不存在对应的账号');
        }

        // 账号下是否存在邦信分详单版V1的产品
        $account_id = $account['account_id'];
        $product_id = $this->product_cuishou_id;
        $product = $this->getOneAccountProductByCondition(compact('account_id', 'product_id'));
        if (!$product) {
            throw new \Exception('催收分析APPKEY && 催收分析APPsercert不存在对应的产品');
        }
    }

    /**
     * 获取一个账户
     * @param array $where
     * @param string $field
     * @return array
     */
    private function getOneAccountByCondition(array $where, $field = '*')
    {
        return (new AccountModel())->where($where)
            ->field($field)
            ->find();
    }

    /**
     * 授权状态推送地址
     * @throws \Exception
     */
    private function validateAuthorizeUrlForApiCrawler()
    {
        $data = I('post.data');
        $authorize_notify_url = array_key_exists('authorize_notify_url', $data) ? trim($data['authorize_notify_url']) : '';

        // 有填充且填充不是合法的URL则抛出异常
        if ($authorize_notify_url !== '' && validateUrl($authorize_notify_url) === false) {
            throw new \Exception('授权状态推送地址不合法');
        }
    }


    /**
     * 选中加密推送详单 则检查详单推送地址
     * @throws \Exception
     */
    private function validatePushDetailForReport()
    {
        $data = I('post.data');
        $is_push_detail = $data['is_push_detail'];
        $notify_url = array_key_exists('notify_url', $data) ? trim($data['notify_url']) : '';

        // 如果没有选中推送
        if ($is_push_detail != 1) {
            return;
        }
        if (validateUrl($notify_url) === false) {
            throw new \Exception('详单推送地址不合法');
        }
    }

    /**
     * h5邦秒爬检测特定条件
     * @throws \Exception
     */
    protected function verifyItemForH5Crawler()
    {
        // 检查h5
        $this->validateItemForH5();

        // 检查报告
        $this->validateItemForReport();
    }

    /**
     * 号码分检测特定条件
     * @throws \Exception
     */
    protected function verifyItemForHmf()
    {
        $data = I('post.data');
        //如果开启了分支等级展示，但没有配置等级区间
        if (!array_key_exists('showScoreGrade', $data)) {
            throw new \Exception('请选择是否展示分值等级');
        }
        if ($data['showScoreGrade'] == 1) {
            if ((count($data['scoreGradeLimit']) == 1) && ($data['scoreGradeLimit'][0] <= 0)) {
                throw new \Exception('请填写分值等级区间');
            }
        }
    }

    /**
     * 检查报告
     * @throws \Exception
     */
    private function validateItemForReport()
    {
        // 检查加密推送详单
        $this->validatePushDetailForReport();

        // 检查返回邦信分详单版V1
        $this->validateNeedDunningForReport();

        // 更新报告时检查报告状态推送地址
        $this->validateReportForReport();
    }

    /**
     * 检查h5
     * @throws \Exception
     */
    private function validateItemForH5()
    {
        // 限定必须传入是否输入了联系人页面
        $this->limitContactorPageForH5();

        // 校验H5授权链接失效限制时间
        $this->verifyTimeEffectiveForH5();

        // 校验H5紧急联系人
        $this->verifyEmergencyContactForH5();

        // 地址系列的校验
        $this->verifyUrlForH5();

        // 重定向地址域名白名单
        $this->verifyDomainForH5();

        // H5配置-显示字段和必选字段
        $this->verifyShowAndRequiredFieldForH5();

        // 校验授权协议
        $this->verifyProtocolContentForH5();
    }

    /**
     * 校验授权协议
     * @throws \Exception
     */
    protected function verifyProtocolContentForH5()
    {
        $data = I('post.data');
        if (!array_key_exists('protocol_default', $data) || !array_key_exists('protocol_content', $data)) {
            throw new \Exception('缺少协议的相关配置');
        }
        $protocol_default = $data['protocol_default'];
        $protocol_content = trim($data['protocol_content']);
        if ($protocol_default == 1 && $protocol_content && $protocol_content !== '<p><br></p>' && $protocol_content !== htmlentities('<p><br></p>')) {
            throw new \Exception('选择默认协议的时候, 不可以填写协议内容');
        }

        // wangEditor 编辑框默认带了一些符号
        if ($protocol_default != 1 && $protocol_content === htmlentities('<p><br></p>')) {
            throw new \Exception('自定义协议的时候， 协议内容不可以为空');
        }
    }


    /**
     * H5配置-显示字段和必选字段
     * @throws \Exception
     */
    protected function verifyShowAndRequiredFieldForH5()
    {
        $data = I('post.data');
        if ($data['contactor_page'] === 'N') {
            return true;
        }

        if (!array_key_exists('ui_proposer_show_fields', $data)) {
            throw new \Exception('显示字段不可以为空');
        }

        if (!array_key_exists('ui_proposer_required_fields', $data)) {
            throw new \Exception('必选字段不可以为空');
        }

        $ui_proposer_show_fields = $data['ui_proposer_show_fields'];
        $ui_proposer_required_fields = $data['ui_proposer_required_fields'];
        if (array_diff($ui_proposer_required_fields, $ui_proposer_show_fields)) {
            throw new \Exception('必选字段必须是显示字段');
        }
    }

    /**
     * 重定向地址域名白名单
     * @throws \Exception
     */
    protected function verifyDomainForH5()
    {
        $data = I('post.data');

        if (!array_key_exists('redirect_url_domain', $data) || !trim($data['redirect_url_domain'])) {
            return true;
        }
        $redirect_url_domain = trim($data['redirect_url_domain']);

        // 如果没有限定 则pass
        if (!$redirect_url_domain) {
            return true;
        }

        $list_domain = explode(',', $redirect_url_domain);
        array_walk($list_domain, function ($domain) {
            $domain = trim($domain);
            // 检索纯数字
            if (is_numeric(str_replace('.', '', $domain))) {
                throw new \Exception('重定向地址域名白名单不合法 : ' . $domain);
            }
            // 正常检索主机IP
            if (filter_var(gethostbyname($domain), FILTER_VALIDATE_IP) === false) {
                throw new \Exception('重定向地址域名白名单不合法 : ' . $domain);
            }
        });
    }


    /**
     * 地址系列的校验
     * @throws \Exception
     */
    protected function verifyUrlForH5()
    {
        $list_section = [
            'notify_url' => '详单推送地址',
            'authorize_notify_url' => '授权状态回调地址',
            'close_redirect_url' => '重定向地址',
        ];

        $data = I('post.data');

        array_walk($list_section, function ($tip, $key_url) use ($data) {
            // 如果不存在key pass
            if (!array_key_exists($key_url, $data) || trim($data[$key_url]) === '') {
                return true;
            }

            $url = trim($data[$key_url]);
            if (validateUrl($url) === false) {
                throw new \Exception($tip . ' 不是一个合法的url');
            }
        });
    }

    /**
     * 校验H5紧急联系人
     * @throws \Exception
     */
    protected function verifyEmergencyContactForH5()
    {
        // 最多紧急联系人数量
        $this->verifyLimitMaxForH5();

        // 紧急联系人总量限制
        $this->verifyEmergencyLimitForH5();
    }

    /**
     * 紧急联系人总量限制
     * @throws \Exception
     */
    protected function verifyEmergencyLimitForH5()
    {
        $data = I('post.data');
        if ($data['contactor_page'] === 'N') {
            return;
        }

        // 紧急联系人限制的条数
        $count_emergency_contact_detail_limits = 0;
        array_walk($data, function ($item, $key) use (&$count_emergency_contact_detail_limits) {
            if (strpos($key, 'emergency_contact_detail_limits') !== false) {
                $count_emergency_contact_detail_limits++;
            }
        });

        // 限制最大
        $emergency_contact_max_number = $data['emergency_contact_max_number'];
        if ($emergency_contact_max_number < $count_emergency_contact_detail_limits) {
            throw new \Exception('H5必填紧急联系人限制条数必须不大于H5最多紧急联系人数量');
        }
    }

    /**
     * 最多紧急联系人数量
     * @throws \Exception
     */
    protected function verifyLimitMaxForH5()
    {
        $data = I('post.data');
        if ($data['contactor_page'] === 'N') {
            return true;
        }

        if (!array_key_exists('emergency_contact_max_number', $data)) {
            throw new \Exception('请填写最多紧急联系人数量');
        }

        $emergency_contact_max_number = $data['emergency_contact_max_number'];
        if (!is_numeric($emergency_contact_max_number)) {
            throw new \Exception('最多紧急联系人数量必须是0--15的正整数');
        }

        $emergency_contact_max_number = (int)$emergency_contact_max_number;
        if ($emergency_contact_max_number < 0 || $emergency_contact_max_number > 15) {
            throw new \Exception('最多紧急联系人数量必须是0--15的正整数');
        }
    }

    /**
     * 校验H5授权链接失效限制时间
     * @throws \Exception
     */
    protected function verifyTimeEffectiveForH5()
    {
        $data = I('post.data');
        // 如果这个字段是必须的有填充 则必须是1-36之间的正整数
        if (array_key_exists('effective_authorization_time', $data) && $data['effective_authorization_time']) {

            $effective_authorization_time = $data['effective_authorization_time'];
            if (!is_numeric($effective_authorization_time)) {
                throw new \Exception('H5授权链接失效限制时间必须是个整数');
            }

            $effective_authorization_time = (int)$effective_authorization_time;
            if ($effective_authorization_time != -1 && ($effective_authorization_time < 1 || $effective_authorization_time > 36)) {
                throw new \Exception('H5授权链接失效限制时间允许范围是1--36的正整数(不限制-1)');
            }
        }
    }

    /**
     * 限定必须传入是否输入了联系人页面
     * @throws \Exception
     */
    private function limitContactorPageForH5()
    {
        $data = I('post.data');
        // 如果没有介入紧急联系人 则不检测最多紧急联系人数量
        if (!array_key_exists('contactor_page', $data)) {
            throw new \Exception('请选定是否需要介入联系人页面contactor_page');
        }
    }

    /**
     * 检测必须项目
     * @throws \Exception
     */
    protected function verifyRequiredParamsForUpdate()
    {
        $data_json = json_decode(I('post.data_json', '', 'trim'), true);
        $data = I('post.data', '');
        $product_id = I('post.product_id', '');
        //210产品开通子产品时，需要data_json中product_ids和data[product_ids]两者结合，1000产品只在data[product_ids]中
        if($product_id == 1000){
            if(!isset($data['product_ids'])){
                throw new \Exception('请选择开通的子产品');
            }else{
                //检验产品是否下架
                $this->verifyMultipleProductStatus($data['product_ids']);
            }
        }

        array_walk($data_json, function ($item) use ($data) {
            // 校验必须填写
            $this->verifyRequireForCommon($item, $data);

            // 如果是product_id、out_fields字段，必须选择一个产品开通且产品不是下架产品
            // 开通的子产品的字段在邦信分中字段为product_ids,在号码风险等级产品则叫out_fields
            if(in_array($item['name'], ['product_ids', 'out_fields'])){
                if(!isset($data[$item['name']])){
                    throw new \Exception($item['cn_name'] . '未填写');
                }else{
                    //检验产品是否下架
                    $this->verifyMultipleProductStatus($data[$item['name']]);
                }
            }
        });
    }

    /**
     * 检测公用的部分
     * @param array $item 单个的单元
     * @param array $data 结果
     * @throws \Exception
     */
    protected function verifyRequireForCommon(array $item, array $data)
    {
        // 检查是否必须
        if ($item['is_need'] && (!array_key_exists($item['name'], $data) || ($data[$item['name']] === ''))) {
            throw new \Exception($item['cn_name'] . '未填写');
        }
    }

    /**
     * 检测基本的参数
     * @throws \Exception
     */
    protected function verifyBaseParamsForUpdate()
    {
        // 截至时间
        $this->verifyEndTimeForUpdate();

        // 限额
        $this->verifyLimitNumberForUpdate();

        // 秒并发
        $this->verifyConcurrencyForUpdate();

        // 产品调用类型限定
        $this->validateUseTypeForUpdate();
    }

    /**
     * 产品调用类型限定
     * @throws \Exception
     */
    private function validateUseTypeForUpdate()
    {
        $use_type = I('post.use_type', '', 'trim');
        if (!in_array($use_type, [1, 2, '1', '2'])) {
            throw new \Exception('产品调用类型限定只可以1,2中选择');
        }
    }

    /**
     * 校验秒并发
     * @throws \Exception
     */
    protected function verifyConcurrencyForUpdate()
    {
        $concurrency = I('post.concurrency', '', 'trim');
        if (!$concurrency) {
            throw new \Exception('请输入秒并发 ' . $concurrency);
        }

        if ($concurrency > 100 || $concurrency <= 0 || !is_numeric($concurrency)) {
            throw new \Exception('请输入合法的秒并发(0--100)');
        }
    }

    /**
     * 更新产品检测限额
     * @throws \Exception
     */
    protected function verifyLimitNumberForUpdate()
    {
        $daily_limit = I('post.daily_limit', '', 'trim');
        $month_limit = I('post.month_limit', '', 'trim');
        $year_limit = I('post.year_limit', '', 'trim');
        $total_limit = I('post.total_limit', '', 'trim');

        // 限额是否存在
        if (!$total_limit && $total_limit != 0 || $total_limit < -1 || !is_numeric($total_limit)) {
            throw new \Exception('请填写合法的限额总量');
        }
        if (!$daily_limit && $daily_limit != 0 || $daily_limit < -1 || !is_numeric($daily_limit)) {
            throw new \Exception('请填写合法的日限额');
        }
        if (!$month_limit && $month_limit != 0 || $month_limit < -1 || !is_numeric($month_limit)) {
            throw new \Exception('请填写合法的月限额');
        }
        if (!$year_limit && $year_limit != 0 || $year_limit < -1 || !is_numeric($year_limit)) {
            throw new \Exception('请填写合法的年限额');
        }

        // 限额之间的
        if ($month_limit != -1 && $daily_limit > $month_limit) {
            throw new \Exception('日限额必须不大于月限额');
        }

        if ($year_limit != -1 && $month_limit > $year_limit) {
            throw new \Exception('月限额必须不大于年限额');
        }

        if ($total_limit != -1 && $year_limit > $total_limit) {
            throw new \Exception('年限额必须不大于总限额');
        }
    }

    /**
     * 更新产品检查结束时间
     * @throws \Exception
     */
    protected function verifyEndTimeForUpdate()
    {
        // 如果没有输入截至时间
        $end_time = I('post.end_time', '', 'trim');
        if (!$end_time) {
            throw new \Exception('请输入合法的截至时间');
        }

        // 如果截至时间
        $end_time = strtotime($end_time);
        $today_time = strtotime(date('Y-m-d'));
        if ($end_time < $today_time) {
            throw new \Exception('请输入合法的截至时间');
        }
    }

    /**
     * 检测必选条件是否存在
     * @throws \Exception
     */
    private function verifyParamsExistsForUpdate()
    {
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $data_json = I('post.data_json', '', 'trim');
        $data = I('post.data', '');

        if (!$product_id) {
            throw new \Exception('请选择产品');
        }

        if (!$data) {
            throw new \Exception('至少要有一项当前产品的特殊配置项');
        }

        if (!$account_id) {
            throw new \Exception('account_id是必须传递的参数');
        }

        if (!$data_json) {
            throw new \Exception('data_json是必须传递的参数');
        }
    }

    /**
     * 检测产品是否下架
     * @throws \Exception
     */
    private function verifyProductStatus()
    {
        $product_id = I('post.product_id', '', 'trim');
        $where['product_id'] = ['EQ', $product_id];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $exists = $this->getOneProductByCondition($where);
        if (!$exists) {
            throw new \Exception('抱歉,该产品已经下架哦');
        }
    }

    /**
     * 检测多个产品中是否有下架产品
     * @throws \Exception
     */
    private function verifyMultipleProductStatus($product_ids = []){
        if(empty($product_ids)){
            throw new \Exception('请选择开通的子产品');
        }

        $where['product_id'] = ['IN', $product_ids];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $usableList = (new ProductModel())->where($where)
            ->field('product_id')
            ->select();

        $usableList = array_column($usableList, 'product_id');
        $diff = [];
        foreach ($product_ids as $pid){
            if(!in_array($pid, $usableList)){
                $diff[] = $pid;
            }
        }

        if(!empty($diff)){
            throw new \Exception('产品'.implode(',', $diff).'已下架哦');
        }

    }

    /**
     * 获取某个产品的data信息
     * @return array
     * @throws \Exception
     */
    public function product()
    {
        // POST访问限定
        $this->limitMethodPost();

        // 限定访问条件
        $this->verifyParamsForProduct();

        // 获取产品
        $account = $this->getAccountInfoForProduct();

        // 组装产品的data信息
        return $this->tidyDataForProduct($account);
    }

    /**
     * 组装产品的data信息
     * @param array $account
     * @return array
     */
    private function tidyDataForProduct(array $account)
    {
        // 产品信息
        $product = $this->getChooseProduct();

        // 整合
        return $this->tidyDataDo($product, $account);
    }

    /**
     * 整合产品data 信息
     * @param array $product
     * @param array $account
     * @return array
     */
    private function tidyDataDo(array $product, array $account)
    {
        $data_account = json_decode($account['data'], true);
        $data_product = json_decode($product['data'], true);

        // 补全val
        return array_map(function ($item_product) use ($data_account) {
            return $this->tidyDataItemForEdit($item_product, $data_account);
        }, $data_product);
    }

    /**
     * 整理配置项的值
     * @param array $item_product
     * @param array $data_account
     * @return array
     */
    private function tidyDataItemForEdit(array $item_product, array $data_account)
    {
        // 通用的配置整理
        $item_tidy = $this->tidyCommonDataItemForEdit($item_product, $data_account);

        // 特定产品的处理
        return $this->tidyItemForSpecialProduct($item_tidy, $data_account);
    }

    /**
     * 特定产品的处理
     * @param array $item_tidy
     * @param array $data_account
     * @return array
     */
    private function tidyItemForSpecialProduct(array $item_tidy, array $data_account)
    {
        $product_id = I('post.product_id', '', 'trim');
        switch ($product_id) {
            case 301;
                return $this->tidyItemForH5($item_tidy, $data_account);
                break;
            default :
                return $item_tidy;
        }
    }

    /**
     * 301特殊处理
     * @param array $item_tidy
     * @param array $data_account
     * @return array
     */
    private function tidyItemForH5(array $item_tidy, array $data_account)
    {
        switch ($item_tidy['type']) {
            case 8:
                // 针对多选下拉框进行特殊处理
                return $this->tidyMultipleSelectForH5($item_tidy, $data_account);
                break;
            default:
                return $item_tidy;
        }
    }

    /**
     * 对301的产品进行复选下拉框的处理
     * @param array $item_tidy
     * @param array $data_account
     * @return array
     */
    protected function tidyMultipleSelectForH5(array $item_tidy, array $data_account)
    {
        // 如果字段不是紧急联系人，则不做处理
        if ($item_tidy['name'] !== 'emergency_contact_detail_limits') {
            return $item_tidy;
        }

        // 如果数据还是新版 则直接返回
        if (!$this->isOldEmergencyForH5($data_account)) {
            $item_tidy['val'] = $data_account['emergency_contact_detail_limits'];
            return $item_tidy;
        }

        // 容器
        $list_container = [];
        array_walk($data_account, function ($val, $key) use ($list_container) {
            if (strpos($key, 'emergency_contact_detail_limits') !== false && !!$val) {
                $item = array_fill(0, $val, $this->list_emergency_map[$key]);
                array_push($list_container, $item);
            }
        });

        $item_tidy['val'] = $list_container;
        return $item_tidy;
    }

    /**
     * 判断是不是老版本紧急联系人配置
     * @param array $data_account 产品的配置
     * @return bool
     */
    private function isOldEmergencyForH5(array $data_account)
    {
        return array_key_exists('emergency_contact_detail_limits_children', $data_account);
    }

    /**
     * 通用的配置整理
     * @param array $item_product
     * @param array $data_account
     * @return array
     */
    private function tidyCommonDataItemForEdit(array $item_product, array $data_account)
    {
        $column = $item_product['name'];
        $item_product['val'] = array_key_exists($column, $data_account) ? $data_account[$column] : $item_product['default'];

        // 如果是富文本框的话 需要将html实体转乘相应的字符串
        return $this->convertEntityToCharacter($item_product);
    }

    /**
     * 将html实体转成字符串
     * @param array $item
     * @return array
     */
    protected function convertEntityToCharacter(array $item)
    {
        if ((int)$item['type'] !== 6) {
            return $item;
        }
        $item['val'] = html_entity_decode($item['val']);
        return $item;
    }

    /**
     * 获取选中的产品
     * @return array
     */
    private function getChooseProduct()
    {
        // 如果选中的是301 302 则需要组合运营商报告来处理
        $product_id = I('post.product_id', '', 'trim');
        if (in_array($product_id, [301, 302])) {
            return $this->getProductDataForCrawler($product_id);
        }

        return $this->getOneProductByCondition(compact('product_id'));
    }

    /**
     * @param string $product_id
     * @return array
     */
    private function getProductDataForCrawler($product_id)
    {
        // 爬虫产品
        $product_crawler = $this->getOneProductByCondition(compact('product_id'));

        // 运营商报告产品
        $product_report = $this->getOneProductByCondition(['product_id' => $this->product_report_id]);

        // 组合data
        $data_crawler = json_decode($product_crawler['data'], true);
        $data_report = json_decode($product_report['data'], true);
        $product_crawler['data'] = json_encode(array_merge($data_crawler, $data_report), JSON_UNESCAPED_UNICODE);
        return $product_crawler;
    }

    /**
     * 获取一个产品
     * @param array $where
     * @return array
     */
    private function getOneProductByCondition(array $where)
    {
        return (new ProductModel())->where($where)
            ->find();
    }

    /**
     * 获取选中的产品
     * @return array
     */
    private function getAccountInfoForProduct()
    {
        // 如果301 302 则需要特殊处理
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        if (in_array($product_id, [301, 302])) {
            return $this->getCrawlerAccount($product_id, $account_id);
        }
        return $this->getOneAccountProductByCondition(compact('product_id', 'account_id'));
    }

    /**
     * 获取爬虫现在的产品数值
     * @param integer $product_id
     * @param  integer $account_id
     * @return array
     */
    private function getCrawlerAccount($product_id, $account_id)
    {
        // 爬虫的数据
        $product_crawler = $this->getOneAccountProductByCondition(compact('product_id', 'account_id'));

        // 报告的数据
        $product_id = $this->product_report_id;
        $product_report = $this->getOneAccountProductByCondition(compact('product_id', 'account_id'));

        // 组合data
        $data_crawler = json_decode($product_crawler['data'], true);
        $data_report = $product_report ? json_decode($product_report['data'], true) : [];
        $product_crawler['data'] = json_encode(array_merge($data_crawler, $data_report), JSON_UNESCAPED_UNICODE);

        return $product_crawler;
    }

    /**
     * @param array $where
     * @return array
     */
    private function getOneAccountProductByCondition(array $where)
    {
        return (new AccountProductModel())->where($where)
            ->find();
    }

    /**
     * 限定访问的参数
     * @throws \Exception
     */
    private function verifyParamsForProduct()
    {
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        if (!$product_id || !$account_id) {
            throw new \Exception('product_id && account_id是必传参数');
        }
    }
}
