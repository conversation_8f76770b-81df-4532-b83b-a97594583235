<?php

namespace Api\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;

class CuishouPrivateStatRepository extends BaseRepository
{
    /*
     * 产品ID
     * */
    protected $product_id = 501;

    /*
     * 基本的列表统计单元
     * */
    protected $base_stat_item = [
        "sum_counts" => "0",
        "sum_valid_counts" => "0",
        "min_status" => 1
    ];

    /*
    * 基本的详情统计单元
    * */
    protected $base_detail_item = [
        "sum_counts" => "0",
        "sum_valid_counts" => "0",
        "min_status" => 1
    ];

    /*
    * 数值型字段
    * */
    protected $list_field_number = [
        'sum_counts', 'sum_valid_counts'
    ];

    /**
     * 统计详情
     * @throws \Exception
     */
    public function detailStat()
    {
        // 限定访问类型
        $this->limitRequestMethod();

        // 检查参数
        $this->verifyParamsForDetail();

        // 获取统计信息
        $list_stat_info = $this->getStatInfoForDetail();

        // 追加总计数据
        $list_stat_info = $this->appendTotalToStat($list_stat_info);

        // 格式化数据
        return $this->formatData($list_stat_info);
    }

    /**
     * 格式化数据
     * @param array $list_stat
     * @return array
     */
    protected function formatData($list_stat)
    {
        return array_map(function ($item) {
            // 格式化单元
            return $this->formatItem($item);
        }, $list_stat);

    }

    /**
     * 格式化单元
     * @param array $item_stat
     * @return array
     */
    protected function formatItem($item_stat)
    {
        array_walk($this->list_field_number, function ($field_number) use (&$item_stat) {
            $item_stat[$field_number] = $this->formatNum($item_stat[$field_number]);

        });
        return $item_stat;
    }

    /**
     * 统计排序
     * @return array
     * @throws \Exception
     */
    public function sortBy()
    {
        $response_body = $this->genParamsForPost();

        // 传递的数据
        $filter_field = $this->getValueWithDefault($response_body, 'filter_field', '');
        $filter_order = $this->getValueWithDefault($response_body, 'filter_order', '');
        $list_data = $this->getValueWithDefault($response_body, 'list_data', []);

        // 如果缺少必要元素 则不进行操作
        if (!$filter_field || !$filter_order) {
            return $list_data;
        }

        // 排序
        return $this->sortData($list_data, $filter_field, $filter_order);
    }

    /**
     * 数据排序
     * @param array $list_data 原始数据
     * @param string $filter_field 排序字段
     * @param string $filter_order 排序方式
     * @return mixed
     */
    protected function sortData($list_data, $filter_field, $filter_order)
    {
        // 总计不参与排序
        $item_total = array_shift($list_data);

        // 统计单元 && 统计单元格式化
        $list_item = array_column($list_data, $filter_field);
        $list_item = $this->formatDataForSort($list_item, $filter_field);

        // 排序
        $sort_order = strtolower($filter_order) === 'asc' ? SORT_ASC : SORT_DESC;
        array_multisort($list_item, $sort_order, SORT_NUMERIC, $list_data);

        // 总计不参与排序
        array_unshift($list_data, $item_total);
        return $list_data;
    }

    /**
     * 格式化统计元素(转成可以直接排序的数值型元素)
     * @param array $list_item
     * @param string $filter_field
     * @return array
     */
    protected function formatDataForSort($list_item, $filter_field)
    {
        $delimiter = in_array($filter_field, $this->list_field_number) ? ',' : '%';

        return array_map(function ($item) use ($delimiter) {
            // 如果统计原始是需要反格式化的化
            if (strpos($item, $delimiter) !== false) {
                $item = str_replace($delimiter, '', $item);
            }
            return $item;
        }, $list_item);
    }

    /**
     * 增加总计数据
     * @param array $list_stat_info
     * @return array
     */
    protected function appendTotalToStat($list_stat_info)
    {
        // 获取总计数据
        $list_total = $this->getTotalForDetail($list_stat_info);

        // 追加总计数据
        return $this->appendTotalToStatForDetail($list_stat_info, $list_total);
    }

    /**
     * 追加总计数据
     * @param array $list_stat_info
     * @param array $list_total
     * @return array
     */
    protected function appendTotalToStatForDetail($list_stat_info, $list_total)
    {
        array_unshift($list_stat_info, $list_total);
        return $list_stat_info;
    }

    /**
     * 获取总计数据
     * @param array $list_stat_info
     * @return array
     */
    protected function getTotalForDetail($list_stat_info)
    {
        // 容器
        $list_container = $this->base_detail_item;
        $list_container['date'] = '总计';

        array_walk($list_stat_info, function ($item_stat) use (&$list_container) {
            $list_container['sum_counts'] += $item_stat['sum_counts'];
            $list_container['sum_valid_counts'] += $item_stat['sum_valid_counts'];
            $list_container['min_status'] = $item_stat['sum_valid_counts'] === 0 ? 0 : $list_container['min_status'];
        });
        return $list_container;
    }

    /**
     * 默认排序
     * @param $list_stat_info
     * @return array
     */
    protected function soreDefaultForDetail($list_stat_info)
    {
        $list_sort_field = array_column($list_stat_info, 'sum_counts');
        array_multisort($list_sort_field, SORT_DESC, SORT_NUMERIC, $list_stat_info);
        return $list_stat_info;
    }

    /**
     * 需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = $this->genParamsForPost();
        $day_begin = date('Ymd', strtotime($request_body['time_begin']));
        $day_end = date('Ymd', strtotime($request_body['time_end']));

        // 容器 ['2018-10-01', '2018-10-02']
        $list_date = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            $date_format = date('Y-m-d', strtotime($day_end));
            array_push($list_date, $date_format);

            $day_end = date('Ymd', strtotime($day_end . '-1 day'));
        }

        return $list_date;
    }


    /**
     * 限定访问类型
     * @throws \Exception
     */
    protected function limitRequestMethod()
    {
        // 限定访问类型
        if (IS_GET) {
            throw new \Exception('请使用POST方式访问');
        }
    }

    /**
     * 获取统计信息
     * @throws \Exception
     * @return array
     */
    protected function getStatInfoForDetail()
    {
        // 条件
        $params = $this->genStatConditionForDetail();

        // 调用API
        $list_stat_info = $this->getApiDetailStatInfo($params);

        // 补全统计信息
        return $this->appendMissDayForDetail($list_stat_info);
    }

    /**
     * 补全缺失的天
     * @param array $list_stat_info
     * @return array
     */
    protected function appendMissDayForDetail($list_stat_info)
    {
        $list_stat_info = array_key_exists('stat', $list_stat_info['data']) ? $list_stat_info['data']['stat'] : [];
        $list_stat_info = array_column($list_stat_info, null, 'date');

        // 时间范围
        $list_date_range = $this->dateRange();

        // 容器
        $list_container = [];
        array_walk($list_date_range, function ($date) use (&$list_container, $list_stat_info) {
            $date_format = date('Ymd', strtotime($date));

            // 如果没有这天的数据 则赋默认值
            if (!array_key_exists($date_format, $list_stat_info)) {
                $list_container[$date] = $this->base_detail_item;
            } else {
                $list_container[$date] = $list_stat_info[$date_format];
            }

            $list_container[$date]['date'] = $date;
        });

        return $list_container;
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getApiDetailStatInfo($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_private_detail'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['code'] === 0) {
                break;
            }
            $i++;
        }
        // 三次请求失败,做记录
        $this->logDetailForFail($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 如果连续三次请求失败 则记录日志
     * @param array $result_request
     * @param string $url
     * @param array $params 请求的参数
     * @throws \Exception
     */
    protected function logDetailForFail($result_request, $url, $params)
    {
        if ($result_request['code'] !== 0) {
            $msg = [
                'msg' => '邦信分私有云统计详情API连续三次请求失败',
                'data' => $result_request,
                'url' => $url,
                'params' => $params
            ];
            $this->log($msg);
            throw new \Exception('邦信分私有云统计详情API连续三次请求失败');
        }
    }

    /**
     * 详情统计条件
     * @return array
     */
    protected function genStatConditionForDetail()
    {
        // apikey 限制
        $limit_apikey = $this->limitApikeyParamsForDetail();

        // 时间限制
        $limit_time = $this->genLimitTimeForDetailApi();
        return array_merge($limit_apikey, $limit_time);
    }

    /**
     * 时间限制
     * @return array
     */
    protected function genLimitTimeForDetailApi()
    {
        $request_body = $this->genParamsForPost();
        $date_start = date('Ymd', strtotime($request_body['time_begin']));
        $date_end = date('Ymd', strtotime($request_body['time_end']));
        return compact('date_end', 'date_start');
    }

    /**
     * apikey 限制
     * @return array
     */
    protected function limitApikeyParamsForDetail()
    {
        $request_body = $this->genParamsForPost();
        $customer_id = array_key_exists('customer_id', $request_body) ? trim($request_body['customer_id']) : '';
        $account_id = array_key_exists('account_id', $request_body) ? trim($request_body['account_id']) : '';

        // 如果只限制了 account_id
        if (!$customer_id && $account_id) {
            return $this->genParamsWhenLimitAccount($account_id);
        }

        // 如果限制了 customer_id
        if ($customer_id && !$account_id) {
            return $this->genParamsWhenLimitCustomer($customer_id);
        }

        // 同时限制了两者
        return $this->genParamsWhenLimitBoth($customer_id, $account_id);
    }

    /**
     * 同时限制了customer_id and account_id
     * @param integer $customer_id 客户ID
     * @param integer $account_id 账号ID
     * @return array
     */
    protected function genParamsWhenLimitBoth($customer_id, $account_id)
    {
        $list_account = $this->getAccountByCondition(compact('customer_id', 'account_id'));
        // 如果当前客户没有开通客户，则永远得不到数据
        if (!$list_account) {
            return ['apikey' => '当前客户没有开通账户, 客户id : ' . $customer_id];
        }

        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 生成apikey限制 (只是限制了customer_id的时候)
     * @param integer $customer_id
     * @return array
     */
    protected function genParamsWhenLimitCustomer($customer_id)
    {
        // 默认限制
        $father_id = ['NEQ', '0'];

        $list_account = $this->getAccountByCondition(compact('customer_id', 'father_id'));
        // 如果当前客户没有开通客户，则永远得不到数据
        if (!$list_account) {
            return ['apikey' => '当前客户没有开通客户, 客户id : ' . $customer_id];
        }

        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 生成apikey限制 (只是限制了account_id的时候)
     * @param integer $account_id 账号ID
     * @return array
     */
    protected function genParamsWhenLimitAccount($account_id)
    {
        $list_account = $this->getAccountByCondition(compact('account_id'));
        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParamsForDetail()
    {
        // 检查时间
        $this->verifyTime();

        // 是否选定了客户或者账号
        $this->verifyExistsSelectedApikey();
    }

    /**
     * 是否选定了客户或者账号
     * @throws \Exception
     */
    protected function verifyExistsSelectedApikey()
    {
        $request_body = $this->genParamsForPost();
        $customer_id = array_key_exists('customer_id', $request_body) ? trim($request_body['customer_id']) : '';
        $account_id = array_key_exists('account_id', $request_body) ? trim($request_body['account_id']) : '';
        if (!$customer_id && !$account_id) {
            throw new \Exception('请选定客户或者账号后再查询');
        }
    }

    /**
     * 统计列表
     * @throws \Exception
     */
    public function listStat()
    {
        // 限定访问类型
        $this->limitRequestMethod();

        // 检查参数
        $this->verifyParams();

        // 符合条件得账号列表
        $list_account = $this->getLegalAccountList();

        // 追加统计信息
        $list_account = $this->appendStatInfoToAccount($list_account);

        // 和并成客户为单位
        $list_customer = $this->formatDataByUniqueCustomer($list_account);

        // 实现客户的异常查询的过滤
        $list_customer = $this->filterStatCustomer($list_customer);

        // 默认排序 总查询量倒序
        $list_customer = $this->soreDefaultForList($list_customer);

        // 追加总计数据
        $list_customer = $this->appendTotalToList($list_customer);

        // 格式化数据
        return $this->formatData($list_customer);
    }

    /**
     * 实现客户的异常查询的过滤
     * @param array $list_customer 客户信息
     * @return array
     */
    protected function filterStatCustomer($list_customer)
    {
        // 请求类型
        $request_body = $this->genParamsForPost();
        $status = $request_body['status'];

        // 如果选择全部的时候
        if ($status === '') {
            return $list_customer;
        }

        // 过滤掉不符合条件
        $list_container = [];
        array_walk($list_customer, function($item) use (&$list_container, $status){
            if ($status == $item['min_status']) {
                array_push($list_container, $item);
            }
        });

        return $list_container;
    }

    /**
     * 默认排序
     * @param array $list_customer
     * @return array
     */
    protected function soreDefaultForList($list_customer)
    {
        $list_sort_field = array_column($list_customer, 'sum_counts');
        array_multisort($list_sort_field, SORT_DESC, SORT_NUMERIC, $list_customer);
        return $list_customer;
    }

    /**
     * 追加总计数据
     * @param array $list_customer
     * @return array
     */
    protected function appendTotalToList($list_customer)
    {
        // 获取总计数据
        $list_total = $this->tidyTotalData($list_customer);

        // 追加
        array_unshift($list_customer, $list_total);

        return $list_customer;
    }


    /**
     * 加入总计数据
     * @param array $list_customer 客户数据
     * @return array
     */
    protected function tidyTotalData($list_customer)
    {
        // 容器
        $list_total = $this->base_stat_item;
        $list_total['name'] = '总计';

        array_walk($list_customer, function ($item) use (&$list_total) {
            $list_total['sum_counts'] += $item['sum_counts'];
            $list_total['sum_valid_counts'] += $item['sum_valid_counts'];
            $list_total['min_status'] = $item['min_status'] == 0 ? 0 : $list_total['min_status'];
        });
        return $list_total;
    }

    /**
     * 和并成客户为单位
     * @param array $list_account 账号数据
     * @return array
     */
    protected function formatDataByUniqueCustomer($list_account)
    {
        // 本次查询的客户
        $list_customer = $this->getCustomerForList();

        // 客户追加账号信息
        return $this->appendStatToCustomerBy($list_account, $list_customer);
    }

    /**
     * 客户追加账号信息
     * @param array $list_account
     * @param array $list_customer
     * @return array
     */
    protected function appendStatToCustomerBy($list_account, $list_customer)
    {
        // 客户默认统计值为0
        $list_customer = array_map(function ($item) {
            return array_merge($item, $this->base_stat_item);
        }, $list_customer);
        $list_customer = array_column($list_customer, null, 'customer_id');

        // 计算客户信息
        array_walk($list_account, function ($item_account) use (&$list_customer) {
            $customer_id = $item_account['customer_id'];

            // 如果没有绑定客户则过滤掉
            if (!array_key_exists($customer_id, $list_customer)) {
                return true;
            }

            // 计算值
            $list_customer[$customer_id]['sum_counts'] += $item_account['sum_counts'];
            $list_customer[$customer_id]['sum_valid_counts'] += $item_account['sum_valid_counts'];
            $list_customer[$customer_id]['min_status'] = $item_account['min_status'] == 0 ? 0 : $list_customer[$customer_id]['min_status'];

            $list_customer[$customer_id]['children'][] = $item_account;
        });
        //过滤掉没有账号的客户数据
        $list_customer = array_filter($list_customer, function ($item) {
            if (!isset($item['children']) || empty($item['children'])) {
                return false;
            }
            return true;
        });
        return $list_customer;
    }

    /**
     * 统计列表选中的客户
     * @return array
     */
    protected function getCustomerForList()
    {
        // 条件
        $where = $this->genLimitByCustomerForList();

        return $this->getCustomerByCondition($where);
    }

    /**
     * 根据条件获取客户
     * @param array $where
     * @return array
     */
    protected function getCustomerByCondition($where)
    {
        return (new CustomerModel())->where($where)
            ->select();
    }

    /**
     * 追加统计信息
     * @param array $list_account
     * @return array
     * @throws \Exception
     */
    protected function appendStatInfoToAccount($list_account)
    {
        // 获取统计信息
        $list_stat_info = $this->getStatInfo($list_account);

        // 整合账号 && 统计信息
        return $this->tidyStatInfoForList($list_stat_info, $list_account);
    }

    /**
     * 整合账号 && 统计信息
     * @param array $list_stat_info 获取到的统计数据
     * @param array $list_account 原始账号数据
     * @return array
     */
    protected function tidyStatInfoForList($list_stat_info, $list_account)
    {
        // 调整格式
        $list_stat_info = array_key_exists('stat', $list_stat_info['data']) ? $list_stat_info['data']['stat'] : [];
        $list_stat_info = $list_stat_info ? array_column($list_stat_info, null, 'apikey') : [];

        return array_map(function ($item) use ($list_stat_info) {
            $apikey = $item['apikey'];
            $item_stat = array_key_exists($apikey, $list_stat_info) ? $list_stat_info[$apikey] : $this->base_stat_item;
            return array_merge($item, $item_stat);
        }, $list_account);
    }

    /**
     * 获取统计信息
     * @param array $list_account
     * @return array
     * @throws \Exception
     */
    protected function getStatInfo($list_account)
    {
        // 条件
        $params = $this->genParamsForStatApi($list_account);

        // 调用API
        return $this->getApiStatInfo($params);
    }


    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getApiStatInfo($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_private_list'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['code'] === 0) {
                break;
            }
            $i++;
        }
        // 三次请求失败,做记录
        $this->logForFail($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 如果连续三次请求失败 则记录日志
     * @param array $result_request
     * @param string $url
     * @param array $params 请求的参数
     * @throws \Exception
     */
    protected function logForFail($result_request, $url, $params)
    {
        if ($result_request['code'] !== 0) {
            $msg = [
                'msg' => '邦信分私有云统计列表API连续三次请求失败',
                'data' => $result_request,
                'url' => $url,
                'params' => $params
            ];
            $this->log($msg);
            throw new \Exception('邦信分私有云统计列表API连续三次请求失败');
        }
    }


    /**
     * 为调用统计信息生成条件
     * @return array
     */
    protected function genParamsForStatApi($list_account)
    {
        // 账号限制
        $limit_apikey = $this->genLimitAccountForListApi($list_account);

        // 时间限制
        $limit_time = $this->genLimitTimeForListApi();

        // 是否有异常查询
        $limit_status = $this->genLimitStatusForListApi();

        return array_merge($limit_status, $limit_time, $limit_apikey);
    }

    /**
     * 是否有异常查询
     * @param array $list_account
     * @return array
     */
    protected function genLimitStatusForListApi()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('status', $request_body) || $request_body['status'] === '') {
            return [];
        }

        $status = $request_body['status'];
        return compact('status');
    }

    /**
     * 时间限制
     * @return array
     */
    protected function genLimitTimeForListApi()
    {
        $request_body = $this->genParamsForPost();
        $date_start = date('Ymd', strtotime($request_body['time_begin']));
        $date_end = date('Ymd', strtotime($request_body['time_end']));
        return compact('date_end', 'date_start');
    }

    /**
     * 账号限制
     * @param array $list_account
     * @return array
     */
    protected function genLimitAccountForListApi($list_account)
    {
        $apikey = implode(',', array_column($list_account, 'apikey'));
        return compact('apikey');
    }


    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParams()
    {
        // 检查时间
        $this->verifyTime();
    }

    /**
     * 检查时间
     * @throws \Exception
     */
    protected function verifyTime()
    {
        // 检查时间参数是否存在
        $this->verifyExistTime();

        // 时间得其他需求
        $this->verifyTimeOtherRequire();
    }

    /**
     * 时间得其他需求
     * @throws \Exception
     */
    protected function verifyTimeOtherRequire()
    {
        $request_body = $this->genParamsForPost();
        // 合法
        $time_begin = strtotime($request_body['time_begin']);
        $time_end = strtotime($request_body['time_end']) + 86399;
        if ($time_begin > $time_end) {
            throw new \Exception('开始时间必须小于结束时间');
        }

        // 365天限制
        $time_diff = $time_end - $time_begin > 365 * 86400;
        if ($time_diff) {
            throw new \Exception('时间范围不合法,单次查询最多365天');
        }
    }

    /**
     * 检查时间参数是否存在
     * @throws \Exception
     */
    protected function verifyExistTime()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('time_begin', $request_body) || !$request_body['time_begin']) {
            throw new \Exception('请选择开始时间');
        }
        if (!array_key_exists('time_end', $request_body) || !$request_body['time_end']) {
            throw new \Exception('请选择结束时间');
        }
    }

    /**
     * 符合条件得账号列表
     */
    protected function getLegalAccountList()
    {
        // 限制条件
        $where = $this->genLimitForList();

        // 账号列表
        $list_account = $this->getAccountByCondition($where);

        // 过滤掉没有开通用产品的账号
        return $this->filterNotHasProduct($list_account);
    }

    /**
     * 过滤掉没有开通产品的账号
     * @param array $list_account 等待过滤的账号
     * @return array
     */
    protected function filterNotHasProduct($list_account)
    {
        // 全量账号产品
        $list_account_product = $this->getAllAccountProduct();
        $list_account_product = array_column($list_account_product, 'account_id');

        // 容器
        $list_container = [];
        array_walk($list_account, function ($item) use (&$list_container, $list_account_product){
            $account_id = $item['account_id'];
            if (in_array($account_id, $list_account_product)) {
                array_push($list_container, $item);
            }
        });
        return $list_container;
    }

    /**
     * 获取全量的账号id
     */
    protected function getAllAccountProduct()
    {
        $product_id = $this->product_id;
        return $this->getAccountProductByCondition(compact('product_id'));
    }

    /**
     * 单个账号
     * @param array $where
     * @return array
     */
    protected function getOneAccountByCondition($where)
    {
        return (new AccountModel())->where($where)
            ->find();
    }

    /**
     * 账号列表
     * @param array $where
     * @return array
     */
    protected function getAccountByCondition($where)
    {
        return (new AccountModel())->where($where)
            ->select();
    }

    /**
     * 为统计列表生成限制条件
     * @return array
     */
    protected function genLimitForList()
    {
        // 客户限制
        $limit_customer = $this->genLimitByCustomerForList();

        // 签约状态限制
        $limit_contract_status = $this->genLimitContractForList();

        // 默认限制
        $father_id = ['NEQ', '0'];

        //是否删除
        $is_delete = 0;

        return array_merge($limit_contract_status, $limit_customer, compact('father_id', 'is_delete'));
    }

    /**
     * 签约状态限制
     * @return array
     */
    protected function genLimitContractForList()
    {
        $requset_body = $this->genParamsForPost();
        if (!array_key_exists('contract_status', $requset_body) || !$requset_body['contract_status']) {
            return [];
        }

        // 满足签约类型的账号
        $contract_status = $requset_body['contract_status'];
        $product_id = $this->product_id;
        $list_account = $this->getAccountProductByCondition(compact('contract_status', 'product_id'));

        if (!$list_account) {
            return [
                'account_id' => '该签约类型，没有找到数据'
            ];
        }
        $account_id = ['in', array_column($list_account, 'account_id')];
        return compact('account_id');
    }

    /**
     * 根据条件获取账号列表
     * @param array $where
     * @return array
     */
    protected function getAccountProductByCondition($where)
    {
        return (new AccountProductModel())->where($where)
            ->select();
    }

    /**
     * 为统计列表生成客户限制
     * @return array
     */
    protected function genLimitByCustomerForList()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('customer_id', $request_body) || !$request_body['customer_id']) {
            return [];
        }
        $customer_id = $request_body['customer_id'];
        $is_delete = 0;
        return compact('customer_id', 'is_delete');
    }

    /**
     * 生成已开通此产品的客户列表
     * @return array
     */
    public function customerList()
    {
        //获取已开通此产品的账号数据
        //未获取账号列表生成条件
        // 签约状态限制
        $limit_contract_status = $this->genLimitContractForList();

        // 默认限制
        $father_id = ['NEQ', '0'];

        $where = array_merge($limit_contract_status, compact('father_id'));
        // 账号列表
        $list_account = $this->getAccountByCondition($where);
        // 过滤掉没有开通用产品的账号
        $list_account = $this->filterNotHasProduct($list_account);
        //获取已开通产品的客户ID
        $customer_ids = array_column($list_account, 'customer_id');
        return (new CustomerModel())->where([
            'customer_id'   => ['in', $customer_ids],
            'is_delete'     => 0
        ])->select();
    }

    /**
     * 产看单个的客户
     * @throws \Exception
     * @return array
     */
    public function customerShow()
    {
        // 检查参数
        $this->verifyParamsForCustomerShow();

        // 生成条件
        $where = $this->genConditionForCustomerShow();
        return (new CustomerModel())->where($where)->find();
    }

    /**
     * 生成条件
     * @return array
     */
    protected function genConditionForCustomerShow()
    {
        $customer_id = I('get.customer_id', '', 'trim');
        return compact('customer_id');
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParamsForCustomerShow()
    {
        $customer_id = I('get.customer_id', '', 'trim');
        if (!$customer_id) {
            throw new \Exception('请输入customer_id');
        }
    }

    /**
     * 生成全量的账号列表
     * @return array
     * @throws \Exception
     */
    public function accountList()
    {
        // 限制请求方法
        $this->limitGetMethod();

        // 条件
        $where = $this->genConditionForListAccount();

        // 列表
        $list_account = $this->getAccountListByCondition($where);

        // 过滤掉没有开通用产品的账号
        return $this->filterNotHasProduct($list_account);
    }

    /**
     * 根据条件获取账号列表
     * @param array $where
     * @return array
     */
    protected function getAccountListByCondition($where)
    {
        return (new AccountModel())->where($where)
            ->select();
    }

    /**
     * 限制为Get请求
     * @throws \Exception
     */
    protected function limitGetMethod()
    {
        if (!IS_GET) {
            throw new \Exception('请求必须是GET方法');
        }
    }

    /**
     * 为账号列表生成条件
     * @return array
     */
    protected function genConditionForListAccount()
    {
        // 默认的限制
        $father_id = ['NEQ', '0'];

        // 客户限制
        $customer_id = I('get.customer_id', '', 'trim');
        if (!$customer_id) {
            return compact('father_id');
        }

        return compact('customer_id', 'father_id');
    }

    /**
     * 查看单个的账号
     * @throws \Exception
     */
    public function accountShow()
    {
        // 检查参数
        $this->verifyParamsForShow();

        // 生成条件
        $where = $this->genConditionForShow();

        return (new AccountModel())->where($where)->find();
    }

    /**
     * 生成条件
     * @return array
     */
    protected function genConditionForShow()
    {
        $account_id = I('get.account_id', '', 'trim');
        return compact('account_id');
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    protected function verifyParamsForShow()
    {
        $account_id = I('get.account_id', '', 'trim');
        if (!$account_id) {
            throw new \Exception('请输入account_id');
        }
    }
}