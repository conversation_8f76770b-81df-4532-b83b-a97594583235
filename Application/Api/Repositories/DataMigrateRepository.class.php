<?php

namespace Api\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;
use Api\Model\CrsAuxiliaryServicesModel;
use Common\Common\HandlerLog;
use Common\Common\ResponseTrait;
use Common\Common\WechatBackendExceptionTrait;
use Common\Model\AdminApictrlModel;
use Common\Model\AdminApikeyFieldsModel;
use Common\Model\AdminApikeyModel;
use Common\Model\AuthModel;
use Common\Model\AuxiliaryServices;
use Common\Model\CuishouUserModel;
use Common\Model\FinanceAccountProductModel;
use Common\Model\FinanceAccountsModel;
use Think\Cache\Driver\Redis;

class DataMigrateRepository
{
    use ResponseTrait, WechatBackendExceptionTrait;

    /*
     * http authorization
     * */
    private $http_authorization = 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6Ij';

    /*
     * 是否已经执行过了migrate在redis中的key
     * */
    private $migrate_done_key = 'backend_rollback_already_repair';

    private $product_id_crawler_h5 = 301;
    private $product_id_crawler_api = 302;
    private $product_id_matching = 601;
    private $product_id_cuishou = 101;
    private $product_id_report = 102;

    /*
     * 临时等待repair的数据存放的位置
     * */
    private $list_repair_template = [];

    /*
     * 迁移数据新生成的数据
     * */
    private $queue_table_migrate = 'repair_table_history_queue';

    /*
     * 数据回退单元
     * */
    private $rollback_item;

    /*
     * 缺少紧急联系人限制属性的集合
     * */
    private $list_missing_emergency = [];

    /*
     * 数值奇怪的紧急联系人集合
     * */
    private $list_emergency_unusual = [];


    /**
     * 解决数据迁移的问题
     * @throws \Exception
     */
    public function repair()
    {
        // 查看是否有权限
        $this->validatePermission();

        // 检查是否已经执行过了repair
        $this->determineMigrateDone();

        // 执行repair
        $this->repairH5();
    }

    /**
     * 修复H5数据
     * @throws \Exception
     */
    private function repairH5()
    {
        // h5产品的集合
        $list_products_h5 = $this->getAllH5Product();

        // 修复数据
        $this->repairH5Do($list_products_h5);

        // 记录日志
        $this->logRepair();
    }

    /**
     * @throws \Exception
     */
    private function logRepair()
    {
        $list_missing_emergency = $this->list_missing_emergency;
        $list_emergency_unusual = $this->list_emergency_unusual;
        $log = [
            'handle_type' => 'migrate',
            'description' => '修复H5产品日志',
            'content' => compact('list_missing_emergency', 'list_emergency_unusual'),
            'handle_user' => 'system',
            'handle_result' => 0
        ];
        HandlerLog::log($log);
    }

    /**
     * 修复数据
     * @param array $list_products_h5
     */
    private function repairH5Do(array $list_products_h5)
    {
        array_walk($list_products_h5, function($item_product){
            // 是否需要修复
            if (!$this->determineH5ProductNeedRepaired($item_product)) {
                return;
            }

            // 说明
            $this->intro('开始修复account_id=' . $item_product['account_id'] . ' H5产品的数据');

            // 修复数据
            $this->updateH5Emergency($item_product);
        });

    }

    /**
     * 更新h5产品的紧急联系人限制字段
     * @param array $item_product
     * @throws \Exception
     */
    private function updateH5Emergency(array $item_product)
    {
        // 参数
        $params = $this->genParamsForUpdateH5($item_product);

        // 设置临时存放原来数据，等待rollback使用
        $this->setListRepairTemplate($item_product);

        // 条件
        $where = $this->genConditionForUpdateH5($item_product);

        // 更新
        $this->updateProductDo($where, $params, $item_product['account_id']);
    }

    /**
     * @param array $where
     * @param array $params
     * @param string $account_id
     * @throws \Exception
     */
    private function updateProductDo(array $where, array $params, $account_id)
    {
        $result_update = (new AccountProductModel())
            ->where($where)
            ->save($params);

        if ($result_update === false) {
            $msg = 'Repair Error: account_id=' . $account_id . '更新出错, 请执行rollback之后再更新';
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        $this->insertHistoryQueue($this->list_repair_template);
    }

    /**
     * 生成更新h5的条件
     * @param array $item_product
     * @return array
     */
    private function genConditionForUpdateH5(array $item_product)
    {
        $id = $item_product['id'];
        return compact('id');
    }

    /**
     * 清除临时变量
     */
    private function clearListRepairTemplate()
    {
        $this->list_repair_template = [];
    }


    /**
     * 设置临时存放原来数据，等待rollback使用
     * @param array $item_product
     */
    private function setListRepairTemplate(array $item_product)
    {
        $this->list_repair_template = [
            'id' => $item_product['id'],
            'data' => $item_product['data']
        ];
    }

    /**
     * 为修复数据生成参数
     * @param array $item_product
     * @return array
     */
    private function genParamsForUpdateH5(array $item_product)
    {
        $data_list = json_decode($item_product['data'], true);
        $data_list['emergency_contact_detail_limits'] = $this->genEmergencyLimitForH5($data_list['emergency_contact_detail_limits']);
        $data = json_encode($data_list, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * @param $emergency_limit
     * @return array
     */
    public static function genEmergencyLimitForH5($emergency_limit)
    {
        $list_emergency = unserialize($emergency_limit);
        // 容器
        $list_container = [];

        array_walk($list_emergency, function($number, $limit_type) use(&$list_container){
            if ($limit_type == '无限制') {
                return;
            }
            $item = array_fill(0, $number, [$limit_type]);
            $list_container = array_merge($list_container, $item);
        });
        return $list_container;
    }

    /**
     * h5产品是否需要修复
     * @param array $item_product
     * @return bool
     * @throws \Exception
     */
    private function determineH5ProductNeedRepaired(array $item_product)
    {
        $data = json_decode($item_product['data'], true);
        // 缺少字段
        if (!array_key_exists('emergency_contact_detail_limits', $data)) {
            $msg = 'Repair Error: account_id=' . $item_product['account_id'] . ' H5产品缺少emergency_contact_detail_limits属性';
            $this->intro($msg);
            array_push($this->list_missing_emergency, $item_product['account_id']);
            return false;
        }

        // 数组自然不进行处理
        $emergency_contact_detail_limits = $data['emergency_contact_detail_limits'];
        if (is_array($emergency_contact_detail_limits)) {
            return false;
        }

        if (in_array($emergency_contact_detail_limits, ['', null])) {
            return false;
        }

        // 非字符串
        if (!is_string($emergency_contact_detail_limits)) {
            $msg = 'Repair Error: account_id=' . $item_product['account_id'] . ' H5产品emergency_contact_detail_limits属性不是array && string';
            $this->intro($msg);
            array_push($this->list_emergency_unusual, $item_product['account_id']);
            return false;
        }

        // 不可以被序列化
        if (unserialize($emergency_contact_detail_limits) === false) {
            $msg = 'Repair Error: account_id=' . $item_product['account_id'] . ' H5产品emergency_contact_detail_limits是一个不可以被反序列化的字符串';
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        return true;
    }

    /**
     * h5产品的集合
     * @return mixed
     */
    private function getAllH5Product()
    {
        $product_id = $this->product_id_crawler_h5;
        return $this->getAccountProductByCondition(compact('product_id'));
    }

    private function getAccountProductByCondition(array $where)
    {
        return (new AccountProductModel())->where($where)
            ->select();
    }


    /**
     * @throws \Exception
     */
    public function migrate()
    {
        // 查看是否有权限
        $this->validatePermission();

        // 是否已经执行了migrate, 提示: rollback
        $this->determineMigrateDone();

        // handle 操作
        $this->handleMigrate();
    }

    /**
     * 回退
     * @throws \Exception
     */
    public function rollback()
    {
        // 查看是否有权限
        $this->validatePermission();

        // rollback Do
        $this->rollbackDo();

        // 标记已经rollback
        $this->setMigrateAlready('');
    }

    /**
     *  rollback Do
     * @throws \Exception
     */
    private function rollbackDo()
    {
        // 循环获取队列单元
        while ($this->rollback_item = $this->getOneQueueItemForRollback()) {
            $id = $this->rollback_item['id'];
            $data = $this->rollback_item['data'];
            $result_rollback = (new AccountProductModel())
                ->where(compact('id'))
                ->save(compact('data'));

            // 如果操作失败 则重新入队
            if ($result_rollback === false) {
                $this->insertHistoryQueue($this->rollback_item);
            }
        }
    }

    /**
     * 获取回退单元
     * @throws \Exception
     * @return array
     */
    private function getOneQueueItemForRollback()
    {
        $item_queue = (new Redis())->lpop($this->queue_table_migrate);
        if (!$item_queue) {
            return null;
        }

        // 是否是json
        if (json_validate($item_queue) === false) {
            $msg = 'Migrate Error：版本回退失败，队列单元不是合法的json msg:' . $item_queue;
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        // id 检查
        $item_migrate = json_decode($item_queue, true);
        $id_migrate = defaultValue($item_migrate, 'id');
        if (!is_numeric($id_migrate)) {
            $msg = 'Rollback Error：版本回退失败，队列单元id字段不合法 msg:' . $item_queue;
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }
        return $item_migrate;
    }

    /**
     * 查看是不是已经已经做了migrate操作
     * @throws \Exception
     */
    private function determineMigrateDone()
    {
        if ($this->getMigrateAlready()) {
            throw new \Exception('已经执行过来了repair; 如果想再次执行的话，请先执行rollback清空之前的操作');
        }

        $this->setMigrateAlready('done');
    }

    /**
     * 查看是否有执行权限
     * @throws \Exception
     */
    private function validatePermission()
    {
        $header = array_key_exists('HTTP_AUTHORIZATION', $_SERVER) ? trim($_SERVER['HTTP_AUTHORIZATION']) : '';
        if ($header !== $this->http_authorization) {
            throw new \Exception('认证失败,请传递正确的bearer token');
        }
    }

    /**
     * migrate do
     * @throws \Exception
     */
    private function handleMigrate()
    {
        // 聚合数据
        $list_migrate_data = $this->genMigrateData();

        // 映射注入
        $this->migrateTable($list_migrate_data);

        // 记录日志
        $this->handleLog();
    }

    /**
     * 记录日志
     * @throws \Exception
     */
    private function handleLog()
    {
        $list_product_with_double_account_name = $this->list_product_with_double_account_name;
        $missing_customer = $this->missing_customer;
        $list_crawler_with_account_create_already = $this->list_crawler_with_account_create_already;
        $list_account_double_and_has_product_already = $this->list_account_double_and_has_product_already;
        $list_product_callback = $this->list_product_callback;

        $destination = 'migrate_' . date('Ymd') . '.log';
        $info_log = json_encode(compact('list_product_with_double_account_name', 'missing_customer',
            'list_crawler_with_account_create_already', 'list_account_double_and_has_product_already', 'list_product_callback'),
            JSON_UNESCAPED_UNICODE);

        file_put_contents(
            $destination, $info_log
        );
    }

    /**
     * 映射注入数据表
     * @param array $list_migrate_data
     */
    private function migrateTable(array $list_migrate_data)
    {
        array_walk($list_migrate_data, function ($item_migrate, $customer_id) {
            // 进度说明
            $this->intro('正在迁移Customer:' . $customer_id . ' 的数据 ...');

            array_walk($item_migrate, [$this, 'migrateProduct']);
        });
    }

    /**
     * 对不同的产品进行migrate处理
     * @param array $item
     * @param $product_type
     * @throws \Exception
     */
    private function migrateProduct(array $item, $product_type)
    {
        switch ($product_type) {
            case 'crawler_h5':
                $this->migrateCrawlerH5Product($item);
                break;
            case 'crawler_api':
                $this->migrateCrawlerApiProduct($item);
                break;
            case 'cuishou':
                $this->migrateCuishouProduct($item);
                break;
            case 'matching':
                $this->migrateMatchingProduct($item);
                break;
            default:
                $msg = 'Migrate Error: migrate table 数据异常, msg: product_type=' . $product_type;
                $this->wehcatException($msg);
                throw new \Exception($msg);
        }
    }

    /**
     * 迁移matching产品的数据
     * @param array $list_matchings 待处理的产品数据
     */
    private function migrateMatchingProduct(array $list_matchings)
    {
        array_walk($list_matchings, function ($item_matching) {
            // 初始化账号重复是否重复的定义
            $this->determine_double_account_name_now = false;

            // 生成账号
            $account_create = $this->migrateAccount($item_matching, 'matching');

            // 生成产品
            $this->genMatchingProduct($account_create, $item_matching);
        });
    }

    /**
     * 生成账号
     * @param array $account_create 新生成的账号
     * @param array $product
     * @throws \Exception
     * @return mixed | void
     */
    private function genMatchingProduct($account_create, array $product)
    {
        // 如果账号名称重复则不再进行后续操作
        if ($this->determine_double_account_name_now) {
            return;
        }

        // 生成参数
        $params = $this->genParamsForMatchingProduct($account_create, $product);

        // 注册产品
        return $this->registerProduct($params);
    }

    /**
     * 生成参数
     * @param array $account_create
     * @param array $product
     * @return array
     */
    private function genParamsForMatchingProduct(array $account_create, array $product)
    {
        // 普通参数
        $params_common = $this->genCommonParamsForProduct($account_create, $product, 'matching');
        $params_common['product_id'] = $this->product_id_matching;

        // data参数
        $params_data = $this->genDataParamsForMatchingProduct($product);

        return array_merge($params_common, $params_data);
    }

    /**
     * data参数
     * @param array $product
     * @return array
     */
    private function genDataParamsForMatchingProduct(array $product)
    {
        $uid = $product['id'];
        $bind_domain = defaultValue($product, 'bind_domain', '', 'trim');
        $output_yscs = defaultValue($product, 'output_yscs', '2', 'trim');
        $output_cs = defaultValue($product, 'output_cs', '2', 'trim');
        $encrypt_type = defaultValue($product, 'encrypt_type', '1', 'trim');
        $out_fields = defaultValue($product, 'out_fields', [2, 3, 5, 15, 16, 17, 18]);

        $data = compact('bind_domain', 'uid', 'out_fields', 'output_cs', 'output_yscs', 'encrypt_type');
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 迁移cuishou产品的数据
     * @param array $list_cuishou 待处理的产品数据
     */
    private function migrateCuishouProduct(array $list_cuishou)
    {
        array_walk($list_cuishou, function ($item_cuishou) {
            // 初始化账号重复是否重复的定义
            $this->determine_double_account_name_now = false;

            // 生成账号
            $account_create = $this->migrateAccount($item_cuishou, 'cuishou');

            // 生成产品
            $this->genCuishouProduct($account_create, $item_cuishou);
        });
    }

    /**
     * 生成账号
     * @param array $account_create 新生成的账号
     * @param array $product
     * @throws \Exception
     * @return mixed | void
     */
    private function genCuishouProduct($account_create, array $product)
    {
        // 如果账号名称重复则不再进行后续操作
        if ($this->determine_double_account_name_now) {
            return;
        }

        // 生成参数
        $params = $this->genParamsForCuishouProduct($account_create, $product);

        // 注册产品
        return $this->registerProduct($params);
    }

    /**
     * 生成参数
     * @param array $account_create
     * @param array $product
     * @return array
     */
    private function genParamsForCuishouProduct(array $account_create, array $product)
    {
        // 普通参数
        $params_common = $this->genCommonParamsForProduct($account_create, $product, 'cuishou');
        $params_common['product_id'] = $this->product_id_cuishou;

        // data参数
        $params_data = $this->genDataParamsForCuishouProduct($product);

        return array_merge($params_common, $params_data);
    }

    /**
     * data参数
     * @param array $product
     * @return array
     */
    private function genDataParamsForCuishouProduct(array $product)
    {
        $uid = $product['id'];
        $is_level = defaultValue($product, 'is_level', '0', 'trim');
        $flag = defaultValue($product, 'flag', '1', 'trim');
        $need_itag = defaultValue($product, 'need_itag', '2', 'trim');
        $itag_vip_queue = (array_key_exists('itag_vip_queue', $product) && $product['itag_vip_queue'] == 1) ? 'true' : 'false';
        $only_itag = defaultValue($product, 'only_itag', '0', 'trim');
        $itag_notify_url = defaultValue($product, 'itag_notify_url', '', 'trim');
        $itag_apikey = defaultValue($product, 'itag_apikey', '', 'trim');
        $itag_password = defaultValue($product, 'itag_password', '', 'trim');
        $need_async_notify = defaultValue($product, 'need_async_notify', '2', 'trim');
        $notify_url = defaultValue($product, 'notify_url', '', 'trim');
        $require_tel = defaultValue($product, 'require_tel', 'true', 'trim');
        $require_name = defaultValue($product, 'require_name', 'true', 'trim');
        $require_idnum = defaultValue($product, 'require_idnum', 'true', 'trim');

        $data = compact('uid', 'is_level', 'flag', 'need_itag', 'itag_vip_queue', 'only_itag',
            'itag_notify_url', 'itag_apikey', 'itag_password', 'need_async_notify', 'notify_url',
            'require_idnum', 'require_name', 'require_tel');
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 迁移crawler api产品的数据
     * @param array $list_crawlers 待处理的产品数据
     */
    private function migrateCrawlerApiProduct(array $list_crawlers)
    {
        array_walk($list_crawlers, function ($item_crawler) {
            // 初始化账号重复是否重复的定义
            $this->crawler_api_info_matching = false;

            // 生成账号
            $account_create = $this->migrateAccount($item_crawler, 'crawler_api');

            // 生成产品
            $this->genCrawlerApiProduct($account_create, $item_crawler);

            // 生成报告
            $this->genReportProduct($account_create, $item_crawler);
        });
    }


    /**
     * 生成账号
     * @param array $account_create 新生成的账号
     * @param array $product
     * @throws \Exception
     * @return mixed | void
     */
    private function genCrawlerApiProduct($account_create, array $product)
    {
        // 如果账号名称重复则不再进行后续操作
        if ($this->crawler_api_info_matching && $this->determineApiInfoNotMatch($product, $account_create)) {
            return;
        }

        // 记录 $list_crawler_with_account_create_already 属性
        $this->setListCrawlerWithAccountCreateAlready($product);

        // 生成参数
        $params = $this->genParamsForCrawlerApi($account_create, $product);

        // 注册产品
        return $this->registerProduct($params);
    }

    /**
     * 生成参数
     * @param array $account_create
     * @param array $product
     * @return array
     */
    private function genParamsForCrawlerApi(array $account_create, array $product)
    {
        // 普通参数
        $params_common = $this->genCommonParamsForProduct($account_create, $product, 'crawler_api');
        $params_common['product_id'] = $this->product_id_crawler_api;

        // data参数
        $params_data = $this->genDataParamsForCrawlerApi($product);

        return array_merge($params_common, $params_data);
    }


    /**
     * data参数
     * @param array $product
     * @return array
     */
    private function genDataParamsForCrawlerApi(array $product)
    {
        $cid = $product['id'];
        $fixed_channel = $product['fixed_channel'];
        $crawl_version = "1";
        $api_config_info = $this->getApiConfigForCrawlerApi($product);
        $authorize_notify_url = $product['authorize_notify_url'];

        $data = compact('cid', 'fixed_channel', 'crawl_version', 'api_config_info', 'authorize_notify_url');
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 获取必传参数列表
     * @param array $product
     * @return array
     */
    private function getApiConfigForCrawlerApi(array $product)
    {
        if (!array_key_exists('api_config_info', $product) || !array_key_exists('required_field', $product['api_config_info'])) {
            return [1, 2, 3];
        }

        return $product['api_config_info']['required_field'];
    }

    /**
     * 迁移crawler h5产品的数据
     * @param array $list_crawlers 待处理的产品数据
     */
    private function migrateCrawlerH5Product(array $list_crawlers)
    {
        array_walk($list_crawlers, function ($item_crawler) {
            // 初始化账号重复是否重复的定义
            $this->crawler_api_info_matching = false;

            // 生成账号
            $account_create = $this->migrateAccount($item_crawler, 'crawler_h5');

            // 生成产品
            $this->genCrawlerH5Product($account_create, $item_crawler);

            // 生成报告
            $this->genReportProduct($account_create, $item_crawler);
        });
    }

    /**
     * 生成报告
     * @param array $account_create
     * @param array $product
     * @return void
     * @throws \Exception
     */
    private function genReportProduct(array $account_create, array $product)
    {
        // 如果是已经创建了账号 && 账号下含有api h5 report, 不再进行后续操作
        if ($this->crawler_api_info_matching && $this->determineApiInfoNotMatch($account_create, $product)) {
            return;
        }

        // 生成参数
        $params = $this->genParamsForReport($account_create, $product);

        // 注册产品
        $this->registerProduct($params);
    }

    /**
     * @param array $account_create
     * @param array $product
     * @return array
     */
    private function genParamsForReport(array $account_create, array $product)
    {
        // 普通参数
        $params_common = $this->genCommonParamsForProduct($account_create, $product, 'report');
        $params_common['product_id'] = $this->product_id_report;

        // data 参数
        $params_data = $this->genCommonParamsForReport($product);
        return array_merge($params_common, $params_data);
    }

    /**
     * 获取报告的data参数
     * @param array $product
     * @return array
     */
    private function genCommonParamsForReport(array $product)
    {
        $is_push_detail = isset($product['is_push_detail']) ? $product['is_push_detail'] : '0';
        $security_push_service = array_key_exists('security_push_service', $product) ? $product['security_push_service'] : '0';
        $notify_url = array_key_exists('notify_url', $product) ? $product['notify_url'] : '';
        $need_itag = array_key_exists('need_itag', $product) ? $product['need_itag'] : '0';
        $need_dunning = array_key_exists('need_dunning', $product) ? $product['need_dunning'] : '0';
        $cuishou_apikey = array_key_exists('cuishou_apikey', $product) ? $product['cuishou_apikey'] : '';
        $cuishou_appsecret = array_key_exists('cuishou_appsecret', $product) ? $product['cuishou_appsecret'] : '';
        $is_vip = array_key_exists('is_vip', $product) ? $product['is_vip'] : '0';
        $need_report = array_key_exists('need_report', $product) ? $product['need_report'] : '0';
        $record_source = array_key_exists('record_source', $product) ? $product['record_source'] : '1';
        $report_type = array_key_exists('report_source', $product) ? $product['report_source'] : '1';

        // 报告模块
        $report_module = (array_key_exists('output_report', $product) && $product['output_report']) ? [$product['output_report']] : [1, 2];
        $report_notify_url = array_key_exists('report_notify_url', $product) ? $product['report_notify_url'] : '';

        $data = compact('is_push_detail', 'security_push_service', 'notify_url', 'need_itag',
            'need_dunning', 'cuishou_apikey', 'cuishou_appsecret', 'is_vip', 'need_report', 'record_source', 'report_type',
            'report_module', 'report_notify_url');

        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 生成账号
     * @param array $account_create 新生成的账号
     * @param array $product
     * @throws \Exception
     * @return mixed | void
     */
    private function genCrawlerH5Product($account_create, array $product)
    {
        // 如果账号名称重复 && apikey, secret不匹配 则不再进行后续操作
        if ($this->crawler_api_info_matching && $this->determineApiInfoNotMatch($product, $account_create)) {
            return;
        }

        // 记录 $list_crawler_with_account_create_already 属性
        $this->setListCrawlerWithAccountCreateAlready($product);


        // 生成参数
        $params = $this->genParamsForCrawlerH5($account_create, $product);

        // 注册产品
        return $this->registerProduct($params);
    }

    /**
     * 记录已经新建过对应的账号的秒爬产品的记录
     * @param array $product
     */
    public function setListCrawlerWithAccountCreateAlready(array $product)
    {
        if ($this->crawler_api_info_matching) {
            array_push($this->list_crawler_with_account_create_already, $product);
        }
    }

    /**
     * 查看账号或者产品的API相关信息是否不匹配
     * @param array $product
     * @param array $account
     * @return bool
     */
    private function determineApiInfoNotMatch(array $product, array $account)
    {
        // 对比 apikey api secret  还要要求此账号下没有 report  && h5 && api 产品
        if ($this->determineDoubleAccountNameHasProduct($account)) {
            array_push($this->list_account_double_and_has_product_already, $product);
            return true;
        }

        return false;
    }

    /**
     * 名字重复的账号下是否已经有了相应的产品
     * @param array $account
     * @return bool
     */
    private function determineDoubleAccountNameHasProduct(array $account)
    {
        $account_id = $account['account_id'];
        $product_id = [
            'in', [$this->product_id_crawler_h5, $this->product_id_crawler_api, $this->product_id_report]
        ];
        return (boolean)$this->getOneAccountProductByCondition(compact('product_id', 'account_id'));
    }

    /**
     * 注册产品
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function registerProduct(array $params)
    {
        $id = (new AccountProductModel())->add($params);
        if ($id === false) {
            $msg = 'Migrate Error: 新建产品失败,请执行回滚操作! msg:' . json_encode($params, JSON_UNESCAPED_UNICODE);
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        // 入历史队列
        $this->insertHistoryQueue(['table' => 'account_product', 'id' => $id]);
        return $this->getOneAccountProductByCondition(compact('id'));
    }

    /**
     * 获取产品
     * @param array $where
     * @return array
     */
    private function getOneAccountProductByCondition(array $where)
    {
        return (new AccountProductModel())
            ->where($where)
            ->find();
    }

    /**
     * 生成参数
     * @param array $account_create
     * @param array $product
     * @return array
     */
    private function genParamsForCrawlerH5(array $account_create, array $product)
    {
        // 普通参数
        $params_common = $this->genCommonParamsForProduct($account_create, $product, 'crawler_h5');
        $params_common['product_id'] = $this->product_id_crawler_h5;

        // data参数
        $params_data = $this->genDataParamsForCrawlerH5($product);

        return array_merge($params_common, $params_data);
    }

    /**
     * data参数
     * @param array $product
     * @return array
     */
    private function genDataParamsForCrawlerH5(array $product)
    {
        unset($product['protocol_content']);
        $this->response(compact('product'));
        $cid = $product['id'];
        $token = $product['token'];
        $contactor_page = $product['contactor_page'];
        $fixed_channel = $product['fixed_channel'];
        $emergency_contact_max_number = $product['emergency_contact_max_number'];
        $emergency_contact_detail_limits = $this->genEmergencyLimitForH5($product['emergency_contact_detail_limits']);
        $authorize_notify_url = $product['authorize_notify_url'];
        $close_redirect_url = $product['close_redirect_url'];
        $redirect_url_domain = $product['redirect_url_domain'];
        $ui_proposer_show_fields = $product['ui_proposer_show_fields'];
        $ui_proposer_required_fields = $product['ui_proposer_required_fields'];
        $effective_authorization_time = $product['effective_authorization_time'];
        $protocol_default = $product['protocol_default'];
        $protocol_content = $product['protocol_content'];
        $is_three_check = "0";
        $crawl_version = "1";
        $is_editable = "0";

        $data = compact('cid', 'token', 'contactor_page', 'fixed_channel', 'emergency_contact_max_number',
            'emergency_contact_detail_limits', 'authorize_notify_url', 'close_redirect_url', 'redirect_url_domain', 'ui_proposer_show_fields',
            'ui_proposer_required_fields', 'effective_authorization_time', 'protocol_content', 'protocol_default',
            'is_three_check', 'crawl_version', 'is_editable');
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        return compact('data');
    }

    /**
     * 普通参数
     * @param array $account_create
     * @param array $product
     * @param string $product_type 产品类型
     * @return array
     */
    private function genCommonParamsForProduct(array $account_create, array $product, $product_type)
    {
        $account_id = $account_create['account_id'];
        $status = $account_create['status'];
        $use_type = $this->getUseTypeForProduct($product, $product_type);
        $contract_status = defaultValue($product, 'contract_status', 3);
        $end_time = $account_create['end_time'];
        list($daily_limit, $month_limit, $year_limit) = $this->getLimitForProduct($product, $product_type);

        $create_at = $update_at = time();
        return compact('account_id', 'status', 'use_type', 'contract_status',
            'end_time', 'daily_limit', 'month_limit', 'year_limit', 'create_at', 'update_at');
    }

    /**
     * 获取调用类型
     * @param array $product
     * @param $product_type
     * @return int
     */
    private function getUseTypeForProduct(array $product, $product_type)
    {
        switch ($product_type) {
            case 'cuishou':
                return $this->determineIfCuishouProductIsCallback($product) ? 2 : 1;
                break;
            case 'matching':
                return $this->determineIfMatchingProductIsCallback($product) ? 2 : 1;
                break;
            default:
                return 1;
        }
    }

    /**
     * @param array $product
     * @param string $product_type
     * @return array
     */
    private function getLimitForProduct(array $product, $product_type)
    {
        switch ($product_type) {
            case "cuishou":
                $day_limit_key = 'daily_limit';
                $month_limit_key = 'monthly_limit';
                $year_limit_key = 'yearly_limit';
                break;
            case 'matching':
                $day_limit_key = 'daily_limit';
                $month_limit_key = 'monthly_limit';
                $year_limit_key = 'yearly_limit';
                break;
            default :
                $day_limit_key = 'daily_limit';
                $month_limit_key = 'month_limit';
                $year_limit_key = 'year_limit';
                break;
        }
        $day_limit = defaultValue($product, $day_limit_key, '-1');
        $month_limit = defaultValue($product, $month_limit_key, '-1');
        $year_limit = defaultValue($product, $year_limit_key, '-1');

        return [$day_limit, $month_limit, $year_limit];
    }

    /**
     * 注册账号
     * @param array $item
     * @param $product_type
     * @return array
     * @throws \Exception
     */
    private function migrateAccount(array $item, $product_type)
    {
        // 参数
        $params = $this->genParamsForAccount($item, $product_type);

        // 催收,秒配  如果注册的账号名称 已经存在则不再进行后续操作
        if ($this->determine_double_account_name_now  && strpos($product_type, 'crawler') === false) {
            return $this->getTheExistsAccountByName($params);
        }

        // 秒爬是否需要重新生成账户
        if ($account_crawler = $this->determineCreateAccountForCrawler($item)) {
            $this->crawler_api_info_matching = true;
            return $account_crawler;
        }

        // 注册
        return $this->registerAccount($params);
    }

    /**
     * 是否需要重新要为秒爬创建一个账号
     * @param array $product
     * @return array
     */
    private function determineCreateAccountForCrawler(array $product)
    {
        $apikey = $product['appid'];
        $appsecret = $product['appsecret'];

        return $this->getOneAccountByCondition(compact('appsecret', 'apikey'));
    }

    /**
     * 获取已经存在的账号名称的账号
     * @param array $params
     * @return array
     */
    private function getTheExistsAccountByName(array $params)
    {
        $account_name = $params['account_name'];
        return $this->getOneAccountByCondition(compact('account_name'));
    }

    /**
     * 注册账户
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function registerAccount(array $params)
    {
        $id = (new AccountModel())
            ->add($params);
        if ($id === false) {
            $msg = 'Migrate Error: 新建账号失败,请执行回滚操作! msg:' . json_encode($params, JSON_UNESCAPED_UNICODE);
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        // 入历史队列
        $this->insertHistoryQueue(['table' => 'account', 'id' => $id]);
        return $this->getOneAccountByCondition(compact('id'));
    }

    /**
     * 入历史队列
     * @param array $queue_data
     * @throws \Exception
     */
    private function insertHistoryQueue(array $queue_data)
    {
        $validate_error = !array_key_exists('id', $queue_data) || !$queue_data['id']
            || !array_key_exists('data', $queue_data) || !$queue_data['data'];

        if ($validate_error) {
            $msg = 'Repair Error: 填充历史队列的数据错误 msg:' . json_encode($queue_data, JSON_UNESCAPED_UNICODE);
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        (new Redis())->lpush($this->queue_table_migrate, json_encode($queue_data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 为注册账号生成参数
     * @param array $item
     * @param string $product_type
     * @return array
     * @throws \Exception
     */
    private function genParamsForAccount(array $item, $product_type)
    {
        $account_id = $this->genAccountIdForStore();
        $customer_id = $item['customer_id'];
        $father_id = $this->genAccountFatherIdForStore($item);
        $account_name = $this->genAccountNameForStore($item, $product_type);
        list($email, $password) = ['', ''];
        $status = $this->getAccountStatusForStore($item, $product_type);
        $type = 1;
        list($apikey, $appsecret) = $this->setAccountKeyAndSecret($item, $product_type);
        $end_time = $this->getAccountEndTimeForStore($item, $product_type);
        $access_ip = $this->getAccountAccessIpForStore($item, $product_type);
        $mark = '系统迁移';
        $admin = 'system';
        $create_at = $update_at = time();

        return compact('account_id', 'customer_id', 'father_id', 'account_name', 'email', 'password',
            'status', 'type', 'apikey', 'appsecret', 'end_time', 'access_ip', 'mark', 'admin', 'create_at', 'update_at');
    }

    /**
     * 获取账号的白名单
     * @param array $product
     * @param $product_type
     * @return string
     */
    private function getAccountAccessIpForStore(array $product, $product_type)
    {
        if ($product_type == 'cuishou') {
            return array_key_exists('access_ip', $product) && $product['access_ip'] ? serialize($product['access_ip']) : '';
        }

        if ($product_type == 'matching') {
            return array_key_exists('limit_access_ip', $product) && $product['limit_access_ip'] ?: '';
        }

        return '';
    }

    /**
     * 获取账号的截至时间
     * @param array $product
     * @param $product_type
     * @return integer
     */
    private function getAccountEndTimeForStore(array $product, $product_type)
    {
        if ($product_type == 'cuishou') {
            return $product['validuntil'];
        }

        if ($product_type == 'matching') {
            return strtotime($product['validuntil']);
        }

        return $product['expiration_date'];
    }

    /**
     * 设置账号的apikey appsecret
     * @param array $product
     * @param string $product_type
     * @return array
     */
    private function setAccountKeyAndSecret(array $product, $product_type)
    {
        if ($product_type == 'cuishou') {
            return [$product['apikey'], $product['password']];
        }

        if ($product_type === 'matching') {
            return [$product['apikey'], $product['password']];
        }

        return [$product['appid'], $product['appsecret']];
    }

    /**
     * 获取账号状态
     * @param array $product
     * @param string $product_type
     * @return integer
     */
    private function getAccountStatusForStore(array $product, $product_type)
    {
        if ($product_type == 'cuishou') {
            return $product['status'] == 1 ? 1 : 0;
        }

        if ($product_type == 'matching') {
            return $product['active'] == 1 ? 1 : 0;
        }

        return $product['status'] == 1 ?: 0;
    }

    /**
     * @param array $item
     * @param string $product_type
     * @return string
     */
    private function genAccountNameForStore(array $item, $product_type)
    {
        // crawler 账号名称
        if (strpos($product_type, 'crawler') !== false) {
            $this->setDetermineDoubleAccountNameNow($item['developer'], $item);
            return $item['developer'];
        }

        // matching
        if ($product_type === 'matching') {
            $account_name = $this->getMatchingAccountNameForStore($item);
            $this->setDetermineDoubleAccountNameNow($account_name, $item);
            return $account_name;
        }

        // cuishou
        $account_name = $this->getCuishouAccountNameForStore($item);
        $this->setDetermineDoubleAccountNameNow($account_name, $item);
        return $account_name;
    }

    /**
     * 获取催收分析产品的账号名称
     * @param array $product
     * @return string
     */
    private function getCuishouAccountNameForStore(array $product)
    {
        // 判断是否是回调函数
        $is_callback = $this->determineIfCuishouProductIsCallback($product);

        return $is_callback ? $product['developer'] . '(返邦信分详单版V1)' : $product['developer'];
    }

    /**
     * 查看某个邦信分详单版V1产品是否是内部调用的
     * @param array $product
     * @return boolean
     */
    private function determineIfCuishouProductIsCallback(array $product)
    {
        $service_key = $product['apikey'];
        $service_secret = $product['password'];
        return array_key_exists('cuishou', $this->list_product_callback) && in_array(compact('service_key', 'service_secret'), $this->list_product_callback['cuishou']);
    }

    /**
     * 生成邦秒配单号版账号名称
     * @param array $item
     * @return string
     */
    private function getMatchingAccountNameForStore(array $item)
    {
        // 如果不是内部调用的
        if ($this->determineIfMatchingProductIsCallback($item) === false) {
            return $item['owner'];
        }

        return $item['owner'] . '（返邦秒配单号版)';
    }

    /**
     * 查看某个邦秒配单号版产品是否是内部调用的
     * @param array $product
     * @return boolean
     */
    private function determineIfMatchingProductIsCallback(array $product)
    {
        $service_key = $product['apikey'];
        $service_secret = $product['password'];
        return array_key_exists('matching', $this->list_product_callback) && in_array(compact('service_key', 'service_secret'), $this->list_product_callback['matching']);
    }

    /**
     * @param string $account_name
     * @param array $item
     */
    public function setDetermineDoubleAccountNameNow($account_name, array $item)
    {
        $account = $this->getOneAccountByCondition(compact('account_name'));
        $this->determine_double_account_name_now = (boolean)$account;

        // 更新重名属性
        if ($this->determine_double_account_name_now) {
            $this->list_product_with_double_account_name[] = $item;
        }
    }

    /**
     * 新建账号的father_id
     * @param array $item
     * @return string
     * @throws \Exception
     */
    private function genAccountFatherIdForStore(array $item)
    {
        $customer_id = $item['customer_id'];
        $father_id = '0';
        $account_father = $this->getOneAccountByCondition(compact('customer_id', 'father_id'));
        if (!$account_father) {
            $msg = 'Migrate Error: customer_id=' . $customer_id . '的客户缺少father_id=0的账户';
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }
        return $account_father ? $account_father['account_id'] : '0';
    }


    /**
     * 为新建账号生成账号ID
     * @throws \Exception
     * @return array
     */
    protected function genAccountIdForStore()
    {
        $account_id = '';
        while (true) {
            // 生成account_id && 保证这个account_id之前是没有生成的
            $account_id = genAccountId('FA');
            $exists = $this->getOneAccountByCondition(compact('account_id'));
            if (!$exists) {
                break;
            }
        }
        return $account_id;
    }

    /**
     * 获取一个账号
     * @param array $where
     * @return array
     */
    private function getOneAccountByCondition(array $where)
    {
        return (new AccountModel())
            ->where($where)
            ->find();
    }

    /**
     * 聚合数据
     */
    private function genMigrateData()
    {
        $this->intro('... 组装数据中, 请等待 ...');

        // 新版客户体系
        $list_customer_new = $this->getNewCustomer();

        // 老版本的数据体系
        return $this->getOldMigrateData($list_customer_new);
    }

    /**
     * 提示
     * @param string $msg
     */
    private function intro($msg)
    {
        echo $msg . PHP_EOL;
    }

    /**
     * 老版本的数据体系
     * @param array $list_customer_new
     * @return array
     */
    private function getOldMigrateData(array $list_customer_new)
    {
        // 总量老版本的客户
        $list_customer_old = $this->getAllOldCustomer();
        $list_customer_old = array_column($list_customer_old, null, 'name');

        // 容器 填充旧版产品的信息
        $list_container = [];
        array_walk($list_customer_new, function ($item_customer_new) use (&$list_container, $list_customer_old) {
            $list_container = $this->fillOldMigrateData($item_customer_new, $list_customer_old, $list_container);
        });

        return $list_container;
    }

    /**
     * 填充旧版产品西悉
     * @param array $item_customer_new
     * @param array $list_customer_old
     * @param $list_container
     * @return array
     */
    private function fillOldMigrateData(array $item_customer_new, array $list_customer_old, $list_container)
    {
        $name = $item_customer_new['name'];

        // 如果不存在对应的客户 则记录日志
        if (!array_key_exists($name, $list_customer_old)) {
            array_push($this->missing_customer, $item_customer_new);
            return $list_container;
        }

        // 填充下辖的旧版产品信息
        return $this->fillOldProductMigrateData($item_customer_new, $list_customer_old[$name], $list_container);
    }

    /**
     * 填充下辖的旧版产品信息
     * @param array $item_customer_new
     * @param array $customer_old
     * @param array $list_container
     * @return array
     */
    private function fillOldProductMigrateData(array $item_customer_new, array $customer_old, array $list_container)
    {
        // 填充邦秒爬
        $list_container = $this->fillOldCrawlerProductMigrateData($item_customer_new, $customer_old, $list_container);

        // 填充邦信分详单版V1
        $list_container = $this->fillOldCuishouProductMigrateData($item_customer_new, $customer_old, $list_container);

        // 填充邦秒配单号版
        $list_container = $this->fillOldMatchingProductMigrateData($item_customer_new, $customer_old, $list_container);

        return $list_container;
    }

    /**
     * 填充邦秒配单号版
     * @param array $item_customer_new
     * @param array $customer_old
     * @param array $list_container
     * @return array
     */
    private function fillOldMatchingProductMigrateData(array $item_customer_new, array $customer_old, array $list_container)
    {
        $account_id = $customer_old['id'];
        $type_id = 2;
        $list_matching_old = $this->getOldAccountProduct(compact('account_id', 'type_id'));

        // 产看当前产品是否已经在之前就填充过了
        array_walk($list_matching_old, function ($item_relation_old) use (&$list_container, $item_customer_new) {
            // 产品
            $product = $this->getFillMatchingProduct($item_relation_old['product_id']);

            // 如果产品已经publish过了
            if ($this->determineMatchingIsPublished($item_customer_new, $product)) {
                return;
            }

            // 填充容器
            $list_container = $this->appendMatchingToContainer($list_container, $product, $item_customer_new);
        });

        return $list_container;
    }


    /**
     * 向容器中注入邦秒爬产品
     * @param array $list_container
     * @param array $product
     * @param array $item_customer_new
     * @return array
     * @throws \Exception
     */
    private function appendMatchingToContainer(array $list_container, array $product, array $item_customer_new)
    {
        // key && value
        list($key_filled, $value_filled) = $this->getMatchingValueToContainer($product, $item_customer_new);

        // 填充容器
        $customer_id_new = $item_customer_new['customer_id'];
        $list_container[$customer_id_new][$key_filled][] = $value_filled;
        return $list_container;
    }

    /**
     * 获取要填充的信息
     * @param array $product
     * @param array $item_customer_new
     * @return array
     * @throws \Exception
     */
    private function getMatchingValueToContainer(array $product, array $item_customer_new)
    {
        $key_index = 'matching';
        $product['customer_id'] = $item_customer_new['customer_id'];
        return [$key_index, $product];
    }

    /**
     * 判断邦秒爬产品是否已经published过了
     * @param array $item_customer_new
     * @param array $product
     * @return boolean
     */
    private function determineMatchingIsPublished(array $item_customer_new, array $product)
    {
        $apikey = $product['apikey'];
        $appsecret = $product['password'];
        $item_old = compact('appsecret', 'apikey');

        if (array_key_exists('matching', $item_customer_new) && in_array($item_old, $item_customer_new['matching'])) {
            return true;
        }

        return false;
    }

    /**
     * 获取旧版的matching产品
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    private function getFillMatchingProduct($id)
    {
        $product = $this->getOneOldMatchingProduct(compact('id'));
        if (!$product) {
            $msg = 'Migrate Error: 旧版邦秒配单号版产品在id=' . $id . '是不存在的';
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }

        // 附加控制的字段
        $product = $this->appendMatchingFieldToProduct($product, $id);

        // 附加接口输出字段
        return $this->appendApiOutFiledToMatchingProduct($product, $id);
    }

    /**
     * 附加接口输出字段
     * @param array $product
     * @param int $apikeyid
     * @return array
     */
    private function appendApiOutFiledToMatchingProduct(array $product, $apikeyid)
    {
        $status = 1;
        $list_fields = $this->getMatchingApiOutFields(compact('apikeyid', 'status'));

        $product['out_fields'] = array_reduce($list_fields, function ($carry, $item) {
            array_push($carry, $item['fid']);
            return $carry;
        }, []);

        return $product;
    }

    /**
     * 获取邦秒配单号版接受口输出字段
     * @param array $where
     * @return mixed
     */
    private function getMatchingApiOutFields(array $where)
    {
        return (new AdminApikeyFieldsModel())
            ->where($where)
            ->select();
    }

    /**
     * 附加额外的选项
     * @param array $product
     * @param $apikeyid
     * @return array
     */
    private function appendMatchingFieldToProduct(array $product, $apikeyid)
    {
        $enabled = 1;
        $list_fields = $this->getMatchingCtrlField(compact('apikeyid', 'enabled'));
        array_walk($list_fields, function ($item_field) use (&$product) {
            $product[$item_field['field']] = $item_field['value'];
        });

        return $product;
    }


    /**
     * 获取邦秒配单号版产品控制的字段
     * @param array $where
     * @return array
     */
    private function getMatchingCtrlField(array $where)
    {
        return (new AdminApictrlModel())
            ->where($where)
            ->select();
    }

    /**
     * 填充邦信分详单版V1
     * @param array $item_customer_new
     * @param array $customer_old
     * @param array $list_container
     * @return array
     */
    private function fillOldCuishouProductMigrateData(array $item_customer_new, array $customer_old, array $list_container)
    {
        $account_id = $customer_old['id'];
        $type_id = 3;
        $list_cuishou_old = $this->getOldAccountProduct(compact('account_id', 'type_id'));

        // 产看当前产品是否已经在之前就填充过了
        array_walk($list_cuishou_old, function ($item_relation_old) use (&$list_container, $item_customer_new) {
            // 产品
            $product = $this->getFillCuishouProduct($item_relation_old['product_id']);

            // 如果产品已经publish过了
            if ($this->determineCuishouIsPublished($item_customer_new, $product)) {
                return;
            }

            // 填充容器
            $list_container = $this->appendCuishouToContainer($list_container, $product, $item_customer_new);
        });

        return $list_container;
    }

    /**
     * 向容器中注入邦秒爬产品
     * @param array $list_container
     * @param array $product
     * @param array $item_customer_new
     * @return array
     * @throws \Exception
     */
    private function appendCuishouToContainer(array $list_container, array $product, array $item_customer_new)
    {
        // key && value
        list($key_filled, $value_filled) = $this->getCuishouValueToContainer($product, $item_customer_new);

        // 填充容器
        $customer_id_new = $item_customer_new['customer_id'];
        $list_container[$customer_id_new][$key_filled][] = $value_filled;
        return $list_container;
    }

    /**
     * 获取要填充的信息
     * @param array $product
     * @param array $item_customer_new
     * @return array
     * @throws \Exception
     */
    private function getCuishouValueToContainer(array $product, array $item_customer_new)
    {
        $key_index = 'cuishou';
        $product['customer_id'] = $item_customer_new['customer_id'];


        // 如果有异步返回 则补充cuishou_apikey && api_secret
        if ($product['need_itag'] !== 1) {
            return [$key_index, $product];
        }

        $matching_api_info = $this->getCallbackServiceForCuishou($product);
        $product = array_merge($product, $matching_api_info);
        return [$key_index, $product];
    }

    /**
     * 获取邦秒爬异步返回的催收分析服务
     * @param array $product
     * @return array
     * @throws \Exception
     */
    private function getCallbackServiceForCuishou(array $product)
    {
        $callback_service = $this->getOneMatchingServices($product);
        if (!$callback_service) {
            $msg_error = 'Migrate Error: 老版邦信分详单版V1产品 ID:' . $product['id'] . ' 需要异步返回邦秒配单号版，但是却没有找到对应的apikey api secret';
            $this->wehcatException($msg_error);
            throw new \Exception($msg_error);
        }

        $service_key = $callback_service['apikey'];
        $service_secret = $callback_service['password'];

        // 填充到回调容器中
        return $this->list_product_callback['matching'][] = compact('service_key', 'service_secret');
    }

    /**
     * 获取异步返回的邦秒配单号版服务
     * @param array $product
     * @return array
     */
    private function getOneMatchingServices(array $product)
    {
        $apikey = $product['itag_apikey'];
        $password = $product['itag_password'];
        return $this->getOneOldMatchingProduct(compact('apikey', 'password'));
    }

    /**
     * 获取一个旧版的邦秒配单号版
     * @param array $where
     * @return array
     */
    private function getOneOldMatchingProduct(array $where)
    {
        return (new AdminApikeyModel())
            ->where($where)
            ->find();
    }

    /**
     * 判断邦秒爬产品是否已经published过了
     * @param array $item_customer_new
     * @param array $product
     * @return boolean
     */
    private function determineCuishouIsPublished(array $item_customer_new, array $product)
    {
        $apikey = $product['apikey'];
        $appsecret = $product['password'];
        $item_old = compact('appsecret', 'apikey');

        if (array_key_exists('cuishou', $item_customer_new) && in_array($item_old, $item_customer_new['cuishou'])) {
            return true;
        }

        return false;
    }

    /**
     * 获取旧版的crawler产品
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    private function getFillCuishouProduct($id)
    {
        $id = (int)$id;
        $product = $this->getOneOldCuishouProduct(compact('id'));
        if (!$product) {
            $msg = 'Migrate Error: 旧邦信分详单版V1产品在id=' . $id . '是不存在的';
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }
        return $product;
    }

    /**
     * 获取一个旧版的crawler产品
     * @param array $where
     * @return mixed
     */
    private function getOneOldCuishouProduct(array $where)
    {
        return (new CuishouUserModel())->where($where)
            ->find();
    }

    /**
     * 填充邦秒爬
     * @param array $item_customer_new
     * @param array $customer_old
     * @param array $list_container
     * @return array
     */
    private function fillOldCrawlerProductMigrateData(array $item_customer_new, array $customer_old, array $list_container)
    {
        $account_id = $customer_old['id'];
        $type_id = 1;
        $list_crawler_old = $this->getOldAccountProduct(compact('account_id', 'type_id'));

        // 产看当前产品是否已经在之前就填充过了
        array_walk($list_crawler_old, function ($item_relation_old) use (&$list_container, $item_customer_new) {

            // 产品
            $product = $this->getFillCrawlerProduct($item_relation_old['product_id']);

            // 如果产品已经publish过了
            if ($this->determineCrawlerIsPublished($item_customer_new, $product)) {
                return;
            }

            // 填充容器
            $list_container = $this->appendCrawlerToContainer($list_container, $product, $item_customer_new);
        });

        return $list_container;
    }

    /**
     * 向容器中注入邦秒爬产品
     * @param array $list_container
     * @param array $product
     * @param array $item_customer_new
     * @return array
     * @throws \Exception
     */
    private function appendCrawlerToContainer(array $list_container, array $product, array $item_customer_new)
    {
        // key && value
        list($key_filled, $value_filled) = $this->getCrawlerValueToContainer($product, $item_customer_new);

        // 填充容器
        $customer_id_new = $item_customer_new['customer_id'];
        $list_container[$customer_id_new][$key_filled][] = $value_filled;
        return $list_container;
    }

    /**
     * 获取要填充的信息
     * @param array $product
     * @param array $item_customer_new
     * @return array
     * @throws \Exception
     */
    private function getCrawlerValueToContainer(array $product, array $item_customer_new)
    {
        $key_index = ($product['source'] === 'api') ? 'crawler_api' : 'crawler_h5';
        $product['customer_id'] = $item_customer_new['customer_id'];


        // 如果有异步返回 则补充cuishou_apikey && api_secret
        if ($product['need_dunning'] != 1) {
            return [$key_index, $product];
        }

        $cuishou_api_info = $this->getCallbackServiceForCrawler($product);
        $product = array_merge($product, $cuishou_api_info);
        return [$key_index, $product];
    }

    /**
     * 获取邦秒爬异步返回的催收分析服务
     * @param array $product
     * @return array
     * @throws \Exception
     */
    private function getCallbackServiceForCrawler(array $product)
    {
        $uid = $product['id'];
        $service_cateid = 2;
        $callback_service = $this->getOneCrawlerServices(compact('uid', 'service_cateid'));
        if (!$callback_service) {
            $msg_error = 'Migrate Error: 老版邦秒爬产品 ID:' . $product['id'] . ' 需要异步返回催收分，但是却没有找到对应的apikey api secret';
            $this->wehcatException($msg_error);
            throw new \Exception($msg_error);
        }

        $wait_mid = arrOnly($callback_service, ['service_key', 'service_secret']);

        // 填充到回调容器中
        $this->list_product_callback['cuishou'][] = $wait_mid;
        return $wait_mid;
    }

    /**
     * 获取一个爬虫异步返回的服务
     * @param array $where
     * @return array
     */
    private function getOneCrawlerServices(array $where)
    {
        return (new AuxiliaryServices())
            ->where($where)
            ->find();
    }

    /**
     * 获取旧版的crawler产品
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    private function getFillCrawlerProduct($id)
    {
        $product = $this->getOneOldCrawlerProduct(compact('id'));
        if (!$product) {
            $msg = 'Migrate Error:邦秒爬产品在product_id=' . $id . '是不存在的';
//            $this->wehcatException($msg);
//            throw new \Exception($msg);
        }

        // 附加邦信分详单版V1服务
        return $this->appendCuishouToCrawler($id, $product);
    }

    /**
     * 附加邦信分详单版V1服务
     * @param $uid
     * @param array $product
     * @return array
     */
    private function appendCuishouToCrawler($uid, array $product)
    {
        $service_cateid = 2;
        $cuishou_service = $this->getOneCrawlerServiceByCondition(compact('uid', 'service_cateid'));
        $product['cuishou_apikey'] = $cuishou_service ? $cuishou_service['service_key'] : '';
        $product['cuishou_appsecret'] = $cuishou_service ? $cuishou_service['service_secret'] : '';
        return $product;
    }

    /**
     * 获取一个和邦秒爬绑定的服务
     * @param array $where
     * @return array
     */
    private function getOneCrawlerServiceByCondition(array $where)
    {
        return (new AuxiliaryServices())
            ->where($where)
            ->find();
    }

    /**
     * 判断邦秒爬产品是否已经published过了
     * @param array $item_customer_new
     * @param array $product
     * @return boolean
     */
    private function determineCrawlerIsPublished(array $item_customer_new, array $product)
    {
        $apikey = $product['appid'];
        $appsecret = $product['appsecret'];
        $item_old = compact('appsecret', 'apikey');

        if (array_key_exists('crawler_h5', $item_customer_new) && in_array($item_old, $item_customer_new['crawler_h5'])) {
            return true;
        }

        if (array_key_exists('crawler_api', $item_customer_new) && in_array($item_old, $item_customer_new['crawler_api'])) {
            return true;
        }

        return false;
    }

    /**
     * 获取一个旧版的crawler产品
     * @param array $where
     * @return mixed
     */
    private function getOneOldCrawlerProduct(array $where)
    {
        return (new AuthModel())->where($where)
            ->find();
    }


    /**
     * 获取旧版账号产品
     * @param array $where
     * @return array
     */
    private function getOldAccountProduct(array $where)
    {
        return (new FinanceAccountProductModel())
            ->where($where)
            ->select();
    }


    /**
     * 全量老版本的客户
     * @return array
     */
    private function getAllOldCustomer()
    {
        return (new FinanceAccountsModel())
            ->select();
    }

    /**
     * 新版客户体系
     * @return mixed
     */
    private function getNewCustomer()
    {
        // 总量的客户
        $list_customer = $this->getAllNewCustomer();

        // 补充产品的apikey appsecret 相关信息
        return $this->appendApikeyToNewCustomer($list_customer);

    }

    /**
     * 补充产品的apikey appsecret 相关信息
     * @param array $list_customers
     * @return array
     */
    private function appendApikeyToNewCustomer(array $list_customers)
    {
        // 产品
        $list_products = $this->getAllNewProduct();

        // 账号列表
        $list_accounts = $this->getAllNewAccount();

        // 整合
        return $this->tidyNewCustomer($list_customers, $list_accounts, $list_products);
    }

    /**
     * 整合新版客户
     * @param array $list_customers
     * @param array $list_accounts
     * @param array $list_products
     * @return array
     */
    private function tidyNewCustomer(array $list_customers, array $list_accounts, array $list_products)
    {
        $list_accounts = array_column($list_accounts, null, 'account_id');
        $list_customers = array_column($list_customers, null, 'customer_id');
        array_walk($list_products, function ($item_product) use (&$list_customers, $list_accounts) {
            $key_fill = $this->list_product_id_mapping[$item_product['product_id']];

            // 选中的账号
            if (!array_key_exists($item_product['account_id'], $list_accounts)) {
                $this->wehcatException('Migrate Error: ' . $item_product['account_id'] . ' 缺少对应的账号');
                return;
            }
            $account = $list_accounts[$item_product['account_id']];
            $customer_id = $account['customer_id'];
            $value_fill = arrOnly($account, ['apikey', 'appsecret']);

            $list_customers[$customer_id][$key_fill][] = $value_fill;
        });

        return $list_customers;
    }

    /**
     * 新版全量的账号
     * @return array
     */
    private function getAllNewAccount()
    {
        return (new AccountModel())
            ->select();
    }

    /**
     * 新版产品
     * @return array
     */
    private function getAllNewProduct()
    {
        $list_product_ids = array_keys($this->list_product_id_mapping);
        $product_id = ['in', $list_product_ids];
        return $this->getNewAccountProduct(compact('product_id'));
    }

    /**
     * 获取新版的产品
     * @param array $where
     * @return mixed
     */
    private function getNewAccountProduct(array $where)
    {
        return (new AccountProductModel())
            ->where($where)
            ->select();
    }

    /**
     * 总量的客户
     * @return array
     */
    private function getAllNewCustomer()
    {
        $customer_id = ['not in', $this->list_customer_not_migrate];
        return (new CustomerModel())
            ->where(compact('customer_id'))
            ->select();
    }

    /**
     * 检查是否已经migrate过了
     * @return mixed
     */
    public function getMigrateAlready()
    {
        $migrate_done = (new Redis())->get($this->migrate_done_key);
        return $migrate_done === 'done';
    }

    /**
     * 设置成已经更新过了
     * @param string $done
     */
    public function setMigrateAlready($done)
    {
        (new Redis())->set($this->migrate_done_key, $done);
    }
}
