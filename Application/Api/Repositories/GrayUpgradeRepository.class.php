<?php

namespace Api\Repositories;

class GrayUpgradeRepository
{

    /**
     *
     * @throws \Exception
     */
    public function getGrayUpgrade()
    {
        // 校验参数
        $this->verifyParams();

        // 获取发布版本
        $info_upgrade = $this->getUpgradeInfo();
        return isset($info_upgrade['upgrade_body']) ? $info_upgrade['upgrade_body'] : '';
    }

    /**
     *
     */
    protected function getUpgradeInfo()
    {
        $upgrade_key = I('post.key', '', 'trim');

        return D('FinanceGrayUpgradeApi')
            ->where(compact('upgrade_key'))
            ->order('id desc')
            ->find();
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    protected function verifyParams()
    {
        // 基本信息校验
        $this->verifyBaseParams();

        // 校验是否upgrade_key是否存在
        $this->verifyUpgradeKey();

        // 校验签名是否合法
        $this->verifySign();
    }

    /**
     * 校验key是否合法
     * @throws \Exception
     */
    protected function verifyUpgradeKey()
    {
        $upgrade_key = I('post.key', '', 'trim');
        if ($upgrade_key === '') {
            throw new \Exception('请输入合法的策略key');
        }

        // 校验key是不是存在
        $count =  D('FinanceGrayUpgradeApi')
            ->where(compact('upgrade_key'))
            ->count();

        if ($count == 0) {
            throw new \Exception('请重新传入key,当前key不存在');
        }
    }

    /**
     * 检查签名是否合法
     * @throws \Exception
     */
    protected function verifySign()
    {
        $key = I('post.key', '', 'trim');
        $timestamp = I('post.timestamp', '', 'trim');
        $random =  I('post.random', '', 'trim');
        $sign = I('post.sign', '', 'trim');

        // 生成签名
        $verify_sign = $this->genSign(compact('key', 'timestamp', 'random'));

        if ($verify_sign != $sign) {
            throw new \Exception('签名错误');
        }
    }

    /**
     * 生成签名
     * @param array $params_data 参数
     * @return string
     */
    protected function genSign($params_data)
    {
        sort($params_data, SORT_STRING);
        return md5(md5(implode($params_data)));
    }


    /**
     * 校验参数是否存在
     * @throws \Exception
     */
    protected function verifyBaseParams()
    {
        $timestamp = I('post.timestamp', '', 'trim');
        $sign = I('post.sign', '', 'trim');
        $random =  I('post.random', '', 'trim');

        if ($timestamp === '' || !is_numeric($timestamp)) {
            throw new \Exception('请输入合法的时间戳参数timestamp');
        }
        if ($sign === '') {
            throw new \Exception('请输入合法的签名');
        }
        if ($random === '') {
            throw new \Exception('请输入合法的四位随机字符串');
        }

        // 超过10分钟 则过期
        $time_diff = abs(time()-$timestamp);

        if ($time_diff > 600) {
            throw new \Exception('当前签名已经过期，请重新请求(10分钟内有效)');
        }
    }
}