<?php

namespace Api\Repositories;

use Api\Model\CuishouDailyStatModel;

class StatCuishouRepository
{
    private $biz;

    /*
     * 产品必然要获取的单元
     * */
    private $item_base = [
        'success_counts' => 0,
        'access_counts' => 0
    ];

    public function __construct($biz)
    {
        $this->biz = $biz;
    }


    public function getStatListForDetail()
    {
        // 检查参数
        $this->checkParamsForDetail();

        // 获取选定的产品
        $product = $this->getOneProductByIdForDetail();

        // 条件
        $where = $this->genConditionForDetail();

        // 需要展示的日期列表
        $date_list = $this->showDateListForDetail();

        // 这段日期各天的统计信息
        $date_stat_show = $this->dailyStat($where, $date_list);

        // 这段日期的数据总量
        $total_data = $this->chooseOneStat($where);


        return compact('total_data', 'date_stat_show', 'product');
    }

    /**
     * 为详情页检查参数
     */
    public function  checkParamsForDetail()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            throw new \Exception('缺少参数,催收产品的ID');
        }
    }

    /**
     * 获取单个产品在某段时间的指标的统计
     * @param $where
     * @param string $group_type
     * @return array
     */
    public function chooseOneStat($where, $group_type = '$uid')
    {
        // 统计信息
        $info_stat = $this->statGroupByItem($where, $group_type);
        return isset($info_stat[0]) ? array_merge($this->item_base, $info_stat[0]) : $this->item_base;
    }

    /**
     * 某个产品在某段时间的数据情况
     * @param array $where 条件
     * @param $date_list
     * @return mixed
     */
    protected function dailyStat($where, $date_list)
    {
        // 统计信息
        $info_date_stat = $this->statGroupByItem($where, '$time');

        // 原始统计数据变成以‘2018-04-03’为索引的数组 && 计算覆盖率
        $info_date_stat = $this->tidyIndexFormatForDetailTongji($info_date_stat);

        // 将统计信息和需要展示的日期合并
        return $this->tidyDataForDetail($info_date_stat, $date_list);
    }

    /**
     * 统计信息合并到日期中(以天为单位)
     * @param $info_date_stat
     * @param $date_list
     * @return mixed
     */
    protected function tidyDataForDetail($info_date_stat, $date_list)
    {
        // 统计信息合并到日期中
        foreach ($date_list as $date => $date_stat_show) {
            $date_list[$date] = isset($info_date_stat[$date]) ? array_merge($this->item_base, $info_date_stat[$date]) : $this->item_base;
        }
        return $date_list;
    }

    /**
     * 原始的统计数据变成以‘2018-04-03’为索引的数组  && 计算覆盖率
     * @param array $info_date_stat
     * @return array
     */
    protected function tidyIndexFormatForDetailTongji($info_date_stat)
    {
        $info_date_format = [];
        if ($info_date_stat) {
            $info_date_stat = array_column($info_date_stat, null, '_id');
            foreach ($info_date_stat as $date => $date_stat) {

                // 确保每个天都有基本的元素
                $date_stat = array_merge($this->item_base, $date_stat);

                $date_format = date('Y-m-d', strtotime($date));
                $info_date_format[$date_format] = $date_stat;
            }
        }
        return $info_date_format;
    }

    /**
     * 详情页需要展示日期的列表
     * @return array
     */
    protected function showDateListForDetail()
    {
        $begin = I('get.time_begin', '', 'trim');
        $end = I('get.time_end', '', 'trim');

        if (!$begin || !$end) {
            // 默认时间
            $begin = time() - 86400 * 30;
            $end = time();
        } else {
            $begin = strtotime($begin);
            $end = strtotime($end) + 86399;
        }

        return $this->dateList($begin, $end);
    }

    /**
     * 详情页要展示的date list, 这个时间是倒叙的
     * @param integer $begin
     * @param integer $end
     * @return array
     */
    public function dateList($begin, $end)
    {
        $date_list = [];

        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }

        return $date_list;
    }

    /**
     * 为详情页生成条件
     * @return array
     */
    protected function genConditionForDetail()
    {
        // 时间限制
        $where = $this->timeLimitForDetail();

        // 特定产品限制
        $where['uid'] = (int)I('get.id');

        return $where;
    }

    /**
     * 详情页的时间限制
     */
    public function timeLimitForDetail()
    {
        $begin = I('get.time_begin', '');
        $end = I('get.time_end', '');

        // 默认时间
        if (!$begin || !$end) {
            $begin_default = time() - 86400 * 30;
            $time = ['$gte' => date('Ymd', $begin_default)];
            return compact('time');
        }

        // 时间限制
        $begin = date('Ymd', strtotime($begin));
        $end = date('Ymd', strtotime($end) + 86399);
        $time = [
            '$gte' => $begin,
            '$lte' => $end
        ];

        return compact('time');
    }


    /**
     * 根据ID为详情页获取指定的产品(GET)
     */
    protected function getOneProductByIdForDetail()
    {
        $id = I('get.id', '', 'trim');
        $id = new \MongoInt32($id);
        return D('CuishouUser')
            ->where(compact('id'))
            ->find();
    }

    /**
     * 催收分的列表统计
     */
    public function getStatListForGet()
    {
        // 获取登录用户下辖催收分产品的
        $product_list = $this->getCuishouProductOfLoginAccount();

        // 条件
        $where = $this->genConditionForList();

        // 分组获得统计数据
        $personal_list = $this->statGroupByItem($where);

        // 合并产品和分组数据
        return $this->MergeDataForProductList($product_list, $personal_list);
    }

    /**
     * 为了列表统计生成条件
     */
    protected function genConditionForList()
    {
        // 时间限制
        return $this->timeLimitForList();
    }

    /**
     * 将用户列表和分组得到的数据合并(以用户列表为基准,将数据补齐)
     * @param $product_list
     * @param $personal_list
     * @return array 新版的用户列表和数据统计
     */
    protected function MergeDataForProductList($product_list, $personal_list)
    {
        // merge stat and user info
        $personal_list = array_column($personal_list, null, '_id');

        $total_stat = [
            'success_counts' => 0,
            'access_counts' => 0,
        ];

        foreach ($product_list as &$product) {

            $product_id = $product['id'];

            // 合并
            $stat = isset($personal_list[$product_id]) ? array_merge($this->item_base, $personal_list[$product_id]) : $this->item_base;
            $product = array_merge($product, $stat);

            // 总计统计
            $total_stat['access_counts'] += $stat['access_counts'];
            $total_stat['success_counts'] += $stat['success_counts'];
        }

        return compact('product_list', 'total_stat');
    }

    /**
     * 遍历计算各个账户的催收覆盖率
     * @param $personal_list
     * @return array
     */
    protected function cuiShouCoverageForSingle($personal_list)
    {
        /*
         *  计算公式
         * （1）催收覆盖率=含催收号码的详单量/有效调用量
         * （2）疑似催收覆盖率=含疑似催收号码的详单量/有效调用量
         * （3）整体覆盖率=100%-不含催收也不含疑似催收的详单量/有效调用量
         * */

        return array_map(function ($item) {
            // 确保item包含基本的8个元素
            $item = array_merge($this->item_base, $item);
            return $this->computeCoverage($item);
        }, $personal_list);
    }


    /**
     * 在某个条件下催收分各个指标的分组统计情况
     * @param $where
     * @param string $group_type
     * @return array
     */
    public function statGroupByItem($where, $group_type = '$uid')
    {
        return (new CuishouDailyStatModel())->accessCount($where, $group_type);
    }


    /**
     * 列表时间上的制约
     * @return mixed
     */
    protected function timeLimitForList()
    {
        $begin = I('post.time_begin', '', 'trim');
        $end = I('post.time_end', '', 'trim');

        // 默认时间限制
        if (!$begin || !$end) {
            $time = date('Ymd');
            return compact('time');
        }

        // 时间限制
        $begin = strtotime($begin);
        $end = strtotime($end) + 86399;
        $time = [
            '$gte' => date('Ymd', $begin),
            '$lte' => date('Ymd', $end)
        ];
        return compact('time');
    }

    /**
     * 获取登录用户下辖催收分产品的
     * @return array
     */
    protected function getCuishouProductOfLoginAccount()
    {
        // 限定登录客户下辖的id
        $list_ids = $this->biz['list_cuishou'];
        $list_ids = array_map(function ($item) {
            return new \MongoInt32($item);
        }, $list_ids);
        $where['id'] = ['$in' => $list_ids];

        $fields = ['id' => 1, '_id' => 0, 'developer' => 1, 'status' => 1];
        return D('CuishouUser')->where($where)
            ->field($fields)
            ->select();
    }
}