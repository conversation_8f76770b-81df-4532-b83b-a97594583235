<?php

namespace Api\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Common\CurlTrait;
use Common\Common\ResponseTrait;
use Common\Common\WechatBackendExceptionTrait;

class BackendStatOverviewRepository
{
    use CurlTrait, WechatBackendExceptionTrait, ResponseTrait;

    /*
     * 调用量接口
     * */
    private $url_amount_api;

    /**
     * APIKEY
     */
    protected $apiKey = '0aa2cb33d0eaedc4abe412348045dc8';

    /*
     * APISECRET
     * */
    protected $apiSecret = '0b178ed2d1d049e46472711d8f92bf4';

    /**
     * 列表统计
     */
    protected $listUrl = '/admin/crawler/list';

    /*
     * 产品id列表
     * */
    private $list_product_ids = [
        'cuishou_v2' => 105, // 邦信分详单版V2
        'pei_single' => 601, // 邦秒配单号版
        'pei_xiangdan' => 104, // 邦秒配详单版
        'bang' => 401, // 邦企查
        'tel' => 801, // 号码状态查询
        'cuishou_private' => 501, // 催收分私有云
        'pa_api' => 302, // 邦秒爬api
        'pa_h5' => 301, // 邦秒爬H5版
        'data_validate' => 200, // 邦秒验
        'cuishou_xiangdan' => 101, // 催收详单版
        'cuishou_short' => 210, // 邦秒信快捷版
    ];

    /*
     * 折线图标记到产品名称的映射
     * */
    private $list_mapping_tag_to_name = [
        'cuishou_v2' => '邦信分详单版V2',
        'pei_single' => '邦秒配单号版',
        'pei_xiangdan' => '邦秒配详单版',
        'bang' => '邦企查',
        'tel' => '号码状态查询',
        'cuishou_private' => '邦信分私有云',
        'pa_api' => '邦秒爬api',
        'pa_h5' => '邦秒爬H5版',
        'data_validate' => '邦秒验',
        'cuishou_xiangdan' => '邦信分详单版V1',
        'crawler' => '邦秒爬',
        'miao_pei' => '邦秒配单号版'
    ];

    /*
     * 字段映射（中文）
     * */
    private $list_mapping_date_to_chinese = [
        'today' => '今天',
        'yesterday' => '昨天',
        '7days' => '近7天',
        '30days' => '近30天',
        '90days' => '近90天',
        'year' => '近一年'
    ];

    /*
     * 生成折线图花费的时间的记录
     * */
    private $time_speed_record_for_chart;

    /*
     * 生成pie图 api消耗的时间
     * */
    private $api_speed_time_of_pie;

    /*
     * 生成折线图的颜色集合
     * */
    private $list_color_for_chart = [
        '#800080', '#FF00FF', '#000080',
        '#0000FF', '#008080', '#00FFFF',
        '#008000', '#00FF00', '#808000',
        '#FFFF00', '#800000', '#FF0000',
        '#000000', '#808080', '#C0C0C0',
    ];

    /*
     * table模式时间条件
     * */
    private $list_times_table;

    public function __construct()
    {
        $this->url_amount_api = C('LIST_API_URL')['backend_api_amount'];

        $today = date('Ymd');
        $yesterday = date('Ymd', strtotime('-1 day'));
        $this->list_times_table = [
            'today' => ['start_time' => $today, 'end_time' => $today],
            'yesterday' => ['start_time' => $yesterday, 'end_time' => $yesterday],
            '7days' => ['start_time' => date('Ymd', strtotime('-6 days')), 'end_time' => $today],
            '30days' => ['start_time' => date('Ymd', strtotime('-29 days')), 'end_time' => $today],
            '90days' => ['start_time' => date('Ymd', strtotime('-89 days')), 'end_time' => $today],
            'year' => ['start_time' => date('Ymd', strtotime('-364 days')), 'end_time' => $today],
        ];
    }

    /**
     * 初始化时间参数
     * @return array
     * @throws \Exception
     */
    public function initTimeForChart()
    {
        $this->limitMethodGet();
        $end_time = date('Y-m-d');
        $start_time = date('Y-m-d', strtotime('-30 days'));
        return compact('start_time', 'end_time');
    }

    /**
     * 饼图
     * @throws \Exception
     * @return array
     */
    public function amountPie()
    {
        // 检验参数
        $this->validateParamsForPie();

        // 获取选中的参数
        $product_type = I('get.product_type', '', 'trim');
        switch ($product_type) {
            case 'cuishou_v2' : // 邦信分详单版V2
                return $this->getAmountOfBackendForPie('cuishou_v2');
                break;
            case 'bang': // 邦企查
                return $this->getAmountOfBackendForPie('bang');
                break;
            case  'data_validate': // 邦秒验
                return $this->getAmountOfBackendForPie('data_validate');
                break;
            case "cuishou_xiangdan": // 邦信分详单版V1
                return $this->getAmountOfBackendForPie('cuishou_xiangdan');
                break;
            case "pei_xiangdan": // 帮秒配详单版
                return $this->getAmountOfBackendForPie($product_type);
                break;
            case "pei_single": // 帮秒配单号版
                return $this->getAmountOfBackendForPie($product_type);
                break;
            case "miao_pei": // 秒配(单号版 详单版二合一)
                return $this->getPeiForPie();
                break;
            case "cuishou_private": // 老版本的数据来源
                return $this->getCuishouPrivateForPie();
                break;
            case "crawler":  // 老版本来源(邦秒爬)
                return $this->getCrawlerForPie();
                break;
            default :
                throw new \Exception('不支持该产品');
        }
    }

    /**
     * 秒配饼图
     * @return array
     * @throws \Exception
     */
    private function getPeiForPie()
    {
        // 获取秒配的分组信息
        list($list_apikey_customer, $list_apikey_amount) = $this->getPeiAmountOfGroupForPie();

        // 格式化数据
        return $this->formatDataOfGroupForPie($list_apikey_customer, $list_apikey_amount);
    }

    /**
     * @return array
     * @throws \Exception
     */
    private function getPeiAmountOfGroupForPie()
    {
        // 单号版分组信息
        list($list_single_mapping, $list_single_group_amount) = $this->getPeiOfGroupForPieByIndex('pei_single');

        // 详单版分组信息
        list($list_xiangdan_mapping, $list_xiangdan_group_amount) = $this->getPeiOfGroupForPieByIndex('pei_xiangdan');

        // 组合
        $list_apikey_customer = array_merge($list_single_mapping, $list_xiangdan_mapping);
        $list_apikey_amount = [];
        array_walk($list_apikey_customer, function($customer_id, $apikey) use(&$list_apikey_amount, $list_single_group_amount, $list_xiangdan_group_amount){
            $amount_single = array_key_exists($apikey, $list_single_group_amount) ? $list_single_group_amount[$apikey] : 0;
            $amount_xiangdan = array_key_exists($apikey, $list_xiangdan_group_amount) ? $list_xiangdan_group_amount[$apikey] : 0;
            $list_apikey_amount[$apikey] = $amount_single + $amount_xiangdan;
        });

        return [$list_apikey_customer, $list_apikey_amount];
    }

    /**
     * 单号版本的分组信息
     * @param $index
     * @return array
     * @throws \Exception
     */
    private function getPeiOfGroupForPieByIndex($index)
    {
        // 产品id
        $product_id= $this->list_product_ids[$index];

        // 客户下辖的拥有该产品的apikey列表
        $list_apikey_customer = $this->getApikeyofCustomerByProductId($product_id);
        $list_apikeys = array_keys($list_apikey_customer);

        // 获取apikey分组的接口调用
        $list_apikey_amount = $this->getInvokedAmountForGroup($product_id, $list_apikeys);

        return [$list_apikey_customer, $list_apikey_amount];
    }

    /**
     * 秒爬饼图数据
     * @return array
     * @throws \Exception
     */
    private function getCrawlerForPie()
    {
        // 获取cid到customer_id的映射关系
        $list_cid_customer = $this->getCidsofCustomerByProductId();
        $list_cids = array_keys($list_cid_customer);

        // 获取cid分组的调用信息
        $list_cids_amount = $this->getCrawlerAmountOfGroupForPie($list_cids);

        // 格式化数据
        return $this->formatDataOfGroupForPie($list_cid_customer, $list_cids_amount);
    }

    /**
     * @param array $list_cids
     * @return array
     * @throws \Exception
     */
    private function getCrawlerAmountOfGroupForPie(array $list_cids)
    {
        $time_begin = microtime(true);
        list($url_api, $params) = $this->genCrawlerParamsOfGroupForPie($list_cids);

        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($url_api, $params);
            if ($response['status'] == 0 || $i >= 3) {
                break;
            }
        }

        // 检查是否成功调用
        $this->validateSuccessInvokedCrawlerForTable($response, $url_api, $params);

        // 设置调用时间
        $this->api_speed_time_of_pie = microtime(true) - $time_begin;

        $list_amount = array_key_exists('data', $response) ? $response['data'] : [];
        return array_column($list_amount, 'crawl_nums', 'cid');
    }

    /**
     * 生成参数
     * @param array $list_cids
     * @return array
     */
    private function genCrawlerParamsOfGroupForPie(array $list_cids)
    {
        // cid限制
        $cids = '[' . implode(',', $list_cids) . ']';

        // 时间参数
        $dimension_date = I('get.dimension_date', '', 'trim');
        $end_date = date('Ymd');
        $start_date = date('Ymd', strtotime('-' . $dimension_date . ' days'));

        // 签名
        $url_api = C('CRS_API_CONFIG')['domain'] . $this->listUrl;
        $params_sign = $this->getSign();
        $params = array_merge($params_sign, compact('start_date', 'end_date', 'cids'));
        return [$url_api, $params];
    }

    /**
     * 获取cid到customer_id的映射关系
     * @return mixed
     */
    private function getCidsofCustomerByProductId()
    {
        // 获取拥有该产品的账号
        $list_account_products = $this->getAccountListWhichHasCrawler();

        // 获取cid到customer_id的映射关系
        return $this->getCrawlerMapingFromCidToCustomer($list_account_products);
    }

    /**
     * 获取cid到customer_id的映射关系
     * @param array $list_account_products
     * @return mixed
     */
    private function getCrawlerMapingFromCidToCustomer(array $list_account_products)
    {
        // 客户信息
        $list_account_ids = array_keys($list_account_products);
        $account_id = ['in', $list_account_ids ? $list_account_ids : ['不存在的']];

        // 获取拥有该产品的客户
        $list_account_customer = $this->getListAccountByCondition(compact('account_id'), ['customer_id', 'account_id']);

        // 格式化 'cid' => ‘customer_id’
        return array_reduce($list_account_customer, function ($carry, $item) use ($list_account_products) {
            list($customer_id, $account_id) = [$item['customer_id'], $item['account_id']];

            $cid = $list_account_products[$account_id];
            if (is_numeric($cid)) {
                $carry[$cid] = $customer_id;
            }
            return $carry;
        }, []);
    }


    /**
     * 开通秒爬产品的账号列表
     * @return array
     */
    private function getAccountListWhichHasCrawler()
    {
        $product_id = ['in', [$this->list_product_ids['pa_api'], $this->list_product_ids['pa_h5']]];

        // 获取拥有该产品的账号
        $list_account_products = $this->getListAccountProductByCondition(compact('product_id'), ['account_id', 'data']);

        return array_reduce($list_account_products, function ($carry, $item) {
            $data = json_decode($item['data'], true);
            $cid = array_key_exists('cid', $data) ? $data['cid'] : '不存在CID';
            $carry[$item['account_id']] = $cid;
            return $carry;
        }, []);
    }

    /**
     * 获取邦信分私有云Pie信息
     * @return array
     * @throws \Exception
     */
    private function getCuishouPrivateForPie()
    {
        // 客户下辖的拥有该产品的apikey列表
        $list_apikey_customer = $this->getApikeyofCustomerByProductId($this->list_product_ids['cuishou_private']);
        $list_apikeys = array_keys($list_apikey_customer);

        // 获取apikey分组的接口调用
        $list_apikey_amount = $this->getCuishouPrivateAmountForGroup($list_apikeys);

        // 格式化数据
        return $this->formatDataOfGroupForPie($list_apikey_customer, $list_apikey_amount);
    }

    /**
     * 获取邦信分私有云分组的信息
     * @param array $list_apikeys
     * @return array
     * @throws \Exception
     */
    private function getCuishouPrivateAmountForGroup(array $list_apikeys)
    {
        $time_begin = microtime(true);
        // 参数
        list($params, $url_api) = $this->genParamsOfCuishouPrivateForPie($list_apikeys);

        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($url_api, $params);
            if ($response['code'] == 0 || $i >= 3) {
                break;
            }
        }

        // 检查是否成成调用
        $this->validateSuccessInvokedCuishouPrivateForTable($response, $params, $url_api);
        $list_apikey_amount = array_key_exists('stat', $response['data']) ? $response['data']['stat'] : [];

        // 设置接口消耗时间
        $this->api_speed_time_of_pie = microtime(true) - $time_begin;
        return array_column($list_apikey_amount, 'sum_counts', 'apikey');
    }

    /**
     * 生成参数
     * @param array $list_apikeys
     * @return array
     */
    private function genParamsOfCuishouPrivateForPie(array $list_apikeys)
    {
        $dimension_date = I('get.dimension_date', '', 'trim');
        $date_end = date('Ymd');
        $date_start = date('Ymd', strtotime('-' . $dimension_date . ' days'));
        $apikey = implode(',', $list_apikeys);

        return [compact('apikey', 'date_end', 'date_start'), C('LIST_API_URL')['cuishou_private_list']];
    }

    /**
     * @param string $index_product
     * @return array
     * @throws \Exception
     */
    private function getAmountOfBackendForPie($index_product)
    {
        // 客户下辖的拥有该产品的apikey列表
        $list_apikey_customer = $this->getApikeyofCustomerByProductId($this->list_product_ids[$index_product]);
        $list_apikeys = array_keys($list_apikey_customer);

        // 获取apikey分组的接口调用
        $list_apikey_amount = $this->getInvokedAmountForGroup($this->list_product_ids[$index_product], $list_apikeys);

        // 格式化数据
        return $this->formatDataOfGroupForPie($list_apikey_customer, $list_apikey_amount);
    }

    /**
     * 格式化参数
     * @param array $list_apikey_customer
     * @param array $list_apikey_amount
     * @return array
     */
    private function formatDataOfGroupForPie(array $list_apikey_customer, array $list_apikey_amount)
    {
        // 格式化
        list($list_customers, $total) = $this->formatBaseOfGroupForPie($list_apikey_customer, $list_apikey_amount);

        // 排序 && 计算比重
        return $this->sortAndComputedDataOfGroupForPie($list_customers, $total);
    }

    /**
     *  排序 && 计算比重
     * @param array $list_customers
     * @param $total
     * @return array
     */
    private function sortAndComputedDataOfGroupForPie(array $list_customers, $total)
    {
        // 计算比重
        $list_customers = $this->computedWeightOfGroupForPie($list_customers, $total);

        // 排序
        $list_customers_sorted = $this->sortDataOfGroupForPie($list_customers);

        // Top截取
        return $this->cutDataForPie($list_customers_sorted, $total);
    }

    /**
     * Top截取
     * @param array $list_customers_sorted
     * @param int $total
     * @return array
     */
    private function cutDataForPie(array $list_customers_sorted, $total)
    {
        $dimension_customer = I('get.dimension_customer', '', 'trim');
        $list_customer_cuted = array_slice($list_customers_sorted, 0, $dimension_customer);

        // 计算weight
        $amount_cuted = array_reduce($list_customer_cuted, function ($carry, $item) {
            $carry += $item['amount'];
            return $carry;
        }, 0);

        $weight = $this->formatPercentage(($total - $amount_cuted) / $total);
        $other_item = [
            'customer_name' => '其他(' . $weight . ')',
            'amount' => $total - $amount_cuted
        ];

        array_push($list_customer_cuted, $other_item);

        $list_customer_name_cuted = array_column($list_customer_cuted, 'customer_name');
        $list_customer_amount_cuted = array_column($list_customer_cuted, 'amount');
        $api_speed_time_of_pie = $this->api_speed_time_of_pie;
        return compact('list_customer_name_cuted', 'list_customer_amount_cuted', 'total', 'list_customers_sorted', 'api_speed_time_of_pie');
    }

    /**
     * 排序
     * @param array $list_customers
     * @return array
     */
    private function sortDataOfGroupForPie(array $list_customers)
    {
        $list_amount_sort = array_reduce($list_customers, function ($carry, $item) {
            array_push($carry, $item['amount']);
            return $carry;
        }, []);

        array_multisort($list_amount_sort, SORT_DESC, SORT_NUMERIC, $list_customers);
        return $list_customers;
    }

    /**
     * 计算比重
     * @param array $list_customers
     * @param $total
     * @return array
     */
    private function computedWeightOfGroupForPie(array $list_customers, $total)
    {
        return array_map(function ($item) use ($total) {
            $item['amount'] = $amount = array_key_exists('amount', $item) ? $item['amount'] : 0;
            $item['weight'] = $weight = $this->formatPercentage($amount / $total);
            $item['customer_name'] = $item['name'] . '(' . $weight . ')';
            return $item;
        }, $list_customers);
    }

    /**
     * 百分比格式化
     * @param $item
     * @return string
     */
    public function formatPercentage($item)
    {
        return round($item * 100, 2) . '%';
    }

    /**
     * 格式化
     * @param array $list_apikey_customer
     * @param array $list_apikey_amount
     * @return array
     */
    private function formatBaseOfGroupForPie(array $list_apikey_customer, array $list_apikey_amount)
    {
        $list_customers = $this->getListCustomerByCondition([], ['customer_id', 'name']);
        $list_customers = array_column($list_customers, null, 'customer_id');
        $total = 0;
        array_walk($list_apikey_amount, function ($amount, $apikey) use (&$total, &$list_customers, $list_apikey_customer) {
            $customer_id = $list_apikey_customer[$apikey];
            // 初始化第一个数值
            if (!array_key_exists('amount', $list_customers[$customer_id])) {
                $list_customers[$customer_id]['amount'] = 0;
            }

            $list_customers[$customer_id]['amount'] += $amount;
            $total += $amount;
        });

        return [$list_customers, $total];
    }

    /**
     * 获取客户列表
     * @param array $where
     * @param string $field
     * @return array
     */
    private function getListCustomerByCondition(array $where, $field = '*')
    {
        return (new CustomerModel())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 获取apikey分组的接口调用
     * @param int $product_id
     * @param array $list_apikeys
     * @throws \Exception
     * @return array
     */
    private function getInvokedAmountForGroup($product_id, array $list_apikeys = [])
    {
        $time_begin = microtime(true);

        // 参数
        $params = $this->genParamsOfGroupForPie($product_id, $list_apikeys);
        
        // apikey分组的接口调用
        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($this->url_amount_api, $params);
            if ($i > 3 || $response['status'] == 0) {
                break;
            }
        }
        $this->checkResponseAmountApi($params, $response);

        $this->api_speed_time_of_pie = microtime(true) - $time_begin;
        return $response['amount'];
    }


    /**
     * 获取参数
     * @param $product_id
     * @param array $apikey
     * @return array
     * @throws \Exception
     */
    private function genParamsOfGroupForPie($product_id, array $apikey)
    {
        $dimension_date = I('get.dimension_date', '', 'trim');
        $end_time = date('Ymd');
        $start_time = date('Ymd', strtotime('-' . $dimension_date . ' days'));
        $key = $this->getProductKeyById($product_id);
        $group = 'apikey';
        return compact('end_time', 'start_time', 'key', 'group', 'apikey');
    }

    /**
     * 根据产品id获取客户下辖的对应的apikey列表
     * @param string $product_id
     * @return array
     */
    private function getApikeyofCustomerByProductId($product_id)
    {
        // 获取拥有该产品的账号
        $list_account_products = $this->getListAccountProductByCondition(compact('product_id'), ['account_id']);
        $list_account_ids = array_column($list_account_products, 'account_id');
        $account_id = ['in', $list_account_ids ? $list_account_ids : ['不存在的']];

        // 获取拥有该产品的客户
        $list_account_customer = $this->getListAccountByCondition(compact('account_id'), ['apikey', 'customer_id']);
        return array_column($list_account_customer, 'customer_id', 'apikey');
    }

    /**
     * 获取账号列表
     * @param array $where
     * @param string|array $field
     * @return mixed
     */
    private function getListAccountByCondition(array $where, $field = '*')
    {
        return (new AccountModel())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 获取产品账号列表
     * @param array $where
     * @param string|mixed $field
     * @return mixed
     */
    private function getListAccountProductByCondition(array $where, $field = '*')
    {
        return (new AccountProductModel())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 检查条件
     * @throws \Exception
     */
    private function validateParamsForPie()
    {
        // 访问方式
        $this->limitMethodGet();

        $dimension_customer = I('get.dimension_customer', '', 'trim');
        if (!$dimension_customer) {
            throw new \Exception('请传入客户维度限制');
        }

        $dimension_date = I('get.dimension_date', '', 'trim');
        if (!$dimension_date && $dimension_date !== '0' && $dimension_date !== 0) {
            throw new \Exception('请传入时间维度的限制');
        }

        // 访问产品
        $product_type = I('get.product_type', '', 'trim');
        if (!$product_type) {
            throw new \Exception('请传入product_type参数,标注请求的产品');
        }
    }

    /**
     * 统计概览曲线图(各天各个产品的调用量)
     * @throws \Exception
     * @return array
     */
    public function amountChart()
    {
        // 检查参数
        $this->validateParamsForChart();

        // 计算各个产品各天的调用量
        return $this->getProductAmountForChart();
    }

    /**
     * 计算各个产品各天的调用量
     * @return array
     * @throws \Exception
     */
    private function getProductAmountForChart()
    {
        // 获取各个产品的数据
        $list_day_data = $this->getRequestProductAmountOfDayForChart();

        // 格式化数据
        return $this->formatDataForChart($list_day_data);
    }

    /**
     * 格式化数据
     * @param array $list_day_data
     * @return array
     */
    private function formatDataForChart(array $list_day_data)
    {
        // 需要展示的时间
        $list_days_need = $this->getListDayWhichNeedShowForChart();

        // 格式化
        $list_container = [];
        $counter = 0;
        array_walk($list_day_data, function($list_day_amount, $product_tag) use ($list_days_need, &$list_container, &$counter){
            // 补全每一天的数据
            $data = $this->fixedEveryDayDataForChart($list_day_amount, $list_days_need);
            $backgroundColor = $this->list_color_for_chart[$counter];
            $label = $this->list_mapping_tag_to_name[$product_tag];

            array_push($list_container, compact('data', 'backgroundColor', 'label'));
            $counter ++;
        });

        return [
            'labels' => $list_days_need,
            'datasets' => $list_container
        ];
    }

    /**
     * 补全每一天的数据
     * @param array $list_day_amount
     * @param array $list_days_need
     * @return array
     */
    private function fixedEveryDayDataForChart(array $list_day_amount,array $list_days_need)
    {
        $list_container = [];
        array_walk($list_days_need, function($day) use (&$list_container, $list_day_amount){
            $amount_day = array_key_exists($day, $list_day_amount) ? $list_day_amount[$day] : 0;
            array_push($list_container, $amount_day);
        });

        return $list_container;
    }

    /**
     * 折线图需要展现的时间范围
     * @return array
     */
    private function getListDayWhichNeedShowForChart()
    {
        list($day_start, $day_end) = $this->listDaysForChart();
        $list_container = [];
        while ($day_start <= $day_end) {
            array_push($list_container, $day_start);
            $day_start = date('Ymd', strtotime('+1 day', strtotime($day_start)));
        }

        return $list_container;
    }

    /**
     * 折线图的时间参数
     * @return array
     */
    private function listDaysForChart()
    {
        $start_time = I('get.start_time', '', 'trim');
        $end_time = I('get.end_time', '', 'trim');
        $start_day = date('Ymd', strtotime($start_time));
        $end_day = date('Ymd', strtotime($end_time));
        return [$start_day, $end_day];
    }

    /**
     *  请求需要的各个产品的各天的数据
     * @throws \Exception
     * @return mixed
     */
    private function getRequestProductAmountOfDayForChart()
    {
        $list_product_types = I('get.list_product_type');
        return array_reduce($list_product_types, function($carry, $product_type){
            switch ($product_type) {
                case "bang": // 邦企查
                    $carry[$product_type] = $this->getProductAmountOfGroupForChart($product_type);
                    break;
                case "cuishou_v2": // 邦信分详单版V2
                    $carry[$product_type] = $this->getProductAmountOfGroupForChart($product_type);
                    break;
                case "data_validate": // 邦秒验
                    $carry[$product_type] = $this->getProductAmountOfGroupForChart($product_type);
                    break;
                case "cuishou_xiangdan": // 邦信分详单版V1
                    $carry[$product_type] = $this->getProductAmountOfGroupForChart($product_type);
                    break;
                case "miao_pei": // 邦秒配单号版
                    $carry[$product_type] = $this->getMiaoPeiOfDayForChart();
                    break;
                case "cuishou_private":
                    $carry[$product_type] = $this->getCuishouPrivateOfDayForChart();
                    break;
                case "crawler":
                    $carry[$product_type] = $this->getCrawlerOfDayForChart();
                    break;

                case 'pei_single': // 帮秒配单号版
                    $carry[$product_type] = $this->getProductAmountOfGroupForChart($product_type);
                    break;
                case "pei_xiangdan": // 帮秒配详单版
                    $carry[$product_type] = $this->getProductAmountOfGroupForChart($product_type);
                    break;
                default :
                    throw new \Exception('不支持查询的产品类型 msg: ' . $product_type );
            }
            return $carry;
        }, []);
    }

    /**
     * @return array
     * @throws \Exception
     */
    private function getCrawlerOfDayForChart()
    {
        // 生成参数
        $params = $this->genParamsOfCrawlerForChart();

        // 调用第三方的接口 获取各天的数据
        return $this->invokeApiOfCrawlerForChart($params);
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function invokeApiOfCrawlerForChart(array $params)
    {
        // 接口URL
        $url_api = C('CRS_API_CONFIG')['domain'] . '/admin/crawler/detail';

        // 连续请求接口三次,确保不会失败
        $response = [];
        $i = 0;
        while (true) {
            $response = $this->post($url_api, $params);
            if ($response['status'] == 0 || $i > 2) {
                break;
            }
            $i++;
        }

        // 判断是否请求成功
        $this->determineCrawlerInvokedSuccess($response, $url_api, $params);
        $list_stat = array_key_exists('data', $response) ? $response['data'] : [];
        return array_column($list_stat, 'crawl_nums', 'date');
    }

    /**
     * @param array $response
     * @param $url_api
     * @param array $params
     * @throws \Exception
     */
    private function determineCrawlerInvokedSuccess(array $response, $url_api,array $params)
    {
        if ($response['status'] == 0) {
            return;
        }

        $msg = '请求邦秒爬统计详情接口连续三次失败';
        $this->wehcatException($msg . ' msg: ' . json_encode(compact('response', 'url_api', 'params')));
        throw new \Exception($msg);
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsOfCrawlerForChart()
    {
        list($start_date, $end_date) = $this->listDaysForChart();
        $sign = $this->getSign();
        return array_merge(compact('start_date', 'end_date'), $sign);
    }


    /**
     * 邦信分私有云折线图数据
     * @return array
     * @throws \Exception
     */
    private function getCuishouPrivateOfDayForChart()
    {
        // 生成参数
        $params = $this->genParamsOfCuishouPrivateForChart();

        // 调用接口
        return $this->invokeApiOfCuishouPrivateDayForChart($params);
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsOfCuishouPrivateForChart()
    {
        list($date_start, $date_end) = $this->listDaysForChart();
        return compact('date_start', 'date_end');
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function invokeApiOfCuishouPrivateDayForChart($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_private_detail'];

        // 连续请求接口三次,确保不会失败
        $response = [];
        $i = 0;
        while (true) {
            $response = $this->post($url_api, $params);
            if ($response['code'] == 0 || $i > 2) {
                break;
            }
            $i++;
        }

        // 判断是否请求成功
        $this->determineCuishouPrivateInvokedSuccess($response, $url_api, $params);
        $list_stat = array_key_exists('stat', $response['data']) ? $response['data']['stat'] : [];
        return array_column($list_stat, 'sum_valid_counts', 'date');
    }

    /**
     * 是否成功
     * @param array $response
     * @param $url_api
     * @param array $params
     * @throws \Exception
     */
    private function determineCuishouPrivateInvokedSuccess(array $response, $url_api, array $params)
    {
        if ($response['code'] == 0) {
            return;
        }

        $msg = '请求邦信分私有云统计详情接口连续三次失败';
        $this->wehcatException($msg . ' msg: ' . json_encode(compact('response', 'url_api', 'params')));
        throw new \Exception($msg);
    }

    /**
     * 获取秒配的日调用量
     * @return array
     * @throws \Exception
     */
    private function getMiaoPeiOfDayForChart()
    {
        // 获取秒配详单版的数据
        $list_xiangdan_day_amount = $this->getProductAmountOfGroupForChart('pei_xiangdan');

        // 获取秒配单号版的数据
        $list_single_day_amount = $this->getProductAmountOfGroupForChart('pei_single');

        // 聚合
        return $this->aggregateDayOfPeiForChart($list_single_day_amount, $list_xiangdan_day_amount);
    }

    /**
     * 聚合两种秒配
     * @param array $list_single_day_amount
     * @param array $list_xiangdan_day_amount
     * @return array
     */
    private function aggregateDayOfPeiForChart(array $list_single_day_amount,array $list_xiangdan_day_amount)
    {
        $list_days_single = array_keys($list_single_day_amount);
        $list_days_xiangdan = array_keys($list_xiangdan_day_amount);
        $list_days = array_merge($list_days_single, $list_days_xiangdan);

        return array_reduce($list_days, function($carry, $day) use($list_single_day_amount, $list_xiangdan_day_amount){
            $amount_xiangdan = array_key_exists($day, $list_xiangdan_day_amount) ? $list_xiangdan_day_amount[$day] : 0;
            $amount_single = array_key_exists($day, $list_single_day_amount) ? $list_single_day_amount[$day] : 0;
            $carry[$day] = $amount_xiangdan + $amount_single;
            return $carry;
        }, []);
    }

    /**
     * 获取邦秒爬折线图的数据
     * @param string $product_type
     * @return array
     * @throws \Exception
     */
    private function getProductAmountOfGroupForChart($product_type)
    {
        // 生成参数
        $params = $this->genParamsForChart($product_type);

        // 调用统计接口
        return $this->invokedApiOfGroupForChart($params, $product_type);
    }

    /**
     * 为折线图不全缺失的天的数据
     */
    private function fixedDayDataForChart()
    {
        $start_time = I('get.start_time', '', 'trim');
        $end_time = I('get.end_time', '', 'trim');

    }

    /**
     * 调用分组接口
     * @param array $params
     * @param string $product_type
     * @return array
     * @throws \Exception
     */
    private function invokedApiOfGroupForChart(array $params, $product_type)
    {
        $time_begin = microtime(true);

        // apikey分组的接口调用
        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($this->url_amount_api, $params);
            if ($i > 3 || $response['status'] == 0) {
                break;
            }
        }
        $this->checkResponseAmountApi($params, $response);

        $this->time_speed_record_for_chart[$product_type] = microtime(true) - $time_begin;
        return $response['amount'];

    }

    /**
     * 生成参数
     * @param string  $product_type
     * @return array
     * @throws \Exception
     */
    private function genParamsForChart($product_type)
    {
        list($start_time, $end_time) = $this->listDaysForChart();
        $group = 'day';
        $key = $this->getProductKeyById($this->list_product_ids[$product_type]);

        return compact('start_time', 'end_time', 'group', 'key');
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    private function validateParamsForChart()
    {
        $this->limitMethodGet();

        // 时间检查
        $this->validateTimeParamsForChart();

        //  检查产品
        $this->validateProductParamsForChart();
    }

    /**
     * 检查产品
     * @throws \Exception
     */
    private function validateProductParamsForChart()
    {
        $list_product_types = I('get.list_product_type');
        if (!$list_product_types || !is_array($list_product_types)) {
            throw new \Exception('请选择查看的产品');
        }
    }

    /**
     * 检查时间参数
     * @throws \Exception
     */
    private function validateTimeParamsForChart()
    {
        $start_time = I('get.start_time', '', 'trim');
        if (!$start_time) {
            throw new \Exception('请选择合法的开始时间');
        }

        $end_time = I('get.end_time', '', 'trim');
        if (!$end_time) {
            throw new \Exception('请选择合法的结束时间');
        }

        // 两者的差值必须小于365天
        $timestamps_diff = strtotime($end_time) - strtotime($start_time);
        if ($timestamps_diff < 0) {
            throw new \Exception('开始时间必须小于结束时间');
        }

        if ($timestamps_diff > 365 * 3600 * 24) {
            throw new \Exception('选择时间间隔超过了365天, 请重新选择');
        }

        if (strtotime($end_time) > time()) {
            throw new \Exception('结束时间不可以超过当前时间');
        }
    }

    /**
     * 统计概览number形式的展示
     * @return array
     * @throws \Exception
     */
    public function amountTable()
    {
        // 检查请求方式
        $this->limitMethodGet();

        // 催收分析详单V2
        $cuishou_v2 = $this->getCuishouV2AmountForTable();

        // 邦秒配单号版
        list($pei_single, $pei_xiangdan, $maio_pei) = $this->getPeiAmountForTable();

        // 邦企查
        $bang = $this->getBangAmountForTable();

        // 邦秒验
        $data_validate = $this->getTelAmountForTable();

        // 邦信分详单版V1
        $cuishou_xiangdan = $this->getCuishouXiangdanForTable();

        // 老版本来源 (邦信分私有云)
        $cuishou_private = $this->getCuishouPrivateForTable();

        // 老版本来源 (邦秒爬)
//        $crawler = $this->getCrawlerForTable();

        // 催收快捷版（老版本）
        $cuishou_short = $this->getCuishouShortForTable();

        // 格式化数据
        return $this->formatDataForTable(compact( 'cuishou_private', 'data_validate', 'bang',
            'maio_pei', 'cuishou_v2', 'cuishou_short', 'cuishou_xiangdan', 'pei_xiangdan', 'pei_single'));
    }

    /**
     * 邦信分详单版V1(老版本)
     * @throws \Exception
     */
    private function getCuishouXiangdanForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['cuishou_xiangdan']);
        return $this->getInvokedAmountForTable($key);
    }

    /**
     * 催收快捷版（老版本）
     * @throws \Exception
     */
    private function getCuishouShortForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['cuishou_short']);

        // 容器
        $list_container = [];

        $time_begin = microtime(true);
        array_walk($this->list_times_table, function ($where_time, $type_time) use ($key, &$list_container) {
            $where_time['key'] = $key;
            $where_time['product_id'] = $this->list_product_ids['cuishou_short'];

            $list_container[$type_time] = $this->invokedAmountFromBackApi($where_time);
        });

        $list_container['time_speed'] = microtime(true) - $time_begin;
        return $list_container;
    }

    /**
     * 获取催收快捷版的调用量
     * @param array $where_time
     * @throws \Exception
     * @return int
     */
    private function getOneCuishouShortForTable(array $where_time)
    {
        list($date_end, $date_start) = [$where_time['end_time'], $where_time['start_time']];
        $url_api = C('LIST_API_URL')['cuishou_short_stat_list'];

        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($url_api, compact('date_start', 'date_end'));
            if ($response['status'] == 0 || $i >= 3) {
                break;
            }
        }

        // 检查是否成功调用
        $this->validateSuccessInvokedCuishouShortForTable($response);

        // 计算总
        return $this->computedTotalOfCuishouShortForTable($response);
    }

    /**
     * @param array $response
     * @return int
     */
    public function computedTotalOfCuishouShortForTable(array $response)
    {
        return array_reduce($response['data']['stat'], function ($carry, $item) {
            $carry += $item['sum_success'];
            return $carry;
        }, 0);
    }

    /**
     * 检查是否调用成功
     * @param array $response
     * @throws \Exception
     */
    private function validateSuccessInvokedCuishouShortForTable(array $response)
    {
        if ($response['code'] != 0) {
            $msg = '数据概览连续三次调用催收分析快捷版本失败';
            $this->wehcatException($msg . ' msg：' . json_encode(compact('response'), JSON_UNESCAPED_UNICODE));
            throw new \Exception($msg);
        }
    }

    /**
     * 格式化数据
     * @param array $source
     * @return array
     */
    private function formatDataForTable(array $source)
    {
        $list_container = [];
        $list_types = array_keys($this->list_times_table);
        array_unshift($list_types, 'time_speed');

        array_walk($list_types, function ($date_type) use (&$list_container, $source) {
            $list_container[$date_type]['date_zh'] = array_key_exists($date_type, $this->list_mapping_date_to_chinese)
                ? $this->list_mapping_date_to_chinese[$date_type] : '';
            array_walk($source, function ($product, $product_type) use (&$list_container, $date_type) {
                $list_container[$date_type][$product_type] = $product[$date_type];
            });
        });

        return $list_container;
    }

    /**
     * 老版本来源 (邦秒爬)
     * @return array
     */
    private function getCrawlerForTable()
    {
        $time_begin = microtime(true);
        // 容器
        $list_container = [];
        array_walk($this->list_times_table, function ($where_time, $type_time) use (&$list_container) {
            $list_container[$type_time] = $this->getOneCrawlerForTable($where_time);
        });

        $list_container['time_speed'] = microtime(true) - $time_begin;
        return $list_container;
    }

    /**
     * @param array $where_time
     * @return int
     * @throws \Exception
     */
    private function getOneCrawlerForTable(array $where_time)
    {
        list($url_api, $params) = $this->tidyParamsForCrawler($where_time);

        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($url_api, $params);
            if ($response['status'] == 0 || $i >= 3) {
                break;
            }
        }

        // 检查是否成功调用
        $this->validateSuccessInvokedCrawlerForTable($response, $url_api, $params);

        // 累加获取总计信息
        return $this->computedTotalOfCrawlerForTable($response);
    }

    /**
     * @param array $response
     * @return int
     */
    public function computedTotalOfCrawlerForTable(array $response)
    {
        return array_reduce($response['data'], function ($carry, $item) {
            $carry += $item['crawl_nums'];
            return $carry;
        }, 0);
    }

    /**
     * 检查是否成功调用
     * @param array $response
     * @param $url_api
     * @param array $params
     * @throws \Exception
     */
    private function validateSuccessInvokedCrawlerForTable(array $response, $url_api = null, array $params = null)
    {
        if ($response['status'] != 0) {
            $msg = '数据概览：邦秒爬调用量API连续三次调用失败';
            $this->wehcatException($msg . ' msg: ' . json_encode(compact('response', 'url_api'), JSON_UNESCAPED_UNICODE));
            throw new \Exception($msg . ' msg: ' . json_encode(compact('response', 'url_api', 'params'), JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 为邦秒爬整理参数
     * @param array $where_time
     * @return array
     */
    private function tidyParamsForCrawler(array $where_time)
    {
        $url_api = C('CRS_API_CONFIG')['domain'] . $this->listUrl;
        list($start_date, $end_date) = [$where_time['start_time'], $where_time['end_time']];
        $params_sign = $this->getSign();

        $params = array_merge($params_sign, compact('start_date', 'end_date'));
        return [$url_api, $params];
    }

    /**
     * 获取签名
     * @return array
     */
    public function getSign()
    {
        $param['t'] = time();
        $param['n'] = rand(1000, 9999);

        // 签名
        $api_key = $this->apiKey;
        $api_secret = $this->apiSecret;
        $dict_source = compact('api_key', 'api_secret');
        $data = array_merge($dict_source, $param);
        sort($data, SORT_STRING);
        $param['s'] = md5(md5(implode($data)));
        return $param;
    }

    /**
     * 获取催收分私有云的调用信息
     * @throws \Exception
     * @return array
     */
    private function getCuishouPrivateForTable()
    {
        $time_begin = microtime(true);
        // 容器
        $list_container = [];
        array_walk($this->list_times_table, function ($where_time, $type_time) use (&$list_container) {
            $list_container[$type_time] = $this->getOneCuishouPrivateForTable($where_time);
        });

        $list_container['time_speed'] = microtime(true) - $time_begin;
        return $list_container;
    }

    /**
     * 获取某段时间邦信分私有云的调用信息
     * @param array $where_time
     * @throws \Exception
     * @return int
     */
    private function getOneCuishouPrivateForTable(array $where_time)
    {
        $url_api = C('LIST_API_URL')['cuishou_private_list'];
        list($date_start, $date_end) = [$where_time['start_time'], $where_time['end_time']];

        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($url_api, compact('date_end', 'date_start'));
            if ($response['code'] == 0 || $i >= 3) {
                break;
            }
        }

        // 检查是否成成调用
        $this->validateSuccessInvokedCuishouPrivateForTable($response, compact('date_start', 'date_end'), $url_api);

        // 累加获取总计信息
        return $this->computedTotalOfCuishouPrivateForTable($response);
    }

    /**
     * @param array $response_cuishou_private
     * @return integer
     */
    private function computedTotalOfCuishouPrivateForTable(array $response_cuishou_private)
    {
        return array_reduce($response_cuishou_private['data']['stat'], function ($carry, $item) {
            $carry += $item['sum_valid_counts'];
            return $carry;
        }, 0);
    }

    /**
     * @param array $response
     * @param null $params
     * @param null $url_api
     * @throws \Exception
     */
    private function validateSuccessInvokedCuishouPrivateForTable(array $response, $params = null, $url_api = null)
    {
        if ($response['code'] != 0) {
            $msg = '数据概览: 调用邦信分私有云统计口连续失败三次';
            $this->wehcatException($msg . ' msg: ' . json_encode(compact('response', 'url_api', 'params'), JSON_UNESCAPED_UNICODE));
            throw new \Exception($msg);
        }
    }

    /**
     * 号码状态
     * @return array
     * @throws \Exception
     */
    private function getTelAmountForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['data_validate']);
        return $this->getInvokedAmountForTable($key);
    }


    /**
     * 邦企查
     * @throws \Exception
     * @return array
     */
    private function getBangAmountForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['bang']);
        return $this->getInvokedAmountForTable($key);
    }

    /**
     * 邦秒配单号版table类型的调用
     * @return array
     * @throws \Exception
     */
    private function getPeiAmountForTable()
    {
        // 秒配单号版
        $pei_single = $this->getPeiSingleAmountForTable();

        // 秒配详单版
        $pei_xiangdan = $this->getPeiXiangdanAmountForTable();

        // 整合
        $maio_pei =  $this->mergePeiDataForTable($pei_single, $pei_xiangdan);
        return [$pei_single, $pei_xiangdan, $maio_pei];
    }

    /**
     * 为秒配整合数据
     * @param array $pei_single
     * @param array $pei_xiangdan
     * @return array
     */
    private function mergePeiDataForTable(array $pei_single, array $pei_xiangdan)
    {
        $list_container = [];

        array_walk($pei_xiangdan, function ($item, $key) use (&$list_container, $pei_single) {
            $list_container[$key] = $item + $pei_single[$key];
        });
        return $list_container;
    }

    /**
     * 邦秒配详单版
     * @return array
     * @throws \Exception
     */
    private function getPeiXiangdanAmountForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['pei_xiangdan']);
        return $this->getInvokedAmountForTable($key);
    }

    /**
     * 秒配单号版
     * @return array
     * @throws \Exception
     */
    private function getPeiSingleAmountForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['pei_single']);
        return $this->getInvokedAmountForTable($key);
    }

    /**
     * 催收分析v2的调用量
     * @throws \Exception
     */
    private function getCuishouV2AmountForTable()
    {
        $key = $this->getProductKeyById($this->list_product_ids['cuishou_v2']);
        return $this->getInvokedAmountForTable($key);
    }

    /**
     * 获取日调用量
     * @param $key
     * @return array
     */
    private function getInvokedAmountForTable($key)
    {
        // 容器
        $list_container = [];

        $time_begin = microtime(true);
        array_walk($this->list_times_table, function ($where_time, $type_time) use ($key, &$list_container) {
            $where_time['key'] = $key;
            $list_container[$type_time] = $this->invokedAmountFromBackApi($where_time);
        });

        $list_container['time_speed'] = microtime(true) - $time_begin;
        return $list_container;
    }

    /**
     * 请求调用量
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function invokedAmountFromBackApi(array $params)
    {
        $i = 0;
        while (true) {
            $i++;
            $response = $this->post($this->url_amount_api, $params);
            if ($i > 3 || $response['status'] == 0) {
                break;
            }
        }
        $this->checkResponseAmountApi($params, $response);
        return $response['amount'];
    }

    /**
     * 检查调用量接口返回是否正常
     * @param array $params
     * @param array $response
     * @throws \Exception
     */
    private function checkResponseAmountApi(array $params, array $response)
    {
        if ($response['status'] == 0) {
            return;
        }

        $msg = '统计概览调用量查询接口连续三次调用失败 content:' . json_encode(compact('response', 'params'), JSON_UNESCAPED_UNICODE);
        $this->wehcatException($msg);
    }

    /**
     * 获取product_key
     * @param $product_id
     * @throws \Exception
     * @return string
     */
    private function getProductKeyById($product_id)
    {
        $product = $this->getOneProductByCondition(compact('product_id'));
        if (!$product) {
            throw new \Exception('product_id: ' . $product_id . '未找到对应产品');
        }
        return $product['product_key'];
    }

    /**
     * 获取一个产品
     * @param array $where
     * @return array
     */
    private function getOneProductByCondition(array $where)
    {
        return (new ProductModel())->where($where)
            ->find();
    }
}
