<?php

namespace Api\Repositories;

class BaseRepository
{

    /**
     * 限定GET
     * @throws \Exception
     */
    protected function limitMethodGET()
    {
        if (!IS_GET) {
            throw new \Exception('本方法只可以GET方式访问');
        }
    }

    /**
     * 限定POST
     * @throws \Exception
     */
    protected function limitMethodPOST()
    {
        if (!IS_POST) {
            throw new \Exception('本方法只可以POST方式访问');
        }
    }

    /**
     * 格式化数字
     * @param  integer $number
     * @return string
     */
    public function formatNum($number)
    {
        if (!is_numeric($number)) {
            return $number;
        }
        return number_format($number, 0, '.', ',');
    }

    /**
     * 百分比格式化
     * @param $item
     * @return string
     */
    public function formatPercentage($item)
    {
        if ($item === 'NA') {
            return $item;
        }

        return round($item * 100, 2) . '%';
    }

    /**
     * 日志
     * @param $response
     * @return string
     */
    protected function log($response)
    {
        // 組裝要寫日志
        $info_log = is_string($response) ? $response : json_encode($response, JSON_UNESCAPED_UNICODE);

        // 檢查日志文件是否存在
        $dir = RUNTIME_PATH . date('Ymd') . '/';
        if (!file_exists($dir) || !is_dir($dir)) {
            @mkdir($dir, 0755, true);
        }

        // 日志文件名
        $log_name = $this->genLogName();
        $destination = $dir . $log_name . '.log';

        // 寫入
        file_put_contents(
            $destination,
            '[' . date('Y-m-d H:i:s') . ']  ' . $info_log . PHP_EOL,
            FILE_APPEND
        );
    }

    /**
     * 生成日志名字
     * @return mixed
     */
    protected function genLogName()
    {
        // 回溯调用者的文件的名字
        $file_name_invoking = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 2)[1]['file'];
        $file_name_invoking = trim($file_name_invoking, '.class.php');
        $list_name = explode('/', $file_name_invoking);

        return end($list_name);
    }

    /**
     * 获取payload && formData数据
     * @return array
     */
    public function genParamsForPost()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        return array_merge(I('post.'), (array)$request_body);
    }


    /**
     * 获取从一个数组中获取一个特定索引的数值， 支持默认值
     * @param $list_source
     * @param $key_search
     * @param string $default_value
     * @throws \Exception
     * @return mixed
     */
    public function getValueWithDefault($list_source, $key_search, $default_value = '')
    {
        if (!is_array($list_source) || !is_string($key_search)) {
            throw new \Exception(__METHOD__ . ' 函数需要第一个参数必须是array, 第二参数必选是string 第三个参数可选参数');
        }

        return isset($list_source[$key_search]) ? $list_source[$key_search] : $default_value;
    }

    /**
     * 发送post请求
     * @param $url
     * @param  array $post_data
     * @return array|mixed
     */
    public function curlRequestForPost($url, array $post_data)
    {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $response = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'msg' => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($response)) {
            return json_decode($response, true);
        }

        return $response;
    }

    /**
     * 发送get请求
     * @param string $url 请求的Url
     * @param array $url_data 请求参数
     * @return array|mixed
     */
    public function curlRequestForGet($url, $url_data = [])
    {
        // 追加参数
        if ($url_data && is_array($url_data)) {
            $url_data = http_build_query($url_data);
            $url .= '?' . $url_data;
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        $response = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'msg' => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($response)) {
            return json_decode($response, true);
        }

        return $response;
    }

    /**
     * curl请求
     * @param  string $method 请求方式
     * @param  string $url 请求地址
     * @param  array $vars 传入参数
     * @param  string $build 参数格式
     * @return array  返回结果
     */
    public function getCurl($method, $url, $vars = array(), $build = 'form')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        if ($vars && 'form' == strtolower($build)) {
            $vars = http_build_query($vars, '', '&');
        }
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Expect:']);
        if ($vars && 'form' != strtolower($build)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type' => 'application/json', 'Content-Length' => strlen($vars)]);
        }
        if ($vars) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);
        }
        curl_setopt($curl, CURLOPT_TIMEOUT, 180);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 180);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        switch (strtoupper($method)) {
            case 'HEAD':
                curl_setopt($curl, CURLOPT_NOBODY, true);
                break;
            case 'GET':
                curl_setopt($curl, CURLOPT_HTTPGET, true);
                break;
            case 'POST':
                curl_setopt($curl, CURLOPT_POST, true);
                break;
            default:
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
                break;
        }

        $response = curl_exec($curl);
        //释放curl
        curl_close($curl);
        return $response;
    }
}