<?php
/**
 * @Author: lidandan
 * @Date:   2018-06-21 20:18:16
 */
namespace Api\Repositories;

class StatCrawlerRepository extends BaseRepository
{
    protected $biz;
    //APIKEY
    protected $apiKey = '0aa2cb33d0eaedc4abe412348045dc8';
    //APISECRET
    protected $apiSecret = '0b178ed2d1d049e46472711d8f92bf4';
    //列表统计
    protected $listUrl = '/admin/crawler/list';
    //详情统计
    protected $detailUrl = '/admin/crawler/detail';

    public function __construct($biz)
    {
        $this->biz = $biz;
    }

    /**
     * 获取列表统计数据
     * @return array list
     */
    public function getReportList()
    {
        //查询条件
        $where = $this->getReportListParam();
        //列表统计数据
        $list = $this->getReportListApi($where);
        //邦秒爬账户信息
        $crawler_list = $this->getCrawlerAccountList();

        $total_data = [];
        if (!empty($crawler_list) && is_array($crawler_list)) {
            foreach ($crawler_list as $key => &$value) {
                $value['authen_nums'] = isset($list[$value['id']]['authen_nums']) ? $list[$value['id']]['authen_nums'] : 0;
                $value['crawl_nums'] = isset($list[$value['id']]['crawl_nums']) ? $list[$value['id']]['crawl_nums'] : 0;
                $value['report_nums'] = isset($list[$value['id']]['report_nums']) ? $list[$value['id']]['report_nums'] : 0;
                $value['crawl_pct'] = isset($list[$value['id']]['crawl_pct']) ? sprintf("%.2f", $list[$value['id']]['crawl_pct']*100).'%' : '0.00%';
                $value['report_pct'] = isset($list[$value['id']]['report_pct']) ? sprintf("%.2f", $list[$value['id']]['report_pct']*100).'%' : '0.00%';

                $total_data['authen_nums'] += $value['authen_nums'];
                $total_data['crawl_nums'] += $value['crawl_nums'];
                $total_data['report_nums'] += $value['report_nums'];
                $total_data['crawl_pct'] = empty($total_data['authen_nums']) ? 'NA' : sprintf("%.2f", $total_data['crawl_nums']/$total_data['authen_nums']*100).'%';
                $total_data['report_pct'] = empty($total_data['crawl_nums']) ? 'NA' : sprintf("%.2f", $total_data['report_nums']/$total_data['crawl_nums']*100).'%';
            }
        }

        return ['crawler_list' => $crawler_list, 'total_data' => $total_data];
    }

    /**
     * 获取详情统计数据
     * @return array
     */
    public function getReportDetail()
    {
        $crawler = $this->checkDetailParam();
        //查询条件
        $where = $this->getReportDetailParam();
        //详情统计
        $list = $this->getReportDetailApi($where);
        //日期列表
        $date_list = $this->getReportDetailDateList();
        $total_data = $data = [];
        if (!empty($date_list) && is_array($date_list)) {
            foreach ($date_list as $key => $value) {
                $date = date('Ymd', strtotime($key));
                $data[$key]['authen_nums'] = isset($list[$date]['authen_nums']) ? $list[$date]['authen_nums'] : 0;
                $data[$key]['crawl_nums'] = isset($list[$date]['crawl_nums']) ? $list[$date]['crawl_nums'] : 0;
                $data[$key]['report_nums'] = isset($list[$date]['report_nums']) ? $list[$date]['report_nums'] : 0;
                $data[$key]['crawl_pct'] = isset($list[$date]['crawl_pct']) ? sprintf("%.2f", $list[$date]['crawl_pct']*100).'%' : '0.00%';
                $data[$key]['report_pct'] = isset($list[$date]['report_pct']) ? sprintf("%.2f", $list[$date]['report_pct']*100).'%' : '0.00%';

                $total_data['authen_nums'] += $data[$key]['authen_nums'];
                $total_data['crawl_nums'] += $data[$key]['crawl_nums'];
                $total_data['report_nums'] += $data[$key]['report_nums'];
                $total_data['crawl_pct'] = empty($total_data['authen_nums']) ? 'NA' : sprintf("%.2f", $total_data['crawl_nums']/$total_data['authen_nums']*100).'%';
                $total_data['report_pct'] = empty($total_data['crawl_nums']) ? 'NA' : sprintf("%.2f", $total_data['report_nums']/$total_data['crawl_nums']*100).'%';
            }
        }
        return ['detail_data' => $data, 'total_data' => $total_data, 'crawler_info' => $crawler];
    }

    /**
     * 检查参数
     * @return [type] [description]
     */
    public function checkDetailParam()
    {
        $id = I('post.id', '', 'trim');
        $list_ids = isset($this->biz['list_crawler']) ? $this->biz['list_crawler'] : [];
        if (!$id || !in_array($id, $list_ids)) {
            throw new \Exception('参数错误，邦秒爬产品的ID');
        }
        $info = $this->getCrawlerById($id);
        return $info;
    }

    /**
     * 获取查询的时间列表
     * @return array
     */
    public function getReportDetailDateList()
    {
        $start_date = I('post.start_date', '', 'trim');
        $end_date = I('post.end_date', '', 'trim');
        if (!$start_date && !$end_date) {
            $end_date = time();
            $start_date = $end_date-86400*30;
        } else {
            $start_date = strtotime($start_date);
            $end_date = strtotime($end_date) + 86399;
        }
        return $this->dateList($start_date, $end_date);
    }

    /**
     * 详情时间列表
     * @param number $begin
     * @param number $end
     * @return array
     */
    public function dateList($begin, $end)
    {
        $date_list = [];
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }

    /**
     * 列表统计查询条件
     * @return array
     */
    public function getReportListParam()
    {
        $where = $this->timeLimitParam();
        $where['cids'] = isset($this->biz['list_crawler']) ? json_encode($this->biz['list_crawler']) : [];
        return $where;
    }

    /**
     * 详情统计查询条件
     * @return array
     */
    public function getReportDetailParam()
    {
        $where = $this->timeLimitParam();
        $where['cids'] = json_encode([I('post.id', '', 'trim')]);
        return $where;
    }

    /**
     * 时间限制条件
     * @return array
     */
    public function timeLimitParam()
    {
        $start_date = I('post.start_date', '', 'trim');
        $end_date = I('post.end_date', '', 'trim');
        if (!$start_date && !$end_date) {
            $end_date = time();
            $start_date = $end_date-86400*30;
            $end_date = date('Ymd', $end_date);
            $start_date = date('Ymd', $start_date);
            return compact('start_date', 'end_date');
        }

        $start_date = date('Ymd', strtotime($start_date));
        $end_date = date('Ymd', strtotime($end_date) + 86399);
        return compact('start_date', 'end_date');
    }

    /**
     * 获取账号信息
     * @param  number $id 账号ID
     * @return list
     */
    public function getCrawlerById($id)
    {
        $user_info = D('Auth')->field('id,status,source,developer')
                              ->where(['id' => $id])
                              ->index('id')
                              ->find();
        return $user_info;
    }

    /**
     * 获取该客户下的所有邦秒爬账户
     * @return array
     */
    public function getCrawlerAccountList()
    {
        $where = $this->getCrawlerListParam();
        $user_list = D('Auth')
            ->field('id,status,source,appid,appsecret,developer,created_at,expiration_date')
            ->where($where)
            ->index('id')
            ->select();
        return $user_list;
    }

    /**
     * 获取查询的条件
     * @return array
     */
    public function getCrawlerListParam()
    {
        $list_crawler = isset($this->biz['list_crawler']) ? $this->biz['list_crawler'] : [];
        $param['id'] = ['in', $list_crawler];
        return $param;
    }

    /**
     * 获取列表统计数据
     * @param  array $param 查询数据
     * @return array
     */
    public function getReportListApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->listUrl;
        $param = array_merge($param, $this->getSign());
        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 获取详情统计数据
     * @param  array $param 参数
     * @return array
     */
    public function getReportDetailApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->detailUrl;
        $param = array_merge($param, $this->getSign());
        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 获取签名
     * @return array
     */
    public function getSign()
    {
        $api_key = $this->apiKey;
        $api_secret = $this->apiSecret;
        $dict_source = compact('api_key', 'api_secret');
        $param['t'] = time();
        $param['n'] = rand(1000, 9999);
        $data = array_merge($dict_source, $param);
        sort($data, SORT_STRING);
        $param['s'] = md5(md5(implode($data)));
        return $param;
    }
}