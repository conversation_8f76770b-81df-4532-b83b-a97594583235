<?php

namespace Api\Repositories;

use Account\Model\ProductModel;
use Common\Common\CurlTrait;
use Common\Model\ProductWechatWarningModel;
use Common\Model\WechatWarningModel;

class BackendProductWechatWarningRepository
{
    use CurlTrait;

    /**
     * 更新产品的微信配置
     * @throws \Exception
     */
    public function update()
    {
        // 检查参数
        $this->validateParamsForUpdate();

        // 生成 条件&& 参数
        list($where, $params) = $this->getListConditionAndParamsForUpdate();

        // 更新
        $this->updateDo($where, $params);
    }

    /**
     * 更新
     * @param array $where
     * @param array $params
     */
    private function updateDo(array $where, array $params)
    {
        (new ProductWechatWarningModel())->where($where)
            ->save($params);
    }

    /**
     * 生成 条件&& 参数
     * @return array
     */
    private function getListConditionAndParamsForUpdate()
    {
        $product_id = $this->postKey('product_id','', 'trim');
        $type = $this->postKey('type', '', 'trim');
        $list_slug = $this->postKey('list_slug');
        $slug = implode(',', array_unique($list_slug));
        $updated_at = time();

        return [compact('product_id', 'type'), compact('slug', 'updated_at')];
    }

    /**
     * 检查条件
     * @throws \Exception
     */
    private function validateParamsForUpdate()
    {
        // 请求方式
        $this->limitMethodPost();

        // 如果缺少product_id
        $product_id = $this->postKey('product_id','', 'trim');
        $type = $this->postKey('type', '', 'trim');
        $list_slug = $this->postKey('list_slug', []);

        if (!$product_id || !is_numeric($product_id)) {
            throw new \Exception('product_id不合法');
        }

        if (!$type) {
            throw new \Exception('type不合法');
        }

        if (!$list_slug || !is_array($list_slug)) {
            throw new \Exception('list_slug不合法');
        }
    }

    /**
     * @throws \Exception
     * @return array
     */
    public function wechatChoose()
    {
        // 检查条件
        $this->validateParamsForChooseWechat();

        // 条件
        $where = $this->genParamsForChooseWechat();

        // 获取微信配置
        return $this->getListWechatByCondition($where);
    }

    /**
     * 获取条件
     * @return array
     */
    private function genParamsForChooseWechat()
    {
        $product_id = $this->postKey('product_id', '', 'trim');
        $type = $this->postKey('type', '', 'trim');
        $product_wechat = $this->getOneProductWechatWarningByCondition(compact('product_id', 'type'));
        $slug = ['in', explode(',', $product_wechat['slug'])];
        return compact('slug');
    }

    /**
     *
     * @param array $where
     * @return mixed
     */
    private function getOneProductWechatWarningByCondition(array $where)
    {
        return (new ProductWechatWarningModel())->where($where)
            ->find();
    }

    /**
     * 检查条件
     * @throws \Exception
     */
    private function validateParamsForChooseWechat()
    {
        // 访问方式
        $this->limitMethodPost();

        $product_id = $this->postKey('product_id', '', 'trim');
        $type = $this->postKey('type', '', 'trim');

        $product_wechat = $this->getOneProductWechatWarningByCondition(compact('product_id', 'type'));

        if (!$product_id || !$type || !$product_wechat) {
            throw new \Exception('product_id或者type不合法');
        }
    }

    /**
     * 微信配置列表
     * @throws \Exception
     */
    public function listWechat()
    {
        $this->limitMethodGet();

        return $this->getListWechatByCondition([]);
    }

    /**
     * 微信配置获取
     * @param array $where
     * @return array
     */
    private function getListWechatByCondition(array $where)
    {
        return (new WechatWarningModel())->where($where)
            ->select();
    }

    /**
     * 获取没有开通配置的产品列表
     * @return array
     * @throws \Exception
     */
    public function listProductWithoutOpen()
    {
        // 检查条件
        $this->validateParamsForOpened();

        // 条件
        $where = $this->genParamsWithoutOpen();

        // 获取产品
        return $this->getProductListByCondition($where);
    }

    /**
     * @throws \Exception
     */
    private function validateParamsForOpened()
    {
        // 限定Post访问
        $this->limitMethodPost();

        // type
        $type = $this->postKey('type', '', 'trim');
        if (!$type) {
            throw new \Exception('缺少必选参数type');
        }
    }

    /**
     * 没有开通微信配置的产品生成条件
     * @return array
     */
    private function genParamsWithoutOpen()
    {
        // 获取已经开通过相应场景的产品
        $list_open_products = $this->getOpenedWechatProduct();

        $list_opened_product_ids = array_column($list_open_products, 'product_id');

        $product_id = ['not in', $list_opened_product_ids ?: [-1]];
        return compact('product_id');
    }

    /**
     * 获取已经开通过相应场景的产品
     * @return array
     */
    private function getOpenedWechatProduct()
    {
        $type = $this->postKey('type', '', 'trim');
        return (new ProductWechatWarningModel())->where(compact('type'))
            ->distinct(true)
            ->field('product_id')
            ->select();
    }

    /**
     * 新建配置
     * @throws \Exception
     */
    public function store()
    {
        // 校验条件
        $this->validateParamsForStore();

        // 生成参数
        $params = $this->genParamsForStore();

        // 存储
        $this->storeDo($params);
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsForStore()
    {
        $list_slug = $this->postKey('list_slug', []);
        $product_id = $this->postKey('product_id', '', 'trim');
        $type = $this->postKey('type', '', 'trim');
        $slug = implode(',', array_unique($list_slug));
        $operater = session(C('LOGIN_SESSION_NAME'));
        $created_at = $updated_at = time();

        return compact('slug', 'product_id', 'type', 'operater', 'created_at', 'updated_at');
    }

    /**
     * 存储
     * @param array $params
     */
    private function storeDo(array $params)
    {
        (new ProductWechatWarningModel())->add($params);
    }

    /**
     * 校验条件
     * @throws \Exception
     */
    private function validateParamsForStore()
    {
        $list_slug = $this->postKey('list_slug', []);
        if (!$list_slug || !is_array($list_slug)) {
            throw new \Exception('请选择关联的微信配置');
        }

        $product_id = $this->postKey('product_id', '', 'trim');
        if (!$product_id) {
            throw new \Exception('请选择产品');
        }

        $type = $this->postKey('type', '', 'trim');
        if (!$type) {
            throw new \Exception('请选择场景');
        }
    }

    /**
     * 已经配置过微信预警的产品列表
     */
    public function listProduct()
    {
        // 条件
        $where = $this->genParamsForListProduct();

        // 产品列表
        return $this->getProductListByCondition($where);
    }

    /**
     * 追加附加信息
     * @param array $list_products
     * @return array
     */
    private function appendOtherInfoToProduct(array $list_products)
    {
        // 微信配置
        $list_wechat = $this->getListWechatByCondition([]);

        // 产品名称
        $list_product_base = $this->getProductListByCondition([]);
        $list_product_base = array_column($list_product_base, null, 'product_id');

        return array_map(function ($item) use ($list_product_base, $list_wechat) {
            $product_id = $item['product_id'];
            $item['product_name'] = $list_product_base[$product_id]['product_name'];
            $item['list_slug'] = $this->getSlugListForProduct($list_wechat, $item['slug']);
            return $item;
        }, $list_products);
    }

    /**
     * 获取slug列表
     * @param array $list_wechat
     * @param $list_slug
     * @return string
     */
    private function getSlugListForProduct(array $list_wechat, $list_slug)
    {
        $list_slug = explode(',', $list_slug);
        $list_wechat = array_column($list_wechat, null, 'slug');

        // 容器
        $list_container = [];

        array_walk($list_slug, function ($slug) use (&$list_container, $list_wechat) {
            $name = isset($list_wechat[$slug]['name']) ? $list_wechat[$slug]['name'] : '异常的slug :' . $slug;
            array_push($list_container, $name);
        });

        return implode(' , ', $list_container);
    }

    /**
     * 获取产品列表
     * @param array $where
     * @return array
     */
    private function getProductListByCondition(array $where)
    {
        return (new ProductModel())->where($where)
            ->select();
    }

    /**
     * @return array
     */
    private function genParamsForListProduct()
    {
        $list_product = (new ProductWechatWarningModel())->distinct(true)
            ->field('product_id')
            ->select();

        $product_id = ['in', array_column($list_product, 'product_id')];
        return compact('product_id');
    }

    /**
     * 列表
     */
    public function listShow()
    {
        // 条件
        $where = $this->genParamsForList();

        // 聚合列表
        $list_products = $this->getListProductWechatWarningByCondition($where);

        // 追加其他的信息
        return $this->appendOtherInfoToProduct($list_products);

    }

    /**
     * @param array $where
     * @return mixed
     */
    private function getListProductWechatWarningByCondition(array $where)
    {
        return (new ProductWechatWarningModel())->where($where)
            ->select();
    }


    /**
     * 生成条件
     * @return array
     */
    private function genParamsForList()
    {
        $where = [];
        $product_id = $this->postKey('product_id', '', 'trim');
        $type = $this->postKey('type', '', 'trim');

        $product_id && $where = compact('product_id');
        $type && $where['type'] = $type;

        return $where;
    }
}
