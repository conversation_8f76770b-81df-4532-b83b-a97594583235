<?php

namespace Api\Repositories;

use Common\Common\FlushOldCrawlerToken;
use Common\Common\ResponseTrait;
use Common\Model\AuthModel;

class BackendCrawlerRepository extends BaseRepository
{
    use ResponseTrait, FlushOldCrawlerToken;
    // 默认协议
    protected $protocol_default_content = '&lt;h1 style=&quot;margin-top: 0px; font-weight: 600; text-align: center; font-size: 24px; color: rgb(0, 0, 0); line-height: 60px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;运营商授权协议&lt;/h1&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;此运营商授权协议（以下简称本协议）由您与北京羽乐创新科技有限公司（以下简称本公司/我们）签订。如您点击选择同意此协议将视为授权我们查看您的手机运营商信息，包括但不限于用户身份信息、是否实名认证、通话记录、手机账单等信息在内的各类信息，并将视为已阅读并立即本协议的全部内容。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;如果您不同意本协议的任一内容，或者无法准确理解本公司对条款的说明，请不要进行后续操作。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;一、	授权条款&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一） 您知道授权成功后不可撤销，并确认在您授权我们验证并获取您的手机运营商信息前，您已充分阅读、理解并接受本协议的全部内容，一旦您使用本服务，即表示您同意遵循本协议的所有约定。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二）您同意，在您授权我们验证并获取您的手机运营商信息后，我们有权查看并读取包括但不限于您的用户身份信息、是否实名认证、通话记录、手机账单等信息，同时对该等信息加以留存、分析、整理及加工。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（三）您同意授权本公司将上述信息提供给向您提供借款资信评审的机构，用于该机构判断您的资信水平和相关风险，从而决定相关产品与您的交易条件或者其他相关的决策。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（四）在为您提供服务的过程中，因某种原因导致本公司无法通过您留存的基本信息完成本次服务，您同意并授权本公司运用曾经（如有）为您提供服务时所留存的信息来完成本次服务。如果本公司留存的关于您的信息不足以支持本公司完成本次服务，本公司有权拒绝向您提供服务，并不承担由此给您造成的损失。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（五）上述条款中涉及的“留存、分析、整理及加工”包括但不限于对用户信息的内容和/或形式进行重新排序、结构化、格式化、标准化等方法。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（六）为更好地为您提供服务，您同意并授权本公司可与其合作的第三方进行联合研究，并可将通过本协议获得的您的信息投入到该等联合研究中。但本公司与其合作的第三方在开展上述联合研究前，应要求其合作的第三方对在联合研究中所获取的您的信息予以保密。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（七）您在本协议项下对本公司的授权将视为对本公司及本公司之关联公司的授权。本公司及本公司关联公司均可凭借您的授权及本协议约定执行相关操作。另外，在签署保密协议的情况下，您在本协议中的相关授权可延伸到本公司指定的第三方公司。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;二、	保密条款&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;本公司重视对用户隐私的保护。因收集您的信息是处于遵守国家法律法规的规定以及向您提供服务及提升服务质量的目的，我们对您的运营商信息承担保密义务，不会为满足第三方的一些目的而向其出售或出租您的任何信息，我们会在下列情况下才将您的手机运营商信息与第三方共享。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一） 获得您的同意或授权。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二） 为了向您提供或推荐服务、产品、或为了向您提供更完善的服务，或为了您拥有更广泛的社交体验，我们会与包括本公司旗下公司及合作商户在内的第三方共享您的相关信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（三） 某些情况下，只有共享您的信息，才能提供您需要的服务、产品，或处理您与他人的交易纠纷或争议。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（四） 某些服务、产品由我们的合作伙伴提供或由我们与合作伙伴、供应商共同提供，我们会与其共享所需的信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（五） 我们与第三方进行联合推广时，我们会与第三方共享为完成活动所需要的必要信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（六） 为维护本公司及关联公司、旗下公司和其他本公司用户的合法权益。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（七） 根据法律规定及合理商业习惯，在我们计划与其他公司合并或被其收购或进行其他资本市场活动（包括但不限于IPO，债券发行）时，以及其他情形下我们需要接受来自其他主体的尽职调查时，我们会把您的信息提供给必要的主体，但我们会通过和这些主体签署保密协议等方式要去其对您的个人信息采取合理的保密措施。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（八） 如您授权第三方向本公司查询、采集您的相关信息，我们有权在法律法规和您的授权许可范围内向第三方分析您的部分信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（九） 为了维护和改善我们的服务。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（十） 根据法律法规的规定或有权机关的要求。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（十一） 您理解并同意，我们可以保存您已经授权的原始信息；在您和我们的合作存续期间，我们随时可以重新采集和更新数据，对于经过加工和脱敏处理的数据，我们可以永久保存在服务器上；对于原始数据，在合作结束后，我们保留最长不超过180天。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;三、	用户义务及免责声明&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一）您保证，您使用自己的手机进行手机运营商信息的授权，授权的内容为您本人的信息，不可为他人信息授权。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二）如果您所授权的手机运营商信息为他人信息，我们将有权暂停或终止与您的全部或部分服务协议，并将由您承担由此行为所产生的全部法律责任，我们将不对此承担法律责任。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;四、	不可抗力条款&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;因台风、地震、海啸、洪水、战争、计算机病毒感染、黑客攻击、网络通信故障等不能预见、不能控制的不可抗力因素，造成本公司不能正常向您提供服务而可能导致的损失，本公司不承担责任。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;五、	特殊情形&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一） 您理解并同意，鉴于网络服务的特殊性，信息来源提供方随时可能变更、暂停、中止或者终止部分或全部的查询服务。本协议中的相关条款根据该变更而自动做相应修改，双方无须另行签订协议，本公司也无需就上述服务的变更、暂停、中止或者终止向您承担任何责任。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二） 本协议可在您接受本公司提供服务的过程中多次使用，未来为您提供服务时再次涉及到本协议服务内容时无需您另行签署。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;六、	知识产权&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;本公司是提供信息服务的第三方，与本软件相关的任何内容和资源（包括但不限于文字、图案、图表、色彩、动画、声音、页面设计）的知识产权均属于本公司所有，受《著作权法》、《商标法》《专利法》、《反不正当竞争法》及其他相关法律法规的保护。未经本公司书面明确许可，任何单位和个人不得以任何方式将平台之内容和相关资源作全部或部分复制、转载、引用、编辑和建立本手机客户端的镜像。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;七、	网络传输风险&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;您理解并同意，由于本协议所列服务涉及个人隐私，通过网络提供和传输存在特定的泄密风险，用户一经充分考虑到该风险，并愿意承担该风险通过网络的方式完成本项服务，如果因网络传输导致个人隐私泄露等后果，将由用户自行承担。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;八、	法律适用条款以及争议解决方式&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;本协议的解释、履行及争议的解决均适用中华人民共和国法律。在协议履行期间，凡由本协议引起的或与本协议有关的一切争议、纠纷，当事人应首先协商解决。协商不成，在本公司所在地法院提起诉讼。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;九、	附则&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一）若本协议中的任何条文无论因何种原因完全或部分无效或不具有执行力，本协议的其他条款仍继续有效。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二）本协议将使用电子签署的方式完成签署，自您点击“同意”或“勾选”并进行下一步操作后则协议生效。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（三）您在本协议项下对本公司的授权将视为对本公司及本公司之关联公司的授权。本公司及本公司关联公司均可凭借您的授权及本协议约定执行相关操作。&lt;/p&gt;';

    /**
     * h5配置预览的内容
     * @throws \Exception
     */
    public function previewData()
    {
        // 限定访问方式
        $this->limitPost();

        // 预览的内容
       return $this->getPreviewData();
    }

    /**
     * 预览的内容
     * @throws \Exception
     */
    private function getPreviewData()
    {
        // 紧急联系人配置
        $data_emergency = $this->getEmergencyDataForPreview();

        // 申请信息配置
        $data_proposer = $this->getProposerDataForPreview();

        // 协议预览地址
        $data_url = $this->getProtocolDataForPreview();

        return array_merge($data_emergency, $data_proposer, $data_url);
    }

    /**
     * 协议预览地址
     * @return array
     */
    private function getProtocolDataForPreview()
    {
        $preview_url = C('PREVIEW_URL');
        return compact('preview_url');
    }

    /**
     * 申请信息配置
     * @return array
     */
    private function getProposerDataForPreview()
    {
        $request_body = $this->genParamsForPost();
        $ui_proposer_required_fields = implode(',', $request_body['ui_proposer_required_fields']);
        $ui_proposer_show_fields = implode(',', $request_body['ui_proposer_show_fields']);
        return compact('ui_proposer_required_fields', 'ui_proposer_show_fields');
    }

    /**
     * 为预览生成紧急联系人信息
     * @return array
     */
    private function getEmergencyDataForPreview()
    {
        $request_body = $this->genParamsForPost();

        // 最多紧急联系人
        $emergency_contact_max_number = $request_body['emergency_contact_max_number'];

        // 必填紧急联系人限制
        $emergency_contact_detail_relation_limits = $this->getEmergencyLimitForPreview($request_body);

        return compact('emergency_contact_max_number', 'emergency_contact_detail_relation_limits');
    }

    /**
     * 必填紧急联系人限制
     * @param array $request_body
     * @return array
     */
    private function getEmergencyLimitForPreview(array $request_body)
    {
        $list_container = [];

        array_walk($request_body, function($item, $key) use(&$list_container){
            // 如果不是紧急联系人的设置 则抛掉
            if (strpos($key, 'relationship') === false) {
                return true;
            }
            array_push($list_container, $item);
        });

        return json_encode($list_container, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 限定访问POST方式
     * @throws \Exception
     */
    private function limitPost()
    {
        if (!IS_POST) {
            throw new \Exception('本方法只可以使用POST方式请求b');
        }
    }

    /**
     * 授权协议
     * @throws \Exception
     * @return string
     */
    public function protocol()
    {
        // 限定访问方式
        $this->validateMethodForProtocol();

        // 检测参数
        $this->validateParamsForProtocol();

        // 获取协议
        return $this->getProtocol();
    }

    /**
     * 获取协议
     * @return string
     */
    private function getProtocol()
    {
        // 条件
        $where = $this->genConditionForProtocol();

        // 爬虫信息
        $crawler_info = $this->getOneCrawlerByCondition($where);

        return html_entity_decode($crawler_info['protocol_content']);
    }

    /**
     * 为协议生成条件
     * @return array
     */
    private function genConditionForProtocol()
    {
        $id = I('get.id', '', 'trim');
        return compact('id');
    }

    /**
     * 检测参数
     * @throws \Exception
     */
    private function validateParamsForProtocol()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            throw new \Exception('请传入合法的邦秒爬产品ID');
        }
    }

    /**
     * 限定访问方式
     * @throws \Exception
     */
    private function validateMethodForProtocol()
    {
        if (!IS_GET) {
            throw new \Exception('本方法只允许GET方式访问');
        }
    }

    /**
     * 设置h5配置
     * @throws \Exception
     */
    public function h5Config()
    {
        // 限定访问的方式
        $this->validateMethodsForH5();

        // 检测参数
        $this->validateParamsForH5();

        // 更新配置
        $this->updateH5Config();
    }

    /**
     * 更新h5配置
     * @throws \Exception
     */
    private function updateH5Config()
    {
        // 生成参数
        $params = $this->genParamsForUpdateH5();

        // 条件
        $where = $this->genConditionForH5Update();

        // DO Update
        $this->updateH5Do($where, $params);

        // 通知接口数据发生了变化
        $this->flushToken($where);
    }

    /**
     * @param array $where
     * @return array
     */
    protected function getOneCrawlerByCondition(array $where)
    {
        return (new AuthModel())->where($where)
            ->find();
    }

    /**
     * 更新h5配置的条件
     * @return array
     */
    private function genConditionForH5Update()
    {
        $id = I('post.id', '', 'trim');

        return compact('id');
    }

    /**
     * 更新h5配置
     * @param array $where
     * @param array $params
     */
    private function updateH5Do(array $where, array $params)
    {
        (new AuthModel())->where($where)->save($params);
    }

    /**
     * 为更新h5配置生成参数
     * @return array
     */
    private function genParamsForUpdateH5()
    {
        // 申请人信息参数
        $params_proposer = $this->genProposerParamsForUpdateH5();

        // 其他配置参数
        $params_other = $this->genOtherParamsForUpdateH5();

        // 紧急联系人配置参数
        $params_emergency = $this->genEmergencyParamsForUpdateH5();

        return array_merge($params_emergency, $params_other, $params_proposer);
    }

    /**
     * 紧急联系人配置参数
     * @return array
     */
    private function genEmergencyParamsForUpdateH5()
    {
        $need_contactor = $this->isNeedContactorPageForH5();
        if ($need_contactor === false) {
            return [];
        }

        // 最多紧急联系人数量
        $emergency_contact_max_number = (int)I('post.emergency_contact_max_number', '', 'trim');

        // 必填紧急联系人限制
        $emergency_contact_detail_limits = $this->genEmergencyLimitForH5Config();

        return compact('emergency_contact_detail_limits', 'emergency_contact_max_number');
    }

    /**
     * 必填紧急联系人限制
     * @return string
     */
    private function genEmergencyLimitForH5Config()
    {
        $emergency_contact_detail_limits = [];
        array_walk(I('post.'), function ($item, $item_key) use (&$emergency_contact_detail_limits) {
            // 是否是必填紧急联系人配置
            $is_emergency = $this->itemBelongsToEmergency($item_key);
            if ($is_emergency === true) {
                array_push($emergency_contact_detail_limits, $item);
            }
        });

        return serialize($emergency_contact_detail_limits);
    }

    /**
     * 生成其他的配置参数
     */
    private function genOtherParamsForUpdateH5()
    {
        // H5授权链接失效限制时间
        $effective_authorization_time = (int)I('post.effective_authorization_time', '', 'trim');

        $protocol_content = I('post.protocol_content', '', 'trim');
        $protocol_default = (int)I('post.protocol_default', '', 'trim');

        // 如果是默认协议的话 则重置协议内容
        $protocol_content = $protocol_default === 1 ? $this->protocol_default_content : $protocol_content;

        return compact('effective_authorization_time', 'protocol_default', 'protocol_content');
    }

    /**
     * 申请人参数
     * @return array
     */
    private function genProposerParamsForUpdateH5()
    {
        $need_contactor = $this->isNeedContactorPageForH5();
        if ($need_contactor === false) {
            return [];
        }

        $ui_proposer_required_fields = I('post.ui_proposer_required_fields', []);
        $ui_proposer_show_fields = I('post.ui_proposer_show_fields', []);
        $ui_proposer_required_fields = implode(',', $ui_proposer_required_fields);
        $ui_proposer_show_fields = implode(',', $ui_proposer_show_fields);

        return compact('ui_proposer_show_fields', 'ui_proposer_required_fields');
    }

    /**
     * 检测参数
     * @throws \Exception
     */
    private function validateParamsForH5()
    {
        // 检测id是否传递
        $this->validateIdForH5();

        // 检查其他配置
        $this->validateOtherParamsForH5();

        // 接入联系人页面
        $this->validateContactoPageForH5();
    }

    /**
     * 接入联系人页面
     * @throws \Exception
     */
    private function validateContactoPageForH5()
    {
        // 如果申请人信息不需要的话 则不进行限制
        $validate_contactor_page = $this->isNeedContactorPageForH5();
        if ($validate_contactor_page === false) {
            return true;
        }

        // 检查申请人信息
        $this->validateUiProposerFieldsH5();

        // 检查紧急联系人信息
        $this->validateEmergencyForH5();
    }

    /**
     * 是否介入了紧急联系人页面
     * @return bool
     */
    private function isNeedContactorPageForH5()
    {
        $contactor_page = I('post.contactor_page', '', 'trim');
        return $contactor_page === 'Y';
    }

    /**
     * 检测id是否传递
     * @throws \Exception
     */
    private function validateIdForH5()
    {
        $id = I('post.id', '', 'trim');
        if ($id === '' || !is_numeric($id)) {
            throw new \Exception('更新id是必须传递的参数');
        }
    }

    /**
     * 检查紧急联系人信息
     * @throws \Exception
     */
    private function validateEmergencyForH5()
    {
        // 检测最多联系人数量
        $this->validateContactMaxForH5();

        // 检测必填联系人
        $this->validateEmergencyGroupForH5();
    }

    /**
     * 检测必填联系人
     * @throws \Exception
     */
    private function validateEmergencyGroupForH5()
    {
        $emergency_contact_max_number = (int)I('post.emergency_contact_max_number', '', 'trim');

        // 必填紧急联系人组数
        $emergency_number = $this->getEmergencyGroupNumberForH5();
        if ($emergency_contact_max_number < $emergency_number) {
            throw new \Exception('必填紧急联系人组数必须小于最多联系人数量');
        }
    }

    /**
     * 必填紧急联系人组数
     * @return integer
     */
    private function getEmergencyGroupNumberForH5()
    {
        $emergency_number = 0;
        array_walk(I('post.'), function ($item, $item_key) use (&$emergency_number) {
            // 是否是必填紧急联系人
            $emergency_number += $this->itemBelongsToEmergency($item_key);
        });
        return $emergency_number;
    }

    /**
     * 当前参数是否属于紧急联系人
     * @param string $item_key
     * @return boolean
     */
    private function itemBelongsToEmergency($item_key)
    {
        return strpos($item_key, 'relationship') !== false;
    }

    /**
     * 检测最多联系人数量
     * @throws \Exception
     */
    private function validateContactMaxForH5()
    {
        $emergency_contact_max_number = I('post.emergency_contact_max_number', '', 'trim');
        if (!is_numeric($emergency_contact_max_number) || $emergency_contact_max_number < 0 || $emergency_contact_max_number > 15) {
            throw new \Exception('最多紧急联系人数量必须是0到15之间的整数');
        }
    }

    /**
     * 检查申请人信息配置
     * @throws \Exception
     */
    protected function validateUiProposerFieldsH5()
    {
        $ui_proposer_required_fields = I('post.ui_proposer_required_fields', []);
        $ui_proposer_show_fields = I('post.ui_proposer_show_fields', []);
        if (!$ui_proposer_required_fields || !$ui_proposer_show_fields) {
            throw new \Exception('申请人显示的字段和必选字段不可以为空');
        }

        // 必选字段必须是必须首先是可选字段
        if (array_diff($ui_proposer_required_fields, $ui_proposer_show_fields)) {
            throw new \Exception('必选字段必须是显示的字段');
        }
    }

    /**
     * 检查其他配置
     * @throws \Exception
     */
    private function validateOtherParamsForH5()
    {
        // 检查H5授权链接失效限制时间
        $this->validateAuthorizationTimeForH5();

        // 授权协议
        $this->validateProtocolForH5();
    }

    /**
     * 检测授权协议
     * @return mixed
     * @throws \Exception
     */
    private function validateProtocolForH5()
    {
        $protocol_content = I('post.protocol_content', '', 'trim');

        // wangEditor 不传入任何参数的的时候回向textarea填充<p><br></p>的html实体
        if ($protocol_content == '&lt;p&gt;&lt;br&gt;&lt;/p&gt;') {
            throw new \Exception('请填写授权协议');
        }
    }

    /**
     * 检查H5授权链接失效限制时间
     * @return mixed
     * @throws \Exception
     */
    private function validateAuthorizationTimeForH5()
    {
        $effective_authorization_time = I('post.effective_authorization_time', '', 'trim');
        if (!is_numeric($effective_authorization_time)) {
            throw new \Exception('H5授权链接失效限制时间必须是1到36的整数或者-1(不限制)');
        }

        if (!($effective_authorization_time <= 36 && $effective_authorization_time > 0) && $effective_authorization_time != -1) {
            throw new \Exception('H5授权链接失效限制时间必须是1到36的整数或者-1(不限制)');
        }
    }

    /**
     * 限定访问的方式
     * @throws \Exception
     */
    private function validateMethodsForH5()
    {
        if (!IS_POST) {
            throw new \Exception('本方法只允许POST方式访问');
        }
    }
}