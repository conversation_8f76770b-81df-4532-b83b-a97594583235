<?php

namespace Api\Repositories;

use Api\Model\CrsAuxiliaryServicesModel;
use Api\Model\CuishouUserModel;

class BmCuishouRepository extends BaseRepository
{

    private $biz;

    public function __construct($biz)
    {
        $this->biz = $biz;
    }

    /**
     * 获取登陆客户下辖的产品
     * return array
     */
    public function getProductListOfLoginAccount()
    {
        // 条件
        $where = $this->genConditionForProductList();

        // 催收产品列表
        $list_cuishou = $this->getProductForCuishou($where);

        // 当前的催收分产品和邦秒爬产品之间的关系
        return $this->relationWithCrawler($list_cuishou);
    }

    /**
     * 获取催收产品
     * @param array $where 条件
     * @return array
     */
    protected function getProductForCuishou($where)
    {
        return (new CuishouUserModel())->where($where)
            ->field(['_id' => 0])
            ->select();
    }

    /**
     * 当前的催收分产品和邦秒爬产品之间的关系
     * @param array $list_cuishou 查询的催收分账号
     * @return array
     */
    protected function relationWithCrawler($list_cuishou)
    {
        $list_cuishou = array_map(function ($item_product) {
            // 获取和邦秒爬产品的关联关系
            $service_key = $item_product['apikey'];
            $service_cateid = 2;
            $item_relationship = $this->getRelationship(compact('service_cateid', 'service_key'));

            return array_merge($item_product, $item_relationship);
        }, $list_cuishou);

        return array_column($list_cuishou, null, 'id');
    }

    /**
     * 获取当前条件的催收分产品和邦秒爬产品之间的联系
     * @param array $where 条件
     * @return array
     */
    protected function getRelationship($where)
    {
        // 获取关联关系
        $list_relationship = $this->getRelationWithCrawler($where);

        // 没有关联邦秒爬产品
        if (!$list_relationship) {
            return [
                'belongs_crawler' => false,
                'belongs_id_crawler' => []
            ];
        }

        // 返回关联信息
        $list_relationship = array_column($list_relationship, null, 'uid');
        $belongs_id_crawler = array_keys($list_relationship);
        $belongs_crawler = true;
        return compact('belongs_crawler', 'belongs_id_crawler');
    }

    /**
     * 获取当前条件的催收分产品和邦秒爬产品之间的联系
     * @param array $where
     * @return array
     */
    protected function getRelationWithCrawler($where)
    {
        return (new CrsAuxiliaryServicesModel())->where($where)
            ->select();
    }

    /**
     * 生成登录客户下辖催收分产品的条件
     * @return array
     */
    protected function genConditionForProductList()
    {
        $list_cuishou = isset($this->biz['list_cuishou']) ? $this->biz['list_cuishou'] : [];
        $list_cuishou = array_map(function ($item) {
            return new \MongoInt32($item);
        }, $list_cuishou);

        $id = [
            'in', $list_cuishou
        ];
        return compact('id');
    }
}

