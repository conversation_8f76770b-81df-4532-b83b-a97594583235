<?php

namespace Api\Repositories;


class BangSyncRepository
{
    // token 参数
    private $token = '028974dfcf3f3b052f25777fd64b0debxxxx';

    // 产品需要的字段
    private $product_field_map = [
        'id' => 'id',
        'name' => 'name', 'type' => 'limit_type',
        'limit_num' => 'limit_num', 'total' => 'limit_total', 'cut_time' => 'expiration_date',
        'second_num' => 'limit_second', 'status' => 'status',
        'white_ips' => 'limit_ip', 'client_key' => 'apikey', 'client_screct' => 'apisecret',
        'add_time' => 'created_at',
        'updated_at', 'remark', 'contract_status'];

    // 同步产品需要传递的参数
    private $product_fillable = [
        'id', 'name', 'client_key', 'client_screct', 'white_ips', 'type', 'update_total_time',
        'total', 'total_num', 'limit_num', 'num', 'cut_time', 'second_num', 'status', 'add_time',
        'client_type'
    ];

    // 同步字段需要传递的字段
    private $relationship_fillable = [
        'client', 'name_bkwd', 'address_bkwd', 'type', 'num', 'itag_ids',
        'catnames', 'name_score', 'name_result', 'address_score', 'address_result', 'time'
    ];

    // 字段列表
    private $relationship_need = [
        'name_bkwd', 'address_bkwd', 'type', 'num', 'itag_ids',
        'catnames', 'name_score', 'name_result', 'address_score', 'address_result'
    ];

    /**
     * 日志
     * @param $response
     * @return string
     */
    public function log($response)
    {
        // 衹有失敗才會記錄日志
        if ($response['success']) {
            return '';
        }

        // 組裝要寫日志
        $token = I('get.token', '', 'trim');
        $data_post = I('post.');
        $method = __METHOD__;
        $msg = compact('token', 'data_post', 'method');
        $data_request = compact('response','msg');
        $info_log = json_encode($data_request, JSON_UNESCAPED_UNICODE);

        // 檢查日志文件是否存在
        $dir = RUNTIME_PATH . date('Ymd') . '/';
        if (!file_exists($dir) || !is_dir($dir)) {
            @mkdir($dir, 0755, true);
        }
        $destination = $dir . 'bang_sync_data' . '.log';

        // 寫入
        file_put_contents(
            $destination,
            '[' . date('Y-m-d H:i:s') . ']  ' . $info_log . PHP_EOL,
            FILE_APPEND
        );
    }

    /**
     * 同步relationship
     * @throws \Exception
     */
    public function syncRelationship()
    {
        // 检查参数
        $this->checkParamsForRelationship();

        // 生成参数
        $params_relationship = $this->genParamsForRelationship();

        // 添加
        D('BangProductFieldsApi')->addAll($params_relationship);
    }

    /**
     * 为同步字段生成参数
     * @return array
     */
    protected function genParamsForRelationship()
    {
        $data_post = I('post.');
        $data_relationship = [];
        $product_id = $data_post['client'];

        // 本地字段的存储方式
        $list_field_local = $this->getFieldProductOfLocal();

        // 循环遍历post参数, 生成需要的参数
        array_walk($data_post, function ($item, $item_key) use (&$data_relationship, $product_id, $list_field_local) {

            // 检查是否入库
            $is_useful = $this->ifThrowFieldForRelationship($item, $item_key);
            if ($is_useful == false) {
                return '';
            }

            // 选中的字段ID
            $field_id = $list_field_local[$item_key]['id'];

            // 新单元
            array_push($data_relationship, compact('product_id', 'field_id'));
        });

        return $data_relationship;
    }

    /**
     * 检查传入的字段是否入库
     * @param $item
     * @param $item_key
     * @return bool
     */
    private function ifThrowFieldForRelationship($item, $item_key)
    {
        if (!in_array($item_key, $this->relationship_need)) {
            return false;
        }
        if ($item == 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取本地库的字段数据构成
     * @return array
     */
    protected function getFieldProductOfLocal()
    {
        return D('BangFieldsApi')
            ->index('slug')
            ->select();

    }

    /**
     * 为字段同步检查参数
     * @throws \Exception
     */
    protected function checkParamsForRelationship()
    {
        // 检查token
        $this->checkToken();

        // 检查基本参数
        $this->checkBaseParamsForRelationship();
    }

    /**
     * 为同步字段检查基本参数
     * @throws \Exception
     */
    protected function checkBaseParamsForRelationship()
    {
        $data_post = I('post.');

        array_walk($this->relationship_fillable, function ($field) use ($data_post) {
            if (!isset($data_post[$field])) {
                throw new \Exception('缺少字段 ' . $field);
            }
        });
    }

    /**
     * 同步产品
     * @throws \Exception
     */
    public function syncProduct()
    {
        // 检查参数
        $this->checkParamsForSyncProduct();

        // 生成参数
        $params_product = $this->genParamsForProduct();

        // 添加产品
        D('BangProductsApi')->add($params_product);
    }

    /**
     * 为产品生成参数
     * @return array
     */
    protected function genParamsForProduct()
    {
        $data_post = I('post.');
        $data_product = [];

        array_walk($data_post, function ($item, $item_key) use (&$data_product) {

            // 如果是需要的参数 那么更换索引
            if (isset($this->product_field_map[$item_key])) {
                $field_product = $this->product_field_map[$item_key];

                // status存储是有区别的
                if ($field_product == 'status') {
                    $item = $item ? $item : 2;
                }

                $data_product[$field_product] = $item;
            }
        });
        return $data_product;
    }

    /**
     * 为产品同步检查参数
     * @throws \Exception
     */
    protected function checkParamsForSyncProduct()
    {
        // 检查 token
        $this->checkToken();

        // 检查基本参数
        $this->checkBaseParam();
    }

    /**
     * 检查基本参数
     * @throws \Exception
     */
    protected function checkBaseParam()
    {
        $post_data = I('post.');
        foreach ($this->product_fillable as $field) {
            if (!isset($post_data[$field])) {
                throw new \Exception('缺少参数' . $field);
            }
        }
    }

    /**
     * 检查token
     * @throws \Exception
     */
    protected function checkToken()
    {
        $token = I('get.token', '', 'trim');
        if (!$token) {
            throw new \Exception('请输入参数token');
        }

        if ($token !== $this->token) {
            throw new \Exception('请输入合法的token');
        }
    }
}
