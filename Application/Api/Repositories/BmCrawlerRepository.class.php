<?php
/**
 * @Author: lidandan
 * @Date:   2018-06-22 11:51:00
 */
namespace Api\Repositories;

class BmCrawlerRepository
{
    protected $biz;

    public function __construct($biz)
    {
        $this->biz = $biz;
    }

    /**
     * 获取该客户下的所有邦秒爬账户
     * @return array
     */
    public function getCrawlerAccountList()
    {
        $where = $this->getCrawlerListParam();
        $user_list = D('Auth')
            ->field('id,status,source,appid,appsecret,developer,created_at,expiration_date')
            ->where($where)
            ->index('id')
            ->select();
        return $user_list;
    }

    /**
     * 获取查询的条件
     * @return array
     */
    public function getCrawlerListParam()
    {
        $list_crawler = isset($this->biz['list_crawler']) ? $this->biz['list_crawler'] : [];
        $param['id'] = ['in', $list_crawler];
        return $param;
    }
}
