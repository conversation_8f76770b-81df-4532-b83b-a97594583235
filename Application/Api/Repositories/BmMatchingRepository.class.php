<?php

namespace Api\Repositories;

use Api\Model\CuishouUserModel;
use Api\Model\MatchingAccountModel;

class BmMatchingRepository extends BaseRepository
{
    protected $biz;

    public function __construct($biz)
    {
        $this->biz = $biz;
    }

    /**
     * 获取登陆客户下辖的产品
     * return array
     */
    public function getProductListOfLoginAccount()
    {
        // 条件
        $where = $this->genConditionForProductList();

        // 催收产品列表
        $list_matching = $this->getProductForMatching($where);

        // 当前的催收分产品和邦秒爬产品之间的关系
        return $this->relationWithCuishou($list_matching);
    }

    /**
     * @param array $list_matching 邦秒配单号版产品列表
     * @return array
     */
    protected function relationWithCuishou($list_matching)
    {
        $list_matching = array_map(function ($item_product) {
            // 获取和邦秒爬产品的关联关系
            $itag_apikey = $item_product['apikey'];
            $item_relationship = $this->getRelationship(compact('itag_apikey'));

            return array_merge($item_product, $item_relationship);
        }, $list_matching);

        return array_column($list_matching, null, 'id');
    }

    /**
     * 获取当前条件的催收分产品和邦秒爬产品之间的联系
     * @param array $where 条件
     * @return array
     */
    protected function getRelationship($where)
    {
        // 获取关联关系
        $list_relationship = $this->getRelationWithCuishou($where);

        // 没有关联邦秒爬产品
        if (!$list_relationship) {
            return [
                'belongs_cuishou' => false,
                'belongs_id_cuishou' => []
            ];
        }

        // 返回关联信息
        $list_relationship = array_column($list_relationship, null, 'id');
        $belongs_id_cuishou = array_keys($list_relationship);
        $belongs_cuishou = true;
        return compact('belongs_cuishou', 'belongs_id_cuishou');
    }

    /**
     * 获取当前条件的催收分产品和邦秒爬产品之间的联系
     * @param array $where
     * @return array
     */
    protected function getRelationWithCuishou($where)
    {
        return (new CuishouUserModel())->where($where)
            ->field(['_id' => 0, 'id' => 1, 'developer' => 1])
            ->select();
    }

    /**
     * 获取邦秒爬产品的信息
     * @param array $where 条件
     * @return array
     */
    protected function getProductForMatching($where)
    {
        return (new MatchingAccountModel())->where($where)
            ->select();
    }

    /**
     * 生成登录客户下辖邦秒配单号版产品的条件
     * @return array
     */
    protected function genConditionForProductList()
    {
        $list_matching = isset($this->biz['list_matching']) ? $this->biz['list_matching'] : [];
        $id = [
            'in', $list_matching
        ];
        return compact('id');
    }
}