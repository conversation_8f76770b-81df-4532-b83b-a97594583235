<?php
namespace Api\Repositories;

class StatMatchingRepository extends BaseRepository
{
    protected $biz;

    public function __construct($biz)
    {
        $this->biz = $biz;
    }

    /**
     * 获取邦秒配单号版统计列表
     * @return array
     */
    public function getMatchingList()
    {
        $where = $this->getMatchingListParam();
        $list = $this->getMatchingListData($where);
        $total['used'] = array_sum(array_column($list, 'used'));
        return ['list' => $list, 'total' => $total];
    }

    /**
     * 获取邦秒配单号版统计详情
     * @return array
     */
    public function getMatchingDetail()
    {
        $where = $this->getMatchingDetailParam();
        $list = $this->getMatchingDetailData($where);
        $total['daily_used'] = array_sum(array_column($list, 'daily_used'));

        return ['list' => $list, 'total' => $total];
    }

    /**
     * 获取邦秒配单号版列表参数
     * @return array
     */
    public function getMatchingListParam()
    {
        $where = $this->getTimeLimitParam();
        $apikey = isset($this->biz['apikey']) ? $this->biz['apikey'] : [];
        if (!$apikey) {
            throw new \Exception('邦秒配单号版参数错误');
        }
        $where['apikey'] = ['in', $apikey];
        return $where;
    }

    /**
     * 获取邦秒配单号版统计详情
     * @return array
     */
    public function getMatchingDetailParam()
    {
        $where = $this->getTimeLimitParam();
        $apikey = I('post.apikey', '', 'trim');
        if (!$apikey || !isset($this->biz['apikey']) || !in_array($apikey, $this->biz['apikey'])) {
            throw new \Exception("邦秒配单号版参数错误");
        }
        $where['apikey'] = $apikey;
        return $where;
    }

    /**
     * 获取时间查询条件
     * @return array
     */
    public function getTimeLimitParam()
    {
        $start_date = I('post.start_date', '', 'trim');
        $end_date = I('post.end_date', '', 'trim');

        $start_date = $start_date ? date('Ymd', strtotime($start_date)) : date('Ymd');
        $end_date = $end_date ? date('Ymd', strtotime($end_date)) : date('Ymd', strtotime('-1 month'));

        $where['daily_time'] = ['between', [$start_date, $end_date]];
        return $where;
    }

    /**
     * 根据条件获取统计列表数据
     * @param  array $where 查询条件
     * @return list
     */
    public function getMatchingListData($where)
    {
        $where['project'] = 'itag';
        $list = D('AdminApistat')->where($where)
                                 ->group('apikey')
                                 ->index('apikey')
                                 ->field('sum(daily_used) as used, apikey')
                                 ->select();
        return $list;
    }

    /**
     * 获取邦秒配单号版统计详情数据
     * @param  array $where 查询条件
     * @return list
     */
    public function getMatchingDetailData($where)
    {
        $where['project'] = 'itag';
        $list = D('AdminApistat')->where($where)
                                 ->group('daily_time')
                                 ->index('daily_time')
                                 ->field('daily_used, daily_time')
                                 ->select();
        return $list;
    }
}