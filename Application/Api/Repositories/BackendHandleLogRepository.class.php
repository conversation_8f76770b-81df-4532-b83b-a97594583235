<?php

namespace Api\Repositories;

use Common\Common\ResponseTrait;
use Common\Model\HandlerLogModel;

class BackendHandleLogRepository extends BaseRepository
{
    use ResponseTrait;

    /**
     * log列表
     * @throws \Exception
     */
    public function listLog()
    {
        // 限定访问方式
        $this->limitPostMethod();

        // 生成条件
        $where = $this->genParamsForList();

        // 获取列表
        return $this->listDo($where);
    }

    /**
     * 获取列表
     * @param array $where
     * @return array
     */
    private function listDo(array $where)
    {
        return (new HandlerLogModel())->where($where)
            ->order('id desc')
            ->select();
    }

    private function genParamsForList()
    {
        // 限定操作类型
        $limit_handle_type = $this->limitHandleType();

        // 限定状态
        $limit_handle_result = $this->limitHandleResult();

        // 限定操作人
        $limit_handle_user = $this->limitHandleUser();

        return array_merge($limit_handle_type, $limit_handle_result, $limit_handle_user);
    }

    /**
     * 限定操作人
     * @return array
     */
    private function limitHandleUser()
    {
        $request_body = $this->genParamsForPost();
        $handle_user = array_key_exists('handle_user', $request_body) ? trim($request_body['handle_user']) : '';

        if (!$handle_user) {
            return [];
        }

        $handle_user = [
            'LIKE', '%' . $handle_user . '%'
        ];
        return compact('handle_user');
    }

    /**
     * 限定状态
     * @return array
     */
    private function limitHandleResult()
    {
        $request_body = $this->genParamsForPost();
        $handle_result = array_key_exists('handle_result', $request_body) ? trim($request_body['handle_result']) : '';
        if ($handle_result === '') {
            return [];
        }
        return compact('handle_result');
    }

    /**
     * 限定操作类型
     * @return array
     */
    private function limitHandleType()
    {
        $request_body = $this->genParamsForPost();
        $handle_type = array_key_exists('handle_type', $request_body) ? trim($request_body['handle_type']) : '';
        if ($handle_type) {
            return compact('handle_type');
        }
        return [];
    }

    /**
     * 限定访问方式
     * @throws \Exception
     */
    private function limitPostMethod()
    {
        if (!IS_POST) {
            throw new \Exception('本方法只通过POST方式访问');
        }
    }
}
