<?php

namespace Api\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Account\Repositories\AccountRepository;
use Common\Common\CurlTrait;
use Common\Model\FeeConfigModel;

class BackendCustomerRepository
{
    use CurlTrait;

    /**
     * 获取某个客户下的所有开通特定产品的账号列表
     * @return array
     * @throws \Exception
     */
    public function refreshAccountList()
    {
        // 校验参数
        $this->_validateParamsForRefreshAccountList();

        // 生成条件
        $where = $this->_genParamsForRefreshAccountList();

        // 获取列表
        return AccountModel::getAccountList($where, ['account_name', 'account_id']);
    }

    /**
     * 生成条件
     * @return array
     */
    private function _genParamsForRefreshAccountList()
    {
        list($customer_id, $product_id) = [
            trim(I('get.customer_id')),
            trim(I('get.product_id')),
        ];

        $list_customers_with_bill = (new FeeConfigModel())->where(compact('customer_id', 'product_id'))
            ->distinct(true)
            ->field('account_id')
            ->select();
        $account_id = [
            'in',
            array_column($list_customers_with_bill, 'account_id')
        ];
        return compact('account_id');
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    private function _validateParamsForRefreshAccountList()
    {
        list($customer_id, $product_id) = [
            trim(I('get.customer_id')),
            trim(I('get.product_id')),
        ];

        if (!$customer_id || !$product_id) {
            throw new \Exception('必须传入customer_id和product_id');
        }
    }

    /**
     * 刷新产品列表
     * @return array
     * @throws \Exception
     */
    public function refreshProductList()
    {
        // 校验参数
        $this->_validateParamsForRefreshProductList();

        // 生成条件
        $where = $this->_genParamsForRefreshProductList();

        // 获取列表
        return ProductModel::getListProduct($where, ['product_id', 'product_name']);
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    private function _validateParamsForRefreshProductList()
    {
        if (!trim(I('get.customer_id'))) {
            throw new \Exception('请传入客户ID');
        }
    }

    /**
     * 生成条件
     * @return array
     */
    private function _genParamsForRefreshProductList()
    {
        $customer_id = trim(I('get.customer_id'));
        $list_customers_with_bill = (new FeeConfigModel())->where(compact('customer_id'))
            ->distinct(true)
            ->field('product_id')
            ->select();
        $product_id = [
            'in',
            array_column($list_customers_with_bill, 'product_id')
        ];
        return compact('product_id');

    }

    /**
     * 获取开通了账单的客户列表
     * @return array
     */
    public function getBillCustomer()
    {
        // 生成条件
        $where = $this->_genParamsForBillCustomers();

        // 获取列表
        return CustomerModel::getListCustomer($where, ['customer_id', 'name']);
    }

    /**
     * 生成条件
     * @return array
     */
    private function _genParamsForBillCustomers()
    {
        $list_customers_with_bill = (new FeeConfigModel())->distinct(true)
            ->field('customer_id')
            ->select();
        $customer_id = [
            'in',
            array_column($list_customers_with_bill, 'customer_id')
        ];
        return compact('customer_id');
    }

    /**
     * 一键更新
     * @throws \Exception
     * @return array
     */
    public function upgradeAtOnce()
    {
        $this->limitMethodPatch();

        // 检查参数
        $this->validateParamsForUpgrade();

        // 更新账号状态
        $this->updateAccountStatusAtOnce();

        // 更新产品
        $result = $this->updateProductAtOnce();

        //监听开通产品状态，使的客户状态根据所属产品状态进行变化
        (new AccountRepository())->listenAccountProductStatusForUpgradeAtOnce($this->patchkey('customer_id', '', 'trim'), $this->patchKey('status_product', '', 'trim'));
        return $result;
    }

    /**
     * 更新产品状态
     * @return array
     */
    private function updateProductAtOnce()
    {
        // 是否需要更新产品
        if (!$this->determineNeedUpdateProductForOnce()) {
            return $this->getProductAndAccountAfterUpdated();
        }

        // 参数
        list($where, $params) = $this->listProductParamsForOnce();

        // 更新账号产品
        $this->updateAccountProductByCondition($where, $params);

        return $this->getProductAndAccountAfterUpdated();
    }

    /**
     * 更新后的账号产品的状态
     * @return array
     */
    private function getProductAndAccountAfterUpdated()
    {
        // 获取所属的账号
        $list_account = $this->getListAccountOfCustomerForOnce();

        // 获取所属的产品
        $list_product = $this->getListProductOfCustomerForOnce();
        return compact('list_account', 'list_product');
    }


    /**
     * 获取所属的产品
     * @return mixed
     */
    private function getListProductOfCustomerForOnce()
    {
        $where = $this->genConditionsForProductOnce();
        return $this->getListProductByCondition($where);
    }

    /**
     * @param array $where
     * @return mixed
     */
    private function getListProductByCondition(array $where)
    {
        return (new AccountProductModel())->where($where)
            ->select();
    }

    /**
     *
     * @return mixed
     */
    private function getListAccountOfCustomerForOnce()
    {
        $customer_id = $this->patchKey('customer_id', '', 'trim');
        return $this->getListAccountByCondition(compact('customer_id'));
    }

    /**
     * 更新账号产品
     * @param array $where
     * @param array $params
     */
    private function updateAccountProductByCondition(array $where, array $params)
    {
        (new AccountProductModel())->where($where)
            ->save($params);
    }

    /**
     * @return array
     */
    private function listProductParamsForOnce()
    {
        // 条件
        $where = $this->genConditionsForProductOnce();

        // 参数
        $params = $this->genParamsForProductOnce();

        return [$where, $params];
    }

    /**
     * 参数
     * @return array
     */
    private function genParamsForProductOnce()
    {
        $where['update_at'] = time();
        $contract_status = $this->patchKey('status_product', '', 'trim');
        $end_time = $this->patchKey('end_time', '', 'trim');

        $contract_status && $where['contract_status'] = $contract_status;
        if ($end_time) {
            $where['end_time'] = strtotime($end_time) + 86399;
        }
        return $where;
    }

    /**
     * 产品条件
     * @return array
     */
    private function genConditionsForProductOnce()
    {
        $customer_id = $this->patchKey('customer_id', '', 'trim');
        $status = 1;
        $list_accounts = $this->getListAccountByCondition(compact('customer_id', 'status'), ['account_id']);

        // 账号iD 列表
        $list_account_ids = $list_accounts ? array_column($list_accounts, 'account_id') : ['none'];

        $account_id = [
            'in', $list_account_ids
        ];

        return compact('status', 'account_id');
    }

    private function getListAccountByCondition(array $where, $field = '*')
    {
        return (new AccountModel())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 是否需要更新产品
     * @return bool
     */
    private function determineNeedUpdateProductForOnce()
    {
        $contract_status = $this->patchKey('status_product', '', 'trim');
        $end_time = $this->patchKey('end_time', '', 'trim');

        return $contract_status || $end_time;
    }

    /**
     * 更新账号状态
     */
    private function updateAccountStatusAtOnce()
    {
        // 不更新
        $status = $this->patchKey('status_account', '', 'trim');
        if (!$status && $status !== 0 && $status !== '0') {
            return;
        }

        // 更新
        $customer_id = $this->patchKey('customer_id', '', 'trim');
        $update_at = time();
        (new AccountModel())->where(compact('customer_id'))
            ->save(compact('update_at', 'status'));
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    private function validateParamsForUpgrade()
    {
        $customer_id = $this->patchkey('customer_id', '', 'trim');
        if (!$customer_id) {
            throw new \Exception('请传入customer_id参数');
        }

        // 状态
        $status_product = $this->patchkey('status_product', '', 'trim');
        $status_account = $this->patchkey('status_account', '', 'trim');
        $end_time = $this->patchkey('end_time', '', 'trim');

        // 是否完全没有改变
        $determine_nothing_changed = !$status_product && (!$status_account && $status_account !== 0 && $status_account !== '0') && !$end_time;
        if ($determine_nothing_changed) {
            throw new \Exception('您未变更任何信息，无法保存!');
        }
    }
}