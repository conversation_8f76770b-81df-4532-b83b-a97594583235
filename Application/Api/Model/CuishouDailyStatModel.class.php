<?php

namespace Api\Model;

use Think\Model\MongoModel;

class CuishouDailyStatModel extends MongoModel
{
    protected $connection = 'DUNNING_MONGO';
    protected $tablePrefix = '';
    protected $dbName = 'cuishou';
    protected $tableName = 'cuishou_daily_stat';

    /**
     * @param $where
     * @param null $group_type
     * @return mixed
     */
    public function accessCount($where, $group_type = null)
    {
        $pipeline = [
            ['$match' => $where],
            ['$group' => [
                '_id' => $group_type,
                'access_counts' => [
                    '$sum' => '$access_counts'
                ],
                'success_counts' => [
                    '$sum' => '$success_counts'
                ],
                'dunning_times' => [
                    '$sum' => '$dunning_times'
                ],
                'not_sure_dunning_times' => [
                    '$sum' => '$not_sure_dunning_times'
                ],
                'not_times' => [
                    '$sum' => '$not_times'
                ]
            ]],
        ];

        $stat_data = $this
            ->getCollection()
            ->aggregate($pipeline, ['cursor' => (object)['batchSize' => 10000]]);

        return $stat_data['cursor']['firstBatch'] ? $stat_data['cursor']['firstBatch'] : [];
    }
}
