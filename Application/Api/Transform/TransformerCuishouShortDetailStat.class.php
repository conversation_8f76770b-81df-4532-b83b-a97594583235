<?php

namespace Api\Transform;

use Common\Common\CurlTrait;
use Common\Common\HandlerLog;
use Common\Common\ResponseTrait;

class TransformerCuishouShortDetailStat extends Transformer
{
    use CurlTrait,ResponseTrait;
    protected $list_field = ['c_all', 'c_success'];

    /*
    * 详情的字段列表存放session中key
    * */
    private $session_detail_field_key = 'session_cuishou_short_detail_key';

    /**
     * @param array $item
     * @throws \Exception
     * @return array
     */
    public function transForm(array $item)
    {
        // 获取slug列表
        $this->initSlugList();

        // 格式数据
        return $this->formatData($item);
    }

    /**
     * 格式数据
     * @param array $item
     * @return array
     */
    private function formatData(array $item)
    {
        array_walk($this->list_field, function($field) use (&$item){
            // 如果这个slug不存在 则赋值0
            if (!array_key_exists($field, $item)) {
                $item[$field] = 0;
            }
        });
        return $item;
    }

    /**
     * 初始化slug列表
     * @throws \Exception
     */
    private function initSlugList()
    {
        // 如果session中已经存放了字段列表
        if ($list_field = session($this->session_detail_field_key)) {
            $this->list_field = $list_field;
            return true;
        }

        // 组合
        $list_slug = $this->getSlugFromApi([]);
        $list_slug = array_key_exists('list', $list_slug['data']) ? $list_slug['data']['list'] : [];
        $this->list_field = array_merge($this->list_field, array_column($list_slug, 'name'));
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function getSlugFromApi($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['cuishou_short_slug_list'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->post($url_api, $params);
            if ($result_request['code'] === 0) {
                break;
            }
            $i++;
        }
        // 三次请求失败,做记录
        $this->logForSlug($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 连续三次请求API失败
     * @param array $result_request
     * @param string $url
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    private function logForSlug(array $result_request, $url, array $params)
    {
        if ((int)$result_request['code'] === 0) {
            return true;
        }

        $msg_log = [
            'handle_type' => 'stat',
            'description' => '邦信分快捷版SlugAPI连续三次请求失败',
            'content' => compact('result_request', 'url', 'params'),
            'handle_result' => 0
        ];
        HandlerLog::log($msg_log);
        throw new \Exception('邦信分快捷版SlugAPI连续三次请求失败');
    }
}
