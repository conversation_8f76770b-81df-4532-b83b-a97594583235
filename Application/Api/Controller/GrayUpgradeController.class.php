<?php

namespace Api\Controller;

use Api\Repositories\GrayUpgradeRepository;

class GrayUpgradeController extends BaseCrawlerController
{
    private $repository_gray_upgrade;

    public function __construct()
    {
        parent::__construct();
        $this->repository_gray_upgrade = new GrayUpgradeRepository();
    }

    public function getUpgrade()
    {
        try {

            // 获取相应的灰度发布版本
            $data = $this->repository_gray_upgrade->getGrayUpgrade();
            $msg = [
                'success' => true,
                'status' => 0,
                'data' => $data,
                'msg' => '查询成功'
            ];

        } catch (\Exception $e) {
            $msg = [
                'success' => false,
                'status' => 9999,
                'msg' => $e->getMessage()
            ];

        }
        $this->ajaxResponse($msg);
    }
}
