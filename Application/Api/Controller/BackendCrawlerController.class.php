<?php

namespace Api\Controller;

use Api\Repositories\BackendCrawlerRepository;

class BackendCrawlerController extends BaseController
{
    protected $repository_crawler;

    /**
     * BackendCrawlerController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->repository_crawler = new BackendCrawlerRepository();
    }

    /**
     * h5配置 action
     */
    public function h5Config()
    {
        try {
            $this->repository_crawler->h5Config();
            $msg = '更新成功';
            $data_transmit = I('post.');
            $this->response(compact('msg', 'data_transmit'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 某个产品的协议
     */
    public function protocol()
    {
        try {
            $protocol = $this->repository_crawler->protocol();
            $this->response(compact('protocol'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * h5配置预览的内容
     */
    public function previewData()
    {
        try {
            $preview_data = $this->repository_crawler->previewData();
            $this->response(compact('preview_data'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
