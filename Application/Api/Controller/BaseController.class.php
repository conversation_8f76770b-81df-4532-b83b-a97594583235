<?php

namespace Api\Controller;
use Common\Common\Approval;

use Think\Controller;

class BaseController extends Controller
{
    private $status = 0;

    public function __construct()
    {
        parent::__construct();
        //校验操作是否需要审核
        (new Approval())->checkAddApprove(session('site_login_name'));
        // TODO 统计鉴权
    }

    /**
     * @return int
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * 设置status
     * @param int $status
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;
        return $this;
    }

    public function responseNotFound($message = 'Not Found')
    {
        $this->setStatus(404)->responseError($message);
    }

    /**
     * @param  string $msg 错误信息
     */
    public function responseError($msg)
    {
        $status = $this->getStatus();
        $tip = 'failed';
        $errors = compact('tip', 'msg');
        $this->response(compact('status', 'errors'));
    }

    /**
     * 回复信息(success or error)
     * @param array $response
     */
    public function response(array $response)
    {

        if (!array_key_exists('status', $response)) {
            $status = $this->getStatus();
            $response = array_merge($response, compact('status'));
        }

        $response = json_encode($response, JSON_UNESCAPED_UNICODE);
        header('Content-Type:application/json; charset=utf-8');
        exit($response);
    }

    public function ajaxResponse($response)
    {
        if (is_array($response)) {
            $response = json_encode($response, JSON_UNESCAPED_UNICODE);
        }
        header('Content-Type:application/json; charset=utf-8');
        exit($response);
    }

    /**
     * 页面返回
     * @param  string $info     提示信息
     * @param  string $data     返回数据(跳转链接url 使用array('url'=>'') 跳转时间 使用array('times'=>))
     * @param  string $status   返回状态(stripos) error|success|tip_error|tip_success
     * @param  string $ajax     是否为ajax 默认为自动判断
     */
    public function __Return($info = '', $data = '', $status = 'tip_error', $ajax = 'auto') {
        $ajax = $ajax === 'auto' ? IS_AJAX : $ajax;
        if ($ajax === FALSE) {
            $jumpUrl = isset($data['url']) ? $data['url'] : '';
            $times = isset($data['times']) && is_numeric($data['times']) ? $data['times'] : FALSE;
            if (stripos($status, 'error') !== FALSE) {
                $this->error($info, $jumpUrl, $times);
            } else {
                $this->success($info, $jumpUrl, $times);
            }
        } else {
            $this->ajaxReturn(array('data' => $data, 'info' => $info, 'status' => $status), 'JSON', JSON_UNESCAPED_UNICODE);
        }
        exit;
    }

    
}
