<?php
namespace Api\Controller;

use Api\Repositories\StatMatchingRepository;

class BmStatMatchingController extends BaseBmpController
{
    private $matchingStat;
    public function __construct()
    {
        parent::__construct();
        $this->matchingStat = new StatMatchingRepository($this->biz);
    }

    public function index()
    {
        try {
            // 列表统计数据
            $list = $this->matchingStat->getMatchingList();
            $response = [
                'success' => true,
                'data' => $list
            ];
            $this->ajaxResponse($response);
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }

    public function detail()
    {
        try {
            // 列表统计数据
            $list = $this->matchingStat->getMatchingDetail();
            $response = [
                'success' => true,
                'data' => $list
            ];
            $this->ajaxResponse($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }
}