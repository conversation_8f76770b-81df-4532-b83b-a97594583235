<?php

namespace Api\Controller;

use Common\Controller\DataAuthController;
use Think\Controller;


class EmailDataAuthController extends Controller
{
    public function index()
    {
        ob_end_clean();
        header('Content-Type:application/json;charset=UTF8');
        try {
            if (IS_POST) {
                $customer_id = I('post.customer_id');
                if (empty($customer_id)) {
                    throw new \Exception('customer_id不存在');
                }
                $type = I('post.type', '');
                if (!empty($type)) {
                    if (!in_array($type, ['daily_auth', 'balance_auth', 'surplus_auth', 'expire_auth'])) {
                        throw new \Exception('不识别的type类型');
                    }
                }
                $result = DataAuthController::instance()->getUserDataByCustomerIdDataAuth($customer_id, $type);
                echo json_encode(
                    [
                        'status' => 'success',
                        'data'   => $result
                    ]
                );
            } else {
                throw new \Exception('页面不存在');
            }
        } catch (\Exception $exception) {
            echo json_encode(
                [
                    'status'  => 'error',
                    'message' => $exception->getMessage()
                ],
                JSON_UNESCAPED_UNICODE
            );
        }
    }

    public function getAllowCustomerId()
    {
        ob_end_clean();
        header('Content-Type:application/json;charset=UTF8');
        try {
            if (IS_POST) {
                $username = I('post.username');
                $result = DataAuthController::instance()->getReadCustomerIdsByUsername($username);
                echo json_encode(
                    [
                        'status' => 'success',
                        'data'   => $result
                    ]
                );
            } else {
                throw new \Exception('页面不存在');
            }
        } catch (\Exception $exception) {
            echo json_encode(
                [
                    'status'  => 'error',
                    'message' => $exception->getMessage()
                ],
                JSON_UNESCAPED_UNICODE
            );
        }
    }
}