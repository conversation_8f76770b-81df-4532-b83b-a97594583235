<?php

namespace Api\Controller;

use Api\Repositories\BangSyncRepository;

class BangSyncController extends BaseBangController
{
    // repository
    private $repository_bang_sync;

    public function __construct()
    {
        parent::__construct();

        $this->repository_bang_sync = new BangSyncRepository();
    }

    /**
     * 同步产品
     */
    public function syncProduct()
    {
        try {
            // 产品同步
            $this->repository_bang_sync->syncProduct();
            $response = [
                'success' => true,
                'msg' => '本次同步成功'
            ];

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'msg' => $e->getMessage()
            ];
        }

        // 寫日志
        $this->repository_bang_sync->log($response);

        $this->ajaxResponse($response);
    }

    /**
     * 同步字段
     */
    public function syncRelationship()
    {
        try {
            // 字段同步
            $this->repository_bang_sync->syncRelationship();
            $response = [
                'success' => true,
                'msg' => '本次同步成功'
            ];
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'msg' => $e->getMessage()
            ];
        }

        // 寫日志
        $this->repository_bang_sync->log($response);

        $this->ajaxResponse($response);
    }
}
