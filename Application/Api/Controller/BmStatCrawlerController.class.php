<?php
/**
 * @Author: lidandan
 * @Date:   2018-06-21 20:03:55
 */
namespace Api\Controller;

use Api\Repositories\StatCrawlerRepository;

class BmStatCrawlerController extends BaseBmpController
{
    private $crawlerStat;

    public function __construct()
    {
        parent::__construct();
        $this->crawlerStat = new StatCrawlerRepository($this->biz);
    }

    /**
     * 统计列表
     * @return
     */
    public function index()
    {
        try {
            // 列表统计数据
            $list = $this->crawlerStat->getReportList();
            $response = [
                'success' => true,
                'data' => $list
            ];
            $this->ajaxResponse($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }

    /**
     * 详情统计数据
     * @return [type] [description]
     */
    public function detail()
    {
        try {
            // 列表统计数据
            $list = $this->crawlerStat->getReportDetail();
            $response = [
                'success' => true,
                'data' => $list
            ];
            $this->ajaxResponse($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }
}