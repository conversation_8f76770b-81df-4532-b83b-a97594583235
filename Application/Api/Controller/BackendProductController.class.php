<?php

namespace Api\Controller;

use Account\Model\ProductModel;
use Api\Repositories\BackendProductRepository;

class BackendProductController extends BaseController
{
    private $repository_product;

    /**
     * BackendPreviewController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->repository_product = new BackendProductRepository();
    }

    public function getListProduct()
    {
        try {
            $list = ProductModel::getListProduct();
            $this->response(compact('list'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 根据id获取对应的产品信息
     */
    public function product()
    {
        try {
            $data = $this->repository_product->product();
            $this->response(compact('data'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 更新产品
     */
    public function updateProduct()
    {
        try {
            $this->repository_product->updateProduct();
            $msg = '更新成功';
            $this->response(compact('msg'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 当更新了重要字段的时候,弹窗预警
     */
    public function warningWhenUpdateImportField()
    {
        try {
            $import_fields_changed = $this->repository_product->warningWhenUpdateImportField();
            $this->response(compact('import_fields_changed'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * GET方式获取秒配的签名字符串
     */
    public function signs()
    {
        try {
            $response = $this->repository_product->signs();
            $this->response($response);
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
