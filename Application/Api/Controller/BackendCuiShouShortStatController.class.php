<?php

namespace Api\Controller;

use Api\Repositories\BackendCuiShouShortStatRepository;

class BackendCuiShouShortStatController extends BaseController
{
    protected $repository_stat;

    public function __construct()
    {
        parent::__construct();
        $this->repository_stat = new BackendCuiShouShortStatRepository();
    }

    /**
     * 查看单个客户
     */
    public function customerShow()
    {
        try {
            // 全量客户列表
            $customer = $this->repository_stat->customerShow();
            $this->response(compact('customer'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 统计详情
     */
    public function detailStat()
    {
        try {
            $list_stat = $this->repository_stat->detailStat();
            $this->response(compact('list_stat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 统计排序
     */
    public function sortBy()
    {
        try {
            $list_stat = $this->repository_stat->sortBy();
            $this->response(compact('list_stat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 客户全量列表
     */
    public function customerList()
    {
        try {
            $list_customer = $this->repository_stat->customerList();
            $this->response(compact('list_customer'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 统计列表 action
     */
    public function listStat()
    {
       try {
           $list_stat = $this->repository_stat->listStat();
           $this->response(compact('list_stat'));
       } catch (\Exception $e) {
           $this->setStatus(1478)->responseError($e->getMessage());
       }
    }

    /**
     * 账号列表
     */
    public function accountList()
    {
        try {
            // 全量账号列表
            $list_account = $this->repository_stat->accountList();
            $this->response(compact('list_account'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 查看单个账号
     */
    public function accountShow()
    {
        try {
            // 全量账号列表
            $account = $this->repository_stat->accountShow();
            $this->response(compact('account'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     *  详情的展示的columns
     */
    public function detailColumns()
    {
        try {
            $list_columns = $this->repository_stat->detailColumns();
            $this->response(compact('list_columns'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
