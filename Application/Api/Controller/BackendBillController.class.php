<?php

namespace Api\Controller;

use Account\Model\AccountModel;
use Common\Common\ResponseTrait;

class BackendBillController
{
    use ResponseTrait;

    /**
     * 获取一个客户下辖的账号列表
     */
    public function getAccountListOfOneCustomer()
    {
        try {
            // 检查参数
            $this->validateParamsForList();

            // 生成条件
            $where = ['customer_id' => I('get.customer_id')];

            // 获取列表
            $list_account = AccountModel::getAccountList($where);
            $this->response(compact('list_account'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 检查参数
     * @throws \Exception
     */
    private function validateParamsForList()
    {
        $customer_id = I('get.customer_id', '');
        if (!$customer_id) {
            throw new \Exception('缺少必选参数customer_id');
        }
    }
}