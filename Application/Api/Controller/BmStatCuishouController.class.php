<?php

namespace Api\Controller;

use Api\Repositories\StatCuishouRepository;

class BmStatCuishouController extends BaseBmpController
{
    /*
     * repository
     * */
    private $repository_stat_cuishou;

    public function __construct()
    {
        parent::__construct();
        $this->repository_stat_cuishou = new StatCuishouRepository($this->biz);
    }

    /**
     * 催收统计列表
     */
    public function index()
    {
        try {
            // 列表统计数据
            $list_stat = $this->repository_stat_cuishou->getStatListForGet();
            $response = [
                'success' => true,
                'data' => $list_stat
            ];
            $this->ajaxResponse($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }

    /**
     * 详情页数据
     */
    public function detail()
    {
        try {
            // 统计详情
            $list_stat = $this->repository_stat_cuishou->getStatListForDetail();
            $response = [
                'success' => true,
                'data' => $list_stat
            ];
            $this->ajaxResponse($response);
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }
}
