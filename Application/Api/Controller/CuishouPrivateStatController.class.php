<?php

namespace Api\Controller;

use Api\Repositories\CuishouPrivateStatRepository;

class CuishouPrivateStatController extends BaseController
{
    protected $repository;

    /**
     * CustomerController constructor
     */
    public function __construct()
    {
        parent::__construct();
        $this->repository = new CuishouPrivateStatRepository();
    }

    /**
     * 统计列表
     */
    public function listStat()
    {
        try {
            $status = 0;
            $list_stat = $this->repository->listStat();
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 统计详情
     */
    public function detailStat()
    {
        try {
            $status = 0;
            $list_stat = $this->repository->detailStat();
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 统计排序
     */
    public function sortBy()
    {
        try {
            $list_stat = $this->repository->sortBy();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }


    /**
     * 客户列表
     */
    public function customerList()
    {
        try {
            // 全量客户列表
            $list_customer = $this->repository->customerList();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_customer'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 查看单个客户
     */
    public function customerShow()
    {
        try {
            // 全量客户列表
            $customer = $this->repository->customerShow();
            $status = 0;
            $this->ajaxResponse(compact('status', 'customer'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 账号列表
     */
    public function accountList()
    {
        try {
            // 全量账号列表
            $list_account = $this->repository->accountList();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_account'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 查看单个账号
     */
    public function accountShow()
    {
        try {
            // 全量账号列表
            $account = $this->repository->accountShow();
            $status = 0;
            $this->ajaxResponse(compact('status', 'account'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }
}