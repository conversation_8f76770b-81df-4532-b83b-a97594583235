<?php

namespace Api\Controller;

use Account\Model\CustomerModel;
use Api\Repositories\BackendCustomerRepository;
use Common\Common\ResponseTrait;

class BackendCustomerController
{
    use ResponseTrait;

    private $repository;

    /**
     * BackendCustomerController constructor.
     */
    public function __construct()
    {
        $this->repository = new BackendCustomerRepository();
    }

    /**
     * 一键更新
     */
    public function upgradeAtOnce()
    {
        try {
            $response = $this->repository->upgradeAtOnce();
            $this->response(compact('response'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取客户列表
     */
    public function getCustomerList()
    {
        try {
            $list = CustomerModel::getListCustomer([], ['customer_id', 'name']);
            $this->response(compact('list'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取开通了账单的客户列表
     */
    public function getBillCustomer()
    {
        try {
            $list = $this->repository->getBillCustomer();
            $this->response(compact('list'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取某个客户下的所有开通计费配置的产品
     */
    public function refreshProductList()
    {
        try {
            $list = $this->repository->refreshProductList();
            $this->response(compact('list'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取某个客户下的所有开通特定产品的账号列表
     */
    public function refreshAccountList()
    {
        try {
            $list = $this->repository->refreshAccountList();
            $this->response(compact('list'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

}