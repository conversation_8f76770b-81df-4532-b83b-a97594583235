<?php

namespace Api\Controller;

use Api\Repositories\BackendPreviewRepository;

class BackendPreviewController extends BaseController
{
    private $repository_preview;

    /**
     * BackendPreviewController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->repository_preview = new BackendPreviewRepository();
    }

    /**
     * 预览信息需要的url（授权协议, 基础信息）
     */
    public function previewUrl()
    {
        try {
            // 预览url
            $preview_url = $this->repository_preview->previewUrl();
            $this->response($preview_url);
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取默认协议
     */
    public function defaultProtocol()
    {
        try {
            // 默认协议
            $protocol_default = $this->repository_preview->defaultProtocol();
            $this->response(compact('protocol_default'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}