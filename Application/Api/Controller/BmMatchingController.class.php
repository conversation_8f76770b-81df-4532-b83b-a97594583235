<?php

namespace Api\Controller;

use Api\Repositories\BmMatchingRepository;

class BmMatchingController extends BaseBmpController
{
    // repository
    private $repository_matching;

    public function __construct()
    {
        parent::__construct();
        $this->repository_matching = new BmMatchingRepository($this->biz);
    }

    /**
     * 获取客户下辖催收分产品的列表
     */
    public function getProductList()
    {
        try {
            // 获取邦秒配产品列表
            $list_product = $this->repository_matching->getProductListOfLoginAccount();
            $response = [
                'success' => true,
                'data' => $list_product
            ];
            $this->ajaxResponse($response);
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }
}