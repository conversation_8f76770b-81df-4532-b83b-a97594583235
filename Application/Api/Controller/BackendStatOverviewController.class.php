<?php

namespace Api\Controller;

use Api\Repositories\BackendStatOverviewRepository;
use Common\Common\ResponseTrait;

class BackendStatOverviewController
{
    use ResponseTrait;

    private $repository;

    /**
     * BackendStatOverviewController constructor.
     * @param $repository
     */
    public function __construct()
    {
        $this->repository = new BackendStatOverviewRepository();
    }

    /**
     * 统计概览number格式的调用量
     */
    public function amountTable()
    {
        try {
            $list_stat = $this->repository->amountTable();
            $this->response(compact('list_stat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 统计概览各天的调用量
     */
    public function amountChart()
    {
        try {
            $chart = $this->repository->amountChart();
            $this->response(compact('chart'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public function amountPie()
    {
        try {
            $list_stat = $this->repository->amountPie();
            $this->response(compact('list_stat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    public function initTime()
    {
        try {
            $init_time = $this->repository->initTimeForChart();
            $this->response(compact('init_time'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
