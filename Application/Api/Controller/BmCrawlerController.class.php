<?php
/**
 * @Author: lidandan
 * @Date:   2018-06-22 10:31:22
 */
namespace Api\Controller;
use Api\Repositories\BmCrawlerRepository;

class BmCrawlerController extends BaseBmpController
{
    protected $bmCrawler;

    public function __construct()
    {
        parent::__construct();
        $this->bmCrawler = new BmCrawlerRepository($this->biz);
    }

    public function getProductList()
    {
        try {
            // 列表统计数据
            $list = $this->bmCrawler->getCrawlerAccountList();
            $response = [
                'success' => true,
                'data' => $list
            ];
            $this->ajaxResponse($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }
}