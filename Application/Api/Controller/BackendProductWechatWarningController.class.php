<?php

namespace Api\Controller;

use Api\Repositories\BackendProductWechatWarningRepository;
use Common\Common\ResponseTrait;

class BackendProductWechatWarningController
{
    use ResponseTrait;

    private $repository;

    /**
     * BackendProductWechatWarningController constructor.
     */
    public function __construct()
    {
        $this->repository = new BackendProductWechatWarningRepository();
    }

    /**
     * 产品微信列表
     */
    public function listShow()
    {
        try {
            $list_show = $this->repository->listShow();
            $this->response(compact('list_show'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 已经开通的产品列表
     */
    public function listProduct()
    {
        try {
            $list_product = $this->repository->listProduct();
            $this->response(compact('list_product'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 新建配置
     */
    public function store()
    {
        try {
            $this->repository->store();
            $this->response(['ok']);
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取没有开通某种类型微信配置产品列表
     */
    public function listProductWithoutOpen()
    {
        try {
            $list_products = $this->repository->listProductWithoutOpen();
            $this->response(compact('list_products'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取微信配置列表
     */
    public function listWechat()
    {
        try {
            $list_wechat = $this->repository->listWechat();
            $this->response(compact('list_wechat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 获取某个产品选中的wechat配置
     */
    public function wechatChoose()
    {
        try {
            $list_wechat = $this->repository->wechatChoose();
            $this->response(compact('list_wechat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 更新产品的微信配置
     */
    public function update()
    {
        try {
             $this->repository->update();
            $this->response(['success']);
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
