<?php

namespace Api\Controller;

use Api\Repositories\DataMigrateRepository;
use Common\Common\ResponseTrait;
use Common\Common\WechatBackendExceptionTrait;

class BackendMigrateController
{
    use ResponseTrait,WechatBackendExceptionTrait;

    private $repository;

    /**
     * DataMigrateController constructor.
     */
    public function __construct()
    {
        $this->repository = new DataMigrateRepository();
    }

    /**
     * migrate
     * @throws \Exception
     */
    public function repair()
    {
        try {
            set_time_limit(0);
            $this->repository->repair();
            $msg = 'Repair Successfully!';
            $this->response(compact('msg'));
        } catch (\Exception $e) {
            $this->wehcatException('Repair Error:' . $e->getMessage());
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * callback
     * @throws \Exception
     */
    public function rollback()
    {
        try {
            set_time_limit(0);
           // rollback
            $this->repository->rollback();

            $msg = '数据回退成功';
            $this->response(compact('msg'));
        } catch (\Exception $e) {
            $this->wehcatException('Repair Error:' . $e->getMessage());
            $this->setStatus(1478)->responseError($e->getMessage());
        }

    }
}