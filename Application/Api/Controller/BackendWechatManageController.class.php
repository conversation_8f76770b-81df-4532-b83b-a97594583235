<?php

namespace Api\Controller;

use Api\Repositories\BackendWechatManageRepository;
use Common\Common\ResponseTrait;

class BackendWechatManageController
{
    use ResponseTrait;

    private $repository;

    /**
     * BackendWechatManageController constructor.
     */
    public function __construct()
    {
        $this->repository = new BackendWechatManageRepository();
    }

    /**
     * 全量的wechat配置
     */
    public function listWechat()
    {
        try {
            $list_wechat = $this->repository->listWechat();
            $this->response(compact('list_wechat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 查找微信配置
     */
    public function searchWechat()
    {
        try {
            $list_wechat = $this->repository->searchWechat();
            $this->response(compact('list_wechat'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 新增微信配置
     */
    public function create()
    {
        try {
            $this->repository->create();
            $msg = '新建成功';
            $this->response(compact('msg'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    public function update()
    {
        try {
            $this->repository->update();
            $msg = '编辑成功';
            $this->response(compact('msg'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }

    }
}
