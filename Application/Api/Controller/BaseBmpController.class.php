<?php

namespace Api\Controller;

class BaseBmpController extends BaseController
{
    /*
     * 存储登录客户信息
     * */
    public $biz;

    public function __construct()
    {
        parent::__construct();

        // 检查客户参数
        $this->checkParams();

        // 初始化登陆客户信息
        $this->biz = $this->getAccountInfo();
    }

    /**
     * 获取客户的信息
     * @return array
     */
    protected function getAccountInfo()
    {
        return I('post.biz', '');
    }

    /**
     * 检查登录客户的参数
     */
    protected function checkParams()
    {
        $biz = I('post.biz', '');
        if (!$biz) {
            $this->ajaxResponse('缺少需要的客户信息');
        }
    }
}
