<?php

namespace Api\Controller;

use Api\Repositories\BackendHandleLogRepository;
use Common\Common\ResponseTrait;

class BackendHandleLogController
{
    use ResponseTrait;
    private $repository;

    /**
     * BackendHandleLogController constructor.
     * @param $repository
     */
    public function __construct( $repository)
    {
        $this->repository = new BackendHandleLogRepository();
    }

    public function listLog()
    {
        try {
            $list_log = $this->repository->listLog();
            $this->response(compact('list_log'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }

    }
}