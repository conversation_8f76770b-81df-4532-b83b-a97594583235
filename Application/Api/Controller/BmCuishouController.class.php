<?php

namespace Api\Controller;

use Api\Repositories\BmCuishouRepository;

class BmCuishouController extends BaseBmpController
{
    // repository
    private $repository_bm_cuishou;

    public function __construct()
    {
        parent::__construct();
        $this->repository_bm_cuishou = new BmCuishouRepository($this->biz);
    }

    /**
     * 获取客户下辖催收分产品的列表
     */
    public function getProductList()
    {
        try {

            // 获取催收分产品列表
            $list_product = $this->repository_bm_cuishou->getProductListOfLoginAccount();
            $response = [
                'success' => true,
                'data' => $list_product
            ];
            $this->ajaxResponse($response);
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'data' => $e->getMessage()
            ];
            $this->ajaxResponse($response);
        }
    }
}