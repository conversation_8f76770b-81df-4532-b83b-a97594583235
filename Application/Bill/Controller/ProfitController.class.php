<?php
/**
 * Created by PhpStorm.
 * User: gang8
 * Date: 2020/5/25
 * Time: 11:20
 */

namespace Bill\Controller;

use Bill\Repository\ProfitRepository;
use Common\Controller\AdminController;

//权责收入统计
class ProfitController extends AdminController
{
	protected $repository;
	
	public function region()
	{
		if (IS_POST) {
			try {
				$data   = $this->repository()
							   ->getRegionInfo();
				$status = 0;
				$msg    = '';
				$this->ajaxReturn(compact('status', 'msg', 'data'));
			} catch (\Exception $exception) {
				$data   = [];
				$msg    = $exception->getMessage();
				$status = 1;
				$this->ajaxReturn(compact('status', 'msg', 'data'));
			}
		}
		
		$this->display();
	}
	
	public function product()
	{
		if (IS_POST) {
			if ('linkage_get_username' == I('post.type')) {
				//二级联动拉取用户
				$data = $this->repository()
							 ->getLinkageUsernameInfo();
				$this->ajaxReturn(compact('data'));
			} else if ('linkage_get_customer' == I('post.type')) {
				//二级联动拉取客户
				$data = $this->repository()
							 ->getLinkageCustomerInfo();
				$this->ajaxReturn(compact('data'));
			} else {
				try {
					$data   = $this->repository()
								   ->getProductInfo();
					$status = 0;
					$msg    = '';
					$this->ajaxReturn(compact('status', 'msg', 'data'));
				} catch (\Exception $exception) {
					$data   = [];
					$msg    = $exception->getMessage();
					$status = 1;
					$this->ajaxReturn(compact('status', 'msg', 'data'));
				}
			}
		}
		$data = $this->repository()
					 ->getProductHtmlInfo();
		$this->assign($data);
		$this->display();
	}
	
	public function customer()
	{
		if (IS_POST) {
			if ('linkage_get_username' == I('post.type')) {
				//二级联动拉取用户
				$data = $this->repository()
							 ->getLinkageUsernameInfo();
				$this->ajaxReturn(compact('data'));
			} else if ('linkage_get_customer' == I('post.type')) {
				//二级联动拉取客户
				$data = $this->repository()
							 ->getLinkageCustomerInfo();
				$this->ajaxReturn(compact('data'));
			} else {
				try {
					$data   = $this->repository()
								   ->getCustomerInfo();
					$status = 0;
					$msg    = '';
					$this->ajaxReturn(compact('status', 'msg', 'data'));
				} catch (\Exception $exception) {
					$data   = [];
					$msg    = $exception->getMessage();
					$status = 1;
					$this->ajaxReturn(compact('status', 'msg', 'data'));
				}
			}
		}
		$data = $this->repository()
					 ->getCustomerHtmlInfo();
		$this->assign($data);
		$this->display();
	}
	
	/**
	 * 特殊费用明细
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/6/11 14:27
	 *
	 * @return void
	 **/
	public function special()
	{
		if (IS_POST) {
			if ('linkage_get_username' == I('post.type')) {
				//二级联动拉取用户
				$data = $this->repository()
							 ->getLinkageUsernameInfo();
				$this->ajaxReturn(compact('data'));
			} else if ('linkage_get_customer' == I('post.type')) {
				//二级联动拉取客户
				$data = $this->repository()
							 ->getLinkageCustomerInfo();
				$this->ajaxReturn(compact('data'));
			} else {
				try {
					$data   = $this->repository()
								   ->getCustomerSpecialInfo();
					$status = 0;
					$msg    = '';
					$this->ajaxReturn(compact('status', 'msg', 'data'));
				} catch (\Exception $exception) {
					$data   = [];
					$msg    = $exception->getMessage();
					$status = 1;
					$this->ajaxReturn(compact('status', 'msg', 'data'));
				}
			}
		}
		$data = $this->repository()
					 ->getCustomerHtmlInfo();
		$this->assign($data);
		$this->display();
	}
	
	/**
	 * 获取代码仓库对象
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 11:21
	 *
	 * @return ProfitRepository
	 **/
	protected function repository()
	{
		if (is_null($this->repository)) {
			$this->repository = new ProfitRepository();
		}
		
		return $this->repository;
	}
}