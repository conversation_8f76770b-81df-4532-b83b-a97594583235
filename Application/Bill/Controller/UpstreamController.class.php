<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/22 0022
 * Time: 15:33
 */

namespace Bill\Controller;


use Common\Controller\AdminController;
use Stat\Repositories\UpstreamRepository;

//数据源调用量统计
class UpstreamController extends AdminController
{

    public function index()
    {
        $repository = new UpstreamRepository();

        $data = $repository->getBillListHtmlInfo();

        $this->assign('data', $data);

        $this->display();
    }

    public function get_index_data()
    {
        $repository = new UpstreamRepository();

        try {
            $data   = $repository->getBillListInfo();
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }

    public function file()
    {
        $repository = new UpstreamRepository();

        $repository->downloadExcelForBillListInfo();
    }
}