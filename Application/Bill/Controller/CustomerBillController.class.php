<?php

namespace Bill\Controller;

use Account\Model\CustomerGroupModel;
use Account\Model\CustomerModel;
use Common\Controller\AdminController;
use Common\Model\CommonEnumModel;

/**
 * Class CustomerBillController 客户对账单
 * @package Bill\Controller
 */
class CustomerBillController extends AdminController
{
	/**
	 * 客户对账单列表
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/11/3 10:35
	 *
	 * @return void
	 */
	public function index()
	{
		//接口地址
		$url = C('BACK_API_DOMAIN') . '/bill/income/customerStatementList';
		
		$customer_list    = array_map(function ($item) {
			$item['name'] = $item['name'];
			
			return $item;
		}, CustomerModel::getListCustomer(['is_delete' => 0], ['customer_id', 'name']));

		$customer_options = makeOption(array_column($customer_list, 'name', 'customer_id'));

		$source_options = makeOption((new CommonEnumModel())->getEnumPairs(1));

        $productData = (new CustomerGroupModel())->getGroupListByWhere([]);
        $productData = array_column($productData, 'group_name', 'group_id');
		$group_options = makeOption($productData);

		$this->assign('url', $url);
		$this->assign('customer_options', $customer_options);
		$this->assign('source_options', $source_options);
		$this->assign('group_options', $group_options);

		$this->display('');
	}
	
	/**
	 * protected
	 *
	 * @access   客户对账单明细
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:00
	 *
	 * @return void
	 */
	public function details()
	{
		//接口地址
		$url = C('BACK_API_DOMAIN') . '/bill/income/customerStatementDetails';
		
		//发送邮件地址
		$email_url = C('BACK_API_DOMAIN') . '/bill/income/sendStatement';
		
		//获取客户ID
		$customer_id = I('GET.customer_id');
		$source = I('GET.source');
		if (empty($customer_id)) {
			$this->redirect('index');
		}
		
		$this->assign('customer_id', $customer_id);
		$this->assign('source', $source);
		$this->assign('url', $url);
		$this->assign('email_url', $email_url);
		$this->display('');
	}
}