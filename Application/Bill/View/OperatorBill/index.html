<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner{
        width: 256px;
    }
    .el-dialog{
        height: 550px;
        overflow-y: scroll;
    }
</style>

<div id="app">
    <div class="container" id="cuishou_list_app">
        <div class="panel panel-default">
            <div class="panel-body">

                <el-form :inline="true" :model="searchForm" style="height:42px;" label-width="100px" class="demo-form-inline">

                    <el-form-item label="账单月份">
                        <el-date-picker
                                v-model="searchForm.month"
                                type="month"
                                value-format="yyyyMM"
                                placeholder="选择月">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="运营商">
                        <el-select v-model="searchForm.channel_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in operatorList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" filterable clearable placeholder="请选择">
                            <el-option label="未对账" value="0"></el-option>
                            <el-option label="待确认" value="1"></el-option>
                            <el-option label="已确认" value="2"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="searchTableData()">查询</el-button>
                    </el-form-item>

                </el-form>

            </div>
        </div>
    </div>

    <div class="container">

        <div id="app_body">

            <template>
                <el-table
                        :data="tableData"
                        border
                        :header-cell-style="setTableHeader"
                        :span-method="objectSpanMethod"
                        :height="tableHeight"
                        style="width: 100%">
                    <el-table-column label="基础信息" align="center">
                        <el-table-column
                                prop="month"
                                label="月份"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="operator"
                                label="运营商"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="bill_method"
                                label="计费方式"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="operator_minimum_fee"
                                label="保底费用"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="operator_cost"
                                label="最终成本"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="interface_label"
                                label="金融接口"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="operator_pname"
                                label="运营商接口"
                                width="120"
                        >
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="运营商" align="center">
                        <el-table-column
                                prop="operator_product_count"
                                label="调用量"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="operator_product_cost"
                                label="消耗成本"
                        >
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="金融部" align="center">
                        <el-table-column
                                prop="total"
                                label="总量"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="success"
                                label="成功量"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="valid"
                                label="查得量"
                        >
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="差异" align="center">
                        <el-table-column
                                prop="diff_number"
                                label="差异条数"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="diff_money"
                                label="差异金额"
                        >
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="" align="center">
                        <el-table-column label="操作" width="120" align="center">
                            <template slot-scope="scope">

                                <el-tag
                                        v-if="scope.row.status==0"
                                        type="danger"
                                        effect="dark">未对账</el-tag>
                                <el-tag
                                        v-else-if="scope.row.status==1"
                                        effect="dark">已确认</el-tag>
                                <el-button
                                        v-else
                                        size="mini"
                                        type="success"
                                        @click="confirmOperatorBill(scope.$index, scope.row.month, scope.row.channel_id)">待确认</el-button>

                            </template>
                        </el-table-column>
                    </el-table-column>

                </el-table>
            </template>


        </div>

        <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40, 50]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalNum">
            </el-pagination>
        </div>

    </div>

</div>

<script type="application/javascript">

    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/operator/BillOperatorList";

    var vm = new Vue({
        el:'#app',
        data:{
            tableHeight:600,
            tableData: [],
            totalNum: 0,
            pageSize: 10,
            currentPage:1,
            fatherProductList: [],
            productList: [],
            customerList:[],
            operatorList:[],
            pickerOptions: {},
            getUrl: url,
            dialogFormVisible: false,
            dialog_title:'添加数据',
            operate:false,
            form: {
                id:'',
                title: '',
                customer_id: '',
                father_id: '',
                product_id: '',
                operator: '',
                date: '',
                fee_number: '',
                money: '',
                remark: ''
            },
            formLabelWidth: '120px',
            searchForm: {
                month: '',
                status:'',
                channel_id:''
            }

        },
        created: function(){
            this.getTableData();
            //this.getFatherProductSelectData();
            //this.getCustomerSelectData();
            this.getOperatorSelectData();
        },
        methods:{
            getTableData:function(){
                var self = this;
                var month = this.searchForm.month;
                var channel_id = this.searchForm.channel_id;
                var status = this.searchForm.status;
                var where = {limit:this.pageSize, page:this.currentPage};

                if(month){
                    where.month = month;
                }
                if(status != ''){
                    where.status = status;
                }
                if(channel_id){
                    where.channel_id = channel_id;
                }
                axios.post(url, where).then(function (response) {
                    //console.log(response);
                    self.tableData = response.data.data.list;
                    self.totalNum = response.data.data.count;
                }).catch(function (error) {
                    console.log(error);
                });

            },
            // 更改表头样式
            setTableHeader:function({ row, column, rowIndex, columnIndex }) {
                if (rowIndex == 0 && columnIndex >= 0 && columnIndex<=0) {
                    return 'background-color:#2098ff;color:white;';
                }
                if (rowIndex == 1 && columnIndex >= 0 && columnIndex<=6) {
                    return 'background-color:#2098ff;color:white;';
                }

                if (rowIndex == 0 && columnIndex >= 1 && columnIndex<=1) {
                    return 'background-color:#f7a830;color:white;';
                }
                if (rowIndex == 1 && columnIndex >= 7 && columnIndex<=8) {
                    return 'background-color:#f7a830;color:white;';
                }

                if (rowIndex == 0 && columnIndex >=2 && columnIndex<=2) {
                    return 'background-color:#53a729;color:white;';
                }
                if (rowIndex == 1 && columnIndex >=9 && columnIndex<=11) {
                    return 'background-color:#53a729;color:white;';
                }

                if (rowIndex == 0 && columnIndex >=3 && columnIndex<=3) {
                    return 'background-color:#ff3c3c;color:white;';
                }
                if (rowIndex == 1 && columnIndex >=12 && columnIndex<=13) {
                    return 'background-color:#ff3c3c;color:white;';
                }

                if (rowIndex == 0 && columnIndex >=4 && columnIndex<=4) {
                    return 'background-color:#9300ffeb;color:white;';
                }
                if (rowIndex == 1 && columnIndex >=14 && columnIndex<=14) {
                    return 'background-color:#9300ffeb;color:white;';
                }

            },
            objectSpanMethod:function ({ row, column, rowIndex, columnIndex }){
                if (columnIndex == 0) {
                    return [row.month_merge_count, 1];
                }
                if (columnIndex == 1) {
                    return [row.operator_merge_count, 1];
                }
                if (columnIndex == 2) {
                    return [row.bill_method_merge_count, 1];
                }
                if (columnIndex == 3) {
                    return [row.operator_minimum_fee_merge_count, 1];
                }
                //最终成本
                if (columnIndex == 4) {
                    return [row.operator_cost_merge_count, 1];
                }
                //运营商接口
                if (columnIndex == 6) {
                    return [row.operator_pname_merge_count, 1];
                }
                //运营商/调用量
                if (columnIndex == 7) {
                    return [row.operator_product_count_merge_count, 1];
                }
                //运营商/消耗成本
                if (columnIndex == 8) {
                    return [row.operator_product_cost_merge_count, 1];
                }
                //差异/差异条数
                if (columnIndex == 12) {
                    return [row.diff_number_merge_count, 1];
                }
                //差异/差异金额
                if (columnIndex == 13) {
                    return [row.diff_money_merge_count, 1];
                }
                //操作
                if (columnIndex == 14) {
                    return [row.operate_merge_count, 1];
                }

                return [1, 1]; //不合并
            },
            getFatherProductSelectData:function(){
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/mainProduct";
                axios.get(url, {
                    params:{options:false}
                }).then(function (response) {
                    //console.log(response.data.data);
                    Object.getOwnPropertyNames(response.data.data).forEach(function(key){
                        var name = response.data.data[key];
                        self.fatherProductList.push({value:key,label:name});
                    });

                }).catch(function (error) {
                    console.log(error);
                });

            },
            getCustomerSelectData:function(){
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                axios.get(url, {
                    params:{customer:true}
                }).then(function (response) {

                    Object.getOwnPropertyNames(response.data.data.customer).forEach(function(key){
                        var name = response.data.data.customer[key];
                        self.customerList.push({value:key,label:name});
                    });
                    self.customerList.push({value:'all',label:'全部'});

                }).catch(function (error) {
                    console.log(error);
                });

            },
            getChildrenProduct:function(){
                this.productList = [];
                var father_id = this.searchForm.father_id;
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/childrenProductList";
                axios.get(url, {
                    params:{father_id:father_id}
                }).then(function (response) {
                    //console.log(response.data.data);
                    Object.getOwnPropertyNames(response.data.data).forEach(function(key){
                        var name = response.data.data[key];
                        self.productList.push({value:key,label:name});
                    });

                }).catch(function (error) {
                    console.log(error);
                });

            },
            getFormChildrenProduct:function(){
                this.productList = [];
                var father_id = this.form.father_id;
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/childrenProductList";
                axios.get(url, {
                    params:{father_id:father_id}
                }).then(function (response) {
                    //console.log(response.data.data);
                    Object.getOwnPropertyNames(response.data.data).forEach(function(key){
                        var name = response.data.data[key];
                        self.productList.push({value:key,label:name});
                    });

                }).catch(function (error) {
                    console.log(error);
                });

            },
            getOperatorSelectData:function(){
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                axios.get(url, {
                    params:{channel:true}
                }).then(function (response) {

                    Object.getOwnPropertyNames(response.data.data.channel).forEach(function(key){
                        var name = response.data.data.channel[key];
                        self.operatorList.push({value:key,label:name});
                    });

                }).catch(function (error) {
                    console.log(error);
                });

            },
            onSubmit:function(formName){

                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        var user_cookie = getCookie('PHPSESSID');
                        this.form.user_cookie = user_cookie;
                        var request_params = this.form;
                        var self = this;
                        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/customerBillAdjust/save";

                        axios.post(url, request_params).then(function (response) {
                            if(response.data.code == 0){
                                successMsg(response.data.msg);

                            }else{
                                errorMsg(response.data.msg);
                            }
                        }).catch(function (error) {
                            console.log(error);
                            errorMsg(error);
                        });

                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });

            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.pageSize = val;
                this.currentPage = 1;
                this.getTableData();
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.currentPage = val;
                this.getTableData();
            },
            searchTableData:function (){
                this.currentPage = 1;
                this.getTableData();
            },
            addTableData:function(){
                this.dialog_title = '添加数据';
                this.operate = false;
                this.resetForm();

                this.dialogFormVisible = true;

            },
            closeDialogCallBack:function(formName){
                this.$refs[formName].resetFields();
            },
            handleEdit(index, id) {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/customerBillAdjust/info";
                axios.get(url, {
                    params:{id:id}
                }).then(function (response) {

                    if(response.data.code == 0){
                        //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                        self.form.id = response.data.data.id;
                        self.form.title = response.data.data.title;
                        self.form.customer_id = response.data.data.customer_id;
                        self.form.father_id = response.data.data.father_id;
                        self.getFormChildrenProduct();
                        self.form.product_id = response.data.data.product_id;
                        self.form.operator = response.data.data.operator;
                        self.form.date = response.data.data.date;
                        self.form.fee_number = response.data.data.fee_number;
                        self.form.money = response.data.data.money;
                        self.form.remark = response.data.data.remark;

                        self.dialog_title = '编辑数据';
                        self.operate = true;
                        self.dialogFormVisible = true;

                    }else{
                        errorMsg(response.data.msg);
                    }

                }).catch(function (error) {
                    console.log(error);
                });

            },
            confirmOperatorBill(index, month, channel_id) {
                let self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/operator/ConfirmOperatorBill";
                this.$confirm('请确认,是否执行该操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var user_cookie = getCookie('PHPSESSID');
                    var where = {month:month,channel_id:channel_id,user_cookie:user_cookie};

                    axios.post(url, where).then(function (response) {
                        if (response.data.status === 0) {
                            successMsg(response.data.msg);
                            self.getTableData();
                        } else {
                            errorMsg(response.data.msg);
                        }
                    }).catch(function (error) {
                        console.log(error);
                        errorMsg(error);
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消该操作'
                    });
                });

            },
            resetForm:function(){
                this.form.id = '';
                this.form.title = '';
                this.form.customer_id = '';
                this.form.father_id = '';
                this.form.product_id = '';
                this.form.operator = '';
                this.form.date = '';
                this.form.fee_number = '';
                this.form.money = '';
                this.form.remark = '';
                this.productList = [];
            }

        }

    })

    function successMsg(msg){
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
        vm.getTableData();
        vm.resetForm();
        vm.dialogFormVisible = false;
    }
    function errorMsg(msg){
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }
    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return (arr[2]);
        else
            return null;
    }

</script>
</body>
</html>
