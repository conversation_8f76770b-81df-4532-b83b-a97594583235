<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom: 15px;
        }

        .index-btn {
            margin: 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="/Bill/Upstream/file" method="post" class="form-inline">
                <div class="form-group">
                    <label class="control-label" for="start_date">时间区间：</label>
                    <input type="date" name="start_date" id="start_date" class="form-control"
                           value="{$start_date}"/>
                    -
                    <input type="date" name="end_date" id="end_date" class="form-control"
                           value="{$end_date}"/>
                </div>
                <div class="form-group">
                    <label for="dept_id">选择大区：</label>
                    <select name="dept_id" id="dept_id" class="form-control">
                        <option value="">--全部--</option>
                        {$businessDeptOption}
                    </select>
                </div>

                <div class="form-group">
                    <label for="username">商务人员：</label>
                    <select name="username" id="username" class="form-control">
                        <option value="">--全部--</option>
                        {$usernameOption}
                    </select>
                </div>

                <div class="form-group">
                    <label for="customer_id">客户：</label>
                    <select name="customer_id" id="customer_id" class="form-control">
                        <option value="">--全部--</option>
                        {$customerOption}
                    </select>
                </div>

                <div class="form-group">
                    <label for="product_id">选择产品：</label>
                    <select name="product_id" id="product_id" class="form-control selectpicker" multiple
                            data-live-search="true" data-actions-box="true">
                        {$productOption}
                    </select>
                </div>

                <div class="form-group">
                    <input type="button" id="submit_btn" class="btn btn-primary btn-sm" value="查询"
                           onclick="refreshData()">
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="table">
            <thead>
            <th>产品</th>
            <th>子产品</th>
            <th onclick="sort($(this), 'income_number')">
                <span class="v-table-sort-icon">
                    <i class="v-icon-up-dir" data-field="income_number" data-sort="asc"></i>
                    <i class="v-icon-down-dir" data-field="income_number" data-sort="desc"></i>
                </span>
                收入计费用量
            </th>
            <th onclick="sort($(this), 'income_money')">
                <span class="v-table-sort-icon">
                    <i class="v-icon-up-dir" data-field="income_money" data-sort="asc"></i>
                    <i class="v-icon-down-dir checked" data-field="income_money" data-sort="desc"></i>
                </span>
                权责收入
            </th>
            <th onclick="sort($(this), 'cost_money')">
                <span class="v-table-sort-icon">
                    <i class="v-icon-up-dir" data-field="cost_money" data-sort="asc"></i>
                    <i class="v-icon-down-dir" data-field="cost_money" data-sort="desc"></i>
                </span>
                权责成本
            </th>
            <th onclick="sort($(this), 'profit_money')">
                <span class="v-table-sort-icon">
                    <i class="v-icon-up-dir" data-field="profit_money" data-sort="asc"></i>
                    <i class="v-icon-down-dir" data-field="profit_money" data-sort="desc"></i>
                </span>
                权责毛利
            </th>
            <th>操作</th>
            </thead>
            <tbody id="tableBody">

            </tbody>
        </table>
    </div>
</div>
</body>
</html>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.4/layui/layui.all.js"></script>
<script type="text/javascript">
    $(function () {
        $("#product_id").selectpicker({
            width: '220px'
        });
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width: '300px'
        });
        $("#dept_id").change(function () {
            $.ajax({
                url: './product.html',
                type: 'post',
                data: {
                    dept_id: $("#dept_id").val(),
                    type: 'linkage_get_username'
                },
                success: function (response) {
                    $("#username").html('<option value="">--全部--</option>' + response.data.usernameOption);
                    $("#customer_id").html('<option value="">--全部--</option>' + response.data.customerOption);
                    $("#customer_id").selectpicker('refresh');
                }
            });
        });
        $("#username").change(function () {
            $.ajax({
                url: './product.html',
                type: 'post',
                data: {
                    username: $("#username").val(),
                    dept_id: $("#dept_id").val(),
                    type: 'linkage_get_customer'
                },
                success: function (response) {
                    $("#customer_id").html('<option value="">--全部--</option>' + response.data.customerOption);
                    $("#customer_id").selectpicker('refresh');
                }
            });
        });


//        $("#file_export").click(function () {
//            var conf = confirm('文件导出将导出已查询出的全部数据（不分页），是否继续？');
//            if (conf) {
//                window.open("{:U('Account/Account/export')}" + location.search);
//            }
//        });
        refreshData();
    });
    function refreshData() {
        layui.layer.load(0, {shade: [0.3, '#393D49']});
        $.ajax({
            url: './product.html',
            type: 'post',
            data: {
                start_date: $("#start_date").val(),
                end_date: $("#end_date").val(),
                dept_id: $("#dept_id").val(),
                username: $("#username").val(),
                customer_id: $("#customer_id").val(),
                product_id: $("#product_id").val(),
                sort_field: $(".v-table-sort-icon").find('.checked').attr('data-field'),
                sort_type: $(".v-table-sort-icon").find('.checked').attr('data-sort'),
            },
            success: function (res) {
                if (Number(res.status) === 0) {
                    refreshTable(res.data);
                } else {
                    alert(res.msg);
                    layui.layer.closeAll();
                }
            }
        });
    }

    function refreshTable(data) {
        let tableBody = '';

        let total = data.total;
        let result = data.result;
        let operation;
        let start_date = $("#start_date").val();
        let end_date = $("#end_date").val();

        tableBody += `<tr>
<td colspan="2">合计</td>
<td align="right">${total.income_number}</td>
<td align="right">${total.income_money}</td>
<td align="right">${total.cost_money}</td>
<td align="right">${total.profit_money}</td>
<td>
<a class="btn index-btn btn-info btn-sm" href="./customer.html?start_date=${start_date}&end_date=${end_date}">明细</a>
</td>
</tr>`;

        tableBody += `<tr>
<td colspan="2">特殊消耗/赠送（消耗-赠送）</td>
<td align="right"></td>
<td align="right">${data.specialTotal}</td>
<td align="right"></td>
<td align="right"></td>
<td>
    <a class="btn index-btn btn-info btn-sm" href="./special.html?start_date=${start_date}&end_date=${end_date}">明细</a>
</td>
</tr>`;

        result.forEach((item) => {
            if (typeof item.children == 'undefined') {
                tableBody += `<tr>
<td>${item.product_name}</td>
<td>--</td>
<td align="right">${item.income_number}</td>
<td align="right">${item.income_money}</td>
<td align="right">${item.cost_money}</td>
<td align="right">${item.profit_money}</td>
<td><a class="btn index-btn btn-info btn-sm" href="./customer.html?product_id=${item.product_id}&start_date=${start_date}&end_date=${end_date}">明细</a></td>
</tr>`;
            } else {
                let rows = item.children.length + 1;
                tableBody += `<tr>
<td rowspan="${rows}">${item.product_name}</td>
<td>总计</td>
<td align="right">${item.income_number}</td>
<td align="right">${item.income_money}</td>
<td align="right">${item.cost_money}</td>
<td align="right">${item.profit_money}</td>
<td><a class="btn index-btn btn-info btn-sm" href="./customer.html?product_id=${item.product_id}&start_date=${start_date}&end_date=${end_date}">明细</a></td>
</tr>`;
                console.log(item.children);
                item.children.forEach((childrenItem) => {
                    tableBody += `<tr>
<td>${childrenItem.product_name}</td>
<td align="right">${childrenItem.income_number}</td>
<td align="right">${childrenItem.income_money}</td>
<td align="right">${childrenItem.cost_money}</td>
<td align="right">${childrenItem.profit_money}</td>
<td><a class="btn index-btn btn-info btn-sm" href="./customer.html?product_id=${childrenItem.product_id}&start_date=${start_date}&end_date=${end_date}">明细</a></td>
</tr>`;
                });
            }
        });

        $("#tableBody").html(tableBody);
        layui.layer.closeAll();
    }

    function sort(element, field) {
        let sort_field = $(".v-table-sort-icon").find('.checked').attr('data-field');
        let index = 1;

        if (sort_field == field) {
            let sort_type = element.find('.checked').attr('data-sort');
            index = (sort_type == 'desc' ? 0 : 1);
        }
        $(".v-table-sort-icon").find('i').removeClass('checked');
        element.find('i').eq(index).addClass('checked');
        refreshData();
    }
</script>