<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom: 15px;
        }

        .index-btn {
            margin: 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="/Bill/Upstream/file" method="post" class="form-inline">
                <div class="form-group">
                    <label class="control-label" for="start_date">时间区间：</label>
                    <input type="date" name="start_date" id="start_date" class="form-control"
                           value="{$start_date}"/>
                    -
                    <input type="date" name="end_date" id="end_date" class="form-control"
                           value="{$end_date}"/>
                </div>
                <div class="form-group">
                    <label for="dept_id">选择大区：</label>
                    <select name="dept_id" id="dept_id" class="form-control">
                        <option value="">--全部--</option>
                        {$businessDeptOption}
                    </select>
                </div>

                <div class="form-group">
                    <label for="username">商务人员：</label>
                    <select name="username" id="username" class="form-control">
                        <option value="">--全部--</option>
                        {$usernameOption}
                    </select>
                </div>

                <div class="form-group">
                    <label for="customer_id">客户：</label>
                    <select name="customer_id" id="customer_id" class="form-control">
                        <option value="">--全部--</option>
                        {$customerOption}
                    </select>
                </div>

                <div class="form-group">
                    <input type="button" id="submit_btn" class="btn btn-primary btn-sm" value="查询"
                           onclick="refreshData()">
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="table">
            <thead>
            <th>客户ID</th>
            <th>客户名称</th>
            <th>月份</th>
            <th>类型</th>
            <th>金额</th>
            </thead>
            <tbody id="tableBody">

            </tbody>
        </table>
    </div>
</div>
</body>
</html>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.4/layui/layui.all.js"></script>
<script type="text/javascript">
    $(function () {
        $("#product_id").selectpicker({
            width: '220px'
        });
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width: '300px'
        });
        $("#dept_id").change(function () {
            $.ajax({
                url: './special.html',
                type: 'post',
                data: {
                    dept_id: $("#dept_id").val(),
                    type: 'linkage_get_username'
                },
                success: function (response) {
                    $("#username").html('<option value="">--全部--</option>' + response.data.usernameOption);
                    $("#customer_id").html('<option value="">--全部--</option>' + response.data.customerOption);
                    $("#customer_id").selectpicker('refresh');
                }
            });
        });
        $("#username").change(function () {
            $.ajax({
                url: './special.html',
                type: 'post',
                data: {
                    username: $("#username").val(),
                    dept_id: $("#dept_id").val(),
                    type: 'linkage_get_customer'
                },
                success: function (response) {
                    $("#customer_id").html('<option value="">--全部--</option>' + response.data.customerOption);
                    $("#customer_id").selectpicker('refresh');
                }
            });
        });


//        $("#file_export").click(function () {
//            var conf = confirm('文件导出将导出已查询出的全部数据（不分页），是否继续？');
//            if (conf) {
//                window.open("{:U('Account/Account/export')}" + location.search);
//            }
//        });
        refreshData();
    });
    function refreshData() {
        layui.layer.load(0, {shade: [0.3, '#393D49']});
        $.ajax({
            url: './special.html',
            type: 'post',
            data: {
                start_date: $("#start_date").val(),
                end_date: $("#end_date").val(),
                dept_id: $("#dept_id").val(),
                username: $("#username").val(),
                customer_id: $("#customer_id").val(),
                product_id: $("#product_id").val(),
                sort_field: $(".v-table-sort-icon").find('.checked').attr('data-field'),
                sort_type: $(".v-table-sort-icon").find('.checked').attr('data-sort'),
            },
            success: function (res) {
                if (Number(res.status) === 0) {
                    refreshTable(res.data);
                } else {
                    alert(res.msg);
                    layui.layer.closeAll();
                }
            }
        });
    }

    function refreshTable(data) {
        let tableBody = '';
        let operation;
        let start_date = $("#start_date").val();
        let end_date = $("#end_date").val();
        let type_str = '';

        data.forEach((item) => {
            type_str = item.type == 1 ? `<span style="color:red;">特殊充值</span>` : `<span style="color:green;">特殊消耗</span>`;

            tableBody += `<tr>
<td>${item.customer_id}</td>
<td>${item.name}</td>
<td>${item.start_date}</td>
<td>${type_str}</td>
<td align="right">${item.money}</td>
</tr>`;

        });

        $("#tableBody").html(tableBody);
        layui.layer.closeAll();
    }

    function sort(element, field) {
        let sort_field = $(".v-table-sort-icon").find('.checked').attr('data-field');
        let index = 1;

        if (sort_field == field) {
            let sort_type = element.find('.checked').attr('data-sort');
            index = (sort_type == 'desc' ? 0 : 1);
        }
        $(".v-table-sort-icon").find('i').removeClass('checked');
        element.find('i').eq(index).addClass('checked');
        refreshData();
    }
</script>