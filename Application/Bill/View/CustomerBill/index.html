<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客户对账单（V2）列表页</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <link rel="stylesheet" href="__STATICS__js/layui-plugin/cascader/cascader.css">
    <style>

        .popper .el-cascader-panel .el-radio {
            width: 100%;
            height: 100%;
            z-index: 10;
            position: absolute;
            top: 0;
            right: 0;
        }
        .popper .el-cascader-panel .el-checkbox {
            width: 100%;
            height: 100%;
            z-index: 10;
            position: absolute;
            top: 0;
            right: 0;
        }
        .popper .el-cascader-panel .el-radio__input {
            margin-top: 10px;
            margin-left: 8px;
        }
        .popper .el-cascader-panel .el-checkbox__input {
            margin-top: 2px;
            margin-left: 8px;
        }
        .popper .el-cascader-panel .el-cascader-node__postfix {
            top: 10px;
        }

        #area {
            border: none;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form" method="POST">
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">日期</label>
            <div class="layui-input-block">
                <input type="text" name="month_section" placeholder="请选择月份区间" autocomplete="off" class="layui-input" id="month_section">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">选择主体</label>
            <div class="layui-input-block">
                <select name="group_id" lay-search id="group_id">
                    <option value="">--全部--</option>
                    {$group_options}
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">选择客户</label>
            <div class="layui-input-block">
                <select name="customer_id" lay-search id="customer_id">
                    <option value="">--全部--</option>
                    {$customer_options}
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">消费</label>
            <div class="layui-input-inline" style="width:30%;">
                <input type="text" name="min_consume" id="min_consume" placeholder="￥" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid">-</div>
            <div class="layui-input-inline" style="width:30%;">
                <input type="text" name="max_consume" id="max_consume" placeholder="￥" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">余额</label>
            <div class="layui-input-inline" style="width:30%;">
                <input type="text" name="min_balance" id="min_balance" placeholder="￥" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid">-</div>
            <div class="layui-input-inline" style="width:30%;">
                <input type="text" name="max_balance" id="max_balance" placeholder="￥" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">邮件类型</label>
            <div class="layui-input-block">
                <select name="email_type" lay-search id="email_type">
                    <option value="">--全部--</option>
                    <option value="1">标准</option>
                    <option value="2">非标准</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">客户状态</label>
            <div class="layui-input-block">
                <select name="status" lay-search id="status">
                    <option value="">--全部--</option>
                    <option value="1">可用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
        </div>
        <!--<div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">-->
        <!--    <label class="layui-form-label">区域</label>-->
        <!--    <div class="layui-input-block">-->
        <!--        <select name="area" lay-search id="area">-->
        <!--        </select>-->
        <!--    </div>-->
        <!--</div>-->
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">区域</label>
            <div class="layui-input-block">
                <input id="area" name="area"/>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label">来源</label>
            <div class="layui-input-block">
                <select name="source" lay-search id="source">
                    {$source_options}
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
            <div class="layui-btn" id="bulk_send" data-type="getCheckData">
                批量发送
            </div>
            <input type="hidden" name="export_statement" value="0" />

            <div style="margin-left:10px" class="layui-btn layui-btn-normal" lay-submit id="export_statement_btn"  lay-filter="exp_stat">
                导出结算单
            </div>
            <div style="margin-left:10px" class="layui-btn layui-btn-normal" lay-submit id="export_qifu" lay-filter="export_qifu">
                导出列表(区分企服)
            </div>
            
        </div>
    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="list_table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__js/layui-plugin/cascader/cascader.js"></script>
<script type="application/javascript" src="__STATICS__bignumber.js-master/bignumber.min.js"></script>
<script type="application/javascript" src="__STATICS__jquery-dateFormat-master/dist/jquery-dateformat.min.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
<script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
<script type="application/javascript">
    window.cache = {
        list_url         : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/income/customerStatementList",
        list_order_field : "consume",
        list_order_type  : "desc",
        list_where       : {
            month_section : "{:date('Y-m', strtotime('-1 months'))} - {:date('Y-m', strtotime('-1 months'))}",
            source       : '0',
            export_statement:'0'
        },
        list_title       : `客户对账单_${Common.datetime('yyyy-MM-dd')}`,
        list_form        : true,
        list_fields      : [[
            {
                type:'checkbox',
                fixed: 'left'
            },
            {
                field : 'group_name',
                title : '主体名称',
                width : 275,
                align : 'left',
            },
            {
                field    : 'customer_id',
                title    : '客户ID',
                width : 180,
                align    : 'left',
            },
            {
                field : 'customer_name',
                title : '客户名称',
                width : 150,
                align : 'left',
            },
            {
                field    : 'company',
                title    : '公司名称',
                align    : 'left',
                minWidth : 275
            },
            {
                field   : 'consume',
                title   : '消费',
                align   : 'right',
                width : 150,
                sort    : true,
                templet : function (data) {
                    return Common.money_format(data.consume);
                }
            },
            {
                field   : 'balance',
                title   : '余额',
                align   : 'right',
                width : 150,
                sort    : true,
                templet : function (data) {
                    if (data.balance >= 0) {
                        return `<span class="color_green">${Common.money_format(data.balance)}</span>`;
                    } else {
                        return `<span class="color_red">${Common.money_format(data.balance)}</span>`;
                    }
                }
            },
            {
                field : 'interval',
                title : '最近发送',
                align : 'center',
            },
            {
                field   : 'operation',
                title   : '操作',
                width : 150,
                templet : function (data) {
                    return "" === data.customer_id ? "" : `<div><button class="layui-btn layui-btn-xs" onclick='Popup.iframe("./details.html?customer_id=${data.customer_id}&source=${window.cache.list_where.source}", "对账单")'>对账单</button></div>`
                }
            }
        ]]
    };

    layui.laydate.render({
        elem  : '#month_section',
        type  : 'month',
        range : true,
        value : window.cache.list_where.month_section
    });

    //加载表格
    Table.reloadTable({
        where          : window.cache.list_where,
        page           : false,
        limit          : 10000,
        defaultToolbar : ['filter', 'print', 'exports'],
        toolbar        : true,
        height : 600,
    });
    
    //导出普道结算单
    layui.form.on('submit(exp_stat)', function(data){
        data.field.export_statement='1';
        window.cache.list_where.export_statement='1';

        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/income/customerStatementList", {
            user_auth:true, 
            user_cookie: $.cookie('PHPSESSID'),
            export_statement:'1',
            source:data.field.source,
            month_section:data.field.month_section
        }).then(function (data) {
            var content = data.data.file; //Blob数据
            var elink = document.createElement('a'); // 创建一个a标签用于下载
            elink.download = data.data.filename; //规定被下载的超链接目标名字
            elink.style.display = 'none'; //标签隐藏
            var blob = base64ToBlob(content, 'application/vnd.ms-excel;charset=UTF-8')
            elink.href = URL.createObjectURL(blob); //规定链接指向的页面的URL
            document.body.appendChild(elink);
            elink.click(); //原生dom触发
            document.body.removeChild(elink);
            //layui.form.render('select');
        });
        return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
    });

    function base64ToBlob(base64, fileType) {
            let audioSrc = base64; // 拼接最终的base64
            let arr = audioSrc.split(',');
            let array = arr[0].match(/:(.*?);/);
            let mime = (array && array.length > 1 ? array[1] : type) || type;
            // 去掉url的头，并转化为byte
            let bytes = window.atob(arr[1]);
            // 处理异常,将ascii码小于0的转换为大于0
            let ab = new ArrayBuffer(bytes.length);
            // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
            let ia = new Uint8Array(ab);
            for (let i = 0; i < bytes.length; i++) {
                ia[i] = bytes.charCodeAt(i);
            }
            return new Blob([ab], {
                type: mime
            });
        }

    $('#bulk_send').on('click', function() {
        var checkStatus = layui.table.checkStatus('list');
        var data = checkStatus.data;
        if (data.length < 1) {
            layer.alert('请选择至少一个客户');
            return false;
        }

        var customer_ids = [];

        data.forEach(function (item) {
            if (item.customer_id) {
                customer_ids.push(item)
            }
        })

        if (customer_ids.length < 1) {
            layer.alert('请选择至少一个客户');
            return false;
        }

        layui.layer.open({
            type: 1,
            title: '邮件发送情况',
            area: ['600px', '500px'],
            id: 'layer_table',
            content: '<div style="padding-top: 20px; padding-left: 20px">总共发送邮件个数：' + customer_ids.length + ', 其中成功发送个数：<b class="send_count">0</b>，失败个数：<b class="send_fail">0</b></div>' +
                '<div class="layui-form" style="padding: 20px">\n' +
                '  <table class="layui-table">\n' +
                '    <colgroup>\n' +
                '      <col width="150">\n' +
                '      <col width="150">\n' +
                '      <col>\n' +
                '    </colgroup>\n' +
                '    <thead>\n' +
                '      <tr>\n' +
                '        <th>客户ID</th>\n' +
                '        <th>客户名称</th>\n' +
                '        <th>发送情况</th>\n' +
                '      </tr> \n' +
                '    </thead>\n' +
                '    <tbody>\n' +
                '    </tbody>\n' +
                '  </table>\n' +
                '</div>',
            btn: '关闭全部',
            btnAlign: 'c', //按钮居中
            shade: 0, //不显示遮罩
            yes: function() {
                layui.layer.closeAll();
                Table.refreshTable();
            }
        });

        customer_ids.forEach(function (item) {
            sendEmail(item);
        })
    });

    function sendEmail(info) {
        var json = {
            customer_id : [info.customer_id],
            month       : "{:date('Ym', strtotime('-1 months'))}",
            source      : window.cache.list_where.source
        }
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/income/bulkSendStatement", json).then(function (response) {
            console.log(response)
            if (response.status == 0) {
                var data = response.data;
                if (data.customer_desc.length > 0) {
                    var html = '';
                    data.customer_desc.forEach(function (item) {
                        html += '<tr>' +
                            '<td>' + item[0] + '</td>' +
                            '<td>' + item[1] + '</td>' +
                            '<td>' + item[2] + '</td>' +
                            '</tr>';
                    });
                    $('#layer_table table>tbody').append(html);
                }
                var send_count = parseInt($('.send_count').html());
                $('.send_count').html(send_count + data.send_count);
                var send_fail = parseInt($('.send_fail').html());
                $('.send_fail').html(send_fail + data.send_fail.length);
                // str += "<br>其中成功发送邮件个数：" + data.send_count;
                // if (data.send_fail.length > 0) {
                //     str += "<br>其中发送失败的客户：" + data.send_fail.join('，');
                // }
                // if (data.customer_is_remind.length > 0) {
                //     str += "<br>本月已发送过的客户：" + data.customer_is_remind.join('，');
                // }
                // if (data.customer_del.length > 0) {
                //     str += "<br>不存在或已被删除的客户：" + data.customer_del.join('，');
                // }
            } else {
                html = '<tr>' +
                    '<td>' + info.customer_id + '</td>' +
                    '<td>' + info.customer_name + '</td>' +
                    '<td>' + response.msg + '</td>' +
                    '</tr>';
                $('#layer_table table>tbody').append(html);
            }

        });
    }

    layui.form.on('submit(export_qifu)', function(data) {
        var postData = data.field;
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/customerStatement/download", postData).then(function (data) {
            var returnData = data.data;
            let sheetHeader = ['客户id', '客户', '公司名称', '消耗', '余额', '类型'];
            let sheetData = [];
            $.each(returnData, function (key, val){
                sheetData.push({
                    customer_id: val['customerId'],
                    customer: val['customerName'],
                    company_name: val['companyName'],
                    consume: val['consume'],
                    balance: val['balance'],
                    type: val['type'] == 'dianhua' ? '羽乐科技' : '企服'
                })
            });
            let option = {};
            option.fileName = '客户对账单导出';
            option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
            (new ExportJsonExcel(option)).saveExcel();
        });
        return false;
    });

    // console.log($.cookie('PHPSESSID'))
    Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap", {dept_list:true, user_cookie: $.cookie('PHPSESSID')}).then(function (data) {
        // console.log(data.data.user_auth.area_person);
        //var option = '<option></option>';
        //$.each(data.data.user_auth.area_person,function (index,item){
            //option += '<option value=' + item.area.dept_id+ '>' + item.area.dept_name + '</option>';
        //})
        //$("#area").html(option);
        //layui.form.render('select');
        layui.use('layCascader', function () {
            let layCascader = layui.layCascader;
            layCascader({
                elem: '#area',
                options: data.data.dept_list.dept_list,
                clearable: true,
                popperClass:'popper',
                props: {
                    checkStrictly: true,
                    expandTrigger: 'hover',

                }
            });
        });
    });
</script>