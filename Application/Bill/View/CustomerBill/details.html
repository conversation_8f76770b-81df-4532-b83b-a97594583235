<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客户对账单（V2）--明细</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__CSS__/bootstrap.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        .container {
            width      : 90%;
            margin-top : 10px;
        }

        .layui-form-item {
            width : 98%;
        }

        .layui-table {
            width  : 98%;
            margin : 10px auto;
        }

        .submit {
            float        : right;
            margin-right : 10px;
        }
    </style>
</head>
<body>
<div class="container">
    <form class="layui-form layui-form-pane" action="" type="post">
        <fieldset class="layui-elem-field">
            <legend>客户对账单</legend>
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">邮件设置</h2>
                    <div class="layui-colla-content layui-show layui-row">
                        <div class="layui-form-item">
                            <label class="layui-form-label" for="addressee">收件人</label>
                            <div class="layui-input-block">
                                <input type="text" name="addressee" required lay-verify="required" placeholder="请输入收件人" autocomplete="off" class="layui-input" id="addressee">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label" for="cc">抄送人</label>
                            <div class="layui-input-block">
                                <input type="text" name="cc" placeholder="请输入抄送人" autocomplete="off" class="layui-input" id="cc">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label" for="subject">主题</label>
                            <div class="layui-input-block">
                                <input type="text" name="subject" required lay-verify="required" placeholder="请输入主题" autocomplete="off" class="layui-input" id="subject">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label" for="subject">邮件内容</label>
                            <div class="layui-input-block">
                                <textarea name="content" id="content" placeholder="请输入邮件内容"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">结算单</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
<!--                            <tr>-->
<!--                                <td colspan="3" id="final_bill_info_header_0" align="center"></td>-->
<!--                            </tr>-->
                            <tr>
                                <td colspan="3" id="final_bill_info_header_1" align="center"></td>
                            </tr>
                            <tr>
                                <td colspan="3">&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="3" id="final_bill_info_header_2" align="center"></td>
                            </tr>
                            <tr>
                                <td colspan="3">&nbsp;</td>
                            </tr>
                            <tr>
                                <td align="center">公司名称</td>
                                <td colspan="2" id="final_bill_info_company" align="left"></td>
                            </tr>
                            <tr>
                                <td align="center">客户名称</td>
                                <td colspan="2" id="final_bill_info_customer_name" align="left"></td>
                            </tr>
                            <tr>
                                <td align="center">上月剩余金额</td>
                                <td colspan="2" id="final_bill_info_prev_month_balance" align="left"></td>
                            </tr>
                            <tr>
                                <td align="center">期间</td>
                                <td align="center">充值金额</td>
                                <td align="center">消耗金额</td>
                            </tr>
                            <tbody id="final_bill_info_details"></tbody>
                            <tr>
                                <td colspan="2" align="center">剩余金额</td>
                                <td id="final_bill_info_balance" align="center"></td>
                            </tr>
                            <tr>
                                <td colspan="3">&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="3">&nbsp;</td>
                            </tr>

                            <tr>
                                <td align="left">甲方信息确认：（签章）</td>
                                <td align="center"></td>
                                <td align="left">乙方信息确认：（签章）</td>
                            </tr>
                            <tr>
                                <td align="right" id="final_bill_info_footer_0_left"></td>
                                <td align="center"></td>
                                <td align="right" id="final_bill_info_footer_0"></td>
                            </tr>
                            <tr>
                                <td align="right" id="final_bill_info_footer_1_left"></td>
                                <td align="center"></td>
                                <td align="right" id="final_bill_info_footer_1"></td>
                            </tr>
                            <tr>
                                <td align="right" id="final_bill_info_footer_2_left"></td>
                                <td align="center"></td>
                                <td align="right" id="final_bill_info_footer_2"></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">账单明细</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table" id="bill_details"></table>
                    </div>
                </div>

                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">产品日调用明细</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead>
                            <tr>
                                <th align="center">主产品</th>
                                <th align="center">子产品</th>
                                <th align="center">日期</th>
                                <th align="center">计费用量</th>
                            </tr>
                            </thead>
                            <tbody id="usage_details"></tbody>
                        </table>
                    </div>
                </div>

                <div class="layui-form-item" style="margin:0 auto;">
                    <button type="button" class="layui-btn submit" lay-submit lay-filter="submit">发送对账单</button>
                </div>
            </div>
        </fieldset>
    </form>
</div>
</body>
</html>
<script src="__JS__axios.min.js" type="text/javascript"></script>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script src="__STATICS__ckeditor-v4/ckeditor.js"></script>
<script type="application/javascript">
    window.cache = {
        data : {}
    };
    //发送对账单
    layui.form.on('submit(submit)', function (data) {

        if (window.cache.data.other.isRemind) {
            Popup.confirm(window.cache.data.other.interval + '已经发送过此账单，是否重复发送？', function () {
                sendMail(data);
            });
        } else {
            sendMail(data);
        }
        return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
    });

    function sendMail(data) {
        let json                 = window.cache.data;
        json.base_info.addressee = data.field.addressee;
        json.base_info.cc        = data.field.cc;
        json.base_info.subject   = data.field.subject;
        //json.base_info.content   = data.field.content;
        json.base_info.content   = CKEDITOR.instances.content.getData();
        json.customer_id = "{$Think.get.customer_id}";
        json.source = "{$Think.get.source}";

        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/income/sendStatement", json).then(function (response) {
            Popup.success('邮件已发送');
        });
    }

    //加载富文本编辑器
    function loadUeditor() {
        CKEDITOR.replace('content', {
            height : 400
        });
    }

    //获取数据
    function getInfo() {
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/income/customerStatementDetails", {
            customer_id : "{$customer_id}",
            source : "{$source}",
            month       : "{:date('Ym', strtotime('-1 months'))}"
        }).then(function (response) {
            //页面渲染
            window.cache.data = response.data;
            display(response.data);
        });
    }

    //页面渲染
    function display(data) {
        //基础信息
        $("#addressee").val(data.base_info.addressee);
        $("#cc").val(data.base_info.cc);
        $("#subject").val(data.base_info.subject);
        $("#content").val(data.base_info.content);
        loadUeditor();

        //结算单
        //$("#final_bill_info_header_0").html(data.final_bill_info.header[0]);
        $("#final_bill_info_header_1").html(data.final_bill_info.header[1]);
        $("#final_bill_info_header_2").html(data.final_bill_info.header[2]);
        $("#final_bill_info_company").html(data.final_bill_info.company);
        $("#final_bill_info_customer_name").html(data.final_bill_info.customer_name);
        $("#final_bill_info_prev_month_balance").html(data.final_bill_info.prev_month_balance);
        let final_bill_info_details = ``;
        data.final_bill_info.details.forEach(function (item) {
            final_bill_info_details += `<tr><td align="center">${item[0]}</td><td align="center">${item[1]}</td><td align="center">${item[2]}</td></tr>`;
        });
        $("#final_bill_info_details").html(final_bill_info_details);
        //甲方信息确认
        $("#final_bill_info_footer_0_left").html(data.final_bill_info.company);
        $("#final_bill_info_footer_2_left").html(data.final_bill_info.footer[2]);

        //乙方信息确认
        $("#final_bill_info_footer_0").html(data.final_bill_info.footer[0]);
        $("#final_bill_info_footer_1").html(data.final_bill_info.footer[1]);
        $("#final_bill_info_footer_2").html(data.final_bill_info.footer[2]);
        $("#final_bill_info_balance").html(data.final_bill_info.balance);


        //账单明细
        let details_bill = `<thead><tr><th align="center">产品名称</th>`;
        if (!data.details_bill_info.hide_fields.children_product_name) {
            details_bill += `<th align="center">子产品</th>`;
        }
        if (!data.details_bill_info.hide_fields.operator_name) {
            details_bill += `<th align="center">运营商</th>`;
        }
        details_bill += `<th align="center">单价</th><th align="center">计费用量</th><th align="center">消耗金额</th></tr></thead><tbody>`;
        data.details_bill_info.bills.forEach(function (item) {
            details_bill += `<tr><td rowspan="${item.data.length}" align="center">${item.father_name}</td>`;
            item.data.forEach(function (detail) {
                if (!data.details_bill_info.hide_fields.children_product_name) {
                    details_bill += `<td align="center">${detail.children_product_name}</td>`;
                }
                if (!data.details_bill_info.hide_fields.operator_name) {
                    details_bill += `<td align="center">${detail.operator_name}</td>`;
                }
                details_bill += `<td align="center">${detail.price}</td><td align="right">${detail.number}</td><td align="right">${detail.money}</td></tr><tr>`;
            });
            details_bill += `</tr>`;
        });
        $("#bill_details").html(details_bill);

        //调用明细
        let usage_details = ``;
        data.customer_usage.forEach(function (item) {
            usage_details += `<tr>
<td align="center">${item.father_name}</td>
<td align="center">${item.product_name}</td>
<td align="center">${item.date}</td>
<td align="right">${item.number}</td>
</tr>`;
        });
        $("#usage_details").html(usage_details);
    }

    getInfo();
</script>