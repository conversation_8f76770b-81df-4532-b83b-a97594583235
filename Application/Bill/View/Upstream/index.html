<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }

        .index-btn {
            margin : 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="/Bill/Upstream/file" method="post" class="form-inline">
                <div class="form-group">
                    <label class="control-label" for="start_date">时间区间：</label>
                    <input type="date" name="start_date" id="start_date" class="form-control"
                           value="<?php echo date('Y-m-d', strtotime('-1 month'));?>"/>
                    -
                    <input type="date" name="end_date" id="end_date" class="form-control"
                           value="<?php echo date('Y-m-d'); ?>"/>
                </div>
                <div class="form-group">
                    <label for="customer_id">选择客户：</label>
                    <?php $customer_get = I('get.customer'); ?>
                    <select name="customer_id" id="customer_id" class="form-control">
                        <option value=""></option>
                        {$data.customerOption}
                    </select>
                </div>
                <div class="form-group">
                    <label for="product_id">选择产品：</label>
                    <select name="product_id" id="product_id" class="form-control">
                        <option value=""></option>
                        {$data.productOption}
                    </select>
                </div>
                <div class="form-group">
                    <label for="channel">选择渠道：</label>
                    <select name="channel[]" id="channel" class="form-control selectpicker" multiple
                            data-live-search="true" data-actions-box="true">
                        {$data.upstreamChannelOption}
                    </select>
                </div>
                <div class="form-group">
                    <input type="button" id="submit_btn" class="btn btn-primary btn-sm" value="查询"
                           onclick="refreshData()">

                    <input type="submit" id="excel" class="btn btn-primary btn-sm" value="导出">
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="table">
            <thead>
            <th>客户ID</th>
            <th>客户名称</th>
            <th>产品</th>
            <th>渠道</th>
            <th onclick="sort($(this), 'number')">
                <span class="v-table-sort-icon">
                    <i class="v-icon-up-dir" data-field="number" data-sort="asc"></i>
                    <i class="v-icon-down-dir checked" data-field="number" data-sort="desc"></i>
                </span>
                计费用量
            </th>
            <th onclick="sort($(this), 'money')">
                <span class="v-table-sort-icon">
                    <i class="v-icon-up-dir" data-field="money" data-sort="asc"></i>
                    <i class="v-icon-down-dir" data-field="money" data-sort="desc"></i>
                </span>
                成本费用
            </th>
            </thead>
            <tbody id="tableBody">

            </tbody>
        </table>
    </div>
</div>
</body>
</html>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.4/layui/layui.all.js"></script>
<script type="text/javascript">
    $(function () {
        $("#channel").selectpicker({
            width: '220px'
        });
        $("#customer_id").select2({
            allowClear : true,
            theme      : "bootstrap",
            placeholder: '选择客户',
            width      : '300px'
        });
        $("#product_id").select2({
            allowClear : true,
            theme      : "bootstrap",
            placeholder: '选择产品',
            width      : '200px'
        });

//        $("#file_export").click(function () {
//            var conf = confirm('文件导出将导出已查询出的全部数据（不分页），是否继续？');
//            if (conf) {
//                window.open("{:U('Account/Account/export')}" + location.search);
//            }
//        });
        refreshData();
    });
    function refreshData() {
        layui.layer.load(0, {shade: [0.3, '#393D49']});
        $.ajax({
            url    : '/Bill/Upstream/get_index_data.html',
            type   : 'post',
            data   : {
                start_date : $("#start_date").val(),
                end_date   : $("#end_date").val(),
                customer_id: $("#customer_id").val(),
                product_id : $("#product_id").val(),
                channel    : $("#channel").val(),
                sort_field : $(".v-table-sort-icon").find('.checked').attr('data-field'),
                sort_type  : $(".v-table-sort-icon").find('.checked').attr('data-sort'),
            },
            success: function (res) {
                if (Number(res.status) === 0) {
                    refreshTable(res.data);
                } else {
                    alert(res.msg);
                }
            }
        });
    }

    function refreshTable(data) {
        let tableBody = '';

        let number = 0;
        let money  = 0;
        data.forEach((item) => {
            tableBody += `<tr></tr><td>${item.customer_id}</td><td>${item.customer_name}</td><td>${item.product_name}</td><td>${item.channel_name}</td><td align="right">${item.number}</td><td align="right">${item.money}</td></tr>`;

            number += item.number;
            money += item.money;
        });

        money = money.toFixed(2);

        //求合计
        tableBody = `<tr><th colspan="4" align="center" style="text-align: center;">合计</th><td align="right">${number}</td><td align="right">${money}</td></tr>` + tableBody;

        $("#tableBody").html(tableBody);
        layui.layer.closeAll();
    }

    function sort(element, field) {
        let sort_field = $(".v-table-sort-icon").find('.checked').attr('data-field');
        let index      = 1;

        if (sort_field == field) {
            let sort_type = element.find('.checked').attr('data-sort');
            index         = (sort_type == 'desc' ? 0 : 1);
        }
        $(".v-table-sort-icon").find('i').removeClass('checked');
        element.find('i').eq(index).addClass('checked');
        refreshData();
    }
</script>