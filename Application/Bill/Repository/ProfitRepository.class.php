<?php
/**
 * Created by PhpStorm.
 * User: gang8
 * Date: 2020/5/25
 * Time: 11:21
 */

namespace Bill\Repository;

use Account\Model\CompanytypeModel;
use Account\Model\CustomerExpendModel;
use Account\Model\CustomerModel;
use Common\Common\CurlTrait;
use Common\Controller\DataAuthController;
use Common\Model\ProductTypeModel;
use Common\Model\SystemDeptGradeModel;
use Common\Model\SystemUserModel;
use Stat\Model\ProductModel;

class ProfitRepository
{
	use CurlTrait;
	//当前用户存在权限的客户ID
	private $allowCustomerIds = [];
	
	/**
	 * 获取大区维度的统计
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/5/25 15:03
	 *
	 * @return array
	 **/
	public function getRegionInfo()
	{
		$params = $this->getRegionCurlParams();
		
		//通过接口拉取余额数据
		return $this->getProfitInfo($params);
	}
	
	/**
	 * 通过接口拉取余额数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 15:15
	 *
	 * @param $params array 请求参数
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getProfitInfo($params)
	{
		//通过接口获取利润数据
		$data = $this->post(C('LIST_API_URL.profit_customer'), $params);
		
		if (0 != $data['status']) {
			throw new \Exception($data['msg']);
		}
		$data = $data['data'];
		
		//获取商务部门的部门数据
		$businessDeptInfo = $this->getBusinessDeptInfo();
		
		//获取客户ID-商务部门的映射关系
		$customerId2BusinessDeptIdMapping = $this->getCustomerId2BusinessDeptIdMapping($businessDeptInfo);
		
		//整理一个结果数据
		$result = array_map(function ($item) {
			$dept_id       = $item['dept_id'];
			$dept_name     = $item['dept_name'];
			$income_number = $income_money = $cost_money = $profit_money = 0;
			
			return compact('dept_id', 'dept_name', 'income_money', 'income_number', 'cost_money', 'profit_money');
		}, $businessDeptInfo);
		$result = array_column($result, null, 'dept_id');
		
		//计算数据
		array_walk($data, function ($item) use (&$result, $customerId2BusinessDeptIdMapping) {
			$customer_id = $item['customer_id'];
			$dept_id     = $customerId2BusinessDeptIdMapping[$customer_id];
			if (!array_key_exists($customer_id, $customerId2BusinessDeptIdMapping)) {
				return;
			}
			$income_number                     = $item['income_arr']['number'];
			$income_money                      = $item['income_arr']['money'];
			$cost_money                        = $item['cost_arr']['money'];
			$profit_money                      = $item['profit_arr']['money'];
			$result[$dept_id]['income_number'] += $income_number;
			$result[$dept_id]['income_money']  += $income_money;
			$result[$dept_id]['cost_money']    += $cost_money;
			$result[$dept_id]['profit_money']  += $profit_money;
		});
		
		//过滤数据
		$result = array_filter($result, function (&$item) {
			$item['income_money'] = sprintf('%01.2f', round($item['income_money'], 2));
			$item['cost_money']   = sprintf('%01.2f', round($item['cost_money'], 2));
			$item['profit_money'] = sprintf('%01.2f', round($item['profit_money'], 2));
			
			return 0 != $item['income_money'] || 0 != $item['income_number'] || 0 != $item['cost_money'] || 0 != $item['profit_money'];
		});
		$result = array_values($result);
		
		//排序
		$orderField = I('post.sort_field', 'income_money');
		$orderType  = I('post.sort_type', 'desc') == 'asc' ? SORT_ASC : SORT_DESC;
		array_multisort(array_column($result, $orderField), $orderType, $result);
		
		//特殊消耗统计
		$specialTotal = $this->getCustomerSpecialTotal($params['customerIds'], $params['start_date'], $params['end_date']);
		
		//计算总数
		$total = [
			'income_number' => array_sum(array_column($result, 'income_number')),
			'income_money'  => sprintf('%01.2f', (array_sum(array_column($result, 'income_money')))),
			'cost_money'    => sprintf('%01.2f', array_sum(array_column($result, 'cost_money'))),
			'profit_money'  => sprintf('%01.2f', (array_sum(array_column($result, 'profit_money')))),
		];
		
		return compact('total', 'result', 'specialTotal');
	}
	
	/**
	 * 获取客户ID-商务部门的映射关系
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 17:20
	 *
	 * @param $businessDeptInfo array 获取商务部门的相关数据 部门ID，部门名称，包含的商务人员
	 *
	 * @return array
	 **/
	protected function getCustomerId2BusinessDeptIdMapping($businessDeptInfo)
	{
		//获取商务人员与部门的映射关系
		$username2BusinessDeptMapping = [];
		array_walk($businessDeptInfo, function ($item) use (&$username2BusinessDeptMapping) {
			$username = explode(',', $item['username']);
			$dept_id  = $item['dept_id'];
			
			foreach ($username as $usernameItem) {
				$username2BusinessDeptMapping[$usernameItem] = $dept_id;
			}
		});
		
		//获取所有商务人员关联的客户
		$customerModel = new CustomerModel();
		$customerIds   = $customerModel->field([
			'customer_id',
			'salesman',
		])
									   ->where(['is_delete' => 0])
									   ->select();
		
		//遍历客户，建立关系
		$result = [];
		array_walk($customerIds, function ($item) use (&$result, $username2BusinessDeptMapping) {
			$customer_id = $item['customer_id'];
			$username    = $item['salesman'];
			if (array_key_exists($username, $username2BusinessDeptMapping)) {
				$result[$customer_id] = $username2BusinessDeptMapping[$username];
			}
		});
		
		return $result;
	}
	
	/**
	 * 获取商务部门的相关数据 部门ID，部门名称，包含的商务人员
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 17:18
	 *
	 * @return array
	 **/
	protected function getBusinessDeptInfo()
	{
		$deptId = 'DEPT2019061815074748';
		//获取商务部所有分组部门ID
		$systemDeptGradeModel = new SystemDeptGradeModel();
		$businessDeptIds      = $systemDeptGradeModel->where(['grade_father_id' => $deptId])
													 ->field(['grade_dept_id'])
													 ->select();
		$businessDeptIds      = array_values(array_unique(array_column($businessDeptIds, 'grade_dept_id')));
		
		//获取该部门下的所有商务人员
		$systemUserModel = new SystemUserModel();
		
		return $systemUserModel->field([
			'GROUP_CONCAT(`username`) as username',
			'crs_system_user.dept_id',
			'SUBSTRING_INDEX(GROUP_CONCAT(`dept_name`), ",", 1) AS dept_name',
		])
							   ->where([
								   'crs_system_user.dept_id' => [
									   'in',
									   $businessDeptIds,
								   ],
							   ])
							   ->group('crs_system_user.dept_id')
							   ->join('crs_system_dept ON crs_system_dept.dept_id=crs_system_user.dept_id')
							   ->select();
		
	}
	
	/**
	 * 获取查询条件（大区维度）
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 15:12
	 *
	 * @return array
	 **/
	protected function getRegionCurlParams()
	{
		//获取当前登录账号所能查看的客户ID
		$customerIds = $this->getCustomerIds();
		
		$start_date = I('post.start_date', date('Y-m-d', strtotime('first day of this month')));
		$end_date   = I('post.end_date', date('Y-m-d', strtotime('-1 days')));
		
		$key = '5168b337cb7cdc2cd11675d634719ee9';
		
		return compact('customerIds', 'start_date', 'end_date', 'key');
	}
	
	
	/**
	 * 获取当前登录账号所能查看的客户ID
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 11:22
	 *
	 * @return array
	 **/
	protected function getCustomerIds()
	{
		if (empty($this->allowCustomerIds)) {
			$this->allowCustomerIds = DataAuthController::instance()
														->getReadCustomerIdsByUsername($_SESSION[C('LOGIN_SESSION_NAME')]);
		}
		
		return $this->allowCustomerIds;
	}
	
	/**
	 * 获取产品维度的统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 10:48
	 *
	 * @return array
	 **/
	public function getProductInfo()
	{
		//查询参数
		$params = $this->getProductParams();
		
		//通过接口获取利润数据
		$data = $this->post(C('LIST_API_URL.profit_product'), $params);
		
		if (0 != $data['status']) {
			throw new \Exception($data['msg']);
		}
		//整合并过滤数据并排序
		$orderField = I('post.sort_field', 'income_money');
		$orderType  = I('post.sort_type', 'desc') == 'asc' ? SORT_ASC : SORT_DESC;
		
		$result = array_filter($data['data'], function (&$item) use ($orderField, $orderType) {
			$product_name  = $item['product_name'];
			$product_id    = $item['product_id'];
			$income_number = $item['data']['income_arr']['number'];
			
			$income_money = sprintf('%01.2f', round($item['data']['income_arr']['money'], 2));
			$cost_money   = sprintf('%01.2f', round($item['data']['cost_arr']['money'], 2));
			$profit_money = sprintf('%01.2f', round($item['data']['profit_arr']['money'], 2));
			if (0 == $income_number && 0 == $income_money && 0 == $cost_money && 0 == $profit_money) {
				return false;
			}
			if (array_key_exists('children', $item)) {
				$children = $item['children'];
				$children = array_filter($children, function (&$childrenItem) {
					$product_name  = $childrenItem['product_name'];
					$product_id    = $childrenItem['product_id'];
					$income_number = $childrenItem['data']['income_arr']['number'];
					$income_money  = sprintf('%01.2f', round($childrenItem['data']['income_arr']['money'], 2));
					$cost_money    = sprintf('%01.2f', round($childrenItem['data']['cost_arr']['money'], 2));
					$profit_money  = sprintf('%01.2f', round($childrenItem['data']['profit_arr']['money'], 2));
					if (0 == $income_number && 0 == $income_money && 0 == $cost_money && 0 == $profit_money) {
						return false;
					}
					$childrenItem = compact('product_name', 'product_id', 'income_number', 'income_money', 'cost_money', 'profit_money');
					
					return true;
				});
				array_multisort(array_column($children, $orderField), $orderType, $children);
				$children = array_values($children);
				$item     = compact('product_name', 'product_id', 'income_number', 'income_money', 'cost_money', 'profit_money', 'children');
				
				return true;
			}
			$item = compact('product_name', 'product_id', 'income_number', 'income_money', 'cost_money', 'profit_money');
			
			return true;
		});
		array_multisort(array_column($result, $orderField), $orderType, $result);
		
		//特殊费用
		$specialTotal = $this->getCustomerSpecialTotal($params['customerIds'], $params['start_date'], $params['end_date']);
		
		//汇总数据
		$total  = [
			'income_number' => array_sum(array_column($result, 'income_number')),
			'income_money'  => sprintf('%01.2f', (array_sum(array_column($result, 'income_money')))),
			'cost_money'    => sprintf('%01.2f', array_sum(array_column($result, 'cost_money'))),
			'profit_money'  => sprintf('%01.2f', (array_sum(array_column($result, 'profit_money')))),
		];
		$result = array_values($result);
		
		return compact('result', 'total', 'specialTotal');
	}
	
	/**
	 * 获取查询参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 15:37
	 *
	 * @return array
	 **/
	protected function getProductParams()
	{
		$dept_id     = I('post.dept_id', '');
		$username    = I('post.username', '');
		$customer_id = I('post.customer_id', '');
		
		$selectCustomerIds = [];
		if (!empty($customer_id)) {
			$selectCustomerIds = [$customer_id];
		} else if (!empty($username)) {
			$customerModel     = new CustomerModel();
			$selectCustomerIds = $customerModel->field(['customer_id'])
											   ->where(['salesman' => $username])
											   ->select();
			$selectCustomerIds = array_column($selectCustomerIds, 'customer_id');
		} else if (!empty($dept_id)) {
			$businessDeptInfo  = $this->getBusinessDeptInfo();
			$businessDeptInfo  = array_column($businessDeptInfo, null, 'dept_id');
			$salesman          = [
				'in',
				explode(',', $businessDeptInfo[$dept_id]['username']),
			];
			$customerModel     = new CustomerModel();
			$selectCustomerIds = $customerModel->field(['customer_id'])
											   ->where(compact('salesman'))
											   ->select();
			$selectCustomerIds = array_column($selectCustomerIds, 'customer_id');
		}
		
		if (empty($selectCustomerIds)) {
			$customerIds = $this->getCustomerIds();
		} else {
			$customerIds = array_intersect($this->getCustomerIds(), $selectCustomerIds);
			if (empty($customerIds)) {
				throw new \Exception('您没有查看这些数据的权限');
			}
		}
		$start_date = I('post.start_date', '');
		$end_date   = I('post.end_date', '');
		$productIds = I('post.product_id', []);
		
		$productTypeModel = new ProductTypeModel();
		if (in_array(2101, $productIds)) {
			//快捷版--评分类字段
			$key = array_search(2101, $productIds);
			unset($productIds[$key]);
			$scoreProductIds = $productTypeModel->where(['type' => 1])
												->field('product_id')
												->select();
			$productIds      = array_merge($productIds, array_column($scoreProductIds, 'product_id'));
		}
		
		if (in_array(2102, $productIds)) {
			//快捷版--统计类字段
			$key = array_search(2102, $productIds);
			unset($productIds[$key]);
			$normalProductIds = $productTypeModel->where(['type' => 2])
												 ->field('product_id')
												 ->select();
			$productIds       = array_merge($productIds, array_column($normalProductIds, 'product_id'));
			$productIds[]     = 210;
		}
		
		if (in_array(661, $productIds)) {
			//661查看时，也能查看615
			$productIds[] = 615;
		}
		
		$key         = '5168b337cb7cdc2cd11675d634719ee9';
		$productIds  = array_values($productIds);
		$customerIds = array_values($customerIds);
		
		return compact('customerIds', 'productIds', 'start_date', 'end_date', 'key');
	}
	
	/**
	 * 获取产品维度页面展示的表单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 10:49
	 *
	 * @return array
	 **/
	public function getProductHtmlInfo()
	{
		$businessDeptInfo = $this->getBusinessDeptInfo();
		$businessDeptInfo = array_column($businessDeptInfo, null, 'dept_id');
		
		//筛选的大区
		$dept_id = I('get.dept_id', '');
		//筛选客户
		$customer_id = I('get.customer_id', '');
		if (empty($dept_id)) {
			if (!empty($customer_id)) {
				//如果是筛选的客户
				//通过客户获取到大区ID
				$customerId2BusinessDeptId = $this->getCustomerId2BusinessDeptIdMapping($businessDeptInfo);
				$dept_id                   = $customerId2BusinessDeptId[$customer_id];
			}
		}
		//大区选择器
		$businessDeptOption = makeOption(array_column($businessDeptInfo, 'dept_name', 'dept_id'), $dept_id);
		
		//用户选择器
		$usernameOption = $this->getUsernameOption($dept_id, $businessDeptInfo);
		
		//客户选择器
		$customerOption = $this->getCustomerOption($dept_id, '', $customer_id, $businessDeptInfo);
		
		//产品选择器
		$productOption = $this->getProductOption();
		
		$start_date = I('get.start_date', date('Y-m-d', strtotime('first day of this month')));
		$end_date   = I('get.end_date', date('Y-m-d'));
		
		return compact('businessDeptOption', 'usernameOption', 'customerOption', 'productOption', 'start_date', 'end_date');
	}
	
	/**
	 * 产品选择器
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 15:11
	 *
	 * @return string
	 **/
	protected function getProductOption()
	{
		//邦信分快捷版
		$productInfo = [
			2101 => '邦信分快捷版--评分类字段',
			2102 => '邦信分快捷版--统计类字段',
		];
		
		//金盾非分流产品
		$productModel     = new ProductModel();
		$productInfoExtra = $productModel->field([
			'product.product_id',
			'product_name',
		])
											 ->where(['father_id'=> 615])
											 ->select();
		$productInfo      = $productInfo + array_map(function ($product_name) {
				return '金盾--' . $product_name;
			}, array_column($productInfoExtra, 'product_name', 'product_id'));
		$productInfo[603] = '金盾--金盾贷后外呼风险';
		$productInfo[612] = '金盾--金盾贷前';
		$productInfo[613] = '金盾--金盾贷后';
		$productInfo[614] = '金盾--号码风险等级';
		$productInfo[616] = '金盾--风险符号';
		$productInfo[664] = '金盾--号码预警提示';
		
		//邦秒验
		$productInfoExtra = $productModel->field([
			'product_id',
			'product_name',
		])
										 ->where(['father_id' => 200])
										 ->select();
		$productInfo      = $productInfo + array_map(function ($product_name) {
				return '邦秒验--' . $product_name;
			}, array_column($productInfoExtra, 'product_name', 'product_id'));
		$productInfo[801] = '邦秒验--号码状态查询';
		
		//邦秒配
		$productInfo += [
			601 => '邦秒配--单号版',
			604 => '邦秒配--批量版',
			104 => '邦秒配--详单版',
		];
		
		//邦信分详单版
		$productInfo += [
			101 => '邦信分详单版--V1',
			105 => '邦信分详单版--V2',
		];
		
		//邦信分私有云
		$productInfo[501] = '邦信分私有云';
		
		//邦企查
		$productInfo[401] = '邦企查';
		
		$option = '';
		foreach ($productInfo as $product_id => $product_name) {
			$option .= "<option value='{$product_id}' selected>{$product_name}</option>";
		}
		
		return $option;
	}
	
	/**
	 * 客户选择器
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 11:23
	 *
	 * @param $dept_id          string 大区ID
	 * @param $username         string 商务用户
	 * @param $default          string 默认选择
	 * @param $businessDeptInfo array 大区数据
	 *
	 * @return string
	 **/
	protected function getCustomerOption($dept_id, $username, $default = '', $businessDeptInfo = null)
	{
		$customerModel = new CustomerModel();
		if (!empty($username)) {
			$salesman = $username;
			$result   = $customerModel->field([
				'customer_id',
				'name',
			])
									  ->where(compact('salesman'))
									  ->select();
			
			return makeOption(array_column($result, 'name', 'customer_id'), $default);
		}
		
		if (empty($businessDeptInfo)) {
			$businessDeptInfo = $this->getBusinessDeptInfo();
			$businessDeptInfo = array_column($businessDeptInfo, null, 'dept_id');
		}
		
		if (empty($dept_id)) {
			$salesman = [
				'in',
				array_reduce(array_column($businessDeptInfo, 'username'), function ($result, $item) {
					return array_merge($result, explode(',', $item));
				}, []),
			];
		} else {
			$salesman = [
				'in',
				explode(',', $businessDeptInfo[$dept_id]['username']),
			];
		}
		
		$result = $customerModel->field([
			'customer_id',
			'name',
		])
								->where(compact('salesman'))
								->select();
		
		return makeOption(array_column($result, 'name', 'customer_id'), $default);
	}
	
	/**
	 * 获取某个大区内的所有用户Option
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 11:20
	 *
	 * @param $dept_id          string 大区ID
	 * @param $businessDeptInfo array 大区数据
	 *
	 * @return string
	 **/
	protected function getUsernameOption($dept_id, $businessDeptInfo = null)
	{
		if (empty($businessDeptInfo)) {
			$businessDeptInfo = $this->getBusinessDeptInfo();
			$businessDeptInfo = array_column($businessDeptInfo, null, 'dept_id');
		}
		
		if (empty($dept_id)) {
			//所有商务人员
			$username = array_reduce(array_column($businessDeptInfo, 'username'), function ($result, $item) {
				return array_merge($result, explode(',', $item));
			}, []);
		} else {
			$username = explode(',', $businessDeptInfo[$dept_id]['username']);
		}
		
		$userModel = new SystemUserModel();
		$username  = $userModel->field([
			'realname',
			'username',
		])
							   ->where([
								   'username' => [
									   'in',
									   $username,
								   ],
							   ])
							   ->select();
		
		return makeOption(array_column($username, 'realname', 'username'));
	}
	
	/**
	 * 获取二级联动时，修改大区所带来的数据变化
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 15:48
	 *
	 * @return array
	 **/
	public function getLinkageUsernameInfo()
	{
		$dept_id          = I('post.dept_id', '');
		$businessDeptInfo = $this->getBusinessDeptInfo();
		$businessDeptInfo = array_column($businessDeptInfo, null, 'dept_id');
		
		$usernameOption = $this->getUsernameOption($dept_id, $businessDeptInfo);
		
		$customerOption = $this->getCustomerOption($dept_id, '', '', $businessDeptInfo);
		
		return compact('usernameOption', 'customerOption');
	}
	
	/**
	 * 获取二级联动时，修改商务用户所带来的变化
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/5/26 16:01
	 *
	 * @return array
	 **/
	public function getLinkageCustomerInfo()
	{
		$username = I('post.username', '');
		$dept_id  = I('post.dept_id', '');
		
		$businessDeptInfo = $this->getBusinessDeptInfo();
		$businessDeptInfo = array_column($businessDeptInfo, null, 'dept_id');
		
		$customerOption = $this->getCustomerOption($dept_id, $username, '', $businessDeptInfo);
		
		return compact('customerOption');
	}
	
	/**
	 * 客户统计页获取表单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 17:43
	 *
	 * @return array
	 **/
	public function getCustomerHtmlInfo()
	{
		$businessDeptInfo = $this->getBusinessDeptInfo();
		$businessDeptInfo = array_column($businessDeptInfo, null, 'dept_id');
		
		//大区选择器
		$businessDeptOption = makeOption(array_column($businessDeptInfo, 'dept_name', 'dept_id'), '');
		
		//用户选择器
		$usernameOption = $this->getUsernameOption('', $businessDeptInfo);
		
		//客户选择器
		$customerOption = $this->getCustomerOption('', '', '', $businessDeptInfo);
		
		//产品选择器
		$productOption = $this->getCustomerProductOption();
		
		$start_date = I('get.start_date', date('Y-m-d', strtotime('first day of this month')));
		$end_date   = I('get.end_date', date('Y-m-d'));
		
		return compact('businessDeptOption', 'usernameOption', 'customerOption', 'productOption', 'start_date', 'end_date');
	}
	
	/**
	 * 获取客户统计页产品选择器
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 17:44
	 *
	 * @return string
	 **/
	protected function getCustomerProductOption()
	{
		//邦信分快捷版
		$productInfo = [
			2101 => '邦信分快捷版--评分类字段',
			2102 => '邦信分快捷版--统计类字段',
		];
		
		//金盾非分流产品
		$productModel = new ProductModel();
		$productInfoExtra = $productModel->field([
			'product.product_id',
			'product_name',
		])
											 ->where(['father_id' => 615])
											 ->select();
		$productInfo      = $productInfo + array_map(function ($product_name) {
				return '金盾--' . $product_name;
			}, array_column($productInfoExtra, 'product_name', 'product_id'));
		$productInfo[603] = '金盾--金盾贷后外呼风险';
		$productInfo[612] = '金盾--金盾贷前';
		$productInfo[613] = '金盾--金盾贷后';
		$productInfo[614] = '金盾--号码风险等级';
		$productInfo[616] = '金盾--风险符号';
		$productInfo[664] = '金盾--号码预警提示';
		
		//邦秒验
		$productModel     = new ProductModel();
		$productInfoExtra = $productModel->field([
			'product_id',
			'product_name',
		])
										 ->where(['father_id' => 200])
										 ->select();
		$productInfo      = $productInfo + array_map(function ($product_name) {
				return '邦秒验--' . $product_name;
			}, array_column($productInfoExtra, 'product_name', 'product_id'));
		$productInfo[801] = '邦秒验--号码状态查询';
		
		//邦秒配
		$productInfo += [
			601 => '邦秒配--单号版',
			604 => '邦秒配--批量版',
			104 => '邦秒配--详单版',
		];
		
		//邦信分详单版
		$productInfo += [
			101 => '邦信分详单版--V1',
			105 => '邦信分详单版--V2',
		];
		
		//邦信分私有云
		$productInfo[501] = '邦信分私有云';
		
		//邦企查
		$productInfo[401] = '邦企查';
		
		
		$productIds = I('get.product_id', '');
		if (empty($productIds)) {
			$option = '';
			foreach ($productInfo as $product_id => $product_name) {
				$option .= "<option value='{$product_id}' selected>{$product_name}</option>";
			}
			
			return $option;
		} else {
			$productIds = explode(',', $productIds);
			if (in_array(251, $productIds)) {
				$productIds[] = 2101;
			}
			
			if (in_array(241, $productIds)) {
				$productIds[] = 2102;
			}
			$option = '';
			foreach ($productInfo as $product_id => $product_name) {
				$selected = in_array($product_id, $productIds) ? 'selected' : '';
				$option   .= "<option value='{$product_id}' {$selected}>{$product_name}</option>";
			}
			
			return $option;
		}
	}
	
	
	/**
	 * 获取客户维度的统计
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/26 17:53
	 *
	 * @return array
	 **/
	public function getCustomerInfo()
	{
		//获取接口请求参数
		$params = $this->getProductParams();
		
		//通过接口获取利润数据
		$data = $this->post(C('LIST_API_URL.profit_customer'), $params);
		
		if (0 != $data['status']) {
			throw new \Exception($data['msg']);
		}
		$data = $data['data'];
		
		
		//获取客户数据
		$customerId2CustomerNameMapping = (new CustomerModel())->where(['is_delete' => 0])
															   ->field([
																   'customer_id',
																   'name',
															   ])
															   ->select();
		$customerId2CustomerNameMapping = array_column($customerId2CustomerNameMapping, 'name', 'customer_id');
		
		
		//过滤并整理数据
		$result = array_filter($data, function (&$item) use ($customerId2CustomerNameMapping) {
			$income_number = $item['income_arr']['number'];
			$income_money  = sprintf('%01.2f', round($item['income_arr']['money'], 2));
			$cost_money    = sprintf('%01.2f', round($item['cost_arr']['money'], 2));
			$profit_money  = sprintf('%01.2f', round($item['profit_arr']['money'], 2));
			$balance_money = sprintf('%01.2f', round($item['balance_arr']['balance'], 2));
			if (0 == $income_number && 0 == $income_money && 0 == $cost_money && 0 == $profit_money) {
				return false;
			}
			$customer_id   = $item['customer_id'];
			$customer_name = $customerId2CustomerNameMapping[$customer_id];
			$item          = compact('customer_id', 'customer_name', 'income_number', 'income_money', 'cost_money', 'profit_money', 'balance_money');
			
			return true;
		});
		$result = array_values($result);
		
		//排序
		$orderField = I('post.sort_field', 'income_money');
		$orderType  = I('post.sort_type', 'desc') == 'asc' ? SORT_ASC : SORT_DESC;
		array_multisort(array_column($result, $orderField), $orderType, $result);
		
		//特殊费用
		$specialTotal = $this->getCustomerSpecialTotal($params['customerIds'], $params['start_date'], $params['end_date']);
		
		$total = [
			'income_number' => array_sum(array_column($result, 'income_number')),
			'income_money'  => sprintf('%01.2f', (array_sum(array_column($result, 'income_money')))),
			'cost_money'    => sprintf('%01.2f', array_sum(array_column($result, 'cost_money'))),
			'profit_money'  => sprintf('%01.2f', (array_sum(array_column($result, 'profit_money')))),
			'balance_money' => sprintf('%01.2f', array_sum(array_column($result, 'balance_money'))),
		];
		
		return compact('total', 'result', 'specialTotal');
	}
	
	/**
	 * 获取特殊消耗数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/6/11 10:49
	 *
	 * @return array
	 **/
	public function getCustomerSpecialInfo()
	{
		$params = $this->getProductParams();
		
		$customerIds = $params['customerIds'];
		$start_date  = $params['start_date'];
		$end_date    = $params['end_date'];
		$start_month = date('Ym', strtotime($start_date));
		$end_month   = date('Ym', strtotime($end_date));
		
		$data = (new CustomerExpendModel())->field([
			'customer_expend.type',
			'money',
			'customer.customer_id',
			'customer.name',
			'customer_expend.start_date',
		])
										   ->join('customer ON customer.customer_id = customer_expend.customer_id')
										   ->where([
											   'customer.customer_id' => ['in', $customerIds],
											   'start_date'           => ['between', [$start_month, $end_month]],
										   ])
										   ->order('start_date')
										   ->select();
		
		return $data;
	}
	
	/**
	 * 获取特殊消耗统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/6/11 14:02
	 *
	 * @param $customerIds array 客户ID
	 * @param $start_date  string 开始日期
	 * @param $end_date    string 截止日期
	 *
	 * @return float
	 **/
	protected function getCustomerSpecialTotal($customerIds, $start_date, $end_date)
	{
		$start_month         = date('Ym', strtotime($start_date));
		$end_month           = date('Ym', strtotime($end_date));
		$customerExpendModel = new CustomerExpendModel();
		
		$specialRecharge = $customerExpendModel->where([
			'customer_id' => ['in', $customerIds],
			'start_date'  => ['between', [$start_month, $end_month]],
			'type'        => 1,
		])
											   ->sum('money');
		$specialConsume  = $customerExpendModel->where([
			'customer_id' => ['in', $customerIds],
			'start_date'  => ['between', [$start_month, $end_month]],
			'type'        => 2,
		])
											   ->sum('money');
		
		return sprintf("%01.2f", $specialConsume - $specialRecharge);
	}
	
}