<?php
/**
 * Created by PhpStorm.
 * User: liuchang
 * Date: 2020/6/18
 * Time: 5:14 PM
 */
namespace Opdata\Controller;

use Common\Controller\AdminController;
use Opdata\Model\ChanneldetailModel;
use Opdata\Model\ProductModel;
use Opdata\Model\ChannelModel;
use Opdata\Model\ApikeyModel;
use Opdata\Model\AccountModel;

class BaseController extends AdminController
{
    public $proName;
    public $channelName;
    public $channelDetail;
    public $apikeyName;
    public $sdate;
    public $edate;
    public $customerInfo;

    public $compareUrl = [
        "1" => '/Stat/DataValid/detail',
        "2" => '/Stat/DataValid/detail',
        "3" => '/Opdata/Index/channelDetail',
    ];

    public $detailUrl = [
        "1" => '/Opdata/Detail/Product',
        "2" => '/Opdata/Detail/Client',
        "3" => '/Opdata/Detail/Channel',
    ];

    public function __construct()
    {
        parent::__construct();

        $proNames = ProductModel::getAll();
        $channelNames = ChannelModel::getAll();
        $apikeyNames = ApikeyModel::getAll();
        $channelDetails = ChanneldetailModel::getAll();

        foreach ( $proNames as $v ){
            $this->proName[$v['pid']] = $v['name'];
        }

        foreach ( $channelNames as $v ){
            $this->channelName[$v['type']] = $v['name'];
        }

        foreach ( $apikeyNames as $v ){
            $this->apikeyName[$v['apikey']] = $v['name'];
        }

        foreach ( $channelDetails as $v ){
            $key = $v['pid'].'-'.$v['channeltype'].'-'.$v['operator_type'].'-'.$v['encrypt_way'];
            $this->channelDetail[$key] = $v["price"];
        }

        $this->channelName['0'] = "缓存";
    }

    public function getDate()
    {
        $this->sdate = I("sdate",  date("Y-m-d"));
        $this->edate = I("edate",  date("Y-m-d"));
        $param = I('get.');
        if( in_array($param['search-way'], [1, 2, 3, 4]) ){
            $return_time = $this->getDateFromParam($param['search-way']);
            $this->sdate = $return_time['start_time'];
            $this->edate = $return_time['end_time'];
        }

        return [
            'sdate' =>$this->sdate,
            'edate' =>$this->edate,
        ];
    }

    public function intVal($data)
    {
        foreach ($data as $k => $v) {

            foreach ($v as $kk=>$vv) {
                $vv['valid_num'] = intval($vv['valid_num']);
                $vv['income'] = intval($vv['income']);
                $vv['cost'] = intval($vv['cost']);
                $vv['profit'] = intval($vv['profit']);

                $data[$k][$kk] = $vv;
            }
        }

        return $data;
    }

    public function getChannelPrice($pid, $channeltype, $operator)
    {
        $pricesClear = "";
        $pricesMd5 = "";
        $pricesSha256 = "";

        //明文
        $key = $pid.'-'.$channeltype.'-'.$operator.'-'.'0';
        if( isset($this->channelDetail[$key]) ){
            $pricesClear = round( $this->channelDetail[$key], 4 );
        }

        //MD5
        $key = $pid.'-'.$channeltype.'-'.$operator.'-'.'1';
        if( isset($this->channelDetail[$key]) ){
            $pricesMd5 = round( $this->channelDetail[$key], 4 );
        }

        //SHA255
        $key = $pid.'-'.$channeltype.'-'.$operator.'-'.'3';
        if( isset($this->channelDetail[$key]) ){
            $pricesSha256 = round( $this->channelDetail[$key], 4 );
        }

        if( $pricesMd5 !== "" || $pricesSha256 !== "" ){
            $prices = $pricesClear.'/'.$pricesMd5.'/'.$pricesSha256;
        }else{
            $prices = $pricesClear;
        }

        return $prices;
    }

    public function getOperatorTypeByName($name)
    {
        return $name == "联通" ? 1 : ($name == "电信" ? 2 : 3);
    }

    public function getOperatorNameByType($type)
    {
        return $type == 1 ? "联通" : ( $type == 2 ? "电信" : "移动" );
    }

    /**
     * 获取最近时间戳
     */
    public function getDateFromParam($param = 0){
        $time_arr = [
            'start_time' => '',
            'end_time' => ''
        ];
        if(empty($param)){
            return $time_arr;
        }
        switch($param){
            case 1;
                $start_time= date("Y-m-d");
                $end_time  = date("Y-m-d");
                $time_arr = [
                    'start_time' => $start_time,
                    'end_time' => $end_time
                ];
                break;
            case 2;
                $start_time = date("Y-m-d",strtotime("-1 day"));
                $end_time  = date("Y-m-d",strtotime("-1 day"));
                $time_arr = [
                    'start_time' => $start_time,
                    'end_time' => $end_time
                ];
                break;
            case 3:
                $start_time = date("Y-m-d",strtotime("-7 day"));
                $end_time = date("Y-m-d", strtotime("-1 day"));
                $time_arr = [
                    'start_time' => $start_time,
                    'end_time' => $end_time
                ];
                break;
            case 4:
                $start_time = date("Y-m-d",strtotime("-1 month"));
                $end_time = date("Y-m-d", strtotime("-1 day"));
                $time_arr = [
                    'start_time' => $start_time,
                    'end_time' => $end_time
                ];
                break;
            default:
                break;
        }

        return $time_arr;
    }

    /**
     * 获取apikey和customer_id的关系
     */
    public function getCustomerInfo(){
        $customer_info = AccountModel::getAccountList(['is_delete'=>0], 'apikey, customer_id');
        $arr = array_column($customer_info, 'customer_id', 'apikey');
        return $arr;
    }
}