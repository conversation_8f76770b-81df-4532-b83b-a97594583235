<?php

namespace Opdata\Controller;

use Opdata\Model\ChanneldetailModel;
use Opdata\Model\ProfitModel;
use Opdata\Model\ProductModel;
use Opdata\Model\ChannelModel;
use Opdata\Model\ApikeyModel;

class IndexController extends BaseController
{
    public function Channel()
    {
        $dateInfo = $this->getDate();
        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [];
        foreach ($data as $k => $v) {

            if( 0 == $v['channeltype'] ){
                continue;
            }

            $channel = $this->channelName[$v['channeltype']];
            $pid = $this->proName[$v['pid']];

            $result[$channel][$pid]['valid_num'] += $v['valid_num'];
            $result[$channel][$pid]['income'] += $v['income'];
            $result[$channel][$pid]['cost'] += $v['cost'];
            $result[$channel][$pid]['profit'] += $v['profit'];
        }

        //生成合计
        $result = $this->getSum($result);

        //排序
        $result = $this->sortByValidNum($result);
        $result = $this->intVal($result);

        $resultTable = $this->drowTableChannel($result);

        $this->assign('channelInfo', $resultTable);

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    public function Client()
    {
        $dateInfo = $this->getDate();
        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [];
        foreach ($data as $k => $v) {
            $apikey = isset($this->apikeyName[$v['apikey']]) ? $this->apikeyName[$v['apikey']] : $v['apikey'];
            $pid = $this->proName[$v['pid']];

            $result[$apikey][$pid]['valid_num'] += $v['valid_num'];
            $result[$apikey][$pid]['income'] += $v['income'];
            $result[$apikey][$pid]['cost'] += $v['cost'];
            $result[$apikey][$pid]['profit'] += $v['profit'];

            //计算缓存的量
            if( 0 == $v['channeltype'] ){
                $result[$apikey][$pid]['cache'] += $v['valid_num'];
            }
        }

        //计算缓存占比
        foreach ( $result as $k=>$v ){
            foreach ( $v as $kk => $vv ){
                if( 0 == $vv['cache'] || 0 == $vv['valid_num'] ){
                    $result[$k][$kk]['cacheRatio'] = "0%";
                }else{
                    $cacheRatio = (round($vv['cache']/$vv['valid_num'], 4)*100)."%";
                    $result[$k][$kk]['cacheRatio'] = $cacheRatio;
                }
            }
        }

        //生成合计
        $result = $this->getSum($result);

        //排序
        $result = $this->sortByValidNum($result);
        $result = $this->intVal($result);

        //获取客户信息
        $this->customerInfo = $this->getCustomerInfo();

        $resultTable = $this->drowTablePro($result, 2);

        $this->assign('clientInfo', $resultTable);

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    public function Product()
    {
        $dateInfo = $this->getDate();
        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [];
        foreach ($data as $k => $v) {
            $pid = $this->proName[$v['pid']];
            $operator = $v['operator'] == 1 ? "联通" : ($v['operator'] == 2 ? "电信" : "移动");

            $result[$pid][$operator]['valid_num'] += $v['valid_num'];
            $result[$pid][$operator]['income'] += $v['income'];
            $result[$pid][$operator]['cost'] += $v['cost'];
            $result[$pid][$operator]['profit'] += $v['profit'];

            //计算缓存的量
            if( 0 == $v['channeltype'] ){
                $result[$pid][$operator]['cache'] += $v['valid_num'];
            }
        }

        //计算缓存占比
        foreach ( $result as $k=>$v ){
            foreach ( $v as $kk => $vv ){
                if( 0 == $vv['cache'] || 0 == $vv['valid_num'] ){
                    $result[$k][$kk]['cacheRatio'] = "0%";
                }else{
                    $cacheRatio = (round($vv['cache']/$vv['valid_num'], 4)*100)."%";
                    $result[$k][$kk]['cacheRatio'] = $cacheRatio;
                }
            }
        }

        //生成合计
        $result = $this->getSum($result);

        //排序
        $result = $this->sortByValidNum($result);

        $result = $this->intVal($result);

        $resultTable = $this->drowTablePro($result, 1);

        $this->assign('productInfo', $resultTable);

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    public function ProductChannel()
    {
        $dateInfo = $this->getDate();
        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $channelArray = [
            "数盒魔方",
            "瑞森玛雅",
            "数盾",
            "大有",
            "自有数据",
            "金盾",
            "创蓝",
            "诚智天扬",
            "品钛",
            "领地",
            "聚合",
        ];

        $result = [];
        foreach ($data as $k => $v) {
            $pid = $v['pid'];
            $operator = $v['operator'];
            $channeltype = $v['channeltype'];

            $result[$pid][$operator][$channeltype]['valid_num'] += $v['valid_num'];
            $result[$pid][$operator][$channeltype]['income'] += $v['income'];
            $result[$pid][$operator][$channeltype]['cost'] += $v['cost'];
            $result[$pid][$operator][$channeltype]['profit'] += $v['profit'];
        }

        $resultPriceAndNum = [];
        foreach ( $result as $pid=>$pv ){
            $pName = $this->proName[$pid];
            $resultPriceAndNum[$pName]["移动"] = [];
            $resultPriceAndNum[$pName]["联通"] = [];
            $resultPriceAndNum[$pName]["电信"] = [];

            foreach ( $pv as $operator => $ov ){
                foreach ( $ov as $channeltype => $cv ){
                    if( $channeltype == 0 ){
                        continue;
                    }

                    $channel = $this->channelName[$channeltype];
                    $cItem['valid_num'] = $cv["valid_num"];
                    $cItem['price'] = $this->getChannelPrice($pid, $channeltype, $operator);
                    if( "" === $cItem['price'] ){
                        continue;
                    }

                    $operatorName = $operator == 1 ? "联通" : ($operator == 2 ? "电信" : "移动");

                    $resultPriceAndNum[$pName][$operatorName][$channel] = $cItem;
                }
            }
        }

        //补充所有渠道
        foreach ( $resultPriceAndNum as $pname=>$pv ){
            $pid = array_search($pname, $this->proName);
            foreach ( $pv as $oname => $ov ){
                //获取该产品下 所有渠道
                $operator = $oname == "联通" ? 1 : ($oname == "电信" ? 2 : 3);
                $allChannel = $this->getChannelsByPidAndOperator($pid, $operator);

                $hc = array_keys($ov);
                $nhc = array_keys($allChannel);

                $rc = array_diff($nhc, $hc);

                foreach ( $rc as $rcv ){
//                    if( round($allChannel[$rcv]['price'], 4) == 0 ){
//                        continue;
//                    }
                    $channeltype = array_search($rcv, $this->channelName);
                    $resultPriceAndNum[$pname][$oname][$rcv]['valid_num'] = 0;
                    $resultPriceAndNum[$pname][$oname][$rcv]['price'] = $this->getChannelPrice($pid, $channeltype, $operator);
                }
            }
        }

        //排序
        $resultPriceAndNum = $this->sortFroProductChannel($resultPriceAndNum);

        //增加排序标识
        $resultPriceAndNum = $this->addSortLable($resultPriceAndNum);

        $resultTable = $this->drowTableProductChannel($resultPriceAndNum, $channelArray);
        $channelThInfo = $this->getChannelTh($channelArray);

        $this->assign('channelThInfo', $channelThInfo);
        $this->assign('productInfo', $resultTable);

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    private function getChannelsByPidAndOperator($pid, $operator)
    {
        $channels = ChanneldetailModel::getChannelsByPidAndOperator($pid, $operator);
        $result = [];
        foreach ( $channels as $k=>$v ){
            if( !isset($this->channelName[$k]) ){
                continue;
            }
            $result[ $this->channelName[$k] ] = $v;
        }

        return $result;
    }

    private function sortFroProductChannel($data)
    {
        foreach ( $data as $pk=>$pv ){
            foreach ( $pv as $ok=>$ov ){

                $itemKeys = array_column($ov, "valid_num");
                array_multisort($itemKeys, SORT_DESC, $ov);

                $data[$pk][$ok] = $ov;
            }
        }

        return $data;
    }

    private function addSortLable($data)
    {
        foreach ( $data as $pk=>$pv ){
            foreach ( $pv as $ok=>$ov ){
                $i = 1;
                foreach ( $ov as $ck => $cv )
                {
                    $cv['sort'] = $i;
                    $data[$pk][$ok][$ck] = $cv;

                    $i++;
                }
            }
        }

        return $data;
    }



    private function getSum($data)
    {
        $validNum = 0;
        $income = 0;
        $cost = 0;
        $profit = 0;
        $cache = 0;

        foreach ($data as $k => $v) {

            $ivalidNum = 0;
            $iincome = 0;
            $icost = 0;
            $iprofit = 0;
            $icache = 0;

            foreach ($v as $vv) {
                $validNum += $vv['valid_num'];
                $income += $vv['income'];
                $cost += $vv['cost'];
                $profit += $vv['profit'];
                $cache += $vv['cache'];

                $ivalidNum += $vv['valid_num'];
                $iincome += $vv['income'];
                $icost += $vv['cost'];
                $iprofit += $vv['profit'];
                $icache += $vv['cache'];
            }

            $sumItem = [
                "valid_num" => $ivalidNum+0.1,
                "income" => $iincome,
                "cost" => $icost,
                "profit" => $iprofit,
                "cache" => $icache,
                "cacheRatio" => $icache > 0 ? (round($icache/$ivalidNum, 4)*100)."%" : "0%",
            ];

            $v["合计"] = $sumItem;
            $data[$k] = $v;
        }

        $sum = [
            "合计" => [
                '--' => [
                    "valid_num" => $validNum+0.1,
                    "income" => $income,
                    "cost" => $cost,
                    "profit" => $profit,
                    "cache" => $cache,
                    "cacheRatio" => $cache > 0 ? (round($cache/$validNum, 4)*100)."%" : "0%",
                ]
            ]
        ];

        $result = $sum + $data;

        return $result;
    }

    //type 1=产品维度，2=客户维度，3=羽乐科技自有数据
    private function drowTablePro($data, $type = 1)
    {
        $table = "";
        foreach ($data as $pk => $pv) {
            $table .= $this->getTr($pk, $pv, $type);
        }

        return $table;
    }

    private function getTr($name, $data, $type = 1)
    {
        $i = 0;
        $trs = "";
        foreach ($data as $ok => $ov) {

            //如果是合计列，则调整背景色
            if( "合计" == $name ){
                $tr = "<tr style=\"background:cadetblue; color:white\">";
            }else{
                $tr = "<tr>";
            }
            $tds = "";

            //绘制第一列
            if (0 == $i) {
                $count = count($data);
                if( "合计" == $name ){
                    $tds .= "<td class='haveRowSpan' data-rows='{$count}' rowspan='{$count}'>$name</td>";
                }else{
                    if ( 1 == $type ) {
                        $pid = array_search($name, $this->proName);
                        $tds .= "<td class='haveRowSpan' data-rows='{$count}' rowspan='{$count}'>"
                            .$this->getDetailAStr($name, $type, $pid)
                            .$this->getCompareAStr($type, $pid)."</td>";
                    } else {
                        $apikey = array_search($name, $this->apikeyName);
                        $customer_id = isset($this->customerInfo[$apikey]) ? $this->customerInfo[$apikey] : '';
                        $tds .= "<td class='haveRowSpan' data-rows='{$count}' rowspan='{$count}'>"
                            .$this->getDetailAStr($name, $type, $apikey)
                            .$this->getCompareAStr($type, $customer_id)."</td>";
                    }
                }
            }

            //如果是合计列，则 字体标红
            if( '合计' == $ok ){
                $color = "<span style='font-weight:bold'>";
                $tds .= "<td>" .$color. $ok . "</td>";
            }elseif( '--' == $ok ){
                $color = "";
                $tds .= "<td>" .$color. $ok . "</td>";
            }else{
                $color = "<span style='color: grey'>";
                $tds .= "<td class='canHide'>" .$color. $ok . "</td>";
            }

            $tds .= "<td>" .$color. $ov['valid_num'] . "</td>";
            $tds .= "<td>" .$color. intval($ov['cache']) . "</td>";
            $tds .= "<td>" .$color. $ov['cacheRatio'] . "</td>";
            $tds .= "<td>" .$color. $ov['income'] . "</td>";
            $tds .= "<td>" .$color. $ov['cost'] . "</td>";
            $tds .= "<td>" .$color. $ov['profit'] . "</td>";

            $tr .= $tds;
            $tr .= "</tr>";
            $trs .= $tr;

            $i++;
        }

        return $trs;
    }


    //type1-表示产品对比，2表示客户对比，3表示渠道对比
    private function getCompareAStr($type, $id)
    {
        $str = "<a ";

        $href = "href='".$this->compareUrl[$type]."?contract_status=-1&start_time={$this->sdate}&end_time={$this->edate}";
        if( 1 == $type ){
            $href .= "&product_id={$id}";
        }elseif ( 2 == $type ){
            $href .= "&customer_id={$id}";
        }
        $href .= "'>对比";

        $str .= $href;
        $str .= '</a>';

        return $str;
    }

    //type1-表示产品对比，2表示客户对比，3表示渠道对比
    private function getDetailAStr($name, $type, $id)
    {
        $str = "<a ";

        $href = "href='".$this->detailUrl[$type]."?sdate={$this->sdate}&edate={$this->edate}";
        if( 1 == $type ){
            $href .= "&pid={$id}";
        }elseif ( 2 == $type ){
            $href .= "&apikey={$id}";
        }
        $href .= "'>{$name}";

        $str .= $href;
        $str .= '</a>';

        return $str;
    }

    private function drowTableChannel($data)
    {
        $table = "";
        foreach ( $data as $pk=>$pv ){
            $i =0;
            foreach ( $pv as $ok=>$ov ){
                if( "合计" == $pk ){
                    $tr = "<tr style=\"background:cadetblue; color:white\">";
                }else{
                    $tr = "<tr>";
                }
                $tds = "";

                if( 0 == $i ){
                    $count = count($pv);
                    if( "合计" == $pk ) {
                        $tds .= "<td class='haveRowSpan' data-rows='{$count}' rowspan='{$count}'>$pk</td>";
                    }else{
                        $channeltyp = array_search($pk, $this->channelName);
                        $tds .= "<td class='haveRowSpan' data-rows='{$count}' rowspan='{$count}'>
<a href=\"/Opdata/Detail/Channel?channeltype={$channeltyp}&sdate={$this->sdate}&edate={$this->edate}\">$pk</a>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<a href=\"/Opdata/Index/channelDetail?channel_id={$channeltyp}&start_time={$this->sdate}&end_time={$this->edate}\">对比</a>
</td>";
                    }
                }

                //如果是合计列，则 字体标红
                if( '合计' == $ok ){
                    $color = "<span style='font-weight:bold'>";
                    $tds .= "<td>".$color.$ok."</td>";
                }elseif ('--' == $ok){
                    $color = "";
                    $tds .= "<td>".$color.$ok."</td>";
                }else{
                    $color = "<span style='color: grey'>";
                    $tds .= "<td class='canHide'>".$color.$ok."</td>";
                }

                $tds .= "<td>".$color.$ov['valid_num']."</td>";
                $tds .= "<td>".$color.intval($ov['cost'])."</td>";

                $tr .= $tds;
                $tr .= "</tr>";
                $table .= $tr;

                $i++;
            }
        }

        return $table;
    }

    private function drowTableProductChannel($data, $channelArray)
    {
        $table = "";
        foreach ($data as $pk => $pv) {
            $table .= $this->getProductChannelTr($pk, $pv, $channelArray);
        }

        return $table;
    }

    private function getProductChannelTr($name, $data, $channelArray)
    {
        $i = 0;
        $trs = "";
        foreach ($data as $ok => $ov) {
            $tr = "<tr>";
            $tds = "";

            //绘制第一列
            if (0 == $i) {
                $count = count($data);
                $tds .= "<td rowspan='{$count}'>$name</td>";

            }

            $tds .= "<td>".$ok."</td>";
            $tds .= $this->getProductChannelTd($ov, $channelArray);

            $tr .= $tds;
            $tr .= "</tr>";
            $trs .= $tr;

            $i++;
        }

        return $trs;
    }

    private function getProductChannelTd($data, $channelArray)
    {
        $tds = "";
        $i = 1;
        foreach ( $channelArray as $v ){
            if( isset($data[$v]) ){
                if( 1 == $data[$v]['sort'] ){
                    $backColeor = "background: red; color:white";
                }elseif ( 2 == $data[$v]['sort'] ){
                    $backColeor = "background: green; color:white";
                }else{
                    $backColeor = "";
                }
                $tds .= "<td class='canHide'>".$data[$v]['valid_num']."</td>"."<td style='{$backColeor}'>".$data[$v]['price']."</td>";

                $i++;
            }else{
                $tds .= "<td class='canHide'></td><td></td>";
            }
        }

        return $tds;
    }

    private function getChannelTh($data)
    {
        $th = "";

        foreach ( $data as $k=>$v ){
            $th .= "<th class='canHide'>有效量</th><th>$v</th>";
        }

        return $th;
    }

    private function sortByValidNum($result, $sortField="valid_num")
    {
        $result = array_map(function ($item, $sortField = "valid_num"){

            $itemKeys = array_column($item, $sortField);
            array_multisort($itemKeys, SORT_DESC, $item);

            return $item;

        }, $result);

        $first['合计'] = array_shift($result);
        $resultKeys = array_column( array_column($result, "合计"), $sortField );
        array_multisort($resultKeys, SORT_DESC, $result);

        $result = $first + $result;

        return $result;
    }

    /**
     * 获取渠道详情
     */
    public function channelDetail()
    {
        $data = I('get.');
        $where = $input = [];
        //$input['start_time'] = !empty($data['start_time']) ? $data['start_time'] : date('Y-m-d', strtotime('-1 month'));

        $input['start_time'] = !empty($data['end_time']) ? date('Y-m-d', strtotime('-1 month', strtotime($data['end_time']))) : date('Y-m-d', strtotime('-1 month'));
        $input['end_time'] = !empty($data['end_time']) ? $data['end_time'] : date('Y-m-d');
        $input['channel_id'] =  !empty($data['channel_id']) ? $data['channel_id'] : 9999;
        $input['apikey'] = $data['apikey'];
        $input['product_id'] = $data['product_id'];

        $where['date_at'][] = ['EGT',$input['start_time']];
        $where['date_at'][] = ['ELT', $input['end_time']];
        $where['channeltype'] = $input['channel_id'];

        if(!empty($input['apikey'])){
            $where['apikey'] = $input['apikey'];
        }
        if(!empty($input['product_id'])){
            $where['pid'] = $input['product_id'];
        }

        $profit_model = new ProfitModel();
        $field = 'apikey, channeltype, pid, valid_num, cost, date_at';
        $info = $profit_model->where($where)->field($field)->order('date_at desc')->select();

        //获取产品名称
        $product_info = (new ProductModel())->field('pid,name')->select();
        $product_arr = array_column($product_info, 'name', 'pid');

        $channel_info = (new ChannelModel())->field('type,name')->select();
        $channel_arr = array_column($channel_info, 'name', 'type');

        $apikey_info = (new ApikeyModel())->field('apikey,name')->select();
        $apikey_arr = array_column($apikey_info, 'name', 'apikey');

        $arr = [];
        array_walk($info, function($v) use (&$arr, $product_arr, $channel_arr, $apikey_arr){
            $v['channel_name'] = isset($channel_arr[$v['channeltype']]) ? $channel_arr[$v['channeltype']] : '';
            $v['p_name'] = isset($product_arr[$v['pid']]) ? $product_arr[$v['pid']] : '';
            $v['apikey_name'] = isset($apikey_arr[$v['apikey']]) ? $apikey_arr[$v['apikey']] : '';
            $arr[$v['date_at']][] = $v;
        });
        $res = [];
        $total_num = 0;
        $total_sum = 0;
       foreach($arr as $key=>$value){
           $num = array_sum(array_column($value, 'valid_num'));
           $sum = array_sum(array_column($value, 'cost'));
           $total_num += $num;
           $total_sum += $sum;
           $res[$key]['data'] = $value;
           $res[$key]['num'] = $num;
           $res[$key]['sum'] = $sum;
       }
        $apikey_option = "<option value=''>选择客户</option>";
        foreach($apikey_arr as $k=>$v){
            if($k == $input['apikey']){
                $apikey_option .= "<option selected value='".$k."'>".$v."</option>";
            }else{
                $apikey_option .= "<option value='".$k."'>".$v."</option>";
            }

        }

        $product_option = "<option value=''>选择产品</option>";
        foreach($product_arr as $k=>$v){
            if($k ==  $input['product_id']){
                $product_option .= "<option selected value='".$k."'>".$v."</option>";
            }else{
                $product_option .= "<option value='".$k."'>".$v."</option>";
            }

        }

        $channel_option = "<option value=''>选择渠道</option>";
        foreach($channel_arr as $k=>$v){
            if($k ==  $input['channel_id']){
                $channel_option .= "<option selected value='".$k."'>".$v."</option>";
            }else{
                $channel_option .= "<option value='".$k."'>".$v."</option>";
            }

        }

       $res['total_num'] = $total_num;
       $res['total_sum'] = $total_sum;
       $this->assign('product_option', $product_option);
       $this->assign('channel_option', $channel_option);
       $this->assign('apikey_option', $apikey_option);
       $this->assign('input', $input);
       $this->assign('res', $res);
       $this->display();
    }
}