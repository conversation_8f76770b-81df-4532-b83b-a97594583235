<?php

namespace Opdata\Controller;

use Opdata\Model\ChanneldetailModel;
use Opdata\Model\ProfitModel;

class SelfController extends BaseController
{
    public function SelfWithClient()
    {
        $gchanneltype = I("channeltype", "0");
        $gpid = I("pid", "213");

        $dateInfo = $this->getDate();
        $data = ProfitModel::getProInfoByChannelAndType($dateInfo['sdate'], $dateInfo['edate'], $gchanneltype, $gpid);
//        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [];
        foreach ($data as $v) {

            $operator = $v['operator'] == 1 ? "联通" : ($v['operator'] == 2 ? "电信" : "移动");
            $clientName = isset($this->apikeyName[$v['apikey']]) ? $this->apikeyName[$v['apikey']] : $v['apikey'];

            $result[$clientName]['valid_num'] += $v['valid_num'];
            $result[$clientName]['income'] += $v['income'];
            $result[$clientName]['operator'][$operator] += $v['valid_num'];
        }

        //获取节约的成本
        $result = $this->getSelfClientSaveMoney($gpid, $result);

        //生成合计
        $result = $this->getSelfClientSum($result);

        //排序
        $result = $this->sortByField($result, "money");

        $resultTable = $this->drowTablePro($result, 2);

        $this->assign('selfClientInfo', $resultTable);

        $pname = $this->proName[$gpid];
        $cname = $this->channelName[$gchanneltype];
        $this->assign('pc', ' ('.$pname.'-'.$cname.') ');

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    public function Self()
    {
        $dateInfo = $this->getDate();
        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [
            "自有数据" => [],
            "金盾" => [],
            "缓存" => [],
        ];
        $resultTotal = [];
        foreach ($data as $v) {

            $pname = $this->proName[$v['pid']];
            $resultTotal[$pname] += $v['valid_num'];

            if (!in_array($v['channeltype'], [0, 21, 24])) {
                continue;
            }

            $operator = $v['operator'] == 1 ? "联通" : ($v['operator'] == 2 ? "电信" : "移动");
            $channeltype = $this->channelName[$v['channeltype']];

            $result[$channeltype][$pname]['valid_num'] += $v['valid_num'];
            $result[$channeltype][$pname]['operator'][$operator] += $v['valid_num'];
        }

        //补充 总数
        foreach ( $result as $k=>$v ){
            foreach ( $v as $pk=>$pv ){
                $total = $resultTotal[$pk];
                $result[$k][$pk]['total'] = $total;
            }
        }

        //获取节约的成本
        $result = $this->getSaveMoney($result);

        //生成合计
        $result = $this->getSelfSum($result, $resultTotal);

        //排序
        $result = $this->sortSelfByMoney($result, "money");

        $resultTable = $this->drowTablePro($result);

        $this->assign('selfInfo', $resultTable);

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    private function getSelfClientSaveMoney($pid, $result)
    {
        foreach ($result as $k => $v) {
            //获取最便宜的渠道
            $money = $this->getSaveMoneyBypid($pid, $v['operator']);

            $result[$k]['money'] = round($money, 1);
        }

        return $result;
    }

    private function getSaveMoney($result)
    {
        foreach ($result as $k => $v) {
            foreach ($v as $pk => $pv) {
                //获取最便宜的渠道
                $pid = array_search($pk, $this->proName);
                $money = $this->getSaveMoneyBypid($pid, $pv['operator']);

                $result[$k][$pk]['money'] = round($money, 1);
            }
        }

        return $result;
    }

    private function getSaveMoneyBypid($pid, $data)
    {
        $money = 0;
        foreach ($data as $ok => $ov) {
            $operator = $ok == "联通" ? 1 : ($ok == "电信" ? 2 : 3);
            $mostCheapChannel = ChanneldetailModel::getMostCheapChannel($pid, $operator);
            if (isset($mostCheapChannel['price'])) {
                $money += $ov * $mostCheapChannel['price'];
            } else {
                $money += 0;
            }
        }

        return $money;
    }

    private function getSelfClientSum($data)
    {
        $validNum = 0;
        $income = 0;

        foreach ( $data as $v )
        {
            $validNum += $v['valid_num'];
            $income += $v['money'];
        }

        $data['合计']['valid_num'] = $validNum;
        $data['合计']['money'] = $income;

        return $data;
    }

    private function getSelfSum($data, $pTotalData)
    {
        $validNum = 0;
        $money = 0;

        foreach ($data as $k => $v) {

            $ivalidNum = 0;
            $imoney = 0;
            $itotal = 0;

            foreach ($v as $vv) {
                $validNum += $vv['valid_num'];
                $money += $vv['money'];

                $ivalidNum += $vv['valid_num'];
                $imoney += $vv['money'];
                $itotal += $vv['total'];
            }

            $sumItem = [
                "valid_num" => $ivalidNum,
                "money" => $imoney+0.1,
                "total" => $itotal,
            ];

            $v["合计"] = $sumItem;
            $data[$k] = $v;
        }

        $pTotal = 0;
        foreach ( $pTotalData as $k=>$v ){
            $pTotal += $v;
        }

        $sum = [
            "合计" => [
                '--' => [
                    "valid_num" => $validNum+0.1,
                    "money" => $money,
                    "total" => $pTotal,
                ]
            ]
        ];

        $result = $sum + $data;

        return $result;
    }

    private function drowTablePro($data, $type = 1)
    {
        $table = "";
        foreach ($data as $pk => $pv) {
            if( 1 == $type ){
                $table .= $this->getSelfTr($pk, $pv);
            }else{
                $table .= $this->getSelfClientTr($pk, $pv);
            }
        }

        return $table;
    }

    private function getSelfTr($name, $data)
    {
        $i = 0;
        $trs = "";
        foreach ($data as $ok => $ov) {

            //如果是合计列，则调整背景色
            if( "合计" == $name ){
                $tr = "<tr style=\"background:cadetblue; color:white\">";
            }else{
                $tr = "<tr>";
            }
            $tds = "";

            //绘制第一列
            if (0 == $i) {
                $count = count($data);
                $tds .= "<td data-rows='{$count}' rowspan='{$count}'>$name</td>";
            }

            //如果是合计列，则 字体标红

            if( '合计' == $ok ){
                $backColeor = "color:#FF5722";
            }else if( '--' == $ok  ){
                $backColeor = "background: cadetblue; color:white";
            }else{
                $backColeor = "";
            }

            $channeltype = array_search($name, $this->channelName);
            $pid = array_search($ok, $this->proName);

            if( $ok != "合计" && $ok != "--" ){
                $tds .= "<td style='{$backColeor}'><a href=\"/Opdata/Self/SelfWithClient?channeltype={$channeltype}&pid={$pid}&sdate={$this->sdate}&edate={$this->edate}\">{$ok}</a></td>";
            }else{
                $tds .= "<td style='{$backColeor}'>" . $ok . "</td>";
            }
            $tds .= "<td style='{$backColeor}'>" . $ov['total'] . "</td>";
            $tds .= "<td style='{$backColeor}'>" . $ov['valid_num'] . "</td>";
            $tds .= "<td style='{$backColeor}'>" . $ov['money'] . "</td>";

            $tr .= $tds;
            $tr .= "</tr>";
            $trs .= $tr;

            $i++;
        }

        return $trs;
    }

    private function getSelfClientTr($k, $data)
    {
        if( "合计" == $k ){
            $tr = "<tr style=\"background:cadetblue; color:white\">";
        }else{
            $tr = "<tr>";
        }

        $tds = "";
        $tds .= "<td>".$k . "</td>";
        $tds .= "<td>".$data['valid_num'] . "</td>";
        $tds .= "<td>".$data['money'] . "</td>";

        $tr .= $tds;
        $tr .= "</tr>";

        return $tr;
    }

    private function sortSelfByMoney($result, $field)
    {
        foreach ( $result as $k =>$v ){
            $afterSort = $this->sortByField($v, $field);
            $result[$k] = $afterSort;
        }

        return $result;
    }

    private function sortByField($result, $field)
    {
        $itemKeys = array_column($result, $field);
        array_multisort($itemKeys, SORT_DESC, $result);

        return $result;
    }
}