<?php

namespace Opdata\Controller;

use Opdata\Model\ProfitModel;
use Opdata\Model\UserPriceModel;

class DetailController extends BaseController
{
    public function Client()
    {
        $dateInfo = $this->getDate();

        $gapikey = I("apikey",  "78093b56a19a1a6a2a34be5d4518226d");
        $gClientName = $this->apikeyName[$gapikey];

        //获取基础数据
        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        //处理基础数据
        $result = [];
        foreach ($data as $k=>$v){
            $pid = $this->proName[ $v['pid'] ];
            $operator = $v['operator'] == 1 ? "联通" : ( $v['operator'] == 2 ? "电信" : "移动" );
            $apikey = $this->apikeyName[ $v['apikey'] ];
            $channel = $this->channelName[ $v['channeltype'] ];

            $result[$apikey][$pid][$operator]['valid_num'] += $v['valid_num'];
            $result[$apikey][$pid][$operator]['income'] += $v['income'];
            $result[$apikey][$pid][$operator]['cost'] += $v['cost'];
            $result[$apikey][$pid][$operator]['profit'] += $v['profit'];

            $result[$apikey][$pid][$operator]['channel'][$channel] += $v['valid_num'];
        }

        $result = isset($result[$gClientName]) ? $result[$gClientName] : [];

        $haveChannelInfo = $this->getHaveChannelTh($result);

        //生成合计
        $result = $this->getSum($result, $haveChannelInfo['channelArray']);

        //排序
        $result = $this->orderByValidNum($result);

        $result = $this->intVal($result);

        //增加 客户签约价格
        $result = $this->getClientPrice($gapikey, $result);

        $resultTable = $this->drowTablePro($gClientName, $result, $haveChannelInfo['channelArray'], 2);

        $this->assign('channelInfo', $haveChannelInfo['channelTh']);
        $this->assign('clientInfo', $resultTable);

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        //下拉框
        $this->assign("apikeyName", makeOption($this->apikeyName, $gapikey));

        $this->display();
    }

    private function getClientPrice($apikey, $data)
    {
        $result = [];

        foreach ( $data as $pname=>$pv ){
            if( '合计' == $pname ){
                $result[$pname] = $pv;
                continue;
            }

            $pid = array_search($pname, $this->proName);
            foreach ( $pv as $operator => $ov ){
                if( '合计' == $operator ){
                    $result[$pname][$operator] = $ov;
                    continue;
                }

                $operatorType = $this->getOperatorTypeByName($operator);
                $userPrice = UserPriceModel::getPriceByApikeyAndPidAndOperator($apikey, $pid, $operatorType);


                $result[$pname][$operator.'('.$userPrice.')'] = $ov;
            }
        }

        return $result;
    }

	public function Product()
	{
        $dateInfo = $this->getDate();
        $gpid = I("pid",  201);
        $gpname = $this->proName[$gpid];

		$data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

		$result = [];
		foreach ($data as $k=>$v){
            $pid = $this->proName[ $v['pid'] ];
            $operator = $v['operator'] == 1 ? "联通" : ( $v['operator'] == 2 ? "电信" : "移动" );
            $apikey = $this->apikeyName[ $v['apikey'] ];
            $channel = $this->channelName[ $v['channeltype'] ];

            $result[$pid][$apikey][$operator]['valid_num'] += $v['valid_num'];
            $result[$pid][$apikey][$operator]['income'] += $v['income'];
            $result[$pid][$apikey][$operator]['cost'] += $v['cost'];
            $result[$pid][$apikey][$operator]['profit'] += $v['profit'];

            $result[$pid][$apikey][$operator]['channel'][$channel] += $v['valid_num'];
        }

        $result = isset($result[$gpname]) ? $result[$gpname] : [];

		$haveChannelInfo = $this->getHaveChannelTh($result);

        //生成合计
        $result = $this->getSum($result, $haveChannelInfo['channelArray']);

        //排序
        $result = $this->orderByValidNum($result);

        $result = $this->intVal($result);

        $resultTable = $this->drowTablePro($gpname, $result, $haveChannelInfo['channelArray'], 1);

        $this->assign('channelInfo', $haveChannelInfo['channelTh']);
        $this->assign('productInfo', $resultTable);

        //下拉框
        $this->assign("proName", makeOption($this->proName, $gpid));

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
	}

    public function Channel()
    {
        $dateInfo = $this->getDate();
        $gtype = I("channeltype",  16);
        $gcname = $this->channelName[$gtype];

        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [];
        foreach ($data as $k=>$v){
            $pid = $this->proName[ $v['pid'] ];
            $channel = $this->channelName[ $v['channeltype'] ];
            $apikey = $this->apikeyName[ $v['apikey'] ];

            $result[$channel][$pid][$apikey]['valid_num'] += $v['valid_num'];
            $result[$channel][$pid][$apikey]['cost'] += $v['cost'];
        }

        $result = isset($result[$gcname]) ? $result[$gcname] : [];

        $haveChannelInfo = $this->getHaveChannelTh($result);

        //生成合计
        $result = $this->getSum($result, $haveChannelInfo['channelArray']);

        //排序
        $result = $this->orderByValidNum($result);

        $result = $this->intVal($result);

        $resultTable = $this->drowTableChannel($gcname, $result);

        $this->assign('channelInfo', $resultTable);

        //下拉框
        $this->assign("channelName", makeOption($this->channelName, $gtype));

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    public function ChannelProduct()
    {
        $dateInfo = $this->getDate();
        $gtype = I("channeltype",  16);
        $gcname = $this->channelName[$gtype];

        $data = ProfitModel::getProInfo($dateInfo['sdate'], $dateInfo['edate']);

        $result = [];
        foreach ($data as $k=>$v){
            $pid = $this->proName[ $v['pid'] ];
            $channel = $this->channelName[ $v['channeltype'] ];
            $operator = $v['operator'] == 1 ? "联通" : ( $v['operator'] == 2 ? "电信" : "移动" );

            $result[$channel][$pid][$operator]['valid_num'] += $v['valid_num'];
            $result[$channel][$pid][$operator]['cost'] += $v['cost'];
            $result[$channel][$pid][$operator]['price'] = $this->getChannelPrice($v['pid'], $v['channeltype'], $v['operator']);

        }

        $result = isset($result[$gcname]) ? $result[$gcname] : [];

        $haveChannelInfo = $this->getHaveChannelTh($result);

        //生成合计
        $result = $this->getSum($result, $haveChannelInfo['channelArray']);

        //排序
        $result = $this->orderByValidNum($result);

        $result = $this->intVal($result);

        $resultTable = $this->drowTableChannel($gcname, $result);

        $this->assign('channelInfo', $resultTable);

        //下拉框
        $this->assign("channelName", makeOption($this->channelName, $gtype));

        $this->assign('sdate', $dateInfo['sdate']);
        $this->assign('edate', $dateInfo['edate']);

        $this->display();
    }

    private function orderByValidNum($result, $sortField = "valid_num")
    {
        $result = array_map(function ($item, $sortField = "valid_num"){

            $itemKeys = array_column($item, $sortField);
            array_multisort($itemKeys, SORT_DESC, $item);

            return $item;

        }, $result);

        $first['合计'] = array_shift($result);
        $resultKeys = array_column( array_column($result, "合计"), $sortField );
        array_multisort($resultKeys, SORT_DESC, $result);

        $result = $first + $result;

        return $result;
    }

	private function getSum($data, $channelArray)
    {
        $validNum = 0;
        $income = 0;
        $cost = 0;
        $profit = 0;
        $channelSum = $channelArray;


        foreach ( $data as $k=>$v){

            $ivalidNum = 0;
            $iincome = 0;
            $icost = 0;
            $iprofit = 0;
            $ichannelSum = $channelArray;

            foreach ($v as $vv){
                $validNum += $vv['valid_num'];
                $income += $vv['income'];
                $cost += $vv['cost'];
                $profit += $vv['profit'];

                //渠道统计
                foreach ( $vv['channel'] as $chk=>$chv ){
                    $channelSum[$chk] += $chv;
                    $ichannelSum[$chk] += $chv;
                }

                $ivalidNum += $vv['valid_num'];
                $iincome += $vv['income'];
                $icost += $vv['cost'];
                $iprofit += $vv['profit'];
            }

            $sumItem = [
                "valid_num" => $ivalidNum+0.1,
                "income" => $iincome,
                "cost" => $icost,
                "profit" => $iprofit,
                "channel" => $ichannelSum,
            ];

            $v["合计"] = $sumItem;

            $data[$k] = $v;
        }

        $sum = [
            "合计" => [
                '--' => [
                    "valid_num" => $validNum+0.1,
                    "income" => $income,
                    "cost" => $cost,
                    "profit" => $profit,
                    "channel" => $channelSum
                ]
            ]
        ];

        $result = $sum + $data;

        return $result;
    }

    /**
     * @param $name
     * @param $data
     * @param $channelArray
     * @param int $type 1表示产品详情，2表示客户详情
     * @return string
     */
	private function drowTablePro($name, $data, $channelArray, $type=1)
    {
        $table = "";
        $i = 0;
        foreach ( $data as $ck=>$cv ){
            $j = 0;
            foreach ( $cv as $ok=>$ov ){
                $tr = "<tr>";
                $tds = "";

                if( 0 == $i && 0 == $j ){
                    $count = $this->getPnameRowSpan($data);
                    $tds .= "<td rowspan='{$count}'>$name</td>";
                }

                if( 0 == $j ){
                    $countO = count($cv);
                    if( "合计" == $ck ){
                        $backColeor = "background: cadetblue; color:white";
                        $tds .= "<td rowspan='{$countO}' style='{$backColeor}'>"."$ck</td>";
                    }else{
                        if( 1 == $type ){
                            $apikey = array_search($ck, $this->apikeyName);
                            $tds .= "<td rowspan='{$countO}' ><a href=\"/Opdata/Detail/Client?apikey={$apikey}&sdate={$this->sdate}&edate={$this->edate}\">$ck</a></td>";
                        }else{
                            $pid = array_search($ck, $this->proName);
                            $tds .= "<td rowspan='{$countO}' ><a href=\"/Opdata/Detail/Product?pid={$pid}&sdate={$this->sdate}&edate={$this->edate}\">$ck</a></td>";
                        }
                    }
                }

                //如果是合计列，则 字体标红
                if( '合计' == $ok ){
                    $backColeor = "color:#FF5722";
                }else if( '--' == $ok  ){
                    $backColeor = "background: cadetblue; color:white";
                }else{
                    $backColeor = "";
                }

                $tds .= "<td style='{$backColeor}'>".$ok."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['valid_num']."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['income']."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['cost']."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['profit']."</td>";

                //获取渠道量的td
                $tds .= $this->getChannelTd($ov['channel'], $channelArray, $backColeor);

                $tr .= $tds;
                $tr .= "</tr>";
                $table .= $tr;

                $j++;
            }

            $i++;
        }

        return $table;
    }

    private function drowTableChannel($name, $data)
    {
        $table = "";
        $i = 0;
        foreach ( $data as $ck=>$cv ){
            $j = 0;
            foreach ( $cv as $ok=>$ov ){
                $tr = "<tr>";
                $tds = "";

                if( 0 == $i && 0 == $j ){
                    $count = $this->getPnameRowSpan($data);
                    $tds .= "<td rowspan='{$count}'>$name</td>";
                }

                if( 0 == $j ){
                    $countO = count($cv);
                    if( "合计" == $ck ){
                        $backColeor = "background: cadetblue; color:white";
                    }else{
                        $backColeor = "cadetblue";
                    }
                    $tds .= "<td rowspan='{$countO}' style='{$backColeor}'>$ck</td>";
                }

                //如果是合计列，则 字体标红
                if( '合计' == $ok ){
                    $backColeor = "color:#FF5722";
                }else if( '--' == $ok  ){
                    $backColeor = "background: cadetblue; color:white";
                }else{
                    $backColeor = "";
                }

                $tds .= "<td style='{$backColeor}'>".$ok."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['valid_num']."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['cost']."</td>";
                $tds .= "<td style='{$backColeor}'>".$ov['price']."</td>";

                $tr .= $tds;
                $tr .= "</tr>";
                $table .= $tr;

                $j++;
            }

            $i++;
        }

        return $table;
    }

    private function getChannelTd($channelNum, $channelArray, $color = "")
    {
        $tds = "";
        foreach ( $channelArray as $k=>$v ){
            if( isset( $channelNum[$v] ) ){
                $num = $channelNum[$v];
            }else{
                $num = 0;
            }
            $tds .= "<td style='{$color}'>".$num."</td>";
        }

        return $tds;
    }

    private function getHaveChannelTh($data)
    {
        $channelNum = [];

        $th = "";
        foreach ( $data as $ck=>$cv ){
            foreach ( $cv as $ok=>$ov ){
                $channels = $ov['channel'];

                foreach ( $channels as $chk=>$chv ){

                    $channelNum[$chk] += $chv;
                }
            }
        }

        //排序
        arsort($channelNum);
        $channelArray = array_keys($channelNum);

        $cacheKey = array_search("缓存", $channelArray);
        if( $cacheKey !== false ){
            unset($channelArray[$cacheKey]);
        }
        array_unshift($channelArray, "缓存");

        foreach ( $channelArray as $v ){
            $th .= "<th>".$v."</th>";
        }

        $result['channelArray'] = $channelArray;
        $result['channelTh'] = $th;

        return $result;
    }

    private function getPnameRowSpan($data)
    {
        $rowSpan = count($data);

        foreach ( $data as $ck=>$cv ){
            $rowSpan += count($cv);
        }

        return $rowSpan;
    }
}