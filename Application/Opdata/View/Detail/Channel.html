<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
        .index-btn {
            margin:5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form id="form_init" action="/Opdata/Detail/Channel" method="get" class="form-inline">
                <input type="hidden" name="search-way" id="search-way" value="0"/>
                <div class="form-group">
                    <label class="control-label" for="start_time">开始时间：</label>
                    <input type="date" name="sdate" id="start_time" class="form-control"
                           value="{$sdate}"/>
                    <label class="control-label" for="end_time">结束时间：</label>
                    <input type="date" name="edate" id="end_time" class="form-control" value="{$edate}"/>
                </div>

                <div class="form-group">
                    <label for="channel">选择客户：</label>
                    <select name="channeltype" id="channel">
                        {$channelName}
                    </select>
                </div>

                <div class="form-group">
                    <input id="searchBtn" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group" style="margin-left: 80px">
                    <input id="today" type="button" class="btn btn-warning btn-sm" value="今天">
                </div>

                <div class="form-group">
                    <input id="yesterday" type="button" class="btn btn-warning btn-sm" value="昨天">
                </div>

                <div class="form-group">
                    <input id="lastWeek" type="button" class="btn btn-warning btn-sm" value="近一周">
                </div>

                <div class="form-group">
                    <input id="localMonth" type="button" class="btn btn-warning btn-sm" value="近一月">
                </div>

            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue">
            <thead>
            <tr>
                <th width="300">渠道</th>
                <th>产品</th>
                <th>客户</th>
                <th>有效量</th>
                <th>成本</th>
            </tr>
            </thead>

            <tbody>

            <?php echo $channelInfo ?>

            </tbody>

        </table>
    </div>
</div>
<div class="container">
    <nav>
        <ul class="pagination">
        </ul>
    </nav>
</div>

<script src="__JS__opdata.js"></script>
<script type="text/javascript">
    $("#searchBtn").click(function () {
        let start_time = $("#start_time").val();
        let end_time   = $("#end_time").val();
        let start_date = Date.parse(start_time);
        let end_date   = Date.parse(end_time);
        let cha        = Math.abs(start_date - end_date);
        cha            = Math.floor(cha / (24 * 3600 * 1000));
        if (cha > 365) {
            alert('查询范围需要小于365天！！！');
            return false;
        }
        $('#search-way').val(0);
        $("#form_init").submit();
    });

    $("#channel").select2({
        allowClear  : true,
        theme       : "bootstrap",
        width       : '150',
        placeholder : '选择客户'
    });

</script>

</body>
</html>
