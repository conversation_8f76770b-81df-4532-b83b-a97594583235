<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form id="form_init" action="/Opdata/Self/SelfWithClient" method="get" class="form-inline">
                <input type="hidden" name="search-way" id="search-way" value="0"/>
                <div class="form-group">
                    <label class="control-label" for="start_time">开始时间：</label>
                    <input type="date" name="sdate" id="start_time" class="form-control"
                           value="{$sdate}"/>
                    <label class="control-label" for="end_time">结束时间：</label>
                    <input type="date" name="edate" id="end_time" class="form-control" value="{$edate}"/>
                </div>

                <div class="form-group">
                    <input id="searchBtn" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group">
                    <input id="localDday" type="button" class="btn btn-primary btn-sm" value="近一天">
                </div>

                <div class="form-group">
                    <input id="localWeek" type="button" class="btn btn-primary btn-sm" value="近一周">
                </div>

                <div class="form-group">
                    <input id="localMonth" type="button" class="btn btn-primary btn-sm" value="近一月">
                </div>

            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue">
            <thead>
            <tr>
                <th width="300">客户<?php echo $pc ?></th>
                <th>有效数</th>
                <th>节约成本</th>
            </tr>
            </thead>

            <tbody>

            <?php echo $selfClientInfo ?>

            </tbody>

        </table>
    </div>
</div>
<div class="container">
    <nav>
        <ul class="pagination">
        </ul>
    </nav>
</div>

<script src="__JS__opdata.js"></script>
<script type="text/javascript">

    $("#spreadTh").click(
        function (){
            if( $(this).attr("data-open") == 0 ){
                showTh();
            }else{
                hideTh();
            }

            $(this).attr("data-open", $(this).attr("data-open") == 0 ? 1 : 0 );
            $(this).html( $(this).attr("data-open") == 0 ? " + " : " - " );
        }
    );

    $("#searchBtn").click(function () {
        let start_time = $("#start_time").val();
        let end_time   = $("#end_time").val();
        let start_date = Date.parse(start_time);
        let end_date   = Date.parse(end_time);
        let cha        = Math.abs(start_date - end_date);
        cha            = Math.floor(cha / (24 * 3600 * 1000));
        if (cha > 365) {
            alert('查询范围需要小于365天！！！');
            return false;
        }
        $('#search-way').val(0);
        $("#form_init").submit();
    });

    hideTh();

    function hideTh()
    {
        //隐藏tr
        $(".canHide").hide();
    }

    function showTh()
    {
        //隐藏tr
        $(".canHide").show();
    }

    /**
     * 查询最近一天
     */
    $("#localDday").on('click', function(){
        $('#search-way').val(1);
        $("#form_init").submit();
    });
    /**
     * 查询最近一周
     */
    $('#localWeek').on('click', function(){
        $('#search-way').val(2);
        $("#form_init").submit();
    });
    /**
     * 查询最近一月
     */
    $('#localMonth').on('click', function(){
        $('#search-way').val(3);
        $("#form_init").submit();
    });

</script>

</body>
</html>
