<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
        .index-btn {
            margin:5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form id="form_init" action="/Account/Customer/index" method="get" class="form-inline">
                    <div class="form-group">
                        <label for="customer_id">ID：</label>
                        <input type="text" name="customer_id" id="customer_id" class="form-control"
                               value="">
                    </div>
                    <div class="form-group">
                        <label for="status">状态：</label>
                        <select name="status" id="status" class="form-control">
                            <option value="-1">状态</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="name">客户名称：</label>
                        <select name="name" id="name" class="form-control">

                        </select>
                    </div>
                    <div class="form-group">
                        <label for="company">公司名称：</label>
                        <select name="company" id="company" class="form-control">
                            <option value="">选择公司名称</option>

                        </select>

                    </div>
                    <div class="form-group">
                        <label for="productName">产品名称：</label>
                        <select name="product_name" id="productName" class="form-control">

                        </select>
                    </div>
                    <div class="form-group">
                        <label for="operator">运营跟进人：</label>
                        <select name="operator" id="operator" class="form-control">
                            <option value="">选择运营跟进人</option>

                        </select>
                    </div>

                <div class="form-group">
                    <input type="submit" id="submit_btn" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <a id="file_export" class="btn btn-success btn-sm">导出</a>
                </div>
                <div class="form-group">
                    <a id="fee_config" class="btn btn-success btn-sm">导出计费配置</a>
                </div>
                <div class="form-group">
                    <a href="{:U('add')}?callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn btn-info btn-sm">添加客户</a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue">
            <thead>
            <tr>
                <th width="150">客户ID</th>
                <th>客户名称</th>
                <th>公司名称</th>
                <th>邮箱地址</th>
                <th>公司类型</th>
                <th width="110">签约状态</th>
                <th width="80">状态</th>
                <th>已开通账号</th>
                <th>运营跟进人</th>
                <th>商务跟进人</th>
                <th width="190">TIME</th>
                <th>操作人</th>
                <th width="280">操作</th>
            </tr>
            </thead>
        </table>
    </div>
</div>
<div class="container">
    <nav>
        <ul class="pagination">
        </ul>
    </nav>
</div>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>

</body>
</html>
