<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        .row-first {
            margin-bottom : 10px;
        }
        label {
            margin-left : 10px;
        }
        td, th {
            text-align : center;
        }
        #detail_title {
            width       : 100%;
            background  : #CCCCCC;
            color       : #333333;
            font-size   : 14px;
            text-indent : 10px;
            font-weight : bold;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('channelDetail')}" class="form-inline" method="get" id="form_init">
                <div class="form-group">
                    <label class="control-label" for="start_time">开始时间：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control"
                           value="{$input.start_time}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="end_time">结束时间：</label>
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>
                <div class="form-group">
                    <label for="product_id">渠道名称：</label>
                    <select name="channel_id" id="channel_id">
                        {$channel_option}
                    </select>
                </div>

                <div class="form-group">
                    <label for="product_id">客户名称：</label>
                    <select name="apikey" id="apikey">
                        {$apikey_option}
                    </select>
                </div>

                <div class="form-group">
                    <label for="product_id">产品名称：</label>
                    <select name="product_id" id="product_id">
                        {$product_option}
                    </select>
                </div>
                <div class="form-group">
                    <input id="searchBtn" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <caption align="top" id="detail_title">{$title}</caption>
            <thead>
            <tr>
                <th>时间</th>
                <th>有效调用量</th>
                <th>成本</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>合计</td>
                <td><?php echo $res['total_num'];?></td>
                <td><?php echo $res['total_sum'];?></td>
            </tr>
            <?php
                foreach($res as $key=>$value){
                    if($key != 'total_num' && $key != 'total_sum'){
            ?>
            <tr>
                <td><?php echo $key; ?></td>
                <td><?php echo $res[$key]['num'];?></td>
                <td><?php echo $res[$key]['sum'];?></td>
            </tr>
            <?php } } ?>
            </tbody>
        </table>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        // 客户select2
        $("#apikey").select2({
            allowClear  : true,
            theme       : "bootstrap",
            width       : '180',
            placeholder : '选择客户'
        });
        $("#channel_id").select2({
            allowClear  : true,
            theme       : "bootstrap",
            width       : '180',
            placeholder : '选择账号'
        });
        $("#product_id").select2({
            allowClear  : true,
            theme       : "bootstrap",
            width       : '180',
            placeholder : '选择产品'
        });


        $("#customer_id").change(function () {
            let customer_id = $(this).val();
            $.ajax({
                url     : "{:U('detail')}",
                data    : {
                    customer_id : customer_id
                },
                type    : 'post',
                success : function (res) {
                    $("#account_id").html(res.account_option);
                    $("#account_id").select2({
                        allowClear  : true,
                        theme       : "bootstrap",
                        width       : '180',
                        placeholder : '选择账号'
                    });
                }
            });
        });
        $("#searchBtn").click(function () {
            let start_time = $("#start_time").val();
            let end_time   = $("#end_time").val();
            let start_date = Date.parse(start_time);
            let end_date   = Date.parse(end_time);
            let cha        = Math.abs(start_date - end_date);
            cha            = Math.floor(cha / (24 * 3600 * 1000));
            if (cha > 365) {
                alert('查询范围需要小于365天！！！');
                return false;
            }
            $("#form_init").submit();
        });
    });
</script>
</body>
</html>
