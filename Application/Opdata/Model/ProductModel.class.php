<?php

namespace Opdata\Model;

use Account\Repositories\FeeConfigRepository;
use Common\Controller\DataAuthController;
use Think\Model;
use Account\Model\CustomerModel;
use Common\Controller\ChangeNoticeController;

class ProductModel extends Model
{
    protected $connection = 'BMY_DB';
    protected $tableName = 'opdata_product';
    protected $tablePrefix = '';

    public static function getByPid($pid)
    {
        $model = new static();
        $result = $model->query("select * from opdata_product where pid={$pid}");

        return $result;
    }

    public static function getAll()
    {
        $model = new static();
        $result = $model->query("select * from opdata_product");

        return $result;
    }
}