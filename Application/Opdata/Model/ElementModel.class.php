<?php

namespace Opdata\Model;

use Account\Repositories\FeeConfigRepository;
use Common\Controller\DataAuthController;
use Think\Model;
use Common\ORG\Page;
use Account\Model\CustomerModel;
use Common\Controller\ChangeNoticeController;

class ElementModel extends Model
{
    protected $connection = 'BMY_DB';
    protected $tableName = '';
    protected $tablePrefix = '';

    /**
     * 更新信息
     * @param array $where
     * @param array $data
     */
    public static function updateData(array $where, array $data)
    {
        (new static())->where($where)
            ->save($data);
    }

    public static function getAccountList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 创建账号
     */
    public function createAccount($params)
    {
        return $this->add($params);
    }

    /**
     * 创建账号
     * @return mixed
     * @throws \Exception
     */
    public function createAccountByForm(){
        // 检查参数
        $this->checkParamsByCreateForPost();

        // 获取参数
        $params = $this->getParamForCreate();
        return $this->createAccount($params);
    }

    /**
     * 修改产品操作人
     * @param $id
     * @param $admin
     * @return bool|null
     */
    public function updateAdminInfo($id, $admin){
        if (empty($id) || empty($admin)){
            return null;
        }
        $data['admin'] = $admin;
        return $this->where('id='.$id)->save($data);
    }

    /**
     * 为创建账号过滤参数
     */
    protected function getParamForCreate()
    {
        $account_name = I('post.account_name', '', 'trim');
        $email = I('post.email', '', 'trim');
        $type = I('post.type', '', 'trim');
        $apikey = I('post.apikey', '', 'trim');
        $appsecret = I('post.appsecret', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');
        $end_time = intval(strtotime($end_time));
        $end_time += 86399;
        $status = I('post.status', '', 'trim');
        $access_ip = I('post.access_ip', '', 'trim');
        if (!empty($access_ip)){
            $ip_list = explode(PHP_EOL, str_replace(' ', '', $access_ip));
            $ip_list = array_map(function($ipStr){
                return  trim($ipStr);
            },$ip_list);
            $access_ip = serialize($ip_list);
        }
        $mark = I('post.mark', '', 'trim');
        $customer_id = I('post.customer_id', '', 'trim');
        $father_id = I('post.father_id', '', 'trim');

        //默认设置账号的 account_id
        $customer_model = new CustomerModel();
        if ($type == '1'){
            $account_id = $customer_model->createUUid('FA');
        }else {
            $account_id = $customer_model->createUUid('TA');
        }
        $password = $customer_model->genPasswordForCreate();

        $create_at = $update_at = time();

        return compact('account_name', 'email', 'type', 'apikey', 'appsecret', 'end_time', 'status', 'access_ip', 'mark', 'customer_id', 'father_id', 'create_at', 'update_at', 'account_id', 'password');
    }

    /**
     * 检查新建账号的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByCreateForPost()
    {
        $email = I('post.email', '', 'trim');
        $account_name = I('post.account_name', '', 'trim');
        $apikey = I('post.apikey', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');
        $access_ip = I('post.access_ip', '', 'trim');

        if (!$account_name) {
            throw new \Exception('账号名称未填写');
        }
        // name email的唯一性
        $name_unique = $this->where(compact('account_name'))->count();
        if ($name_unique) {
            throw new \Exception('账号名称已经被占用，请更换账号名称');
        }
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('请检查邮箱的格式');
        }
        // email 的唯一性
        $email_unique = $this->where(compact('email'))->count();
        if ($email_unique) {
            throw new \Exception('邮箱已经被占用,请更换邮箱');
        }
        // apikey的唯一性
        $apikey_unique = $this->where(compact('apikey'))->count();
        if ($apikey_unique){
            throw  new \Exception('apikey已经被占用，请更换apikey');
        }
        if (empty($end_time)){
            throw new \Exception('截止日期未填写');
        }

        // ip白名单
        if ($access_ip) {
            $ip = explode(PHP_EOL, str_replace(' ', '', $access_ip));
            $ip_preg = '/^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d|[xX])))$/';
            $ip = array_map(function($ipStr) use ($ip_preg) {
                $tmp = trim($ipStr);
                if (preg_match($ip_preg, $tmp)) {
                    return $tmp;
                } else {
                    throw new \Exception('ip白名单输入不合法【' . $tmp . '】');
                }
            },$ip);
        }
    }

    /**
     * 根据条件查询所有的账号
     * @param $where
     * @param string $field
     * @return mixed
     */
    public function getAccountListByWhere($where, $field='*') {
        return $this->where($where)->field($field)->order('id asc')->select();
    }

    /**
     * 根据条件查询所有的账号
     * @param $where
     * @param string $field
     * @return mixed
     */
    public function getAccountInfoByWhere($where, $field='*'){
        return $this->where($where)->field($field)->find();
    }

    /**
     * 查询单个账号信息
     * @param $id
     * @return array|mixed
     */
    public function getAccountInfoById($id){
        if (empty($id)){
            return [];
        }
        $account_info = $this->where('id='.$id)->find();

        //数据权限校验
        DataAuthController::instance()->validAllowDoCustomer($account_info['customer_id']);

        if ($account_info['access_ip']){
            $account_info['access_ip'] = implode(PHP_EOL, unserialize($account_info['access_ip']));
        }
        if ($account_info['end_time']){
            $account_info['end_time'] = date('Y-m-d',$account_info['end_time']);
        }
        //所属客户
        $customer_id = $account_info['customer_id'];
        $customer_model = new CustomerModel();
        $customer_info = $customer_model->getCustomerByCustomerId($customer_id, 'name');

        $account_info = array_merge($account_info, $customer_info);

        return $account_info;
    }

    /**
     * 账号管理编辑页面重置密码功能
     */
    public function updatePwdById($id){
        if (empty($id)){
            throw new \Exception('请求失败');
        }
        $customer_model = new CustomerModel();

        $where['id'] = $id;
        // 生成password
        $data['password'] = $customer_model->genPasswordForCreate();
        $data['update_at'] = time();
        return $this->where($where)->save($data);
    }

    /**
     * post方式通过id修改账号 && relationship
     * @throws \Exception
     */
    public function updateAccountByIdForPost()
    {
        // 检查参数
        $this->checkParamsByUpdateForPost();

        // 更新账号
        $id = I('post.id', '', 'trim');
        $params = $this->getParamForUpdate();

        //计费配置监听账号编辑事件
        (new FeeConfigRepository())->listenUpdateAccount($params, $id);

        $notice = new ChangeNoticeController();
        $notice->updateAccountForNotice($id);
        return $this->where(compact('id'))->save($params);
    }

    /**
     * 检查更新账号的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByUpdateForPost()
    {
        // 检查邮箱是否唯一
        $this->checkUniqueEmailForUpdate();

        // 检查Name是否唯一
        $this->checkUniqueNameForUpdate();
    }

    /**
     * 检查更新传递的邮件是否唯一
     * @throws \Exception
     */
    protected function checkUniqueEmailForUpdate()
    {
        $id = I('post.id', '', 'trim');

        $email = I('post.email', '', 'trim');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('请检查邮箱的格式');
        }
        $email_unique = $this->where(compact('email'))->find();
        if ($email_unique && $email_unique['id'] != $id) {
            throw new \Exception('账号邮箱已经被占用,请更换邮箱');
        }
    }

    /**
     * 检查更新传递的name(id)是否是唯一的
     * @throws \Exception
     */
    protected function checkUniqueNameForUpdate()
    {
        $id = I('post.id', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');
        $access_ip = I('post.access_ip', '', 'trim');
        $apikey = I('post.apikey', '', 'trim');

        // name email的唯一性
        $account_name = I('post.account_name', '', 'trim');
        if (!$account_name) {
            throw new \Exception('账号名称未填写');
        }
        $name_unique = $this->where(compact('account_name'))->find();
        if ($name_unique && $name_unique['id'] !=$id) {
            throw new \Exception('账号名称已经被占用，请切更换账号名称');
        }
        //end time
        if (empty($end_time)){
            throw  new \Exception('截止日期未填写');
        }
        // ip白名单
        if ($access_ip) {
            $ip = explode(PHP_EOL, str_replace(' ', '', $access_ip));
            $ip_preg = '/^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d|[xX])))$/';
            $ip = array_map(function($ipStr) use ($ip_preg) {
                $tmp = trim($ipStr);
                if (preg_match($ip_preg, $tmp)) {
                    return $tmp;
                } else {
                    throw new \Exception('ip白名单输入不合法【' . $tmp . '】');
                }
            },$ip);
            /*if (!in_array(false, filter_var_array($ip, FILTER_VALIDATE_IP))) {

            } else {
                throw new \Exception('ip白名单输入不合法');
            }*/
        }

        // apikey的唯一性
        $apikey_unique = $this->where(compact('apikey'))->find();
        if ($apikey_unique && $apikey_unique['id'] != $id){
            throw  new \Exception('apikey已经被占用，请更换apikey');
        }
    }

    /**
     * 为创建账号过滤参数
     */
    protected function getParamForUpdate()
    {
        $account_name = I('post.account_name', '', 'trim');
        $email = I('post.email', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');
        $end_time = intval(strtotime($end_time)) + 86399;
        $access_ip = I('post.access_ip', '', 'trim');
        $apikey = I('post.apikey', '', 'trim');
        $appsecret = I('post.appsecret', '', 'trim');
        $status = I('post.status', '', 'trim');
        $type = I('post.type', '', 'trim');

        if (!empty($access_ip)){
            $ip_list = explode(PHP_EOL, str_replace(' ', '', $access_ip));
            $ip_list = array_map(function($ipStr){
                return  trim($ipStr);
            },$ip_list);
            $access_ip = serialize($ip_list);
        }
        $mark = I('post.mark', '', 'trim');
        $update_at = time();

        // 生成password
        return compact('account_name', 'status','type', 'email', 'end_time', 'access_ip', 'mark', 'update_at', 'apikey', 'appsecret');
    }
    /**
     * 获取查询账号数据
     * <AUTHOR>
     * @datetime 11:35 2018/12/19
     *
     * @access public
     *
     * @return array
     **/
    public function accountListForGet()
    {
        return $this->where([
            ['father_id'    => ['neq', '0']],
            ['is_delete'    => ['eq', 0]]
        ])->where(DataAuthController::instance()->getAccountWhere())->field('account_name,account_id')->select();
    }
    /**
     * 根据条件删除账号数据
     * <AUTHOR>
     * @datetime 15:25 2018/12/21
     *
     * @access public
     * @param $where array
     *
     * @return int
     **/
    public function deleteAccountByWhere($where)
    {
        return $this->where($where)->save([
            'is_delete' => 1
        ]);
    }
}