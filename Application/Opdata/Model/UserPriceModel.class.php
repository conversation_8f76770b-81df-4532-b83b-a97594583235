<?php

namespace Opdata\Model;

use Think\Model;

class UserPriceModel extends Model
{
    protected $connection = 'BMY_DB';
    protected $tableName = 'opdata_user_price';
    protected $tablePrefix = '';

    public static function getPriceByApikeyAndPidAndOperator($apikey, $pid, $operator)
    {
        $model = new static();
        $result = $model->query("select * from opdata_user_price where apikey='{$apikey}' and pid={$pid} and operator_type={$operator}");

        return isset($result[0]) ? round($result[0]['price'],4) : "0.00";
    }
}