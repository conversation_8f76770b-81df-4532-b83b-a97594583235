<?php

namespace Opdata\Model;

use Account\Repositories\FeeConfigRepository;
use Common\Controller\DataAuthController;
use Think\Model;
use Account\Model\CustomerModel;
use Common\Controller\ChangeNoticeController;

class ApikeyModel extends Model
{
    protected $connection = 'BMY_DB';
    protected $tableName = 'opdata_apikey';
    protected $tablePrefix = '';

    public static function getByType($type)
    {
        $model = new static();
        $result = $model->query("select * from opdata_channel where `type`={$type}");

        return $result;
    }

    public static function getAll()
    {
        $model = new static();
        $result = $model->query("select * from opdata_apikey");

        return $result;
    }
}