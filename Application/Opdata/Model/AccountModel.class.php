<?php

namespace Opdata\Model;

use Account\Repositories\FeeConfigRepository;
use Common\Controller\DataAuthController;
use Think\Model;
use Account\Model\CustomerModel;
use Common\Controller\ChangeNoticeController;

class AccountModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'account';
    protected $tablePrefix = '';

    public static function getByApikey($apikey)
    {
        $model = new static();
        $result = $model->query("select * from account where `apikey`={$apikey} ");

        return $result;
    }

    public static function getByApikeys($apikeyArray)
    {
        $apikeys = implode("','", $apikeyArray);

        $model = new static();
        $result = $model->query("select account_name,apikey from account where `apikey` in ('{$apikeys}')");

        return $result;
    }

    public static function getAccountList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }
}