<?php

namespace Opdata\Model;

use Think\Model;

class ChannelModel extends Model
{
    protected $connection = 'BMY_DB';
    protected $tableName = 'opdata_channel';
    protected $tablePrefix = '';

    public static function getByType($type)
    {
        $model = new static();
        $result = $model->query("select * from opdata_channel where `type`={$type}");

        return $result;
    }

    public static function getAll()
    {
        $model = new static();
        $result = $model->query("select * from opdata_channel");

        return $result;
    }
}