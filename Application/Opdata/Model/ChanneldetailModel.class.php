<?php

namespace Opdata\Model;

use Think\Model;

class ChanneldetailModel extends Model
{
    protected $connection = 'BMY_DB';
    protected $tableName = 'opdata_channel_detail';
    protected $tablePrefix = '';

    public static function getAll()
    {
        $model = new static();
        $result = $model->query("select * from opdata_channel_detail");

        return $result;
    }

    public static function getChannelsByPidAndOperator($pid, $operator)
    {
        $model = new static();
        $resultArray = $model->query("select * from opdata_channel_detail where pid={$pid} and operator_type={$operator} and del_status=0 order by price");
        $result = [];
        foreach ( $resultArray as $k=>$v ){
            $result[$v['channeltype']] = $v;
        }

        return $result;
    }

    public static function getMostCheapChannel($pid, $operator)
    {
        $model = new static();
        $result = $model->query("select * from opdata_channel_detail where pid={$pid} and operator_type={$operator} and price > 0 order by price limit 1");
        if( isset($result[0]) ){
            return $result[0];
        }else{
            return [];
        }
    }
}