<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 15:03
 */

namespace Monitor\Repositories;


use Account\Model\AccountModel;
use Account\Model\ProductModel;
use Config\Model\ChannelStatisModel;
use Config\Model\ProductValueModel;
use Common\Common\CurlTrait;
use Common\Common\PhpExcelTrait;
use Home\Model\ChannelModel;
use Common\ORG\Page;
use Api\Repositories\BaseRepository;

class UpstreamRepository
{
    use CurlTrait, PhpExcelTrait;
    private $productModel;
    private $accountModel;
    private $channel;
    private $productValueModel;
    private $list_rows = 20;

    public function __construct()
    {
        $this->channel      = new ChannelModel();
        $this->productModel = new ProductModel();
        $this->accountModel = new AccountModel();
        $this->productValueModel = new ProductValueModel();
    }

    /**
     * 获取所有渠道数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getChannel()
    {
        return $this->channel->field(['num as value', 'label as name'])->select();
    }

    /**
     * 获取所有渠道中存在的产品数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getProduct()
    {
        return $this->productModel->field(['product_id', 'product_name'])->select();
    }

    /**
     * 获取客户数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getAccount()
    {
        return $this->accountModel->field(['account_id', 'account_name', 'apikey'])->where(['is_delete' => 0])->select();
    }

    /**
     * 获取客户数据
     *
     *  @param null $id
     * @access protected
     *
     * @return array
     **/
    protected function getProductValue($id = null)
    {

        $obj = $this->productValueModel->field(['id', 'pid', 'type', 'value']);
        if ($id) {
            $obj->where(['id'=> $id]);
        }
        return $obj->select();
    }


    /**
     * 获取上游数据源统计数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getUpstreamStatisticsList()
    {
        $params = $this->getListWhere();

        $url  = C('LIST_API_URL.upstream_statistics_list');
        $data = $this->post($url, $params);


        if ($data['status'] != 0) {
            throw new \Exception($data['msg']);
        }

        return $data['data'];
    }

    /**
     * 获取查询条件
     *
     * @access protected
     *
     * @return array
     **/
    protected function getListWhere()
    {
        $params = (new BaseRepository())->genParamsForPost();

        $channel = $params['channel'] ? $params['channel'] : [];
        $product_id = $params['product_id'] ? $params['product_id'] : '';
        $apikey = $params['apikey'] ? $params['apikey'] : '';
        $key = $params['key'] ? $params['key'] : '';
        $start_date = $params['start_date'] ? $params['start_date'] : date('Y-m-d H:i:s', strtotime('-1 months'));
        $end_date = $params['end_date'] ? $params['end_date'] : date('Y-m-d H:i:s');
        $page = (intval($params['page']) > 1) ? intval($params['page']) : 1;
        $limit = (intval($params['limit']) > 1) ? intval($params['limit']) : 15;
        $offset = ($page-1) * $limit;

        return compact('channel', 'product_id', 'apikey', 'start_date', 'end_date', 'key', 'offset', 'limit');
    }

    /**
     * 获取值分布页面所需数据
     *
     * @access public
     *
     * @return array
     **/
    public function getSpreadListHtmlInfo()
    {
        //渠道下拉选项框
        $channel       = $this->getChannel();
        $channelOption = json_encode($channel, JSON_UNESCAPED_SLASHES);
        $channelSelect = makeOption(array_column($channel, 'name', 'value'));
        addBreakPoint('[spreadList]渠道下拉选项框');

        //产品下拉选项框
        $product       = $this->getProduct();
        $productOption = makeOption(array_column($product, 'product_name', 'product_id'));
        addBreakPoint('[spreadList]产品下拉选项框');

        //客户下拉选项框
        $account       = $this->getAccount();
        $accountOption = makeOption(array_column(array_map(function ($item) {
            $item['name'] = '[' . $item['account_id'] . '] ' . $item['account_name'];
            return $item;
        }, $account), 'name', 'apikey'));
        addBreakPoint('[spreadList]客户下拉选项框');

        return compact('channelOption', 'productOption', 'accountOption', 'channelSelect');
    }

    /**
     * 获取值分布页面所需数据
     *
     * @access public
     *
     * @return array
     **/
    public function getStatisListHtml()
    {
        //渠道下拉选项框
        $channel       = $this->getChannel();
        $channelOption = json_encode($channel, JSON_UNESCAPED_SLASHES);
        $channelSelect = makeOption(array_column($channel, 'name', 'value'));

        $typeOption = makeOption(ChannelStatisModel::$typeName);
        return compact('channelOption', 'channelSelect', 'typeOption');
    }

    /**
     * 获取产品-值类型映射数组
     *
     * @access public
     *
     * @return array
     **/
    public function getProductValueHtmlInfo()
    {
        //渠道下拉选项框
        $productValue = $this->getProductValue();
        $types = ProductValueModel::$typeName;
        $valueArr = [];
        foreach ($productValue as $v) {
            $valueArr[$v['pid']][] = [
                'name' => $types[$v['type']] . ' ' . $v['value'],
                'value' => $v['id']
            ];
        }
        return $valueArr;
    }



    /**
     * 获取成功率页面所需数据
     *
     * @access public
     *
     * @return array
     **/
    public function getSuccessListHtmlInfo()
    {
        //渠道下拉选项框
        $channel       = $this->getChannel();
        $channelOption = json_encode($channel, JSON_UNESCAPED_SLASHES);

        //产品下拉选项框
        $product       = $this->getProduct();
        $productOption = makeOption(array_column($product, 'product_name', 'product_id'));

        //客户下拉选项框
        $account       = $this->getAccount();
        $accountOption = makeOption(array_column(array_map(function ($item) {
            $item['name'] = '[' . $item['account_id'] . '] ' . $item['account_name'];
            return $item;
        }, $account), 'name', 'apikey'));
        return compact('channelOption', 'productOption', 'accountOption');
    }

    /**
     * 获取渠道查得率页面所需数据
     *
     * @access public
     *
     * @return array
     **/
    public function getChannelStatisHtmlInfo()
    {
        //渠道下拉选项框
        $channel       = $this->getChannel();
        $channelOption = json_encode($channel, JSON_UNESCAPED_SLASHES);

        return compact('channelOption');
    }

    /**
     * 查询值分布列表数据
     *
     * @access public
     *
     * @return array
     **/
    public function getSpreadListInfo()
    {
        //获取产品映射关系
        $product = array_column($this->getProduct(), 'product_name', 'product_id');

        //获取渠道映射
        $channel = array_column($this->getChannel(), 'name', 'value');

        //获取客户映射
        $account = array_column($this->getAccount(), 'account_name', 'apikey');

        //获取接口数据mixed
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_monitor_value_spread'), $this->getListWhere());

        //融合数据
        $data['data'] = array_map(function ($item) use ($product, $channel, $account) {
            $item['product_name']  = $product[$item['pid']];
            $item['account_name'] = $account[$item['apikey']];
            $item['channel_label']  = $channel[$item['channel']];
            $item['channel_label']  = $channel[$item['channel']];
            $item['ratio']  = $item['ratio'] . '%';
            $item['start_time'] = date('m-d H:i', $item['start_time']);
            $item['end_time'] = date('m-d H:i', $item['end_time']);
            $item['time_span']  = date('m-d H:i', $item['start_time']) .' - '. date('m-d H:i', $item['end_time']);
            return $item;
        }, $data['data']);
        return $data;
    }

    /**
     * 查询值分布列表数据
     *
     * @access public
     *
     * @return array
     **/
    public function getSuccessListInfo()
    {
        //获取产品映射关系
        $product = array_column($this->getProduct(), 'product_name', 'product_id');

        //获取渠道映射
        $channel = array_column($this->getChannel(), 'name', 'value');

        //获取客户映射
        $account = array_column($this->getAccount(), 'account_name', 'apikey');

        //获取接口数据
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_monitor_success_ratio'), $this->getListWhere());

        //融合数据
        $data['data'] = array_map(function ($item) use ($product, $channel, $account) {
            $item['product_name']  = $product[$item['pid']];
            $item['account_name'] = $account[$item['apikey']];
            $item['channel_label']  = $channel[$item['channel']];
            $item['succ_ratio']  .= '%';
            $item['valid_ratio']  .= '%';
            $item['start_time'] = date('m-d H:i', $item['start_time']);
            $item['end_time'] = date('m-d H:i', $item['end_time']);
            $item['time_span']  = date('m-d H:i', $item['start_time']) .' - '. date('m-d H:i', $item['end_time']);
            return $item;
        }, $data['data']);
        return $data;
    }

    /**
     * 查询值分布列表数据
     *
     * @access public
     *
     * @return array
     **/
    public function getChannelStatisList()
    {
        //获取渠道映射
        $channel = array_column($this->getChannel(), 'name', 'value');
        //获取接口数据
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_monitor_channel_statis'), $this->getListWhere());

        //融合数据
        $data['data'] = array_map(function ($item) use ($channel) {
            $item['channel_label']  = $channel[$item['cid']];
            $item['start_date']  = date('m-d H:i', $item['start_time']);
            $item['end_date']  = date('m-d H:i', $item['end_time']);
            $item['error_ratio'] = ($item['all'] > 0) ? bcmul((($item['all'] - $item['success']) / $item['all']), 100, 2) : '0.00';
            $item['over_time_ratio'] = ($item['all'] > 0) ? bcmul(($item['over_time'] / $item['all']),  100, 2) : '0.00';
            $item['not_valid_ratio'] = ($item['all'] > 0) ? bcmul((($item['success'] - $item['valid']) / $item['all']), 100, 2) : '0.00';
            $item['other_ratio'] = ($item['all'] > 0) ? bcmul((($item['all'] - $item['success'] - $item['over_time']) / $item['all']), 100, 2) : '0.00';

            return $item;
        }, $data['data']);
        return $data;
    }

    /**
     * 查询值分布列表数据
     *
     * @access public
     *
     * @return array
     **/
    public function getConfigProductSpreadList()
    {
        //获取产品映射关系
        $product = array_column($this->getProduct(), 'product_name', 'product_id');
        addBreakPoint('[spreadListPost]获取产品映射关系');

        //获取渠道映射
        $channel = array_column($this->getChannel(), 'name', 'value');
        addBreakPoint('[spreadListPost]获取渠道映射');

        //获取客户映射
        $account = array_column($this->getAccount(), 'account_name', 'apikey');
        addBreakPoint('[spreadListPost]获取客户映射');

        //获取客户值分布映射
        $productValue = $this->getProductValueHtml($this->getProductValue());
        addBreakPoint('[spreadListPost]获取客户值分布映射');

        //获取接口数据
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_product_value_spread_list'), $this->getListWhere());
        addBreakPoint('[spreadListPost]获取接口数据');

        //融合数据
        $data['data'] = array_map(function ($item) use ($product, $channel, $account, $productValue) {
            $item['product_name']  = $product[$item['pid']];
            $item['account_name'] = $account[$item['apikey']];
            $item['channel_label']  = $channel[$item['cid']];
            $item['value']  = $productValue[$item['value_id']]['html'];
            return $item;
        }, $data['data']);
        return $data;
    }

    /**
     * 查询值分布列表数据
     *
     * @access public
     *
     * @return array
     **/
    public function getConfigChannelStatisList()
    {
        $params = (new BaseRepository())->genParamsForPost();
        $channel = $params['channel'] ? $params['channel'] : [];
        $page = (intval($params['page']) > 1) ? intval($params['page']) : 1;
        $limit = (intval($params['limit']) > 1) ? intval($params['limit']) : 15;
        $offset = ($page-1) * $limit;
        try {
            //获取接口数据
            $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_channel_statis_list'), compact('channel', 'limit', 'offset'));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage());
        }
        //获取渠道映射
        $channel = array_column($this->getChannel(), 'name', 'value');
        //融合数据
        $data['data'] = array_map(function ($item) use ($channel) {
            $item['channel_label']  = $channel[$item['cid']];
            $item['type_name']  = ChannelStatisModel::$typeName[$item['type']];
            return $item;
        }, $data['data']);
        return $data;
    }

    /**
     * 获取上游数据源
     * @param $url
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    protected function getUpstreamList($url, $params)
    {
        if (!$url) {
            throw new \Exception('缺少必要参数');
        }
        $data = $this->post($url, $params);

        if ($data['status'] != 0) {
            throw new \Exception($data['msg']);
        }

        return $data['data'];
    }

    /**
     * 获取上游数据源值分布配置详情
     *
     * @access protected
     *
     * @return array
     **/
    public function getConfigProductInfo()
    {
        $id = I('get.id', 0);
        if (!$id) {
            return [];
        }
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_product_value_spread_info'), ['id' => $id]);

        return $this->getProductSpreadInfo($data);
    }
    /**
     * 获取上游数据源值分布配置详情
     *
     * @access protected
     *
     * @return array
     **/
    public function getConfigChannelInfo()
    {
        $id = I('get.id', 0);
        if (!$id) {
            return [];
        }
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_channel_statis_info'), ['id' => $id]);

        return $this->getChannelStatisInfo($data);
    }

    /**
     * 修改预警值
     * @param $item
     * @return array
     * @throws \Exception
     */
    public function configProductEdit($item)
    {
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_product_value_spread_edit'), $item);
        return $this->getProductSpreadInfo($data['data']);
    }
    /**
     * 修改渠道查得率预警值
     * @param $item
     * @return array
     * @throws \Exception
     */
    public function configStatisEdit($item)
    {
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_channel_statis_edit'), $item);
        return $this->getChannelStatisInfo($data['data']);
    }

    /**
     * 添加值分布配置
     * @param $item
     * @return array
     * @throws \Exception
     */
    public function SpreadAdd($item)
    {
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_product_value_spread_add'), $item);
        return $data['data'];
    }
    /**
     * 添加渠道查得率配置
     * @param $item
     * @return array
     * @throws \Exception
     */
    public function StatisAdd($item)
    {
        $data = $this->getUpstreamList(C('LIST_API_URL.back_api_config_channel_statis_add'), $item);
        return $data['data'];
    }
    /**
     * 获取值分布配置详情
     *
     * @access public
     *
     * @return array
     **/
    protected function getProductSpreadInfo($data)
    {
        $channel = array_column($this->getChannel(), 'name', 'value');
        $product = array_column($this->getProduct(), 'product_name', 'product_id');
        $account = array_column($this->getAccount(), 'account_name', 'apikey');
        $values = $this->getProductValueHtml($this->getProductValue());
        $data['channel'] = $channel[$data['cid']];
        $data['product'] = $product[$data['pid']];
        $data['account'] = $account[$data['apikey']];
        $data['value'] = $values[$data['value_id']];

        return $data;
    }
    /**
     * 获取渠道查得率配置详情
     *
     * @access public
     *
     * @return array
     **/
    protected function getChannelStatisInfo($data)
    {
        $channel = array_column($this->getChannel(), 'name', 'value');
        $data['channel'] = $channel[$data['cid']];
        $data['type_name'] = ChannelStatisModel::$typeName[$data['type']];

        return $data;
    }

    public function getProductValueByPid($pid) {
        return $this->getProductValue($pid);
    }

    /**
     * 值分布类型映射
     * @param $list
     * @return array
     */
    protected function getProductValueHtml($list) {
        $res = [];
        $types = ProductValueModel::$typeName;
        if (!is_array($list)) {
            return $res;
        }
        foreach ($list as $v) {
            if (isset($types[$v['type']])) {
                $v['html'] = $types[$v['type']] . ' ' . $v['value'];
            } else {
                $v['html'] = $v['value'];
            }
            $res[$v['id']] = $v;
        }
        return $res;
    }

}