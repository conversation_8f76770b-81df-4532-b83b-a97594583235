<?php

namespace Monitor\Controller;

use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Common\CurlTrait;
use Common\Controller\AdminController;
use Common\Model\ProductTypeModel;
use Monitor\Repositories\UpstreamRepository;


/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/4 0004
 * Time: 11:11
 */
class BxfShortController extends AdminController
{
    use CurlTrait;
    protected $key = '99bbdb8426f8b4e8d0cc3ebd92484590';

    public function __construct()
    {
        parent::__construct();

        $model = new ProductTypeModel();
        //获取评分类字段的产品ID
        $this->scoreProductId = $model->field('product_id')->where(['type' => 1])->select();
        $this->scoreProductId = array_column($this->scoreProductId, 'product_id');

        //获取统计类字段的产品ID
        $this->statisProductId = $model->field('product_id')->where(['type' => 2])->select();
        $this->statisProductId = array_column($this->statisProductId, 'product_id');
    }

    protected $scoreProductId = [];
    protected $statisProductId = [];

    public function score()
    {
        if (IS_POST) {
            $this->getScoreMonitor();
        }

        $productInfo    = $this->getProductInfo($this->scoreProductId);
        $customerOption = makeOption($this->getCustomerInfo());

        $this->assign('productInfo', $productInfo);
        $this->assign('url', C('LIST_API_URL.back_api_monitor_score'));
        $this->assign('customerOption', $customerOption);


        $this->display();
    }

    protected function getScoreMonitor()
    {
        $url = C('LIST_API_URL.back_api_monitor_score');

        $start_date    = I('post.start_date');
        $end_date      = I('post.end_date');
        $product_id    = I('post.product_id');
        $customer_id   = I('post.customer_id', null);
        $operator_type = I('post.operator_type', null);
        $key           = $this->key;
        $data          = compact('start_date', 'end_date', 'product_id', 'key');
        if (!is_null($customer_id)) {
            $data['customer_id'] = $customer_id;
        }
        if (!is_null($operator_type)) {
            $data['ctype'] = $operator_type;
        }
        $res = $this->post($url, $data);
        $this->ajaxReturn($res);
    }

    public function statis()
    {
        if (IS_POST) {
            $this->getStatisMonitor();
        }

        $productInfo    = $this->getProductInfo($this->statisProductId);
        $customerOption = makeOption($this->getCustomerInfo());

        $this->assign('productInfo', $productInfo);
        $this->assign('customerOption', $customerOption);

        $this->display();
    }

    protected function getStatisMonitor()
    {
        $url = C('LIST_API_URL.back_api_monitor_statis');

        $start_date    = I('post.start_date');
        $end_date      = I('post.end_date');
        $product_id    = I('post.product_id');
        $customer_id   = I('post.customer_id', null);
        $operator_type = I('post.operator_type', null);
        $key           = $this->key;
        $data          = compact('start_date', 'end_date', 'product_id', 'key');
        if (!is_null($customer_id)) {
            $data['customer_id'] = $customer_id;
        }
        if (!is_null($operator_type)) {
            $data['ctype'] = $operator_type;
        }
        $res = $this->post($url, $data);
        $this->ajaxReturn($res);
    }

    public function valueSpread()
    {
        if (IS_POST) {
            $this->getValueSpreadMonitor();
        }

        $repository = new UpstreamRepository();
        $data = $repository->getSpreadListHtmlInfo();

        $this->assign('data', $data);

        $this->display();
    }

    protected function getValueSpreadMonitor()
    {
        $repository = new UpstreamRepository();
        $_POST['key'] = $this->key;
        try {
            $data   = $repository->getSpreadListInfo();
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }

    public function successRatio()
    {
        if (IS_POST) {
            $this->getSuccessRatioMonitor();
        }

        $repository = new UpstreamRepository();
        $data = $repository->getSuccessListHtmlInfo();

        $this->assign('data', $data);

        $this->display();
    }

    protected function getSuccessRatioMonitor()
    {
        $repository = new UpstreamRepository();
        $_POST['key'] = $this->key;
        try {
            $data   = $repository->getSuccessListInfo();
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }

    public function channelStatis()
    {
        if (IS_POST) {
            $this->getChannelStatisMonitor();
        }

        $repository = new UpstreamRepository();
        $data = $repository->getChannelStatisHtmlInfo();

        $this->assign('data', $data);

        $this->display();
    }

    protected function getChannelStatisMonitor()
    {
        $repository = new UpstreamRepository();
        $_POST['key'] = $this->key;
        try {
            $data   = $repository->getChannelStatisList();
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }

    /**
     * 获取邦信分快捷版的产品数据 id=>name
     *
     * @access protected
     *
     * @param $product_id array 产品ID
     *
     * @return array
     **/
    protected function getProductInfo($product_id)
    {
        $product_id   = ['in', $product_id];
        $productModel = new ProductModel();
        return $productModel->field(['product_id', 'product_name'])->where(compact('product_id'))->select();
    }

    protected function getCustomerInfo()
    {
        //获取开通快捷版的所有客户
        $customerModel = new CustomerModel();
        $customerInfo  = $customerModel->field(['customer.customer_id', 'customer.name as customer_name'])
            ->join('account ON account.customer_id=customer.customer_id')
            ->join('account_product ON account.account_id=account_product.account_id')
            ->select();
        return array_column($customerInfo, 'customer_name', 'customer_id');
    }
}