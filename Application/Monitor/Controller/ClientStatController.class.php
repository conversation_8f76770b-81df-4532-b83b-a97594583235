<?php
/**
 * Created by PhpStorm.
 * User: gang8
 * Date: 2020/11/27
 * Time: 21:46
 */

namespace Monitor\Controller;


use Common\Controller\AdminController;
use Common\Common\CurlTrait;

//蚂蚁监控
class ClientStatController extends AdminController
{
    use CurlTrait;

    //默认京东apikey
    private $apikey = '5b8e11bf06a072a337dfcb5ee2c479e8';
    //默认661产品
    private $product_id = 661;

    private $client_products_api = '/monitor/product/getStatisFilter';
    private $statis_info_api = '/monitor/product/getStatisInfo';
    private $spread_info_api = '/monitor/product/getValueSpreadInfo';

    //特殊客户值分布
    private $special_spread_info_api = '/monitor/product/getSourceValueSpreadInfo';

    public function index()
    {
        $flag = I('post.flag', '');
        $father_id = I('get.father_id', '');
        $id = !empty($father_id) ? $father_id : 615;

        //默认当天时间
        $stime = date('Y-m-d 00:00:00');
        $etime = date('Y-m-d 23:59:59');
        $url = C('FINANCE_MANAGE_API_DOMAIN');


        $client_products_url = $url.$this->client_products_api;

        $client_products_res = $this->post($client_products_url, ['father_id' => $id]);
        $client_info = array_column($client_products_res, 'client');
        $products_info = array_column($client_products_res, 'products');

        if(empty($father_id)){
            $info_params = [
                'apikey' => $this->apikey,
                'product_id' => $this->product_id,
                'stime' => $stime,
                'etime' => $etime

            ];
            $spread_info_url = $url.$this->spread_info_api;


            $spread_info = $this->post($spread_info_url, $info_params);
            $spread_header = $spread_info['data']['header'];
            $spread_items = $spread_info['data']['items'];
            $spread_header_keys = array_keys($spread_header);
            $spread_arr = [];
            foreach($spread_items as $k=>$val){
                $spread_arr[] = $this->getArr($val, $spread_header_keys);
            }

            $statis_info_url = $url.$this->statis_info_api;


            $statis_info = $this->post($statis_info_url, $info_params);
            $statis_header = $statis_info['data']['header'];
            $statis_items = $statis_info['data']['items'];
            $statis_header_keys = array_keys($statis_header);

            $statis_arr = [];
            foreach($statis_items as $k=>$val){
                $statis_arr[] = $this->getArr($val, $statis_header_keys);
            }
        }else{
            $spread_arr = [];
            $spread_header = [];
            $statis_arr = [];
            $statis_header  = [];
        }

        if($flag == 'list'){
            $post_data = I('post.');
            $apikey = $post_data['apikey'];
            $product_id = $post_data['product_id'];
            $start_time = $post_data['start_time'];
            $end_time = $post_data['end_time'];

            $info_params = [
                'apikey' => $apikey,
                'product_id' => $product_id,
                'stime' => $start_time,
                'etime' => $end_time

            ];
            $spread_info = $this->post($spread_info_url, $info_params);

            $spread_header = $spread_info['data']['header'];
            $spread_items = $spread_info['data']['items'];

            $spread_header_keys = array_keys($spread_header);

            $spread_arr = [];
            foreach($spread_items as $k=>$val){
                $spread_arr[] = $this->getArr($val, $spread_header_keys);
            }
            $spread_header_th = '';
            foreach($spread_header as $key=>$value){
                $spread_header_th .= '<th align="center">'.$value.'</th>';
            }
            $spread_body_tr = '';
            foreach($spread_arr as $key=>$value){
                $spread_body_tr .= '<tr>';
                foreach($value as $k=>$val){
                    $spread_body_tr .= '<td align="center">'.$val.'</td>';
                }
                $spread_body_tr .= '</tr>';
            }

            $statis_info = $this->post($statis_info_url, $info_params);
            $statis_header = $statis_info['data']['header'];
            $statis_items = $statis_info['data']['items'];

            $statis_header_keys = array_keys($statis_header);
            $statis_arr = [];
            foreach($statis_items as $k=>$val){
                $statis_arr[] = $this->getArr($val, $statis_header_keys);
            }

            $statis_header_th = '';
            foreach($statis_header as $key=>$value){
                $statis_header_th .= '<th align="center">'.$value.'</th>';
            }
            $statis_body_tr = '';
            foreach($statis_arr as $key=>$value){
                $statis_body_tr .= '<tr>';
                foreach($value as $k=>$val){
                    $statis_body_tr .= '<td align="center">'.$val.'</td>';
                }
                $statis_body_tr .= '</tr>';
            }

            $result = ['status' => 'ok', 'msg'=>'成功', 'statis_header_th' => $statis_header_th, 'statis_body_tr'=>$statis_body_tr, 'spread_header_th'=>$spread_header_th, 'spread_body_tr'=>$spread_body_tr];
            $this->ajaxReturn($result);
        }

        $this->assign('client_info', $client_info[0]);
        $this->assign('products_info', $products_info[0]);
        $this->assign('spread_header', $spread_header);
        $this->assign('spread_arr', $spread_arr);
        $this->assign('statis_header', $statis_header);
        $this->assign('statis_arr', $statis_arr);
        $this->display();
    }

    public function specialClient()
    {

        $flag = I('post.flag', '');
        //默认当天时间
        $stime = date('Y-m-d 00:00:00');
        $etime = date('Y-m-d 23:59:59');
        $url = C('FINANCE_MANAGE_API_DOMAIN');

        $client_info = [
            [
                [
                    'name' => '京东',
                    'apikey' => '5b8e11bf06a072a337dfcb5ee2c479e8'
                ]
            ]
        ];

        $products_info = [
            [
                [
                    'name' => '信用风险等级V2',
                    'product_id' => '661'
                ],
                [
                    'name' => '欺诈风险等级V2',
                    'product_id' => '662'
                ]
            ]
        ];
        $info_params = [
            'apikey' => $this->apikey,
            'product_id' => 661,
            'stime' => $stime,
            'etime' => $etime

        ];
        $spread_info_url = $url.$this->special_spread_info_api;

        $spread_info = $this->post($spread_info_url, $info_params);
        $spread_header = $spread_info['data']['header'];
        $spread_items = $spread_info['data']['items'];
        $spread_header_keys = array_keys($spread_header);
        $spread_arr = [];
        foreach($spread_items as $k=>$val){
            $spread_arr[] = $this->getArr($val, $spread_header_keys);
        }

        $statis_info_url = $url.$this->statis_info_api;


        $statis_info = $this->post($statis_info_url, $info_params);
        $statis_header = $statis_info['data']['header'];
        $statis_items = $statis_info['data']['items'];
        $statis_header_keys = array_keys($statis_header);

        $statis_arr = [];
        foreach($statis_items as $k=>$val){
            $statis_arr[] = $this->getArr($val, $statis_header_keys);
        }

    if($flag == 'list'){
        $post_data = I('post.');
        $apikey = $post_data['apikey'];
        $product_id = $post_data['product_id'];
        $start_time = $post_data['start_time'];
        $end_time = $post_data['end_time'];

        $info_params = [
            'apikey' => $apikey,
            'product_id' => $product_id,
            'stime' => $start_time,
            'etime' => $end_time

        ];
        $spread_info = $this->post($spread_info_url, $info_params);

        $spread_header = $spread_info['data']['header'];
        $spread_items = $spread_info['data']['items'];

        $spread_header_keys = array_keys($spread_header);

        $spread_arr = [];
        foreach($spread_items as $k=>$val){
            $spread_arr[] = $this->getArr($val, $spread_header_keys);
        }
        $spread_header_th = '';
        foreach($spread_header as $key=>$value){
            $spread_header_th .= '<th align="center">'.$value.'</th>';
        }
        $spread_body_tr = '';
        foreach($spread_arr as $key=>$value){
            $spread_body_tr .= '<tr>';
            foreach($value as $k=>$val){
                $spread_body_tr .= '<td align="center">'.$val.'</td>';
            }
            $spread_body_tr .= '</tr>';
        }

        $statis_info = $this->post($statis_info_url, $info_params);
        $statis_header = $statis_info['data']['header'];
        $statis_items = $statis_info['data']['items'];

        $statis_header_keys = array_keys($statis_header);
        $statis_arr = [];
        foreach($statis_items as $k=>$val){
            $statis_arr[] = $this->getArr($val, $statis_header_keys);
        }

        $statis_header_th = '';
        foreach($statis_header as $key=>$value){
            $statis_header_th .= '<th align="center">'.$value.'</th>';
        }
        $statis_body_tr = '';
        foreach($statis_arr as $key=>$value){
            $statis_body_tr .= '<tr>';
            foreach($value as $k=>$val){
                $statis_body_tr .= '<td align="center">'.$val.'</td>';
            }
            $statis_body_tr .= '</tr>';
        }

        $result = ['status' => 'ok', 'msg'=>'成功', 'statis_header_th' => $statis_header_th, 'statis_body_tr'=>$statis_body_tr, 'spread_header_th'=>$spread_header_th, 'spread_body_tr'=>$spread_body_tr];
        $this->ajaxReturn($result);
    }

        $this->assign('client_info', $client_info[0]);
        $this->assign('products_info', $products_info[0]);
        $this->assign('spread_header', $spread_header);
        $this->assign('spread_arr', $spread_arr);
        $this->assign('statis_header', $statis_header);
        $this->assign('statis_arr', $statis_arr);
        $this->display();
    }

    private function getArr($val = [], $keys = [])
    {
        $arr = [];
        for($i = 0; $i < sizeof($keys); $i++){
            $arr[] =  isset($val[$keys[$i]]) ? $val[$keys[$i]] : 0;
        }
        return $arr;
    }


    public function channelInfo()
    {
        $this->display();
    }


    public function productInfo()
    {
        $url = C('FINANCE_MANAGE_API_DOMAIN');
        $api_product = '/monitor/lastRecord/getProduct';
        $api_client = '/monitor/lastRecord/getApikeyByProductId';
        $api_product_detail = '/monitor/lastRecord/getProductRecord';

        $flag = I('get.flag', '');

        $detail_str = '';

        if($flag == 'option'){
            $product_info = $this->get($url.$api_product);
            $product_arr = array_column($product_info['data'], 'product_name', 'product_id');
            $option = '';
            foreach($product_arr as $key=>$value){
                $option .= '<option value="'.$key.'">'.$value.'</option>';
            }
            $result = ['code'=>0, 'message'=>'', 'status'=>0, 'msg'=>'', 'data' => $option];
            $this->ajaxReturn($result);
        }else if($flag == 'detail'){
            $product_id = I('get.product_id');
            $client_id = I('get.client_id');
            $product_detail = $this->get($url.$api_product_detail, ['product_id'=>$product_id, 'apikey'=>$client_id]);

            $str = '';
            foreach($product_detail['data'] as $key=>$value){
                $str .= '<tr><td align="center">'.$value['account_name'].'</td><td align="center">'.$value['product_name'].'</td><td align="center">'.$value['status_text'].'</td><td align="center">'.$value['value'].'</td><td align="center">'.$value['run_time'].'</td><td align="center">'.$value['time'].'</td></tr>';
            }

            $result = ['code'=>0, 'message'=>'', 'status'=>0, 'msg'=>'', 'data' => $str];
            $this->ajaxReturn($result);
        }else if($flag == 'client'){
            $product_id = I('get.product_id');
            $client_info = $this->get($url.$api_client, ['product_id'=>$product_id]);
            $client_arr = array_column($client_info['data'], 'name', 'apikey');
            $option = '<option value=""></option>';
            foreach($client_arr as $key=>$value){
                $option .= '<option value="'.$key.'">'.$value.'</option>';
            }
            $result = ['code'=>0, 'message'=>'', 'status'=>0, 'msg'=>'', 'data' => $option];
            $this->ajaxReturn($result);
        }else{
            /*
            $product_detail = $this->get($url.$api_product_detail);
            foreach($product_detail['data'] as $key=>$value){
                $detail_str .= '<tr><td align="center">'.$value['account_name'].'</td><td align="center">'.$value['product_name'].'</td><td align="center">'.$value['status_text'].'</td><td align="center">'.$value['value'].'</td><td align="center">'.$value['run_time'].'</td><td align="center">'.$value['time'].'</td></tr>';
            }
            */
        }

        //$this->assign('detail_str', $detail_str);
        $this->display();
    }

    /**
     * 曲线图统计
     */
    public function statQuxian()
    {
        $flag = I('get.flag');

        $chal_model = D('Chnl');
        $chal_info = $chal_model->field(['chnlid', 'chnl_name'])->select();

        $chal_name_arr = [];
        foreach($chal_info as $key=>$value){
            $chal_name_arr[$value['chnlid']] = $value['chnl_name'];
        }
        if($flag == 'quxian'){
           // $str = '{"msg":"请求成功","status":0,"data":{"********":{"date":"********","total":"1623566","success":"1620699","valid":"975287","cache":"64503","money":"-","number":"431259"},"********":{"date":********,"total":"1123887","success":"1119901","valid":"756680","cache":"60333","money":"-","number":"442854"},"20210528":{"date":20210528,"total":"1435090","success":"1433572","valid":"922990","cache":"82366","money":"-","number":"466519"},"20210529":{"date":20210529,"total":"1931774","success":"1927811","valid":"1285763","cache":"109201","money":"-","number":"526881"},"20210530":{"date":20210530,"total":"991216","success":"988307","valid":"681039","cache":"57492","money":"-","number":"411583"},"20210531":{"date":20210531,"total":"1799035","success":"1795312","valid":"1135828","cache":"111363","money":"-","number":"538247"},"20210601":{"date":20210601,"total":"2297242","success":"2294698","valid":"1322562","cache":"104442","money":"-","number":"531853"},"20210602":{"date":20210602,"total":"594412","success":"592246","valid":"355599","cache":"19154","money":"-","number":"115570"}}}';

            $date_start = I('get.date_start');
            $date_end = I('get.date_end');
            $chnl_id = I('get.chnl_id');

            $channel_num = D('ChannelNum');
            $where = [];
            $where['chnlid'] = $chnl_id;
            $where['day'][] = array('EGT', $date_start);
            $where['day'][] = array('ELT',$date_end);

            $channel_info = $channel_num->where($where)->select();

            $res_arr = [];
            foreach($channel_info as $key=>&$value){
                $date = date('Ymd', strtotime($value['day']));
                $value['date'] = $date;
                $value['chnl_name'] = isset($chal_name_arr[$value['chnlid']]) ? $chal_name_arr[$value['chnlid']] : '未知';
                $res_arr[$date] = $value;
            }

            $res = [
                'msg' => '请求成功',
                'status' => 0,
                'data' => $res_arr
            ];
            $this->ajaxReturn($res);
                $arr3 = [
                    'msg' => '请求成功',
                    'status' => 0,
                    'data' => [
                        '20210521'=>[
                            "date"=>"20210521",
                            'cnt' => '582',//请求量
                            'uidnum' => '13579',//uid个数
                            'telnum' => '159701',//号码个数
                            'tel1num' => '1659', //手机号个数
                            'tel2num' => '1235', //热线号个数
                            'tel3num' => '137596', //固定号码个数
                            'uidnumpct' => '137',//uid密度
                            'telnumpct' => '135796',//号码密度
                            'tel1numpct' => '135796',//手机号占比
                            'tel2numpct' => '135796',//热线号占比
                            'tel3numpct' => '137596',//固话号占比
                            'uidnumge100w' => '137596',//频数ge100万的uid个数
                            'uidnumge100wpct' => '153796',//频数ge100万的uid占比
                            'uidnumge10w' => '137596',//频数ge10万的uid个数
                            'uidnumge10wpct' => '153796',//频数ge10万的uid占比
                            'uidnumge1w' => '135796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '137596',//频数ge1万的uid占比
                            'uidnumge1000' => '137596',//频数ge1000的uid个数
                            'uidnumge1000pct' => '135796',//频数ge1000的uid占比
                            'uidnumge100' => '137596',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '153796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '513796',//频数等于1的uid个数
                            'uidnume1pct' => '135796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '135796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '135796',//频数le5的uid占比
                            'ngt1uidnum' => '137296',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '123796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '123796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '137926',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '123796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '132796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '132796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '********'=>[
                            "date"=>"********",
                            'cnt' => '582',//请求量
                            'uidnum' => '13579',//uid个数
                            'telnum' => '159701',//号码个数
                            'tel1num' => '1659', //手机号个数
                            'tel2num' => '1235', //热线号个数
                            'tel3num' => '137596', //固定号码个数
                            'uidnumpct' => '137',//uid密度
                            'telnumpct' => '135796',//号码密度
                            'tel1numpct' => '135796',//手机号占比
                            'tel2numpct' => '135796',//热线号占比
                            'tel3numpct' => '137596',//固话号占比
                            'uidnumge100w' => '137596',//频数ge100万的uid个数
                            'uidnumge100wpct' => '153796',//频数ge100万的uid占比
                            'uidnumge10w' => '137596',//频数ge10万的uid个数
                            'uidnumge10wpct' => '153796',//频数ge10万的uid占比
                            'uidnumge1w' => '135796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '137596',//频数ge1万的uid占比
                            'uidnumge1000' => '137596',//频数ge1000的uid个数
                            'uidnumge1000pct' => '135796',//频数ge1000的uid占比
                            'uidnumge100' => '137596',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '153796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '513796',//频数等于1的uid个数
                            'uidnume1pct' => '135796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '135796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '135796',//频数le5的uid占比
                            'ngt1uidnum' => '137296',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '123796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '123796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '137926',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '123796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '132796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '132796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '20210528'=>[
                            "date"=>"20210528",
                            'cnt' => '852',//请求量
                            'uidnum' => '14379',//uid个数
                            'telnum' => '13479701',//号码个数
                            'tel1num' => '1469', //手机号个数
                            'tel2num' => '1423', //热线号个数
                            'tel3num' => '143796', //固定号码个数
                            'uidnumpct' => '1347',//uid密度
                            'telnumpct' => '134796',//号码密度
                            'tel1numpct' => '134796',//手机号占比
                            'tel2numpct' => '13796',//热线号占比
                            'tel3numpct' => '13796',//固话号占比
                            'uidnumge100w' => '137496',//频数ge100万的uid个数
                            'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                            'uidnumge10w' => '137496',//频数ge10万的uid个数
                            'uidnumge10wpct' => '134796',//频数ge10万的uid占比
                            'uidnumge1w' => '137496',//频数ge1万的uid个数
                            'uidnumge1wpct' => '143796',//频数ge1万的uid占比
                            'uidnumge1000' => '137946',//频数ge1000的uid个数
                            'uidnumge1000pct' => '137496',//频数ge1000的uid占比
                            'uidnumge100' => '134796',//频数ge100的uid个数
                            'uidnumge100pct' => '143796',//频数ge100的uid占比
                            'uidnumge50' => '143796',//频数ge50的uid个数
                            'uidnumge50pct' => '143796',//频数ge50的uid占比
                            'uidnume1' => '137946',//频数等于1的uid个数
                            'uidnume1pct' => '137496',//频数等于1的uid占比
                            'uidnumle2' => '137946',//频数le2的uid个数
                            'uidnumle2pct' => '134796',//频数le2的uid占比
                            'uidnumle5' => '137946',//频数le5的uid个数
                            'uidnumle5pct' => '137496',//频数le5的uid占比
                            'ngt1uidnum' => '137946',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '20210529'=>[
                            "date"=>"20210529",
                            'cnt' => '8244',//请求量
                            'uidnum' => '41379',//uid个数
                            'telnum' => '179701',//号码个数
                            'tel1num' => '1469', //手机号个数
                            'tel2num' => '1423', //热线号个数
                            'tel3num' => '134796', //固定号码个数
                            'uidnumpct' => '1437',//uid密度
                            'telnumpct' => '134796',//号码密度
                            'tel1numpct' => '137496',//手机号占比
                            'tel2numpct' => '134796',//热线号占比
                            'tel3numpct' => '137496',//固话号占比
                            'uidnumge100w' => '13796',//频数ge100万的uid个数
                            'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                            'uidnumge10w' => '13796',//频数ge10万的uid个数
                            'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                            'uidnumge1w' => '13796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                            'uidnumge1000' => '13796',//频数ge1000的uid个数
                            'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                            'uidnumge100' => '13796',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '13796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '13796',//频数等于1的uid个数
                            'uidnume1pct' => '13796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '13796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '13796',//频数le5的uid占比
                            'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '20210530'=>[
                            "date"=>"20210530",
                            'cnt' => '182',//请求量
                            'uidnum' => '1379',//uid个数
                            'telnum' => '1379701',//号码个数
                            'tel1num' => '169', //手机号个数
                            'tel2num' => '123', //热线号个数
                            'tel3num' => '13796', //固定号码个数
                            'uidnumpct' => '137',//uid密度
                            'telnumpct' => '13796',//号码密度
                            'tel1numpct' => '13796',//手机号占比
                            'tel2numpct' => '13796',//热线号占比
                            'tel3numpct' => '13796',//固话号占比
                            'uidnumge100w' => '13796',//频数ge100万的uid个数
                            'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                            'uidnumge10w' => '13796',//频数ge10万的uid个数
                            'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                            'uidnumge1w' => '13796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                            'uidnumge1000' => '13796',//频数ge1000的uid个数
                            'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                            'uidnumge100' => '13796',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '13796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '13796',//频数等于1的uid个数
                            'uidnume1pct' => '13796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '13796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '13796',//频数le5的uid占比
                            'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '20210531'=>[
                            "date"=>"20210531",
                            'cnt' => '822',//请求量
                            'uidnum' => '1379',//uid个数
                            'telnum' => '1379701',//号码个数
                            'tel1num' => '169', //手机号个数
                            'tel2num' => '123', //热线号个数
                            'tel3num' => '13796', //固定号码个数
                            'uidnumpct' => '137',//uid密度
                            'telnumpct' => '13796',//号码密度
                            'tel1numpct' => '13796',//手机号占比
                            'tel2numpct' => '13796',//热线号占比
                            'tel3numpct' => '13796',//固话号占比
                            'uidnumge100w' => '13796',//频数ge100万的uid个数
                            'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                            'uidnumge10w' => '13796',//频数ge10万的uid个数
                            'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                            'uidnumge1w' => '13796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                            'uidnumge1000' => '13796',//频数ge1000的uid个数
                            'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                            'uidnumge100' => '13796',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '13796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '13796',//频数等于1的uid个数
                            'uidnume1pct' => '13796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '13796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '13796',//频数le5的uid占比
                            'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '20210601'=>[
                            "date"=>"20210601",
                            'cnt' => '826',//请求量
                            'uidnum' => '1379',//uid个数
                            'telnum' => '1379701',//号码个数
                            'tel1num' => '169', //手机号个数
                            'tel2num' => '123', //热线号个数
                            'tel3num' => '13796', //固定号码个数
                            'uidnumpct' => '137',//uid密度
                            'telnumpct' => '13796',//号码密度
                            'tel1numpct' => '13796',//手机号占比
                            'tel2numpct' => '13796',//热线号占比
                            'tel3numpct' => '13796',//固话号占比
                            'uidnumge100w' => '13796',//频数ge100万的uid个数
                            'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                            'uidnumge10w' => '13796',//频数ge10万的uid个数
                            'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                            'uidnumge1w' => '13796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                            'uidnumge1000' => '13796',//频数ge1000的uid个数
                            'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                            'uidnumge100' => '13796',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '13796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '13796',//频数等于1的uid个数
                            'uidnume1pct' => '13796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '13796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '13796',//频数le5的uid占比
                            'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                        ],
                        '20210602'=>[
                            "date"=>"20210602",
                            'cnt' => '882',//请求量
                            'uidnum' => '1379',//uid个数
                            'telnum' => '1379701',//号码个数
                            'tel1num' => '169', //手机号个数
                            'tel2num' => '123', //热线号个数
                            'tel3num' => '13796', //固定号码个数
                            'uidnumpct' => '137',//uid密度
                            'telnumpct' => '13796',//号码密度
                            'tel1numpct' => '13796',//手机号占比
                            'tel2numpct' => '13796',//热线号占比
                            'tel3numpct' => '13796',//固话号占比
                            'uidnumge100w' => '13796',//频数ge100万的uid个数
                            'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                            'uidnumge10w' => '13796',//频数ge10万的uid个数
                            'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                            'uidnumge1w' => '13796',//频数ge1万的uid个数
                            'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                            'uidnumge1000' => '13796',//频数ge1000的uid个数
                            'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                            'uidnumge100' => '13796',//频数ge100的uid个数
                            'uidnumge100pct' => '13796',//频数ge100的uid占比
                            'uidnumge50' => '13796',//频数ge50的uid个数
                            'uidnumge50pct' => '13796',//频数ge50的uid占比
                            'uidnume1' => '13796',//频数等于1的uid个数
                            'uidnume1pct' => '13796',//频数等于1的uid占比
                            'uidnumle2' => '13796',//频数le2的uid个数
                            'uidnumle2pct' => '13796',//频数le2的uid占比
                            'uidnumle5' => '13796',//频数le5的uid个数
                            'uidnumle5pct' => '13796',//频数le5的uid占比
                            'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                            'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                            'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                            'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                            'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                            'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                            'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                        ]
                    ]
                ];

            //$arr2 = json_decode($str, JSON_UNESCAPED_UNICODE);
            $this->ajaxReturn($arr3);

        }else if($flag == 'qudao'){
            $str = '';
            foreach($chal_info as $key=>$value){
                $str .= '<option value="'.$value['chnlid'].'">'.$value['chnl_name'].'</option>';
            }
            $arr3 = [
                'code' => 0,
                'message' => '',
                'status' => 0,
                'msg' => '',
                'data' => $str
            ];
            $this->ajaxReturn($arr3);

        }
        $this->display();
    }


    /**
     * 曲线图统计
     */
    public function totalQuxian()
    {
        $flag = I('get.flag');

        if($flag == 'quxian'){
            // $str = '{"msg":"请求成功","status":0,"data":{"********":{"date":"********","total":"1623566","success":"1620699","valid":"975287","cache":"64503","money":"-","number":"431259"},"********":{"date":********,"total":"1123887","success":"1119901","valid":"756680","cache":"60333","money":"-","number":"442854"},"20210528":{"date":20210528,"total":"1435090","success":"1433572","valid":"922990","cache":"82366","money":"-","number":"466519"},"20210529":{"date":20210529,"total":"1931774","success":"1927811","valid":"1285763","cache":"109201","money":"-","number":"526881"},"20210530":{"date":20210530,"total":"991216","success":"988307","valid":"681039","cache":"57492","money":"-","number":"411583"},"20210531":{"date":20210531,"total":"1799035","success":"1795312","valid":"1135828","cache":"111363","money":"-","number":"538247"},"20210601":{"date":20210601,"total":"2297242","success":"2294698","valid":"1322562","cache":"104442","money":"-","number":"531853"},"20210602":{"date":20210602,"total":"594412","success":"592246","valid":"355599","cache":"19154","money":"-","number":"115570"}}}';

            $date_start = I('get.date_start');
            $date_end = I('get.date_end');

            $channel_num = D('CollectNum');
            $where = [];
            $where['day'][] = array('EGT', $date_start);
            $where['day'][] = array('ELT',$date_end);

            $channel_info = $channel_num->where($where)->select();

            $res_arr = [];
            foreach($channel_info as $key=>&$value){
                $date = date('Ymd', strtotime($value['day']));
                $value['date'] = $date;
                $value['chnl_name'] = isset($chal_name_arr[$value['chnlid']]) ? $chal_name_arr[$value['chnlid']] : '未知';
                $res_arr[$date] = $value;
            }

            $res = [
                'msg' => '请求成功',
                'status' => 0,
                'data' => $res_arr
            ];
            $this->ajaxReturn($res);
            $arr3 = [
                'msg' => '请求成功',
                'status' => 0,
                'data' => [
                    '20210521'=>[
                        "date"=>"20210521",
                        'cnt' => '582',//请求量
                        'uidnum' => '13579',//uid个数
                        'telnum' => '159701',//号码个数
                        'tel1num' => '1659', //手机号个数
                        'tel2num' => '1235', //热线号个数
                        'tel3num' => '137596', //固定号码个数
                        'uidnumpct' => '137',//uid密度
                        'telnumpct' => '135796',//号码密度
                        'tel1numpct' => '135796',//手机号占比
                        'tel2numpct' => '135796',//热线号占比
                        'tel3numpct' => '137596',//固话号占比
                        'uidnumge100w' => '137596',//频数ge100万的uid个数
                        'uidnumge100wpct' => '153796',//频数ge100万的uid占比
                        'uidnumge10w' => '137596',//频数ge10万的uid个数
                        'uidnumge10wpct' => '153796',//频数ge10万的uid占比
                        'uidnumge1w' => '135796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '137596',//频数ge1万的uid占比
                        'uidnumge1000' => '137596',//频数ge1000的uid个数
                        'uidnumge1000pct' => '135796',//频数ge1000的uid占比
                        'uidnumge100' => '137596',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '153796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '513796',//频数等于1的uid个数
                        'uidnume1pct' => '135796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '135796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '135796',//频数le5的uid占比
                        'ngt1uidnum' => '137296',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '123796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '123796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '137926',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '123796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '132796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '132796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '********'=>[
                        "date"=>"********",
                        'cnt' => '582',//请求量
                        'uidnum' => '13579',//uid个数
                        'telnum' => '159701',//号码个数
                        'tel1num' => '1659', //手机号个数
                        'tel2num' => '1235', //热线号个数
                        'tel3num' => '137596', //固定号码个数
                        'uidnumpct' => '137',//uid密度
                        'telnumpct' => '135796',//号码密度
                        'tel1numpct' => '135796',//手机号占比
                        'tel2numpct' => '135796',//热线号占比
                        'tel3numpct' => '137596',//固话号占比
                        'uidnumge100w' => '137596',//频数ge100万的uid个数
                        'uidnumge100wpct' => '153796',//频数ge100万的uid占比
                        'uidnumge10w' => '137596',//频数ge10万的uid个数
                        'uidnumge10wpct' => '153796',//频数ge10万的uid占比
                        'uidnumge1w' => '135796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '137596',//频数ge1万的uid占比
                        'uidnumge1000' => '137596',//频数ge1000的uid个数
                        'uidnumge1000pct' => '135796',//频数ge1000的uid占比
                        'uidnumge100' => '137596',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '153796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '513796',//频数等于1的uid个数
                        'uidnume1pct' => '135796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '135796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '135796',//频数le5的uid占比
                        'ngt1uidnum' => '137296',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '123796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '123796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '137926',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '123796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '132796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '132796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '20210528'=>[
                        "date"=>"20210528",
                        'cnt' => '852',//请求量
                        'uidnum' => '14379',//uid个数
                        'telnum' => '13479701',//号码个数
                        'tel1num' => '1469', //手机号个数
                        'tel2num' => '1423', //热线号个数
                        'tel3num' => '143796', //固定号码个数
                        'uidnumpct' => '1347',//uid密度
                        'telnumpct' => '134796',//号码密度
                        'tel1numpct' => '134796',//手机号占比
                        'tel2numpct' => '13796',//热线号占比
                        'tel3numpct' => '13796',//固话号占比
                        'uidnumge100w' => '137496',//频数ge100万的uid个数
                        'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                        'uidnumge10w' => '137496',//频数ge10万的uid个数
                        'uidnumge10wpct' => '134796',//频数ge10万的uid占比
                        'uidnumge1w' => '137496',//频数ge1万的uid个数
                        'uidnumge1wpct' => '143796',//频数ge1万的uid占比
                        'uidnumge1000' => '137946',//频数ge1000的uid个数
                        'uidnumge1000pct' => '137496',//频数ge1000的uid占比
                        'uidnumge100' => '134796',//频数ge100的uid个数
                        'uidnumge100pct' => '143796',//频数ge100的uid占比
                        'uidnumge50' => '143796',//频数ge50的uid个数
                        'uidnumge50pct' => '143796',//频数ge50的uid占比
                        'uidnume1' => '137946',//频数等于1的uid个数
                        'uidnume1pct' => '137496',//频数等于1的uid占比
                        'uidnumle2' => '137946',//频数le2的uid个数
                        'uidnumle2pct' => '134796',//频数le2的uid占比
                        'uidnumle5' => '137946',//频数le5的uid个数
                        'uidnumle5pct' => '137496',//频数le5的uid占比
                        'ngt1uidnum' => '137946',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '20210529'=>[
                        "date"=>"20210529",
                        'cnt' => '8244',//请求量
                        'uidnum' => '41379',//uid个数
                        'telnum' => '179701',//号码个数
                        'tel1num' => '1469', //手机号个数
                        'tel2num' => '1423', //热线号个数
                        'tel3num' => '134796', //固定号码个数
                        'uidnumpct' => '1437',//uid密度
                        'telnumpct' => '134796',//号码密度
                        'tel1numpct' => '137496',//手机号占比
                        'tel2numpct' => '134796',//热线号占比
                        'tel3numpct' => '137496',//固话号占比
                        'uidnumge100w' => '13796',//频数ge100万的uid个数
                        'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                        'uidnumge10w' => '13796',//频数ge10万的uid个数
                        'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                        'uidnumge1w' => '13796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                        'uidnumge1000' => '13796',//频数ge1000的uid个数
                        'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                        'uidnumge100' => '13796',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '13796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '13796',//频数等于1的uid个数
                        'uidnume1pct' => '13796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '13796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '13796',//频数le5的uid占比
                        'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '20210530'=>[
                        "date"=>"20210530",
                        'cnt' => '182',//请求量
                        'uidnum' => '1379',//uid个数
                        'telnum' => '1379701',//号码个数
                        'tel1num' => '169', //手机号个数
                        'tel2num' => '123', //热线号个数
                        'tel3num' => '13796', //固定号码个数
                        'uidnumpct' => '137',//uid密度
                        'telnumpct' => '13796',//号码密度
                        'tel1numpct' => '13796',//手机号占比
                        'tel2numpct' => '13796',//热线号占比
                        'tel3numpct' => '13796',//固话号占比
                        'uidnumge100w' => '13796',//频数ge100万的uid个数
                        'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                        'uidnumge10w' => '13796',//频数ge10万的uid个数
                        'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                        'uidnumge1w' => '13796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                        'uidnumge1000' => '13796',//频数ge1000的uid个数
                        'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                        'uidnumge100' => '13796',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '13796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '13796',//频数等于1的uid个数
                        'uidnume1pct' => '13796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '13796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '13796',//频数le5的uid占比
                        'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '20210531'=>[
                        "date"=>"20210531",
                        'cnt' => '822',//请求量
                        'uidnum' => '1379',//uid个数
                        'telnum' => '1379701',//号码个数
                        'tel1num' => '169', //手机号个数
                        'tel2num' => '123', //热线号个数
                        'tel3num' => '13796', //固定号码个数
                        'uidnumpct' => '137',//uid密度
                        'telnumpct' => '13796',//号码密度
                        'tel1numpct' => '13796',//手机号占比
                        'tel2numpct' => '13796',//热线号占比
                        'tel3numpct' => '13796',//固话号占比
                        'uidnumge100w' => '13796',//频数ge100万的uid个数
                        'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                        'uidnumge10w' => '13796',//频数ge10万的uid个数
                        'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                        'uidnumge1w' => '13796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                        'uidnumge1000' => '13796',//频数ge1000的uid个数
                        'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                        'uidnumge100' => '13796',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '13796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '13796',//频数等于1的uid个数
                        'uidnume1pct' => '13796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '13796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '13796',//频数le5的uid占比
                        'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '20210601'=>[
                        "date"=>"20210601",
                        'cnt' => '826',//请求量
                        'uidnum' => '1379',//uid个数
                        'telnum' => '1379701',//号码个数
                        'tel1num' => '169', //手机号个数
                        'tel2num' => '123', //热线号个数
                        'tel3num' => '13796', //固定号码个数
                        'uidnumpct' => '137',//uid密度
                        'telnumpct' => '13796',//号码密度
                        'tel1numpct' => '13796',//手机号占比
                        'tel2numpct' => '13796',//热线号占比
                        'tel3numpct' => '13796',//固话号占比
                        'uidnumge100w' => '13796',//频数ge100万的uid个数
                        'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                        'uidnumge10w' => '13796',//频数ge10万的uid个数
                        'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                        'uidnumge1w' => '13796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                        'uidnumge1000' => '13796',//频数ge1000的uid个数
                        'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                        'uidnumge100' => '13796',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '13796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '13796',//频数等于1的uid个数
                        'uidnume1pct' => '13796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '13796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '13796',//频数le5的uid占比
                        'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                    ],
                    '20210602'=>[
                        "date"=>"20210602",
                        'cnt' => '882',//请求量
                        'uidnum' => '1379',//uid个数
                        'telnum' => '1379701',//号码个数
                        'tel1num' => '169', //手机号个数
                        'tel2num' => '123', //热线号个数
                        'tel3num' => '13796', //固定号码个数
                        'uidnumpct' => '137',//uid密度
                        'telnumpct' => '13796',//号码密度
                        'tel1numpct' => '13796',//手机号占比
                        'tel2numpct' => '13796',//热线号占比
                        'tel3numpct' => '13796',//固话号占比
                        'uidnumge100w' => '13796',//频数ge100万的uid个数
                        'uidnumge100wpct' => '13796',//频数ge100万的uid占比
                        'uidnumge10w' => '13796',//频数ge10万的uid个数
                        'uidnumge10wpct' => '13796',//频数ge10万的uid占比
                        'uidnumge1w' => '13796',//频数ge1万的uid个数
                        'uidnumge1wpct' => '13796',//频数ge1万的uid占比
                        'uidnumge1000' => '13796',//频数ge1000的uid个数
                        'uidnumge1000pct' => '13796',//频数ge1000的uid占比
                        'uidnumge100' => '13796',//频数ge100的uid个数
                        'uidnumge100pct' => '13796',//频数ge100的uid占比
                        'uidnumge50' => '13796',//频数ge50的uid个数
                        'uidnumge50pct' => '13796',//频数ge50的uid占比
                        'uidnume1' => '13796',//频数等于1的uid个数
                        'uidnume1pct' => '13796',//频数等于1的uid占比
                        'uidnumle2' => '13796',//频数le2的uid个数
                        'uidnumle2pct' => '13796',//频数le2的uid占比
                        'uidnumle5' => '13796',//频数le5的uid个数
                        'uidnumle5pct' => '13796',//频数le5的uid占比
                        'ngt1uidnum' => '13796',//号码个数ge2的uid个数
                        'ngt1uidnumgt1d' => '13796',//号码个数ge2的uid占比
                        'ngt1uidnumgt1dpct' => '13796',//号码个数ge2且近30d天数ge2的uid个数
                        'ngt1uidnumgt5d' => '13796',//号码个数ge2且近30d天数ge5的uid个数
                        'ngt1uidnumgt5dpct' => '13796',//号码个数ge2且近30d天数ge5的uid占比
                        'ngt1uidnumgt10d' => '13796',//号码个数ge2且近30d天数ge10的uid个数
                        'ngt1uidnumgt10dpct' => '13796',//号码个数ge2且近30d天数ge10的uid占比
                    ]
                ]
            ];

            //$arr2 = json_decode($str, JSON_UNESCAPED_UNICODE);
            $this->ajaxReturn($arr3);

        }
        $this->display();
    }

}
