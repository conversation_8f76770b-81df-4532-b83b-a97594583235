<!DOCTYPE html>
<html lang="en">
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__xm-select.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }

        .index-btn {
            margin : 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container">
    <form class="layui-form layui-row list_form">
        <div style="display: flex">
            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:235px">
                <label class="layui-form-label">开始时间</label>
                <div class="layui-input-block">
                    <input type="text" name="date_start" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="date_start" value="{:date('Y-m-d H:i:s', strtotime('-1 months'))}">
                </div>
            </div>
            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:235px">
                <label class="layui-form-label">结束时间</label>
                <div class="layui-input-block">
                    <input type="text" name="date_end" placeholder="请选择结束时间" autocomplete="off" class="layui-input" id="date_end" value="{:date('Y-m-d H:i:s')}">
                </div>
            </div>
            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
                <label class="layui-form-label">选择渠道 </label>
                <div class="layui-input-block">
                    <div name='channel[]' id="channel" class="xm-select-demo"></div>
                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="margin-left:10px;">
                <div class="layui-btn layui-btn-normal" id="query">
                    <i class="layui-icon">&#xe615;</i> 查询
                </div>
            </div>
        </div>

    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js"></script>
<script type="application/javascript">
    layui.laydate.render({
        elem  : '#date_start',
        type  : 'datetime',
    });
    layui.laydate.render({
        elem  : '#date_end',
        type  : 'datetime',
    });
    channel = xmSelect.render({
        el: '#channel',
        repeat: true,
        data: JSON.parse('{$data.channelOption}')
    });

    //加载表格
    function loadTable(initWhere = {}) {
        //获取查询的where条件
        let url = "/Monitor/BxfShort/channelStatis";
        let where = getWhere();
        where     = Object.assign(where, initWhere);

        //异步请求数据
        let index = layer.load(0, {shade : [0.3, '#393D49']});
        let fields = [[
            {
                field    : 'channel_label',
                title    : '渠道',
                align    : 'center',
                width    : '15%',
            },
            {
                field    : 'custom_key',
                title    : '接口',
                align    : 'center',
                width    : '15%',
            },
            {
                field    : 'end_date',
                title    : '统计时间',
                align    : 'center',
            },
            {
                field    : 'start_date',
                title    : '统计开始时间',
                align    : 'center',
            },
            {
                field    : 'all',
                title    : '请求计次',
                align    : 'right',
            },
            {
                field    : 'error_ratio',
                title    : '总异常占比',
                align    : 'right',
            },
            {
                field    : 'over_time_ratio',
                title    : '超时占比',
                align    : 'right',
            },
            {
                field    : 'not_valid_ratio',
                title    : '未查得占比',
                align    : 'right',
            },
            {
                field    : 'other_ratio',
                title    : '其它异常占比',
                align    : 'right',
            }
        ]];

        layui.table.render({
            elem           : '#list_table',
            // height         : 'full-280',
            title          : '渠道查得率',
            url            : url,
            method         : 'post',
            where          : where,
            page           : true,
            limits         : [15, 30, 60, 100, 1000],
            limit          : 15,
            cols           : fields,
            parseData      : function (res) {   //渲染表格之前数据校验和数据格式整理
                if (0 !== Number(res.status)) {
                    layer.close(index);
                    layer.alert("接口请求失败，请联系开发人员", {icon : 2, shade : [0.3, '#393D49']});
                    return;
                }
                json = {
                    code : 0,
                    count : res.data.count,
                    data : res.data.data
                };
                $.each(json.data, function(k,v){
                    json.data[k]['error_ratio'] += '%';
                    json.data[k]['over_time_ratio'] += '%';
                    json.data[k]['not_valid_ratio'] += '%';
                    json.data[k]['other_ratio'] += '%';
                });
                return json;
            },
            //禁用前端排序
            autoSort       : false,
            // cellMinWidth   : 200,
            done            : function(res, curr, count) {  //渲染完表格之后事件
                layer.close(index);
            }
        });
    }

    //获取查询where条件
    function getWhere() {
        let where         = {};
        let date_start = $("#date_start").val();
        let date_end = $("#date_end").val();
        if (('' === date_start) || ('' === date_end)) {
            layer.alert('请选择日期区间', {icon : 2, shade : [0.3, '#393D49']});
            return false;
        }
        where.start_date   = date_start;
        where.end_date     = date_end;

        where.channel = channel.getValue('value');
        return where;
    }

    //条件查询
    $("#query").click(function () {
        loadTable();
    });

    //排序
    layui.table.on('sort(table)', function (obj) {
        loadTable({
            sort_field : obj.field,
            sort_type  : obj.type
        }, obj);
    });

    loadTable();
</script>