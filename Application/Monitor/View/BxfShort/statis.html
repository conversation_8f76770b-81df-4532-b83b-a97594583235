<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        .row-first {
            margin-bottom : 10px;
        }

        label {
            margin-left : 10px;
        }

        td, th {
            text-align : center;
        }

        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
    <script type="application/javascript" src="__STATICS__/layui-v2.5.4/layui/layui.all.js"></script>
    <script type="application/javascript" src="__STATICS__/js/echarts.min.js"></script>
    <link rel="stylesheet" href="__STATICS__/layui-v2.5.4/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="form-inline" id="form_init">
                <div class="form-group">
                    <label class="control-label" for="start_date">选择产品：</label>
                    <select name="product_id" id="product_id">
                        <volist name="productInfo" id="productItem">
                            <option value="{$productItem.product_id}">{$productItem.product_name}</option>
                        </volist>
                    </select>
                </div>

                <div class="form-group">
                    <label class="control-label" for="start_date">时间区间：</label>
                    <input type="date" name="start_date" id="start_date" class="form-control"
                           value="{:date('Y-m-d', strtotime('-1 days'))}"/>
                    -
                    <input type="date" name="end_date" id="end_date" class="form-control"
                           value="{:date('Y-m-d', strtotime('-1 days'))}"/>
                </div>
                <div class="form-group">
                    <label for="customer_id">选择客户</label>
                    <select name="customer_id" id="customer_id">
                        <option value="">全部客户</option>
                        {$customerOption}
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="operator_type">渠道：</label>
                    <select class="form-control" name="operator_type" id="operator_type">
                        <option value="">全部</option>
                        <option value="0">电信</option>
                        <option value="1">联通</option>
                        <option value="3">移动</option>
                    </select>
                </div>
                <div class="form-group">
                    <input id="searchBtn" type="button" onclick="reloadMonitor()" class="btn btn-primary btn-sm"
                           value="查询">
                </div>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <table class="table table-bordered">
        <tr>
            <td>总查询数</td>
            <td id="total"></td>
            <td>PSI</td>
            <td id="PSI"></td>
            <td id="PSI_string"></td>
        </tr>
        <tr>
            <td>有效返回数</td>
            <td id="success"></td>
            <td>无效返回数</td>
            <td id="failed" colspan="2"></td>
        </tr>
        <tr>
            <td>有效返回占比</td>
            <td id="successRate"></td>
            <td>无效返回占比</td>
            <td id="failedRate" colspan="2"></td>
        </tr>
    </table>
</div>
<div class="container">
    <div id="chart" style="width:100%;margin:0 auto;height: 400px;"></div>
</div>
<script type="text/javascript">
    $("#customer_id").select2({
        allowClear : true,
        theme      : "bootstrap",
        placeholder: '选择客户'
    });
    $("#product_id").select2({
        allowClear: true,
        theme     : "bootstrap",
    });
    function clickTab(product_id) {
        $(".navbar-header").removeClass('checked');
        $('.navbar-header[data-product-id="' + product_id + '"]').addClass('checked');
        reloadMonitor();
    }
    function reloadMonitor() {
        let product_id    = $('#product_id').val();
        let start_date    = $("#start_date").val();
        let end_date      = $("#end_date").val();
        let customer_id   = $("#customer_id").val();
        let operator_type = $("#operator_type").val();

        let option = {};
        if (start_date === '') {
            alert('请选择一个时间区间');
            return false;
        }
        if (end_date === '') {
            alert('请选择一个时间区间');
            return false;
        }
        option.start_date = start_date;
        option.end_date   = end_date;
        option.product_id = product_id;
        if (customer_id !== '') {
            option.customer_id = customer_id;
        }
        if (operator_type !== '') {
            option.operator_type = operator_type;
        }

        let index = layui.layer.load();
        //异步拉取数据
        $.ajax({
            url    : '',
            type   : 'post',
            data   : option,
            success: function (res) {
                if (Number(res.status) !== 0) {
                    alert(res.msg);
                } else {
                    initChart(res.data);
                    initTable(res.data);
                }
                layui.layer.close(index);
            },
            error  : function (res) {
                alert('请求失败');
                layui.layer.close(index);
            }
        });
    }
    function initChart(response) {

        let option = {
            legend : {
                width: '100%',
                data : ['近七天对照数据', '查询数据']
            },
            title  : {
                text: ''                 // 标题
            },
            xAxis  : {
                name: '分段',
                type: 'category',
                data: response.x
            },
            yAxis  : {
                name: '有效占比（%）',
                type: 'value',
                min : 0,
                max : 100
            },
            series : [{
                name  : '近七天对照数据',
                data  : response.week,
                type  : 'bar',
                barGap: 0
            }, {
                name  : '查询数据',
                data  : response.search,
                type  : 'bar',
                barGap: 0
            }],
            tooltip: {
                trigger    : 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            }
        };

        let myChart = echarts.init(document.getElementById('chart'));
        myChart.setOption(option);

    }

    function initTable(response) {
        $("#total").html(response.total);
        $("#success").html(response.success);
        $("#failed").html(response.failed);
        $("#successRate").html(response.successRate);
        $("#failedRate").html(response.failedRate);
        $("#PSI").html(response.psi);
        $("#PSI_string").html(getPSIString(response.psi));
    }

    function getPSIString(psi) {
        if (psi <= 0.1) {
            return '<span style="color:green;">稳定</span>';
        } else if (psi > 0.1 && psi <= 0.25) {
            return '<span style="color:green;">关注</span>';
        } else {
            return '<span style="color:red;">不稳定</span>';
        }
    }
    reloadMonitor();
</script>
</body>
</html>
