<!DOCTYPE html>
<html lang="en">
    <head>
        <link rel="stylesheet" type="text/css" href="__JS__vue/index.css?v=2"/>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__vue/vue.js?v=2"></script>
        <script type="application/javascript" src="__JS__vue/index.js?v=2"></script>
        <script type="application/javascript" src="__JS__vue/axios.min.js?v=2"></script>

    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <style>
        .showText {
            white-space: pre-wrap;   /*这是重点。文本换行*/
            padding-left: 4px;
            padding-right: 4px;
        }
    </style>

    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body">
                    <form action="" class="form-inline" method="get" id="list_form">
                        <template>
                        <div class="form-group">
                            <label class="control-label" >报警日期</label>
                            <el-date-picker
                                    v-model="searchForm.start_time"
                                    type="datetime"
                                    placeholder="选择日期时间">
                            </el-date-picker>
                        </div>

                        <div class="form-group" style="margin-right: 30px;">
                            <span class="demonstration">-</span>
                            <el-date-picker
                                    v-model="searchForm.end_time"
                                    type="datetime"
                                    placeholder="选择日期时间">
                            </el-date-picker>
                        </div>

                        <div class="form-group" style="margin-right: 30px;">
                            <label class="control-label" >报警级别</label>
                            <el-select v-model="searchForm.level" filterable clearable placeholder="全部" style="width: 100px">
                                <el-option
                                        v-for="item in levelOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </div>

                        <div class="form-group" style="margin-right: 30px;">
                            <label class="control-label" >是否处理</label>
                            <el-select v-model="searchForm.haveo" filterable clearable placeholder="全部" style="width: 100px">
                                <el-option
                                        v-for="item in operateOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </div>

                        <div class="form-group" style="margin-right: 30px;">
                            <label class="control-label" >负责人</label>
                            <el-select v-model="searchForm.ruid" filterable clearable placeholder="全部" style="width: 100px">
                                <el-option
                                        v-for="item in nameOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </div>

                            <div class="form-group" style="margin-right: 20px;">
                                <label class="control-label" >报警业务</label>
                                <el-select v-model="searchForm.business_type" filterable clearable placeholder="全部" style="width: 120px">
                                    <el-option
                                            v-for="item in businessOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>

                            <br/> <br/>

                            <div class="form-group" style="margin-left: 35px;margin-right: 40px;">
                                <label class="control-label" >ID</label>
                                <div class="el-select" style="width: 120px">
                                    <el-input v-model="searchForm.aid"></el-input>
                                </div>

                            </div>


                        <div class="form-group">
                            <el-button type="primary" @click="getTableData()">查询</el-button>
                        </div>

                        <div class="form-group">
                            <el-button type="success"
                                       @click="batchDeal"
                                       :disabled="multipleSelection.length === 0"
                            >
                                批量处理
                            </el-button>
                        </div>
                            </template>
                    </form>
                </div>
            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>
                    <el-table
                            :data="tableData"
                            tooltip-effect="dark"
                            @selection-change="handleSelectionChange"
                            border
                            style="width: 100%">
                        <el-table-column
                                type="selection"
                                width="55">
                        </el-table-column>
                        <el-table-column
                                prop="id"
                                label="ID"
                                width="110">
                        </el-table-column>
                        <el-table-column
                                prop="rname"
                                label="负责人"
                                width="110">
                        </el-table-column>
                        <el-table-column
                                prop="type_name"
                                label="报警来源"
                                width="110">
                        </el-table-column>
                        <el-table-column
                                prop="level_name"
                                label="报警级别"
                                width="110">
                        </el-table-column>
                        <el-table-column
                                prop="haveo"
                                label="处理情况"
                                width="110">
                        </el-table-column>
                        <el-table-column
                                prop="ctime"
                                label="报警时间"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="oname"
                                label="操作人"
                                width="110">
                        </el-table-column>
                        <el-table-column
                                prop="content"
                                label="报警内容"
                                >
                            <template slot-scope="scope">
                                <el-popover
                                        placement="top-start"
                                        width="240"
                                        trigger="hover"
                                       >
                                    <p class="showText">{{ scope.row.content }}</p>
                                    <el-button slot="reference" icon="el-icon-s-comment">查看</el-button>

                                </el-popover>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="170">
                            <template slot-scope="scope">
                                <el-button
                                        size="mini"
                                        :disabled="scope.row.is_haveo"
                                        type="success"
                                        @click="handleEdit(scope.$index, scope.row.id)">处理</el-button>

                            </template>
                        </el-table-column>

                    </el-table>
                </template>

                <el-dialog :title="dialog_title" :visible.sync="dialogFormVisible" @close="closeDialogCallBack('form')" class="customwidth">
                    <el-form :model="form" :rules="rules" ref="form">

                        <!--
                        <el-form-item label="占比" prop="ratio" :label-width="formLabelWidth">
                            <el-input v-model="form.ratio" autocomplete="off"></el-input>
                        </el-form-item>
                        -->


                            <el-input
                                    type="hidden"
                                    v-model="form.id">
                            </el-input>
                        <el-form-item label="备注" prop="note" :label-width="formLabelWidth">
                            <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入内容"
                                    v-model="form.note">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="暂停选项" prop="otype" :label-width="formLabelWidth">
                            <el-radio-group v-model="form.otype">
                                <el-radio-button label="1">暂停10分钟</el-radio-button>
                                <el-radio-button label="2">暂停1小时</el-radio-button>
                                <el-radio-button label="3">暂停半天</el-radio-button>
                                <el-radio-button label="4">暂停1天</el-radio-button>
                                <el-radio-button label="6">已处理</el-radio-button>
                            </el-radio-group>
                        </el-form-item>



                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="dialogFormVisible = false">取 消</el-button>
                        <el-button type="primary" @click="onSubmit('form')">确 定</el-button>
                    </div>
                </el-dialog>

            </div>

            <div class="block" style="margin-bottom: 16px;">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[20, 30, 40, 50]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalNum">
                </el-pagination>
            </div>

        </div>

    </div>

    <script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
    <script type="application/javascript">
        var user_cookie = $.cookie('PHPSESSID');
        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/manage/alarmLogList";

        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                totalNum: 0,
                pageSize: 20,
                currentPage:1,
                nameOptions: [],//负责人列表
                businessOptions: [],//报警业务列表
                levelOptions: [{
                    value: '2',
                    label: '严重'
                },{
                    value: '3',
                    label: '关注'
                },{
                    value: '4',
                    label: '提醒'
                }],
                operateOptions: [{
                    value: '1',
                    label: '已处理'
                },{
                    value: '0',
                    label: '未处理'
                }],
                pickerOptions: {},
                getUrl: url,
                dialogFormVisible: false,
                dialog_title:'报警操作',
                multipleSelection: [],
                form: {
                    id:'',
                    note: '',
                    otype: ''
                },
                formLabelWidth: '120px',
                searchForm: {
                    start_time: '',
                    end_time: '',
                    level: '',
                    haveo: '',
                    ruid: '',
                    business_type:'',
                    aid:''
                },
                rules:{
                    /*
                  channel_id: [
                      { required: true, message: '请选择渠道', trigger: 'change' }
                  ],
                  start_time: [
                      { type: 'string', required: true, message: '请选择开始时间', trigger: 'change' }
                  ],
                  end_time: [
                      { type: 'string', required: true, message: '请选择结束时间', trigger: 'change' }
                  ],
                  keep_time: [
                      { required: true, message: '持续时间不能为空' },
                      { type: 'number', message: '持续时间必须为数字值'}
                  ],
                  total_num: [
                      { required: true, message: '总条数不能为空' },
                      { type: 'number', message: '总条数必须为数字值'}
                  ],
                  affect_num: [
                      { required: true, message: '影响条数不能为空' },
                      { type: 'number', message: '影响条数必须为数字值'}
                  ],
                  ratio: [
                      { required: true, message: '占比不能为空' },
                      { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输入正确格式,可保留两位小数' }
                  ],
                  */
                    note: [
                        { required: true, message: '请输入备注'}
                    ],
                    otype: [
                        { required: true, message: '请选择暂停选项', trigger: 'change' }
                    ]
                }
            },
            created: function(){
                this.getTableData();
                this.getSelectData();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    var start_time = this.searchForm.start_time;
                    var end_time = this.searchForm.end_time;
                    var level = this.searchForm.level;
                    var haveo = this.searchForm.haveo;
                    var ruid = this.searchForm.ruid;
                    var business_type = this.searchForm.business_type;
                    var aid = this.searchForm.aid;

                    var where = {limit:this.pageSize, page:this.currentPage, user_cookie:user_cookie};
                    if(start_time){
                        where.start_time = start_time;
                    }
                    if(end_time){
                        where.end_time = end_time;
                    }
                    if(start_time){
                        where.start_time = start_time;
                    }
                    if(level){
                        where.level = level;
                    }
                    if(haveo){
                        where.haveo = haveo;
                    }
                    if(ruid){
                        where.ruid = ruid;
                    }
                    if(business_type){
                        where.business_type = business_type;
                    }
                    if(aid){
                        where.aid = aid;
                    }
                    axios.post(url, where).then(function (response) {
                        //console.log(response);
                        self.tableData = response.data.data.list;
                        self.totalNum = response.data.data.count;
                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                getSelectData:function(){
                    var self = this;
                    //var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelList";
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/manage/getMembers";
                    axios.get(url, {}).then(function (response) {
                        //console.log(response.data.data);
                        response.data.data.forEach((item, index) => {
                            //console.log(item.id + item.name);
                            self.nameOptions.push({value:item.id,label:item.name});
                        });

                    }).catch(function (error) {
                        console.log(error);
                    });

                    var url2 = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/manage/getMonitorItems";
                    axios.get(url2, {}).then(function (response) {
                        //console.log(response.data.data);
                        response.data.data.forEach((item, index) => {
                            //console.log(item.id + item.name);
                            self.businessOptions.push({value:item.prefix,label:item.name});
                        });

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                onSubmit:function(formName){

                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            var request_params = this.form;
                            var self = this;
                            var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/manage/deal";
                            request_params.user_cookie = user_cookie;

                            axios.post(url, request_params).then(function (response) {
                                if(response.data.code == 0){
                                    successMsg(response.data.msg);

                                }else{
                                    errorMsg(response.data.msg);
                                }
                            }).catch(function (error) {
                                console.log(error);
                                errorMsg(error);
                            });

                            //alert('submit!');
                        } else {
                            console.log('no submit!!');
                            return false;
                        }
                    });



                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.getTableData();
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                    this.currentPage = val;
                    this.getTableData();
                },
                closeDialogCallBack:function(formName){
                    this.$refs[formName].resetFields();
                },
                handleSelectionChange(val) {
                    this.multipleSelection = val;
                },
                batchDeal(){
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/manage/batchDeal";
                    var arr = [];
                    //遍历点击选择的对象集合，拿到每一个对象的id添加到新的集合中
                    this.multipleSelection.forEach(row=>arr.push(row.id));
                    this.$confirm('确定要批量处理吗', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'success',
                        callback: action => {
                            if (action === 'confirm') {

                                axios.post(url, {user_cookie:user_cookie,ids:arr}).then(function (response) {
                                    if(response.data.code == 0){
                                        successMsg(response.data.msg);

                                    }else{
                                        errorMsg(response.data.msg);
                                    }
                                }).catch(function (error) {
                                    console.log(error);
                                    errorMsg(error);
                                });

                            }
                        }

                    });
                    console.log(arr);
                },
                handleEdit(index, id) {
                    var self = this;
                    self.dialog_title = '报警操作ID'+id;
                    self.dialogFormVisible = true;
                    self.form.id = id;
                    self.form.note = '';
                    self.form.otype = '';

                    /*
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/getChannelException";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.form.id = response.data.data.id;
                            self.form.channel_id = response.data.data.channel_id;
                            self.form.start_time = response.data.data.start_time;
                            self.form.end_time = response.data.data.end_time;
                            //self.form.keep_time = response.data.data.keep_time;
                            self.form.total_num = response.data.data.total_num;
                            self.form.affect_num = response.data.data.affect_num;
                            self.form.reason = response.data.data.reason;

                            self.dialog_title = '报警操作';
                            self.dialogFormVisible = true;

                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                    */

                },
                handleDelete(index, id) {
                    var self = this;

                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/delChannelException";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            //vm.$refs['form'].resetFields();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }

    </script>
    </body>
</html>
