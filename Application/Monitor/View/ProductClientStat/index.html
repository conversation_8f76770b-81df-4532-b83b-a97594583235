<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>值分布统计(全部)</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        .layui-table th {
            text-align : center !important;
        }

        .layui-tab-title {
            user-select : none;
        }

        .layui-field-box {
            margin-bottom : 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form">

        <div class="layui-form-item">
            <label class="layui-form-label" for="date_start">开始时间</label>
            <div class="layui-input-block">
                <input type="text" name="date_start" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="date_start" value="{:date('Y-m-d 00:00:00')}">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" for="date_end">结束时间</label>
            <div class="layui-input-block">
                <input type="text" name="date_end" placeholder="请选择结束时间" autocomplete="off" class="layui-input" id="date_end" value="{:date('Y-m-d 23:59:59')}">
            </div>
        </div>

        <div class="layui-form-item" style="width: 350px">
            <label class="layui-form-label">产品：</label>
            <div class="layui-input-block">
                <select name="product_id" lay-search id="product_id">
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">账号：</label>
            <div class="layui-input-block">
                <select name="apikey" lay-search id="apikey">
                </select>
            </div>
        </div>

        <div class="layui-form-item ">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>

<!--        <div style="margin-left: 20px; margin-bottom: 10px">-->
<!--            <div class="layui-btn layui-btn-sm layui-btn-warm btn-day" id="tenMinutes">近十分钟</div>-->
<!--            <div class="layui-btn layui-btn-sm layui-btn-warm btn-day" id="twentyMinutes">近二十分钟</div>-->
<!--            <div class="layui-btn layui-btn-sm layui-btn-warm btn-day" id="halfHour">近半小时</div>-->
<!--        </div>-->
    </form>
</div>


<div class="container" style="width:98% !important; padding:20px;border:none!important;">
    <div class="layui-field-box layui-collapse">
        <div class="layui-colla-item">
            <h2 class="layui-colla-title">产品调用统计</h2>

            <div class="layui-colla-content layui-show">
                <table class="layui-table">
                    <thead id="list_header"></thead>
                    <tbody id="list_body"></tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="layui-field-box layui-collapse">
        <div class="layui-colla-item">
            <h2 class="layui-colla-title">值分布统计</h2>
            <div class="layui-colla-content layui-show">
                <table class="layui-table">
                    <thead id="value_spread_header"></thead>
                    <tbody id="value_spread_body"></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
</html>

<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/dateFormat.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/jquery.dateFormat.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">

    //获取近十分钟的时间区间
    function getTenMinutes() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 600000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        return start_datetime + " - " + end_datetime;
    }
    //获取近二十分钟的时间区间
    function getTwentyMinutes() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 1200000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        return start_datetime + " - " + end_datetime;
    }
    //获取近半小时的时间区间
    function getHalfHour() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 1800000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        return start_datetime + " - " + end_datetime;
    }

    //加载table
    let loadTable = function (params) {
        let config        = [
            //加载产品调用统计数据
            {
                url     : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/product/getStatisInfo",
                params  : params,
                resolve : function (data) {
                    let json        = data.data;
                    //标题
                    let headerTable = `<tr>`;
                    Object.keys(json.header).forEach(function (field) {
                        headerTable += `<th>${json.header[field]}</th>`;
                    });
                    headerTable += `</tr>`;
                    $("#list_header").html(headerTable);

                    //内容
                    let bodyTable = ``;
                    let fields    = Object.keys(json.header);
                    json.items.forEach(function (item) {
                        bodyTable += `<tr>`;

                        fields.forEach(function (field) {
                            bodyTable += `<td align="center">${item[field]}</td>`;
                        });

                        bodyTable += `</tr>`;
                    });
                    $("#list_body").html(bodyTable);
                },
            },
            //加载值分布统计数据
            {
                url     : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/product/getValueSpreadInfo",
                params  : params,
                resolve : function (data) {
                    let product_id  = $("#product_id").val();
                    let json        = data.data;
                    //标题
                    let headerTable = `<tr>`;
                    Object.keys(json.header).forEach(function (field) {
                        headerTable += `<th>${json.header[field]}</th>`;
                    });
                    headerTable += `</tr>`;
                    $("#value_spread_header").html(headerTable);

                    //内容
                    let bodyTable = ``;
                    let fields    = Object.keys(json.header);
                    let numStr = "";
                    json.items.forEach(function (item) {
                        bodyTable += `<tr>`;

                        fields.forEach(function (field) {
                            numStr = item[field] === undefined ? "" : item[field];
                            bodyTable += `<td align="center">${numStr}</td>`;
                        });

                        bodyTable += `</tr>`;
                    });
                    $("#value_spread_body").html(bodyTable);
                }
            },
        ];

        Request.postAll(config);
    };


    (function () {
        layui.laydate.render({
            elem  : '#date_start',
            type  : 'datetime',
        });
        layui.laydate.render({
            elem  : '#date_end',
            type  : 'datetime',
        });

        //绑定快捷时间选择
        $("#tenMinutes").click(function () {
            $("#time_section").val(getTenMinutes());
            $("#query").trigger('click');
        });
        $("#twentyMinutes").click(function () {
            $("#time_section").val(getTwentyMinutes());
            $("#query").trigger('click');
        });
        $("#halfHour").click(function () {
            $("#time_section").val(getHalfHour());
            $("#query").trigger('click');
        });

        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/product", {father_id:210, is_with_id:1}).then(function (data) {
            $("#product_id").html(`<option value=""></option>${data.data}`);
            $("#product_id").val(281);
            layui.form.render('select');
        });
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/apikey", {father_id:210}).then(function (data) {
            $("#apikey").html(`<option value=""></option>${data.data}`);
            $("#apikey").val("889bede7cdd5775fabd05959a5891e92");
            layui.form.render('select');

            loadTable({
                apikey : $("#apikey").val(),
                product_id : $("#product_id").val(),
                stime : $("#date_start").val(),
                etime : $("#date_end").val(),
            });
        });

        //筛选事件
        layui.form.on('submit(list_form)', function (data) {
            let params = {
                apikey       : data.field.apikey,
                product_id       : data.field.product_id,
                stime : data.field.date_start,
                etime : data.field.date_end,
            };
            loadTable(params);
        });
    })();
</script>