<!DOCTYPE html>
<html lang="en">
    <head>
        <link rel="stylesheet" type="text/css" href="__JS__vue/index.css?v=2"/>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__vue/vue.js?v=2"></script>
        <script type="application/javascript" src="__JS__vue/index.js?v=2"></script>
        <script type="application/javascript" src="__JS__vue/axios.min.js?v=2"></script>

    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body">
                    <form action="{:U('billNotes')}" class="form-inline" method="get" id="list_form">

                        <div class="form-group" style="margin-right: 20px;">
                            <label class="control-label" >渠道名称：</label>
                            <el-select v-model="searchForm.channel_id" filterable clearable placeholder="请选择">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="form-group">
                            <el-button type="primary" @click="getTableData()">查询</el-button>
                        </div>

                        <div class="form-group">
                            <el-button type="success"   @click="dialogFormVisible = true">添加数据</el-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%">
                        <el-table-column
                                prop="channel_name"
                                label="渠道名称"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="start_time"
                                label="开始时间"
                                width="140">
                        </el-table-column>
                        <el-table-column
                                prop="end_time"
                                label="结束时间"
                                width="140">
                        </el-table-column>
                        <el-table-column
                                prop="keep_time"
                                label="持续时间(分)"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="total_num"
                                label="总条数"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="affect_num"
                                label="影响条数"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="ratio"
                                label="占比"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="reason"
                                label="事故原因"
                                width="220">
                        </el-table-column>
                        <el-table-column label="操作" width="170">
                            <template slot-scope="scope">
                                <el-button
                                        size="mini"
                                        @click="handleEdit(scope.$index, scope.row.id)">编辑</el-button>
                                <el-button
                                        size="mini"
                                        type="danger"
                                        @click="handleDelete(scope.$index, scope.row.id)">删除</el-button>
                            </template>
                        </el-table-column>

                    </el-table>
                </template>

                <el-dialog :title="dialog_title" :visible.sync="dialogFormVisible" @close="closeDialogCallBack('form')">
                    <el-form :model="form" :rules="rules" ref="form">

                        <el-form-item label="渠道名称" prop="channel_id" :label-width="formLabelWidth">
                            <el-select v-model="form.channel_id"  filterable clearable placeholder="请选择渠道">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="开始时间" prop="start_time" :label-width="formLabelWidth">
                            <el-date-picker
                                    v-model="form.start_time"
                                    type="datetime"
                                    placeholder="选择日期时间"
                                    align="right"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>

                        <el-form-item label="结束时间" prop="end_time" :label-width="formLabelWidth">
                            <el-date-picker
                                    v-model="form.end_time"
                                    type="datetime"
                                    placeholder="选择日期时间"
                                    align="right"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    format="yyyy-MM-dd HH:mm:ss"
                                    :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>

                        <!--
                        <el-form-item label="持续时间" prop="keep_time" :label-width="formLabelWidth">
                            <el-input v-model.number="form.keep_time" autocomplete="off"></el-input>
                        </el-form-item>
                        -->

                        <el-form-item label="总条数" prop="total_num" :label-width="formLabelWidth">
                            <el-input v-model.number="form.total_num" autocomplete="off"></el-input>
                        </el-form-item>

                        <el-form-item label="影响条数" prop="affect_num" :label-width="formLabelWidth">
                            <el-input v-model.number="form.affect_num" autocomplete="off"></el-input>
                        </el-form-item>
                        <!--
                        <el-form-item label="占比" prop="ratio" :label-width="formLabelWidth">
                            <el-input v-model="form.ratio" autocomplete="off"></el-input>
                        </el-form-item>
                        -->

                        <el-form-item label="事故原因" prop="reason" :label-width="formLabelWidth">
                            <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入内容"
                                    v-model="form.reason">
                            </el-input>
                        </el-form-item>

                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="dialogFormVisible = false">取 消</el-button>
                        <el-button type="primary" @click="onSubmit('form')">确 定</el-button>
                    </div>
                </el-dialog>

            </div>

            <div class="block" style="margin-bottom: 16px;">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40, 50]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalNum">
                </el-pagination>
            </div>

        </div>

    </div>

    <script type="application/javascript">

        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/getExceptionList";

        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                totalNum: 0,
                pageSize: 10,
                currentPage:1,
                options: [],
                pickerOptions: {},
                getUrl: url,
                dialogFormVisible: false,
                dialog_title:'添加数据',
                form: {
                    id:'',
                    channel_id: '',
                    start_time: '',
                    end_time: '',
                    //keep_time: '',
                    total_num: '',
                    affect_num: '',
                    //ratio: '',
                    reason: ''
                },
                formLabelWidth: '120px',
                searchForm: {
                    channel_id: '',
                },
                rules:{
                    channel_id: [
                        { required: true, message: '请选择渠道', trigger: 'change' }
                    ],
                    start_time: [
                        { type: 'string', required: true, message: '请选择开始时间', trigger: 'change' }
                    ],
                    end_time: [
                        { type: 'string', required: true, message: '请选择结束时间', trigger: 'change' }
                    ],
                    /*
                    keep_time: [
                        { required: true, message: '持续时间不能为空' },
                        { type: 'number', message: '持续时间必须为数字值'}
                    ],
                    */
                    total_num: [
                        { required: true, message: '总条数不能为空' },
                        { type: 'number', message: '总条数必须为数字值'}
                    ],
                    affect_num: [
                        { required: true, message: '影响条数不能为空' },
                        { type: 'number', message: '影响条数必须为数字值'}
                    ],
                    /*
                    ratio: [
                        { required: true, message: '占比不能为空' },
                        { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输入正确格式,可保留两位小数' }
                    ],
                    */
                    reason: [
                        { required: true, message: '请输入事故原因'}
                    ]
                }
            },
            created: function(){
                this.getTableData();
                this.getSelectData();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    var channel_id = this.searchForm.channel_id;
                    var where = {limit:this.pageSize, page:this.currentPage};
                    if(channel_id){
                        where.channel_id = channel_id;
                    }
                    axios.post(url, where).then(function (response) {
                        //console.log(response);
                        self.tableData = response.data.data.list;
                        self.totalNum = response.data.data.count;
                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                getSelectData:function(){
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelList";
                    axios.get(url, {}).then(function (response) {
                        //console.log(response.data.data);
                        response.data.data.forEach((item, index) => {
                            //console.log(item.channel_id + item.label);
                            self.options.push({value:item.channel_id,label:item.label});
                        });

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                onSubmit:function(formName){

                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            var request_params = this.form;
                            var self = this;
                            var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/saveChannelException";

                            axios.post(url, request_params).then(function (response) {
                                if(response.data.code == 0){
                                    successMsg(response.data.msg);

                                }else{
                                    errorMsg(response.data.msg);
                                }
                            }).catch(function (error) {
                                console.log(error);
                                errorMsg(error);
                            });

                            //alert('submit!');
                        } else {
                            console.log('no submit!!');
                            return false;
                        }
                    });



                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.getTableData();
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                    this.currentPage = val;
                    this.getTableData();
                },
                closeDialogCallBack:function(formName){
                    this.$refs[formName].resetFields();
                },
                handleEdit(index, id) {
                    var self = this;

                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/getChannelException";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.form.id = response.data.data.id;
                            self.form.channel_id = response.data.data.channel_id;
                            self.form.start_time = response.data.data.start_time;
                            self.form.end_time = response.data.data.end_time;
                            //self.form.keep_time = response.data.data.keep_time;
                            self.form.total_num = response.data.data.total_num;
                            self.form.affect_num = response.data.data.affect_num;
                            self.form.reason = response.data.data.reason;

                            self.dialog_title = '编辑数据';
                            self.dialogFormVisible = true;

                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                handleDelete(index, id) {
                    var self = this;

                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/delChannelException";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            vm.$refs['form'].resetFields();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }

    </script>
    </body>
</html>
