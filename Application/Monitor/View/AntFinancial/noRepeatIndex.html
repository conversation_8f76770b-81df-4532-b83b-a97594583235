<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>跑批统计（蚂蚁金服）</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        .layui-table th {
            text-align : center !important;
        }

        .layui-tab-title {
            user-select : none;
        }

        .layui-field-box {
            margin-bottom : 10px;
        }
        body .layui-layer-btn .layui-layer-btn1{
            border-color: #ff0000;
            background-color: #ff0000;
            color: #fff;
        }
        #confirm-button{
            background-color:  #f5ba42;
            color: #fff;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm4 layui-col-md4 layui-col-lg3">
            <label class="layui-form-label" for="config_id">选择批次</label>
            <div class="layui-input-block">
                <select name="config_id" id="config_id"></select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm3 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm3 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="confirm-button" onclick="confirm()">
                数据监控
            </div>
            <input type="hidden" id ="username" value="{$loginuser['username']}">
        </div>
    </form>

</div>


<div class="container" style="width:98% !important; padding:20px;border:none!important;">
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title"></ul>
        <div class="layui-tab-content">
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">产品调用统计
                        <span style="float: right;">上次更新时间：<span class="color_red" id="last_update_time" style="margin-right: 10px;"></span>脚本运行时间：<span class="color_red" id="run_time" style="margin-right: 10px;"></span></span>
                    </h2>

                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="list_header"></thead>
                            <tbody id="list_body"></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">值分布统计</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="value_spread_header"></thead>
                            <tbody id="value_spread_body"></tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    window.cache = {
        product_id : 281
    };
    (function () {
        let config = [
            //产品下拉选择框
            {
                url     : '{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/antFinancial/product',
                resolve : function (data) {
                    let tab_title = '';
                    Object.keys(data.data).forEach(function (product_id) {
                        if (window.cache.product_id == product_id) {
                            tab_title += `<li class="layui-this" data-product-id="${product_id}">${data.data[product_id]}</li>`;
                        } else {
                            tab_title += `<li data-product-id="${product_id}">${data.data[product_id]}</li>`;
                        }
                        $(".layui-tab-title").html(tab_title);
                    });

                    layui.element.on('tab', function (data) {
                        window.cache.product_id = $(this).attr('data-product-id');
                        reloadTable($("#config_id").val(), window.cache.product_id);
                    });
                }
            },
            //批次下拉选择框
            {
                url     : '{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/options/antFinancial/batch',
                resolve : function (data) {
                    $("#config_id").html(data.data);
                    layui.form.render('select');
                }
            }
        ];
        Request.getAll(config).then(function (data) {
            let config_id = $("#config_id").val();
            reloadTable(config_id, window.cache.product_id);
        });
    })();

    //筛选事件
    layui.form.on('submit(list_form)', function (data) {
        reloadTable(data.field.config_id, window.cache.product_id);
    });

    //加载数据、并展示在页面中
    let reloadTable = function (config_id, product_id) {
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/ant_financial/detail", {
            product_id,
            config_id
        }).then(function (json) {
            let data = json.data;
            $("#last_update_time").html(data.update_time);
            $("#run_time").html(data.run_time + 'S');

            //列表标题
            loadListHeader(data.result.header.list);

            //列表明细
            loadListBody(data.result.header.list, data.result.items);

            //值分布标题
            loadValueSpreadHeader(data.result.header.value_spread);

            loadValueSpreadBody(data.result.header.value_spread, data.result.items);
        });
    };

    let loadValueSpreadBody = function (header, items) {
        let value_spread_body = ``;
        let header_keys       = Object.keys(header);
        delete items[0];
        Object.keys(items).forEach(function (channel_id) {
            value_spread_body += `<tr>`;
            header_keys.forEach(function (field) {
                value_spread_body += `<td align="center">${items[channel_id][field]}</td>`;
            });
            value_spread_body += `</tr>`;
        });
        $("#value_spread_body").html(value_spread_body);
    };

    let loadValueSpreadHeader = function (header) {
        let value_spread_header = `<tr>`;
        Object.keys(header).forEach(function (field) {
            value_spread_header += `<th align="center">${header[field]}</th>`;
        });
        value_spread_header += `</tr>`;
        $("#value_spread_header").html(value_spread_header);
    };

    let loadListBody = function (header, items) {
        let list_body   = ``;
        let header_keys = Object.keys(header);
        Object.keys(items).forEach(function (channel_id) {
            list_body += `<tr>`;
            header_keys.forEach(function (field) {
                list_body += `<td align="center">${items[channel_id][field]}</td>`;
            });
            list_body += `</tr>`;
        });
        $("#list_body").html(list_body);
    };

    let loadListHeader = function (header) {
        let list_header = `<tr>`;
        Object.keys(header).forEach(function (field) {
            list_header += `<th align="center">${header[field]}</th>`;
        });
        list_header += `</tr>`;
        $("#list_header").html(list_header);
    }


    //渲染产品下拉选项框
    //    (function () {
    //        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/AntFinancialMonitor").then(function (data) {
    //            $("#total_280").html(data[280].total);
    //            $("#success_280").html(data[280].success);
    //            $("#rate_280").html(data[280].rate);
    //            $("#unique_total_280").html(data[280].unique_total);
    //            $("#unique_success_280").html(data[280].unique_success);
    //            $("#unique_success_rate_280").html(data[280].unique_rate);
    //            $("#unique_run_time_280").html(data[280].unique_run_time);
    //            $("#update_at_280").html(data[280].update_datetime);
    //
    //
    //            let tr280 = '';
    //            Object.keys(data[280].value_spread).forEach(function (value) {
    //                tr280 += `<tr><td colspan="2">${value}</td><td colspan="2">${data[280].value_spread[value].count}</td><td colspan="3">${data[280].value_spread[value].rate}</td></tr>`;
    //            });
    //            $("#value_spread_280").html(tr280);
    //
    //
    //            $("#total_281").html(data[281].total);
    //            $("#success_281").html(data[281].success);
    //            $("#rate_281").html(data[281].rate);
    //            $("#unique_total_281").html(data[281].unique_total);
    //            $("#unique_success_281").html(data[281].unique_success);
    //            $("#unique_success_rate_281").html(data[281].unique_rate);
    //            $("#unique_run_time_281").html(data[281].unique_run_time);
    //            $("#update_at_281").html(data[281].update_datetime);
    //
    //            let tr281 = '';
    //            Object.keys(data[281].value_spread).forEach(function (value) {
    //                tr281 += `<tr><td colspan="2">${value}</td><td colspan="2">${data[281].value_spread[value].count}</td><td colspan="3">${data[281].value_spread[value].rate}</td></tr>`;
    //            });
    //            $("#value_spread_281").html(tr281);
    //
    //        });
    //    })();

    function confirm(){
        var username = $('#username').val();
        layer.open({
            content: '数据监测'
            ,btn: [ '数据正常', '数据异常']
            ,btn1: function(index, layero){
                //按钮【按钮二】的回调
                var username = $('#username').val();
                $.ajax({
                    type : "GET",
                    contentType:"application/x-www-form-urlencoded",
                    url  : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/confirm?status=0&model=ant_total&username="+username,
                    success : function (data){
                       // console.log(data)
                        alert(data.message);
                        layer.closeAll();
                    }
                })
                //return false 开启该代码可禁止点击该按钮关闭
            }
            ,btn2: function(index, layero){
                //按钮【按钮三】的回调
                var username = $('#username').val();
                $.ajax({
                    type : "GET",
                    contentType:"application/x-www-form-urlencoded",
                    url  : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/confirm?status=1&model=ant_total&username="+username,
                    success : function (data){
                       // console.log(data)
                        alert(data.message);
                        layer.closeAll();
                    }
                })

                //return false 开启该代码可禁止点击该按钮关闭
            }
        });
    }
</script>