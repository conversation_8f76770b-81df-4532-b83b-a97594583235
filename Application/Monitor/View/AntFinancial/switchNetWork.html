<!DOCTYPE html>
<html lang="en">
    <head>
        <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__/vue/vue.js"></script>
        <script type="application/javascript" src="__JS__/vue/index.js"></script>
        <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <style>
        .el-dialog__body .el-form .el-input__inner{
            width: 256px;
        }
    </style>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body">

                    <el-form :inline="true" :model="searchForm"  label-width="100px" class="demo-form-inline">

                        <el-form-item label="账号或apikey" label-width="108px">
                            <el-input v-model="searchForm.apikey" placeholder="账号或apikey"></el-input>
                        </el-form-item>

                        <el-form-item label="批次编号" label-width="108px">
                            <el-input v-model="searchForm.code" placeholder="批次编号"></el-input>
                        </el-form-item>

                        <el-form-item label="开关状态">
                            <el-select v-model="searchForm.status" filterable clearable placeholder="请选择状态">
                                <el-option label="开" value="1"></el-option>
                                <el-option label="关" value="0"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" @click="searchTableData()">查询</el-button>
                        </el-form-item>

                    </el-form>

                </div>
            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%">

                        <el-table-column
                                prop="account_name"
                                label="账号名称"
                               >
                        </el-table-column>
                        <el-table-column
                                prop="pids"
                                label="产品id"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="code"
                                label="批次编号"
                                >
                        </el-table-column>

                        <el-table-column
                                prop="created_at"
                                label="创建时间"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="updated_at"
                                label="修改时间"
                                >
                        </el-table-column>
                        <el-table-column label="开关" >
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.status" :value="scope.row.status" :active-value="1" :inactive-value="0" @change="handleChangeStatus(scope.row.id, scope.row.status)"></el-switch>
                            </template>
                        </el-table-column>

                    </el-table>
                </template>

            </div>

            <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40, 50]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalNum">
                </el-pagination>
            </div>

        </div>

    </div>

    <script type="application/javascript">

        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/ant_financial/switchNetWork/list";

        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                totalNum: 0,
                pageSize: 10,
                currentPage:1,
                options: [],
                accountList:[],
                pickerOptions: {},
                getUrl: url,
                dialogFormVisible: false,
                dialog_title:'添加数据',
                operate:false,
                form: {
                    id:'',
                    code: '',
                    apikey: '',
                    status: ''
                },
                formLabelWidth: '120px',
                searchForm: {
                    status: '',
                    code: '',
                    apikey:''
                },
                rules:{
                }
            },
            created: function(){
                this.getTableData();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    var code = this.searchForm.code;
                    var apikey = this.searchForm.apikey;
                    var status = this.searchForm.status;
                    var where = {limit:this.pageSize, page:this.currentPage};
                    if(code){
                        where.code = code;
                    }
                    if(apikey){
                        where.apikey = apikey;
                    }
                    if(status !=''){
                        where.status = status;
                    }
                    axios.post(url, where).then(function (response) {
                        //console.log(response);
                        self.tableData = response.data.data.list;
                        self.totalNum = response.data.data.count;
                    }).catch(function (error) {
                        console.log(error);
                    });

                },

                onSubmit:function(formName){

                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            var request_params = this.form;
                            var self = this;
                            var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/saveChannelComplement";

                            axios.post(url, request_params).then(function (response) {
                                if(response.data.code == 0){
                                    self.$refs['form'].resetFields();
                                    successMsg(response.data.msg);

                                }else{
                                    errorMsg(response.data.msg);
                                }
                            }).catch(function (error) {
                                console.log(error);
                                errorMsg(error);
                            });

                        } else {
                            console.log('error submit!!');
                            return false;
                        }
                    });

                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.getTableData();
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                    this.currentPage = val;
                    this.getTableData();
                },
                searchTableData:function (){
                    this.currentPage = 1;
                    this.getTableData();
                },
                addTableData:function(){
                    this.dialog_title = '添加数据';
                    this.operate = false;
                    this.form.id = '';
                    this.form.channel_id = '';
                    this.form.apikey = '';
                    this.form.father_id = '';
                    this.form.timeout = '';
                    this.form.complement = '';

                    //this.$refs['form'].resetFields();
                    this.dialogFormVisible = true;

                },
                closeDialogCallBack:function(formName){
                    this.$refs[formName].resetFields();
                },
                handleEdit(index, id) {
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelComplement";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.form.id = response.data.data.id;
                            self.form.channel_id = response.data.data.channel_id;
                            self.form.apikey = response.data.data.apikey;
                            self.form.timeout = response.data.data.timeout;
                            self.form.complement = response.data.data.complement;
                            self.form.father_id = response.data.data.father_id;

                            self.dialog_title = '编辑数据';
                            self.operate = true;
                            self.dialogFormVisible = true;

                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                handleChangeStatus(id, status) {
                    var self = this;

                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/ant_financial/switchNetWork/set";
                    axios.get(url, {
                        params:{id:id,status:status}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            vm.$refs['form'].resetFields();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }

    </script>
    </body>
</html>
