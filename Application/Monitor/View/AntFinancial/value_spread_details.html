<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>值分布明细</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md5 layui-col-lg4">
            <label class="layui-form-label" for="time_section">时间区间</label>
            <div class="layui-input-block">
                <input type="text" value="{:date('Y-m-d H:i:s', strtotime('-2 days'))} - {:date('Y-m-d H:i:s')}" name="time_section" id="time_section" placeholder="请选择时间区间" autocomplete="off" class="layui-input">
            </div>
        </div>


        <div class="layui-form-item layui-col-xs12 layui-col-sm4 layui-col-md2 layui-col-lg2">
            <label class="layui-form-label" for="product_id">选择产品</label>
            <div class="layui-input-block">
                <select name="product_id" id="product_id"></select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm4 layui-col-md2 layui-col-lg2">
            <label class="layui-form-label" for="channel_id">选择渠道</label>
            <div class="layui-input-block">
                <select name="channel_id" id="channel_id"></select>
            </div>
        </div>

        <input type="hidden" id="apikey" value="736af14dafd22357c2b961fe75347eea" name="apikey">

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>
        <div class="layui-form-item layui-col-md1 layui-col-lg1"></div>
    </form>
</div>


<div class="container">
    <div id="list_table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/dateFormat.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/jquery.dateFormat.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    window.cache = {
        list_url         : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/product/valueSpreadHistory",
        list_order_field : "consume",
        list_order_type  : "desc",
        list_where       : {},
        list_title       : `蚂蚁跑批专用监控_产品明细_${Common.datetime('yyyy-MM-dd')}`,
        list_form        : false,
        list_fields      : [[]]
    };

    let loadTable = function (params) {
        //异步获取列
        window.cache.list_fields = [[]];
        Request.post('{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/product/valueSpreadHeadColumn', params).then(function (data) {
            Object.keys(data.data).forEach(function (field) {
                let name = data.data[field];
                if ('time_section' === field) {
                    window.cache.list_fields[0].push({
                        field : field,
                        title : name,
                        align : 'center',
                        width : 280,
                    });
                } else {
                    window.cache.list_fields[0].push({
                        field : field,
                        title : name,
                        align : 'center',
                    });
                }
            });

            window.cache.list_where = params;
            //加载表格
            Table.reloadTable({
                where          : window.cache.list_where,
                page           : true,
                limit          : 15,
                parseData      : function (response) {
                    return {
                        "code"  : response.status,
                        "msg"   : response.msg,
                        "count" : response.data.count,
                        "data"  : response.data.data
                    };
                },
                size           : 'default',
            });
        });
    };

    let loadQueryForm = function () {
        //日期时间范围选择
        layui.laydate.render({
            elem  : '#time_section',
            type  : 'datetime',
            range : true, //或 range: '~' 来自定义分割字符
            btns  : ['confirm']
        });

        let config = [
            //产品下拉选择框
            {
                url     : '{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/options/antFinancial/product',
                params  : {
                    "default" : Common.getRequestParam('product_id')
                },
                resolve : function (data) {
                    $("#product_id").html(data.data);
                    layui.form.render('select');
                }
            },
            //渠道下拉选择框
            {
                url     : '{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/options/antFinancial/channel',
                params  : {
                    "default" : Common.getRequestParam('channel_id')
                },
                resolve : function (data) {
                    $("#channel_id").html(data.data);
                    layui.form.render('select');
                }
            }
        ];

        Request.getAll(config).then(function (data) {
            loadTable({
                product_id   : $("#product_id").val(),
                channel_id   : $("#channel_id").val(),
                time_section : $("#time_section").val()
            });
        });

        //绑定form表单提交事件
        layui.form.on('submit(list_form)', function (data) {
            loadTable(data.field);
        });
    };

    (function () {
        loadQueryForm();
    })();
</script>