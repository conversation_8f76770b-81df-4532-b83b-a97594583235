<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>实时统计（蚂蚁金服）</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        .layui-table th {
            text-align : center !important;
        }

        .layui-tab-title {
            user-select : none;
        }

        .layui-field-box {
            margin-bottom : 10px;
        }

        body .layui-layer-btn .layui-layer-btn1{
            border-color: #ff0000;
            background-color: #ff0000;
            color: #fff;
        }
        #confirm-button{
            background-color: #f5ba42;
            color: #fff;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md5 layui-col-lg4">
            <label class="layui-form-label" for="time_section">时间区间</label>
            <div class="layui-input-block">
                <input type="text" value="{:date('Y-m-d H:i:s', strtotime('-10 minute'))} - {:date('Y-m-d H:i:s')}" name="time_section" id="time_section" placeholder="请选择时间区间" autocomplete="off" class="layui-input">
            </div>
        </div>

        <input type="hidden" id="apikey" value="736af14dafd22357c2b961fe75347eea" name="apikey">

        <div class="layui-form-item layui-col-xs4 layui-col-sm4 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>

        <div class="layui-form-item layui-col-xs8 layui-col-sm6 layui-col-md4 layui-col-lg3">
            <div class="layui-btn" id="tenMinutes">近十分钟</div>
            <div class="layui-btn" id="twentyMinutes">近二十分钟</div>
            <div class="layui-btn" id="halfHour">近半小时</div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm3 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="confirm-button" onclick="confirm()">
                数据监控
            </div>
            <input type="hidden" id ="username" value="{$loginuser['username']}">
        </div>
    </form>
</div>


<div class="container" style="width:98% !important; padding:20px;border:none!important;">
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title"></ul>
        <div class="layui-tab-content">
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">产品调用统计</h2>

                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="list_header"></thead>
                            <tbody id="list_body"></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">值分布统计</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="value_spread_header"></thead>
                            <tbody id="value_spread_body"></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">渠道统计（不区分产品）</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="channel_header"></thead>
                            <tbody id="channel_body"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/dateFormat.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/jquery.dateFormat.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    window.cache = {
        product_id : 281
    };
    //获取近十分钟的时间区间
    function getTenMinutes() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 600000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        return start_datetime + " - " + end_datetime;
    }
    //获取近二十分钟的时间区间
    function getTwentyMinutes() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 1200000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        return start_datetime + " - " + end_datetime;
    }
    //获取近半小时的时间区间
    function getHalfHour() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 1800000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        return start_datetime + " - " + end_datetime;
    }

    //加载table
    let loadTable = function (params) {
        params.product_id = window.cache.product_id;
        let config        = [
            //加载产品调用统计数据
            {
                url     : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/product/getSuccessInfoV2",
                params  : params,
                resolve : function (data) {
                    //当前选择的产品
                    let product_id  = $("#product_id").val();
                    let json        = data.data;
                    //标题
                    let headerTable = `<tr>`;
                    Object.keys(json.head).forEach(function (field) {
                        headerTable += `<th>${json.head[field]}</th>`;
                    });
                    headerTable += `</tr>`;
                    $("#list_header").html(headerTable);

                    //内容
                    let bodyTable = ``;
                    let fields    = Object.keys(json.head);
                    json.data.forEach(function (item) {
                        bodyTable += `<tr>`;

                        fields.forEach(function (field) {
                            if ('channel_name' === field) {
                                bodyTable += `<td align="center"><a onclick="window.open('./list_details.html?channel_id=${item.channel_id}&product_id=${window.cache.product_id}')" class="link">${item[field]}</a></td>`;
                            } else {
                                bodyTable += `<td align="center">${item[field]}</td>`;
                            }
                        });

                        bodyTable += `</tr>`;
                    });
                    $("#list_body").html(bodyTable);
                },
            },
            //加载值分布统计数据
            {
                url     : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/product/valueSpreadV2",
                params  : params,
                resolve : function (data) {
                    let product_id  = $("#product_id").val();
                    let json        = data.data;
                    //标题
                    let headerTable = `<tr>`;
                    Object.keys(json.head).forEach(function (field) {
                        headerTable += `<th>${json.head[field]}</th>`;
                    });
                    headerTable += `</tr>`;
                    $("#value_spread_header").html(headerTable);

                    //内容
                    let bodyTable = ``;
                    let fields    = Object.keys(json.head);
                    json.data.forEach(function (item) {
                        bodyTable += `<tr>`;

                        fields.forEach(function (field) {
                            if ('channel_name' === field) {
                                bodyTable += `<td align="center"><a onclick="window.open('./value_spread_details.html?channel_id=${item.channel_id}&product_id=${window.cache.product_id}')" class="link">${item[field]}</a></td>`;
                            } else {
                                bodyTable += `<td align="center">${item[field]}</td>`;
                            }
                        });

                        bodyTable += `</tr>`;
                    });
                    $("#value_spread_body").html(bodyTable);
                }
            },
            //加载渠道统计
            {
                url     : '{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/channel/getSuccessInfoV2',
                params  : params,
                resolve : function (data) {
                    let product_id  = $("#product_id").val();
                    let json        = data.data;
                    //标题
                    let headerTable = `<tr>`;
                    Object.keys(json.head).forEach(function (field) {
                        headerTable += `<th>${json.head[field]}</th>`;
                    });
                    headerTable += `</tr>`;
                    $("#channel_header").html(headerTable);

                    //内容
                    let bodyTable = ``;
                    let fields    = Object.keys(json.head);
                    json.data.forEach(function (item) {
                        bodyTable += `<tr>`;

                        fields.forEach(function (field) {
                            if ('channel_name' === field) {
                                bodyTable += `<td align="center"><a onclick="window.open('./channel_details.html?channel_id=${item.channel_id}')" class="link">${item[field]}</a></td>`;
                            } else {
                                bodyTable += `<td align="center">${item[field]}</td>`;
                            }
                        });

                        bodyTable += `</tr>`;
                    });
                    $("#channel_body").html(bodyTable);
                }
            }
        ];

        Request.postAll(config);
    };


    (function () {
        //日期时间范围选择
        layui.laydate.render({
            elem  : '#time_section',
            type  : 'datetime',
            range : true, //或 range: '~' 来自定义分割字符
            btns  : ['confirm']
        });

        //绑定快捷时间选择
        $("#tenMinutes").click(function () {
            $("#time_section").val(getTenMinutes());
            $("#query").trigger('click');
        });
        $("#twentyMinutes").click(function () {
            $("#time_section").val(getTwentyMinutes());
            $("#query").trigger('click');
        });
        $("#halfHour").click(function () {
            $("#time_section").val(getHalfHour());
            $("#query").trigger('click');
        });


        //产品下拉选择框
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/antFinancial/product", {"default" : 281}, false).then(function (data) {
            let tab_title = '';
            Object.keys(data.data).forEach(function (product_id) {
                if (window.cache.product_id == product_id) {
                    tab_title += `<li class="layui-this" data-product-id="${product_id}">${data.data[product_id]}</li>`;
                } else {
                    tab_title += `<li data-product-id="${product_id}">${data.data[product_id]}</li>`;
                }
                $(".layui-tab-title").html(tab_title);
            });

            layui.element.on('tab', function (data) {
                window.cache.product_id = $(this).attr('data-product-id');
                $("#tenMinutes").trigger('click');
            });

            loadTable({
                apikey       : $("#apikey").val(),
                time_section : $("#time_section").val()
            });
        });

        //筛选事件
        layui.form.on('submit(list_form)', function (data) {
            let params = {
                apikey       : data.field.apikey,
                time_section : data.field.time_section
            };
            loadTable(params);
        });
    })();


    function confirm(){
        var username = $('#username').val();
        layer.open({
            content: '数据监测'
            ,btn: [ '数据正常', '数据异常']
            ,btn1: function(index, layero){
                //按钮【按钮二】的回调
                var username = $('#username').val();
                $.ajax({
                    type : "GET",
                    contentType:"application/x-www-form-urlencoded",
                    url  : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/confirm?status=0&model=ant_in_time&username="+username,
                    success : function (data){
                       // console.log(data)
                        alert(data.message);
                        layer.closeAll();
                    }
                })
                //return false 开启该代码可禁止点击该按钮关闭
            }
            ,btn2: function(index, layero){
                //按钮【按钮三】的回调
                var username = $('#username').val();
                $.ajax({
                    type : "GET",
                    contentType:"application/x-www-form-urlencoded",
                    url  : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/confirm?status=1&model=ant_in_time&username="+username,
                    success : function (data){
                       // console.log(data)
                        alert(data.message);
                        layer.closeAll();
                    }
                })

                //return false 开启该代码可禁止点击该按钮关闭
            }
        });
    }

</script>