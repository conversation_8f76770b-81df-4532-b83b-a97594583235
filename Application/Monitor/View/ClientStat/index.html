<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客户统计</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <style>
        .layui-table th {
            text-align : center !important;
        }

        .layui-tab-title {
            user-select : none;
        }

        .layui-field-box {
            margin-bottom : 10px;
        }

        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }

        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="form-group">
                <label class="control-label">选择客户：</label>
                <select name="client_apikey" id="client_apikey">
                    <?php
                        foreach($client_info as $key=>$value){
                    ?>
                    <option value="<?php echo $value['apikey'] ?>"><?php echo $value['name'] ?></option>
                    <?php } ?>
                </select>
            </div>

            <div class="form-group">
                <label class="control-label">选择产品：</label>
                    <?php
                    $i = 1;
                    foreach($products_info as $key=>$value){
                    if($i == 1){
                    $checked = 'checked';
                    }else{
                    $checked = '';
                    }
                    ?>
                    <input type="radio" <?php echo $checked; ?> value="<?php echo $value['product_id'] ?>" name="product_id">&nbsp;<?php echo $value['name']?>&nbsp;

                    <?php
                     $i++;
                 }
                ?>
            </div>

            <div class="form-group">
                开始时间:&nbsp; <input type="text" class="datetimepicker" id="start_time" name="start_time" value="<?php echo date('Y-m-d 00:00:00') ?>">&nbsp;
                结束时间:&nbsp; <input type="text" class="datetimepicker" id="end_time" name="end_time" value="<?php echo date('Y-m-d 23:59:59') ?>">&nbsp;
            </div>

            <div class="form-group">
                <button type="button" id="search" class="btn btn-primary btn-sm">查询</button>
            </div>
        </div>
    </div>
</div>
<div class="container" style="width:98% !important; padding:20px;border:none!important;">
    <div class="layui-tab layui-tab-brief">
        <div class="layui-tab-content">
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">产品调用统计</h2>

                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="list_header">
                            <?php foreach($statis_header as $key=>$value){ ?>
                                <th align="center"><?php echo $value ?> </th>
                            <?php } ?>
                            </thead>
                            <tbody id="list_body">
                            <?php
                            foreach($statis_arr as $key=>$value){
                            ?>
                                <tr>
                                    <?php
                                        foreach($value as $k=>$val){
                                    ?>
                                    <td align="center"><?php echo $val; ?></td>
                                    <?php
                                        }
                                    ?>
                                </tr>
                            <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-field-box layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">值分布统计</h2>
                    <div class="layui-colla-content layui-show">
                        <table class="layui-table">
                            <thead id="value_spread_header">
                            <?php foreach($spread_header as $key=>$value){ ?>
                            <th align="center"><?php echo $value ?> </th>
                            <?php } ?>
                            </thead>
                            </thead>
                            <tbody id="value_spread_body">
                            <?php
                            foreach($spread_arr as $key=>$value){
                            ?>
                            <tr>
                                <?php
                                     foreach($value as $k=>$val){
                                ?>
                                <td align="center"><?php echo $val; ?></td>
                                <?php
                                        }
                                    ?>
                            </tr>
                            <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script src="__JS__bootstrap-datetimepicker.min.js"></script>
<script src="__JS__bootstrap-datetimepicker.zh-CN.js"></script>
<link rel="stylesheet" href="__CSS__bootstrap-datetimepicker.min.css">
<script src="__JS__select2.full.min.js"></script>

<script type="application/javascript">
   $('.datetimepicker').datetimepicker({
        language: 'zh-CN',//显示中文
        format: 'yyyy-mm-dd hh:ii:ss',//显示格式
        minView: "hour",//设置只显示到月份
        initialDate: new Date(),//初始化当前日期
        autoclose: true,//选中自动关闭
        todayBtn: true//显示今日按钮
    });


   //选择所属客户
   $("#client_apikey").select2({
       allowClear: true,
       theme: "bootstrap",
       placeholder: '选择渠道',
       width : '28%'
   });

    $(function(){
        var father_id = getQueryVariable("father_id");
        if(father_id !== false){
            var start_time = $('#start_time').val();
            var end_time = $('#end_time').val();
            if (start_time > end_time){
                alert('开始时间不能大于结束时间');
                return false;
            }
            var apikey = $('#client_apikey').val();
            if(apikey == null){
                return false;
            }
            var product_id = $("input[name='product_id']:checked").val();

            if(apikey == ''){
                alert('请选择客户名称');
                return false;
            }
            $("#loading").show();
            $.ajax({
                type: 'post',
                url: "{:U('/Monitor/ClientStat/index')}",
                data: {
                    'flag':'list',
                    'apikey': apikey,
                    'product_id': product_id,
                    'start_time': start_time,
                    'end_time': end_time
                },
                success: function(data) {
                    $("#loading").hide();
                    if(data.status == 'ok'){
                        $('#value_spread_header').html(data.spread_header_th);
                        $('#value_spread_body').html(data.spread_body_tr);
                        $('#list_header').html(data.statis_header_th);
                        $('#list_body').html(data.statis_body_tr);
                    }else{
                        alert(data.msg);
                    }
                }
            });
        }

        function getQueryVariable(variable)
        {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i=0;i<vars.length;i++) {
                var pair = vars[i].split("=");
                if(pair[0] == variable){return pair[1];}
            }
            return(false);
        }
    });

    $('#search').on('click', function(){

        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();
        if (start_time > end_time){
            alert('开始时间不能大于结束时间');
            return false;
        }

        if (start_time > end_time){
            alert('开始时间不能大于结束时间');
            return false;
        }
        var apikey = $('#client_apikey').val();
        if(apikey == null){
            return false;
        }
        var product_id = $("input[name='product_id']:checked").val();
        if(apikey == ''){
            alert('请选择客户名称');
            return false;
        }

        $("#loading").show();
        $.ajax({
         type: 'post',
         url: "{:U('/Monitor/ClientStat/index')}",
         data: {
             'flag':'list',
             'apikey': apikey,
             'product_id': product_id,
             'start_time': start_time,
             'end_time': end_time
         },
         success: function(data) {
             $("#loading").hide();
             if(data.status == 'ok'){
                 $('#value_spread_header').html(data.spread_header_th);
                 $('#value_spread_body').html(data.spread_body_tr);
                 $('#list_header').html(data.statis_header_th);
                 $('#list_body').html(data.statis_body_tr);
             }else{
                alert(data.msg);
             }
         }
         });
    });

    $("input[name='product_id']").on('click', function(){
        var start_time = $('#start_time').val();
        var end_time = $('#end_time').val();
        if (start_time > end_time){
            alert('开始时间不能大于结束时间');
            return false;
        }
        if (start_time > end_time){
            alert('开始时间不能大于结束时间');
            return false;
        }

        var apikey = $('#client_apikey').val();
        if(apikey == null){
            return false;
        }

        var product_id = $("input[name='product_id']:checked").val();
        if(apikey == ''){
            alert('请选择客户名称');
            return false;
        }
        $("#loading").show();
        $.ajax({
            type: 'post',
            url: "{:U('/Monitor/ClientStat/index')}",
            data: {
                'flag':'list',
                'apikey': apikey,
                'product_id': product_id,
                'start_time': start_time,
                'end_time': end_time
            },
            success: function(data) {
                $("#loading").hide();
                if(data.status == 'ok'){
                    $('#value_spread_header').html(data.spread_header_th);
                    $('#value_spread_body').html(data.spread_body_tr);
                    $('#list_header').html(data.statis_header_th);
                    $('#list_body').html(data.statis_body_tr);
                }else{
                    alert(data.msg);
                }
            }
        });
    });


   $('#client_apikey').on('change', function(){
       var start_time = $('#start_time').val();
       var end_time = $('#end_time').val();
       if (start_time > end_time){
           alert('开始时间不能大于结束时间');
           return false;
       }
       var apikey = $('#client_apikey').val();
       if(apikey == null){
           return false;
       }
       var product_id = $("input[name='product_id']:checked").val();

       if(apikey == ''){
           alert('请选择客户名称');
           return false;
       }
       $("#loading").show();
       $.ajax({
           type: 'post',
           url: "{:U('/Monitor/ClientStat/index')}",
           data: {
               'flag':'list',
               'apikey': apikey,
               'product_id': product_id,
               'start_time': start_time,
               'end_time': end_time
           },
           success: function(data) {
               $("#loading").hide();
               if(data.status == 'ok'){
                   $('#value_spread_header').html(data.spread_header_th);
                   $('#value_spread_body').html(data.spread_body_tr);
                   $('#list_header').html(data.statis_header_th);
                   $('#list_body').html(data.statis_body_tr);
               }else{
                   alert(data.msg);
               }
           }
       });
   });

  //单选框点击事件
  /*layui.use(['form'], function () {
      var upload = layui.upload;
      var form = layui.form;    //此处即为 radio 的监听事件
     /!* form.on('select(client_apikey)', function(data){

      });*!/
      form.on('radio(product_id)', function(data){
          var start_time = $('#start_time').val();
          var end_time = $('#end_time').val();
          if (start_time > end_time){
              alert('开始时间不能大于结束时间');
              return false;
          }

          if (start_time > end_time){
              alert('开始时间不能大于结束时间');
              return false;
          }
          var apikey = $('#client_apikey').val();
          var product_id = $("input[name='product_id']:checked").val();
          if(apikey == ''){
              alert('请选择客户名称');
              return false;
          }
          $("#loading").show();
          $.ajax({
              type: 'post',
              url: "{:U('/Monitor/ClientStat/index')}",
              data: {
                  'flag':'list',
                  'apikey': apikey,
                  'product_id': product_id,
                  'start_time': start_time,
                  'end_time': end_time
              },
              success: function(data) {
                  $("#loading").hide();
                  if(data.status == 'ok'){
                      $('#value_spread_header').html(data.spread_header_th);
                      $('#value_spread_body').html(data.spread_body_tr);
                      $('#list_header').html(data.statis_header_th);
                      $('#list_body').html(data.statis_body_tr);
                  }else{
                      alert(data.msg);
                  }
              }
          });
        });
  });
*/


   /*layui.use(['form'], function () {
       var upload = layui.upload;
       var form = layui.form;    //此处即为 radio 的监听事件
        form.on('select(client_apikey)', function(data){

        });
       form.on('select(client_apikey)', function(data){

       });
   });*/




</script>