<!DOCTYPE html>
<html lang="en">
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__xm-select.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        /*表格条纹*/
        .strip_1 {
            background-color: #f2f2f2;
        }
        .layui-table-tool-temp{
            Display:none;
        }
        .layui-table-tool{
            display: none;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container">
    <form class="layui-form layui-row list_form">
        <div style="">

            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width:240px">
                <label class="layui-form-label">产品</label>
                <div class="layui-input-block">
                    <select name="product_id" lay-search id="product_id" lay-filter="product_id">
                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width:260px">
                <label class="layui-form-label">客户</label>
                <div class="layui-input-block">
                    <select name="client_id" lay-search id="client_id" lay-filter="client_id">
                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width:260px">
                <label class="layui-form-label">渠道</label>
                <div class="layui-input-block">
                    <select name="channel" lay-search id="channel">
                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:240px">
                <label class="layui-form-label">开始时间</label>
                <div class="layui-input-block">
                    <input type="text" name="date_start" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="date_start" value="{:date('Y-m-d H:i:s', strtotime('-2 hour'))}" >
                </div>
            </div>
            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:240px">
                <label class="layui-form-label">结束时间</label>
                <div class="layui-input-block">
                    <input type="text" name="date_end" placeholder="请选择结束时间" autocomplete="off" class="layui-input" id="date_end" value="{:date('Y-m-d H:i:s', time())}" >
                </div>
            </div>

            <div class="layui-form-item layui-col-xs8 layui-col-sm6 layui-col-md4 layui-col-lg3" style="margin-left: 33px;">
                <div class="layui-btn" id="tenMinutes">近十分钟</div>
                <div class="layui-btn" id="twentyMinutes">近二十分钟</div>
                <div class="layui-btn" id="halfHour">近半小时</div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="margin-left:5px;width:70px;">
                <div class="layui-btn  layui-btn-radius" id="query">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit"><i class="layui-icon">&#xe615;</i> 查询</button>
                </div>
            </div>
        </div>


    </form>
</div>

<div class="container">
    <div id="table_and_page_div_id">
        <div class="panel panel-default table-responsive" id="tableform">

        </div>
    </div>
</div>

<!--
<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue" lay-filter="table">
            <thead style="background:#009688; color:white">
            <tr>
                <th style="width:6%;">客户 </th>
                <th style="width:6%;">子产品 </th>
                <th style="width:6%;">状态 </th>
                <th style="width:6%;">返回值 </th>
                <th style="width:6%;">响应时间 </th>
                <th style="width:6%;">调用时间 </th>
            </tr>
            </thead>

            <tbody id="table_data">

                <?php echo $detail_str; ?>
            </tbody>

        </table>
    </div>
</div>
-->
<!--<script src="__JS__opdata.js"></script>-->
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__bignumber.js-master/bignumber.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript" src="__JS__eleFixed.min.js"></script>
<script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/dateFormat.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/jquery.dateFormat.js"></script>

<script type="application/javascript">
    layui.laydate.render({
        elem  : '#date_start',
        type  : 'datetime',
    });
    layui.laydate.render({
        elem  : '#date_end',
        type  : 'datetime',
    });

    //单选框点击事件

    layui.use(['form'], function () {

        var form = layui.form;    //此处即为 radio 的监听事件
        form.on('select(product_id)', function(data){
            //$('#table_data').empty();
            var product_id = data.value;
            var client_id = $('#client_id').val();
            if(client_id == null){
                client_id = '';
            }
            getClient(product_id);
            //getWhere(product_id, client_id);
        });

        /*
        form.on('select(client_id)', function(data){
            var client_id = data.value;
            var product_id = $('#product_id').val();
            //$('#table_data').empty();
            //getWhere(product_id, client_id);

        });
         */

    });


    (function () {
        Request.get("/Monitor/ClientStat/productInfo.html?flag=option", []).then(function (data) {
            $("#product_id").html(`<option value=""></option>${data.data}`);
            layui.form.render('select');
        });
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap", {channel:true}).then(function (data) {
            //window.cache.channel = data.data.channel;
            let  options = `<option value=""></option>`;
            $.each(data.data.channel, function(key,val) {
                options += `<option value="` + key + `">` + val + `</option>`;
            });
            $("#channel").html(options);
            layui.form.render('select');
        });
    })();

    //表单提交
    var form = layui.form;
    form.on('submit(formSubmit)', function (data) {

        if (data.field.date_start != '' && data.field.date_end != ''){
            var start_time = getUnixTime(data.field.date_start);
            var end_time = getUnixTime(data.field.date_end);
            if(start_time >= end_time){
                alert('开始时间不能大于等于结束时间，请重新选择');
                return false;
            }
            if ((end_time-start_time)/86400 >2){
                alert('请选择时间间隔小于两日');
                return false;
            }

        }

        if (data.field.date_start != '' && data.field.date_end == ''){
            var start_time = getUnixTime(data.field.date_start);
            var timestamp = Date.parse(new Date());
            timestamp = timestamp.toString().substr(0, 10);

            if ((timestamp-start_time)/86400 >2){
                alert('请选择近两日内数据');
                return false;
            }
        }

        if (data.field.date_start == '' && data.field.date_end != ''){
            alert('请选择开始时间');
            return  false;
        }

        if (data.field.date_start == '' && data.field.date_end == ''){
            alert('请选择时间段');
            return  false;
        }

        var product_id = $('#product_id').val();
        var client_id = $('#client_id').val();
        var channel_id = $('#channel').val();
        var string ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getProductRecord?date_start="+data.field.date_start+'&date_end='+data.field.date_end;
        if(product_id){
            string += '&product_id='+product_id;
        }
        if(client_id){
            string += '&apikey='+client_id;
        }
        if(channel_id){
            string += '&channel_id='+channel_id;
        }
        getData(string);
        return false;
    });


    //绑定快捷时间选择
    $("#tenMinutes").click(function () {
        let both_date = getTenMinutes();
        $("#date_start").val(both_date.date_start);
        $("#date_end").val(both_date.date_end);
        getWhere();
    });
    $("#twentyMinutes").click(function () {
        let both_date = getTwentyMinutes();
        $("#date_start").val(both_date.date_start);
        $("#date_end").val(both_date.date_end);
        getWhere();
    });
    $("#halfHour").click(function () {
        let both_date = getHalfHour();
        $("#date_start").val(both_date.date_start);
        $("#date_end").val(both_date.date_end);
        getWhere();
    });

    /**
     * 获取主数据
     */
    /*
    function getData(product_id, client_id){
        Request.get("/Monitor/ClientStat/productInfo.html?flag=detail&product_id="+product_id+"&client_id="+client_id).then(function (data) {
            $('#table_data').html(data.data);
        });
    }
    */
    // 获取渠道信息
    (function () {


        var start_time = $("#date_start").val();
        var end_time = $("#date_end").val();

        var string ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getProductRecord?date_start="+start_time+'&date_end='+end_time;
        getData(string);

        //getData("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getAllRecord");
    })();

    function getWhere(product_id=0, apikey=''){
        var product_id = $('#product_id').val();
        var client_id = $('#client_id').val();
        var channel_id = $('#channel').val();
        var start_time = $("#date_start").val();
        var end_time = $("#date_end").val();
        var url ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getProductRecord?date_start="
            +start_time+'&date_end='+end_time;
        if(product_id){
            url += '&product_id='+product_id;
        }
        if(client_id){
            url += '&apikey='+client_id;
        }
        if(channel_id){
            url += '&channel_id='+channel_id;
        }
        getData(url);

    }

    //获取数据并展示
    function getData(url){

        layui.use(['table'],function () {
            var table = layui.table;
            table.render({
                elem: '#tableform'
                ,height: 420
                ,url: url  //数据接口
                ,page: true //开启分页
                ,toolbar: 'default' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
                ,cols: [[ //表头
                    {field: 'account_name', title: '客户', fixed: 'left'}
                    ,{field: 'product_name', title: '子产品'}
                    ,{field: 'channel_name', title: '渠道'}
                    ,{field: 'status_text', title: '状态'}
                    ,{field: 'value', title: '返回值'}
                    ,{field: 'run_time', title: '响应时间'}
                    ,{field: 'time', title: '时间'}
                ]]
                ,limit: 20
                ,limits:[20]
                ,defaultToolbar: false
                ,autoSort: false
            });

            AutoTableHeight();

        });

    }

    function AutoTableHeight()
    {
        var dev_obj = document.getElementById('table_and_page_div_id'); //table的父div

        var layuitable_main = dev_obj.getElementsByClassName("layui-table-main"); //在父div中找 layui-table-main 属性所在标签
        if (layuitable_main != null && layuitable_main.length > 0) {
            layuitable_main[0].style.height = '100%';
        }

        var layuitable = dev_obj.getElementsByClassName("layui-form"); //在父div中找 layui-form 属性所在标签
        if (layuitable != null && layuitable.length > 0) {
            layuitable[0].style.height = '100%';
        }
    }


    function getClient(product_id){
        $.ajax({
            type:'get',
            url : "/Monitor/ClientStat/productInfo.html?flag=client&product_id="+product_id,
            dataType: "json",
            success:function(data){
                $('#client_id').html(data.data);
                layui.form.render('select');
            }
        });
        /*
        Request.get("/Monitor/ClientStat/productInfo.html?flag=client&product_id="+product_id).function (data) {
            $('#client_id').html(data.data);
            layui.form.render('select');
        };
        */
    }

    //获取近十分钟的时间区间
    function getTenMinutes() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 600000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        //return start_datetime + " - " + end_datetime;
        return {'date_start':start_datetime, 'date_end':end_datetime};
    }
    //获取近二十分钟的时间区间
    function getTwentyMinutes() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 1200000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        //return start_datetime + " - " + end_datetime;
        return {'date_start':start_datetime, 'date_end':end_datetime};

    }
    //获取近半小时的时间区间
    function getHalfHour() {
        let date         = new Date();
        let end_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        date.setTime(date.getTime() - 1800000);
        let start_datetime = $.format.date(date, 'yyyy-MM-dd HH:mm:ss');
        //return start_datetime + " - " + end_datetime;
        return {'date_start':start_datetime, 'date_end':end_datetime};

    }

    function getUnixTime(dateStr){
        var newstr = dateStr.replace(/-/g,'/');
        var date =  new Date(newstr);
        var time_str = date.getTime().toString();
        return time_str.substr(0, 10);
    }

</script>
</body>
</html>
