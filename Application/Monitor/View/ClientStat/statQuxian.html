<!DOCTYPE html>
<html lang="en">
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__xm-select.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        /*表格条纹*/
        .strip_1 {
            background-color: #f2f2f2;
        }
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            text-align: center;
        }

        td{
            min-width: 150px;
            max-width: 200px;
            text-align: center;
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container">
    <form class="layui-form layui-row list_form">
        <div style="display: flex">
            <div class="layui-form-item layui-col-xs12 layui-col-sm7 layui-col-md3 layui-col-lg2" style="width:200px">
                <label class="layui-form-label">开始时间</label>
                <div class="layui-input-block">
                    <input type="text" name="date_start" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="date_start" value="{:date('Y-m-d', strtotime('-180 days'))}">
                </div>
            </div>
            <div class="layui-form-item layui-col-xs12 layui-col-sm7 layui-col-md3 layui-col-lg2" style="width:200px">
                <label class="layui-form-label">结束时间</label>
                <div class="layui-input-block">
                    <input type="text" name="date_end" placeholder="请选择结束时间" autocomplete="off" class="layui-input" id="date_end" value="{:date('Y-m-d')}">
                </div>
            </div>
            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width: 300px;">
                <label class="layui-form-label" style="width: 100px;">选择渠道</label>
                <div class="layui-input-block father_id_div">
                    <select name="chnl_id" lay-search id="chnl_id" data-id="1">
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="margin-left:5px;width:130px;">
                <div class="layui-btn  layui-btn-radius" id="query">
                    <i class="layui-icon">&#xe615;</i> 查询
                </div>
            </div>
        </div>

    </form>
</div>
<div class="layui-tab" style="margin-left: 25px;">
    <ul class="layui-tab-title">
        <li class="layui-this">数据表</li>
        <li>折线图</li>
    </ul>
    <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
            <div class="panel panel-default table-responsive">
                <table class="table-bordered" lay-filter="table">
                    <thead style="background:#009688; color:white">
                    <tr>
                        <th class="layui-table-cell"><span >日期</span></th>
                        <th class="layui-table-cell"><span >渠道名称</span></th>
                        <th class="layui-table-cell"><span >请求量</span></th>
                        <th class="layui-table-cell"><span >uid个数</span></th>
                        <th class="layui-table-cell"><span >号码个数</span></th>
                        <th class="layui-table-cell"><span >手机号个数</span></th>
                        <th class="layui-table-cell"><span >热线号个数</span></th>
                        <th class="layui-table-cell"><span >固话号个数</span></th>
                        <th class="layui-table-cell"><span >uid密度</span></th>
                        <th class="layui-table-cell"><span >号码密度</span></th>
                        <th class="layui-table-cell"><span >手机号占比</span></th>
                        <th class="layui-table-cell"><span >热线号占比</span></th>
                        <th class="layui-table-cell"><span >固话号占比</span></th>
                        <th class="layui-table-cell"><span >频数ge100万的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数ge10万的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数ge1万的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数ge1000的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数ge100的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数ge100的uid占比</span></th>
                        <th class="layui-table-cell"><span >频数ge50的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数ge50的uid占比</span></th>
                        <th class="layui-table-cell"><span >频数等于1的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数等于1的uid占比</span></th>
                        <th class="layui-table-cell"><span >频数le2的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数le2的uid占比</span></th>
                        <th class="layui-table-cell"><span >频数le5的uid个数</span></th>
                        <th class="layui-table-cell"><span >频数le5的uid占比</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2的uid个数</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2的uid占比</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2且近30d天数ge2的uid个数</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2且近30d天数ge2的uid占比</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2且近30d天数ge5的uid个数</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2且近30d天数ge5的uid占比</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2且近30d天数ge10的uid个数</span></th>
                        <th class="layui-table-cell"><span >号码个数ge2且近30d天数ge10的uid占比</span></th>
                    </tr>
                    </thead>
                    <tbody id="table_data"></tbody>
                </table>
            </div>
        </div>
        <div class="layui-tab-item">
            <table class="table table-hover table-bordered" lay-filter="table">
                <tbody>
                <table class="table table-hover table-bordered" lay-filter="table">
                    <tbody>
                    <tr>
                        <td><div id="main" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_num" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_num_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="tel_num" style="width: 500px;height:500px;"></div></td>
                        <td><div id="tel_num_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="tel1_num" style="width: 500px;height:500px;"></div></td>
                        <td><div id="tel1_num_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="tel2_num" style="width: 500px;height:500px;"></div></td>
                        <td><div id="tel2_num_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="tel3_num" style="width: 500px;height:500px;"></div></td>
                        <td><div id="tel3_num_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_numge_100w" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_numge_10w" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_numge_1w" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_numge_1000" style="width: 500px;height:500px;"></div></td>
                    </tr>

                    <tr>
                        <td><div id="uid_numge_100" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_numge_100_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_numge_50" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_numge_50_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_nume_1" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_nume_1_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_numle_2" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_numle_2_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="uid_numle_5" style="width: 500px;height:500px;"></div></td>
                        <td><div id="uid_numle_5_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="ngt_1_uid_num" style="width: 500px;height:500px;"></div></td>
                        <td><div id="ngt_1_uid_num_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="ngt_1_uid_numgt1d" style="width: 500px;height:500px;"></div></td>
                        <td><div id="ngt_1_uid_numgt1d_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="ngt_1_uid_numgt5d" style="width: 500px;height:500px;"></div></td>
                        <td><div id="ngt_1_uid_numgt5d_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>
                    <tr>
                        <td><div id="ngt_1_uid_numgt10d" style="width: 500px;height:500px;"></div></td>
                        <td><div id="ngt_1_uid_numgt10d_pct" style="width: 500px;height:500px;"></div></td>
                    </tr>

                    </tbody>
                </table>

                </tbody>
            </table>

        </div>
    </div>
</div>
<!--<script src="__JS__opdata.js"></script>-->
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__bignumber.js-master/bignumber.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript" src="__JS__eleFixed.min.js"></script>
<script type="application/javascript" src="__JS__echarts.min.js"></script>
<script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
<script type="application/javascript">
    (function () {
        Request.get("/Monitor/ClientStat/statQuxian.html?flag=qudao", {}).then(function (data) {
            $("#chnl_id").html(`${data.data}`);
            layui.form.render('select');
        });
    })();


    /***************************zj********************************/
    let cnt_chat = echarts.init(document.getElementById('main'));
    let uid_num_chat = echarts.init(document.getElementById('uid_num'));
    let uid_num_pct_chat = echarts.init(document.getElementById('uid_num_pct'));
    let tel_num_chat = echarts.init(document.getElementById('tel_num'));
    let tel_num_pct_chat = echarts.init(document.getElementById('tel_num_pct'));
    let tel1_num_chat = echarts.init(document.getElementById('tel1_num'));
    let tel1_num_pct_chat = echarts.init(document.getElementById('tel1_num_pct'));
    let tel2_num_chat = echarts.init(document.getElementById('tel2_num'));
    let tel2_num_pct_chat = echarts.init(document.getElementById('tel2_num_pct'));
    let tel3_num_chat = echarts.init(document.getElementById('tel3_num'));
    let tel3_num_pct_chat = echarts.init(document.getElementById('tel3_num_pct'));
    let uid_numge_100w_chat = echarts.init(document.getElementById('uid_numge_100w'));
    let uid_numge_10w_chat = echarts.init(document.getElementById('uid_numge_10w'));

    let uid_numge_1w_chat = echarts.init(document.getElementById('uid_numge_1w'));
    let uid_numge_1000_chat = echarts.init(document.getElementById('uid_numge_1000'));
    let uid_numge_100_chat = echarts.init(document.getElementById('uid_numge_100'));
    let uid_numge_100_pct_chat = echarts.init(document.getElementById('uid_numge_100_pct'));

    let uid_numge_50_chat = echarts.init(document.getElementById('uid_numge_50'));
    let uid_numge_50_pct_chat = echarts.init(document.getElementById('uid_numge_50_pct'));

    let uid_nume_1_chat = echarts.init(document.getElementById('uid_nume_1'));
    let uid_nume_1_pct_chat = echarts.init(document.getElementById('uid_nume_1_pct'));

    let uid_numle_2_chat = echarts.init(document.getElementById('uid_numle_2'));
    let uid_numle_2_pct_chat = echarts.init(document.getElementById('uid_numle_2_pct'));

    let uid_numle_5_chat = echarts.init(document.getElementById('uid_numle_5'));
    let uid_numle_5_pct_chat = echarts.init(document.getElementById('uid_numle_5_pct'));

    let ngt_1_uid_num_chat = echarts.init(document.getElementById('ngt_1_uid_num'));
    letngt_1_uid_num_pct_chat = echarts.init(document.getElementById('ngt_1_uid_num_pct'));

    let ngt_1_uid_numgt1d_chat = echarts.init(document.getElementById('ngt_1_uid_numgt1d'));
    let ngt_1_uid_numgt1d_pct_chat = echarts.init(document.getElementById('ngt_1_uid_numgt1d_pct'));

    let ngt_1_uid_numgt5d_chat = echarts.init(document.getElementById('ngt_1_uid_numgt5d'));
    let ngt_1_uid_numgt5d_pct_chat = echarts.init(document.getElementById('ngt_1_uid_numgt5d_pct'));

    let ngt_1_uid_numgt10d_chat = echarts.init(document.getElementById('ngt_1_uid_numgt10d'));
    let ngt_1_uid_numgt10d_pct_chat = echarts.init(document.getElementById('ngt_1_uid_numgt10d_pct'));
    /**********************************************************/


    window.cache = {data:{},customer:{}};
    if (getRequestParam('date_start') && getRequestParam('date_end')){
        let date1 = new Date(getRequestParam('date_start'));
        let date2 = new Date(getRequestParam('date_end'));
        let date3=date2.getTime()-date1.getTime();
        if (Math.floor(date3/(24*3600*1000)) >= 3){
            $("#date_start").val(getRequestParam('date_start'));
            $("#date_end").val(getRequestParam('date_end'));
        }
    }
    layui.laydate.render({
        elem  : '#date_start',
        type  : 'date',
    });
    layui.laydate.render({
        elem  : '#date_end',
        type  : 'date',
    });
    let table = layui.table;
    let strip = 0;
    let yAxis_type = '';
    let yAxis_min = 0;

    /**
     * 固定表头
     */
    eleFixed.push({
        target: document.getElementsByTagName('thead')[0], // it must be a HTMLElement
        offsetTop: $("thead").offset().top // height from window offsetTop
    });

    $(document).on("click", ".btn-page", function() {
        window.location= $(this).data("url");
    });
    /**
     * 搜索按钮
     */
    $("#query").click(function () {getData();});

    layui.use(['form'], function() {
        //监听指定开关
        layui.form.on('switch(test_data)', function (data) {
            $("#test_data").val(this.checked ? 1 : 0);
            getData();
        });
    });

    layui.use(['form'], function () {
        let form = layui.form;
        form.on('radio(day_type)', function(){display();});
    });
    /**
     * 排序
     */
    let field_sort = {'field':'date', 'sort':0};
    $(".sort_btn").click(function () {
        if ($(this).data("sort") === "down"){
            $(this).data("sort", "up");
            field_sort = {'field':$(this).data("field"), 'sort':1};
        } else {
            $(this).data("sort", "down");
            field_sort = {'field':$(this).data("field"), 'sort':0};
        }
        display();
    });
    /*ngt_1_uid_numgt10d_chat.on('legendselectchanged', function (params) {
        ngt1uidnumgt10d_option_18_1.legend.selected = {};
        $.map(params['selected'], function(value, name) {
            ngt1uidnumgt10d_option_18_1.legend.selected[name] = (name === params['name']);
        });
        ngt1uidnumgt10d_option_18_1.yAxis.type='value';
        ngt1uidnumgt10d_option_18_1.yAxis.min=0;
        ngt_1_uid_numgt10d_chat.setOption(ngt1uidnumgt10d_option_18_1);
    });
    ngt_1_uid_numgt10d_chat.on('legendselectall', function (params) {
        if (!option.legend.selected) option.legend.selected = {};
        ngt1uidnumgt10d_option_18_1.yAxis.type = 'log';
        ngt1uidnumgt10d_option_18_1.yAxis.min = yAxis_min;
        $.map(params['selected'], function(value, name) {
            ngt1uidnumgt10d_option_18_1.legend.selected[name] = true;
        });
        ngt_1_uid_numgt10d_chat.clear();
        ngt_1_uid_numgt10d_chat.setOption(ngt1uidnumgt10d_option_18_1);
    });*/

    /**
     * 获取主数据
     */
    function getData(){
        var date_start = $('#date_start').val();
        var date_end = $('#date_end').val();
        var chnl_id = $('#chnl_id').val();
        if(chnl_id == null){
            chnl_id = 2;
        }

        Request.get("/Monitor/ClientStat/statQuxian.html?flag=quxian&date_start="+date_start+"&date_end="+date_end+"&chnl_id="+chnl_id).then(function (data) {
            display(data.data);
        });
    }
    getData();  //加载完页面后获取表格数据


    function getWhere() {
        let where = {};
        let date_start = $("#date_start").val();
        let date_end = $("#date_end").val();
        //防止动态客户列表没渲染完成
        let father_id = $("#father_id").val() ? $("#father_id").val() : getRequestParam('father_id');
        let customer_id = $("#customer_id").val();
        let product_id = $("#product_id").val();
        let operator = $("#operator").val();
        if (('' === date_start) || ('' === date_end)) {
            Popup.error('请选择日期区间');
            return false;
        }
        if (!father_id) {
            Popup.error('请选择主产品');
            return false;
        }
        where.start_date   = date_start.replaceAll('-', '');
        where.end_date     = date_end.replaceAll('-', '');
        where.father_id  = father_id;
        where.customer_id  = customer_id;
        where.product_id    = product_id;
        where.operator    = operator;
        where.test_data    = $("#test_data").val();
        return where;
    }

    /**
     * 排序
     * @param data
     * @param sort
     * @returns {*}
     */
    function sortData(data, sort=null) {
        let sort_arr = sort ? sort : field_sort;
        data.sort(function(i,j) {return sort_arr.sort ? (i[sort_arr.field] - j[sort_arr.field]) : (j[sort_arr.field] - i[sort_arr.field]);});
        return data;
    }

    function display(dat)
    {
        let date_map = dateMap(dat);
        //let data = mathData(formatData(dat, date_map));
        let data = formatData(dat, date_map);
        data = sortData(data);
        let html = '';
        $.each(data, function(key, item){
            html += '<tr>';
            html += getStripClass()+item['date_str']+'</td>';
            html += getTableNumTd(item);
            strip += 1;
        });

        $("#table_data").html(getTotalHtml(dat) + html);
        //渲染折线图
        formatChart(sortData(data, {'field':'date', 'sort':1}));
    }
    function formatChart(data){

        let max = 0;
        let min = 999999;

        cnt_option.legend.data = [
            '请求量'
        ];
        cnt_option.legend.selected = {
            '请求量' : true

        };
        cnt_option.series = [
            {name: '请求量', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['请求量'] =  value['cnt'] ? parseInt(value['cnt']) : 0;
                return value['cnt']})
            }
        ];
        cnt_option.xAxis.data = $.map(data, function(value){return value['date_str']});//鼠标放上去的时候显示
        cnt_option.xAxis.interval = 1;

        yAxis_min = Math.pow(0, String(min).length - 1);
        cnt_chat.setOption(cnt_option);


        /*************************2-1**************************/
        uidnum_option_2_1.legend.data = [
            'uid个数'
        ];
        uidnum_option_2_1.legend.selected = {
            'uid个数' : true

        };
        uidnum_option_2_1.series = [
            {name: 'uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                return value['uidnum']})
            }
        ];
        uidnum_option_2_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_num_chat.setOption(uidnum_option_2_1);



        /*******************2-2********************************/
        uidnumpct_option_2_2.legend.data = [
            'uid密度'
        ];
        uidnumpct_option_2_2.legend.selected = {
            'uid密度' : true

        };
        uidnumpct_option_2_2.series = [
            {name: 'uid密度', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumpct'] =  value['uidnumpct'] ? parseInt(value['uidnumpct']) : 0;
                return value['uidnumpct']})
            }
        ];
        uidnumpct_option_2_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_num_pct_chat.setOption(uidnumpct_option_2_2);

        /*******************************************************/


        telnum_option_3_1.legend.data = [
            '号码个数'
        ];
        telnum_option_3_1.legend.selected = {
            '号码个数' : true

        };
        telnum_option_3_1.series = [
            {name: '号码个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['telnum'] =  value['telnum'] ? parseInt(value['telnum']) : 0;
                return value['telnum']})
            }
        ];
        telnum_option_3_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel_num_chat.setOption(telnum_option_3_1);
        /******************************************/
        telnumpct_option_3_2.legend.data = [
            '号码密度'
        ];
        telnumpct_option_3_2.legend.selected = {
            '号码密度' : true

        };
        telnumpct_option_3_2.series = [
            {name: '号码密度', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['telnumpct'] =  value['telnumpct'] ? parseInt(value['telnumpct']) : 0;
                return value['telnumpct']})
            }
        ];
        telnumpct_option_3_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel_num_pct_chat.setOption(telnumpct_option_3_2);
        /*************************************************/
        tel1num_option_4_1.legend.data = [
            '手机号个数'
        ];
        tel1num_option_4_1.legend.selected = {
            '手机号个数' : true

        };
        tel1num_option_4_1.series = [
            {name: '手机号个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['tel1num'] =  value['tel1num'] ? parseInt(value['tel1num']) : 0;
                return value['tel1num']})
            }
        ];
        tel1num_option_4_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel1_num_chat.setOption(tel1num_option_4_1);
        /*************************************************/

        tel1numpct_option_4_2.legend.data = [
            '手机号占比'
        ];
        tel1numpct_option_4_2.legend.selected = {
            '手机号占比' : true

        };
        tel1numpct_option_4_2.series = [
            {name: '手机号占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['tel1numpct'] =  value['tel1numpct'] ? parseInt(value['tel1numpct']) : 0;
                return value['tel1numpct']})
            }
        ];
        tel1numpct_option_4_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel1_num_pct_chat.setOption(tel1numpct_option_4_2);
        /*************************************************/

        tel2num_option_5_1.legend.data = [
            '手机号占比'
        ];
        tel2num_option_5_1.legend.selected = {
            '手机号占比' : true

        };
        tel2num_option_5_1.series = [
            {name: '手机号占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['tel2num'] =  value['tel2num'] ? parseInt(value['tel2num']) : 0;
                return value['tel2num']})
            }
        ];
        tel2num_option_5_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel2_num_chat.setOption(tel2num_option_5_1);

        /*************************************************/

        tel2numpct_option_5_2.legend.data = [
            '手机号占比'
        ];
        tel2numpct_option_5_2.legend.selected = {
            '手机号占比' : true

        };
        tel2numpct_option_5_2.series = [
            {name: '热线号占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['tel2numpct'] =  value['tel2numpct'] ? parseInt(value['tel2numpct']) : 0;
                return value['tel2numpct']})
            }
        ];
        tel2numpct_option_5_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel2_num_pct_chat.setOption(tel2numpct_option_5_2);
        /*************************************************/

        tel3num_option_6_1.legend.data = [
            '固话号个数'
        ];
        tel3num_option_6_1.legend.selected = {
            '固话号个数' : true

        };
        tel3num_option_6_1.series = [
            {name: '固话号个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['tel3num'] =  value['tel3num'] ? parseInt(value['tel3num']) : 0;
                return value['tel3num']})
            }
        ];
        tel3num_option_6_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel3_num_chat.setOption(tel3num_option_6_1);
        /*************************************************/

        tel3numpct_option_6_2.legend.data = [
            '固定号占比'
        ];
        tel3numpct_option_6_2.legend.selected = {
            '固定号占比' : true

        };
        tel3numpct_option_6_2.series = [
            {name: '固定号占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['tel3numpct'] =  value['tel3numpct'] ? parseInt(value['tel3numpct']) : 0;
                return value['tel3numpct']})
            }
        ];
        tel3numpct_option_6_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        tel3_num_pct_chat.setOption(tel3numpct_option_6_2);
        tel3_num_chat.setOption(tel3num_option_6_1);
        /*************************************************/

        uidnumge100w_option_7_1.legend.data = [
            '频数ge100万的uid个数'
        ];
        uidnumge100w_option_7_1.legend.selected = {
            '频数ge100万的uid个数' : true

        };
        uidnumge100w_option_7_1.series = [
            {name: '频数ge100万的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge100w'] =  value['uidnumge100w'] ? parseInt(value['uidnumge100w']) : 0;
                return value['uidnumge100w']})
            }
        ];
        uidnumge100w_option_7_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_100w_chat.setOption(uidnumge100w_option_7_1);
        /*************************************************/

        uidnumge10w_option_7_2.legend.data = [
            '频数ge10万的uid个数'
        ];
        uidnumge10w_option_7_2.legend.selected = {
            '频数ge10万的uid个数' : true

        };
        uidnumge10w_option_7_2.series = [
            {name: '频数ge10万的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge10w'] =  value['uidnumge10w'] ? parseInt(value['uidnumge10w']) : 0;

                return value['uidnumge10w']})
            }
        ];
        uidnumge10w_option_7_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_10w_chat.setOption(uidnumge10w_option_7_2);





        /*************************************************/

        uidnumge1w_option_8_1.legend.data = [
            '频数ge1万的uid个数'
        ];
        uidnumge1w_option_8_1.legend.selected = {
            '频数ge1万的uid个数' : true

        };
        uidnumge1w_option_8_1.series = [
            {name: '频数ge1万的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge1w'] =  value['uidnumge1w'] ? parseInt(value['uidnumge1w']) : 0;
                return value['uidnumge1w']})
            }
        ];
        uidnumge1w_option_8_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_1w_chat.setOption(uidnumge1w_option_8_1);
        /*************************************************/

        uidnumge1000_option_8_2.legend.data = [
            '频数ge1000的uid个数'
        ];
        uidnumge1000_option_8_2.legend.selected = {
            '频数ge1000的uid个数' : true

        };
        uidnumge1000_option_8_2.series = [
            {name: '频数ge1000的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge1000'] =  value['uidnumge1000'] ? parseInt(value['uidnumge1000']) : 0;
                return value['uidnumge1000']})
            }
        ];
        uidnumge1000_option_8_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_1000_chat.setOption(uidnumge1000_option_8_2);




        /*************************************************/

        uidnumge100_option_10_1.legend.data = [
            '频数ge100的uid个数'
        ];
        uidnumge100_option_10_1.legend.selected = {
            '频数ge100的uid个数' : true

        };
        uidnumge100_option_10_1.series = [
            {name: '频数ge100的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge100'] =  value['uidnumge100'] ? parseInt(value['uidnumge100']) : 0;
                return value['uidnumge100']})
            }
        ];
        uidnumge100_option_10_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_100_chat.setOption(uidnumge100_option_10_1);
        /*************************************************/

        uidnumge100pct_option_10_2.legend.data = [
            '频数ge100的uid占比'
        ];
        uidnumge100pct_option_10_2.legend.selected = {
            '频数ge100的uid占比' : true

        };
        uidnumge100pct_option_10_2.series = [
            {name: '频数ge100的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge100pct'] =  value['uidnumge100pct'] ? parseInt(value['uidnumge100pct']) : 0;
                return value['uidnumge100pct']})
            }
        ];
        uidnumge100pct_option_10_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_100_pct_chat.setOption(uidnumge100pct_option_10_2);
        /*************************************************/

        uidnumge50_option_11_1.legend.data = [
            '频数ge50的uid个数'
        ];
        uidnumge50_option_11_1.legend.selected = {
            '频数ge50的uid个数' : true

        };
        uidnumge50_option_11_1.series = [
            {name: '频数ge50的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge50'] =  value['uidnumge50'] ? parseInt(value['uidnumge50']) : 0;
                return value['uidnumge50']})
            }
        ];
        uidnumge50_option_11_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_50_chat.setOption(uidnumge50_option_11_1);


        /*************************************************/

        uidnumge50pct_option_11_2.legend.data = [
            '频数ge50的uid占比'
        ];
        uidnumge50pct_option_11_2.legend.selected = {
            '频数ge50的uid占比' : true

        };
        uidnumge50pct_option_11_2.series = [
            {name: '频数ge50的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumge50pct'] =  value['uidnumge50pct'] ? parseInt(value['uidnumge50pct']) : 0;
                return value['uidnumge50pct']})
            }
        ];
        uidnumge50pct_option_11_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numge_50_pct_chat.setOption(uidnumge50pct_option_11_2);


        /*************************************************/

        uidnume1_option_12_1.legend.data = [
            '频数等于1的uid个数'
        ];
        uidnume1_option_12_1.legend.selected = {
            '频数等于1的uid个数' : true

        };
        uidnume1_option_12_1.series = [
            {name: '频数等于1的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnume1'] =  value['uidnume1'] ? parseInt(value['uidnume1']) : 0;
                return value['uidnume1']})
            }
        ];
        uidnume1_option_12_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_nume_1_chat.setOption(uidnume1_option_12_1);


        /*************************************************/

        uidnume1pct_option_12_2.legend.data = [
            '频数等于1的uid占比'
        ];
        uidnume1pct_option_12_2.legend.selected = {
            '频数等于1的uid占比' : true

        };
        uidnume1pct_option_12_2.series = [
            {name: '频数等于1的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnume1pct'] =  value['uidnume1pct'] ? parseInt(value['uidnume1pct']) : 0;
                return value['uidnume1pct']})
            }
        ];
        uidnume1pct_option_12_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_nume_1_pct_chat.setOption(uidnume1pct_option_12_2);

        /*************************************************/

        uidnumle2_option_13_1.legend.data = [
            '频数le2的uid个数'
        ];
        uidnumle2_option_13_1.legend.selected = {
            '频数le2的uid个数' : true

        };
        uidnumle2_option_13_1.series = [
            {name: '频数le2的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumle2'] =  value['uidnumle2'] ? parseInt(value['uidnumle2']) : 0;
                return value['uidnumle2']})
            }
        ];
        uidnumle2_option_13_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numle_2_chat.setOption(uidnumle2_option_13_1);

        /*************************************************/

        uidnumle2pct_option_13_2.legend.data = [
            '频数le2的uid占比'
        ];
        uidnumle2pct_option_13_2.legend.selected = {
            '频数le2的uid占比' : true

        };
        uidnumle2pct_option_13_2.series = [
            {name: '频数le2的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                return value['uidnumle2pct']})
            }
        ];
        uidnumle2pct_option_13_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numle_2_pct_chat.setOption(uidnumle2pct_option_13_2);

        /*************************************************/

        uidnumle5_option_14_1.legend.data = [
            '频数le5的uid个数'
        ];
        uidnumle5_option_14_1.legend.selected = {
            '频数le5的uid个数' : true

        };
        uidnumle5_option_14_1.series = [
            {name: '频数le5的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumle5'] =  value['uidnumle5'] ? parseInt(value['uidnumle5']) : 0;
                return value['uidnumle5']})
            }
        ];
        uidnumle5_option_14_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numle_5_chat.setOption(uidnumle5_option_14_1);

        /*************************************************/

        uidnumle5pct_option_14_2.legend.data = [
            '频数le5的uid占比'
        ];
        uidnumle5pct_option_14_2.legend.selected = {
            '频数le5的uid占比' : true

        };
        uidnumle5pct_option_14_2.series = [
            {name: '频数le5的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumle5pct'] =  value['uidnumle5pct'] ? parseInt(value['uidnumle5pct']) : 0;
                return value['uidnumle5pct']})
            }
        ];
        uidnumle5pct_option_14_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        uid_numle_5_pct_chat.setOption(uidnumle5pct_option_14_2);


        /*************************************************/

        ngt1uidnum_option_15_1.legend.data = [
            '号码个数ge2的uid个数'
        ];
        ngt1uidnum_option_15_1.legend.selected = {
            '号码个数ge2的uid个数' : true

        };
        ngt1uidnum_option_15_1.series = [
            {name: '号码个数ge2的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnum'] =  value['ngt1uidnum'] ? parseInt(value['ngt1uidnum']) : 0;
                return value['ngt1uidnum']})
            }
        ];
        ngt1uidnum_option_15_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_num_chat.setOption(ngt1uidnum_option_15_1);


        /*************************************************/

        ngt1uidnumpct_option_15_2.legend.data = [
            '号码个数ge2的uid占比'
        ];
        ngt1uidnumpct_option_15_2.legend.selected = {
            '号码个数ge2的uid占比' : true

        };
        ngt1uidnumpct_option_15_2.series = [
            {name: '号码个数ge2的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnumpct'] =  value['ngt1uidnumpct'] ? parseInt(value['ngt1uidnumpct']) : 0;
                return value['ngt1uidnumpct']})
            }
        ];
        ngt1uidnumpct_option_15_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        letngt_1_uid_num_pct_chat.setOption(ngt1uidnumpct_option_15_2);


        /*************************************************/

        ngt1uidnumgt1d_option_16_1.legend.data = [
            '号码个数ge2且近30d天数ge2的uid个数'
        ];
        ngt1uidnumgt1d_option_16_1.legend.selected = {
            '号码个数ge2且近30d天数ge2的uid个数' : true

        };
        ngt1uidnumgt1d_option_16_1.series = [
            {name: '号码个数ge2且近30d天数ge2的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnumgt1d'] =  value['ngt1uidnumgt1d'] ? parseInt(value['ngt1uidnumgt1d']) : 0;
                return value['ngt1uidnumgt1d']})
            }
        ];
        ngt1uidnumgt1d_option_16_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_numgt1d_chat.setOption(ngt1uidnumgt1d_option_16_1);


        /*************************************************/

        ngt1uidnumgt1dpct_option_16_2.legend.data = [
            '号码个数ge2且近30d天数ge2的uid占比'
        ];
        ngt1uidnumgt1dpct_option_16_2.legend.selected = {
            '号码个数ge2且近30d天数ge2的uid占比' : true

        };
        ngt1uidnumgt1dpct_option_16_2.series = [
            {name: '号码个数ge2且近30d天数ge2的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnumgt1dpct'] =  value['ngt1uidnumgt1dpct'] ? parseInt(value['ngt1uidnumgt1dpct']) : 0;
                return value['ngt1uidnumgt1dpct']})
            }
        ];
        ngt1uidnumgt1dpct_option_16_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_numgt1d_pct_chat.setOption(ngt1uidnumgt1dpct_option_16_2);
        /*************************************************/

        ngt1uidnumgt5d_option_17_1.legend.data = [
            '号码个数ge2且近30d天数ge5的uid个数'
        ];
        ngt1uidnumgt5d_option_17_1.legend.selected = {
            '号码个数ge2且近30d天数ge5的uid个数' : true

        };
        ngt1uidnumgt5d_option_17_1.series = [
            {name: '号码个数ge2且近30d天数ge5的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnumgt5d'] =  value['ngt1uidnumgt5d'] ? parseInt(value['ngt1uidnumgt5d']) : 0;
                return value['ngt1uidnumgt5d']})
            }
        ];
        ngt1uidnumgt5d_option_17_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_numgt5d_chat.setOption(ngt1uidnumgt5d_option_17_1);
        /*************************************************/

        ngt1uidnumgt5dpct_option_17_2.legend.data = [
            '号码个数ge2且近30d天数ge5的uid占比'
        ];
        ngt1uidnumgt5dpct_option_17_2.legend.selected = {
            '号码个数ge2且近30d天数ge5的uid占比' : true

        };
        ngt1uidnumgt5dpct_option_17_2.series = [
            {name: '号码个数ge2且近30d天数ge5的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['uidnumngt1uidnumgt5dpctge10w'] =  value['ngt1uidnumgt5dpct'] ? parseInt(value['ngt1uidnumgt5dpct']) : 0;
                return value['ngt1uidnumgt5dpct']})
            }
        ];
        ngt1uidnumgt5dpct_option_17_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_numgt5d_pct_chat.setOption(ngt1uidnumgt5dpct_option_17_2);

        /*************************************************/

        ngt1uidnumgt10d_option_18_1.legend.data = [
            '号码个数ge2且近30d天数ge10的uid个数'
        ];
        ngt1uidnumgt10d_option_18_1.legend.selected = {
            '号码个数ge2且近30d天数ge10的uid个数' : true

        };
        ngt1uidnumgt10d_option_18_1.series = [
            {name: '号码个数ge2且近30d天数ge10的uid个数', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnumgt10d'] =  value['ngt1uidnumgt10d'] ? parseInt(value['ngt1uidnumgt10d']) : 0;
                /*if(value['ngt1uidnumgt10d'] && value['ngt1uidnumgt10d']>0 && value['ngt1uidnumgt10d'] > max){max=value['ngt1uidnumgt10d']}
                if(value['ngt1uidnumgt10d'] && value['ngt1uidnumgt10d']>0 && value['ngt1uidnumgt10d'] < min){min=value['ngt1uidnumgt10d']}*/
                return value['ngt1uidnumgt10d']})
            }
        ];
        //yAxis_min = Math.pow(10, String(min).length - 1);
        ngt1uidnumgt10d_option_18_1.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_numgt10d_chat.setOption(ngt1uidnumgt10d_option_18_1);

        /*************************************************/

        ngt1uidnumgt10dpct_option_18_2.legend.data = [
            '号码个数ge2且近30d天数ge10的uid占比'
        ];
        ngt1uidnumgt10dpct_option_18_2.legend.selected = {
            '号码个数ge2且近30d天数ge10的uid占比' : true

        };
        ngt1uidnumgt10dpct_option_18_2.series = [
            {name: '号码个数ge2且近30d天数ge10的uid占比', type: 'line', animationDuration: 500, data: $.map(data, function(value){
                value['ngt1uidnumgt10dpct'] =  value['ngt1uidnumgt10dpct'] ? parseInt(value['uidnumge10w']) : 0;
                return value['ngt1uidnumgt10dpct']})
            }
        ];
        ngt1uidnumgt10dpct_option_18_2.xAxis.data = $.map(data, function(value){return value['date_str']});
        ngt_1_uid_numgt10d_pct_chat.setOption(ngt1uidnumgt10dpct_option_18_2);



    }
    /**
     * 比例数据和成本后期计算
     * @param data
     * @returns {*}
     */
    function mathData(data) {
        $.each(data, function(key, item){
            let success = item['total'] ? item['success'] / item['total'] * 100 : 0;
            let valid = item['total'] ? item['valid'] / item['total'] * 100 : 0;
            data[key]['success_ratio'] = success > 10 ? parseInt(success) : success.toFixed(2);
            data[key]['valid_ratio'] = valid > 10 ? parseInt(valid) : valid.toFixed(2);
            data[key]['number'] = item['number'] ? item['number'] : 0;
            if ((item['money'] === '-') || (item['cost'] === '-')) {
                data[key]['money'] = data[key]['cost'] = data[key]['profit'] = '-';
            } else {
                data[key]['money'] = parseFloat(item['money']).toFixed(2);
                data[key]['cost'] = parseFloat(item['cost']).toFixed(2);
                data[key]['profit'] = (data[key]['money'] - data[key]['cost']).toFixed(2);
            }
        });
        return data;
    }

    function formatData(data, date_map){
        let map_arr = {};
        let map_key = '';
        $.each(data, function(key, item){
            map_key = date_map[key];
            if (!map_arr.hasOwnProperty(map_key)) {
                map_arr[map_key] = getTpl();
                map_arr[map_key]['date_str'] = map_key;
                map_arr[map_key]['date'] = item['date'];
            }
            map_arr[map_key] = addValue(map_arr[map_key], item);
        });
        // 为了使用sort排序 把object改成list
        return Object.values(map_arr);
    }

    /**
     * 总合计
     * @returns {[]}
     */
    function getTotalHtml(dat) {
        let total = getTpl();
        $.each(dat, function(key, item){
            total = addValue(total, item);
        });
        total = mathData([total]);
        let html = '<tr style="background-color: #EEEEEE;color:#009688"><td>合计</td>';
        html += getTableNumTd(total[0]);
        return html;
    }

    function getTableNumTd(item){
        let html = getStripClass()+item['chnl_name']+'</td>';
        html += getStripClass()+item['cnt']+'</td>';
        html += getStripClass()+item['uidnum']+'</td>';
        html += getStripClass()+item['telnum']+'</td>';
        html += getStripClass()+item['tel1num']+'</td>';
        html += getStripClass()+item['tel2num']+'</td>';
        html += getStripClass()+item['tel3num']+'</td>';
        html += getStripClass()+item['uidnumpct']+'</td>';
        html += getStripClass()+item['telnumpct']+'</td>';
        html += getStripClass()+item['tel1numpct']+'</td>';
        html += getStripClass()+item['tel2numpct']+'</td>';
        html += getStripClass()+item['tel3numpct']+'</td>';
        html += getStripClass()+item['uidnumge100w']+'</td>';
        html += getStripClass()+item['uidnumge10w']+'</td>';
        html += getStripClass()+item['uidnumge1w']+'</td>';
        html += getStripClass()+item['uidnumge1000']+'</td>';
        html += getStripClass()+item['uidnumge100']+'</td>';
        html += getStripClass()+item['uidnumge100pct']+'</td>';
        html += getStripClass()+item['uidnumge50']+'</td>';
        html += getStripClass()+item['uidnumge50pct']+'</td>';
        html += getStripClass()+item['uidnume1']+'</td>';
        html += getStripClass()+item['uidnume1pct']+'</td>';
        html += getStripClass()+item['uidnumle2']+'</td>';
        html += getStripClass()+item['uidnumle2pct']+'</td>';
        html += getStripClass()+item['uidnumle5']+'</td>';
        html += getStripClass()+item['uidnumle5pct']+'</td>';
        html += getStripClass()+item['ngt1uidnum']+'</td>';
        html += getStripClass()+item['ngt1uidnumpct']+'</td>';
        html += getStripClass()+item['ngt1uidnumgt1d']+'</td>';
        html += getStripClass()+item['ngt1uidnumgt1dpct']+'</td>';
        html += getStripClass()+item['ngt1uidnumgt5d']+'</td>';
        html += getStripClass()+item['ngt1uidnumgt5dpct']+'</td>';
        html += getStripClass()+item['ngt1uidnumgt10d']+'</td>';
        html += getStripClass()+item['ngt1uidnumgt10dpct']+'</td>';
        html += '</tr>';
        return html;
    }
    /**
     * 计算合计值
     * @param data
     * @param add
     * @param pid
     * @returns {*}
     */
    function addValue(data, add) {

        /**************zj****************/
        data['chnl_name'] = add['chnl_name'];
        data['cnt'] += parseInt(add['cnt']) ? parseInt(add['cnt']) : 0;
        data['uidnum'] += parseInt(add['uidnum']) ? parseInt(add['uidnum']) : 0;
        data['uidnumpct'] += parseInt(add['uidnumpct']) ? parseInt(add['uidnumpct']) : 0;
        data['telnum'] += parseInt(add['telnum']) ? parseInt(add['telnum']) : 0;
        data['telnumpct'] += parseInt(add['telnumpct']) ? parseInt(add['telnumpct']) : 0;
        data['tel1num'] += parseInt(add['tel1num']) ? parseInt(add['tel1num']) : 0;
        data['tel1numpct'] += parseInt(add['tel1numpct']) ? parseInt(add['tel1numpct']) : 0;
        data['tel2num'] += parseInt(add['tel2num']) ? parseInt(add['tel2num']) : 0;
        data['tel2numpct'] += parseInt(add['tel2numpct']) ? parseInt(add['tel2numpct']) : 0;
        data['tel3num'] += parseInt(add['tel3num']) ? parseInt(add['tel3num']) : 0;
        data['tel3numpct'] += parseInt(add['tel3numpct']) ? parseInt(add['tel3numpct']) : 0;
        data['uidnumge100w'] += parseInt(add['uidnumge100w']) ? parseInt(add['uidnumge100w']) : 0;
        data['uidnumge10w'] += parseInt(add['uidnumge10w']) ? parseInt(add['uidnumge10w']) : 0;

        data['uidnumge1w'] += parseInt(add['uidnumge1w']) ? parseInt(add['uidnumge1w']) : 0;
        data['uidnumge1000'] += parseInt(add['uidnumge1000']) ? parseInt(add['uidnumge1000']) : 0;

        data['uidnumge100'] += parseInt(add['uidnumge1w']) ? parseInt(add['uidnumge1w']) : 0;
        data['uidnumge100pct'] += parseInt(add['uidnumge1000']) ? parseInt(add['uidnumge1000']) : 0;

        data['uidnumge50'] += parseInt(add['uidnumge1w']) ? parseInt(add['uidnumge1w']) : 0;
        data['uidnumge50pct'] += parseInt(add['uidnumge1000']) ? parseInt(add['uidnumge1000']) : 0;

        data['uidnume1'] += parseInt(add['uidnume1']) ? parseInt(add['uidnume1']) : 0;
        data['uidnume1pct'] += parseInt(add['uidnume1pct']) ? parseInt(add['uidnume1pct']) : 0;

        data['uidnumle2'] += parseInt(add['uidnumle2']) ? parseInt(add['uidnumle2']) : 0;
        data['uidnumle2pct'] += parseInt(add['uidnumle2pct']) ? parseInt(add['uidnumle2pct']) : 0;

        data['uidnumle5'] += parseInt(add['uidnumle5']) ? parseInt(add['uidnumle5']) : 0;
        data['uidnumle5pct'] += parseInt(add['uidnumle5pct']) ? parseInt(add['uidnumle5pct']) : 0;

        data['ngt1uidnum'] += parseInt(add['ngt1uidnum']) ? parseInt(add['ngt1uidnum']) : 0;
        data['ngt1uidnumpct'] += parseInt(add['ngt1uidnumpct']) ? parseInt(add['ngt1uidnumpct']) : 0;

        data['ngt1uidnumgt1d'] += parseInt(add['ngt1uidnumgt1d']) ? parseInt(add['ngt1uidnumgt1d']) : 0;
        data['ngt1uidnumgt1dpct'] += parseInt(add['ngt1uidnumgt1dpct']) ? parseInt(add['ngt1uidnumgt1dpct']) : 0;

        data['ngt1uidnumgt5d'] += parseInt(add['ngt1uidnumgt5d']) ? parseInt(add['ngt1uidnumgt5d']) : 0;
        data['ngt1uidnumgt5dpct'] += parseInt(add['ngt1uidnumgt5dpct']) ? parseInt(add['ngt1uidnumgt5dpct']) : 0;

        data['ngt1uidnumgt10d'] += parseInt(add['ngt1uidnumgt10d']) ? parseInt(add['ngt1uidnumgt10d']) : 0;
        data['ngt1uidnumgt10dpct'] += parseInt(add['ngt1uidnumgt10dpct']) ? parseInt(add['ngt1uidnumgt10dpct']) : 0;




        /***************************/

        if (data['money'] !== '-' && data['cost'] !== '-' && add['money'] !== '-' && add['cost'] !== '-') {
            data['money'] = addBigNumber(data['money'], add['money']);
            data['cost'] = addBigNumber(data['cost'], add['cost']);
        } else {
            data['money'] = data['cost'] = '-';
        }


        return data;
    }
    function addBigNumber(i, j) {
        i = isNaN(i) ? 0 : i;
        j = isNaN(j) ? 0 : j;
        return (new BigNumber(i)).plus(new BigNumber(j)).toNumber();
    }
    /**
     * 数据模板
     */
    function getTpl(){
        return {
            'date':0,
            'chnl_name':'未知',
            'cnt':0,
            'uidnum':0,
            'uidnumpct':0,
            'telnum':0,
            'telnumpct':0,
            'tel1num':0,
            'tel1numpct':0,
            'tel2num':0,
            'tel2numpct':0,
            'tel3num':0,
            'tel3numpct':0,
            'uidnumge100w' : 0,
            'uidnumge10w' : 0,
            'uidnumge1w':0,
            'uidnumge1000':0,
            'uidnumge100':0,
            'uidnumge100pct':0,
            'uidnumge50':0,
            'uidnumge50pct':0,
            'uidnume1':0,
            'uidnume1pct':0,
            'uidnumle2':0,
            'uidnumle2pct':0,
            'uidnumle5':0,
            'uidnumle5pct' : 0,
            'ngt1uidnum' : 0,
            'ngt1uidnumpct':0,
            'ngt1uidnumgt1d':0,
            'ngt1uidnumgt1dpct':0,
            'ngt1uidnumgt5d':0,
            'ngt1uidnumgt5dpct':0,
            'ngt1uidnumgt10d':0,
            'ngt1uidnumgt10dpct':0
        };
    }

    function dateMap(dat) {
        let date_map = {};
        for(j in dat){
            date_map[j] = j;
        }
       // let day_type = $('input:radio[name=day_type]:checked').val();
       /* let date_start = $("#date_start").val();
        let date_end = $("#date_end").val();
        if (('' === date_start) || ('' === date_end)) {
            Popup.error('请选择日期区间');
            return false;
        }
        let date_map = {};
        let s_time = (new Date(date_start)).getTime();
        let e_time = (new Date(date_end)).getTime();
        let curr_time = s_time;
        let date_str = '';
        while (true) {
            let curr_date = (new Date(curr_time).toLocaleDateString()).split("/");
            let key = curr_date[0]+Str.left_pad(curr_date[1], 2, 0)+Str.left_pad(curr_date[2], 2, 0);
            /!*if (day_type === 'week') {
                let week = (new Date(curr_time)).getDay();
                let end_week = curr_time + (getWeekNum('end', week) * 86400 * 1000);
                let start_week = curr_time - (getWeekNum('start', week) * 86400 * 1000);
                (start_week < s_time) && (start_week = s_time);
                (end_week > e_time) && (end_week = e_time);
                let monday_date = (new Date(start_week).toLocaleDateString()).split("/");
                let sunday_date = (new Date(end_week).toLocaleDateString()).split("/");
                date_str = Str.left_pad(monday_date[1], 2, 0)+Str.left_pad(monday_date[2], 2, 0) + '~' +
                        Str.left_pad(sunday_date[1], 2, 0)+Str.left_pad(sunday_date[2], 2, 0);
            } else if (day_type === 'month') {
                date_str = curr_date[0] + '-' + Str.left_pad(curr_date[1], 2, 0);
                if (curr_date[1] === '12') {
                    date_str += '~' + (parseInt(curr_date[0]) + 1) + '-01';
                } else {
                    date_str += '~' + curr_date[0] + '-' + Str.left_pad(String(parseInt(curr_date[1]) + 1), 2, 0);
                }
            } else {
                date_str = key;
            }*!/
            date_str = key;
            date_map[key] = date_str;
            if (curr_time >= e_time) break;
            curr_time += 86400 * 1000;
        }*/
        return date_map;
    }
    function getWeekNum(type, week){
        let start_week_map = {5:0, 6:1, 0:2, 1:3, 2:4, 3:5, 4:6};
        let end_week_map = {4:0, 3:1, 2:2, 1:3, 0:4, 6:5, 5:6};
        return (type === 'start') ? start_week_map[week] : end_week_map[week];
    }


    /****************zj********************/

    let cnt_option = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnum_option_2_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let telnum_option_3_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let tel1num_option_4_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let tel2num_option_5_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let tel3num_option_6_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumpct_option_2_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let telnumpct_option_3_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let tel1numpct_option_4_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let tel2numpct_option_5_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let tel3numpct_option_6_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge100w_option_7_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge10w_option_7_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge1w_option_8_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge1000_option_8_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge100_option_10_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge100pct_option_10_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge50_option_11_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumge50pct_option_11_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnume1_option_12_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnume1pct_option_12_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumle2_option_13_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumle2pct_option_13_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumle5_option_14_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let uidnumle5pct_option_14_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnum_option_15_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumpct_option_15_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumgt1d_option_16_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumgt1dpct_option_16_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumgt5d_option_17_1 = {title: {text: '	'}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumgt5dpct_option_17_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumgt10d_option_18_1 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};
    let ngt1uidnumgt10dpct_option_18_2 = {title: {text: ''}, tooltip: {trigger: 'axis'}, legend: {data: [], selector: [{type: 'all', title: '全选'}]}, grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true}, toolbox: {feature: {saveAsImage: {}}}, xAxis: {type: 'category', boundaryGap: false, data: []}, yAxis: {type: 'value', axisLine: {show: true}}, series: []};


    /***********************************/

    /**
     * 表格斑马条纹样式
     * @returns {string}
     */
    function getStripClass(){return '<td class="strip_'+strip%2+'">';}
</script>

</body>
</html>
