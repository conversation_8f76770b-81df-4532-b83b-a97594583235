<!DOCTYPE html>
<html lang="en">
    <head>
<!--        <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
        <include file="Common@Public/head"/>
        <script src="__JS__select2.full.min.js"></script>
        <script src="__JS__xm-select.js"></script>
        <link rel="stylesheet" href="__CSS__select2.min.css">
        <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
        <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
        <style>
            /*表格条纹*/
            .strip_1 {
                background-color: #f2f2f2;
            }
            .layui-table-tool-temp{
                Display:none;
            }
            .layui-table-tool{
                display: none;
            }
        </style>
    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>

    <div class="container">
        <form class="layui-form layui-row list_form">
            <div style="display: flex">
                <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width:300px">
                    <label class="layui-form-label">渠道：</label>
                    <div class="layui-input-block">
                        <select name="channel_id" lay-search id="channel_id" lay-filter="channel_id">
                        </select>
                    </div>
                </div>

                <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:335px">
                    <label class="layui-form-label">开始时间</label>
                    <div class="layui-input-block">
                        <input type="text" name="date_start" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="date_start" value="{:date('Y-m-d H:i:s', strtotime('-1 hour'))}" >
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:335px">
                    <label class="layui-form-label">结束时间</label>
                    <div class="layui-input-block">
                        <input type="text" name="date_end" placeholder="请选择结束时间" autocomplete="off" class="layui-input" id="date_end" value="{:date('Y-m-d H:i:s', time())}" >
                    </div>
                </div>

                <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="margin-left:5px;width:100px;">
                    <div class="layui-btn  layui-btn-radius" id="query">
                        <button class="layui-btn" lay-submit lay-filter="formSubmit"><i class="layui-icon">&#xe615;</i> 查询</button>
                    </div>
                </div>


            </div>



        </form>
    </div>

    <div class="container">
        <div id="table_and_page_div_id">
            <div class="panel panel-default table-responsive" id="tableform">

            </div>
        </div>
    </div>
    <!--<script src="__JS__opdata.js"></script>-->
    <script src="__JS__jquery.min.js" type="text/javascript"></script>
    <script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
    <script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
    <script type="application/javascript" src="__STATICS__bignumber.js-master/bignumber.js"></script>
    <script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
    <script type="application/javascript" src="__JS__/stat/stat-customer.js?version=v1.0"></script>
    <script type="application/javascript" src="__JS__eleFixed.min.js"></script>
    <script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
        <script type="application/javascript">
            layui.laydate.render({
                elem  : '#date_start',
                type  : 'datetime',
            });
            layui.laydate.render({
                elem  : '#date_end',
                type  : 'datetime',
            });

            //表单提交
            var form = layui.form;
            form.on('submit(formSubmit)', function (data) {

                console.log(data.field.date_start);
                console.log(data.field.date_end);

                if (data.field.date_start != '' && data.field.date_end != ''){
                   var start_time = getUnixTime(data.field.date_start);
                   var end_time = getUnixTime(data.field.date_end);
                    if(start_time >= end_time){
                        alert('开始时间不能大于等于结束时间，请重新选择');
                        return false;
                    }
                    if ((end_time-start_time)/86400 >2){
                        alert('请选择时间间隔小于两日');
                        return false;
                    }

                }

                if (data.field.date_start != '' && data.field.date_end == ''){
                    var start_time = getUnixTime(data.field.date_start);
                    var timestamp = Date.parse(new Date());
                    timestamp = timestamp.toString().substr(0, 10);

                   if ((timestamp-start_time)/86400 >2){
                       alert('请选择近两日内数据');
                       return false;
                   }
                }

                if (data.field.date_start == '' && data.field.date_end != ''){
                    alert('请选择开始时间');
                    return  false;
                }

                if (data.field.date_start == '' && data.field.date_end == ''){
                    alert('请选择时间段');
                    return  false;
                }
                var string ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getAllRecord?channel_id="+data.field.channel_id+'&date_start='+data.field.date_start+'&date_end='+data.field.date_end;
                getData(string);
                return false;
            });
            // 获取渠道信息
            (function () {
                Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getAllChannellabel", []).then(function (data) {
                   // console.log(data.data);
                    var count = data.data.length;
                    var html = joinData(data.data,count);
                    $("#channel_id").html(html);
                    layui.form.render('select');
                });

                var start_time = $("#date_start").val();
                var end_time = $("#date_end").val();

                var string ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getAllRecord?date_start="+start_time+'&date_end='+end_time;
                getData(string);

                //getData("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getAllRecord");
            })();


            //数组拼接下拉选择框
            function joinData(data,count){
                var html = '<option value="" ></option>';
                for (var i = 0; i <count; i++) {
                    html  += '<option value="' + data[i].channel_id + '">' + data[i].label + '</option>';
                }
                return html;
            }

            //获取数据并展示
            function getData(url){

                layui.use(['table'],function () {
                    var table = layui.table;
                    table.render({
                        elem: '#tableform'
                        ,height: 420
                        ,url: url  //数据接口
                        ,page: true //开启分页
                        ,toolbar: 'default' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
                        ,cols: [[ //表头
                            {field: 'channel_name', title: '渠道', fixed: 'left'}
                            ,{field: 'interface', title: '接口名称'}
                            ,{field: 'status_text', title: '状态'}
                            ,{field: 'value', title: '返回值'}
                            ,{field: 'run_time', title: '响应时间'}
                            ,{field: 'time', title: '时间'}
                        ]]
                        ,limit: 20
                        ,limits:[20]
                        ,defaultToolbar: false
                        ,autoSort: false
                    });

                    AutoTableHeight();
                    
                });

            }

            function getUnixTime(dateStr){
                var newstr = dateStr.replace(/-/g,'/');
                var date =  new Date(newstr);
                var time_str = date.getTime().toString();
                return time_str.substr(0, 10);
            }

            function AutoTableHeight()
            {
                var dev_obj = document.getElementById('table_and_page_div_id'); //table的父div

                var layuitable_main = dev_obj.getElementsByClassName("layui-table-main"); //在父div中找 layui-table-main 属性所在标签
                if (layuitable_main != null && layuitable_main.length > 0) {
                    layuitable_main[0].style.height = '100%';
                }

                var layuitable = dev_obj.getElementsByClassName("layui-form"); //在父div中找 layui-form 属性所在标签
                if (layuitable != null && layuitable.length > 0) {
                    layuitable[0].style.height = '100%';
                }
            }


        </script>
    </body>
</html>
