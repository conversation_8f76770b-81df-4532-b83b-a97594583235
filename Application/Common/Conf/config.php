<?php
return array(
    // 系统配置  不建议修改
    'URL_MODEL'             => 2,
    'URL_CASE_INSENSITIVE'  => false, //URL区分大小写
    'COOKIE_EXPIRE'         => 86400 * 30, // 设置cookie()设置cookie有效期

    // 项目目录配置
    'TMPL_PARSE_STRING'     => array(
        '__WEB_PATH__' => __WEB_PATH__, // 网站根目录
        '__STATICS__'  => __WEB_PATH__ . 'statics/', //资源目录
        '__CSS__'      => __WEB_PATH__ . 'statics/css/',
        '__JS__'       => __WEB_PATH__ . 'statics/js/',
        '__IMAGES__'   => __WEB_PATH__ . 'statics/images/',
    ),

    // 自定义配置
    // 'DHB_ITEMID'            => '12',
    'DHB_ITEMID'            => '12', //项目ID 在 `http://auth.mgmt.dianhua.cn` 获取
    // 'SITE_NAME'             => '后台公共框架',
    'SITE_NAME'             => '金融科技业务部后台管理系统',

    //登录用户session变量名 建议修改
    // 'LOGIN_SESSION_NAME'    => 'site_login_name',
    'LOGIN_SESSION_NAME'    => 'site_login_name',
    //用户默认密码
    'USER_DEFAULT_PASSWORD' => '123456',

    //用户密码加密盐值
    'USER_PWD_SALT'         => 'CL!kGJa93Ut$./j?YBAY~z_v_?1w5p/uTqs}$Q^uL~!Gn&mn.6|v',

    // session 保存入库
    'SESSION_TABLE'         => 'crs_system_session', // session 表 注意修改前缀
    'SESSION_OPTIONS'       => array(
        'expire' => 3600*24*5,                      //SESSION保存5天
        'type'   => 'db'
    ),

    'LIST_ROWS' => 30, //每页显示数据的条数

    'UNSURPORTS'                   => ['贵州', '海南', '青海', '山西', '西藏', '新疆', '甘肃', '河北'],

    // 指定的模块
    'MODULE_ALLOW_LIST'            => array(
        'Home',
        'Api',
        'System',
        'Task',
        'Account',
        'Stat',
        'ReportTask',
        'PreSales',
        'Monitor',
        'Bill',
		'Opdata',
		'Config',
		'Payment'
    ),
    'DEFAULT_MODULE'               => 'Home',

    // 当更新了重要字段的时候,弹窗预警(公用)
    'list_important_common_fields' => [
        'concurrency' => ['field' => 'concurrency', 'meaning' => '秒并发'],
        'daily_limit' => ['field' => 'daily_limit', 'meaning' => '日限额'],
        'month_limit' => ['field' => 'month_limit', 'meaning' => '月限额'],
        'year_limit'  => ['field' => 'year_limit', 'meaning' => '年限额'],
        'total_limit' => ['field' => 'total_limit', 'meaning' => '总限额'],
        'status'      => ['field' => 'status', 'meaning' => '产品状态'],
        'end_time'    => ['field' => 'end_time', 'meaning' => '产品截至时间'],
    ],

    // 当更新了重要字段的时候,弹窗预警(定制)
    'list_important_custom_fields' => [
        201 => [],
        202 => [],
        203 => [],
        204 => [],
        205 => [],
        206 => [],
        207 => [],
        208 => [],
        211 => [],
        212 => [],
        213 => [],
        214 => [],
        216 => [],
        230 => [],
        401 => ['output_report' => ['field' => 'output_report', 'meaning' => '输出字段'],],
        210 => ['fields' => ['field' => 'fields', 'meaning' => '开通接口'],],
        101 => ['is_level' => ['field' => 'is_level', 'meaning' => '返回字段'],],
        104 => [],
        105 => [],
        601 => [
            'encrypt_type' => ['field' => 'encrypt_type', 'meaning' => '入参加密方式'],
            'out_fields'   => ['field' => 'out_fields', 'meaning' => '接口输出字段'],
        ],
        301 => [
            'fixed_channel'        => ['field' => 'fixed_channel', 'meaning' => '爬虫供应商'],
            'is_three_check'       => ['field' => 'is_three_check', 'meaning' => '是否前置调用三要素'],
            'is_editable'          => ['field' => 'is_editable', 'meaning' => 'H5页面是否可编辑'],
            'authorize_notify_url' => ['field' => 'authorize_notify_url', 'meaning' => '授权状态推送地址'],
        ],
        302 => [
            'fixed_channel'        => ['field' => 'fixed_channel', 'meaning' => '爬虫供应商'],
            'crawl_notify_url'     => ['field' => 'crawl_notify_url', 'meaning' => '爬虫状态推送地址'],
            'authorize_notify_url' => ['field' => 'authorize_notify_url', 'meaning' => '授权状态推送地址'],
        ],
        102 => [
            'notify_url'            => ['field' => 'notify_url', 'meaning' => '详单推送地址'],
            'security_push_service' => ['field' => 'security_push_service', 'meaning' => '加密推送'],
            'need_itag'             => ['field' => 'need_itag', 'meaning' => '是否返回邦秒配单号版'],
            'is_vip'                => ['field' => 'is_vip', 'meaning' => '是否vip'],
            'report_type'           => ['field' => 'report_type', 'meaning' => '报告类型'],
            'is_logo_display'       => ['field' => 'is_logo_display', 'meaning' => '是否显示logo'],
            'report_notify_url'     => ['field' => 'report_notify_url', 'meaning' => '报告推送地址'],
        ],
    ],
);
