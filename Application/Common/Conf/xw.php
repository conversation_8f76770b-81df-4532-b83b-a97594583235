<?php
return [
    // 数据配置根据情况修改
    'DB_TYPE'              => 'mysql',
    'DB_HOST'              => '*************',
    'DB_PORT'              => '3306',
    'DB_NAME'              => 'crs',
    'DB_USER'              => 'dev',
    'DB_PWD'               => 'Yulore@2019',
    'DB_PREFIX'            => 'crs_',
    'SITE_URL'             => 'http://bmp-mgmt-weixiu.dianhua.cn/',


    //yulore备用服务器
    'yulore_spare'         => 'http://*************:8080',

    //邦秒爬mongo库
    'MONGO'                => [
        'DB_TYPE' => 'mongo',
        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST' => '*************,*************', // 数据库服务器地址
        'DB_PORT' => '27017,27017',
        'DB_NAME' => 'crs',                     // 数据库名称
        'DB_USER' => 'crs_w,crs_w',              // 数据库用户名
        'DB_PWD' => 'dsddfdf,dsddfdf',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
        'DB_CHARSET' => 'utf8',
        'DB_MASTER_NUM' => 1,
    ],

    // 微信预警redsi
    'REDIS_WECHAT_WARNING' => [
        'host' => '*************',
        'port' => '6379',
        'persistent' => true
    ],

    // 邮件队列redis
    'REDIS_EMAIL_QUEUE'    => [
        'host' => '*************',
        'port' => '6379'
    ],

    //密码重置mongo临时配置
    'PWD_MONGO'            => [
        'DB_TYPE' => 'mongo',
        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST' => '*************,*************', // 数据库服务器地址
        'DB_PORT' => '27017,27017',
        'DB_NAME' => 'pwd',                     // 数据库名称
        'DB_USER' => 'pwd_w,pwd_w',              // 数据库用户名
        'DB_PWD' => 'dsddfdf,dsddfdf',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
        'DB_CHARSET' => 'utf8',
        'DB_MASTER_NUM' => 1,            //读写分离
    ],

    'BMY_DB' => [
        'DB_TYPE' => 'mysql',
        'DB_DEPLOY_TYPE' => 0,                 //采用分布式数据库支持
        'DB_HOST' => '*************', // 数据库服务器地址
        'DB_PORT' => '3306',
        'DB_NAME' => 'alarm',                     // 数据库名称
        'DB_USER' => 'dev_lc',              // 数据库用户名
        'DB_PWD' => 'yulore',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
        'DB_CHARSET' => 'utf8',
        'DB_MASTER_NUM' => 1,
    ],

    'MYSQL_CRSAPI'             => [
        'DB_TYPE' => 'mysql',
        'DB_USER' => 'crs',
        'DB_PREFIX' => '',
        'DB_PWD' => 'crs@2017qwe',
        'DB_HOST' => '*************',
        'DB_PORT' => '3306',
        'DB_NAME' => 'crs-api',
    ],
    'DUNNING_MONGO'            => [
        'DB_TYPE' => 'mongo',
        'DB_PORT' => '27017',
        'DB_HOST' => '*************',
    ],

    //风险名单mongo库
    'RISK_LIST_MONGO'          => [
        'DB_TYPE' => 'mongo',
        'DB_HOST' => '*************', // 数据库服务器地址
    ],

    //切换通道分支的配置
    'CRS_API_CONFIG'           => [
        'domain'        => 'http://crs-api-ldd.dianhua.cn',
        'getToken'      => 'http://crs-api-ldd.dianhua.cn/token',
        'channelSwitch' => 'http://crs-api-ldd.dianhua.cn/calls/channel',
        'getChannel'    => 'http://crs-api-ldd.dianhua.cn/calls/channel/detail',
        'manage'        => 'http://*************:8000',
    ],

    // 电话标签
    'MYSQL_CONFIG_ITAG_OLD'    => [
        'DB_TYPE'   => 'mysql',
        'DB_USER'   => 'crs',
        'DB_PREFIX' => '',
        'DB_PWD'    => 'crs@2017qwe',
        'DB_HOST'   => '*************',
        'DB_PORT'   => '3306',
        'DB_NAME'   => 'crs-api',
    ],

    // 缓存邦秒配单号版账户
    'MEMCACHE_CONFIG_ITAG'     => [
      ['host' => '*************', 'port' => '11211']
    ],

    // 邦秒爬账户信息存储在redis中的配置
    'REDIS_CRAWLER'            => [
        'type' => 'redis',
        //        'host' => 'crs-redis.dianhua.cn',
        'host' => '*************',
        'port' => '6379',
    ],

    // 通道存在redis中的信息
    'REDIS_CHANNEL_CONFIG'     => [
        'type' => 'redis',
        'host' => '127.0.0.1',
        'port' => '6379',
    ],

    // 协议预览地址
    'PREVIEW_URL' => 'http://crs-ui-yy.dianhua.cn/calls/preview/token/ff7b1f324ca2e8fd19ddd088571bd520.html',

    // 客户体系数据库
    'DB_FINANCE'               => [
        'DB_TYPE'   => 'mysql',
        'DB_HOST'   => '*************',
        'DB_PORT'   => '3306',
        'DB_NAME'   => 'yulore_finance',
        'DB_USER'   => 'dev',
        'DB_PWD'    => 'Yulore@2019',
        'DB_PREFIX' => '',
    ],

    'DB_MODIAN'               => [
		// 'DB_TYPE'   => 'mysql',
		// 'DB_HOST'   => '*************',
		// 'DB_PORT'   => '3316',
		// 'DB_NAME'   => 'modian_monitor',
		// 'DB_USER'   => 'prod_mdrw',
		// 'DB_PWD'    => 'p3DN0ohb45PacCaI',
		// 'DB_PREFIX' => '',
	],

    // 邦企业查数据库
    'DB_BANG'                  => [
        'DB_TYPE'   => 'mysql',
        'DB_HOST'   => '*************',
        'DB_PORT'   => '6020',
        'DB_NAME'   => 'bang',
        'DB_USER'   => 'dev',
        'DB_PWD'    => 'yulorei7',
        'DB_PREFIX' => '',
    ],

    // 报警程序数据库
    'DB_ALARM'                 => [
        'DB_TYPE'   => 'mysql',
        'DB_HOST'   => '*************',
        'DB_PORT'   => '3306',
        'DB_NAME'   => 'alarm',
        'DB_USER'   => 'dev',
        'DB_PWD'    => 'Yulore@2019',
        'DB_PREFIX' => '',
    ],

    //back-api域名地址
    'BACK_API_DOMAIN'          => 'http://back-api-weixiu.dianhua.cn',

	//finance-manager-api域名地址
	'FINANCE_MANAGE_API_DOMAIN' => 'http://finance-manage-api-weixiu.dianhua.cn',

    //邦企查配置
    'BANG_API_CONFIG'          => [
        'domain' => 'http://bqc-test.dianhua.cn',
    ],
    //邦秒验配置
    'DATA_CHECK_CONFIG'        => [
       'domain' => 'http://private-mgmt-dev.dianhua.cn',
    ],
    //号码状态查询配置
    'TEL_STATUS_CONFIG'        => [
        'domain' => 'https://pns-dev.dianhua.cn:8094',
    ],
    // API列表 灰度版本发布API
    'LIST_API_URL'             => [
        // 邦企查接口的地址
        'bang_api_key'                              => 'DIANHUABANGBANGQICHA',
        'bang_api_secret'                           => 'BANGQICHADIANHUABANG',
        'bang_stat_list'                            => 'http://bqc-test.dianhua.cn/api/stat/client_list',
        'bang_stat_detail'                          => 'http://bqc-test.dianhua.cn/api/stat/client_detail',

        //  邦企查限额
        'bang_limit_num'                            => 'http://bqc-test.dianhua.cn/api/limitation/index',

        // 灰度上线写入的API && 私钥
        'gray_upgrade_write'                        => 'http://crs-api-yy.dianhua.cn/gray/rules/write',
        'private_key'                               => 'y~u!l@o#r$e%^dev',

        // 陌电查询帮秒配统计量
        'matching_modian_stat'                      => 'http://apis-yulore.dianhua.cn/stat/pv/?app=stat&ver=1.0&v=1&operator=shen.liu&apikey=yuloreEq8frxObVfeQLtfvzgkwFbBNaZ',

        // 邦秒爬日报
        'daily_report_crawler'                      => 'http://crs-api-ldd.dianhua.cn/admin/crawler/daily',

        // 爬虫渠道日报
        'daily_report_crawler_channel'              => 'http://crs-api-zj.dianhua.cn/admin/crawler/report',

        // 日报的 api_key api_secret
        'daily_report_api_key'                      => '0aa2cb33d0eaedc4abe412348045dc8',
        'daily_report_api_secret'                   => '0b178ed2d1d049e46472711d8f92bf4',

        // 催收分私有云日报API
        'cuishou_private_report'                    => 'http://private-mgmt-dev.dianhua.cn/priv_stat/daily',

        // 邦信分私有云统计列表API
        'cuishou_private_list'                      => 'http://private-mgmt-dev.dianhua.cn/priv_stat/lists',

        // 邦信分私有云统计详情API
        'cuishou_private_detail'                    => 'http://private-mgmt-dev.dianhua.cn/priv_stat/detail',

        // 邦秒验slug
        'data_validate_slug'                        => 'http://private-mgmt-dev.dianhua.cn/opdata_product/lists',

        // 邦秒验产品日报
        'data_validate_report'                      => 'http://private-mgmt-dev.dianhua.cn/opdata_daily/daily',

        // 邦信分快捷版API
        'cuishou_short_stat_list'                   => 'http://private-mgmt-dev.dianhua.cn/cucc_daily/stat',

        // 邦信分快捷版详情统计
        'cuishou_short_stat_detail'                 => 'http://private-mgmt-dev.dianhua.cn/cucc_daily/detail',

        // 邦信分快捷版slug列表
        'cuishou_short_slug_list'                   => 'http://private-mgmt-dev.dianhua.cn/cucc_fields/lists',

        //运营商报告数据接口地址
        'operator_report_list'                      => 'http://bmpreport.dianhua.cn',

        //统一的统计配置接口地址，xiaogang.cui 10:21 2019/1/4
        'stat_list'                                 => 'http://back-api-weixiu.dianhua.cn/statistic/list',
        'stat_detail'                               => 'http://back-api-weixiu.dianhua.cn/statistic/detail',
        'stat_day'                                  => 'http://back-api-weixiu.dianhua.cn/statistic/day',
        'stat_report'                               => 'http://back-api-weixiu.dianhua.cn/statistic/report',

        //邦秒爬统计地址
        'report_stat_list'                          => 'http://crs-api-test.dianhua.cn/admin/report/list',
        'report_stat_detail'                        => 'http://crs-api-test.dianhua.cn/admin/report/detail',
        'report_stat_day'                           => '',
        //暂不支持按天导出
        'report_stat_report'                        => 'http://crs-api-test.dianhua.cn/admin/report/day',

        //邦秒爬推送日志接口 xiaogang.cui 14:21 2019/3/8
        'bmpa_log_list'                             => 'http://crs-manage.dianhua.cn:8000/push/list',
        //列表地址
        'bmpa_log_detail'                           => 'http://crs-manage.dianhua.cn:8000/push/detail',
        //详细地址
        'bmpa_log_send'                             => 'http://crs-manage.dianhua.cn:8000/push/send',
        //重推地址

        // 清除backend-api 产品用量检测过程中产生的缓存
        'backend_api_clear_flag'                    => 'http://back-api-weixiu.dianhua.cn/product/flag',

        // 清理backend-api鉴权产生的缓存
        'backend_api_clear_auth'                    => 'http://back-api-weixiu.dianhua.cn/auth/fresh',
        //邦秒配单号版单号版统计数据推送地址（用于陌电数据的推送）
        'stat_bm_pei_modian'                        => 'http://back-api-weixiu.dianhua.cn/statistic/sendStatisInfo',

        // backend-api产品调用量查询接口
        'backend_api_amount'                        => 'http://back-api-weixiu.dianhua.cn/v1/statistic/amount',

        // 邮件发送对账单的结算单接口 更新成第二版
        'backend_api_history'                       => 'http://back-api-weixiu.dianhua.cn/v2/bill/history/customer/',

        // 邮件发送对账单的消费明细接口
        'backend_api_email_bill'                    => 'http://back-api-weixiu.dianhua.cn/v2/bill/excel/customer/',

        // 邮件发送对账单
        'backend_api_email'                         => 'http://back-api-weixiu.dianhua.cn/v2/bill/email',

        // 客户账单列表
        'backend_api_bill_customer'                 => 'http://back-api-weixiu.dianhua.cn/v2/bill/customers',

        // 客户产品账单列表
        'backend_customer_product_bill'             => 'http://back-api-weixiu.dianhua.cn/v2/bill/customer/product',

        // 产品对账单
        'backend_product_bill'                      => 'http://back-api-weixiu.dianhua.cn/v2/bill/product',

        // 邮件发送对账单的结算单接口
        //'backend_api_history'                       => 'http://back-api-weixiu.dianhua.cn/v1/bill/history/customer/',

        // 邮件发送对账单的消费明细接口
        //'backend_api_email_bill'                    => 'http://back-api-weixiu.dianhua.cn/v1/bill/excel/customer/',

        // 邮件发送对账单
        //'backend_api_email'                         => 'http://back-api-weixiu.dianhua.cn/v1/bill/email',

        // 客户账单列表
        //'backend_api_bill_customer'                 => 'http://back-api-weixiu.dianhua.cn/v1/bill/customers',

        // 客户产品账单列表
        //'backend_customer_product_bill'             => 'http://back-api-weixiu.dianhua.cn/v1/bill/customer/product',

        // 排序接口
        'backend_api_sorts'                         => 'http://back-api-weixiu.dianhua.cn/v1/bill/sorts',

        // 账单excel下载接口
        'backend_history_download'                  => 'http://back-api-weixiu.dianhua.cn/v1/bill/history/excel',


        // 下载客户产品excel
        'backend_customer_product_download'         => 'http://back-api-weixiu.dianhua.cn/v1/bill/customer/product/excel',

        // 产品对账单excel
        'backend_product_bill_download'             => 'http://back-api-weixiu.dianhua.cn/v1/bill/product/download',

        // 产品客户账单列表
        'backend_product_customer_bill'             => 'http://back-api-weixiu.dianhua.cn/v1/bill/product/customer',

        //  产品客户账单列表生成excel
        'backend_product_customer_bill_excel'       => 'http://back-api-weixiu.dianhua.cn/v1/bill/product/customer/excel',

        // 产品各月的消费金额
        'backend_product_month_bill'                => 'http://back-api-weixiu.dianhua.cn/v1/bill/product/month',

        // 产品各月的消费金额 excel
        'backend_product_month_bill_excel'          => 'http://back-api-weixiu.dianhua.cn/v1/bill/product/month/download',

        // 客户产品月对账单
        'backend_customer_product_month_bill'       => 'http://back-api-weixiu.dianhua.cn/v1/bill/customer/month',

        // 客户产品月对账单excel
        'backend_customer_month_bill_download'      => 'http://back-api-weixiu.dianhua.cn/v1/bill/customer/month/download',

        //  日账单
        'backend_day_bill'                          => 'http://back-api-weixiu.dianhua.cn/v1/bill/day',

        // 日账单生成excel
        'back_day_bill_download'                    => 'http://back-api-weixiu.dianhua.cn/v1/bill/day/excel',

        //打款单认款时需要对余额预警的标记进行清理，清理地址
        'backend_clean_balance_sign'                => 'http://back-api-weixiu.dianhua.cn/v1/bill/customer/warning/{customer_id}',

        // bill_months 查询
        'back_api_bill_month'                       => 'http://back-api-weixiu.dianhua.cn/v1/bill/list',


        //xiaogang.cui 15:00 2019/6/13 0013 添加
        //获取客户的调用量、消费金额数据（批量）
        'customer_consume'                          => 'http://back-api-weixiu.dianhua.cn/v1/bill/customer/money',
        //获取每个计费配置的调用量、消费金额数据（批量）
        'fee_config_consume'                        => 'http://back-api-weixiu.dianhua.cn/v1/bill/section',

        // 邦信分快捷版上游数据通计列表
        'back_api_upstream_shortcuts'               => 'http://back-api-weixiu.dianhua.cn/v1/upstream/shortcuts',

        // 邦信分快捷版上游数据通详情
        'back_api_upstream_shortcuts_details'       => 'http://back-api-weixiu.dianhua.cn/v1/upstream/shortcuts/',

        // 邦信分快捷版上游数据通计列表下载
        'back_api_upstream_shortcuts_download'      => 'http://back-api-weixiu.dianhua.cn/v1/upstream/shortcuts/excel',

        // 邦信分快捷版上游数据通计列表按照天下载
        'back_api_upstream_shortcuts_day_download'  => 'http://back-api-weixiu.dianhua.cn/v1/upstream/shortcuts/day/excel',

        // 邦信分快捷版上游数据通详情excel
        'back_api_upstream_shortcuts_details_excel' => 'http://back-api-weixiu.dianhua.cn/v1/upstream/shortcuts/details/excel',

        // 上游邦妙验统计列表下载
        'back_api_upstream_verification_excel'      => 'http://back-api-weixiu.dianhua.cn/v1/upstream/verification/excel',

        // 上游邦妙验统计列表按天下载
        'back_api_upstream_verification_day_excel'  => 'http://back-api-weixiu.dianhua.cn/v1/upstream/verification/day/excel',

        // 上游邦妙验统计详情
        'back_api_upstream_verification_details'    => 'http://back-api-weixiu.dianhua.cn/v1/upstream/verification/',
        // {product_id}

        'back_api_upstream_verification_details_excel' => 'http://back-api-weixiu.dianhua.cn/v1/upstream/verification/details/excel',

        // 校正账单用量 create
        'back_api_correct_numbers_create'              => 'http://back-api-weixiu.dianhua.cn/v1/correct',

        //  校正账单用量列表 get
        'back_api_correct_numbers'                     => 'http://back-api-weixiu.dianhua.cn/v1/correct',

        // 校正账单用户列表 get
        'back_api_correct_operators'                   => 'http://back-api-weixiu.dianhua.cn/v1/correct/operators',

        // 校正列表下载 post
        'back_api_correct_download'                    => 'http://back-api-weixiu.dianhua.cn/v1/correct/excel',

        // 填写日志 post
        'back_api_correct_add_comment'                 => 'http://back-api-weixiu.dianhua.cn/v1/correct/comment',

        // 备注列表 get correct/comment/{uuid}
        'back_api_correct_comments'                    => 'http://back-api-weixiu.dianhua.cn/v1/correct/comment/',

        // 日账单列表
        'back_api_bill_day_list'                       => 'http://back-api-weixiu.dianhua.cn/v1/bill/day/lists',

        // 日账单日志列表
        'back_api_bill_day_log'                        => 'http://back-api-weixiu.dianhua.cn/v1/bill/day/log/lists',

        // 数据统计原始数据列表
        'backend_api_stat_source_list'                 => 'http://back-api-weixiu.dianhua.cn/v1/log/statistic',

        // 配置管理-值分布
        'back_api_config_product_value_spread_list'    => 'http://back-api-weixiu.dianhua.cn/config/product/spread/spreadList',
        'back_api_config_product_value_spread_info'    => 'http://back-api-weixiu.dianhua.cn/config/product/spread/spreadInfo',
        'back_api_config_product_value_spread_edit'    => 'http://back-api-weixiu.dianhua.cn/config/product/spread/spreadEdit',
        'back_api_config_product_value_spread_add'     => 'http://back-api-weixiu.dianhua.cn/config/product/spread/spreadAdd',

        // 配置管理-渠道查得率阈值
        'back_api_config_channel_statis_list'          => 'http://back-api-weixiu.dianhua.cn/config/channel/statis/statisList',
        'back_api_config_channel_statis_info'          => 'http://back-api-weixiu.dianhua.cn/config/channel/statis/statisInfo',
        'back_api_config_channel_statis_edit'          => 'http://back-api-weixiu.dianhua.cn/config/channel/statis/statisEdit',
        'back_api_config_channel_statis_add'           => 'http://back-api-weixiu.dianhua.cn/config/channel/statis/statisAdd',

        //邦信分快捷版监控接口
        #评分类
        'back_api_monitor_score'                       => 'http://back-api-weixiu.dianhua.cn/monitor/bxfshort/score',
        #统计类
        'back_api_monitor_statis'                      => 'http://back-api-weixiu.dianhua.cn/monitor/bxfshort/statis',
        #产品值分布
        'back_api_monitor_value_spread'                => 'http://back-api-weixiu.dianhua.cn/monitor/bxfshort/valueSpread',
        #产品查得率
        'back_api_monitor_success_ratio'               => 'http://back-api-weixiu.dianhua.cn/monitor/bxfshort/successRatio',
        #渠道查得率
        'back_api_monitor_channel_statis'              => 'http://back-api-weixiu.dianhua.cn/monitor/bxfshort/channelStatis',


        //数据源V3相关接口
        'upstream_statistics_list'                     => 'http://back-api-weixiu.dianhua.cn/statistics/upstream/list', //统计量列表接口
        'upstream_bill_list'                           => 'http://back-api-weixiu.dianhua.cn/bill/upstream/list',       //账单列表接口

        //余额账单接口
        'profit_customer'                              => 'http://back-api-weixiu.dianhua.cn/v2/bill/customer_profit',
        'profit_product'                               => 'http://back-api-weixiu.dianhua.cn/v2/bill/product_profit',
    ],
    'MAIL'                     => [
        // 日报群组设置
        'REPORT_DAILY'   => [
            // 'host'                          => 'smtp.yulore.com',
            // // Specify main and backup SMTP servers
            // 'username'                      => '<EMAIL>',
            // // SMTP usernamer
            // 'password'                      => '01d9c39Af375A79c',
            // // SMTP password
            // 'smtp_secure'                   => 'tls',
            // // Enable TLS encryption, `ssl` also accepted
            // 'port'                          => 465,
            // // TCP port to connect to
            // 'mail_from_address_self'        => '<EMAIL>',
            // // 金融后台项目组发送邮件的邮箱
            // 'mail_from_address_official'    => '<EMAIL>',
            // // 金融日报群组发送邮件的邮箱
            // 'mail_from_name'                => '金融日报',
            // // 发送邮件人名字
            // 'mail_receive_address_self'     => '<EMAIL>',
            // // 金融后台收件人地址
            // 'mail_receive_address_official' => '<EMAIL>',
            // // 金融日报群组收件人地址
            // 'mail_receive_name'             => '金融日报邮件组',
            // // 收件人名字
            // 'mail_reply'                    => '<EMAIL>',
            // // 处理邮件的人
        ],
        // 日报之项目经理设置
        'REPORT_PROJECT' => [
            // 爬虫渠道日报
            'crawler_channel'   => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦秒爬项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 邦秒验
            'data_validate'     => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报负责人', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦秒验项目经理', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 催收分析快快捷版
            'cuishou_short'     => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报负责人', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收快捷版云项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报负责人2', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收快捷版项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 邦信分私有云
            'cuishou_private'   => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报负责人', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收分私有云项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报负责人2', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收分私有云项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 催收分
            'cuishou'           => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '催收分日报负责人', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收分项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '催收分日报负责人2', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收分项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 邦信分详单版V2
            'cuishou_v2'        => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '催收分日报负责人', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收分项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '催收分日报负责人2', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '催收分项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 邦秒配单号版
            'bm_matching'       => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦秒配单号版项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦秒配单号版项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 邦秒爬
            'bm_crawler'        => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦秒爬项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦秒爬项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 信德
            'xinde'             => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '信德项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '信德项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            // 邦企查
            'bang'              => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦企查项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '邦企查项目经理2', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            //号码状态查询
            'tel_status'        => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台项目组发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 金融日报群组发送邮件的邮箱
                    'mail_from_name'                => '金融日报', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 金融日报群组收件人地址
                    'mail_receive_name'             => '号码状态查询项目经理1', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件的人
                ],
            ],
            //金盾 V2.0
            'golden_shield_new' => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '张柳', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
        ],
        // 提醒邮件配置
        'REMINDER_MAIL'  => [
            // 到期账号产品
            'customer_expire' => [
                'mail_from_address_self'             => '<EMAIL>', // 金融后台发送邮件的邮箱
                'mail_from_address_official'         => '<EMAIL>', // 日报群组发送邮件的邮箱
                'mail_from_name'                     => '运营系统', // 发送邮件人名字
                'mail_receive_address_name_self'     => [
                    '<EMAIL>',
                ], // 金融后台群组收件人地址
                'mail_receive_address_name_official' => [

                ],
                'mail_reply'                         => '<EMAIL>',  // 处理邮件回复的人
            ],
        ],
    ],
    //是否允许增加历史的计费配置
    'ALLOW_HISTORY_FEE_CONFIG' => true,
    'TEST_CHANNEL_URL' => 'http://back-api.dianhua.cn/cuishouExpress/checkChannel',
    'ALLOW_INTERFACE_PRICE_TIME_CONFIG' => false,//接口价格配置等于true的时候打开时间判断,false的时候关闭关闭

    //合同文件域名
	// 'CONTRACT_API_DOMAIN' => 'http://fin-contract-weixiu.dianhua.cn',
    'CONTRACT_API_DOMAIN' => 'http://fin-contract.dianhua.cn',


    // 财务流水数据地址
    'ACCOUNTANT_API_DOMAIN' => 'http://**************:18089',


    'FEI_SHU'   => [
        'url_prefix' => 'https://open.feishu.cn/open-apis',

        'key_tenant_access_token' => 'bmp_feishu::tenant_access_token',
        'key_app_access_token'    => 'bmp_feishu::app_access_token',
        'key_jsapi_ticket'        => 'bmp_feishu::jsapi_ticket',
        'key_user_info'           => 'bmp_feishu::user_info',


        //电话邦监控 应用
        'feishu_monitoring' => [
            'app_id'     => 'cli_a425066d24a9900b',
            'app_secret' => 'hisY1wURpMAG70c0vorLXKOCos4ZeN67',
        ],
    ],

    // 飞书用redis
    'REDIS_FEI_SHU'    => [
        'host' => '*************',
        'port' => '6379'
    ],
];
