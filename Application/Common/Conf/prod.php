<?php
return [
    // 数据配置根据情况修改
    'DB_TYPE'              => 'mysql',
    'DB_HOST'              => '************',
    'DB_PORT'              => '6012',
    'DB_NAME'              => 'crs-api',
    'DB_USER'              => 'crs-api',
    'DB_PWD'               => 'YulorecomCRS',
    'DB_PREFIX'            => 'crs_',
    'SITE_URL'             => 'https://finance-manage.dianhua.cn/',


    //yulore备用服务器
    'yulore_spare'         => 'http://*************:8080',

    //邦秒爬mongo库
    'MONGO'                => [
        'DB_TYPE' => 'mongo',
        //        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST' => 'sz52-208-mongodb-master01.dianhua.cn', // 数据库服务器地址
        'DB_PORT' => '27017',
        'DB_NAME' => 'crs',                     // 数据库名称
        'DB_USER' => 'crs_r',              // 数据库用户名
        'DB_PWD'  => 'c8kPmcJxQ',             // 数据库密码
        //        'DB_RW_SEPARATE' => true,                //读写分离
    ],

    // 微信预警redsi
    'REDIS_WECHAT_WARNING' => [
        'host'       => '*************',
        'port'       => '6379',
        'persistent' => true,
    ],

    // 邮件队列redis
    'REDIS_EMAIL_QUEUE'    => [
        'host' => '*************',
        'port' => '6379',
    ],

    //密码重置mongo临时配置
    'PWD_MONGO'            => [
        'DB_TYPE' => 'mongo',
        //        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST' => 'sz52-208-mongodb-master01.dianhua.cn', // 数据库服务器地址
        'DB_PORT' => '27017',
        'DB_NAME' => 'pwd',                     // 数据库名称
        'DB_USER' => 'pwd',              // 数据库用户名
        'DB_PWD'  => 'Vb$nLy#cJb',             // 数据库密码
        //        'DB_RW_SEPARATE' => true,                //读写分离
    ],

    'BMY_DB' => [
        'DB_TYPE'        => 'mysql',
        'DB_DEPLOY_TYPE' => 0,                 //采用分布式数据库支持
        'DB_HOST'        => '*************', // 数据库服务器地址
        'DB_PORT'        => '3306',
        'DB_NAME'        => 'alarm',                     // 数据库名称
        'DB_USER'        => 'dev_lc',              // 数据库用户名
        'DB_PWD'         => 'yulore',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
        'DB_CHARSET'     => 'utf8',
        'DB_MASTER_NUM'  => 1,
    ],

    'MYSQL_CRSAPI'             => [
        'DB_TYPE'   => 'mysql',
        'DB_USER'   => 'finance_backend',
        'DB_PREFIX' => '',
        'DB_PWD'    => 'Yulore@2018Qaz',
        'DB_HOST'   => '************',
        'DB_PORT'   => '6021',
        'DB_NAME'   => 'itag_apis',

    ],
    'DUNNING_MONGO'            => [
        'DB_TYPE'        => 'mongo',
        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST'        => '*************,*************', // 数据库服务器地址
        'DB_NAME'        => 'admin',                     // 数据库名称
        'DB_USER'        => 'admin,admin',              // 数据库用户名
        'DB_PWD'         => 'yulorei7,yulorei7',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
    ],

    //风险名单mongo库
    'RISK_LIST_MONGO'          => [
        'DB_TYPE'        => 'mongo',
        'DB_DEPLOY_TYPE' => 1,                 //采用分布式数据库支持
        'DB_HOST'        => '*************,*************', // 数据库服务器地址
        'DB_NAME'        => 'blacklist',                     // 数据库名称
        'DB_USER'        => 'blacklist,blacklist',              // 数据库用户名
        'DB_PWD'         => 'pNlZmi8c0B,pNlZmi8c0B',             // 数据库密码
        'DB_RW_SEPARATE' => true,                //读写分离
    ],

    //切换通道分支的配置
    'CRS_API_CONFIG'           => [
        'domain'        => 'http://crs-api.dianhua.cn:8080',
        'getToken'      => 'http://crs-api.dianhua.cn:8080/token',
        'channelSwitch' => 'http://crs-api.dianhua.cn:8080/calls/channel',
        'getChannel'    => 'http://crs-api.dianhua.cn:8080/calls/channel/detail',
        'manage'        => 'http://crs-manage.dianhua.cn',
    ],

    // 电话标签
    'MYSQL_CONFIG_ITAG_OLD'    => [
        'DB_TYPE'   => 'mysql',
        'DB_USER'   => 'bangmiaopei',
        'DB_PREFIX' => '',
        'DB_PWD'    => 'Yulore.com@2017',
        'DB_HOST'   => '************',
        'DB_PORT'   => '3306',
        'DB_NAME'   => 'yule',
    ],

    // 缓存邦秒配单号版账户
    'MEMCACHE_CONFIG_ITAG'     => [
        ['host' => '*************', 'port' => '11211'],
        ['host' => '*************', 'port' => '11211'],
        ['host' => '*************', 'port' => '11211'],
        ['host' => '*************', 'port' => '11211'],
        ['host' => '*************', 'port' => '11211'],
        ['host' => '*************', 'port' => '11211'],
    ],

    // 邦秒爬账户信息存储在redis中的配置
    'REDIS_CRAWLER'            => [
        'type' => 'redis',
        'host' => '*************',
        'port' => '19000',
    ],

    // 通道存在redis中的信息
    'REDIS_CHANNEL_CONFIG'     => [
        'type' => 'redis',
        'host' => '*************',
        'port' => '19000',
    ],

    // 协议预览地址
    'PREVIEW_URL'              => 'https://crs-ui.dianhua.cn/calls/preview/token/aa5ee53d193af5e07c6cb51709837999.html',

    // 客户体系数据库
    'DB_FINANCE'               => [
        'DB_TYPE'   => 'mysql',
        'DB_HOST'   => '************',
        'DB_PORT'   => '6012',
        'DB_NAME'   => 'yulore_finance',
        'DB_USER'   => 'finance_yulore',
        'DB_PWD'    => 'finance20180510',
        'DB_PREFIX' => '',
    ],

    'DB_MODIAN'               => [
		'DB_TYPE'   => 'mysql',
		'DB_HOST'   => '*************',
		'DB_PORT'   => '3316',
		'DB_NAME'   => 'modian_monitor',
		'DB_USER'   => 'prod_mdrw',
		'DB_PWD'    => 'p3DN0ohb45PacCaI',
		'DB_PREFIX' => '',
	],

    // 邦企业查数据库
    'DB_BANG'                  => [
        'DB_TYPE'   => 'mysql',
        'DB_HOST'   => '************',
        'DB_PORT'   => '6023',
        'DB_NAME'   => 'bang',
        'DB_USER'   => 'bang',
        'DB_PWD'    => 'Yulore.com@2018',
        'DB_PREFIX' => '',
    ],

    // 报警程序数据库
    'DB_ALARM'                 => [
        'DB_TYPE'   => 'mysql',
        'DB_HOST'   => '*************',
        'DB_PORT'   => '3306',
        'DB_NAME'   => 'alarm',
        'DB_USER'   => 'alarm',
        'DB_PWD'    => 'yulore@alarm2021',
        'DB_PREFIX' => '',
    ],

    //back-api域名地址
    'BACK_API_DOMAIN'          => 'https://back-api.dianhua.cn',

	//finance-manager-api域名地址
	'FINANCE_MANAGE_API_DOMAIN' => 'https://finance-manage-api.dianhua.cn',
	'FINANCE_STAT_API_DOMAIN' => 'https://fin-stat-api.dianhua.cn',
    //前端页面地址
	'FINANCE_STAT_WEB_DOMAIN' => 'https://fin-stat.dianhua.cn',

    //邦企查配置
    'BANG_API_CONFIG'          => [
        'domain' => 'http://bqc.dianhua.cn',
    ],
    //邦秒验配置
    'DATA_CHECK_CONFIG'        => [
        'domain' => 'http://private-mgmt.dianhua.cn',
    ],
    //号码状态查询配置
    'TEL_STATUS_CONFIG'        => [
        'domain' => 'http://pns.dianhua.cn',
    ],
    // API列表
    'LIST_API_URL'             => [
        // 邦企查接口的地址
        'bang_api_key'                                 => 'DIANHUABANGBANGQICHA',
        'bang_api_secret'                              => 'BANGQICHADIANHUABANG',
        'bang_stat_list'                               => 'https://bqc.dianhua.cn/api/stat/client_list',
        'bang_stat_detail'                             => 'https://bqc.dianhua.cn/api/stat/client_detail',

        // 邦企查限额
        'bang_limit_num'                               => 'https://bqc.dianhua.cn/api/limitation/index',

        // 灰度上线写入的API && 私钥
        'gray_upgrade_write'                           => 'http://crs-api.dianhua.cn:8080/gray/rules/write',
        'private_key'                                  => 'y~u!l@o#r$e%^prod',

        // 陌电查询帮秒配统计量
        'matching_modian_stat'                         => 'http://apis-yulore.dianhua.cn/stat/pv/?app=stat&ver=1.0&v=1&operator=shen.liu&apikey=yuloreEq8frxObVfeQLtfvzgkwFbBNaZ',

        // 邦秒爬日报
        'daily_report_crawler'                         => 'http://crs-api.dianhua.cn:8080/admin/crawler/daily',

        // 爬虫渠道日报
        'daily_report_crawler_channel'                 => 'http://crs-api.dianhua.cn:8080/admin/crawler/report',

        // 日报的 api_key api_secret
        'daily_report_api_key'                         => '0aa2cb33d0eaedc4abe412348045dc8',
        'daily_report_api_secret'                      => '0b178ed2d1d049e46472711d8f92bf4',

        // 催收分私有云日报API
        'cuishou_private_report'                       => 'http://private-mgmt.dianhua.cn/priv_stat/daily',

        // 邦信分私有云统计列表API
        'cuishou_private_list'                         => 'http://private-mgmt.dianhua.cn/priv_stat/lists',

        // 邦信分私有云统计详情API
        'cuishou_private_detail'                       => 'http://private-mgmt.dianhua.cn/priv_stat/detail',

        // 邦信分快捷版日报API
        'cuishou_short_report'                         => 'http://private-mgmt.dianhua.cn/cucc_daily/daily',

        // 邦秒验slug
        'data_validate_slug'                           => 'http://private-mgmt.dianhua.cn/opdata_product/lists',

        // 邦秒验产品日报
        'data_validate_report'                         => 'http://private-mgmt.dianhua.cn/opdata_daily/daily',

        // 邦信分快捷版API
        'cuishou_short_stat_list'                      => 'http://private-mgmt.dianhua.cn/cucc_daily/stat',

        // 邦信分快捷版详情统计
        'cuishou_short_stat_detail'                    => 'http://private-mgmt.dianhua.cn/cucc_daily/detail',

        // 邦信分快捷版slug列表
        'cuishou_short_slug_list'                      => 'http://private-mgmt.dianhua.cn/cucc_fields/lists',

        //运营商报告数据接口地址
        'operator_report_list'                         => 'http://bmpreport.dianhua.cn',

        //统一的统计配置接口地址，xiaogang.cui 10:21 2019/1/4
        'stat_list'                                    => 'https://back-api.dianhua.cn/statistic/list',
        'stat_detail'                                  => 'https://back-api.dianhua.cn/statistic/detail',
        'stat_day'                                     => 'https://back-api.dianhua.cn/statistic/day',
        'stat_report'                                  => 'https://back-api.dianhua.cn/statistic/report',

        //邦秒爬统计地址
        'report_stat_list'                             => 'http://crs-api.dianhua.cn:8080/admin/report/list',
        'report_stat_detail'                           => 'http://crs-api.dianhua.cn:8080/admin/report/detail',
        'report_stat_day'                              => '',
        //暂不支持按天导出
        'report_stat_report'                           => 'http://crs-api.dianhua.cn:8080/admin/report/day',

        //邦秒爬推送日志接口 xiaogang.cui 14:21 2019/3/8
        'bmpa_log_list'                                => 'http://crs-manage.dianhua.cn/push/list',
        //列表地址
        'bmpa_log_detail'                              => 'http://crs-manage.dianhua.cn/push/detail',
        //详细地址
        'bmpa_log_send'                                => 'http://crs-manage.dianhua.cn/push/send',
        //重推地址

        // 清除backend-api 产品用量检测过程中产生的缓存
        'backend_api_clear_flag'                       => 'https://back-api.dianhua.cn/product/flag',

        // 清理backend-api鉴权产生的缓存
        'backend_api_clear_auth'                       => 'https://back-api.dianhua.cn/auth/fresh',

        //邦秒配单号版单号版统计数据推送地址（用于陌电数据的推送）
        'stat_bm_pei_modian'                           => 'https://back-api.dianhua.cn/statistic/sendStatisInfo',

        // backend-api产品调用量查询接口
        'backend_api_amount'                           => 'https://back-api.dianhua.cn/v1/statistic/amount',

        // 邮件发送对账单的结算单接口
        'backend_api_history'                          => 'https://back-api.dianhua.cn/v2/bill/history/customer/',

        // 邮件发送对账单的消费明细接口
        'backend_api_email_bill'                       => 'https://back-api.dianhua.cn/v2/bill/excel/customer/',

        // 邮件发送对账单
        'backend_api_email'                            => 'https://back-api.dianhua.cn/v2/bill/email',

        // 客户账单列表
        'backend_api_bill_customer'                    => 'https://back-api.dianhua.cn/v2/bill/customers',

        // 客户产品账单列表
        'backend_customer_product_bill'                => 'https://back-api.dianhua.cn/v2/bill/customer/product',

        // 产品对账单
        'backend_product_bill'                         => 'https://back-api.dianhua.cn/v2/bill/product',

        // 客户账单列表
        //'backend_api_bill_customer'                    => 'https://back-api.dianhua.cn/v1/bill/customers',

        // 排序接口
        'backend_api_sorts'                            => 'https://back-api.dianhua.cn/v1/bill/sorts',

        // 账单excel下载接口
        'backend_history_download'                     => 'https://back-api.dianhua.cn/v1/bill/history/excel',

        // 客户产品账单列表
        // 'backend_customer_product_bill'                => 'https://back-api.dianhua.cn/v1/bill/customer/product',

        // 下载客户产品excel
        'backend_customer_product_download'            => 'https://back-api.dianhua.cn/v1/bill/customer/product/excel',

        // 产品对账单
        //'backend_product_bill'                         => 'https://back-api.dianhua.cn/v1/bill/product',

        // 产品对账单excel
        'backend_product_bill_download'                => 'https://back-api.dianhua.cn/v1/bill/product/download',

        // 产品客户账单列表
        'backend_product_customer_bill'                => 'https://back-api.dianhua.cn/v1/bill/product/customer',

        //  产品客户账单列表生成excel
        'backend_product_customer_bill_excel'          => 'https://back-api.dianhua.cn/v1/bill/product/customer/excel',

        // 产品各月的消费金额
        'backend_product_month_bill'                   => 'https://back-api.dianhua.cn/v1/bill/product/month',

        // 产品各月的消费金额 excel
        'backend_product_month_bill_excel'             => 'https://back-api.dianhua.cn/v1/bill/product/month/download',

        // 客户产品月对账单
        'backend_customer_product_month_bill'          => 'https://back-api.dianhua.cn/v1/bill/customer/month',

        // 客户产品月对账单excel
        'backend_customer_month_bill_download'         => 'https://back-api.dianhua.cn/v1/bill/customer/month/download',

        //  日账单
        'backend_day_bill'                             => 'https://back-api.dianhua.cn/v1/bill/day',

        // 日账单生成excel
        'back_day_bill_download'                       => 'https://back-api.dianhua.cn/v1/bill/day/excel',

        //打款单认款时需要对余额预警的标记进行清理，清理地址
        'backend_clean_balance_sign'                   => 'https://back-api.dianhua.cn/v1/bill/customer/warning/{customer_id}',

        // back-api日志
        'backend_api_log'                              => 'https://back-api.dianhua.cn/v1/log',

        // bill_months 查询
        'back_api_bill_month'                          => 'https://back-api.dianhua.cn/v1/bill/list',

        //xiaogang.cui 15:00 2019/6/13 0013 添加
        //获取客户的调用量、消费金额数据（批量）
        'customer_consume'                             => 'http://back-api.dianhua.cn/v1/bill/customer/money',
        //获取每个计费配置的调用量、消费金额数据（批量）
        'fee_config_consume'                           => 'http://back-api.dianhua.cn/v1/bill/section',

        // 邦信分快捷版上游数据通计列表
        'back_api_upstream_shortcuts'                  => 'https://back-api.dianhua.cn/v1/upstream/shortcuts',

        // 邦信分快捷版上游数据通详情
        'back_api_upstream_shortcuts_details'          => 'https://back-api.dianhua.cn/v1/upstream/shortcuts/',

        // 邦信分快捷版上游数据通计列表下载
        'back_api_upstream_shortcuts_download'         => 'https://back-api.dianhua.cn/v1/upstream/shortcuts/excel',

        // 邦信分快捷版上游数据通计列表按照天下载
        'back_api_upstream_shortcuts_day_download'     => 'https://back-api.dianhua.cn/v1/upstream/shortcuts/day/excel',

        // 邦信分快捷版上游数据通详情excel
        'back_api_upstream_shortcuts_details_excel'    => 'https://back-api.dianhua.cn/v1/upstream/shortcuts/details/excel',

        // 上游邦妙验统计列表
        'back_api_upstream_verification'               => 'https://back-api.dianhua.cn/v1/upstream/verification',

        // 上游邦妙验统计列表下载
        'back_api_upstream_verification_excel'         => 'https://back-api.dianhua.cn/v1/upstream/verification/excel',

        // 上游邦妙验统计列表按天下载
        'back_api_upstream_verification_day_excel'     => 'https://back-api.dianhua.cn/v1/upstream/verification/day/excel',

        // 上游邦妙验统计详情
        'back_api_upstream_verification_details'       => 'https://back-api.dianhua.cn/v1/upstream/verification/',
        // {product_id}

        // 上游邦妙验统计详情下载
        'back_api_upstream_verification_details_excel' => 'https://back-api.dianhua.cn/v1/upstream/verification/details/excel',

        // 校正账单用量 create post
        'back_api_correct_numbers_create'              => 'https://back-api.dianhua.cn/v1/correct',

        //  校正账单用量列表 get
        'back_api_correct_numbers'                     => 'https://back-api.dianhua.cn/v1/correct',

        // 校正账单用户列表 get
        'back_api_correct_operators'                   => 'https://back-api.dianhua.cn/v1/correct/operators',

        // 校正列表下载 post
        'back_api_correct_download'                    => 'https://back-api.dianhua.cn/v1/correct/excel',

        // 填写日志 post
        'back_api_correct_add_comment'                 => 'https://back-api.dianhua.cn/v1/correct/comment',

        // 备注列表 get correct/comment/{uuid}
        'back_api_correct_comments'                    => 'https://back-api.dianhua.cn/v1/correct/comment/',

        // 日账单列表
        'back_api_bill_day_list'                       => 'https://back-api.dianhua.cn/v1/bill/day/lists',

        // 日账单日志列表
        'back_api_bill_day_log'                        => 'https://back-api.dianhua.cn/v1/bill/day/log/lists',

        // 数据统计原始数据列表

        'backend_api_stat_source_list' => 'https://back-api.dianhua.cn/v1/log/statistic',

        // 配置管理-值分布
        'back_api_config_product_value_spread_list' => 'http://back-api.dianhua.cn/config/product/spread/spreadList',
        'back_api_config_product_value_spread_info' => 'http://back-api.dianhua.cn/config/product/spread/spreadInfo',
        'back_api_config_product_value_spread_edit' => 'http://back-api.dianhua.cn/config/product/spread/spreadEdit',
        'back_api_config_product_value_spread_add' => 'http://back-api.dianhua.cn/config/product/spread/spreadAdd',

        // 配置管理-渠道查得率阈值
        'back_api_config_channel_statis_list' => 'http://back-api.dianhua.cn/config/channel/statis/statisList',
        'back_api_config_channel_statis_info' => 'http://back-api.dianhua.cn/config/channel/statis/statisInfo',
        'back_api_config_channel_statis_edit' => 'http://back-api.dianhua.cn/config/channel/statis/statisEdit',
        'back_api_config_channel_statis_add' => 'http://back-api.dianhua.cn/config/channel/statis/statisAdd',

        // 统计字段入库日志
        'backend_api_stat_log'         => 'https://back-api.dianhua.cn/v1/log/progress',

        // 爬虫电话
        'backend_api_crawler_tel'      => 'https://back-api.dianhua.cn/v1/crawler/tels',

        //邦信分快捷版监控接口
        #评分类
        'back_api_monitor_score'       => 'http://back-api.dianhua.cn/monitor/bxfshort/score',
        #统计类
        'back_api_monitor_statis'      => 'http://back-api.dianhua.cn/monitor/bxfshort/statis',
        #产品值分布
        'back_api_monitor_value_spread' => 'http://back-api.dianhua.cn/monitor/bxfshort/valueSpread',
        #产品查得率
        'back_api_monitor_success_ratio' => 'http://back-api.dianhua.cn/monitor/bxfshort/successRatio',
        #渠道查得率
        'back_api_monitor_channel_statis' => 'http://back-api.dianhua.cn/monitor/bxfshort/channelStatis',

        //数据源V3相关接口
        'upstream_statistics_list'     => 'http://back-api.dianhua.cn/statistics/upstream/list', //统计量列表接口
        'upstream_bill_list'           => 'http://back-api.dianhua.cn/bill/upstream/list',       //账单列表接口

        //余额账单接口
        'profit_customer'              => 'http://back-api.dianhua.cn/v2/bill/customer_profit',
        'profit_product'               => 'http://back-api.dianhua.cn/v2/bill/product_profit',
    ],
    'MAIL'                     => [
        // 日报群组设置
        'REPORT_DAILY'   => [
            'host'                          => 'smtp.feishu.cn',
            // Specify main and backup SMTP servers
            'username'                      => '<EMAIL>',
            // SMTP username
            'password'                      => 'NNCxXgfxjx5l3DEZ',
            // SMTP password
            'smtp_secure'                   => 'ssl',
            // Enable TLS encryption, `ssl` also accepted
            'port'                          => 465,
            // TCP port to connect to
            'mail_from_address_self'        => '<EMAIL>',
            // 金融后台发送邮件的邮箱
            'mail_from_address_official'    => '<EMAIL>',
            // 日报群组发送邮件的邮箱
            'mail_from_name'                => '运营系统',
            // 发送邮件人名字
            'mail_receive_address_self'     => '<EMAIL>',
            // 金融后台群组收件人地址
            'mail_receive_address_official' => '<EMAIL>',
            // 日报群组收件人地址
            'mail_receive_name'             => '金融日报邮件组',
            // 收件人名字
            'mail_reply'                    => '<EMAIL>',
            // 处理邮件回复的人
        ],
        // 日报之项目经理设置
        'REPORT_PROJECT' => [
            // 爬虫渠道日报
            'crawler_channel'   => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李晏铭', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 邦秒验
            'data_validate'     => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李晏铭', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '张波飞', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],

            // 催收分析快快捷版
            'cuishou_short'     => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],

            // 邦信分私有云
            'cuishou_private'   => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '樊魏', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李毅', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 邦信分详单版V1
            'cuishou'           => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => 'linying.jin', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 邦信分详单版V2
            'cuishou_v2'        => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => 'linying.jin', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 邦秒配单号版
            'bm_matching'       => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 邦秒爬
            'bm_crawler'        => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李晏铭', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 信德
            'xinde'             => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李晏铭', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            // 邦企查
            'bang'              => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李晏铭', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            //号码状态查询
            'tel_status'        => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李晏铭', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            //金盾相关产品
            'golden_shield'     => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '张柳', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '李丹丹', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
            //金盾 V2.0
            'golden_shield_new' => [
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '张柳', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
                [
                    'mail_from_address_self'        => '<EMAIL>', // 金融后台发送邮件的邮箱
                    'mail_from_address_official'    => '<EMAIL>', // 日报群组发送邮件的邮箱
                    'mail_from_name'                => '运营系统', // 发送邮件人名字
                    'mail_receive_address_self'     => '<EMAIL>', // 金融后台群组收件人地址
                    'mail_receive_address_official' => '<EMAIL>', // 日报群组收件人地址
                    'mail_receive_name'             => '杨成圣', // 收件人名字
                    'mail_reply'                    => '<EMAIL>',  // 处理邮件回复的人
                ],
            ],
        ],
        // 提醒邮件配置
        'REMINDER_MAIL'  => [
            // 到期账号产品
            'customer_expire' => [
                'mail_from_address_self'             => '<EMAIL>', // 金融后台发送邮件的邮箱
                'mail_from_address_official'         => '<EMAIL>', // 日报群组发送邮件的邮箱
                'mail_from_name'                     => '运营系统', // 发送邮件人名字
                'mail_receive_address_name_self'     => [
                    '<EMAIL>',
                ], // 金融后台群组收件人地址
                'mail_receive_address_name_official' => [

                ],
                'mail_reply'                         => '<EMAIL>',  // 处理邮件回复的人
            ],
        ],
    ],
    //是否允许增加历史的计费配置
    'ALLOW_HISTORY_FEE_CONFIG' => true,
    'TEST_CHANNEL_URL' => 'http://back-api.dianhua.cn/cuishouExpress/checkChannel',
    'ALLOW_INTERFACE_PRICE_TIME_CONFIG' => false,//接口价格配置等于true的时候打开时间判断,false的时候关闭关闭

    //合同文件域名
	'CONTRACT_API_DOMAIN' => 'https://fin-contract.dianhua.cn',

    // 财务流水数据地址
    'ACCOUNTANT_API_DOMAIN' => 'http://*************:18089',

    'FEI_SHU'   => [
        'url_prefix' => 'https://open.feishu.cn/open-apis',

        'key_tenant_access_token' => 'bmp_feishu::tenant_access_token',
        'key_app_access_token'    => 'bmp_feishu::app_access_token',
        'key_jsapi_ticket'        => 'bmp_feishu::jsapi_ticket',
        'key_user_info'           => 'bmp_feishu::user_info',


        //电话邦监控 应用
        'feishu_monitoring' => [
            'app_id'     => 'cli_a425066d24a9900b',
            'app_secret' => 'hisY1wURpMAG70c0vorLXKOCos4ZeN67',
        ],
    ],

    // 飞书用redis
    'REDIS_FEI_SHU'    => [
        'host' => '127.0.0.1',
        'port' => '6379'
    ],


    'PRETEST_DOMAIN' => 'http://*************:8085',

    'COMPANY_CN_NAME' => '羽乐科技',
];
