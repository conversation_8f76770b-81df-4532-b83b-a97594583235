<?php

namespace Common\Common;

use Common\Model\WechatWarningModel;

trait WechatBackendExceptionTrait
{
    use WechatWarningReportQueueTrait;

    /*
     * 后台异常微信配置slug
     * */
    private $wechat_slug = 'wechat.backend.exception';

    /**
     * 将后天异常记录日志， 然后微信预警
     * @param string $msg_exception
     * @throws \Exception
     */
    public function wehcatException($msg_exception)
    {
        // 记录日志
        $this->logException($msg_exception);

        // 微信预警
        $this->wechat($msg_exception);
    }

    /**
     * 微信预警
     * @param string $msg_exception
     * @throws \Exception
     */
    private function wechat($msg_exception)
    {
        // 获取异常微信配置信息
        $queue_info = $this->getWechatMessage($msg_exception);

        // 入微信队列
        $this->wechatQueue($queue_info);
    }

    /**
     * 获取组合的队列信息
     * @param $msg
     * @return array
     */
    private function getWechatMessage($msg)
    {
        $wechat_config = $this->getOneWechatConfig(['slug' => $this->wechat_slug]);
        $wechat_config = arrOnly($wechat_config, ['corp_id', 'agent_id', 'secret', 'itag_id']);
        $wechat_config['msg'] = $msg;
        return $wechat_config;
    }


    /**
     * 获取微信异常配置
     * @param array $where
     * @return array
     */
    private function getOneWechatConfig(array $where)
    {
        return (new WechatWarningModel())->where($where)
            ->find();
    }

    /**
     * 记录日志
     * @param string $msg_exception
     * @throws \Exception
     */
    private function logException($msg_exception)
    {
        $log_info = [
            'handle_type' => 'report',
            'description' => $msg_exception,
            'content' => $msg_exception,
            'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: 'system',
            'handle_result' => 0
        ];

        HandlerLog::log($log_info);
    }
}
