<?php


if (!function_exists('validateUrl')) {
	/**
	 * 校验url是否合法
	 *
	 * @param string $url
	 *
	 * @return bool
	 */
	function validateUrl($url)
	{
		if (!is_string($url)) {
			return false;
		}
		
		$parse_host = parse_url($url, PHP_URL_HOST);
		
		// 域名白名单
		$list_white_hosts = [
			'www.dc_huisu.lemoncome.com',
			'dc_huisu.lemoncome.com',
		];
		
		// 命中白名单
		if (in_array($parse_host, $list_white_hosts)) {
			return true;
		}
		
		return (bool)filter_var($url, FILTER_VALIDATE_URL);
	}
}


if (!function_exists('defaultValue')) {
	/**
	 * 大海捞针
	 *
	 * @param array  $haystack
	 * @param string $needle
	 * @param string $default
	 * @param null   $filter
	 *
	 * @return mixed|string
	 */
	function defaultValue(array $haystack, $needle, $default = '', $filter = null)
	{
		$value_search = array_key_exists($needle, $haystack) ? $haystack[$needle] : $default;
		if ($filter !== null) {
			return call_user_func($filter, $value_search);
		}
		
		return $value_search;
	}
}


if (!function_exists('genAccountId')) {
	/**
	 * 生成账号或者客户ID
	 *
	 * @param string $pre_type
	 * @param int    $random_len
	 *
	 * @return string
	 * @throws Exception
	 */
	function genAccountId($pre_type, $random_len = 6)
	{
		// C客户 TA测试账号 FA 正式账号
		if (!in_array($pre_type, ['C', 'FA', 'TA'])) {
			throw new \Exception('传入的类型错误, 请检查后重试');
		}
		
		// 固定的前缀
		$date   = date('Ymd');
		$output = $pre_type . $date;
		
		// 后6位随机
		$b        = range('A', 'Z');
		$c        = range('0', '9');
		$chars    = array_merge($b, $c);
		$charslen = count($chars) - 1;
		shuffle($chars);
		
		for ($i = 0; $i < $random_len; $i++) {
			$output .= $chars[mt_rand(0, $charslen)];
		}
		
		return $output;
	}
}


// 获取数据的子集
if (!function_exists('mockMysqlEncrypt')) {
    function mockMysqlEncrypt($data) {
        $encrypted = @openssl_encrypt($data, 'aes-128-cbc', ']?@Fl;qj?a!j,E${', OPENSSL_RAW_DATA,'');
        $hexEncoded = bin2hex($encrypted);

        return strtolower($hexEncoded);
    }
}

if (!function_exists('mockMysqlDecrypt')) {
    function mockMysqlDecrypt($data) {
        $data = hex2bin($data);
        $decrypted = @openssl_decrypt($data, 'aes-128-cbc', ']?@Fl;qj?a!j,E${', OPENSSL_RAW_DATA,'');

        return strtolower($decrypted);
    }
}


// 获取数据的子集
if (!function_exists('arrOnly')) {
	function arrOnly(array $arr, array $keys)
	{
		return array_intersect_key($arr, array_flip($keys));
	}
}

// 判断变量是否是合法的json
if (!function_exists('json_validate')) {
	function json_validate($string)
	{
		if (is_string($string)) {
			@json_decode($string);
			
			return (json_last_error() === JSON_ERROR_NONE);
		}
		
		return false;
	}
}


if (!function_exists('genCrawlerToken')) {
	/**
	 * 为邦秒爬生成token
	 * @return string
	 */
	function genCrawlerToken()
	{
		return md5(mt_rand(0, 9999999) . uniqid() . mt_rand(0, 9999999));
	}
}

/**
 * 返回经addslashes处理过的字符串或数组
 *
 * @param $string 需要处理的字符串或数组
 *
 * @return mixed
 */
function newAddslashes($string)
{
	if (!is_array($string)) {
		return addslashes($string);
	}
	
	foreach ($string as $key => $val) {
		$string[$key] = newAddslashes($val);
	}
	
	return $string;
}

/**
 * 返回经stripslashes处理过的字符串或数组
 *
 * @param $string 需要处理的字符串或数组
 *
 * @return mixed
 */
function newStripslashes($string)
{
	if (!is_array($string)) {
		return stripslashes($string);
	}
	
	foreach ($string as $key => $val) {
		$string[$key] = newStripslashes($val);
	}
	
	return $string;
}

/**
 * 返回经htmlspecialchars处理过的字符串或数组
 *
 * @param $obj 需要处理的字符串或数组
 *
 * @return mixed
 */
function newHtmlspecialchars($string)
{
	if (!is_array($string)) {
		return htmlspecialchars($string);
	}
	
	foreach ($string as $key => $val) {
		$string[$key] = newHtmlspecialchars($val);
	}
	
	return $string;
}

/**
 * 将字符串转换为数组
 *
 * @param    string $data 字符串
 *
 * @return   array   返回数组格式，如果，data为空，则返回空数组
 */
function string2array($data)
{
	if ($data == '') {
		return [];
	}
	
	@eval("\$array = $data;");
	
	return $array;
}

/**
 * 将数组转换为字符串
 *
 * @param    array $data       数组
 * @param    bool  $isformdata 如果为0，则不使用newStripslashes处理，可选参数，默认为1
 *
 * @return   string  返回字符串，如果，data为空，则返回空
 */
function array2string($data, $isformdata = 1)
{
	if ($data == '') {
		return '';
	}
	
	if ($isformdata) {
		$data = newStripslashes($data);
	}
	
	return addslashes(var_export($data, true));
}

if (!function_exists('array_column')) {
	/**
	 * 返回二维数据中指定列
	 * 使用方法 http://php.net/manual/zh/function.array-column.php
	 * 该函数在php5.5中已默认实现
	 */
	function array_column($input, $columnKey, $indexKey = null)
	{
		$columnKeyIsNumber = (is_numeric($columnKey)) ? true : false;
		$indexKeyIsNull    = (is_null($indexKey)) ? true : false;
		$indexKeyIsNumber  = (is_numeric($indexKey)) ? true : false;
		$result            = [];
		foreach ((array)$input as $key => $row) {
			if ($columnKeyIsNumber) {
				$tmp = array_slice($row, $columnKey, 1);
				$tmp = (is_array($tmp) && !empty($tmp)) ? current($tmp) : null;
			} else {
				$tmp = isset($row[$columnKey]) ? $row[$columnKey] : null;
			}
			if (!$indexKeyIsNull) {
				if ($indexKeyIsNumber) {
					$key = array_slice($row, $indexKey, 1);
					$key = (is_array($key) && !empty($key)) ? current($key) : null;
					$key = is_null($key) ? 0 : $key;
				} else {
					$key = isset($row[$indexKey]) ? $row[$indexKey] : 0;
				}
			}
			$result[$key] = $tmp;
		}
		
		return $result;
	}
}

/**
 * 字符截取
 * 支持中文截取
 *
 * @param   string $string 原字符
 * @param   int    $length 截取长度
 * @param   string $dot    尾部字符
 *
 * @return
 */
function str_cut($string, $length, $dot = '')
{
	$strlen = strlen($string);
	if ($strlen <= $length) {
		return $string;
	}
	
	$string = str_replace([
		' ',
		'&nbsp;',
		'&amp;',
		'&quot;',
		'&#039;',
		'&ldquo;',
		'&rdquo;',
		'&mdash;',
		'&lt;',
		'&gt;',
		'&middot;',
		'&hellip;',
	], ['∵', ' ', '&', '"', "'", '“', '”', '—', '<', '>', '·', '…'], $string);
	$strcut = '';
	$length = intval($length - strlen($dot) - $length / 3);
	$n      = $tn = $noc = 0;
	while ($n < strlen($string)) {
		$t = ord($string[$n]);
		if ($t == 9 || $t == 10 || (32 <= $t && $t <= 126)) {
			$tn = 1;
			$n++;
			$noc++;
		} else if (194 <= $t && $t <= 223) {
			$tn  = 2;
			$n   += 2;
			$noc += 2;
		} else if (224 <= $t && $t <= 239) {
			$tn  = 3;
			$n   += 3;
			$noc += 2;
		} else if (240 <= $t && $t <= 247) {
			$tn  = 4;
			$n   += 4;
			$noc += 2;
		} else if (248 <= $t && $t <= 251) {
			$tn  = 5;
			$n   += 5;
			$noc += 2;
		} else if ($t == 252 || $t == 253) {
			$tn  = 6;
			$n   += 6;
			$noc += 2;
		} else {
			$n++;
		}
		if ($noc >= $length) {
			break;
		}
	}
	if ($noc > $length) {
		$n -= $tn;
	}
	$strcut = substr($string, 0, $n);
	$strcut = str_replace(['∵', '&', '"', "'", '“', '”', '—', '<', '>', '·', '…'], [
		' ',
		'&amp;',
		'&quot;',
		'&#039;',
		'&ldquo;',
		'&rdquo;',
		'&mdash;',
		'&lt;',
		'&gt;',
		'&middot;',
		'&hellip;',
	], $strcut);
	
	return $strcut . $dot;
}

/**
 * 获取请求ip
 */
function ip()
{
	// if (getenv('HTTP_CLIENT_IP') && strcasecmp(getenv('HTTP_CLIENT_IP'), 'unknown')) {
	//     $ip = getenv('HTTP_CLIENT_IP');
	// } elseif (getenv('HTTP_X_FORWARDED_FOR') && strcasecmp(getenv('HTTP_X_FORWARDED_FOR'), 'unknown')) {
	//     $ip = getenv('HTTP_X_FORWARDED_FOR');
	// } elseif (getenv('REMOTE_ADDR') && strcasecmp(getenv('REMOTE_ADDR'), 'unknown')) {
	//     $ip = getenv('REMOTE_ADDR');
	// } elseif (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], 'unknown')) {
	//     $ip = $_SERVER['REMOTE_ADDR'];
	// }
	return $_SERVER['REMOTE_ADDR'];
}

/**
 * 检查权限 根据 节点标示
 *
 * @param string $node
 */
function checkPerByNode($node)
{
	return D('SystemNode')->checkPerByNode($string);
}

/**
 * 检查权限  根据节点ID
 *
 * @param int $nodeid
 */
function checkPerById($nodeid)
{
	return D('SystemNode')->checkPerById($nodeid);
}

/**
 * 快速调试
 *
 * @param $data mixed 调试的数据
 *
 * @return void
 **/
function halt($data)
{
	ob_end_clean();
	header('Content-type:text/html; charset=utf-8');
	dump($data);
	die;
}

/**
 * 对过长的字符串进行换行操作（针对的是连续的英文字符+数字）
 *
 * @param $str    string 需要处理的字符串
 * @param $length integer 每行最大的字符数量
 *
 * @return string
 **/
function autoEnter($str, $length = 15)
{
	//存在中文字符将不会处理
	if (preg_match('/^[\x{4e00}-\x{9fa5}]+$/u', $str)) {
		return $str;
	}
	if (strlen($str) <= $length) {
		return $str;
	}
	
	return implode('<br/>', str_split($str, $length));
}

/**
 * 快速生成Option
 *
 * @param $data    array 数据
 * @param $default string 默认值
 * @param $extra   array 附加数据
 *
 * @return string
 **/
function makeOption($data, $default = null, $extra = [])
{
	$option = '';
	foreach ($data as $value => $show) {
		$checked = ($default == $value) ? 'selected' : '';
		$option  .= "<option value='{$value}' {$checked}>{$show}</option>";
	}
	
	return $option;
}

/**
 * 无限分级数据的处理
 *
 * @param $data               array 数据
 * @param $primary_key        string 主键名称
 * @param $foreign_key        string 外键名称
 * @param $first_value        string 首级数据的外键值
 * @param $children_data_name string 子级数据名称
 *
 * @return array
 **/
function getEndlessGradData($data, $primary_key = 'id', $foreign_key = 'father_id', $first_value = 0, $children_data_name = 'children_data')
{
	$result   = [];
	$new_data = [];
	foreach ($data as $item) {
		if ($item[$foreign_key] == $first_value) {
			$result[] = $item;
		} else {
			$new_data[] = $item;
		}
	}
	foreach ($result as &$item) {
		$item[$children_data_name] = getEndlessGradData($new_data, $primary_key, $foreign_key, $item[$primary_key], $children_data_name);
	}
	
	return $result;
}


function property_test()
{
	static $property_test;
	if ($_GET['property_test'] == 'property_test') {
		if (isset($property_test)) {
			$property_test++;
		} else {
			$property_test = 1;
		}
		
		list($microsecond, $second) = explode(' ', microtime());
		$millisecond = $second + $microsecond;
		echo "【{$property_test}】" . $millisecond . '<br/>';
	}
}

/**
 * 创建一个JSON响应
 *
 * <AUTHOR>
 * @datetime 2020/9/18 10:19
 *
 * @param $code integer 响应状态码，对应的是配置文件中的响应内容
 * @param $data array 响应数据
 *
 * @return void
 */
function createJsonResponse($code, $data = [])
{
	ob_clean();
	$response = require(APP_PATH . 'Common' . DIRECTORY_SEPARATOR . DIRECTORY_SEPARATOR . 'Conf' . DIRECTORY_SEPARATOR . 'response.php');
	header('Content-Type:application/json; charset=utf-8');
	$message = $response[$code] ? $response[$code] : '未知错误';
	echo json_encode(compact('code', 'message', 'data'), JSON_UNESCAPED_UNICODE);
	die;
}
