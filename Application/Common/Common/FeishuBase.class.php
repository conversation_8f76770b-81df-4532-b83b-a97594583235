<?php

namespace Common\Common;

use Exception;
use Think\Cache\Driver\Redis;
use Think\Log;

/**
 *
 *
 * 根据调用接口不同,需要使用不同的access_token 在调用请求是需要指定
 * access_token说明 https://open.feishu.cn/document/home/<USER>/access-credentials
 *
 * api文档地址 https://open.feishu.cn/document/server-docs/docs/docs/upgraded-docs-openapi-access-guide
 * 飞书api调试 https://open.feishu.cn/api-explorer
 *
 * 调用前请确定拥有相关权限
 */
class FeishuBase
{


// namespace App\Utils;
//
// use Exception;
// use Illuminate\Support\Facades\Redis;
// use Illuminate\Support\Facades\Log;

// class FeiShu
// {
    public  $app_id;
    private $app_secret;
    private $url_prefix;
    private $redis;

    const METHOD_GET  = "GET";
    const METHOD_POST = "POST";

    /** @var array|mixed|null 飞书配置 */
    private $fieshu_config;

    public function __construct() {
        $this->redis      = new Redis(C('REDIS_FEI_SHU'));

        $this->fieshu_config = C('FEI_SHU');

        $this->app_id     = $this->fieshu_config['feishu_monitoring']['app_id'];
        $this->app_secret = $this->fieshu_config['feishu_monitoring']['app_secret'];
        $this->url_prefix = $this->fieshu_config['url_prefix'];
    }




    // ----------------------------- no access_token

    /**
     * 获取 tenant_access_token
     *
     * @return string
     * @throws Exception
     * @see https://open.feishu.cn/document/server-docs/authentication-management/access-token/tenant_access_token_internal
     */
    private function get_tenant_access_token() {
        $tenant_access_token = $this->redis->get($this->fieshu_config['key_tenant_access_token']);

        if($tenant_access_token){
            return $tenant_access_token;
        }

        $data = [
            'app_id' => $this->app_id,
            'app_secret' => $this->app_secret,
        ];
        $url = '/auth/v3/tenant_access_token/internal';

        $res = $this->http_post($url,$data,'none');

        if($res['code'] == 0){
            //有效时间2小时 如果在有效期小于 30 分钟的情况下，调用本接口，会返回一个新的 tenant_access_token
            //2*60*60=7200
            $tenant_access_token = $res['tenant_access_token'];
            $this->redis->setex($this->fieshu_config['key_tenant_access_token'], 7000, $tenant_access_token);

            return $tenant_access_token;
        }else{
            throw new Exception($res['msg']);
        }
    }

    /**
     * @throws Exception
     */
    private function get_app_access_token() {
        $app_access_token = $this->redis->get($this->fieshu_config['key_app_access_token']);
        if($app_access_token){
            return $app_access_token;
        }

        $data = [
            'app_id'     => $this->app_id,
            'app_secret' => $this->app_secret,
        ];

        $url = '/auth/v3/app_access_token/internal';

        $res = $this->http_post($url, $data,'none');

        if($res['code'] == 0){
            //有效时间2小时 如果在有效期小于 30 分钟的情况下，调用本接口，会返回一个新的 tenant_access_token
            //2*60*60=7200
            $app_access_token = $res['app_access_token'];
            $this->redis->setex($this->fieshu_config['key_app_access_token'], 7000, $app_access_token);
            return $app_access_token;
        }else{
            throw new Exception($res['msg']);
        }
    }

    /**
     *
     *
     * @throws Exception
     * @see https://open.feishu.cn/document/ukTMukTMukTM/uYTM5UjL2ETO14iNxkTN/h5_js_sdk/authorization
     * @see https://open.feishu.cn/document/client-docs/h5/development-guide/step-2:-call-jsapi(optional)#29c697f8
     */
    public function get_jsapi_ticket(){
        $jsapi_ticket = $this->redis->get($this->fieshu_config['key_jsapi_ticket']);
        if($jsapi_ticket){
            return $jsapi_ticket;
        }

        $url = '/jssdk/ticket/get';
        $res = $this->http_post($url,[]);

        if($res['code'] == 0){
            //2*60*60=7200
            $jsapi_ticket = $res['data']['ticket'];
            $this->redis->setex($this->fieshu_config['key_jsapi_ticket'], 7000, $jsapi_ticket);
            return $jsapi_ticket;
        }else{
            throw new Exception($res['msg']);
        }
    }


    /**
     * @throws Exception
     */
    public function get_signature($url) {
        $jsapi_ticket = $this->get_jsapi_ticket();

        $timestamp = $this->get_millisecond_timestamp();
        $noncestr  = $this->generate_random_string(10);

        $para = [
            'jsapi_ticket' => $jsapi_ticket,
            'noncestr'     => $noncestr,
            'timestamp'    => $timestamp,
            'url'          => $url,
        ];

        $signature = sha1(http_build_query($para));

        return [
            'app_id'       => $this->app_id,
            'jsapi_ticket' => $jsapi_ticket,
            'noncestr'     => $noncestr,
            'timestamp'    => $timestamp,
            'url'          => $url,
            'signature'    => $signature
        ];
    }
    // ----------------------------- no access_token




    // ---------------------------- app_access_token

    /**
     * 这个接口返回一个用户信息对象,其中包含user_account_token
     *
     * @param string $code
     *
     * @return array
     * @throws Exception
     * @see https://open.feishu.cn/document/server-docs/authentication-management/access-token/create-2 获取 user_access_token（网页应用）
     */
    public function get_user_access_token($code) {
        $user_info = $this->redis->get($this->fieshu_config['key_user_info']);
        if($user_info){
            return json_decode($user_info,true);
        }

        $url = '/authen/v1/access_token';
        $data = [
            'grant_type' => 'authorization_code',
            'code'       => $code,
        ];

        $res = $this->http_post($url, $data,'app');

        if($res['code'] == 0){
            //6900秒有效期
            $this->redis->setex($this->fieshu_config['key_user_info'], 6700, json_encode($res));
            return $res;
        }else{
            throw new Exception($res['msg']);
        }
    }

    // ---------------------------- app_access_token





    // ---------------------------- tenant_access_token

    /**
     * 获取机器人所在群 列表
     *
     * @param string $user_id_type
     * @param string $sort_type
     * @param string $page_token 用于翻页
     * @param int    $page_size
     *
     * sort_type:
     * ByCreateTimeAsc：按群组创建时间升序排列
     * ByActiveTimeDesc：按群组活跃时间降序排列
     *
     * @return array
     * @throws Exception
     * @see https://open.feishu.cn/document/server-docs/group/chat/list
     */
    public function im_chats($user_id_type = 'user_id', $sort_type = 'ByCreateTimeAsc', $page_token = '', $page_size = 100) {
        $url = '/im/v1/chats';
        $data = [
            'user_id_type' => $user_id_type,
            'sort_type'    => $sort_type,
            'page_token'   => $page_token,
            'page_size'    => $page_size,
        ];

        $paras = http_build_query($data);

        $url = $url."?".$paras;

        return $this->http_get($url);
    }


    /**
     * 发送消息
     *
     * @return true
     * @throws Exception
     * @see https://open.feishu.cn/document/server-docs/im-v1/message/create
     */
    public function im_messages($receive_id_type,$data) {
        $url = '/im/v1/messages?receive_id_type='.$receive_id_type;
        return $this->http_post($url, $data);
    }


    /**
     * 创建审批(实例)
     *
     * @throws Exception
     * @see https://open.feishu.cn/document/server-docs/approval-v4/instance/create
     * @see https://open.feishu.cn/document/server-docs/approval-v4/approval/overview-of-approval-resources
     */
    public function create_approval($data) {
        $url  = '/approval/v4/instances';
        return $this->http_post($url, $data);
    }


    /**
     * 获取审批定义
     *
     * @throws Exception
     */
    public function approval($approval_code) {
        $url  = '/approval/v4/approvals/'.$approval_code;
        try {
            return $this->http_get($url);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    /**
     * 通过手机号码获取user_id
     *
     * @throws Exception
     */
    public function batch_get_id($mobiles){
        $arr['mobiles'] = $mobiles;
        $url = '/contact/v3/users/batch_get_id?user_id_type=user_id';
        try {
            return $this->http_post($url, $arr);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }



    // ---------------------------- tenant_access_token














    // ---------------------------- tools
    /**
     * 毫秒时间戳
     * @return string
     */
    private function get_millisecond_timestamp() {
        list($microseconds, $seconds) = explode(' ', microtime());
        return $seconds . substr($microseconds, 2, 3);
    }


    /**
     * 随机字符串
     * @param $length
     *
     * @return string
     */
    private function generate_random_string($length) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $string = '';
        $len = strlen($characters) - 1;

        for ($i = 0; $i < $length; $i++) {
            $string .= $characters[mt_rand(0, $len)];
        }
        return $string;
    }



    /**
     * @param string $url
     * @param array  $data
     * @param string $access_token_type
     *
     * @return mixed
     * @throws Exception
     */
    private function http_post($url, $data = [], $access_token_type = 'tenant'){
        try {
            return $this->http_request($url, self::METHOD_POST, $data, $access_token_type);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @param string $url
     * @param array  $data
     * @param string $access_token_type
     *
     * @return mixed
     * @throws Exception
     */
    private function http_get($url, $data = [], $access_token_type = 'tenant'){
        try {
            return $this->http_request($url, self::METHOD_GET, $data, $access_token_type);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    /**
     * @param string $url
     * @param string $method
     * @param array  $data
     * @param string $access_token_type
     *
     * @return mixed
     * @throws Exception
     */
    private function http_request($url, $method, $data, $access_token_type = 'tenant'){
        $curl = curl_init();
        $http_header = [];
        $http_header[] = 'Content-Type: application/json; charset=utf-8';
        $access_token = '';
        try {
            switch ($access_token_type){
                case 'tenant':
                    $access_token = $this->get_tenant_access_token();
                    break;
                case 'app':
                    $access_token = $this->get_app_access_token();
                    break;
                default :
                    //获取access_token
                    break;
            }
            if($access_token_type != 'none' || !empty($access_token)) {
                $http_header[] = 'Authorization: Bearer ' . $access_token;
            }

            curl_setopt_array($curl, [
                CURLOPT_URL            => $this->url_prefix.$url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING       => '',
                CURLOPT_MAXREDIRS      => 10,
                CURLOPT_TIMEOUT        => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST  => $method,
                CURLOPT_HTTPHEADER     => $http_header
            ]);

            if(!empty($data)){
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
            }
            $response = curl_exec($curl);

            if (curl_errno($curl)) {
                throw new Exception('调用飞书接口失败! errno: '.curl_errno($curl).' err:'.curl_error($curl));
            }

            curl_close($curl);
        } catch (Exception $e) {
            throw new Exception('调用飞书接口请求错误,url:'.$url.', 错误信息:'.$e->getMessage());
        }
        $res = json_decode($response,true);

        if($res['code'] != 0){
            $log_info = [
                'handle_type'   => 'feishu',
                'description'   => '调用飞书接口返回异常',
                'content'       => $response,
                'handle_user'   => session(C('LOGIN_SESSION_NAME')) ?: 'system',
                'handle_result' => '1',
            ];
            HandlerLog::log($log_info);
            throw new Exception('调用飞书接口返回异常,code:'.$res['code'].', message:'.$res['msg']);
        }else {
            return $res;
        }
    }
    // ---------------------------- tools
}