<?php

namespace Common\Common;

use Account\Model\AccountModel;
use Account\Model\ProductModel;
use Api\Repositories\BackendProductRepository;
use Think\Controller;
use Think\Log;

class Approval extends Controller
{

    protected $configlist;

    protected $key_name_maps = [];

    protected $uri;

    protected $data_json_arr;

    // 线上测试客户
    protected $test_custumer = [
        // 'C2023032427S698',//修伟-测试-帐号-01
        'C20180828LOCNMG',//羽乐科技-内部
        'C20200622KF31GS',//售前测试
    ];

    public function __construct()
    {
        $this->setConfigList();
    }

    /**
     * 设置需要审核操作的配置，比如审批人 字段名称关系
     */
    protected function setConfigList()
    {
        $mod = D('ApprovalConfig');
        $list = $mod->getList();

        foreach ($list as  $value) {
            $this->configlist[$value['node_url']] = $value;
        }
    }

    /**
     * 判断uri是否需要审核，需要则写访问
     */
    public function checkAddApprove($username)
    {
        $uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : "";
        //去掉uri后面的参数
        $wenhaoPos = strpos($uri, '?');
        if ($wenhaoPos !== false) {
            $uri = substr($uri, 0, $wenhaoPos);
        }
        $uri = str_replace('.html', '', $uri);
        $this->uri = $uri = ltrim($uri, '/');
        $needCheck = array_column($this->getCheckApprovalList(), 'node_url');
        // $approvers = isset($this->getCheckApprovalList()[$uri]['approver']) ? explode(',',$this->getCheckApprovalList()[$uri]['approver']):['no_user'];
        $approval_token =  I('post.approval_token');
        // post有approval_token值 代表是审核通过的请求，不需要审核,
        // 自己是审核人不需要审核 &&(!in_array($username, $approvers))
        if (in_array($uri, $needCheck) && IS_POST && empty($approval_token)) {
            $request = I('param.');

            //账号产品更新时校验一下产品状态
            if($uri == 'Api/BackendProduct/updateProduct'){
                $this->checkProductStatus($request);
            }
            // 检查是否有未审核通过的操作又提交
            if($this->checkUnapproveOperation($request)){
                $this->__Return('已存在未处理的审批，请通过后再提交！', '', 'success');
            };

            //判断是否需要跳过审核
            $need_approval = $this->checkNeedApproval($request);
            if(!$need_approval){//不跳过
                // 提交审核
                $this->saveApprovalAndOpinfo($request, $username);
                $this->__Return('提交成功，审核通过后生效！', '', 'success');
            }
            // else 跳过
        }
    }

    public function checkProductStatus($request){
        $product_id = isset($request['product_id']) ? $request['product_id'] : '';
        $this->verifyProductStatus($product_id);

        $data = isset($request['data']) ? $request['data'] : '';
        $data_json = isset($request['data_json']) ? $request['data_json'] : [];
        $data_json = json_decode(html_entity_decode($data_json), true);

        array_walk($data_json, function ($item) use ($data) {
            // 校验必须填写
            $this->verifyRequireForCommon($item, $data);

            // 如果是product_id、out_fields字段，必须选择一个产品开通且产品不是下架产品
            // 开通的子产品的字段在邦信分中字段为product_ids,在号码风险等级产品则叫out_fields
            if(in_array($item['name'], ['product_ids', 'out_fields'])){
                if(!isset($data[$item['name']])){
                    $this->__Return( $item['cn_name'] . '未填写!!!', '', 'success');
                }else{
                    //检验产品是否下架
                    $this->verifyMultipleProductStatus($data[$item['name']]);
                }
            }
        });

    }

    /**
     * 检测产品是否下架
     * @throws \Exception
     */
    public function verifyProductStatus($product_id)
    {
        $where['product_id'] = ['EQ', $product_id];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $exists = $this->getOneProductByCondition($where);
        if (!$exists) {
            $this->__Return( '抱歉,该产品已经下架哦!!!', '', 'success');
        }
    }

    /**
     * 检测多个产品中是否有下架产品
     * @throws \Exception
     */
    public function verifyMultipleProductStatus($product_ids = []){
        if(empty($product_ids)){
            $this->__Return( '请选择开通的子产品!!!', '', 'success');
        }

        $where['product_id'] = ['IN', $product_ids];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $usableList = (new ProductModel())->where($where)
            ->field('product_id')
            ->select();

        $usableList = array_column($usableList, 'product_id');
        $diff = [];
        foreach ($product_ids as $pid){
            if(!in_array($pid, $usableList)){
                $diff[] = $pid;
            }
        }

        if(!empty($diff)){
            $this->__Return( '产品'.implode(',', $diff).'已下架哦!!!', '', 'success');
        }

    }

    /**
     * 检测公用的部分
     * @param array $item 单个的单元
     * @param array $data 结果
     * @throws \Exception
     */
    public function verifyRequireForCommon(array $item, array $data)
    {
        // 检查是否必须
        if ($item['is_need'] && (!array_key_exists($item['name'], $data) || ($data[$item['name']] === ''))) {
            $this->__Return( $item['cn_name'] . '未填写!!!', '', 'success');
        }
    }

    /**
     * 获取一个产品
     * @param array $where
     * @return array
     */
    public function getOneProductByCondition(array $where)
    {
        return (new ProductModel())->where($where)
            ->find();
    }

    /**
     * 检查是否有审核未通过的审批
     */
    public function checkUnapproveOperation($request){
        $mod = D('Approval');
        $where =[];
        $where['op_type'] = $this->uri;
        $where['status']=1;
        switch($this->uri) {
            case 'Account/Account/edit':
                $where['account_id'] = $request['father_id'];
                break;
            case 'Api/BackendProduct/updateProduct':
                $where['account_id'] = $request['account_id'];
                $where['product_id'] = $request['product_id'];
                break;
            default:
                break;
        }
        $num = $mod->where($where)->where('status', '1')->count();
        return $num>0;
    }

    /**
     * 保存审批信息
     */
    public function saveApprovalAndOpinfo($request, $loginusername)
    {
        $flow_no = $this->getFlowNo();

        $appr['flow_no']  = $flow_no;
        $appr['applicant'] = $loginusername ? $loginusername : "user_not_fount";
        //审批人
        $appr['approver'] = '';
        $appr['op_type'] = $this->uri;
        $appr['data'] = '';
        $appr['create_at'] = time();
        $appr['apply_content'] = $this->getChangeText($request);

        // 三个公共参数 非必需
        $this->setCommmonParams($appr,$request);
        $mod = D('Approval');
        $mod->add($appr);

        $apprequest['flow_no'] = $flow_no;
        $apprequest['url'] = $this->uri;
        $apprequest['status'] = 1;
        $apprequest['op_result'] = '';
        if ($request['data_json']) {
            $request['data_json'] = html_entity_decode($request['data_json']);
        }
        // 不转义中文字符和斜线
        $apprequest['data'] = json_encode($request, 320);
        $apprequest['create_at'] = time();
        $armod = D('ApprovalRequest');
        $armod->add($apprequest);
        // TOOD 事务处理
    }

    public function setCommmonParams(&$appr,$request){
        $appr['customer_id'] = isset($request['customer_id']) ? $request['customer_id'] : '';
        $appr['product_id'] = isset($request['product_id']) ? $request['product_id'] : '';

        // 账号编辑时 请求的account_id 为 father_id 需要特殊处理一下
        switch ($this->uri) {
            case 'Account/Account/edit':
                $model = new AccountModel();
                $account_info = $model->getAccountInfoByWhere(['id' => $request['id']]);
                $appr['account_id'] = isset($account_info['account_id']) ? $account_info['account_id'] : '';
                break;
            default:
                $appr['account_id'] = isset($request['account_id'])? $request['account_id'] : '';
        }

    }

    /**
     * 获取根据不同的页面，生成用于审核的展示信息
     */
    public function getChangeText($request)
    {
        //不需要检查变动的key
        $ignore_keys = [
            'update_at'
        ];
        $changeText = '';
        // 获取key对应的中文名称
        $this->setKeyNameMaps();
        switch ($this->uri) {
            case 'Account/Customer/edit':
                break;

            case 'Account/Account/edit':
                $model = new AccountModel();
                $id = I('post.id', '', 'trim');
                // 旧数据
                $account_info = $model->getAccountInfoById($id);
                // 更新数据
                $params = $model->getParamForUpdate();
                $params['access_ip'] = implode(PHP_EOL,unserialize($params['access_ip']));
                $params['end_time'] = date('Y-m-d', $params['end_time']);
                $diffarr = [];
                foreach ($params as $key => $value) {
                    if (isset($account_info[$key]) && ($account_info[$key] != $value)) {
                        if (!in_array($key, $ignore_keys)) {
                            $diffarr[$key]['old'] = $account_info[$key];
                            $diffarr[$key]['new'] = $value;
                            $diffarr[$key]['type'] = '->';
                        }
                    }
                }
                foreach ($diffarr as $k => $value) {
                    if (isset($this->key_name_maps[$k]['cn_name'])) {
                        $key = $this->key_name_maps[$k]['cn_name'];
                    }else{
                        $key = $k;
                    }
                    // 字段值转为中文
                    if (isset($this->key_name_maps[$k]['option'])) {
                        foreach ($this->key_name_maps[$k]['option'] as $opArr) {
                            if ($opArr['opt_val'] === $value['old']) {
                                $value['old'] = $opArr['opt_name'];
                            }
                            if ($opArr['opt_val'] === $value['new']) {
                                $value['new'] = $opArr['opt_name'];
                            }
                        }
                    }
                    $changeText .= $key . '：' . $value['old'] . $value['type'] . $value['new'] . PHP_EOL;
                }

                break;
            case 'Api/BackendProduct/updateProduct':
                $backRepo = new BackendProductRepository();
                $backRepo->setProductBeforeUpdate();
                $beforedata = $backRepo->getDataBeforeUpdate();
                $afterdata = $backRepo->genParamsForUpdate();
                $changeText = $this->compareDiffData($beforedata, $afterdata, $ignore_keys);
                break;
            default:
                break;
        }
        return $changeText;
    }


    // 修改开通产品时 新旧数据对比
    public function compareDiffData($dataOld, $dataNew, $ignore_keys = [])
    {
        $changeText = '';
        switch ($this->uri) {
            case 'Api/BackendProduct/updateProduct':
                $olddata = json_decode($dataOld['data'], true);
                $newdata = json_decode($dataNew['data'], true);
                // Log::write('old'.var_export($dataOld,true).' new'.var_export($dataNew,true));
                //比较公共字段
                foreach ($dataNew as $key => $value) {
                    // 账号产品更新的截止日期由时间戳转为时间格式
                    if ($key == 'end_time') {
                        $dataOld[$key] = date('Y-m-d', $dataOld[$key]);
                        $value = date('Y-m-d',$value);
                    }
                    if (isset($dataOld[$key]) && ($dataOld[$key] != $value)) {
                        //
                        if (!in_array($key, $ignore_keys) && ($key != 'data')) {
                            $diffarr[$key]['old'] = $dataOld[$key];
                            $diffarr[$key]['new'] = $value;
                            $diffarr[$key]['type'] = '->';
                        }
                    }
                }
                // 比较data字段
                foreach ($newdata as $key => $value) {
                    if (isset($olddata[$key]) && !is_array($olddata[$key]) && ($olddata[$key] != $value)) {
                        if (!in_array($key, $ignore_keys)) {
                            $diffarr[$key]['old'] = $olddata[$key];
                            $diffarr[$key]['new'] = $value;
                            $diffarr[$key]['type'] = '->';
                        }
                    } elseif (isset($olddata[$key]) && is_array($olddata[$key])) { 
                        //data 中的二维数组比较 有差异则记录
                        $arrUnique =false;
                        $arr = $olddata[$key];
                        if(count($arr)!=count($value)) {
                            $arrUnique = true;
                        }else{
                            $arrUnique = !empty(array_diff($arr, $value))||!empty(array_diff($value, $arr))||!empty(array_diff_assoc($arr, $value))||!empty(array_diff_assoc($value, $arr));  
                        }
                        if($arrUnique) {
                            $diffarr[$key]['old'] = json_encode($olddata[$key]);
                            $diffarr[$key]['new'] = json_encode($value);
                            $diffarr[$key]['type'] = '->';
                        }

                    }
                }
                foreach ($diffarr as $k => $value) {
                    if (isset($this->key_name_maps[$k]['cn_name'])) {
                        // 字段转为中文
                        $key = $this->key_name_maps[$k]['cn_name'];
                        // 字段值转为中文
                        if (isset($this->key_name_maps[$k]['option'])) {
                            foreach ($this->key_name_maps[$k]['option'] as $opArr) {
                                if ($opArr['opt_val'] == $value['old']) {
                                    $value['old'] = $opArr['opt_name'];
                                }
                                if ($opArr['opt_val'] == $value['new']) {
                                    $value['new'] = $opArr['opt_name'];
                                }
                            }
                        }
                    }else{
                        $key = $k;
                    }
                    $changeText .= $key . '：' . $value['old'] . $value['type'] . $value['new'] . PHP_EOL;
                }
                break;
            default:
                break;
        }
        return $changeText;
    }

    public function setKeyNameMaps()
    {
        if (isset($this->configlist[$this->uri])) {
            $this->key_name_maps = json_decode($this->configlist[$this->uri]['approve_column'], true);
        }
        switch ($this->uri) {
            case 'Api/BackendProduct/updateProduct':
                $data_json            = I('post.data_json', '', 'trim');
                $this->data_json_arr =$data_json_arr = json_decode($data_json, true);

                foreach ($data_json_arr as  $value) {
                    $this->key_name_maps[$value['name']] = $value;
                }
                break;
            default:
                break;
        }
        // Log::write('key_maps:' . var_export($this->key_name_maps, true));
    }


    // 审批流水号 11位字符+13位时间戳
    public function getFlowNo()
    {
        $string = new \Org\Util\String();
        list($t1, $t2) = explode(' ', microtime());
        return  $string->randString(11) . (float) sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
    }

    public function getApplicantById($id)
    {
        $mod = D('Approval');
        return $mod->where(array('id' => $id))->getField('applicant');
    }

    // 获取需要检查权限的uri
    public function getCheckApprovalList()
    {
        if (empty($this->configlist)) {
            $this->setConfigList();
        }
        return $this->configlist;
    }

    // 根据uri获取审核人
    public function getApproverByuri($uri)
    {
        foreach ($this->configlist as $config) {
            if ($config['node_url'] == $uri) {
                return $config['approver'];
            }
        }
        return 'not config for ' . $uri;
    }

    /**
     * 检查是否需要审批 返回ture为不需要进行审批 返回false为需要
     * @param $new_data array 新数据
     * @return boolean
     */
    private function checkNeedApproval($new_data){
        //不需要检查变动的key
        $ignore_keys = [
            'update_at'
        ];

        $customer_id = '';
        $old_data = [];
        //获取原始数据
        switch($this->uri) {
            case 'Account/Account/edit':
                $model = new AccountModel();
                $id = I('post.id', '', 'trim');
                $customer_id = I('post.customer_id', '', 'trim');
                $old_data = $model->getAccountInfoById($id);
                break;
            case 'Api/BackendProduct/updateProduct':
                $backRepo = new BackendProductRepository();
                $backRepo->setProductBeforeUpdate();
                $old_data = $backRepo->getDataBeforeUpdate();
                $new_data = $backRepo->genParamsForUpdate();
                //获取customer_id
                $account_info = (new AccountModel())->getAccountInfoByWhere(['account_id'=>$old_data['account_id']]);
                $customer_id = $account_info['customer_id'];
                break;
            default:
                break;
        }
        $need = true;
        $changes = [];//存在改动的字段
        //判断是否是测试帐号
        if(!in_array($customer_id,$this->test_custumer)){//测试帐号的任何操作不进行审批
            //判断所有存在改动的字段
            foreach ($new_data as $key => $value) {
                if (isset($old_data[$key]) && ($old_data[$key] != $value) && !in_array($key, $ignore_keys)) {
                    $changes[$key] = $value;
                }
            }

            //判断是否需要处理
            foreach($changes as $column => $value){
                //判断这些字段是否都是配置中的字段
                $t_need = $this->compare_rule($this->uri,$column,$value,$old_data[$column]);
                $need = $need & $t_need;
            }
        }
        return $need;
    }

    /**
     * 与配置的规则进行比较
     * @param $uri string 地址
     * @param $column string 字段
     * @param $new_value string 新值
     * @param $old_value string 原始值
     * @return bool
     */
    private function compare_rule($uri,$column,$new_value,$old_value){
        $ret = false;
        switch($uri) {
            case 'Account/Account/edit'://账号编辑
                if($column == 'concurrency'){//账号秒并发
                    $ret = $this->concurrency_rule($old_value,$new_value);
                }else if($column == 'end_time' && $new_value > $old_value){//截止日期 设置为大于原始值 不需要审核
                    $ret = true;
                }
                break;
            case 'Api/BackendProduct/updateProduct'://账号产品更新
                if(in_array($column, ['concurrency','daily_limit','month_limit','year_limit','total_limit'])){//账号秒并发,用量限额
                    $ret = $this->concurrency_rule($old_value,$new_value);
                }else if($column == 'end_time' && $new_value > $old_value){//截止日期 设置为大于原始值 不需要审核
                    $ret = true;
                }
                break;
            default:
                break;
        }
        return $ret;
    }

    /**
     * 秒并发 无需审批规则
     * @param $old_value
     * @param $new_value
     * @return bool
     */
    private function concurrency_rule($old_value,$new_value){
        $ret = false;
        if($old_value == -1){//原始值为-1的情况 修改为其他值时需要审核
            $ret = false;
        }else if($new_value == -1 || $new_value > $old_value){//设置为大于原始值 或者 为-1 不需要审核
            $ret = true;
        }
        return $ret;
    }

    /**
     * 页面返回
     * @param  string $info 提示信息
     * @param  string $data 返回数据(跳转链接url 使用array('url'=>'') 跳转时间 使用array('times'=>))
     * @param  string $status 返回状态(stripos) error|success|tip_error|tip_success
     * @param  string $ajax 是否为ajax 默认为自动判断
     */
    public function __Return($info = '', $data = '', $status = 'tip_error', $ajax = 'auto')
    {
        $ajax = $ajax === 'auto' ? IS_AJAX : $ajax;
        if ($ajax === FALSE) {
            $jumpUrl = isset($data['url']) ? $data['url'] : '';
            $times = isset($data['times']) && is_numeric($data['times']) ? $data['times'] : FALSE;
            if (stripos($status, 'error') !== FALSE) {
                $this->error($info, $jumpUrl, $times);
            } else {
                $this->success($info, $jumpUrl, $times);
            }
        } else {
            $this->ajaxReturn(array('data' => $data, 'info' => $info, 'status' => $status), 'JSON', JSON_UNESCAPED_UNICODE);
        }
        exit;
    }
}
