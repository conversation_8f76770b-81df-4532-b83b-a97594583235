<?php

namespace Common\Common;

use Common\Model\HandlerLogModel;

class HandlerLog
{
    /*
     * 传递的日志数据
     * */
    private $log_info;

    /*
     * database需要填充的字段
     * */
    private $list_field = [
        'handle_type',
        'description',
        'content',
        'created_at',
        'handle_user',
        'handle_result'
    ];

    private function __construct(array $log_info)
    {
        $this->log_info = $log_info;
    }

    /**
     * 记录错误日志
     * @param array $log_info
     * @throws \Exception
     */
    public static function log(array $log_info)
    {
        return (new static($log_info))->logDo();
    }

    /**
     * @throws \Exception
     */
    private function logDo()
    {
        // 校验参数是否合法
        $this->validateParams();

        // 入库
        $this->writeDatabase();
    }

    /**
     * 入库
     */
    private function writeDatabase()
    {
        // 生成参数
        $params = $this->genParamsForDatabase();

        // 写入
        $this->writeDo($params);
    }

    /**
     * 写入
     * @param array $params
     */
    private function writeDo(array $params)
    {
        $i = 0;
        while (true) {
            $i++;
            $response_write = (new HandlerLogModel())->add($params);
            if ($response_write !== false || $i >= 3) {
                break;
            }
        }
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsForDatabase()
    {
        return array_reduce($this->list_field, function ($carry, $field) {
            $carry[$field] = array_key_exists($field, $this->log_info) ? $this->log_info[$field] : ($field === 'created_at' ? time() : '');

            // 详情内容需要专程json
            if ($field === 'content' || is_array($carry[$field])) {
                $carry[$field] = json_encode($carry[$field], JSON_UNESCAPED_UNICODE);
            }

            // 如果当前操作的人是空的话 则取登陆用户
            if ($field === 'handle_user' && $carry[$field] === '') {
                $carry[$field] = session(C('LOGIN_SESSION_NAME'));
            }

            return $carry;
        }, []);
    }

    /**
     * 校验参数是否合法
     * @throws \Exception
     */
    private function validateParams()
    {
        if (!is_array($this->log_info)) {
            throw new \Exception('Handler Log需要数组参数');
        }

        if (!array_key_exists('handle_type', $this->log_info) || !$this->log_info['handle_type']) {
            throw new \Exception('Handler Log 缺少handle_type参数, 记录类型eg: create update report');
        }

        if (!array_key_exists('content', $this->log_info) || !$this->log_info['content']) {
            throw new \Exception('Handler Log 缺少content参数, 用来记录错误内容');
        }

        if (!array_key_exists('description', $this->log_info) || !$this->log_info['description']) {
            throw new \Exception('Handler Log 缺少description参数, 用来记录提示');
        }

        if (!array_key_exists('handle_result', $this->log_info) || !in_array($this->log_info['handle_result'], ['0', '1', 0, 1])) {
            throw new \Exception('Handler Log 缺少handler_result参数, 用来记录操作的结果(限定: 1成功 0失败)');
        }
    }
}
