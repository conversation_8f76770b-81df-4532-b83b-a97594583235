<?php

namespace Common\Common;

trait CurlTrait
{
    public function patchParams()
    {
        // PHP5.6之前 只可以读取一次
        static  $input_php_5;
        if ($input_php_5) {
            return $input_php_5;
        }

        $input = file_get_contents('php://input');
        return $input_php_5 = json_decode($input, true);
    }

    /**
     * 获取patch参数
     * @param $key
     * @param string $default
     * @param string $filter
     * @return mixed|string
     */
    public function patchKey($key, $default = '', $filter = '')
    {
        $request_body = $this->patchParams();
        $value = array_key_exists($key, $request_body) ? $request_body[$key] : $default;
        if ($filter === '') {
            return $value;
        }

        return call_user_func($filter, $value);
    }

    /**
     * 限定更新方式
     * @throws \Exception
     */
    public function limitMethodPatch()
    {
        if (!in_array($_SERVER['REQUEST_METHOD'], ['PUT', 'PATCH'])) {
            throw new \Exception('本方法只可以使用PUT或者PATCH方法访问');
        }
    }

    /**
     * 从post参数中获取某个key
     * @param $key
     * @param string $default
     * @param string $filter 过滤回调函数
     * @return mixed
     */
    public function postKey($key, $default = '', $filter = '')
    {
        $request_body = $this->postParams();
        $value = array_key_exists($key, $request_body) ? $request_body[$key] : $default;
        if ($filter === '') {
            return $value;
        }

        return call_user_func($filter, $value);
    }

    /**
     * 获取payload && formData数据
     * @return array
     */
    public function postParams()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        return array_merge(I('post.'), (array)$request_body);
    }

    /**
     * 限定POST方式访问
     * @throws \Exception
     */
    public function limitMethodPost()
    {
        if (!IS_POST) {
            throw new \Exception('限定POST方式访问');
        }
    }

    /**
     * 限定GET方式访问
     * @throws \Exception
     */
    public function limitMethodGet()
    {
        if (!IS_GET) {
            throw new \Exception('限定GET方式访问');
        }
    }

    /**
     * 发送get请求
     * @param string $url 请求的Url
     * @param array $url_data 请求参数
     * @return array|mixed
     */
    public function get($url, $url_data = [])
    {
        // 追加参数
        if ($url_data && is_array($url_data)) {
            $url_data = http_build_query($url_data);
            $url .= '?' . $url_data;
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        $response = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'msg' => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($response)) {
            return json_decode($response, true);
        }

        return $response;
    }

    /**
     * 发送post请求
     * @param string $url
     * @param  array|string $post_data
     * @return array|mixed
     */
    public function post($url, array $post_data)
    {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $curl = curl_init();

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $response = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'code' => 1378, // 设置出错标识
                'msg' => curl_error($curl)
            ];
        }
        if (empty($response)) {
            $error = curl_error($curl);
            $errno = curl_errno($curl);
            curl_close($curl);
            throw new \Exception('response is empty, error code is ' . $error . ', errno is ' . $errno);
        }
        curl_close($curl);

        if (is_string($response)) {
            return json_decode($response, true);
        }

        return $response;
    }
}
