<?php

namespace Common\Common;


trait PhpExcelTrait
{
    protected $col;
    /**
     * @var \PHPExcel
     **/
    protected $php_excel;
    /**
     * 初始化一个Excel导出对象
     *
     * @access protected
     *
     * @return $this
     **/
    protected function file_out_init()
    {
        ob_end_clean(); //清空缓冲区界面显示
        include LIB_PATH . '/Org/PHPExcel/PHPExcel.php';
        include LIB_PATH . '/Org/PHPExcel/PHPExcel/Writer/Excel2007.php';
        $this->php_excel = new \PHPExcel();
        $this->col = 1;
        return $this;
    }
    /**
     * 获取一个列的标记
     *
     * @access protected
     * @param $serial integer 列的序号（从0开始计数）
     *
     * @return string
     **/
    protected function getRowString($serial)
    {
        if ($serial>=0 && $serial<26) {
            return chr(65+$serial);
        } elseif ($serial<702) {
            $prefix = chr(64 + floor($serial/26));
            return $prefix . chr(65 + ($serial%26));
        } else {
            throw new \Exception($serial . 'is too long');
        }
    }
    /**
     * 增加一行内容
     *
     * @access protected
     * @param $content array 内容数据
     * @param $height integer 行高
     * @param $isBold boolean 是否加粗
     * @param $isCenter boolean 是否居中显示
     *
     * @return $this
     **/
    protected function addRowContent($content, $height = null, $isBold = false, $isCenter = true)
    {
        //设置内容
        $row = 0;
        $start_row = $end_row = $this->getRowString($row) . $this->col;
        foreach ($content as $item) {
            $end_row = $this->getRowString($row) . $this->col;
            $this->setCellValue($end_row, $item);
            $row ++;
        }
        $pCellCoordinate = $start_row . ':' . $end_row;
        //设置加粗
        if ($isBold) {
            $this->setBold($pCellCoordinate);
        }
        //设置文字居中(双向居中)
        if ($isCenter) {
            $this->setCenter($pCellCoordinate);
        }
        //设置高度
        if (!empty($height)) {
            $this->setHeight($height, $this->col);
        }
        $this->col++;
        return $this;
    }
    /**
     * 填充某单元格
     *
     * @access protected
     * @param $pCoordinate string 单元格
     * @param $value string 值
     * @param
     *
     * @return
     **/
    protected function setCellValue($pCoordinate, $value)
    {
        $this->php_excel->getActiveSheet()->setCellValue($pCoordinate, $value);
        return $this;
    }
    /**
     * 统一设置某列的宽度
     *
     * @access protected
     * @param $width array 宽度
     *
     * @return $this
     **/
    protected function setWidth($width)
    {
        $row = 0;
        foreach ($width as $item) {
            $this->php_excel->getActiveSheet()->getColumnDimension($this->getRowString($row))->setWidth($item);
            $row++;
        }
        return $this;
    }
    /**
     * 设置一个区间内部的字体加粗
     *
     * @access protected
     * @param $pCellCoordinate string 区间
     *
     * @return $this
     **/
    protected function setBold($pCellCoordinate)
    {
        $this->php_excel->getActiveSheet()->getStyle($pCellCoordinate)->getFont()->setBold(true);
        return $this;
    }
    /**
     * 设置一个区间内部的文字双向居中
     *
     * @access protected
     * @param $pCellCoordinate string 区间
     *
     * @return $this
     **/
    protected function setCenter($pCellCoordinate)
    {
        $this->php_excel->getActiveSheet()->getStyle($pCellCoordinate)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $this->php_excel->getActiveSheet()->getStyle($pCellCoordinate)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        return $this;
    }
    /**
     * 设置某行的行高
     *
     * @access protected
     * @param $height integer 高度
     * @param $col string 行
     *
     * @return $this
     **/
    protected function setHeight($height, $col)
    {
        $this->php_excel->getActiveSheet()->getRowDimension($col)->setRowHeight($height);
        return $this;
    }
    /**
     * 合并指定区间的单元格
     *
     * @access protected
     * @param $pRange string 区间
     *
     * @return $this
     **/
    protected function mergeCell($pRange)
    {
        $this->php_excel->getActiveSheet()->mergeCells($pRange);
        return $this;
    }
    /**
     * 设置某区间内的单元格字体颜色
     *
     * @access protected
     * @param $pCellCoordinate string 区间
     * @param $color string 颜色值（目前支持红色red/绿色green/蓝色blue）
     *
     * @return $this
     **/
    protected function setColor($pCellCoordinate, $color)
    {
        switch ($color) {
            case 'green' :
                $php_color = \PHPExcel_Style_Color::COLOR_GREEN;
                break;
            case 'red' :
                $php_color = \PHPExcel_Style_Color::COLOR_RED;
                break;
            case 'blue' :
                $php_color = \PHPExcel_Style_Color::COLOR_BLUE;
                break;
            default :
                $php_color = $color;
                break;
        }
        $this->php_excel->getActiveSheet()->getStyle($pCellCoordinate)->getFont()->getColor()->setARGB($php_color);
        return $this;
    }
    /**
     * 下载生成好的Excel文件
     *
     * @access protected
     * @param $filename string 文件的名称(不带后缀)
     *
     * @return void
     **/
    protected function download($filename)
    {
        header('Content-Type:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition:attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control:max-age=0');
        $objWriter =\PHPExcel_IOFactory:: createWriter($this->php_excel, 'Excel2007');
        $objWriter->save( 'php://output');
        die;
    }
    /**
     * 保存生成号的Excel文件
     *
     * @access protected
     * @param $filePath string 保存的文件路径（需要携带文件名、后缀）
     *
     * @return void
     **/
    protected function save($filePath)
    {
        $objWriter = \PHPExcel_IOFactory::createWriter($this->php_excel, 'Excel2007');
        $objWriter->save($filePath);
    }
    /**
     * 设置Sheet标题
     *
     * @access protected
     * @param $sheet_title string 名称
     *
     * @return $this
     **/
    protected function setSheetTitle($sheet_title)
    {
        $this->php_excel->getActiveSheet()->setTitle($sheet_title);
        return $this;
    }
    /**
     * 切换sheet
     *
     * @access protected
     * @param $index integer sheet序号
     *
     * @return $this
     **/
    protected function cutSheet($index)
    {
        $this->php_excel->setActiveSheetIndex($index);
        return $this;
    }
}