<?php

namespace Common\Common;

trait WechatWarningReportRuleTarit
{
    /*
     * 组数据提示
     * */
    private $wechat_msg_group = '';

    /*
     * 操作符的中文释义
     * */
    protected $list_map_relation = [
        '>' => '大于限定值',
        '<' => '小于限定值',
        '<=' => '不大于限定值',
        '>=' => '不小于限定值',
    ];

    /**
     * 检测
     */
    private function validateStatInfo()
    {
        $limit_relation = $this->wechat_config['limit_items']['relation'];
        $validate_number = $validate_items = count($this->wechat_config['limit_items']['items']);

        // 循环检测
        array_walk($this->wechat_config['limit_items']['items'], function ($item_validate) use (&$validate_number) {
            // 处理要检查的每一项
            $validate_result = $this->validateStatItem($item_validate);
            $validate_number -= (int)$validate_result;
        });

        // 判定本次校验是否触发微信警报
        if ($limit_relation === 'or') {
            return $validate_number !== $validate_items;
        }

        // 如果是and的话 则必须全部命中不合格
        return $validate_number === 0;
    }

    /**
     * @param string $item 要检查的每一项
     * @throws \Exception
     * @return bool
     */
    protected function validateStatItem($item)
    {
        // 如果校验的规则 不合规范则爆出异常
        if ($item !== 'total' && strpos($item, 'top') === false) {
            // 记录错误日志
            $msg_log = [
                'handle_type' => 'wechat',
                'description' => '邦信分详单版V1日报配置微信报警规则不合规范',
                'content' => $this->wechat_config,
                'handle_user' => 'system',
                'handle_result' => 0
            ];
            HandlerLog::log($msg_log);
            throw new \Exception('配置微信报警规则不合规范');
        }

        switch ($item) {
            case 'total':
                return $this->validateStatForTotal();
                break;
            default:
                // top类型的校验
                return $this->validateStatForTop($item);
                break;
        }
    }

    /**
     * top类型的校验
     * @param string $item_top
     * @return bool
     */
    private function validateStatForTop($item_top)
    {
        // 获取top类型的数据
        $list_top_stat = $this->getTopStat($item_top);

        // 校验规则
        return $this->validateRuleForTop($list_top_stat, $item_top);
    }

    /**
     * 校验规则
     * @param array $list_top_stat
     * @param string $item_top top系列的具体名称
     * @return boolean
     */
    private function validateRuleForTop(array $list_top_stat, $item_top)
    {
        $validate_number = $rule_number = count($list_top_stat);
        array_walk($list_top_stat, function ($item_stat) use (&$validate_number, $item_top) {
            // 是否需要触发微信报警, true需要触发 false不需要
            $validate_result = $this->validateRuleForItem($item_stat, $item_top);

            $validate_number -= (int)$validate_result;
        });

        // $validate_number变少了 则需要触发微信报警
        return $validate_number !== $rule_number;
    }

    /**
     * 要检查的top类型的数据
     * @param string $item_top
     * @return array
     */
    private function getTopStat($item_top)
    {
        $validate_number = str_replace('top', '', $item_top);
        $list_stat = session($this->session_key_wechat_warning);
        return array_slice($list_stat[$this->session_account_key], 0, $validate_number);
    }


    /**
     * 检查总计数据
     * @return boolean
     */
    protected function validateStatForTotal()
    {
        // 获取总计数据
        $list_stat = $this->getTotalStat();

        // 校验规则
        return $this->validateRuleForItem($list_stat, 'total');
    }

    /**
     * 获取总计数据
     * @return array
     */
    protected function getTotalStat()
    {
        $list_stat = session($this->session_key_wechat_warning);
        return $list_stat[$this->session_total_key];
    }

    /*
     * 是否到了微信报警的时机
     * @return boolean
     * */
    public function isTimeToWarning()
    {
        $mail_type = I('get.mail_type', 'self', 'trim');
        return $mail_type === 'official';
    }

    /**
     *  校验每一条规则(true 则需要触发微信报警 false不需要触发微信报警)
     * @param array $item_stat
     * @param string $item_check 检查的维度
     * @return boolean
     */
    public function validateRuleForItem(array $item_stat, $item_check)
    {
        // 待检测的规则
        $list_rule = $this->getRuleForItem($item_check);

        // 初始化
        $rule_validate_number = $rule_wait = count($list_rule);
        $limit_relation = $this->wechat_config['rules']['relation'];

        array_walk($list_rule, function ($item_rule) use ($item_stat, &$rule_validate_number, $item_check) {
            // 校验每一条规则，返回命中微信报警的插值
            $rule_validate_number = $this->validateOneRule($item_rule, $item_stat, $rule_validate_number);
        });

        // 判定本次校验是否触发微信警报
        if ($limit_relation === 'or') {
            return $rule_validate_number !== $rule_wait;
        }

        // 如果是and的话 则必须全部命中不合格
        return $rule_validate_number === 0;
    }

    /**
     * 为要检测的维度获取rule
     * @param string $item_check 待检测的维度
     * @return array
     */
    protected function getRuleForItem($item_check)
    {
        // 如果是原始版本
        if (!array_key_exists('version', $this->wechat_config)) {
            return $this->wechat_config['rules']['rule'];
        }

        // 目前支持的版本 只有原始版本和version=1.1.0
        return $this->wechat_config['rules']['rule'][$item_check];
    }

    /**
     * 校验每一条规则,返回命中微信报警的插值
     * @param array $item_rule
     * @param array $item_stat
     * @param  integer $rule_validate_number
     * @return integer
     */
    public function validateOneRule(array $item_rule, array $item_stat, $rule_validate_number)
    {
        // 需要判断规则的版本,如果非原始版本的话，那么有可能是 && 结构的规则
        if ($this->isOriginRuleFormat($item_rule)) {
            return $this->validateOneOriginRule($item_rule, $item_stat, $rule_validate_number);
        }

        return $this->validateOneGroupRule($item_rule, $item_stat, $rule_validate_number);
    }

    /**
     * 检测原始规则集合的版本
     * @param array $item_rules 规则组
     * @param array $item_stat
     * @param $rule_validate_number
     * @return integer
     */
    private function validateOneGroupRule(array $item_rules, array $item_stat, $rule_validate_number)
    {
        // 规则组的安排一定是and类型
        $group_number = count($item_rules);
        array_walk($item_rules, function ($item_rule) use (&$group_number, $item_stat) {
            $group_number = $this->validateOneOriginRule($item_rule, $item_stat, $group_number, 'group');
        });

        // 如果全部命中规则
        if ($group_number == 0) {
            $this->wechat_msg .= $this->wechat_msg_group;
            $this->wechat_msg_group = '';
            return $rule_validate_number - 1;
        }

        $this->wechat_msg_group = '';
        return $rule_validate_number;
    }

    /**
     * 判断规则的结构是原始结构
     * @param array $item_rule
     * @return bool
     */
    private function isOriginRuleFormat(array $item_rule)
    {
        // 如果不存在的话 说明是几个原始规则封装起来的结构
        return array_key_exists('field', $item_rule);
    }

    /**
     * @param array $item_rule
     * @param array $item_stat
     * @param  integer $rule_validate_number
     * @param string $source 更新规则的类型
     * @return integer
     */
    private function validateOneOriginRule(array $item_rule, array $item_stat, $rule_validate_number, $source = 'origin')
    {
        // 限定的方式
        $limit_type = $item_rule['relation'][0];
        $limit_field = $item_rule['field'];
        $limit_tip = $item_rule['tip'];

        // 选中的值
        $validate_val = array_key_exists($limit_field, $item_stat) ? ($item_stat[$limit_field] !== 'NA' ? $item_stat[$limit_field] : 0) : 0;
        $which_validate = array_key_exists('name_account', $item_stat) ? ($item_stat['name_account'] ?: '无绑定') : '总计';

        switch ($limit_type) {
            case '<':
                $limit_val = $item_rule['relation'][1];
                if ($validate_val < $limit_val) {
                    // 格式化限定值
                    $this->setWechatMsg($limit_type, $limit_val, $which_validate, $limit_tip, $validate_val, $source);
                    $rule_validate_number--;
                }

                break;
            case '>':
                $limit_val = $item_rule['relation'][1];
                if ($validate_val > $limit_val) {
                    // 格式化限定值
                    $this->setWechatMsg($limit_type, $limit_val, $which_validate, $limit_tip, $validate_val, $source);
                    $rule_validate_number--;
                }
                break;
            case '>=':
                $limit_val = $item_rule['relation'][1];
                if ($validate_val >= $limit_val) {
                    // 设置错误信息
                    $this->setWechatMsg($limit_type, $limit_val, $which_validate, $limit_tip, $validate_val, $source);
                    $rule_validate_number--;
                }
                break;
            case '<=':
                $limit_val = $item_rule['relation'][1];
                if ($validate_val <= $limit_val) {
                    // 设置错误信息
                    $this->setWechatMsg($limit_type, $limit_val, $which_validate, $limit_tip, $validate_val, $source);
                    $rule_validate_number--;
                }
                break;
            case 'in':
                $limit_val_left = $item_rule['relation'][1][0];
                $limit_val_right = $item_rule['relation'][1][1];
                if ($validate_val > $limit_val_left && $validate_val < $limit_val_right) {
                    $this->setWechatMsg($limit_type, $item_rule['relation'][1], $which_validate, $limit_tip, $validate_val, $source);
                    $rule_validate_number--;
                }
                break;
        }
        return $rule_validate_number;
    }


    /**
     * 设置微信预警的msg
     * @param string $limit_type 操作类型
     * @param mixed $limit_val 限定值
     * @param float $which_validate ‘总计’ 或者名称
     * @param string $limit_tip 字段的中文解释
     * @param float $validate_val 当前值
     * @param string $source 规则来源
     */
    private function setWechatMsg($limit_type, $limit_val, $which_validate, $limit_tip, $validate_val, $source)
    {
        // 如果是in操作 则特殊处理
        if ($limit_type === 'in') {
            $limit_val_left = $limit_val[0];
            $limit_val_right = $limit_val[1];

            // 格式化限定值
            $limit_val_right = $this->limitValToFormat($limit_val_right);
            $limit_val_left = $this->limitValToFormat($limit_val_left);

            // 格式化当前值
            $validate_val = $this->limitValidateValToFormat($limit_val_right, $validate_val);

            $wechat_msg = $limit_tip . ' '
                . $validate_val . '在限定范围[' . $limit_val_left . ',' . $limit_val_right . ']';
            if ($source === 'origin') {
                $this->wechat_msg .= PHP_EOL . PHP_EOL . $which_validate . ' : ' .$wechat_msg;
            } else {
                $this->wechat_msg_group .= $this->wechat_msg_group === '' ? PHP_EOL . PHP_EOL . $which_validate . ' : ' . $wechat_msg : ' && ' . $wechat_msg;
            }
            return;
        }


        // 设置错误信息的处理
        $limit_val = $this->limitValToFormat($limit_val);
        $validate_val = $this->limitValidateValToFormat($limit_val, $validate_val);
        $wechat_msg =  $limit_tip . ' ' . $validate_val . $this->list_map_relation[$limit_type] . $limit_val;
        if ($source === 'origin') {
            $this->wechat_msg .= PHP_EOL . PHP_EOL . $which_validate . ' : ' .$wechat_msg;
        } else {
            $this->wechat_msg_group .= $this->wechat_msg_group === '' ? PHP_EOL . PHP_EOL . $which_validate . ' : ' . $wechat_msg : ' && ' . $wechat_msg;
        }
    }

    /**
     * 格式化当前值
     * @param mixed $limit_format_val
     * @param mixed $validate_val
     * @return mixed
     */
    private function limitValidateValToFormat($limit_format_val, $validate_val)
    {
        // 如果格式化后的限定值是没有含有% 则也是没有%
        if (strpos($limit_format_val, '%') === false) {
            return round($validate_val, 2);
        }

        return round($validate_val * 100, 2) . '%';
    }

    /**
     * 浮点格式化成%
     * @param string $limit_val
     * @return string
     */
    private function limitValToFormat($limit_val)
    {
        // 如果不是'0.6'类型 则原样返回
        if ((int)$limit_val == $limit_val) {
            return round($limit_val, 2);
        }
        return round($limit_val * 100, 2) . '%';
    }
}
