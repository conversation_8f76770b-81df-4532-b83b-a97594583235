<?php

namespace Common\Common;

use Exception;

/**
 * 预付费客户自动拆单
 */
class Feishu
{
    /**
     * @var Feishu
     */
    private $feishu_obj;


    /** @var string[] receive_id_type 类型 用于校验 */
    private $receive_id_type_map = [
        'open_id'  => '标识一个用户在某个应用中的身份。同一个用户在不同应用中的 Open ID 不同',
        'union_id' => '标识一个用户在某个应用开发商下的身份。同一用户在同一开发商下的应用中的 Union ID 是相同的',
        'user_id'  => '标识一个用户在某个租户内的身份',
        'chat_id'  => '以群ID来标识群聊', // 仅用到这个
        'email'    => '以用户的真实邮箱来标识用户',
    ];

    /** @var string[] 发送消息合理的群组 */
    private $rational_chat_ids = [
        'oc_30b4d535e979a0f1f5c5ec5d83015e02' => '金融科技后台消息通知',
    ];

    /** @var string[] 发送消息合理的用户 */
    private $rational_open_id = [
        'ou_064a1f4d82f8f8eb984a393008393caf' => '修伟',
    ];

    /** @var string[] 可以发送的消息类型 */
    private $rational_mag_types = [
        'interactive' => '卡片消息',
        'text'        => '文本消息',

    ];

    /**
     * 构造函数
     */
    public function __construct(){
        $this->feishu_obj = new FeishuBase();
    }


    /**
     * @throws Exception
     */
    public function send_card_message_to_chat_group($err_title,$err_info_array,$receive_id = 'oc_30b4d535e979a0f1f5c5ec5d83015e02'){
        $err_info_json_string = json_encode($err_info_array,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        $content = [
            "type" => "template",
            "data" => [
                "template_id"           => "AAqHSagnC6aHC",//卡片模板id
                "template_version_name" => "1.0.0",
                "template_variable"     => [
                    "error_title" => $err_title,
                    "error_info"  => $err_info_json_string,
                ],
            ],
        ];

        $content = json_encode($content,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $this->send_message('chat_id',$receive_id,'interactive',$content);
    }


    /**
     * 发送飞书消息
     *
     * @param $receive_id_type
     * @param $receive_id
     * @param $msg_type
     * @param $content
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-06-25 16:42:21
     */
    public function send_message($receive_id_type,$receive_id,$msg_type,$content){
        //校验 用户 ID 类型
        $this->check_receive_id_type($receive_id_type);
        $this->check_receive_id($receive_id_type,$receive_id);
        $this->check_receive_msg_type($msg_type);

        //校验 发送到群组

        $uuid = "";//date("YmdHis");
        $data = [
            'receive_id' => $receive_id,
            'msg_type'   => $msg_type,
            'content'    => $content,
            'uuid'       => $uuid,
        ];

        $this->feishu_obj->im_messages($receive_id_type, $data);
    }


    /**
     * 校验 用户 ID 类型
     *
     * @param $receive_id_type
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-06-25 16:47:09
     */
    private function check_receive_id_type($receive_id_type){
        if(!key_exists($receive_id_type, $this->receive_id_type_map)){
            throw new Exception("用户 ID 类型 错误");
        }
    }

    /**
     * @throws Exception
     */
    private function check_receive_msg_type($msg_type){
        if(!key_exists($msg_type, $this->rational_mag_types)){
            throw new Exception("请使用合适的消息类型");
        }
    }

    /**
     * 校验 接受消息id是否合理
     *
     * @param $receive_id_type
     * @param $receive_id
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-06-25 16:47:09
     */
    private function check_receive_id($receive_id_type,$receive_id){
        switch ($receive_id_type) {
            case 'open_id':
                if(!key_exists($receive_id, $this->rational_open_id)){
                    throw new Exception("请使用合适的open_id!");
                }
                break;
            case 'chat_id':
                if(!key_exists($receive_id, $this->rational_chat_ids)){
                    throw new Exception("请使用合适的chat_id!");
                }
                break;
            default:
                throw new Exception("请使用合适的receive_id_type!");
        }
    }
}