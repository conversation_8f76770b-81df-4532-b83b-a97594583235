<?php

namespace Common\Common;

use Common\Model\AuthModel;

trait FlushOldCrawlerToken
{
    use CurlTrait, WechatBackendExceptionTrait;

    /**
     * 刷新token
     * @param array $where 定位到一个产品的条件
     * @throws \Exception
     */
    public function flushToken(array $where)
    {
        // 要编辑的产品
        $crawler = $this->getOneCrawlerProductByCondition($where);

        // 校验要更新的产品
        $this->validateTheFlushCrawler($crawler, $where);

        // 请求token
        $this->requestToken($crawler);
    }

    /**
     * 校验产品
     * @param array $crawler
     * @param array $where
     * @throws \Exception
     */
    private function validateTheFlushCrawler(array $crawler, array $where)
    {
        if (!$crawler) {
            $msg = '刷新token出错,未定位到特定的邦秒爬产品 msg：' . json_encode(compact('where'), JSON_UNESCAPED_UNICODE);
            $this->wehcatException($msg);
            throw new \Exception($msg);
        }
    }

    /**
     * 获取一个秒爬产品
     * @param array $where
     * @return array
     */
    private function getOneCrawlerProductByCondition(array $where)
    {
        return (new AuthModel())->where($where)
            ->find();
    }

    /**
     * 请求token
     * @param array $crawler
     * @throws \Exception
     */
    private function requestToken(array $crawler)
    {
        // 生成参数
        list($params, $url) = $this->genParamsForToken($crawler);

        // 请求token
        $response = $this->requestTokenDo($url, $params);

        // 异常则预警 && \Exception
        $this->determineNoticeClient($response, $crawler);

        // 是否更新token
        $this->determineUpdateToken($crawler, $response);
    }

    /**
     * 是否更新token
     * @param array $crawler
     * @param array $response
     */
    private function determineUpdateToken(array $crawler, array $response)
    {
        // 如果是h5介入则不需要更新
        if ($crawler['source'] == 'api') {
            return;
        }

        // 参数
        list($where, $params) = $this->genParamsRelationForUpdateToken($crawler, $response);
        
        // 条件
        (new AuthModel())->where($where)
            ->save($params);
    }

    /**
     * 参数
     * @param array $crawler
     * @param array $response
     * @return array
     */
    private function genParamsRelationForUpdateToken(array $crawler, array $response)
    {
        $id = $crawler['id'];
        $token = $response['data']['token'];
        return [compact('id'), compact('token')];
    }

    /**
     * 是否通知客户端
     * @param array $response
     * @param array $crawler
     * @throws \Exception
     */
    private function determineNoticeClient(array $response, array $crawler)
    {
        // 没有异常不进行通知
        if ($response['status'] == 0) {
            return;
        }

        $msg = '用户: ' . session(C('LOGIN_SESSION_NAME')) .
            ' 在更新ID:' . $crawler['id'] . '老版本邦秒爬产品时刷新token失败, msg:' .
            json_encode($response, JSON_UNESCAPED_UNICODE);
        $this->wehcatException($msg);

        $msg_show = '刷新token失败,请稍后再试';
        throw new \Exception($msg_show);
    }

    /**
     * 生成参数
     * @param array $crawler
     * @return array
     */
    private function genParamsForToken(array $crawler)
    {
        list($appid, $appsecret) = [$crawler['appid'], $crawler['appsecret']];
        $time = time();
        $sign = md5($appid . $appsecret . $time);

        $params = compact('sign', 'time', 'appid');
        $url = C('CRS_API_CONFIG')['getToken'];
        return [$params, $url];
    }

    /**
     * 请求token
     * @param $url
     * @param array $params
     * @return array|mixed
     */
    private function requestTokenDo($url, array $params)
    {
        $i = 0;
        // 最多请求三次
        while (true) {
            $i++;
            // 请求
            $response = $this->get($url, $params);

            if ($response['status'] == 0 || $i >= 3) {
                break;
            }
        }

        return $response;
    }
}
