<?php

namespace Common\Common;

use Common\Model\ProductWechatWarningModel;
use Common\Model\WechatWarningModel;
use Think\Cache\Driver\Redis;


trait WechatWarningReportQueueTrait
{
    /*
     * redis对象
     * */
    private static $redis;

    /*
     * 队列名称
     * */
    private $queue_report = 'wechat_warning_report';

    /*
     * 微信报警队列存储的内容
     * */
    private $wechat_warning_queue_info;


    /**
     * 发送微信预警
     *
     * @param int    $product_id
     * @param string $type
     * @param string $msg
     *
     * @throws \Exception
     */
    public function sendWarning($product_id, $type, $msg)
    {
        // 检查条件
        $this->validateParamForReport($product_id, $type, $msg);

        // 发送邮件
        $this->sendWechatDo($product_id, $type, $msg);
    }

    /**
     * @param int    $product_id
     * @param string $type
     * @param string $msg
     */
    private function sendWechatDo($product_id, $type, $msg)
    {
        // 需要发送的slug
        $list_wechat = $this->getTheChoosedWechat($product_id, $type);

        array_walk($list_wechat, function ($wechat) use ($msg) {
            $queue_info        = arrOnly($wechat, ['itag_id', 'corp_id', 'agent_id', 'secret']);
            $queue_info['msg'] = $msg;
            $this->wechatQueue($queue_info);
        });
    }

    /**
     * 获取需要发送的微信配置
     *
     * @param int    $product_id
     * @param string $type
     *
     * @return array
     */
    private function getTheChoosedWechat($product_id, $type)
    {
        $product_wechat = $this->getOneProductWechatByCondition(compact('product_id', 'type'));
        $list_slug      = explode(',', $product_wechat['slug']);
        $slug           = ['in', $list_slug];
        return $this->getAllWechatByCondition(compact('slug'));
    }

    /**
     * 获取微信配置
     *
     * @param array $where
     *
     * @return mixed
     */
    private function getAllWechatByCondition(array $where)
    {
        return (new WechatWarningModel())->where($where)
            ->select();
    }

    /**
     * 检查条件
     *
     * @param $product_id
     * @param $msg
     *
     * @throws \Exception
     */
    private function validateParamForReport($product_id, $type, $msg)
    {
        if (!$product_id || !is_numeric($product_id)) {
            throw new \Exception('微信预警发送失败: msg: product_id不合法, product_id is' . $product_id);
        }

        if (!$msg) {
            throw new \Exception('微信预警发送失败: msg: msg不合法, msg is' . $msg);
        }

        if (!$type) {
            throw new \Exception('微信预警发送失败: msg: type不合法, type is' . $type);
        }

        // 是否配置了日报场景下的微信预警设置
        $product_wechat = $this->getOneProductWechatByCondition(compact('type', 'product_id'));
        if (!$product_wechat) {
            throw new \Exception('product_id=' . $product_id . ' 还没有' . $type . '预警预警的配置');
        }
    }

    /**
     * @param array $where
     *
     * @return mixed
     */
    private function getOneProductWechatByCondition(array $where)
    {
        return (new ProductWechatWarningModel())->where($where)
            ->find();
    }


    /**
     * 写入微信报警队列
     *
     * @param array $wechat_warning
     *
     * @throws \Exception
     * @return void
     */
    public function wechatQueue(array $wechat_warning)
    {
        // 初始化环境
        $this->iniParams($wechat_warning);

        // 入队
        $this->wechatDo();
    }

    /**
     * 初始化环境
     *
     * @param array $wechat_warning
     */
    private function iniParams(array $wechat_warning)
    {
        // 参数赋值
        $this->wechat_warning_queue_info = $wechat_warning;

        // 初始化redis对象
        $this->redisInstance();
    }

    /**
     * 初始化redis对象
     * @return bool
     */
    private function redisInstance()
    {
        if (self::$redis) {
            return true;
        }

        $config               = C('REDIS_WECHAT_WARNING');
        $config['persistent'] = false;
        self::$redis          = new Redis($config);
        self::$redis->setOptions(\Redis::OPT_READ_TIMEOUT, - 1);
    }

    /**
     * 微信报警
     * @throws \Exception
     */
    private function wechatDo()
    {
        // 检查队列内容
        $this->validateParams();

        // 存入队列
        $this->pushWechatQueue();

        // 记录日志
        $this->log();
    }

    /**
     * 记录日志
     * @throws \Exception
     */
    protected function log()
    {
        $msg_log = [
            'handle_type'   => 'wechat',
            'description'   => '微信报警信息入队',
            'content'       => $this->wechat_warning_queue_info,
            'handle_user'   => session(C('LOGIN_SESSION_NAME')) ? : 'system',
            'handle_result' => 0
        ];
        HandlerLog::log($msg_log);
    }

    /**
     * 存入队列
     */
    private function pushWechatQueue()
    {
        $wechat_warning_queue_info = json_encode($this->wechat_warning_queue_info, JSON_UNESCAPED_UNICODE);
        self::$redis->lpush($this->queue_report, $wechat_warning_queue_info);
    }

    /**
     * @throws \Exception
     */
    private function validateParams()
    {
        // 类型检查
        if (!is_array($this->wechat_warning_queue_info)) {
            throw new \Exception('WechatWarningReportQueueTrait需要的参数类型是array');
        }

        // 内容检查
        if (!array_key_exists('itag_id', $this->wechat_warning_queue_info) || !$this->wechat_warning_queue_info['itag_id']) {
            throw new \Exception('WechatWarningReportQueueTrait缺少合法的标签IDitag_id');
        }

        if (!array_key_exists('msg', $this->wechat_warning_queue_info) || !$this->wechat_warning_queue_info['msg']) {
            throw new \Exception('WechatWarningReportQueueTrait缺少本次要发送的内容msg');
        }

        if (!array_key_exists('corp_id', $this->wechat_warning_queue_info) || !$this->wechat_warning_queue_info['corp_id']) {
            throw new \Exception('WechatWarningReportQueueTrait缺少corp_id');
        }

        if (!array_key_exists('agent_id', $this->wechat_warning_queue_info) || !$this->wechat_warning_queue_info['agent_id']) {
            throw new \Exception('WechatWarningReportQueueTrait缺少agent_id');
        }

        if (!array_key_exists('secret', $this->wechat_warning_queue_info) || !$this->wechat_warning_queue_info['secret']) {
            throw new \Exception('WechatWarningReportQueueTrait缺少secret');
        }
    }
}
