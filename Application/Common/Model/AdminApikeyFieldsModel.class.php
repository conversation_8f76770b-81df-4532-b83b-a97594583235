<?php

namespace Common\Model;

class AdminApikeyFieldsModel extends BaseModel
{
    protected $connection = 'MYSQL_CRSAPI';
    protected $tablePrefix = '';

    public function getOutFields($apiKeyId)
    {
        $data = $this
            ->where(['apikeyid' => $apiKeyId, 'status' => 1])
            ->index('fid')
            ->field('fid')
            ->select();
        return $data;
    }

    public function updateInfo($apikeyId, $data)
    {
        $this->startTrans();
        try {
            $source = array_keys($this->getOutFields($apikeyId));
            $execute = true;
            //新增的变成不可用的字段
            if ($diff = implode(',', array_diff($source, $data))) {
                if (($this->where("fid in ($diff)")->save(['status' => 2])) === false) {
                    $execute = false;
                }
            }
            //新增的变成可用的字段
            if ($diff = array_diff($data, $source)) {
                $disable = $this
                    ->where(['apikeyid' => $apikeyId, 'status' => 2])
                    ->index('fid')
                    ->field('fid')
                    ->select();
                foreach ($diff as $fid) {
                    if (isset($disable[$fid])) {
                        if (($this->where("fid=$fid")->save(['status' => 1, 'updated_at' => time()])) === false) {
                            $execute = false;
                        }
                    } else {
                        if (($this->addInfo($apikeyId, [$fid])) === false) {
                            $execute = false;
                        }
                    }
                }
            }
            if ($execute === true) {
                $this->commit();
                return true;
            }
        } catch (\Exception $e) {
            $this->rollback();
            echo "Failed: " . $e->getMessage();
        }
    }


    public function updateFields($apikeyId, $data)
    {
        try {
            $source = array_keys($this->getOutFields($apikeyId));
            $execute = true;

            // tidy data
            if (!is_array($data)) {
                $data = (array) $data;
            }

            if (!is_array($source)) {
                $source = (array) $source;
            }

            //新增的变成不可用的字段
            if ($diff = implode(',', array_diff($source, $data))) {
                if (($this->where("fid in ($diff) and apikeyid=$apikeyId")->save(['status' => 2])) === false) {
                    $execute = false;
                }
            }

            //新增的变成可用的字段
            if ($diff = array_diff($data, $source)) {
                $disable = $this
                    ->where(['apikeyid' => $apikeyId, 'status' => 2])
                    ->index('fid')
                    ->field('fid')
                    ->select();
                foreach ($diff as $fid) {
                    if (isset($disable[$fid])) {
                        if (($this->where("fid=$fid and apikeyid=$apikeyId ")->save(['status' => 1, 'updated_at' => time()])) === false) {
                            $execute = false;
                        }
                    } else {
                        if (($this->addInfo($apikeyId, [$fid])) === false) {
                            $execute = false;
                        }
                    }
                }
            }
            if ($execute === true) {
                return true;
            }
        } catch (\Exception $e) {
            echo "Failed: " . $e->getMessage();
        }
    }
    public function addInfo($apiKeyId, $outFields)
    {
        $time = time();

        foreach ($outFields as $fid) {
            $param[] = [
                'apikeyid' => $apiKeyId,
                'fid' => $fid,
                'status' => 1,
                'created_at' => $time
            ];

        }

        return $this->addAll($param);
    }
}
