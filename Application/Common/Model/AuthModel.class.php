<?php

namespace Common\Model;

use Think\Cache\Driver\Redis;

/**
 * SP模型
 */
class AuthModel extends BaseModel
{
    // 默认协议
    protected $protocol_default_content = '&lt;h1 style=&quot;margin-top: 0px; font-weight: 600; text-align: center; font-size: 24px; color: rgb(0, 0, 0); line-height: 60px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;运营商授权协议&lt;/h1&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;此运营商授权协议（以下简称本协议）由您与北京羽乐创新科技有限公司（以下简称本公司/我们）签订。如您点击选择同意此协议将视为授权我们查看您的手机运营商信息，包括但不限于用户身份信息、是否实名认证、通话记录、手机账单等信息在内的各类信息，并将视为已阅读并立即本协议的全部内容。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;如果您不同意本协议的任一内容，或者无法准确理解本公司对条款的说明，请不要进行后续操作。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;一、	授权条款&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一） 您知道授权成功后不可撤销，并确认在您授权我们验证并获取您的手机运营商信息前，您已充分阅读、理解并接受本协议的全部内容，一旦您使用本服务，即表示您同意遵循本协议的所有约定。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二）您同意，在您授权我们验证并获取您的手机运营商信息后，我们有权查看并读取包括但不限于您的用户身份信息、是否实名认证、通话记录、手机账单等信息，同时对该等信息加以留存、分析、整理及加工。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（三）您同意授权本公司将上述信息提供给向您提供借款资信评审的机构，用于该机构判断您的资信水平和相关风险，从而决定相关产品与您的交易条件或者其他相关的决策。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（四）在为您提供服务的过程中，因某种原因导致本公司无法通过您留存的基本信息完成本次服务，您同意并授权本公司运用曾经（如有）为您提供服务时所留存的信息来完成本次服务。如果本公司留存的关于您的信息不足以支持本公司完成本次服务，本公司有权拒绝向您提供服务，并不承担由此给您造成的损失。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（五）上述条款中涉及的“留存、分析、整理及加工”包括但不限于对用户信息的内容和/或形式进行重新排序、结构化、格式化、标准化等方法。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（六）为更好地为您提供服务，您同意并授权本公司可与其合作的第三方进行联合研究，并可将通过本协议获得的您的信息投入到该等联合研究中。但本公司与其合作的第三方在开展上述联合研究前，应要求其合作的第三方对在联合研究中所获取的您的信息予以保密。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（七）您在本协议项下对本公司的授权将视为对本公司及本公司之关联公司的授权。本公司及本公司关联公司均可凭借您的授权及本协议约定执行相关操作。另外，在签署保密协议的情况下，您在本协议中的相关授权可延伸到本公司指定的第三方公司。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;二、	保密条款&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;本公司重视对用户隐私的保护。因收集您的信息是处于遵守国家法律法规的规定以及向您提供服务及提升服务质量的目的，我们对您的运营商信息承担保密义务，不会为满足第三方的一些目的而向其出售或出租您的任何信息，我们会在下列情况下才将您的手机运营商信息与第三方共享。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一） 获得您的同意或授权。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二） 为了向您提供或推荐服务、产品、或为了向您提供更完善的服务，或为了您拥有更广泛的社交体验，我们会与包括本公司旗下公司及合作商户在内的第三方共享您的相关信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（三） 某些情况下，只有共享您的信息，才能提供您需要的服务、产品，或处理您与他人的交易纠纷或争议。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（四） 某些服务、产品由我们的合作伙伴提供或由我们与合作伙伴、供应商共同提供，我们会与其共享所需的信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（五） 我们与第三方进行联合推广时，我们会与第三方共享为完成活动所需要的必要信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（六） 为维护本公司及关联公司、旗下公司和其他本公司用户的合法权益。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（七） 根据法律规定及合理商业习惯，在我们计划与其他公司合并或被其收购或进行其他资本市场活动（包括但不限于IPO，债券发行）时，以及其他情形下我们需要接受来自其他主体的尽职调查时，我们会把您的信息提供给必要的主体，但我们会通过和这些主体签署保密协议等方式要去其对您的个人信息采取合理的保密措施。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（八） 如您授权第三方向本公司查询、采集您的相关信息，我们有权在法律法规和您的授权许可范围内向第三方分析您的部分信息。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（九） 为了维护和改善我们的服务。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（十） 根据法律法规的规定或有权机关的要求。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（十一） 您理解并同意，我们可以保存您已经授权的原始信息；在您和我们的合作存续期间，我们随时可以重新采集和更新数据，对于经过加工和脱敏处理的数据，我们可以永久保存在服务器上；对于原始数据，在合作结束后，我们保留最长不超过180天。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;三、	用户义务及免责声明&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一）您保证，您使用自己的手机进行手机运营商信息的授权，授权的内容为您本人的信息，不可为他人信息授权。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二）如果您所授权的手机运营商信息为他人信息，我们将有权暂停或终止与您的全部或部分服务协议，并将由您承担由此行为所产生的全部法律责任，我们将不对此承担法律责任。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;四、	不可抗力条款&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;因台风、地震、海啸、洪水、战争、计算机病毒感染、黑客攻击、网络通信故障等不能预见、不能控制的不可抗力因素，造成本公司不能正常向您提供服务而可能导致的损失，本公司不承担责任。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;五、	特殊情形&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一） 您理解并同意，鉴于网络服务的特殊性，信息来源提供方随时可能变更、暂停、中止或者终止部分或全部的查询服务。本协议中的相关条款根据该变更而自动做相应修改，双方无须另行签订协议，本公司也无需就上述服务的变更、暂停、中止或者终止向您承担任何责任。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二） 本协议可在您接受本公司提供服务的过程中多次使用，未来为您提供服务时再次涉及到本协议服务内容时无需您另行签署。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;六、	知识产权&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;本公司是提供信息服务的第三方，与本软件相关的任何内容和资源（包括但不限于文字、图案、图表、色彩、动画、声音、页面设计）的知识产权均属于本公司所有，受《著作权法》、《商标法》《专利法》、《反不正当竞争法》及其他相关法律法规的保护。未经本公司书面明确许可，任何单位和个人不得以任何方式将平台之内容和相关资源作全部或部分复制、转载、引用、编辑和建立本手机客户端的镜像。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;七、	网络传输风险&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;您理解并同意，由于本协议所列服务涉及个人隐私，通过网络提供和传输存在特定的泄密风险，用户一经充分考虑到该风险，并愿意承担该风险通过网络的方式完成本项服务，如果因网络传输导致个人隐私泄露等后果，将由用户自行承担。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;八、	法律适用条款以及争议解决方式&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;本协议的解释、履行及争议的解决均适用中华人民共和国法律。在协议履行期间，凡由本协议引起的或与本协议有关的一切争议、纠纷，当事人应首先协商解决。协商不成，在本公司所在地法院提起诉讼。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.16rem; letter-spacing: 1px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;九、	附则&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（一）若本协议中的任何条文无论因何种原因完全或部分无效或不具有执行力，本协议的其他条款仍继续有效。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（二）本协议将使用电子签署的方式完成签署，自您点击“同意”或“勾选”并进行下一步操作后则协议生效。&lt;/p&gt;&lt;p style=&quot;margin-top: 0px; margin-bottom: 15px; font-size: 0.14rem; letter-spacing: 1px; text-indent: 24px; line-height: 20px; font-family: &amp;quot;microsoft yahei&amp;quot;;&quot;&gt;（三）您在本协议项下对本公司的授权将视为对本公司及本公司之关联公司的授权。本公司及本公司关联公司均可凭借您的授权及本协议约定执行相关操作。&lt;/p&gt;';

    public function getStatusLists()
    {
        return array(
            '1' => '可用',
            '2' => '禁用',
        );
    }

    public function getAllSp()
    {
        return $this->index('id')->select();
    }

    public function getSpById($id)
    {
        return $this->find($id);
    }

    protected function getSpByEmail($email)
    {
        return $this->field('id')->where(['email' => $email])->find();
    }

    public function setAccount($data)
    {
        $id = $data['id'];

        // check important fields
        $important_field_change = $this->checkEdit($data, $id);

        // update account with modified token
        $account_result = $this
            ->where("id=$id")
            ->save($data);

        if ($account_result === false) {
            throw new \Exception('更新账户失败, 请刷新后重试');
        }

        // tidy redis cache
        if ($data['status'] != 1) {
            $obj_redis = new Redis(C('REDIS_CRAWLER'));
            $key_redis = 'app_client_info_' . $id;

            if ($obj_redis->exists($key_redis)) {
                $obj_redis->del($key_redis);
            }

            return $account_result;
        }
        // check important fields
        if (!$important_field_change) {
            return $account_result;
        }

        return true;
    }

    /**
     * gen a account
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function addAccount($data)
    {

        // add account
        $account_result = $this->add($data);

        if ($account_result === false) {
            throw new \Exception('添加账户失败，请稍后重试');
        }

        // just return without no token field
        if ($data['source'] != 'ui' || $data['status'] != 1) {
            return $account_result;
        }

        // gen token
        $tokenList = $this->getToken($data['appid'], $data['appsecret']);
        $tokenList = json_decode($tokenList, true);

        if ($tokenList['status'] != 0) {
            throw new \Exception($tokenList['msg']);
        }

        $token = $tokenList['data']['token'];

        // save token
        $save_result = $this->where(['id' => $account_result])->save(['token' => $token]);

        if ($save_result === false) {
            $this->where(['id' => $account_result])->save(['status' => 2]);
            throw new \Exception('账户保存token失败, 请在编辑模式完成账号的填充');
        }

        return $account_result;
    }

    /**
     * filter params
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function filterData($data)
    {
        // init params
        $id = isset($data['id']) ? intval($data['id']) : '';
        $data['token_due_date'] = (isset($data['token_due_date']) && $data['token_due_date']) ? (strtotime($data['token_due_date']) + 86399) : 0;
        $data['expiration_date'] = (isset($data['expiration_date']) && $data['expiration_date']) ? (strtotime($data['expiration_date']) + 86399) : 0;

        // check protocol_content
        $data['protocol_content'] = trim($data['protocol_content']);
        if ($data['source'] == 'ui' && $data['protocol_default'] == 1) {
            $data['protocol_content'] = $this->protocol_default_content;
        }

        // wangEditor 不传入任何参数的的时候回向textarea填充<p><br></p>的html实体
        if ($data['source'] == 'ui' && $data['protocol_content'] == '&lt;p&gt;&lt;br&gt;&lt;/p&gt;') {
            throw new \Exception('请填写授权协议');
        }

        // check residential_address 该字段不再使用,兼容问题 也不需要考虑吗?和丹丹进行联调）
        if ($data['source'] == 'api' || $data['contactor_page'] == 'N') {
            $data['residential_address'] = '';
        }

        // set default value when source = ui and  contactor_page=
        if ($data['source'] == 'ui' && $data['contactor_page'] == 'N') {
            $data['emergency_contact_max_number'] = null;
            $data['emergency_contact_detail_limits'] = null;

            // 兼容杂乱的数据
            if (isset($data['relationship'])) {
                unset($data['relationship']);
            }
            if (isset($data['emergency_contact_limit_number'])) {
                unset($data['emergency_contact_limit_number']);
            }
        }

        // check emergency_contact_max_number
        if (isset($data['emergency_contact_max_number']) && $data['emergency_contact_max_number']) {
            $data['emergency_contact_max_number'] = (int)$data['emergency_contact_max_number'];
            if (!is_numeric($data['emergency_contact_max_number']) || $data['emergency_contact_max_number'] < 1 || $data['emergency_contact_max_number'] > 15) {
                throw new \Exception('最多紧急联系人数量必须是1到15之间的正整数');
            }
        }

        // check emergency_contact_detail_limits
        if (isset($data['relationship'])) {

            // serialize for info
            $emergency_contact_detail_limits = [];
            $limit_times_sum = 0;
            foreach ($data['relationship'] as $key => $relationship) {
                $data['emergency_contact_limit_number'][$key] = (int)$data['emergency_contact_limit_number'][$key];
                if ($data['emergency_contact_limit_number'][$key] < 1) {
                    throw new \Exception('必填紧急联系人数量限制必须是正整数');
                }

                $limit_times_sum += $data['emergency_contact_limit_number'][$key];
                if (!isset($emergency_contact_detail_limits[$relationship])) {
                    $emergency_contact_detail_limits[$relationship] = $data['emergency_contact_limit_number'][$key];
                    continue;
                }
                $emergency_contact_detail_limits[$relationship] += $data['emergency_contact_limit_number'][$key];
            }

            // compare emergency_contact_limit_number and emergency_contact_max_number
            if ($limit_times_sum > $data['emergency_contact_max_number']) {
                throw new \Exception('必填紧急联系人数量必须不大于最多紧急联系人数量');
            }

            // check 配偶
            if (isset($emergency_contact_detail_limits['配偶']) && $emergency_contact_detail_limits['配偶'] > 1) {
                throw new \Exception('配偶只可以配置成1');
            }

            $data['emergency_contact_detail_limits'] = serialize($emergency_contact_detail_limits);
            unset($data['relationship']);
            unset($data['emergency_contact_limit_number']);
        }

        // check effective_authorization_time
        if ($data['source'] == 'api') {
            $data['effective_authorization_time'] = '-1';
        } else {
            $data['effective_authorization_time'] = (int)$data['effective_authorization_time'];
            if (!is_numeric($data['effective_authorization_time'])) {
                throw new \Exception('H5授权链接失效限制时间必须是1到36的整数');
            }

            if (!($data['effective_authorization_time'] <= 36 && $data['effective_authorization_time'] > 0) && $data['effective_authorization_time'] != -1) {
                throw new \Exception('H5授权链接失效限制时间必须是1到36的整数');
            }
        }

        // check output_report
        if (isset($data['output_report']) && $data['output_report']) {
            array_map(function ($output_report) {
                if (!ctype_digit($output_report)) {

                    throw new \Exception('运营商报告输出内容有误, 请刷新后重试');
                }
            }, $data['output_report']);

            $data['output_report'] = implode(',', $data['output_report']);
        } else {
            $data['output_report'] = '';
        }

        // gen password
        if (!$id) {
            $data['password'] = md5(C('USER_PWD_SALT') . md5(C('USER_DEFAULT_PASSWORD')));
        }

        // check cuishou
        if ($data['need_dunning'] == 1) {

            if (!isset($data['service_key']) || !$data['service_key']) {
                throw  new \Exception('催收分APIKEY不可以为空');
            }

            if (!isset($data['service_secret']) || !$data['service_secret']) {
                throw  new \Exception('催收分PASSWORD不可以为空');
            }
        }

        // check developer
        if (!isset($data['developer']) || !$data['developer']) {
            throw new \Exception('请输入客户名称');
        }

        // check email
        if (!isset($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('邮箱格式不正确');
        }

        $where_email = "email='{$data['email']}'";
        $where_email .= $id ? " AND id!=$id " : "";

        if ($this->where($where_email)->count() > 0) {
            throw new \Exception('邮箱已经被占用, 请输入新的邮箱');
        }

        // check status
        if (!in_array($data['status'], [1, 2])) {
            throw new \Exception('状态码不正确, 请刷新后重试一下');
        }

        // check need_dunning status
        if (!in_array($data['need_dunning'], [1, 2])) {
            throw new \Exception('状态码不正确, 请刷新后重试一下');
        }

        // check appid
        if (!isset($data['appid']) || !$data['appid']) {
            throw new \Exception('APPID必填');
        }

        $where_appid = "appid='{$data['appid']}'";
        $where_appid .= $id ? " AND id!=$id " : "";

        if ($this->where($where_appid)->count() > 0) {
            throw new \Exception('APPID重复, 请重新生成');
        }

        // check appsecret
        if (!isset($data['appsecret']) || !$data['appsecret']) {
            throw new \Exception('APPSECRET必填');
        }

        $where_appsecret = "appsecret='{$data['appsecret']}'";
        $where_appsecret .= $id ? " AND id!=$id " : "";

        if ($this->where($where_appsecret)->count() > 0) {
            throw new \Exception('APPSECRET重复, 请重新生成');
        }

        // check notify_url report_notify_url close_redirect_url authorize_notify_url
        if (isset($data['notify_url']) && $data['notify_url'] && !filter_var($data['notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('推送地址必须为合法网络地址！');
        }

        if (isset($data['report_notify_url']) && $data['report_notify_url'] && !filter_var($data['report_notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('报告状态回调地址必须为合法网络地址！');
        }

        if (isset($data['close_redirect_url']) && $data['close_redirect_url'] && !filter_var($data['close_redirect_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('重定向地址必须为合法网络地址！');
        }

        if (isset($data['authorize_notify_url']) && $data['authorize_notify_url'] && !filter_var($data['authorize_notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('授权状态回调地址必须为合法网络地址！');
        }

        // add suffix when authorize_notify_url is same to report_notify_url
        if ($data['authorize_notify_url'] && $data['authorize_notify_url'] == $data['report_notify_url']) {
            if (strpos($data['authorize_notify_url'], '?') === false) {
                $data['authorize_notify_url'] .= '?yulore_flag=auth';
                $data['report_notify_url'] .= '?yulore_flag=report';
            } else {
                $data['authorize_notify_url'] .= '&yulore_flag=auth';
                $data['report_notify_url'] .= '&yulore_flag=report';
            }
        }

        // check token_due_date
        if ($data['source'] == 'api') {
            $data['contactor_page'] = 'N';
        }

        if (($data['source'] == 'ui') && ($data['token_due_date'] <= time()) && !isset($data['id'])) {
            throw new \Exception('授权日期有误');
        }

        // check expiration_date
        if (($data['expiration_date'] <= time()) && !isset($data['id'])) {
            throw new \Exception('账号截止日期有误');
        }

        if ($data['source'] == 'ui' && $data['token_due_date'] != $data['expiration_date']) {
            throw new \Exception('账号截至日期必须等于授权时间');
        }

        return $data;
    }


    /**
     *  检查数据(除去h5配置的部分)
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function filterDataNew($data)
    {
        // init params
        $data = $this->initParams($data);

        // check appid && appsecret
        $id = isset($data['id']) ? intval($data['id']) : '';
        $this->checkAppidUnique($data['appid'], $id);
        $this->checkAppsecretUnique($data['appsecret'], $id);

        // check url && date && output_report && cuishou services
        $this->checkUrl($data);
        $this->checkDte($data);
        $this->checkReportOutput($data);
        $this->checkCuishouServices($data);

        return $data;
    }

    /**
     * 初始化参数
     * @param array $data
     * @return array
     * @throws \Exception
     */
    protected function initParams($data)
    {
        // 检查基本的元素
        $data = $this->checkBaseInfo($data);

        // 目前的动作时创建产品的时候， 需要进行一些默认注入数据
        return $this->initDefaultValueForCreate($data);
    }

    /**
     * 目前的动作时创建产品的时候， 需要进行一些默认注入数据
     * @param $data
     * @return array
     */
    protected function initDefaultValueForCreate($data)
    {
        // 如果是编辑模式  则不需要注入默认值
        $id = isset($data['id']) ? intval($data['id']) : '';
        if ($id){
            return $data;
        }

        // 注入密码
        $data['password'] = md5(C('USER_PWD_SALT') . md5(C('USER_DEFAULT_PASSWORD')));

        // 注入h5接入的时候的默认配置
        return $this->writeDefaultH5Config($data);
    }

    /**
     * 注入默认的h5配置
     * @param $data
     * @return  array
     */
    protected function writeDefaultH5Config($data)
    {
        // 如果当前是API 则无注入（其实是在其他地方注入的）
        if ($data['source'] == 'api') {
            return $data;
        }

        // 如果是h5配置,且没有联系人页面
        if ($data['contactor_page'] == 'N') {
            return  $this->writeInfoWithoutContactorPage($data);
        }

        return $this->writeInfoWithContactorPage($data);
    }

    /**
     * h5接入 && 不需要联系人页面
     * @param $data
     * @return mixed
     */
    private function writeInfoWithoutContactorPage($data)
    {
        // H5授权链接失效限制时间,不限制
        $data['effective_authorization_time'] = -1;

        // 默认协议
        $data['protocol_default'] = 1;
        $data['protocol_content'] = '';
        return $data;
    }


    /**
     * h5接入 && 需要联系人页面
     * @param $data
     * @return array
     */
    protected function writeInfoWithContactorPage($data)
    {
        // H5授权链接失效限制时间,不限制
        $data['effective_authorization_time'] = -1;

        // 默认协议
        $data['protocol_default'] = 1;
        $data['protocol_content'] = '';

        // 最多紧急联系人数量
        $data['emergency_contact_max_number'] = 0;
        $data['emergency_contact_detail_limits'] = 'a:1:{s:9:"无限制";i:0;}';

        // 申请人信息配置
        $data['ui_proposer_required_fields'] = '姓名,身份证号,手机号码';
        $data['ui_proposer_show_fields'] = '姓名,身份证号,手机号码';
        return $data;
    }

    /**
     * 检查基本的元素
     * @param array $data
     * @return array
     * @throws \Exception
     */
    protected function checkBaseInfo($data)
    {
        // 时间转化
        $data['token_due_date'] = (isset($data['token_due_date']) && $data['token_due_date']) ? (strtotime($data['token_due_date']) + 86399) : 0;
        $data['expiration_date'] = (isset($data['expiration_date']) && $data['expiration_date']) ? (strtotime($data['expiration_date']) + 86399) : 0;

        // check residential_address 该字段不再使用,兼容问题 也不需要考虑吗?和丹丹进行联调）
        if ($data['source'] == 'api' || $data['contactor_page'] == 'N') {
            $data['residential_address'] = '';
        }
        if ($data['source'] == 'api') {
            $data['contactor_page'] = 'N';
        }

        // check status
        if (!in_array($data['status'], [1, 2])) {
            throw new \Exception('状态码不正确, 请刷新后重试一下');
        }
        if (!in_array($data['need_dunning'], [1, 2])) {
            throw new \Exception('状态码不正确, 请刷新后重试一下');
        }
        if (!isset($data['developer']) || !$data['developer']) {
            throw new \Exception('请输入客户名称');
        }
        return $data;
    }

    /**
     * 检查催收服务
     * @param $data
     * @throws \Exception
     */
    protected function checkCuishouServices($data)
    {
        if ($data['need_dunning'] == 1) {
            if (!isset($data['service_key']) || !$data['service_key']) {
                throw  new \Exception('催收分APIKEY不可以为空');
            }

            if (!isset($data['service_secret']) || !$data['service_secret']) {
                throw  new \Exception('催收分PASSWORD不可以为空');
            }
        }
    }

    /**
     * 检查报告输出字段
     * @param $data
     */
    protected function checkReportOutput(&$data)
    {
        if (isset($data['output_report']) && $data['output_report']) {
            array_map(function ($output_report) {
                if (!ctype_digit($output_report)) {
                    throw new \Exception('运营商报告输出内容有误, 请刷新后重试');
                }
            }, $data['output_report']);

            $data['output_report'] = implode(',', $data['output_report']);
        } else {
            $data['output_report'] = '';
        }
    }


    /**
     * 检查授权日期 && 账号截止日期
     * @param $data
     * @throws \Exception
     */
    protected function checkDte($data)
    {
        if (($data['source'] == 'ui') && ($data['token_due_date'] <= time()) && !isset($data['id'])) {
            throw new \Exception('授权日期有误');
        }

        if (($data['expiration_date'] <= time()) && !isset($data['id'])) {
            throw new \Exception('账号截止日期有误');
        }

        if ($data['source'] == 'ui' && $data['token_due_date'] != $data['expiration_date']) {
            throw new \Exception('账号截至日期必须等于授权时间');
        }
    }


    /**
     * 检查授权状态回调地址 && 报告状态回调地址 && 详单推送地址 && 重定向地址 && 重定向地址白名单
     * @param  array $data POST传递进来的信息
     * @throws \Exception
     */
    protected function checkUrl(&$data)
    {
        // check notify_url report_notify_url close_redirect_url authorize_notify_url
        if (isset($data['notify_url']) && $data['notify_url'] && !filter_var($data['notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('推送地址必须为合法网络地址！');
        }

        if (isset($data['close_redirect_url']) && $data['close_redirect_url'] && !filter_var($data['close_redirect_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('重定向地址必须为合法网络地址！');
        }

        if (isset($data['redirect_url_domain']) && !empty($data['redirect_url_domain'])){
            //支持输入多个地址，使用英文“;”分隔
            $redirect_url_domain = explode(';', $data['redirect_url_domain']);
            foreach ($redirect_url_domain as $item){
                if (empty($item)){
                    throw new \Exception('重定向地址白名单格式不正确！');
                }
            }
        }


        if (isset($data['report_notify_url']) && $data['report_notify_url'] && !filter_var($data['report_notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('报告状态回调地址必须为合法网络地址！');
        }

        if (isset($data['authorize_notify_url']) && $data['authorize_notify_url'] && !filter_var($data['authorize_notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('授权状态回调地址必须为合法网络地址！');
        }

        // add suffix when authorize_notify_url is same to report_notify_url
        if ($data['authorize_notify_url'] && $data['authorize_notify_url'] == $data['report_notify_url']) {
            if (strpos($data['authorize_notify_url'], '?') === false) {
                $data['authorize_notify_url'] .= '?yulore_flag=auth';
                $data['report_notify_url'] .= '?yulore_flag=report';
            } else {
                $data['authorize_notify_url'] .= '&yulore_flag=auth';
                $data['report_notify_url'] .= '&yulore_flag=report';
            }
        }
    }

    /**
     * 检查appsecret是不是唯一的
     * @param $appsecret
     * @param $id
     * @throws \Exception
     */
    protected function checkAppsecretUnique($appsecret, $id)
    {
        if (!isset($appsecret) || !$appsecret) {
            throw new \Exception('APPSECRET必填');
        }

        $where_appsecret = "appsecret='{$appsecret}'";
        $where_appsecret .= $id ? " AND id!=$id " : "";

        if ($this->where($where_appsecret)->count() > 0) {
            throw new \Exception('APPSECRET重复, 请重新生成');
        }
    }

    /**
     * 检查appid是不是唯一的
     * @param $appid
     * @param $id
     * @throws \Exception
     */
    protected function checkAppidUnique($appid, $id)
    {
        // check appid
        if (!isset($appid) || !$appid) {
            throw new \Exception('APPID必填');
        }

        $where_appid = "appid='{$appid}'";
        $where_appid .= $id ? " AND id!=$id " : "";

        if ($this->where($where_appid)->count() > 0) {
            throw new \Exception('APPID重复, 请重新生成');
        }
    }

    /**
     * @param $lists
     * @param int $id
     * @return int|mixed
     * @throws \Exception
     */
    public function setinfo($lists, $id = 0)
    {

        // check cuishou  too
        if ($lists['need_dunning'] == 1) {
            if (!isset($lists['service_key']) || !$lists['service_key']) {
                throw  new \Exception('催收分APIKEY不可以为空');
            }
            if (!isset($lists['service_secret']) || !$lists['service_secret']) {
                throw  new \Exception('催收分PASSWORD不可以为空');
            }
        }
        if (isset($lists['service_secret'])) {
            unset($lists['service_secret']);
        }
        if (isset($lists['service_key'])) {
            unset($lists['service_key']);
        }

        // check  auth
        if (empty($lists['email']) || !filter_var($lists['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('邮箱为空或格式不正确');
        }
        if (!$id && $this->getSpByEmail($lists['email'])) {
            throw new \Exception('邮箱已经被占用');
        }
        if (empty($lists['appsecret'])) {
            throw new \Exception('APPSECRET必填');
        }
        if (isset($lists['token_due_date']) && (false !== strrpos($lists['token_due_date'], '/') || false !== strrpos($lists['token_due_date'], '-'))) {
            $lists['token_due_date'] = strtotime($lists['token_due_date'] . ' 23:59:59 ');
        } else {
            $lists['token_due_date'] = 0;
        }
        if (isset($lists['expiration_date']) && (false !== strrpos($lists['expiration_date'], '/') || false !== strrpos($lists['expiration_date'], '-'))) {
            $lists['expiration_date'] = strtotime($lists['expiration_date'] . ' 23:59:59 ');
        }
        $status_lists = $this->getStatusLists();
        if (!isset($status_lists[$lists['status']])) {
            throw new \Exception('状态不正确');
        }
        if (!isset($status_lists[$lists['need_dunning']])) {
            throw new \Exception('是否输出催收参数不正确');
        }
        if (empty($lists['appid'])) {
            throw new \Exception('APPID必填');
        }
        if (empty($id) && $this->where(array('appid' => $lists['appid']))->count() > 0) {
            throw new \Exception('APPID重复 请重新生成');
        }
        if (isset($lists['notify_url']) && !empty($lists['notify_url'])
            && !filter_var($lists['notify_url'], FILTER_VALIDATE_URL)
        ) {
            throw new \Exception('推送地址必须为合法网路地址！');
        }
        if (isset($lists['close_redirect_url']) && !empty($lists['close_redirect_url'])
            && !filter_var($lists['close_redirect_url'], FILTER_VALIDATE_URL)
        ) {
            throw new \Exception('重定向地址必须为合法网路地址！');
        }
        if ($lists['source'] == 'api') {
            $lists['token_due_date'] = 0;
            $lists['contactor_page'] = 'N';
        }
        if ($lists['source'] == 'ui' && $lists['token_due_date'] <= time()) {
            throw new \Exception('授权时间有误');
        }
        if ($lists['expiration_date'] <= time()) {
            throw new \Exception('账号截止日期有误');
        }

        if ($lists['source'] == 'ui' && $lists['token_due_date'] > $lists['expiration_date']) {
            throw new \Exception('授权时间必须小于等于账号截至日期');
        }
        $lists = array_filter($lists);

        if ($id) {
            $checkFill = $this->field('password,created_at,email,status')->where(['id' => $id])->find();
            if (empty($checkFill['password'])) {
                $lists['password'] = md5(C('USER_PWD_SALT') . md5(C('USER_DEFAULT_PASSWORD')));
            }
            if (empty($checkFill['created_at'])) {
                $lists['created_at'] = time();
            }
            if ($this->getSpByEmail($lists['email']) && $lists['email'] != $checkFill['email']) {
                throw new \Exception('邮箱已经被占用');
            }
            $lists['token_due_date'] = empty($lists['token_due_date']) ? 0 : $lists['token_due_date'];
            $lists['need_report'] = empty($lists['need_report']) ? 0 : $lists['need_report'];
            $lists['updated_at'] = time();
            $whChnage = ($lists['source'] == 'ui') ? ($this->checkEdit($lists, $id)) : '';
            $save_result = $this->where(array('id' => $id))->data($lists)->save();
            if ($lists['source'] == 'ui' && $lists['status'] == 1) {
                $token = $this->field('token')->where(['id' => $id])->find();
                //token为空 或者关键字段的数据有变化需要更新token
                if (empty($token['token']) || $whChnage) {
                    if ($lists['status'] == 1 && $lists['status'] != $checkFill['status']) {
                        $this->where(['id' => $id])->save(['status' => 1]);
                    }
                    $tokenList = $this->getToken($lists['appid'], $lists['appsecret']);
                    $tokenList = json_decode($tokenList, true);
                    if ($tokenList['status'] != 0) {
                        throw new \Exception($tokenList['msg']);
                    }
                    $token = $tokenList['data']['token'];
                    $save_result = $this->where(['id' => $id])->data(['token' => $token])->save();
                }
            }
            return $save_result;
        } else {
            $lists['password'] = md5(C('USER_PWD_SALT') . md5(C('USER_DEFAULT_PASSWORD')));
            $lists['created_at'] = time();
            $id = $this->data($lists)->add();
            if (!$id) {
                throw new \Exception('数据库插入错误');
            }

            if ($lists['source'] == 'ui' && $lists['status'] == 1) {
                $tokenList = $this->getToken($lists['appid'], $lists['appsecret']);
                $tokenList = json_decode($tokenList, true);
                if ($tokenList['status'] != 0) {
                    $this->where(['id' => $id])->data(['status' => 2])->save();
                    throw new \Exception($tokenList['msg']);
                }

                $token = $tokenList['data']['token'];
                $this->where(['id' => $id])->data(['token' => $token])->save();
            }
            return $id;
        }
    }

    public function getToken($appid, $appsecret)
    {
        // tidy url and url params
        $url = C('CRS_API_CONFIG')['getToken'];
        $time = time();
        $sign = md5($appid . $appsecret . $time);
        $param = ['appid' => $appid, 'sign' => $sign, 'time' => $time];
        $url .= '?' . http_build_query($param);
        // 最多请求3次
        $response = false;
        for ($i = 0; $i < 3; $i++) {
            $response = $this->curlRequest($url);
            if ($response) {
                break;
            }
        }
        if ($i == 3 && $response == false) {
            throw new \Exception('刷新token失败, 请稍后再试');
        }
        return $response;
    }

    protected function curlRequest($url)
    {
        // request
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        // 在尝试连接时等待的秒数
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);
        // 最大执行时间
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);
        $tokenList = curl_exec($curl);
        if (curl_errno($curl)) {
            return false;
        }
        curl_close($curl);
        return $tokenList;
    }

    /**
     * 获取用户的信息
     * @param string $type all 获取所有的用户
     * @param null $id
     * @param null $offset 偏移量
     * @param null $limit 每页要显示的数量
     * @return mixed
     */
    public function getUserInfo($type = null, $id = null, $offset = null, $limit = null)
    {

        $where = " (status=1 || apikeyid is not null) ";
        //选择了单一的用户
        if ($id && !$type) {
            $where .= " and id=$id ";
        }
        $obj = $this->field('id,developer,apikeyid')
            ->where($where)
            ->order('id desc');
        if (!empty($limit) && !$id) {
            $obj = $obj->limit($offset, $limit);
        }
        $userInfo = $obj->select();

        return $userInfo;
    }

    /**
     * 检查编辑模式传递的数据是不是有变化,如果字段发生变化true否则false
     * @param $lists
     * @param $id
     * @return array
     */
    protected function checkEdit($lists, $id)
    {
        unset($lists['updated_at']);

        // don't check created_at
        if (isset($lists['created_at'])) {
            unset($lists['created_at']);
        }

        // service_key and service_secret aren't stored in redis so don't check them
        if (isset($lists['service_key'])) {
            unset($lists['service_key']);
        }

        if (isset($lists['service_secret'])) {
            unset($lists['service_secret']);
        }

        $fields = implode(',', array_keys($lists));
        $source = $this->field($fields)
            ->find($id);
        return array_diff_assoc($lists, $source);
    }

    /**
     * 更新token
     * @param $user_info
     * @return bool
     * @throws \Exception
     */
    public function updateToken($user_info)
    {
        //  update account with modified token
        $tokenList = $this->getToken($user_info['appid'], $user_info['appsecret']);
        $tokenList = json_decode($tokenList, true);

        if ($tokenList['status'] != 0 && $tokenList['status'] != 3997) {
            throw new \Exception($tokenList['msg']);
        }
        if ($user_info['source'] == 'api') {
            return true;
        }

        // save token
        $token = $tokenList['data']['token'];
        $token_result = $this->where(['id' => $user_info['id']])->save(['token' => $token]);
        if ($token_result === false) {
            throw new \Exception('保存token失败, 请刷新后重试');
        }
        return true;
    }
}
