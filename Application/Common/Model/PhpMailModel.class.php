<?php

namespace Common\Model;

use Org\Mailer\PHPMailer;

class PhpMailModel
{

    /**
     * 获取邮箱实例
     * @param $config
     * @return PHPMailer
     * @throws \Exception
     */
    public function getMailInstance($config)
    {
        try {
            $this->verifyConfig($config);

            $mail = new PHPMailer(true);                // Passing `true` enables exceptions
            $mail->SMTPDebug = 0;                                 // Enable verbose debug output
            $mail->isSMTP();                                      // Set mailer to use SMTP
            $mail->Host = $config['host'];                      // Specify main and backup SMTP servers
            $mail->SMTPAuth = true;                               // Enable SMTP authentication
            $mail->Username = $config['username'];                 // SMTP username
            $mail->Password = $config['password'];                           // SMTP password
            $mail->SMTPSecure = $config['smtp_secure'];                            // Enable TLS encryption, `ssl` also accepted
            $mail->Port = $config['port'];                                    // TCP port to connect to
            $mail->CharSet = 'utf8';

            // 设置语言
            $mail->setLanguage('zh_cn', SYSTEM_PATH . 'statics/mailer_language/');
            return $mail;

        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 检查配置
     * @param $config
     * @throws \Exception
     */
    protected function verifyConfig($config)
    {
        //  'username', 'password', 'smtp_secure', 'port'
        if (!array_key_exists('host', $config) || trim($config['host']) == '') {
            throw new \Exception('请检查邮箱的配置文件，缺少合法的host配置');
        }

        if (!array_key_exists('username', $config) || trim($config['username']) == '') {
            throw new \Exception('请检查邮箱的配置文件，缺少合法的username配置');
        }

        if (!array_key_exists('password', $config) || trim($config['password']) == '') {
            throw new \Exception('请检查邮箱的配置文件，缺少合法的password配置');
        }

        if (!array_key_exists('smtp_secure', $config) || trim($config['smtp_secure']) == '') {
            throw new \Exception('请检查邮箱的配置文件，缺少合法的smtp_secure配置');
        }

        if (!array_key_exists('port', $config) || trim($config['port']) == '') {
            throw new \Exception('请检查邮箱的配置文件，缺少合法的port配置');
        }
    }
}
