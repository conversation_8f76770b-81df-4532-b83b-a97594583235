<?php

namespace Common\Model;

class FinanceAccountProductModel extends BaseModel
{
    protected $connection = 'DB_FINANCE';
    protected $tablePrefix = '';

    /**
     * 查询产品关联的用户信息
     * @param $product_id
     * @param $type_id
     */
    public function getAccountIdByProductId($product_id, $type_id=2){
        $where = ['product_id'=>$product_id, 'type_id'=>$type_id];

        $ret = $this->where($where)->select();
        return isset($ret[0]) ? $ret[0] : [];
    }

    /**
     * 批量查询产品关联的用户信息
     * @param $product_id
     * @param $type_id
     */
    public function getAccountIdsByProductIds($product_ids, $type_id=2){
        if (empty($product_ids)){
            return [];
        }
        $where = ['product_id'=>['in', $product_ids], 'type_id'=>$type_id];
        $account_product_lists =  $this->where($where)->select();
        if (empty($account_product_lists)){
            return [];
        }

        $where = ['id'=>['in', array_unique(array_column($account_product_lists, 'account_id'))]];
        $account_info = D('FinanceAccounts')->where($where)->field('id,name')->select();
        $account_info = array_column($account_info, null, 'id');
        //追加用户信息
        foreach ($account_product_lists as $key=>$item){
            $account_name = '';
            if ($item['account_id'] && !empty($account_info[$item['account_id']])){
                $account_name = $account_info[$item['account_id']]['name'];
            }
            $account_product_lists[$key]['account_name'] = $account_name;
        }
        return array_column($account_product_lists, null, 'product_id');
    }

    /**
     * 添加邦秒爬产品时，绑定所属客户
     * @param $product_id
     * @param $account_name
     */
    public function bindToAccounts($product_id, $account_name, $type_id=2){
        if (empty($product_id) || empty($account_name)){
            throw new \Exception('所属客户或所选产品ID为空');
        }

        $where['name'] = $account_name;
        $finance_accounts = D('FinanceAccounts')->where($where)->select();
        if (empty($finance_accounts) || empty($finance_accounts[0]['id'])){
            throw new \Exception('关联的客户不存在');
        }
        $account_id = $finance_accounts[0]['id'];

        $data = [];
        $data['account_id'] = $account_id;
        $data['product_id'] = $product_id;
        $data['type_id'] = $type_id;//邦秒配单号版
        $data['created_at'] = time();
        $data['updated_at'] = time();

        //保存产品所属客户关联信息
        $this->startTrans();
        try {
            $result = $this->add($data);
            if ($result !== false) {
                $this->commit();
                return $result;
            }
        } catch (\Exception $e) {
            $this->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 编辑账号时，判断绑定的所属客户是否已经关联了其他产品
     * @param $name
     */
    public function updateBindToAccounts($product_id, $account_name, $type_id=2){
        //检查当前的客户是否关联了当前产品类型的其他产品
        $where = [
            'product_id'=>$product_id,
            'type_id'=>$type_id
        ];
        $account_product_info = $this->where($where)->select();
        if (empty($account_name) && empty($account_product_info[0])){
            return [];
        }
        //更新产品所属客户关联信息
        $this->startTrans();
        try {
            if (empty($account_name)){
                $result = $this->where(['id'=>$account_product_info[0]['id']])->delete();
                if ($result !== false) {
                    $this->commit();
                    return $result;
                }
            }else {
                $where['name'] = $account_name;
                $finance_accounts = D('FinanceAccounts')->where($where)->select();
                if (empty($finance_accounts) || empty($finance_accounts[0]['id'])) {
                    throw new \Exception('关联的客户不存在');
                }
                $account_id = $finance_accounts[0]['id'];

                if (empty($account_product_info[0]['id'])){//新增
                    $data = [
                        'account_id' => $account_id,
                        'product_id' => $product_id,
                        'type_id' => $type_id,
                        'created_at' => time(),
                        'updated_at' => time()
                    ];
                    $result = $this->add($data);
                }else{//更新
                    $data = [
                        'account_id' => $account_id,
                        'updated_at' => time()
                    ];
                    $result = $this->where(['id=' . $account_product_info[0]['id']])->save($data);
                }
                if ($result !== false) {
                    $this->commit();
                    return $result;
                }
            }
        } catch (\Exception $e) {
            $this->rollback();
            throw new \Exception($e->getMessage());
        }
    }
}