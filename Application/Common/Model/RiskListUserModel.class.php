<?php
/**
 * 风险名单mongo
 * @Author: lidandan
 * @Date:   2018-06-28 14:41:27
 */
namespace Common\Model;

use Think\Model\MongoModel;

class RiskListUserModel extends MongoModel
{
    protected $connection = 'RISK_LIST_MONGO';
    protected $dbName = 'blacklist';
    protected $tableName = 'user';
    protected $tablePrefix = 'blacklist_';

    public function updateUser($data, $where, $options = ['w' => 0])
    {
        return $this->getCollection()->update($where, ['$set' => $data], $options);
    }
}