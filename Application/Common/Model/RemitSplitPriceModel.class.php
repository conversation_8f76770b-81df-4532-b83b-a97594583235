<?php
namespace Common\Model;

use Think\Model;

class RemitSplitPriceModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'remit_split_price';
    protected $tablePrefix = '';

    /**
     *
     * @param $where
     * @param string $field
     * @return mixed
     */
    public function gettListByWhere($where, $field='*') {
        return $this->where($where)->field($field)->select();
    }
}