<?php
namespace Common\Model;

class FeeStatModel extends BaseModel
{
    protected $connection = 'DB_FINANCE';
    protected $tablePrefix = '';

    /**
     * 获取计费方式
     * @return array
     */
    public function getFeeMethod()
    {
        return [
            1 => '按时间',
            2 => '按用量'
        ];
    }

    /**
     * 获取时间计费规则
     * @return array
     */
    public function getFeeTimeRule()
    {
        return [
            1 => '包日',
            2 => '包月',
            3 => '包年'
        ];
    }

    /**
     * 获取用量计费规则
     * @return array
     */
    public function getFeeAmountRule()
    {
        return [
            1 => '固定价格',
            2 => '累进阶梯',
            3 => '到达阶梯',
        ];
    }

    /**
     * 获取阶梯计费规则
     * @return array
     */
    public function getFeeStepRule()
    {
        return [
            1 => '日',
            2 => '月',
            3 => '年',
            4 => '无周期'
        ];
    }
}