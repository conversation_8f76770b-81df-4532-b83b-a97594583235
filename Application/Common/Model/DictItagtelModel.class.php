<?php

namespace Common\Model;

class DictItagtelModel extends BaseModel
{
    protected $connection = 'MYSQL_CRSAPI';
    protected $tablePrefix = '';
    protected $tableName = 'dict_itagtel';

    /*
     * 单次入库电话号码的限量
     * */
    protected $insertLimit = 500;

    /*
     * 每次上传文件大小的限制(bytes)
     * */
    protected $upLimitSize = 512;

    /*
     * 每个电话被标记的次数
     * */
    protected $itagNum = 100;

    public $insertValidTelNum = 0;

    /** gen sql
     * @param array $sql_param
     * @return string
     */
    protected function genSql($sql_param)
    {
        // init params
        $tel_list = $sql_param['tel_list'];
        $up_status = $sql_param['up_status'];
        $itag_id = $sql_param['itag_id'];
        $source = $sql_param['source'];

        $sql_pre = 'insert into dict_itagtel ';
        $sql_value = '';
        $sql_fields = '';
        $utime = date('Y-m-d H:i:s');

        $temp = array(
            'country_code' => 86,
            'itag_id' => $itag_id,
            'itag_num' => $up_status ? $this->itagNum : 0,
            'utime' => $utime,
            'company_name' => '',
            'category' => 0,
        );

        // gen sql
        foreach ($tel_list as $tel) {
            $temp['tel'] = $tel;

            if ($source == 'new') {
                $temp['tel_md5'] = md5($tel);
            }

            if (!$sql_fields) {
                $sql_fields = '(`' . implode('`,`', array_keys($temp)) . '`)';
            }

            $sql_value .= $sql_value ? ',("' . implode('","', $temp) . '")' : '("' . implode('","', $temp) . '")';
        }

        // up line
        if ($up_status) {
            return $sql_value ? $sql_pre . $sql_fields . 'values' . $sql_value . ' ON DUPLICATE KEY UPDATE `itag_num`=`itag_num`+' . $temp['itag_num'] : '';
        } else {
            return $sql_value ? $sql_pre . $sql_fields . 'values' . $sql_value . ' ON DUPLICATE KEY UPDATE `itag_num`=0' : '';
        }
    }

    /**
     * filter tel and gen sql
     * @param  array $param
     * @return string
     * @throws \Exception
     */
    public function filterDataAndGenSql($param)
    {
        try {
            $file_name = $param['file_name'];
            $itag_id = (int)$param['itag_id'];

            // check file size
            $file_size = filesize($file_name);

            if ($file_size/1000 > $this->upLimitSize) {
                throw new \Exception('电话文件超出单次上传的限制 : ' . $this->upLimitSize . 'KB');
            }

            if (in_array($itag_id, [13, 14])) {
                $gen_sql = $this->getUploadExcel($file_name, $itag_id, $param);
            } else {
                // gen file handle
                $handle = fopen($file_name, 'r');
                $gen_sql = $this->getUploadTxt($handle, $file_size, $param);
            }
            return $gen_sql;

        } catch (\Exception $e) {
            if (!in_array($itag_id, [13, 14])) {
                fclose($handle);
            }
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * filter tel
     * @param $tel
     * @return string
     * @throws \Exception
     */
    protected function filterTel($tel)
    {
        $tel = trim($tel);
        $is_check = 0;
        if (preg_match("/^0/", $tel)) {

            // 带区号号码
            if (preg_match("/^0((10)|2[\d])([2-8][\d]{7}|96[\d]{3,11})$/", $tel) || preg_match("/^0[3-9][\d]{2}([2-8][\d]{6,7}|96[\d]{3,10})$/", $tel)) {
                $is_check = 1;
            }
        } else if (preg_match("/^[2-8]/", $tel)) {
            if (preg_match("/^[4|8]00[\d]{7}$/", $tel)) {
                $is_check = 1;
            }
        } else if (preg_match("/^1/", $tel)) {

            // -------------手机-------------------106开头的短信平台号码----------------------------125开头的短信平台号码------------------1010开头的特殊号码
            if (preg_match("/^1[3-8][\d]{9}$/", $tel) || preg_match("/^106[\d]{5,13}$/", $tel) || preg_match("/^125[\d]{5,13}$/", $tel) || preg_match("/^1010\d{4,8}$/", $tel)) {
                $is_check = 1;
            }
        } else if (preg_match("/^95[\d]{3,14}$/", $tel)) {
            $is_check = 1;
        }

        if (!$is_check) {
            $tel = $tel ? $tel : '空行';
            throw new \Exception('Tel :' . $tel . " 不符合电话规则");
        }

        return $tel;
    }

    /**
     * 上传txt格式的文件
     * @param  array  $handle    文件信息
     * @param  number $file_size 文件大小
     * @param  array $param     参数
     * @return
     */
    public function getUploadTxt($handle, $file_size, $param)
    {
        // get tel list from file
        $read_str = fread($handle, $file_size);
        $read_tel = explode(PHP_EOL, $read_str);

        // check tel
        $tel_list = array_map(
            [$this, filterTel]
            , $read_tel
        );

        //  insert sql number
        $count = count($tel_list);

        // insert valid tel num
        $this->insertValidTelNum = count(array_unique($tel_list));

        // gen sql
        $offset = 0;
        $limit = $this->insertLimit;
        $gen_sql = '';

        while ($offset < $count) {
            $param['tel_list'] = array_slice($tel_list, $offset, $limit);
            // gen sql every 500
            $sql = $this->genSql($param);
            $gen_sql .= $gen_sql ? ';' . $sql : $sql;

            $offset += $limit;
        }

        fclose($handle);
        return $gen_sql;
    }

    /**
     * 上传excel表格
     * @param  string $file_path 临时文件
     * @param  number $itag_id   标签ID
     * @param  array $param     插入参数
     * @return sql
     */
    public function getUploadExcel($file_path, $itag_id, $param)
    {
        include_once SYSTEM_PATH.'ThinkPHP/Library/Org/PHPExcel/PHPExcel/PHPExcel.php';
        include_once SYSTEM_PATH.'ThinkPHP/Library/Org/PHPExcel/PHPExcel/Reader/Excel2007.php';
        // import("Org.PHPExcel.PHPExcel");
        // import("Org.PHPExcel.PHPExcel.Reader.Excel2007");
        $php_excel = new \PHPExcel();
        $php_reader = new \PHPExcel_Reader_Excel2007();
        //临时存储文件
        if (!$php_reader->canRead($file_path)) {
            include_once SYSTEM_PATH.'ThinkPHP/Library/Org/PHPExcel/PHPExcel/Reader/Excel5.php';
            // import("Org.PHPExcel.PHPExcel.Reader.Excel5");
            $php_reader = new PHPExcel_Reader_Excel5();
            if (!$php_reader->canRead($file_path)) {
               throw new \Exception('获取信息失败');
            }
        }
        //建立excel对象
        $php_excel = $php_reader->load($file_path);
        //读取excel文件中的指定工作表
        $current_sheet = $php_excel->getSheet(0);
        //取得最大的列号
        $all_column = $current_sheet->getHighestColumn();
        //取得一共有多少行
        $all_row = $current_sheet->getHighestRow();

        if ($all_column != 'D') {
            throw new \Exception('传入格式内容不正确');
        }
        //对excel内容做处理
        $data = $this->dealData($all_column, $all_row, $current_sheet, $itag_id);

        $sql = $this->getSql($data, $param);

        return $sql;
    }

    /**
     * sql语句
     * @param  array $data  表格数据
     * @param  array $param 插入参数
     * @return sql
     */
    public function getSql($data, $param)
    {
        $count = count($data);
        $this->insertValidTelNum = count(array_unique(array_column($data, 'tel')));
        $limit = $this->insertLimit;
        $page = ceil($count/$limit);
        // init params
        $up_status = $param['up_status'];
        $itag_id = $param['itag_id'];

        $sql_pre = 'insert into dict_itagtel ';

        $utime = date('Y-m-d H:i:s');

        $temp = array(
            'country_code' => 86,
            'itag_id' => $itag_id,
            'itag_num' => $up_status ? $this->itagNum : 0,
            'utime' => $utime,
        );
        $gen_sql = '';
        for ($i = 0; $i < $page; $i++) {
            $sql_value = $sql_fields = '';
            $start = $i*$limit;
            $list = array_slice($data, $start, $limit);
            foreach ($list as $key => $value) {
                $info = array_merge($value, $temp);
                $info['tel_md5'] = md5($info['tel']);
                if (!$sql_fields) {
                    $sql_fields = '(`' . implode('`,`', array_keys($info)) . '`)';
                }

                $sql_value .= $sql_value ? ',("' . implode('","', $info) . '")' : '("' . implode('","', $info) . '")';
            }
            // up line
            if ($up_status) {
                $sql = $sql_value ? $sql_pre . $sql_fields . 'values' . $sql_value . ' ON DUPLICATE KEY UPDATE `itag_num`=`itag_num`+' . $temp['itag_num'].';' : '';
            } else {
                $sql = $sql_value ? $sql_pre . $sql_fields . 'values' . $sql_value . ' ON DUPLICATE KEY UPDATE `itag_num`=0;' : '';
            }
            $gen_sql .= $sql;
        }
        return $gen_sql;
    }

    /**
     * 处理数据
     * @param  string $all_column    最大列号
     * @param  number $all_row       最大行数
     * @param  array  $current_sheet 读取工作表
     * @param  number $itag_id       标签ID
     * @return sql
     */
    public function dealData($all_column, $all_row, $current_sheet, $itag_id)
    {
        $data = $info = array();
        //循环读取每个单元格的内容。注意行从1开始，列从A开始
        for ($row_index = 2; $row_index <= $all_row; $row_index++) {
            for ($col_index = 'A'; $col_index <= $all_column; $col_index++) {
                $addr = $col_index.$row_index;
                //value
                $cell = $current_sheet->getCell($addr)->getValue();
                //富文本转换字符串
                if ($cell instanceof PHPExcel_RichText) {
                    $cell = $cell->__toString();
                }
                $data[$row_index][] = $cell;
            }
            //对一些数据做一些验证
            $info[] = $this->checkData($data[$row_index], $itag_id);
        }
        return $info;
    }

    /**
     * 检查传入数据
     * @param  array $data    表格数据
     * @param  number $itag_id 标签ID
     * @return array
     */
    public function checkData($data, $itag_id)
    {
        $field = ['tel', 'source', 'company_name', 'category'];

        $data = array_combine($field, $data);

        $this->filterTel($data['tel']);

        if (isset($data['source']) && !empty($data['source'])) {
            $list = ['日常外呼' => 1, '商务收集' => 2, '360催收' => 3];
            if (is_numeric($data['source']) && !in_array($data['source'], array_values($list))) {
                throw new \Exception('来源内容不正确，请填写1或2或3');
            }
            if (!is_numeric($data['source']) && !in_array($data['source'], array_keys($list))) {
                throw new \Exception('来源内容不正确，请填写日常外呼或商务收集或360号码');
            }
            if (!is_numeric($data['source'])) {
                $data['source'] = $list[$data['source']];
            }
        }
        if (isset($data['company_name']) && !empty($data['company_name']) && (preg_match('/[^a-zA-Z0-9\(\（\）\)(\x{4e00}-\x{9fa5})]/u', $data['company_name']) || strlen($data['company_name'] > 50))) {
            throw new \Exception('公司名称不超过50字符，仅允许上传汉字、字母、数字、中英文括号');
        }
        if (isset($data['category']) && !empty($data['category'])) {
            $list = ['银行' => 1, 'P2P' => 2, '委外' => 3, '汽车' => 4, '消费分期' => 5, '现金分期' => 6, '信用卡代偿' => 7, '其他' => 8];
            if (is_numeric($data['category']) && !in_array($data['category'], array_values($list))) {
                throw new \Exception('业务类型只能上传为1到8的业务类型');
            }
            if (!is_numeric($data['category']) && !in_array($data['category'], array_keys($list))) {
                throw new \Exception('业务类型名称不正确');
            }
            if (!is_numeric($data['category'])) {
                $data['category'] = $list[$data['category']];
            }
        }
        return $data;
    }
}
