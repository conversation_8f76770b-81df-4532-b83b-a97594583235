<?php
namespace Common\Model;
use Common\Common\Approval;

//用户模型
class SystemUserModel extends \Common\Model\BaseModel {

    /**
     * 获取列表
     * @param array $where
     * @param string $field
     * @return mixed
     */
    public static function getListProduct(array $where = [], $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }

	public function getDbDisabledLists(){
        return array('1'=>'启用','2'=>'禁用');
    }

    public function setInfo($id){
    	$id = intval($id);
        $this->create();
        //校验手机号格式
        $phone_reg = '/^0?1[34578]\d{9}$/';
        if (!empty($this->data['phone']) && !preg_match($phone_reg, $this->data['phone'])) {
            throw new \Exception('手机号格式不正确');
        }
        //校验部门
        if (empty($this->data['dept_id'])) {
            throw new \Exception('请选择部门');
        }
        //检查数据权限
        if (empty($this->data['data_auth'])) {
            throw new \Exception('请选择一个数据权限');
        } else {
            $this->data['data_auth'] = implode(',', $this->data['data_auth']);
        }
		//检查数据权限
		$this->data['source_auth'] = isset($this->data['source_auth'])?$this->data['source_auth']:-1;

        //邮件权限
        $this->data['balance_auth'] = $this->data['balance_auth']?1:0;
        $this->data['surplus_auth'] = $this->data['surplus_auth']?1:0;
        $this->data['expire_auth'] = $this->data['expire_auth']?1:0;
        $this->data['profit_auth'] = $this->data['profit_auth']?1:0;
        $this->where(array('id' => $id))->save();
        return $id;
    }

	/**
	 * 设置用户登录
	 */
	public function setUserLogin($user) {
		$user['username'] = trim($user['username']);
		if (empty($user['username'])) {
			throw new \Exception('账号为空 登录失败');
		}

		//查看已有用户
		$userinfo = $this->where(array('username' => trim($user['username'])))->find();
		if (!empty($userinfo)) {
			// 原有用户处理
			if ($this->checkDisabled($userinfo)) {
				throw new \Exception('登录账号已被禁用');
			}
			$info = array(
				'lastloginip'   => ip(),
				'lastlogintime' => NOW_TIME,
			);
			$this->where(array('id' => $userinfo['id']))->data($info)->save();
		} else {
			// 新增用户
			$info = array(
				'username'      => $user['username'],
				'password'      => md5(C('USER_DEFAULT_PASSWORD')),
				'email'         => trim($user['email']),
				'lastloginip'   => ip(),
				'lastlogintime' => NOW_TIME,
				'disabled'      => 1,
				'inputtime'     => NOW_TIME,
			);
			if ($this->data($info)->add() === false) {
				throw new \Exception('用户添加失败');
			}
		}
		$_SESSION[C('LOGIN_SESSION_NAME')] = $user['username'];
		return true;
	}

	/**
	 * 设置用户退出
	 */
	public function setUserLogout() {
		if (isset($_SESSION[C('LOGIN_SESSION_NAME')])) {
			unset($_SESSION[C('LOGIN_SESSION_NAME')]);
		}
		return true;
	}

	/**
	 * 检查是否已经禁用
	 */
	public function checkDisabled($user){
		return $user['disabled'] == '1' ? false : true;
	}

	/**
	 * 获取当前登录用户
	 */
	public function getCurrentUser() {
		static $user;
		if(empty($user)){
			$user = '';
			$username = isset($_SESSION[C('LOGIN_SESSION_NAME')]) ? $_SESSION[C('LOGIN_SESSION_NAME')] : '';
			
			//如果是审核通过的请求,需要模拟申请人登录
			$approval_token = I('post.approval_token');
			if($approval_token){
				$mod = new Approval();
				$username = $mod->getApplicantById($approval_token);
			}

			if (!empty($username)) {
				$user = $this->getUserByUserName($username);
                addBreakPoint('[admin]检查登录-getUserByUserName查库结束');
			}
		}
		return $user;
	}

	/**
	 * 根据用户名获取用户数据
	 */
	public function getUserByUserName($username) {
		//echo $this->where(array('username' => trim($username)))->fetchSql(true)->find();
        addBreakPoint('[admin]检查登录-getUserByUserName进入');
         $where = $this->where(array('username' => trim($username)));
        addBreakPoint('[admin]检查登录-getUserByUserName查库开始');
		return $where->find();
	}

	/**
     * 取得默认角色ID
     * 多个角色 去默认角色 没有设置默认去roleid最大值
     */
    public function getDefaultRoleId($role){
        $defaultroleid = $role[0]['id'];
        if(count($role) > 1){
            foreach ($role as $key => $value) {
                if($value['is_default'] == 1){
                    $defaultroleid = $value['id'];
                }
            }
        }
        return $defaultroleid;
    }


	/**
	 * 根据用户名获取用户数据
	 */
	public static function getUserInfoByName($username) {
        $where = (new static())->where(array('username' => trim($username)));
		return $where->find();
	}


	/**
	 * 根据用户名获取用户数据
	 */
	public static function getAllUsers() {
        return (new static())->select();
	}
}