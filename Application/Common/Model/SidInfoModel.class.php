<?php

namespace Common\Model;

use Think\Model\MongoModel;

class SidInfoModel extends MongoModel
{
    protected $connection = 'MONGO';
    protected $dbName = 'crs';
    protected $tablePrefix = '';

    /**
     * stat amount by user
     * @param $where
     * @return array
     */
    public function statAmount($where)
    {

        $pipeline = [
            ['$match' => $where],
            ['$group' => [
                '_id' => '$cid',
                'amount' => [
                    '$sum' => 1
                ],
            ]],
        ];

        $stat_data = $this
            ->getCollection()
            ->aggregate($pipeline);
        
        return $stat_data['result'] ? $stat_data['result'] : [];
    }

    /**
     * stat unique tel in specific time
     * @param $where
     * @return array
     */
    public function distinctStatus($where)
    {

        $pipeline = [
            ['$match' => $where],
            ['$group' => [
                '_id' => '$cid',
                'unique_tel' => [
                    '$addToSet' => '$tel'
                ]
            ]],
        ];

        $stat_data = $this
            ->getCollection()
            ->aggregate($pipeline);

        return $stat_data['result'] ? $stat_data['result'] : [];
    }

    public function getCollection()
    {
        return parent::getCollection();
    }
}
