<?php
namespace Common\Model;

/**
 * 用户角色模型
 */
class SystemUserRoleModel extends \Common\Model\BaseModel {
    /**
     * 获取用户角色
     */
    public function getUserRole($username){
        $role = array();
        if(empty($username)){
            return $role;
        }
        $role = $this->alias('a')
                ->join(C('DB_PREFIX').'system_role b ON b.id= a.roleid')
                ->field('b.`id`,b.`name`')
                ->where(array('a.`username`'=>$username,'b.disabled'=>1))
                ->select();
        addBreakPoint('[admin]获取用户角色-getUserRole第一次查库完成');
        //没有角色默认赋予访客角色
        if(empty($role)){
            $role = array();
            $role[] = D('SystemRole')->field(array('id','name'))->where(array('id'=>2))->find();
            $this->addUserRole($username,2,1);
        }
        return $role;
    }

    /**
     * 添加用户角色
     */
    public function addUserRole($username,$roleid,$is_default = 2){
        if(empty($username)){
            return;
        }
        return $id = $this->add(array('username'=>$username,'roleid'=>$roleid,'is_default'=>$is_default),array(),true);
    }

    /**
     * 设置用户角色
     */
    public function setUserRole($username,$roles,$default = 0){
        if(empty($username)){
            throw new \Exception('用户名不能为空');
        }
        if(empty($roles)){
            throw new \Exception('请选择用户所属角色');
        }

        $this->where(array('username'=>$username))->delete();
        $is_default = 0;
        foreach($roles as $var){
            $info = array('username'=>$username,$roleid);
            if($default == $var){
                $this->addUserRole($username,$var,1);
                $is_default = 1;
            }
            else{
                $this->addUserRole($username,$var,2);
            }
        }

        //修复默认角色
        if($is_default == 0){
            $this->where(array('username'=>$username,'roleid'=>$roles[0]))->data(['is_default'=>1])->save();
        }
        return;
    }

}