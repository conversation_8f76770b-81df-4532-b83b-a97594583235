<?php
namespace Common\Model;

/**
 * 系统节点模型
 */
class SystemNodeModel extends \Common\Model\BaseModel {
    //用户节点缓存变量名前缀
    private $role_node_cache_prefix = 'role_node_cache_prefix';

    public function getDbTypeLists(){
        return array('1'=>'菜单节点','2'=>'内部节点');
    }
    public function getDbIsShowLists(){
        return array('1'=>'显示','2'=>'不显示');
    }

    /**
     * 添加/编辑节点
     */
    public function setInfo($id = 0,$pid = 0){
        $rules = array(
            array('name', 'require', '节点名称必填！'),
            array('node', 'require', '节点标示必填'),
        );
        if (!$this->validate($rules)->create()) {
            throw new \Exception($this->getError());
        }

        if (empty($id)) {
            $this->inputtime = NOW_TIME;
            $id = $this->add();
        }
        else{
            if($pid && $this->where(array('pid'=>$id))->count() > 0){
                throw new \Exception('有下级节点的节点只能为一级节点');
            }
            $this->where(array('id' => $id))->save();
        }
        $this->clearNodeCache();
        return $id;
    }

    /**
     * 删除节点
     */
    public function del($id){
        if($this->where(array('pid'=>$id))->count() > 0){
            throw new \Exception('有下级节点的节点禁止删除');
        }
        $this->where(array('id'=>intval($id)))->delete();
        D('SystemRoleNode')->delAccessByNodeId(intval($id));
        $this->clearNodeCache();
    }

    /**
     * 获取当前用户节点id集合
     */
    public function getCurrentNodes(){
        static $nodes;
        if(empty($nodes)){
            $user = D('SystemUser')->getCurrentUser();
            addBreakPoint('[admin]检查权限-$user查库完成');
            $roles = D('SystemUserRole')->getUserRole($user['username']);
            addBreakPoint('[admin]检查权限-$roles查库完成');

            $nodes = array();
            //是否包含游客权限
            $is_have_default_user = 0;
            foreach($roles as $var){
                if($var['id'] == 2){
                    $is_have_default_user = 1;
                }
                $nodes = array_merge($nodes,$this->getNodeCache($var['id']));
            }
            addBreakPoint('[admin]检查权限-是否包含游客权限');
            //默认导入游客权限节点
            if($is_have_default_user == 0){
                $nodes = array_merge($nodes,$this->getNodeCache(2));
            }
            addBreakPoint('[admin]检查权限-默认导入游客权限节点');
        }
        return $nodes;
    }

    /**
     * 清空节点缓存
     */
    public function clearNodeCache(){
        $roles = D('SystemRole')->getField('id',true);
        foreach($roles as $var){
            S($this->role_node_cache_prefix.'_'.$var,NULL);
        }
        return;
    }

    public function getNodeCache($roleid){
        $cachename = $this->role_node_cache_prefix.'_'.$roleid;
        if(APP_DEBUG === true || !S($cachename)){
            //超级管理员 默认取得全部权限
            if($roleid == 1){
                $rolenode = $this->getField('id',true);
            }
            else{
                $rolenode = D('SystemRoleNode')->getRoleNode($roleid);
            }
            S($cachename,$rolenode);
        }
        else{
            $rolenode = S($cachename);
        }
        return $rolenode;
    }

    /**
     * 获取当前菜单所属节点
     * 如果有多个所属节点取第一个子节点
     */
    public function getCurrentMenu(){
        $node = MODULE_NAME.'/'.CONTROLLER_NAME.'/'.ACTION_NAME;
        $nodes = $this->where(array('node'=>$node))->select();
        addBreakPoint('[admin]获取当前菜单-查库完成');
        if(count($nodes) > 1){
            $temp = array();
            foreach($nodes as $var){
                if($var['pid']){
                    $temp = $var;
                    break;
                }
            }
            $nodes = empty($temp) ? $nodes[0] : $temp;
        }
        else{
            $nodes = $nodes[0];
        }
        if(!empty($nodes)){
            //生成url
            $nodes['url'] = $this->NodeUrl($nodes);
        }
        return empty($nodes) ? array() : $nodes;
    }

    /**
     * 获取层级菜单
     */
    public function getMultiMenus($nodeid){
        $menu = array();
        $temp = $this->find($nodeid);
        $menus[] = $temp;
        while ($temp['pid']) {
            $temp = $this->find($temp['pid']);
            $menus[] = $temp;
        }

        //生成url
        foreach ($menus as $key => $value) {
            $menus[$key]['url'] = $this->NodeUrl($value);
        }
        $menus = array_reverse($menus,false);
        return $menus;
    }

    /**
     * 获取系统菜单
     * 根据用户角色缓存
     */
    public function getSystemMenus(){
        $usernodes = $this->getCurrentNodes();
        addBreakPoint('[admin]系统菜单-getSystemMenus');
        return $this->getAllNodes(array('id'=>array('in',$usernodes),'type'=>'1','is_show'=>1));
    }

    /**
     * 获取节点
     */
    public function getAllNodes($where = array()){
        $status = 1;
        $allnodes = $this->where($where)->where(compact('status'))->order('`listorder` DESC,`id` ASC')->select();
        addBreakPoint('[admin]系统菜单-getAllNodes查库完成');
        //生成url
        foreach($allnodes as $key=>$var){
            $allnodes[$key]['url'] = $this->NodeUrl($var);
        }

        $nodes = array();
        //一级节点
        foreach($allnodes as $var){
            if($var['pid'] == 0){
                $nodes[$var['id']] = $var;
            }
        }
        //二级节点
        foreach($allnodes as $var){
            if($var['pid'] && isset($nodes[$var['pid']])){
                $nodes[$var['pid']]['son'][] = $var;
            }
        }
        return $nodes;
    }

    /**
     * 生成节点URL
     */
    public function NodeUrl($node){
        $url = '';
        if($node['type'] == 1){
            //头部含有http 为外部链接
            $url = strrpos($node['node'],'http') === 0 ? $node['node'] : U($node['node']);
        }
        return $url;
    }

    /**
     * 检查权限根据节点ID
     */
    public function checkPerById($id){
        return in_array($id,$this->getCurrentNodes());
    }

    /**
     * 检查权限根据节点标示
     */
    public function checkPerByNode($string){
        $node = $this->where(array('node'=>$string))->find();
        return !empty($node) && in_array($node['id'],$this->getCurrentNodes()) ? true : false;
    }
}