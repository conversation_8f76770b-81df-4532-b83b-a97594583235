<?php

namespace Common\Model;

class AdminApistatModel extends BaseModel
{
    protected $connection = 'MYSQL_CRSAPI';
    protected $tablePrefix = '';

    /**
     * 获取客户需要的数据统计信息
     * @param null $apiKey
     * @param null $begin 自定义的开始和结束时间
     * @param null $end
     * @param bool $searchType 查询的级别  false是不启用   true 统计所有用户,忽略apikey
     * @param bool $apikeyid 获取到apikeyid  这个时候即使   $searchType为true 也是要执行 $where 追加操作的
     * @param string $validAppkeyid 获取全部用户的信息时候, 合法的apikeyid
     * @param null $cid  检查是不是在搜索特定的 cid
     * @return mixed
     */
    public function getApiTimes($apiKey = null, $begin = null, $end = null, $searchType = false, $apikeyid = false, $validAppkeyid = '',$cid=null)
    {
        $begin = date('Ymd', $begin);
        $end = date('Ymd', $end);
        $where = "admin_apistat.daily_time between '$begin' and  '$end' and apikey.active=1 ";

        /*检测所有的用户的时候情况(基本上都是在上面的这个流程里面解决的)*/
        if ($searchType) {
            // 查找特定的用户,且这个用户有配置邦秒配单号版（apikeyid不为0）
            if ($apikeyid) {
                $where .= " and apikey.id=$apikeyid";
                //不是在寻找特定的用户
            } elseif($validAppkeyid && !$cid) {
                $where .= $validAppkeyid ? " and apikey.id  in ($validAppkeyid)" : '';
            }else{
                //寻找特定的用户 但是用户没有配置邦秒配单号版
                return ['total'=>0];
            }
            $data = $this->join('join  admin_apikey apikey ON admin_apistat.apikey=apikey.id')
                ->field('SUM(admin_apistat.daily_used) AS total')->where($where)->find();

            return $data;
        }

        /* 检测只是传递第一个参数的时候情况,就是检测单个用户的时候情况 */
        if (!empty($apiKey)) {
            $where .= " and apikey.id=$apiKey";
            $data = $this->join('join  admin_apikey apikey ON admin_apistat.apikey=apikey.id')
                ->field('apikey.apikey,SUM(admin_apistat.daily_used) AS daily_used')->where($where)->find();
        } else {
            /*  这个流程基本上是不走的，但是如果走到这里的话 那么一定要传递最后一个参数  */
            return ['daily_used'=>0];
        }

        return $data;
    }

}