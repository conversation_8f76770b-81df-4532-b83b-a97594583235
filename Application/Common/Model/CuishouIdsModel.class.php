<?php

namespace Common\Model;

use Think\Model\MongoModel;

class CuishouIdsModel extends MongoModel
{
    protected $connection = 'DUNNING_MONGO';
    protected $dbName = 'cuishou';
    protected $tableName = 'ids';
    protected $tablePrefix = 'cuishou_';

    public function getNextSequence()
    {
        $data = [
            '$inc' => [
                'id' => (new \MongoInt32(1))
            ]
        ];
        $result = $this
            ->getCollection()
            ->findAndModify(['name' => 'user'], $data, ['id' => 1, '_id' => 0], ['new' => true]);
        return $result['id'];
    }
}
