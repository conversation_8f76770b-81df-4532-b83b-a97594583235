<?php

namespace Common\Model;


class AdminApikeyModel extends BaseModel
{
    protected $connection = 'MYSQL_CRSAPI';
    protected $tablePrefix = '';

    public function filterData($data)
    {
        // init params
        $id = isset($data['id']) ? $data['id'] : '';

        // check owner
        if (!isset($data['owner']) || !$data['owner']) {
            throw new \Exception('客户名称不可以为空');
        }

        // check valid time
        if (!isset($data['validuntil']) || !$data['validuntil'] || (strtotime($data['validuntil']) < time() && !isset($data['id']))) {
            throw new \Exception('截至日期有误, 请选择选正常的截至日期');
        }

        // check apikey
        if (!isset($data['apikey']) || !$data['apikey']) {
            throw new \Exception('APIKEY 不可以为空');
        }

        // check limit_access_ip
        if (isset($data['limit_access_ip']) && $data['limit_access_ip']) {

            $ip_list = explode(PHP_EOL, $data['limit_access_ip']);

            // check ip one by one
            $ip_list = array_map(function($ip_str){
                $ip_str = trim($ip_str, " \t\n\r\0\x0B ");

                // normal ip
                $ip_validate_result= filter_var($ip_str, FILTER_VALIDATE_IP);
                if ($ip_validate_result) {
                    return $ip_str;
                }

                // match ip eg: 172.18.19.x
                $regex_ip = '/\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){3}x$/';
                $ip_reg_result = preg_match($regex_ip, $ip_str, $matches);
                if ($ip_reg_result) {
                    return $ip_str;
                }

                throw new \Exception('IP白名单输入不合法');
            },$ip_list);

            $data['limit_access_ip'] = serialize($ip_list);
        } else {
            $data['limit_access_ip'] = '';
        }

        // check bind domain
        if (isset($data['bind_domain']) && $data['bind_domain']) {

            $domain_list = explode(PHP_EOL, $data['bind_domain']);

            // check url one by one
            $domain_list = array_map(function($url_str){
                $url_str= trim($url_str);

                if (!filter_var($url_str, FILTER_VALIDATE_URL)) {
                    throw new \Exception('域名绑定不合法');
                }
                return  $url_str;
            }, $domain_list);

            $data['bind_domain'] = serialize($domain_list);
        } else {
            $data['bind_domain'] = '';
        }

        // check daily_limit monthly_limit yearly_limit
        if (($data['daily_limit'] = trim($data['daily_limit'])) == '') {
            $data['daily_limit'] = -1;
        } else {
            if (!ctype_digit($data['daily_limit'])) {
                throw new \Exception('日限额填充有误');
            }
        }

        if (($data['monthly_limit'] = trim($data['monthly_limit'])) == '') {
            $data['monthly_limit'] = -1;
        } else {
            if (!ctype_digit($data['monthly_limit'])) {
                throw new \Exception('月限额填充有误');
            }
        }

        if (($data['yearly_limit'] = trim($data['yearly_limit'])) == '') {
            $data['yearly_limit'] = -1;
        } else {
            if (!ctype_digit($data['yearly_limit'])) {
                throw new \Exception('年限额填充有误');
            }
        }

        if (($data['monthly_limit'] != -1) && ($data['monthly_limit'] < $data['daily_limit'])) {
            throw new \Exception('月限额不可以小于日限额');
        }

        if (($data['yearly_limit'] != -1) && (($data['yearly_limit'] < $data['monthly_limit']) || ($data['yearly_limit'] < $data['daily_limit']))) {
            throw new \Exception('年限额不可以小于月和日限额');
        }

        // check output_yscs
        if (isset($data['output_yscs']) && !in_array($data['output_yscs'], [1,2])) {
            throw new \Exception('疑似催收输出赋值有误');
        }

        // check output_cs
        if (isset($data['output_cs']) && !in_array($data['output_cs'], [1,2])) {
            throw new \Exception('催收输出赋值有误');
        }

        //check api output field
        if (isset($data['out_fields']) && !$data['out_fields']) {
            throw new \Exception('请选择接口输出字段');
        }

        // gen password
        if (!$id) {
            $data['password'] = gen_pwd(165);
        }

        return $data;
    }


    public function getInfos($fields=true, $where=array())
    {
        $infos = $this
            ->field($fields)
            ->where($where)
            ->order(['id'=>'DESC'])
            ->select();
        return $infos;
    }

    public function getInfoList($fields=true, $where=array())
    {
        $msg = $this
            ->field($fields)
            ->where($where)
            ->index('id')
            ->select();

        return $msg;
    }

    public function getInfoById($id)
    {
        $info = $this->where(['id'=>$id])->find();
        return $info;
    }

    public function setInfo($id, $apikey, $apictrl)
    {
        $_validuntil = strtotime($apikey['validuntil'].'23:59:59');
        $validuntil = date('Y-m-d H:i:s',$_validuntil);

        $apikey['validuntil'] = $validuntil;
        $apikey['updated_at'] = time();

        $this->startTrans();
        try {
            $rst = $this->where(array('id'=>$id))->data($apikey)->save();
            $adminapictrl = D('AdminApictrl');
            if ($rst !== false) {
                $result = $adminapictrl->setInfo($id, $apictrl);
            }

            if ($result !== false) {
                $this->commit();
                return $result;
            }
        } catch (\Exception $e) {
            $this->rollback();
            echo "Failed: " . $e->getMessage();
        }
    }

    public function addInfo($apikey,$apictrl)
    {
        $_validuntil = strtotime($apikey['validuntil'].'23:59:59');
        $validuntil = date('Y-m-d H:i:s',$_validuntil);

        $apiInfo[] = [
            'owner'=>$apikey['owner'],
            'active'=>$apikey['active'],
            'apikey'=>$apikey['apikey'],
            'validuntil'=>$validuntil,
            'created_at' => time(),
            'password' => gen_pwd(165),
        ];

        $this->startTrans();
        try {
            $apikeyid = $this->addAll($apiInfo);
            $adminapictrl = D('AdminApictrl');
            if ($apikeyid !== false) {
                $apictrlid = $adminapictrl->addApictrl($apikeyid, $apictrl);
            }

            if ($apictrlid !== false) {
                $data = [
                    'developer' => $apikey['owner'],
                    'apikeyid' => $apikeyid,
                ];
                $auth_id = I('get.auth_id');
                $auth = D('Auth');
                //当前如果是添加模式的话 还需要增加三个属性
                if (empty($auth_id)) {
                    $data = array_merge($data,['status' => 2, 'appid'=>$this->hashid(32),
                        'appsecret'=>$this->hashid(64)]);
                    $result = $auth->add($data);
                } else {
                    $result = $auth->where(['id' => $auth_id])->data($data)->save();
                }
            }

            if ($result !== false) {
                $this->commit();
                return $apikeyid;
            }
        } catch (\Exception $e) {
            $this->rollback();
            echo "Failed: " . $e->getMessage();
        }
    }


    protected function hashid($length)
    {
        $hash_string = '';
        for ($i=0; $i < 10; $i++) {
            $hash_string .= md5(uniqid().mt_rand(1,9999999));
        }

        return  substr($hash_string,0,$length);
    }
}
