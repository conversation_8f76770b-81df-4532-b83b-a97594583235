<?php

namespace Common\Model;

class AdminOutputFieldsModel extends BaseModel
{
    protected $connection = 'MYSQL_CRSAPI';
    protected $tablePrefix = '';

    protected   $_validate = [
        ['name', 'require', '请填充字段！'],
        ['name', '', '字段已经存在', 0, 'unique', 1],
        ['remark', 'require', '请填充备注！'],
        ['remark', '', '备注已经存在！', 0, 'unique', 1],
        ['status', 'require', '请填充状态！'],
        ['status', [1, 2], '状态值的范围不对', 0, 'in'],
    ];
}
