<?php

namespace Common\Model;

class AuxiliaryServices extends BaseModel
{

    protected $tableName = 'auxiliary_services';

    public function filterParam($data)
    {
        if (!is_numeric ($data['service_cateid'])) {
            throw new \Exception('SERVICE ID 不合法');
        }
        if (!isset($data['service_key']) || !$data['service_key']) {
            throw  new \Exception('SERVICE APIKEY不可以为空');
        }
        if (!isset($data['service_secret']) || !$data['service_secret']) {
            throw  new \Exception('SERVICE PASSWORD不可以为空');
        }
        if (!isset($data['uid']) || !is_numeric($data['uid'])) {
            throw new \Exception('SERVICE uid不合法');
        }
        if (isset($data['notify_url']) && !filter_var($data['notify_url'], FILTER_VALIDATE_URL)) {
            throw new \Exception('SERVICE 回调地址不合法');
        }
        return true;
    }

    /**
     * 针对auth选择了need_dunning=1 但是表中并没有存储到数据,导致save失败的情况
     * @param array $data  which  wait to update
     * @return integer
     */
    public function updateParam($data)
    {
        $this->filterParam($data);
        // check whether has already one in this table
        $count = $this->where(['uid' => $data['uid']])
            ->count('id');

        if ($count) {
            return $this->where(['uid' => $data['uid']])->save($data);
        }
        $data['created_at'] = time();
        return $this->add($data);
    }
}
