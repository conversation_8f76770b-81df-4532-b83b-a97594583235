<?php

namespace Common\Model;

use Think\Model;

/**
 * 公共枚举定义
 */
class CommonEnumModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'common_enum';
    protected $tablePrefix = '';

    /** */
    public function getEnumPairs($type=0,$key=null){

        if(!is_null($key)){
            return  $this->where('type=\''.$type.'\'')->where("name='".$key."'")->getField('value');
        }
        return  $this->where('type=\''.$type.'\'')->order('sort asc')->getField('name,value');

    }
}
