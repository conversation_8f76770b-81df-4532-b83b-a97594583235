<?php

namespace Common\Model;


class AdminApictrlModel extends BaseModel
{
    protected $connection = 'MYSQL_CRSAPI';
    protected $tablePrefix = '';

    public function getInfo ($where = array())
    {
        $infos = $this
            ->field('id,field,value')
            ->where($where)
            ->index('field')
            ->select();

        if (!empty($infos['bind_domain'])) {
            $infos['bind_domain']['value'] = implode(PHP_EOL, unserialize($infos['bind_domain']['value']));
        }

        if (!empty($infos['limit_access_ip'])) {
            $infos['limit_access_ip']['value'] = implode(PHP_EOL, unserialize($infos['limit_access_ip']['value']));
        }

        return $infos;
    }

    public function setInfo($apikeyid, $apictrl)
    {
        $base = $this
            ->field('id,field')
            ->where(['apikeyid' => $apikeyid])
            ->index('field')
            ->select();
        $execute = true;
        foreach ($apictrl as $key => $value) {
            $info = [
                'enabled' => 1,
                'apikeyid' => $apikeyid,
                'field' => $key,
                'value' => $value,
            ];
            if (isset($base[$key])) {
                $result = $this->where(['id'=>$base[$key]['id']])->save($info);
            } else {
                $result = $this->add($info);
            }
            if ($result === false) {
                $execute = false;
            }
        }

        return $execute;
    }

    public function addApictrl($apikeyid, $apictrl)
    {
        foreach ($apictrl as $key => $value) {
            $info[] = [
                'enabled' => 1,
                'apikeyid' => $apikeyid,
                'field' => $key,
                'value' => $value
            ];
        }

        $result = $this->addAll($info);
        return $result;
    }

    public function filterInfo($input)
    {
        $input['daily_limit'] = trim($input['daily_limit']);
        $input['monthly_limit'] = trim($input['monthly_limit']);
        $input['yearly_limit'] = trim($input['yearly_limit']);

        if ((is_numeric($input['daily_limit']) && $input['daily_limit'] >= 0) || $input['daily_limit'] === '') {
            $input['daily_limit'] = $input['daily_limit'] === '' ? '-1' : $input['daily_limit'];
        } else {
            throw new \Exception('日限额输入有误');
        }

        if ((is_numeric($input['monthly_limit']) && $input['monthly_limit'] >= 0) || $input['monthly_limit'] === '') {
            $input['monthly_limit'] = $input['monthly_limit'] === '' ? '-1' : $input['monthly_limit'];
        } else {
            throw new \Exception('月限额输入有误');
        }

        if ((is_numeric($input['yearly_limit']) && $input['yearly_limit'] >= 0) || $input['yearly_limit'] === '') {
            $input['yearly_limit'] = $input['yearly_limit'] === '' ? '-1' : $input['yearly_limit'];
        } else {
            throw new \Exception('年限额输入有误');
        }

        if (!empty($input['limit_access_ip'])) {
            $ip = explode(PHP_EOL, str_replace(' ', '', $input['limit_access_ip']));
            $ip = array_map(function($ipStr){
                return  trim($ipStr);
            },$ip);
            if (!in_array(false, filter_var_array($ip, FILTER_VALIDATE_IP))) {
                $input['limit_access_ip'] = serialize($ip);
            } else {
                throw new \Exception('ip地址输入不合法');
            }
        } else {
            $input['limit_access_ip'] = '';
        }

        if (!empty($input['bind_domain'])) {
            $domain = explode(PHP_EOL, str_replace(' ', '', $input['bind_domain']));
            if (!in_array(false, filter_var_array($domain, FILTER_VALIDATE_URL))) {
                $input['bind_domain'] = serialize($domain);
            } else {
                throw new \Exception('域名输入不合法');
            }
        } else {
            $input['bind_domain'] = '';
        }

        if (!in_array($input['output_yscs'], [1, 2])) {
            throw new \Exception('疑似催收输出不合法');
        }
        if (!in_array($input['output_cs'], [1, 2])) {
            throw new \Exception('催收输出不合法');
        }
        return $input;
    }
}
