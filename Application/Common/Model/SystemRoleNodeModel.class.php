<?php
namespace Common\Model;

/**
 * 角色节点模型
 */
class SystemRoleNodeModel extends \Common\Model\BaseModel {

	/**
	 * 获取角色节点
	 * @param  int $roleid 角色ID
	 * @return array nodeid 节点数组
	 */
	public function getRoleNode($roleid) {
		$nodeids = $nodeids = $this->where(array('roleid' => intval($roleid)))->getField('nodeid', true);
		return empty($nodeids) ? array() : $nodeids;
	}

	/**
	 * 根据角色ID删除权限
	 */
	public function delAccessByRoleId($id){
		$this->delAccess(array('roleid'=>intval($id)));
	}

	/**
	 * 根据节点ID删除权限
	 */
	public function delAccessByNodeId($id){
		$this->delAccess(array('nodeid'=>intval($id)));
	}

	/**
	 * 设置权限
	 */
	public function setAccess($roleid,$access){
		if(in_array($roleid,array(1))){
			throw new \Exception('该角色 不允许 被设置权限');
		}
		$nodes = array();
		if(!empty($access)){
			$nodes = array_keys($access);
		}

		$info = array();
		foreach($nodes as $var){
			$info[] = array(
				'roleid'=>$roleid,
				'nodeid'=>$var,
				);
		}

		if(!empty($info)){
			$this->delAccessByRoleId($roleid);
			$this->addAll($info);
			D('SystemNode')->clearNodeCache();
		}
		return;
	}

	private function delAccess($where){
		if(!empty($where)){
			$is = $this->where($where)->delete();
		}
	}
}