<?php

namespace Common\Model;

class DictItagtelLogModel extends BaseModel
{
    protected $connection = 'DB_FINANCE';
    protected $tablePrefix = '';
    protected $tableName = 'dict_itagtel_log';

    /**
     * 添加号码上下线记录
     * @param $data
     * @return mixed
     */
    public function addDictItagtelLogInfo($data){
        if (empty($data['itag_id'])){
            return null;
        }
        $data['up_status'] = empty($data['up_status']) ? 0 : $data['up_status'];
        $data['utime'] = date('Y-m-d H:i:s');
        return $this->add($data);
    }

    /**
     * 获取记录列表
     * @return mixed
     */
    public function getDictItagtelLogList(){
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $itag_id = I('get.itag_id', '', 'trim');
        $up_status = I('get.up_status', '', 'trim');

        $where = [];
        if ($begin && $end){
            $where['utime'] = ['between', [$begin, $end.' 23:59:59']];
        }elseif($begin){
            $where['utime'] = ['egt', $begin];
        }elseif($end){
            $where['utime'] = ['elt', $end. ' 23:59:59'];
        }
        if ($itag_id){
            $where['itag_id'] = $itag_id;
        }
        if ($up_status !== ''){
            $where['up_status'] = $up_status;
        }

        return D('DictItagtelLog')->where($where)->order('id desc')->select();
    }

    /**
     * 格式化记录数据
     * @param $log_list
     * @param $tag_list
     * @return array
     */
    public function formatDictItagtelLogs($log_list, $tag_list){
        return array_map(function ($log) use($tag_list){
            foreach ($tag_list as $item){
                if ($log['itag_id'] == $item['id']){
                    $log['itag_name'] = $item['itag'];
                    break;
                }
            }
            $log['tel_num_f'] = number_format($log['tel_num']);
            $log['up_status_type'] = ($log['up_status'] == 1) ? '上线' : '下线';
            return $log;
        }, $log_list);
    }

    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListForRequest($stat_list, $file_name){
        // gen file
        $title_list = 'ID,号码类型,操作类型,上传数量,净增数量,操作时间,操作人';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $stat_data) {
            // 数据补全
            $file_str = $stat_data['id'] . ',' . $stat_data['itag_name'] . ',' . $stat_data['up_status_type'] . ','
                . $stat_data['tel_num'] . ','. $stat_data['inc_num'] . ','. $stat_data['utime'] . '  ,' . $stat_data['handle_person'];
            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
