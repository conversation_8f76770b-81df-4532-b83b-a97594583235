<?php

namespace Common\Model;

use Think\Model\MongoModel;

class CuishouUserModel extends MongoModel
{
    protected $connection = 'DUNNING_MONGO';
    protected $dbName = 'cuishou';
    protected $tableName = 'user';
    protected $tablePrefix = 'cuishou_';

    /**
     * 更新数据
     * @param array $data
     * @param array $where
     * @param array $options
     * @return mixed
     */
    public function updateInfo($data, $where, $options = ['w' => 0])
    {
        return $this->getCollection()->update($where, $data, $options);
    }

    /**
     * 获取所有的签约状态标识
     * @return array
     */
    public function getContractStatus(){
        return [1=>'已签约已付款', 2=>'已签约未付费', 3=>'未签约', 4=>'其他', 5=> '特殊客户'];
    }
}
