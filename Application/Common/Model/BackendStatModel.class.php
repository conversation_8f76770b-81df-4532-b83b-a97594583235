<?php
namespace Common\Model;

use Think\Model\MongoModel;

class BackendStatModel extends MongoModel
{
    protected $connection = 'STAT_MONGO';
    protected $dbName = 'backend';
    protected $tableName = 'statistic';
    protected $tablePrefix = '';

    public function getStatListByAccountProductDate($where)
    {
        $pipeline = [
            ['$match' => $where],
            ['$group' => [
                '_id' => ['apikey' => '$apikey', 'amount_date' => '$amount_date', 'product_id' => '$product_id'],
                'stat_data' => ['$push' => '$stat_data'],
            ]],
            ['$project' =>[ '_id' => 0, 'apikey' => '$_id.apikey', 'product_id' => '$_id.product_id', 'amount_date' => '$_id.amount_date', 'stat_data' => 1]],
        ];

        $data = $this->getCollection()->aggregate($pipeline);
        return $data['result'] ? $data['result'] : [];
    }
}