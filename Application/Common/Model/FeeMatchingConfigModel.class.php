<?php

namespace Common\Model;

class FeeMatchingConfigModel extends BaseModel
{
    protected $connection = 'DB_FINANCE';
    protected $tablePrefix = '';

    /**
     * 获取计费依据
     * @return array
     */
//    public function getFeeBasis(){
//        return [
//            1=>'有效调用量',
//            2=>'总调用量'
//        ];
//    }

    /**
     * 获取计费方式
     * @return array
     */
    public function getFeeMethod(){
        return [
            1=>'按时间',
            2=>'按用量'
        ];
    }

    /**
     * 获取时间计费规则
     * @return array
     */
    public function getFeeTimeRule(){
        return [
            1=>'包日',
            2=>'包月',
            3=>'包年'
        ];
    }

    /**
     * 获取用量计费规则
     * @return array
     */
    public function getFeeAmountRule(){
        return [
            1=>'固定价格',
            2=>'阶梯价格'
        ];
    }

    /**
     * 获取阶梯计费规则
     * @return array
     */
    public function getFeeStepRule(){
        return [
            1=>'自然日',
            2=>'自然月',
            3=>'自然年',
            4=>'无周期'
        ];
    }
}
