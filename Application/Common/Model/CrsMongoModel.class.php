<?php

namespace Common\Model;

use Think\Model\MongoModel;

class CrsMongoModel extends MongoModel
{
    protected $connection = 'MONGO';
    protected $dbName = 'crs';

    public function setConfig($trueTableName = null)
    {
        $this->trueTableName = $trueTableName;
    }

    public function getErrorList()
    {
        return array(
            'success' => [0, 1, 2, 3100],
            'user_error' => [3003, 3004, 4001, 4002, 4003, 4004, 4005, 4010, 5000, 5001],
            'crawler_error' => [3000, 3001, 3002, 3005, 3006, 3007, 3008, 3009, 3097, 3098, 3099, 4000, 4116, 5017, 9999, 3333],
            'telecom_error' => [4107, 4999],
        );
    }

    /**
     * @param null $collect  集合的名字
     * @param null $field
     * @param array $where 条件
     * @param null $order 排序
     * @param $listRows  每页显示的条数
     * @param string $searchType 本函数执行的目的   collect获取集合   count 计算符合条件的数据的数量
     * @return mixed
     */
    public function queryMongo($collect=null, $field = null, $where, $order = null, $listRows = null, $searchType = 'collect')
    {
        //加入分页的机制
        $this->setConfig($collect);
        $object = $this->field($field);
        $object->where($where);
        $totalRows = $object->count();
        if ($searchType === 'count') {
            return $totalRows;
        }

        if (!empty($order)) {
            $object->order($order);

        }

        if (!empty($listRows)) {
            $Page = new Page($totalRows, $listRows);
            $object->limit($Page->firstRow, $Page->listRows);
        }

        $info = $object->select();
        return $info;

    }

}
