<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-10 14:44:08
 */
namespace Common\Model;

class RiskListFeeConfigModel extends BaseModel
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'blacklist_config';
    protected $tablePrefix = 'fee_';

    public function getFeeBasis()
    {
        return [
            1 => '查得量',
        ];
    }

    public function getFeeMethod()
    {
        return [
            1 => '按时间',
            2 => '按用量',
        ];
    }
}