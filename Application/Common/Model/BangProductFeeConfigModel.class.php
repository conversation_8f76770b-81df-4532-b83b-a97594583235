<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-26 11:22:08
 */
namespace Common\Model;

class BangProductFeeConfigModel extends BaseModel
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'bang_config';
    protected $tablePrefix = 'fee_';

    public function getFeeBasis()
    {
        return [
            1 => '总查得量',
            2 => '名称查得量',
            3 => '地址查得量',
        ];
    }

    public function getFeeMethod()
    {
        return [
            1 => '按时间',
            2 => '按用量',
        ];
    }
}