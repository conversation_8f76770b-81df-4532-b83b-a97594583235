<?php
namespace Common\Model;

/**
 * 角色模型
 */
class SystemRoleModel extends \Common\Model\BaseModel {
    public function getDbDisabledLists(){
        return array('1'=>'启用','2'=>'禁用');
    }

    /**
     * 添加/编辑角色
     */
    public function setInfo($id = 0){
        $rules = array(
            array('name', 'require', '角色名称必填！'),
        );
        if (!$this->validate($rules)->create()) {
            throw new \Exception($this->getError());
        }

        if (empty($id)) {
            $this->inputtime = NOW_TIME;
            $this->disabled = 1;
            $id = $this->add();
        }
        else{
            //游客和超级管理员 不允许 被禁用
            if(in_array($id,array(1,2)) && $this->disabled != 1){
                throw new \Exception('该角色 不允许 被禁用');
            }
            $this->where(array('id' => $id))->save();
        }
        return $id;
    }

    /**
     * 删除角色
     */
    public function del($id){
        //游客和超级管理员 不允许 被删除
        if(in_array($id,array(1,2))){
            throw new \Exception('该角色 不允许 被删除');
        }
        $this->where(array('id'=>intval($id)))->delete();
        D('SystemRoleNode')->delAccessByRoleId(intval($id));
        D('SystemNode')->clearNodeCache();
    }
}