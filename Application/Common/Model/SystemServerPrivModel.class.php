<?php
namespace Common\Model;

/**
 * 模型
 */
class SystemServerPrivModel extends \Common\Model\BaseModel {

    protected $connection = 'DB_FINANCE';

    /**
     * 获取
     */
    public function getAllIps($where = array(),$limit=10,$offset=0,$count=false){

        if($count){
            $allnodes = $this->where($where)->order('`create_time` DESC,`id` ASC,`apply_status` ASC')->count();
        }else{
            $allnodes = $this->where($where)
            ->limit($offset,$limit)
            ->order('`apply_status` ASC,`create_time` DESC,`id` ASC')
            ->select();
        }
        addBreakPoint('[admin]-getAllIps查库完成');
        return $allnodes;
    }


    public function getIpNode($ip){
        list($i1,$i2,$i3,$i4) = explode('.',$ip);

        if($i3){
            if(in_array($i3,['52','53'])){
                return 'shenzhen';
            }elseif(in_array($i3,['56','57'])){
                return 'beijing';
            }else{
                return 'other';
            }
        }
    }

    /**
     * 添加/编辑
     */
    public function setInfo($id = 0,$pid = 0){
        $rules = array(
            array('ip', 'require', '服务器ip必填！'),
            array('type', 'require', '服务器类型必填'),
        );
        if (!$this->validate($rules)->create()) {
            throw new \Exception($this->getError());
        }

        if (empty($id)) {
            $this->create_time = NOW_TIME;
            $id = $this->add();
        }
        else{
            $this->where(array('id' => $id))->save();
        }
        return $id;
    }

    /**
     * 删除
     */
    public function del($id){
        $this->where(array('id'=>intval($id)))->delete();
    }


}