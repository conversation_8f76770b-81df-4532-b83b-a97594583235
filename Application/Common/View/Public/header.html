<nav class="navbar navbar-default" id="system-header">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand" href="{:U('Home/Index/index')}">
                <img alt="Brand" src="__IMAGES__logo.png" height="25px">
                {:C('SITE_NAME')}
            </a>
        </div>
        <div class="collapse navbar-collapse">
            <ul class="nav navbar-nav">
                <?php foreach ($systemmenu as $value): ?>
                <li <?php echo isset($currentmenus[0]) && $value['id'] == $currentmenus[0]['id'] ? 'class="active"' : ''?>>
                    <?php if ($value['son']): ?>
                    <a href="{$value['url']}" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                        {$value['name']}
                        <span class="caret"></span>
                    </a>
                    <?php else: ?>
                    <a href="{$value['url']}">
                        {$value['name']}
                    </a>
                    <?php endif ?>
                    <?php if ($value['son']): ?>
                    <ul class="dropdown-menu">
                        <?php foreach ($value['son'] as $v): ?>
                        <li <?php echo isset($currentmenus[1]) && $v['id'] == $currentmenus[1]['id'] ? 'class="active"' : ''?>>
                            <a href="{$v['url']}">{$v['name']}</a>
                        </li>
                        <?php endforeach ?>
                    </ul>
                    <?php endif ?>
                </li>
                <?php endforeach ?>
            </ul>
            <ul class="nav navbar-nav navbar-right">
                <li>
                    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                        {$loginuser['username']}
                        <span class="caret"></span>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a href="{:U('System/My/edit')}">修改个人信息</a>
                        </li>
                        <li>
                            <a href="{:U('System/Role/lists')}">权限列表</a>
                        </li>
                        <li>
                            <a href="{:U('System/Auth/logout')}">退出</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>