<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>{$SEO['title']}</title>
<meta name="keywords" content="{$SEO['keywords']}">
<meta name="description" content="{$SEO['description']}">

<link rel="stylesheet" href="__CSS__bootstrap.min.css">
<link rel="stylesheet" href="__CSS__style.css?version=1.11">
<link rel="stylesheet" href="__CSS__font-awesome.css">

<script>
    var ROOT_PATH = '__WEB_PATH__', RESOURCES_PATH = '__STATICS__', ADMIN_USERNAME = '{$loginuser["username"]}';
</script>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript">
    window.statics_path = "__STATICS__";
</script>
<script src="__JS__template.js" type="text/javascript"></script>
<script src="__JS__bootstrap.min.js" type="text/javascript"></script>
<script src="__JS__dhb.js?version=1.11" type="text/javascript"></script>

<!-- 加载axios  -->
<script src="__JS__axios.min.js" type="text/javascript"></script>

<!-- 富文本编辑框  -->
<script src="__JS__wangEditor.min.js" type="text/javascript"></script>

<!--  js select2  -->
<link rel="stylesheet" href="__CSS__select2.min.css">
<script src="__JS__select2.full.min.js" type="text/javascript"></script>
<link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">

<!-- 加载fileDownload -->
<script src="__JS__jquery.fileDownload.js"></script>


<!--  加载vue  -->
<!-- 线上环境-->

<script src="__JS__quill.js" type="text/javascript"></script>

<!-- Quill JS Vue -->
<script src="__JS__vue-quill-editor.js" type="text/javascript"></script>

<!-- Include stylesheet -->
<link rel="stylesheet" href="__CSS__quill.snow.css">

<!-- 开发环境 -->
<script src="__JS__vue.js" type="text/javascript"></script>
<script src="__JS__vue-resource.js" type="text/javascript"></script>
<script src="__JS__vue-paginate.js" type="text/javascript"></script>

<script src="__JS__pinyin-match/dist/main.js" type="text/javascript"></script>

<!--<script src="https://cdn.jsdelivr.net/npm/vue-resource@1.5.0"></script>-->
<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/vue-paginate/3.6.0/vue-paginate.js"></script>-->

<!-- vue-easytable  文档最全的一个-->
<link rel="stylesheet" type="text/css" href="__CSS__vue-easytable.css">
<script src="__JS__vue-easytable.js" type="text/javascript"></script>

<!-- 引入select2 -->
<script src="__JS__vue-select.js" type="text/javascript"></script>

<!-- apertureless/vue-chartjs -->
<script src="__JS__Chart.min.js" type="text/javascript"></script>
<script src="__JS__vue-chartjs.min.js" type="text/javascript"></script>

<!-- json vue   -->
<link rel="stylesheet" type="text/css" href="__CSS__json-tree.css">
<!--<script src="__JS__json_tree.js" type="text/javascript"></script>-->
<script src="__JS__json-tree.js"></script>

<!-- 自定义组件 -->
<include file="Common@components/select2_template"/>

<include file="Common@components/dialog_template"/>

<!-- 引入公用得模态框组件 -->
<include file="Common@components/prompt_modal"/>

<script>
    // 引入v-select组件
    Vue.component('v-select', VueSelect.VueSelect);

    // 引入模态框
    Vue.component('dialog_template', {
        template: '#dialog'
    });

    Vue.use(window.VueQuillEditor);
    // 模态框输出信息
    function modalExport(msg, url) {
        $('#alert_content').text(msg);
        $('#exampleModal').modal({
            keyboard: false
        })
        // 如果传入url 则在两秒钟后跳转
        if (url !== undefined) {
            setTimeout(function () {
                window.location.href = url;
            }, 2000);
        }
    }

    $(function () {
        // 定期更新登录状态
        setInterval(function () {
            DHB.refresh_login("{:U('System/Auth/RefreshLogin')}");
        }, 1000 * 60 * 3);
    })
</script>

<script src="__JS__watermark/watermark.js"></script>
<script>
watermark.init({watermark_txt: "{$loginuser['email']}"})
</script>
