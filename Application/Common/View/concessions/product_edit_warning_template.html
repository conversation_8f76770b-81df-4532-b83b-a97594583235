<div class="modal fade" tabindex="-1" role="dialog" id="product_import_update" aria-labelledby="更新提示">
    <div class="modal-dialog modal-lg" role="document" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title title_column">以下重要信息进行了修改,您是否确定修改</h4>
            </div>
            <div class="modal-body table-responsive">
                <table class="table text-nowrap" id="show_import_field">
                    <thead>
                    <tr>
                        <th style="width: 20%">字段</th>
                        <th style="width: 40%">更新前的值</th>
                        <th style="width: 40%">更新后的值</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" onclick="closeWarningTr()">取消更新
                </button>
                <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="continueUpdate()">确定更新
                </button>
            </div>
        </div>
    </div>
</div>
<script>
    // 展示更新的内容
    function showUpdateContent(import_filed_data) {
        // 生成tr
        genTrForImportField(import_filed_data);
        $('#product_import_update').modal({});
    }

    // 确认按钮反馈
    function continueUpdate() {
        // 发送确认请求
        formRequest();

        // 也是需要关闭生成的tr的
        closeWarningTr();
    }

    // 关闭提示按钮
    function closeWarningTr() {
        $('tbody.wait_del').remove();
    }

    // 生成tr
    function genTrForImportField(import_filed_data) {
        let tr_collect = '';

        import_filed_data.forEach(function (item) {
            let tr_prefix = '<tbody class="wait_del"><tr>';
            let tr_middle = '<td>' + item.meaning + '</td>' + '<td>' + item.old + '</td>' + '<td style="color:red">' + item.new + '</td>';
            let tr_suffix = '</tr></tbody>';

            tr_collect += tr_prefix + tr_middle + tr_suffix;
        });

        $('#show_import_field').append(tr_collect);
    }
</script>