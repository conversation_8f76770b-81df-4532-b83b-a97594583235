<template id="upstream_verification_list_cmp">
    <div>
        <prompt_modal ref="prompt_modal_show"></prompt_modal>
        <div class="panel panel-default row">
            <div class="panel-heading">
                    <div>
                        <label>上游 --- 邦秒验</label>
                    </div>
                    <div>
                        <form class="form-inline">
                            <div class="form-group">
                                <label>日期选择</label>
                                <input type="date" class="form-control" v-model.trim=day_begin> ---
                                <input type="date" class="form-control" v-model.trim="day_end">
                            </div>

                            <div class="form-group select2-style-adjust">
                                <v-select :options="list_upstreams_label"  v-model="upstream" placeholder="选择来源"></v-select>
                            </div>

                            <div class="form-group select2-style-adjust">
                                <v-select :options="list_products_label"   label="product_name" v-model="product" placeholder="选择产品"></v-select>
                            </div>

                            <div class="form-group" style="float : right; ">
                                <button type="button" @click="search" class="btn btn-primary">查询</button>
                                <button type="button" @click="download" class="btn btn-success">导出 </button>
                                <button type="button" @click="downloadDay" class="btn btn-warning">导出天明细 </button>
                            </div>
                        </form>
                    </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    column-width-drag
                    :vertical-resize-offset='60'
                    :sort-always=sort_always
                    style="width:100%"
                    :multiple-sort="false"
                    :min-height="350"
                    even-bg-color="#f2f2f2"
                    :title-rows="tableConfig.titleRows"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @sort-change="sortChange"
                    :paging-index="(pageIndex-1)*pageSize"
            ></v-table>

            <div class="mt20 mb20 bold" style="margin-top:10px"></div>
            <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total" :page-size="pageSize"
                          :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
        </div>
    </div>
</template>
<script>
    Vue.component('upstream_verification_list_template', {
        template: '#upstream_verification_list_cmp',
        props: ['day_t_begin', 'day_t_end', 'backend_api_sorts', 'list_api', 'download_api','download_day_api',  'list_upstreams', 'list_products'],
        data: function () {
            return {
                product_key: 'fbaace1340a8706863ed6ae17560355c', // product key鉴权使用
                day_begin: this.day_t_begin,
                day_end: this.day_t_end,
                upstream : {}, // 选中的来源
                product : {},  // 选中的产品

                sort_always : true,
                total : 0, // 总 条数
                tableRange: [], // table 数据
                pageIndex:1,
                pageSize:20,
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'product_name',
                            title: '产品名',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter: (rowData,rowIndex,pagingIndex,field)=>{
                                if (rowData.product_name === '总计') {
                                    return rowData.product_name;
                                }
                                let begin =this.day_begin, end = this.day_end,
                                    upstream = rowData.upstream;
                                return `<a
href="/Home/Upstream/verificationDetails?product_id=${rowData.product_id}&begin=${begin}&end=${end}&upstream=${upstream}">
${rowData.product_name}</a>`;
                            }
                        },
                        {
                            field: 'success',
                            title: '有效调用量',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            orderBy : 'desc',
                            titleCellClassName: 'title_column',
                            formatter(rowData,rowIndex,pagingIndex,field){
                                return formatNumber(rowData.success, 0, 1);
                            }
                        },
                        {
                            field: 'yd_succ',
                            title: '移动有效调用量',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            orderBy : 'desc',
                            formatter : (rowData,rowIndex,pagingIndex,field) =>{
                                return formatNumber(rowData.yd_succ, 0, 1);
                            }
                        },
                        {
                            field: 'lt_succ',
                            title: '联通有效调用量',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            orderBy : 'desc',
                            formatter : (rowData,rowIndex,pagingIndex,field) =>{
                                return formatNumber(rowData.lt_succ, 0, 1);
                            }
                        },
                        {
                            field: 'dx_succ',
                            title: '电信有效调用量',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            orderBy : 'desc',
                            formatter : (rowData,rowIndex,pagingIndex,field) =>{
                                return formatNumber(rowData.dx_succ, 0, 1);
                            }
                        },
                        {
                            field: 'upstream_cn',
                            title: '数据源',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },

                    ]
                }
            }
        },
        mounted: function () {
            // 初始化列表
            this.search();
        },

        computed: {
            // 字段格式化
            list_products_label(){
                return JSON.parse(this.list_products);
            },

            // 来源格式化
            list_upstreams_label(){
                return JSON.parse(this.list_upstreams);
            },
            // 下载excel的文件名称
            fileName(){
                return  `(上游 ---邦秒验列表) ${this.day_begin} -- ${this.day_end}.xlsx`;
            },

            // 按天导出文件名
            fileDayName(){
                return  `(上游 ---邦秒验列表按天) ${this.day_begin} -- ${this.day_end}.xlsx`;
            },
        },
        methods: {
            // 按天导出
            downloadDay(){
                let params = {
                    params : this.searchParams(),
                    responseType: 'blob'
                };

                axios.get(this.download_day_api, params).then(response=>{
                    console.log(response, '按天下载');
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', this.fileDayName); //or any other extension
                    document.body.appendChild(link);
                    link.click();
                });
            },

            // 下载
            download(){

                axios.post(this.download_api, {list : this.tableRange, key : this.product_key}, {responseType: 'blob'}).then(response=>{
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', this.fileName); //or any other extension
                    document.body.appendChild(link);
                    link.click();
                });
            },
            // 查询参数
            searchParams(){
                // 如果没有选择客户 则不可以发送请求
                return {
                    day_begin: this.day_begin,
                    day_end: this.day_end,
                    key: this.product_key,
                    product_id : this.product ? this.product.product_id : '',
                    upstream : this.upstream ? this.upstream.value : '',
                };

            },
            search() {
                // 参数
                let params = this.searchParams();

                axios.get(this.list_api, {
                    params: params
                }).then((response) =>{
                    console.log(response, '查询响应');
                    if (response.data.status === 0) {
                    this.tableRange = response.data.lists;
                        this.getTableData();
                    } else {
                        this.$refs.prompt_modal_show.open({
                            title: '提示',
                            body: '获取列表失败 msg:' + response.data.msg
                        });
                    }
                });
            },
            getTableData(){
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize);
                this.total = this.tableRange.length;
            },
            pageChange(pageIndex){

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            pageSizeChange(pageSize){

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
            sortChange(params){
                console.log(params, '排序');
                Object.keys(params).forEach(key => {
                        if (params[key] === 'asc' || params[key] === 'desc') {
                            let item = {
                                list_source : this.tableRange,
                                field_sort_base : {
                                    field : key,
                                    sort_by : params[key]
                                },
                                key : this.product_key
                            };
                            axios.post(this.backend_api_sorts, item).then(response=>{
                                console.log(response, '排序回应');
                                if (response.data.status === 0) {
                                    this.tableRange = Object.values(response.data.lists);
                                    this.getTableData();
                                } else {
                                    vm.$refs.prompt_modal_show.open({
                                        title: '提示',
                                        body: '排序失败 msg:' + response.data.msg
                                    });
                                }
                            });
                        }
                    }
                )
            },

        },
        created(){
            this.getTableData();
        }
    });


    /**
     * 将数值四舍五入后格式化.
     *
     * @param num 数值(Number或者String)
     * @param cent 要保留的小数位(Number)
     * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
     * @return 格式的字符串,如'1,234,567.45'
     * @type String
     */
    function formatNumber(num,cent = 2,isThousand = 1){
        num = num.toString().replace(/\$|\,/g,'');

        // 检查传入数值为数值类型
        if(isNaN(num))
            num = "0";

        // 获取符号(正/负数)
        sign = (num == (num = Math.abs(num)));

        num = Math.floor(num*Math.pow(10,cent)+0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
        cents = num%Math.pow(10,cent);       // 求出小数位数值
        num = Math.floor(num/Math.pow(10,cent)).toString();  // 求出整数位数值
        cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

        // 补足小数位到指定的位数
        while(cents.length<cent)
            cents = "0" + cents;

        if(isThousand) {
            // 对整数部分进行千分位格式化.
            for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++)
                num = num.substring(0,num.length-(4*i+3))+','+ num.substring(num.length-(4*i+3));
        }

        if (cent > 0)
            return (((sign)?'':'-') + num + '.' + cents);
        else
            return (((sign)?'':'-') + num);
    }

</script>