<template id="wechat_warning_list">
    <div>
        <div class="row">
            <prompt_modal ref="prompt_modal"></prompt_modal>
            <div class="panel panel-default">
                <div class="panel-heading">
                        <label>微信报警</label>
                        <a class="btn btn-primary pull-right btn-sm" href="/Home/WechatManage/create" target="_blank">新增配置</a>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.stop.prevent="listStat">
                        <div>
                            <div class="form-group">
                                <label for="slug"> 选择微信配置</label>
                                <select v-model.trim="slug" id="slug" class="form-control">
                                    <option :value="item.slug"  v-for="item in list_wechat">{{ item.name }}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">查询&nbsp;<i class="icon-search"></i>
                                </button>
                            </div>
                        </div>
                </form>
            </div>
        </div>
    </div>

    <div class="row">
        <v-table
                is-horizontal-resize
                :multiple-sort="tableConfig.multipleSort"
                sort-always
                column-width-drag
                :is-loading="isLoading"
                style="width:100%"
                :columns="tableConfig.columns"
                :table-data="tableConfig.tableData"
                :show-vertical-border="true"
                row-hover-color="#eee"
                row-click-color="#edf7ff"
                @on-custom-comp="customCompFunc"
                :paging-index="(pageIndex-1)*pageSize"

        ></v-table>
        <div style="margin-top: 10px;">
            <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                          :page-size="pageSize"
                          :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
        </div>
    </div>

    </div>
</template>

<template id="edit_wechat">

</template>
<script>
    Vue.component('wechat_warning_list_template', {
        template: '#wechat_warning_list',
        props: ['ini_params'],
        data: function () {
            return {
                slug: '',
                list_wechat : [],
                isLoading: false,
                pageIndex: 1,
                pageSize: 20,
                total: 0,
                tableRange: [],
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'id',
                            title: 'ID',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'name',
                            title: '配置名称',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'creator',
                            title: '创建人',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'slug',
                            title: 'Slug',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'itag_id',
                            title: '标签ID',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'created_at',
                            title: '创建时间',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter :function (rowData) {
                                let date = new Date(1000*rowData.created_at);
                                return date.toLocaleString();
                            }
                        },
                        {
                            field: 'what_ever',
                            title: '操作',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter : function(rowData,rowIndex,pagingIndex,field){
                                return '<a target="_blank" href="/Home/WechatManage/edit?id='+ rowData.id +'" class="btn btn-xs btn-info">编辑</a>';
                            }
                        }
                    ]
                }
            }
        },
        mounted: function () {
            // 初始wechat配置
            this.iniWechat();

            // 初始化表格
            this.listStat();
        },
        methods: {
            iniWechat : function(){
                let vm = this;
                axios.get('/Api/BackendWechatManage/listWechat').then(function(response){
                    if (response.data.status === 0) {
                        vm.list_wechat = response.data.list_wechat;
                        vm.list_wechat.unshift({slug: '', name: '全部'});
                    } else {
                        console.log('初始化微信配置列表出错', response);
                    }
                }).catch(function(response){
                    console.log('初始化微信配置列表出错2', response);
                });
            },
            // 自定义列触发事件
            customCompFunc: function (params) {

            },
            listStat: function () {
                let params = {
                    slug : this.slug
                };
                console.log(params);
                let vm = this;
                axios.post('/Api/BackendWechatManage/searchWechat', params, {responseType: 'json'}).then(function (response) {
                    if (response.data.status === 0) {
                        vm.isLoading = false;
                        let list_range = Object.values(response.data.list_wechat);
                        // 重置页面数据
                        vm.iniTableData(list_range);
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: response.data.errors.msg
                        });
                    }
                }).catch(function(response){
                    console.log('获取选中的微信配置遇到错误', response);

                });
            },
            // 重置页面数据
            iniTableData: function (list_range) {
                this.tableRange = list_range;
                this.total = list_range.length - 1;
                this.getTableData();
            },
            // 重置当前页展示的数据
            getTableData: function () {
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize)
            },
            // 换页重置数据
            pageChange: function (pageIndex) {

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange: function (pageSize) {

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    // 操作组件
    Vue.component('edit_wechat', {
        template: "#edit_wechat",
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            customerShow: function () {
                let params = {type: 'customer_show', customer_id: this.rowData.customer_id, name: this.rowData.name};
                this.$emit('on-custom-comp', params);
            }
        }
    });

</script>