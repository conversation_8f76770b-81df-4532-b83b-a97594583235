<template id="wechat_warning_edit">
    <div class="row">
        <prompt_modal ref="prompt_template"></prompt_modal>
        <div class="panel panel-default">
            <div class="panel-heading">
                <label>编辑微信报警配置</label>
                <a href="/Home/WechatManage/listWechat" class="btn-sm btn btn-primary pull-right">返回列表</a>
            </div>
            <div class="panel-body">
                <form class="form-horizontal" @submit.prevent="update">
                    <div class="form-group">
                        <label for="name" class="col-sm-2 control-label">名称:</label>
                        <div class="col-sm-6">
                            <input type="text" v-model.trim="wechat.name" id="name" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="slug" class="col-sm-2 control-label">Slug:</label>
                        <div class="col-sm-6">
                            <input type="text" v-model.trim="wechat.slug" id="slug" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="agent_id" class="col-sm-2 control-label">企业ID:</label>
                        <div class="col-sm-6">
                            <input type="text" v-model.trim="wechat.corp_id" id="corp_id" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="agent_id" class="col-sm-2 control-label">AgentId:</label>
                        <div class="col-sm-6">
                            <input type="text" v-model.trim="wechat.agent_id" id="agent_id" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="secret" class="col-sm-2 control-label">Secret:</label>
                        <div class="col-sm-6">
                            <input type="text" v-model.trim="wechat.secret" id="secret" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="itag_id" class="col-sm-2 control-label">标签Id:</label>
                        <div class="col-sm-6">
                            <input type="text" v-model.trim="wechat.itag_id" id="itag_id" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="report_line" class="col-sm-2 control-label">配置参数:</label>
                        <div class="col-sm-6">
                            <textarea  id="report_line" rows="5" class=form-control v-model.trim="wechat.report_line"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="remark" class="col-sm-2 control-label">备注:</label>
                        <div class="col-sm-6">
                            <textarea  id="remark" rows="5" class=form-control v-model.trim="wechat.remark"></textarea>
                        </div>
                    </div>
                    <div class="form-group col-sm-8">
                        <button class="btn-primary pull-right btn-sm" type="submit">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
    Vue.component('wechat-warning-edit-template', {
        template: '#wechat_warning_edit',
        props : ['wechat_warning'],
        data : function(){
            return {
                source_wechat :{},
                wechat:{
                    id : '',
                    name: '',
                    slug: '',
                    agent_id: '',
                    secret: '',
                    itag_id: '',
                    report_line: '',
                    remark: '',
                    corp_id : '',
                }
            }
        },
        mounted : function(){
            this.iniParams();
        },
        methods : {
            iniParams : function(){
                this.source_wechat = JSON.parse(this.wechat_warning);

                this.wechat.name = this.source_wechat.name;
                this.wechat.slug = this.source_wechat.slug;
                this.wechat.agent_id = this.source_wechat.agent_id;
                this.wechat.itag_id = this.source_wechat.itag_id;
                this.wechat.secret = this.source_wechat.secret;
                this.wechat.report_line = this.source_wechat.report_line;
                this.wechat.remark = this.source_wechat.remark;
                this.wechat.id = this.source_wechat.id;
                this.wechat.corp_id = this.source_wechat.corp_id;
            },
            update : function() {
                let vm = this;
                axios.post('/Api/BackendWechatManage/update', vm.wechat, {responseType : 'json'}).then(function(response){
                    if (response.data.status === 0) {
                        vm.niceCallback(response);
                    } else {
                        vm.errorCallBack(response);
                    }

                }).catch(function (response) {
                    vm.errorCallBack(response);
                });
            },
            // 成功回调函数
            niceCallback: function(response){
                this.$refs.prompt_template.open({
                    title : '编辑微信报警配置',
                    body : '编辑成功',
                    btn_url_right : '/Home/WechatManage/listWechat',
                    btn_name_right : '返回列表',
                    btn_name_lef : '留在当前页面',
                });
            },
            // 失败回调函数
            errorCallBack : function (response) {
                console.log('编辑微信报警失败', response);
                let msg = '编辑失败 : ' + response.data.errors.msg;
                this.$refs.prompt_template.open({
                    title : '编辑微信报警配置',
                    body : msg,
                });
            }
            }
    });

</script>