<template id="email_bill">
    <div class="row">
        <prompt_modal ref="prompt_modal_show"></prompt_modal>
        <div class="panel panel-default">
            <div class="panel-heading">
                邮件发送对账单
            </div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">收件人</label>
                        <div class="col-sm-6">
                            <input type="text" v-model="email_received" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">抄送人</label>
                        <div class="col-sm-6">
                            <input type="text" v-model="email_copy" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2">主题</label>
                        <div class="col-sm-6">
                            <input type="text" v-model="theme" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2"> 内容</label>
                        <div class="col-sm-6">
                            <quill-editor v-model="content"
                                          ref="quillEditorA"
                                          :options="editorOption"
                                          @blur="onEditorBlur($event)"
                                          @focus="onEditorFocus($event)"
                                          @ready="onEditorReady($event)">
                            </quill-editor>
                        </div>
                    </div>

                    <!-- 历史结算单 -->
                    <history_bill_template :history="history" v-if="history_ready"></history_bill_template>

                    <!-- 消费明细 -->
                    <template v-if="consumption_ready && history_ready">
                        <!-- 萨摩耶 -->
                        <template v-if="false">
                            <!--isSameCustomer('C20181101XM4HQS')-->
                            <consumption_bill_samoye_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                         :customer_info="history.customer_info"></consumption_bill_samoye_cmp>
                        </template>

                        <!--  恒信永利 -->
                        <template v-else-if="isSameCustomer('C2019032668WHR7')">
                            <consumption_bill_hengli_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                         :customer_info="history.customer_info"></consumption_bill_hengli_cmp>
                        </template>

                        <!-- 凡普金科 C20190417PX262B -->
                        <template v-else-if="isSameCustomer('C20190417PX262B')">
                            <consumption_bill_fanpu_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                        :customer_info="history.customer_info"
                                                        :history="history"></consumption_bill_fanpu_cmp>
                        </template>

                        <!-- 特快 -->
                        <template v-else-if="false">
                            <!--isSameCustomer('C20181101PQ6OVN')-->
                            <consumption_bill_tekuai_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                         :customer_info="history.customer_info"></consumption_bill_tekuai_cmp>
                        </template>

                        <!-- 卡牛 -->
                        <template v-else-if="isSameCustomer('C20181101YRXUX2')">
                            <consumption_bill_kaniu_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                        :customer_info="history.customer_info"></consumption_bill_kaniu_cmp>
                        </template>

                        <!-- 随手记 -->
                        <template v-else-if="isSameCustomer('C20181101LHVREG')">
                            <consumption_bill_suishouji_cmp
                                    :list_product_alo_consumptions="consumption.list_account_show"
                                    :customer_info="history.customer_info"></consumption_bill_suishouji_cmp>
                        </template>

                        <!-- 量化派 -->
                        <!--<template v-else-if="isSameCustomer('C20181101SQ9IIO')">-->
                            <!--<consumption_bill_lianghuapai_cmp-->
                                    <!--:list_product_alo_consumptions="consumption.list_account_show"-->
                                    <!--:customer_info="history.customer_info"></consumption_bill_lianghuapai_cmp>-->
                        <!--</template>-->

                        <!-- 人人贷 -->
                        <template v-else-if="isSameCustomer('C20181101QU9C0N')">
                            <consumption_bill_renrendai_cmp
                                    :list_product_alo_consumptions="consumption.list_account_show"
                                    :customer_info="history.customer_info"></consumption_bill_renrendai_cmp>
                        </template>

                        <!--骑呗-->
                        <template v-else-if="isSameCustomer('C20181101G8K9DA')">
                            <consumption_bill_qibai_cmp
                                    :list_product_alo_consumptions="consumption.list_account_show"
                                    :customer_info="history.customer_info"></consumption_bill_qibai_cmp>
                        </template>

                        <!--极速云-->
                        <template v-else-if="isSameCustomer('C201903077LXWJM')">
                            <consumption_bill_qibai_cmp
                                    :list_product_alo_consumptions="consumption.list_account_show"
                                    :customer_info="history.customer_info"></consumption_bill_qibai_cmp>
                        </template>

                        <!--亿数科技-->
                        <template v-else-if="isSameCustomer('C2018110146N4NV')">
                            <consumption_bill_qibai_cmp
                                    :list_product_alo_consumptions="consumption.list_account_show"
                                    :customer_info="history.customer_info"></consumption_bill_qibai_cmp>
                        </template>

                        <!--读秒-->
                        <template v-else-if="isSameCustomer('C201811012TPQQ8')">
                            <consumption_bill_dumiao_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                         :customer_info="history.customer_info"></consumption_bill_dumiao_cmp>
                        </template>

                        <!--杭银消金-->
                        <template v-else-if="isSameCustomer('C20190801R97IIL')">
                            <consumption_bill_dumiao_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                         :customer_info="history.customer_info"></consumption_bill_dumiao_cmp>
                        </template>

                        <!--广汽租赁-->
                        <template v-else-if="isSameCustomer('C20190225VJOUQA')">
                            <consumption_bill_dumiao_cmp :list_product_alo_consumptions="consumption.list_account_show"
                                                         :customer_info="history.customer_info"></consumption_bill_dumiao_cmp>
                        </template>

                        <!--小象优品-->
                        <!--<template v-else-if="isSameCustomer('C201908074WYCOA')">-->
                            <!--<consumption_bill_product_fee_cmp :list_product_alo_consumptions="consumption.list_account_show"-->
                                                         <!--:customer_info="history.customer_info"></consumption_bill_product_fee_cmp>-->
                        <!--</template>-->
                        <!--通用模板-->
                        <template v-else>
                            <consumption_bill_details_template
                                    :list_product_alo_consumptions="consumption.list_account_show"
                                    :customer_info="history.customer_info"></consumption_bill_details_template>
                        </template>
                    </template>


                    <div class="form-group col-sm-8">
                        <button v-if="history_ready && consumption_ready" class="btn btn-primary" style="float: right"
                                @click.prevent="sendEmail">发送对账单
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
    Vue.component('email-bill-template', {
        props     : ['customer_id', 'backend_api_email_bill', 'backend_api_history', 'backend_api_email'],
        template  : '#email_bill',
        data      : function () {
            return {
                product_key      : 'fb53fddb7157dd76fd1bb656df4980a3', // 随意的一个product_key
                email_received   : '',
                email_copy       : '',
                theme            : '',
                content          : '',
                history          : '', // 结算单信息
                consumption      : '', // 消费明细
                editorOption     : {
                    theme: 'snow'
                },
                history_ready    : false,
                consumption_ready: false,
            }
        },
        mounted   : function () {
            // 初始化结算单
            this.initList();

            // 初始化消费明细
            this.initAloConsumptions();
        },
        components: {
            LocalQuillEditor: VueQuillEditor.quillEditor
        },
        methods   : {
            // 是否等于客户ID
            isSameCustomer(customer_id){
                return this.customer_id === customer_id;
            },

            // 发送邮件
            sendEmail: function () {
                let request_params = {
                    email_received               : this.email_received,
                    email_copy                   : this.email_copy,
                    theme                        : this.theme,
                    content                      : this.content,
                    history                      : this.history, // 结算单信息
                    key                          : this.product_key,
                    customer_info                : this.history.customer_info, // 选中的账号信息
                    list_product_alo_consumptions: this.consumption.list_account_show, // 产品账单算法明细
                }
                console.log(request_params, '发送邮件的参数');
                let vm = this;
                axios.post(this.backend_api_email, request_params).then(function (response) {
                    console.log('发送邮件返回', response);
                    if (response.data.status === 0) {
                        vm.$refs.prompt_modal_show.open({
                            title: '提示',
                            body : '邮件发送成功请查收'
                        });
                    } else {
                        vm.$refs.prompt_modal_show.open({
                            title: '错误提示',
                            body : '邮件发送失败 msg:' + response.data.msg
                        });
                    }
                });
            },

            // 初始化消费明细
            initAloConsumptions: function () {
                let api_excel = this.backend_api_email_bill + this.customer_id + '?key=' + this.product_key;
                let vm        = this;
                axios.get(api_excel).then(function (response) {
                    console.log(response, '消费明细数据完成获取');
                    if (response.data.status === 0) {
                        vm.consumption       = response.data.consumption;
                        vm.consumption_ready = true;
                    } else {
                        vm.consumption_ready = false;
                        vm.$refs.prompt_modal_show.open({
                            title: '提示',
                            body : '消费明细异常 msg:' + response.data.msg
                        });
                    }
                });
            },

            // 初始化结算单
            initList: function () {
                let api = this.backend_api_history + this.customer_id + '?key=' + this.product_key;
                let vm  = this;
                axios.get(api).then(function (response) {
                    console.log(response, '结算单数据完成获取');
                    if (response.data.status === 0) {
                        vm.theme          = response.data.history.theme;
                        vm.history        = response.data.history;
                        vm.content        = response.data.history.content;
                        vm.email_received = response.data.history.customer_info.bill_email;
                        vm.email_copy     = response.data.history.customer_info.bill_cc_email;
                        vm.history_ready  = true;
                    } else {
                        vm.history_ready = false;
                        vm.$refs.prompt_modal_show.open({
                            title: '提示',
                            body : '结算单异常 msg:' + response.data.msg
                        });
                    }
                });
            },

            onEditorBlur(quill) {
                console.log('editor blur!', quill)
            },
            onEditorFocus(quill) {
                console.log('editor focus!', quill)
            },
            onEditorReady(quill) {
                console.log('editor ready!', quill)
            }
        },
        computed  : {
            editorA() {
                return this.$refs.quillEditorA.quill
            },
            editorB() {
                return this.$refs.quillEditorB.quill
            }
        },
    });

</script>
