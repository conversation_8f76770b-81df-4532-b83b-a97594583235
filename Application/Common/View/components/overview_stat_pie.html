<template id="stat_overview_pie">
    <div style="margin-top:20px">
        <div class="row">
            <div class="col-sm-4">
                <stat-overview-pie-cuishou-v2-template></stat-overview-pie-cuishou-v2-template>
            </div>
            <div class="col-sm-4">
                <stat-overview-pie-bang-template></stat-overview-pie-bang-template>
            </div>
            <div class="col-sm-4">
                <stat-overview-pie-data-validate-template></stat-overview-pie-data-validate-template>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <stat-overview-pie-cuishou-xiangdan-template></stat-overview-pie-cuishou-xiangdan-template>
            </div>
            <div class="col-sm-4">
                <stat-overview-pie-cuishou-private-template></stat-overview-pie-cuishou-private-template>
            </div>

            <div class="col-sm-4">
                <!--<stat-overview-pie-miao_pei-template></stat-overview-pie-miao_pei-template>-->
                <stat-overview-pie-miao-pei-xiangdan-template></stat-overview-pie-miao-pei-xiangdan-template>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-4">
                <stat-overview-pie-miao-pei-single-template></stat-overview-pie-miao-pei-single-template>
            </div>
        </div>
        <!--<div class="row">-->
            <!--<div class="col-sm-4">-->
                <!--<stat-overview-pie-crawler-template></stat-overview-pie-crawler-template>-->
            <!--</div>-->
        <!--</div>-->

    </div>
</template>

<script>
    Vue.component('line-chart', {
        extends: VueChartJs.Pie,
        mounted () {
            this.renderChart({
                labels: ['VueJs', 'EmberJs', 'ReactJs', 'AngularJs'],
                datasets: [
                    {
                        backgroundColor: [
                            '#41B883',
                            '#E46651',
                            '#00D8FF',
                            '#DD1B16'
                        ],
                        data: [40, 20, 80, 10]
                    }
                ]
            }, {responsive: true, maintainAspectRatio: false})
        }

    });

    Vue.component('stat-overview-pie-template', {
        template: '#stat_overview_pie',
        data : function(){
            return {
                list_stat : [],
                msg : '测试'
            }
        },
    });

</script>