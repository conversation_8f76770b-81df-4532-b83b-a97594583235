<template id="consumption_bill_tekuai_xiangdan_v2">

        <tbody>
        <tr>
            <td></td>
            <th>服务项目</th>
            <td colspan="5">
                邦信分详单版v2
            </td>
        </tr>
        <tr>
            <td></td>
            <th>上月结转余额</th>
            <td colspan="5">
                {{ formatNumber(date_item.money_last_before_this_month.money_last_before_this_month) }}
            </td>
        </tr>
        <tr>
            <td></td>
            <td>期间</td>
            <td>充值金额</td>
            <td>单价</td>
            <td >调取量</td>
            <td colspan="2">消耗金额</td>
        </tr>
        <tr>
            <td></td>
            <td>{{ date_item.details.month_begin }} -- {{ date_item.details.month_end }}</td>
            <td>{{ formatNumber(date_item.details.recharge) }}</td>
            <td>{{ date_item.details.price }}</td>
            <td>{{formatNumber(date_item.details.number, 0) }}</td>
            <td colspan="2">{{ formatNumber(date_item.details.money)}}</td>
        </tr>
        <tr>
            <td></td>
            <td>合计</td>
            <td>{{ formatNumber(date_item.money_recharge_sum) }}</td>
            <td></td>
            <td ></td>
            <td colspan="2">{{ formatNumber(date_item.money_consume_sum)}}</td>
        </tr>
        <tr>
            <td></td>
            <td>剩余金额</td>
            <td colspan="5">{{ formatNumber(date_item.money_last_now.money_last_now) }}</td>
        </tr>
        </tbody>
</template>
<script>

    Vue.component('consumption_bill_tekuai_xiangdan_v2_cmp', {
        template: '#consumption_bill_tekuai_xiangdan_v2',
        props: ['date_item'],
        methods : {
            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        }
    });
</script>