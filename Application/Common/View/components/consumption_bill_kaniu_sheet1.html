<template id="consumption_bill_kaniu_sheet1">
        <tbody>
        <tr>
            <td :rowspan="month_counter+2"></td>
            <td>序号</td>
            <td>公司名称 </td>
            <td>目前总余额(元)</td>
            <td> 时间 </td>
            <td> 产品 </td>
            <td> 计费次数 </td>
            <td> 单价（元） </td>
            <td> 消耗金额（元） </td>
            <td> 合计（元） </td>
            <td> 本期充值（元）</td>
            <td> 余额（元）</td>
        </tr>
        <tr >
            <td :rowspan="month_counter+1">1</td>
            <td :rowspan="month_counter+1">{{ formatNumber(date_item.base_info.company) }}</td>
            <td :rowspan="month_counter+1">{{ formatNumber(date_item.base_info.money_last_now) }}</td>
            <td>{{ date_item.money_last_before_this_year.title}}</td>
            <td colspan="6"></td>
            <td>{{ formatNumber(date_item.money_last_before_this_year.money_residue) }}</td>
        </tr>
        <tr v-for="item in date_item.money_detail_this_month">
            <td>{{ item.month_begin }} -- {{ item.month_end }}</td>
            <td>{{ item.product_name }}</td>
            <td> {{ formatNumber(item.section_invoked_number, 0) }}</td>
            <td>{{ item.price }}</td>
            <td>{{ formatNumber(item.money_consume)}}</td>
            <td>{{ formatNumber(item.money_consume)}}</td>
            <td>{{ formatNumber(item.money_recharge)}}</td>
            <td>{{ formatNumber(item.money_last_now)}}</td>
        </tr>
        </tbody>
</template>
<script>

    Vue.component('consumption_bill_kaniu_sheet1_cmp', {
        template: '#consumption_bill_kaniu_sheet1',
        props: ['date_item'],
        computed : {
            // 需要循环的次数
            month_counter(){
                console.log( "测试",  this.date_item, typeof  this.date_item.money_detail_this_month);
                return this.date_item.money_detail_this_month.length;
            }
        },
        methods : {
            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        }
    });
</script>