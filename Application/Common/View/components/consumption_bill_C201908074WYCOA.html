<template id="consumption_bill_product_fee_cmp">
    <div class="form-group">
        <label class="control-label col-sm-2">消费明细</label>
        <div class="col-sm-6">
            <table class="table text-nowrap table-bordered table-hover">
                <thead>
                <th> 公司名称</th>
                <th colspan="11">{{ customer_info.company }}</th>
                </thead>
                <thead>
                <tr>
                    <th>客户名称</th>
                    <th colspan="11">{{ customer_info.name }}</th>
                </tr>
                </thead>

                <template v-for="alo_item in list_product_alo_consumptions">
                    <thead>
                    <tr>
                        <th>账号名称</th>
                        <th colspan="10">{{ alo_item.account_name }}</th>
                    </tr>
                    </thead>
                    <template v-for="item_bill in alo_item.list_bills">
                        <!--区分产品的结算单-->
                        <consumption_bill_product_fee_sheet_template v-if="item_bill.product_alo==='product_bill'"
                                                                          :date_item="item_bill.details"></consumption_bill_product_fee_sheet_template>

                        <!-- 通用按时间 -->
                        <consumption_bill_common_date_template v-if="item_bill.product_alo === '_1'"
                                                               :date_item="item_bill.details"></consumption_bill_common_date_template>

                        <!-- 如果通用的按用量 && 固定价格 -->
                        <consumption_bill_common_fixed_template v-if="item_bill.product_alo === '_2_1'"
                                                                :date_item="item_bill.details"></consumption_bill_common_fixed_template>
                        <!-- 通用的按用量 && 累进阶梯 -->
                        <consumption_bill_common_progression_template v-if="item_bill.product_alo === '_2_2'"
                                                                      :date_item="item_bill.details"></consumption_bill_common_progression_template>

                        <!-- 通用 && 达到阶梯 -->
                        <consumption_bill_common_reach_template v-if="item_bill.product_alo === '_2_3'"
                                                                :date_item="item_bill.details"></consumption_bill_common_reach_template>

                        <!-- 区分运营商按时间 -->
                        <consumption_bill_operator_date_template v-if="item_bill.product_alo === '_1_2'"
                                                                 :date_item="item_bill.details"></consumption_bill_operator_date_template>

                        <!-- 区分运营商按用量 && 固定价格 -->
                        <consumption_bill_operator_fixed_template v-if="item_bill.product_alo === '_2_1_2'"
                                                                  :fixed_item="item_bill.details"></consumption_bill_operator_fixed_template>

                        <!-- 累进阶梯 区分运营商按用量 && 到达阶梯 -->
                        <consumption_bill_operator_complex_template v-if="algOperatorCommon(item_bill.product_alo)"
                                                                    :date_item="item_bill.details"></consumption_bill_operator_complex_template>
                    </template>
                </template>
            </table>
        </div>
    </div>
</template>
<script>
    Vue.component('consumption_bill_product_fee_cmp', {
        props   : ['list_bill_section_group_alg', 'list_product_alo_consumptions', 'customer_info'],
        template: '#consumption_bill_product_fee_cmp',
        mounted : function () {
            console.log('明细', this.list_product_alo_consumptions);
//            this.list_bills = this.list_product_alo_consumptions.FA201812143COLWP.list_bills;
//            console.log(this.list_bills);
        },
        computed: {
            // 需要循环的次数
//            month_counter(){
//                console.log( "测试",  this.date_item, typeof  this.date_item.money_detail_this_month);
//                return this.date_item.money_detail_this_month.length;
//            }
        },
        filters : {
            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        },
        methods : {
            // 判断应该遵循的算法
            algTouch         : function (product_alo_item) {
                let index = product_alo_item.indexOf('_');
                if (index === -1) {
                    return '';
                }

                return product_alo_item.substr(index);
            },
            // 不可以输出数据
            cantExport       : function (alo_item) {
                return alo_item.hasOwnProperty('export_consumption_details') && alo_item.export_consumption_details === false;
            },
            // 是否是运营商通用的那种 （累进、到达）
            algOperatorCommon: function (alo) {
                return alo === '_2_3_2' || alo === '_2_2_2'
            }
        }
    });
</script>