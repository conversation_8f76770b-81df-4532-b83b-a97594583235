<template id="handle_log_list">
    <div>
        <div class="row">
            <prompt_modal ref="prompt_modal"></prompt_modal>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <label>后台日志</label>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.stop.prevent="listStat">
                        <div>
                            <div class="form-group">
                                <label for="handle_type">操作类型</label>
                                <select v-model="handle_type" id="handle_type" class="form-control">
                                    <option :value="item.slug" v-for="item in list_handle_type">{{ item.name }}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="handle_result">状态</label>
                                <select v-model="handle_result" id="handle_result" class="form-control">
                                    <option :value="item.slug" v-for="item in list_handle_result">{{ item.name }}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="handle_result">操作人</label>
                                <input type="text" v-model.trim="handle_user" id="handle_user" class="form-control">
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">查询&nbsp;<i class="icon-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    :multiple-sort="tableConfig.multipleSort"
                    sort-always
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                              :page-size="pageSize"
                              :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>
    </div>
</template>

<template id="detail_show_log">
    <a @click.prevent="detailShow" style="cursor: pointer">{{ rowData.id }}</a>

</template>
<script>
    Vue.component('handle_log_list_template', {
        template: '#handle_log_list',
        data: function () {
            return {
                list_handle_log : [],
                handle_type : '',
                list_handle_type : [
                    {name: '全部', slug:''},
                    {name:'微信', slug: 'wechat'},
                    {name:'日报', slug: 'report'},
                    {name: '新建', slug: 'create'},
                    {name: '更新', slug: 'update'},
                    {name: '删除', slug: 'del'},
                ],
                handle_result : '',
                list_handle_result : [
                    {name: '全部', slug:''},
                    {name: '正常', slug: 1},
                    {name:'错误', slug: 0},
                ],
                handle_user : '',

                isLoading: false,
                pageIndex: 1,
                pageSize: 20,
                total: 0,
                tableRange: [],
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'id',
                            title: 'ID',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            componentName : 'detail_show_log',
                        },
                        {
                            field: 'handle_type',
                            title: '操作类型',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'handle_result',
                            title: '状态',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter : function(rowData){
                                return parseInt(rowData.handle_result) === 0 ? '错误' : '正常';
                            }
                        },
                        {
                            field: 'handle_user',
                            title: '操作人',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'description',
                            title: '操作描述',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'created_at',
                            title: '创建时间',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter : function(rowData){
                                let time = new Date(rowData.created_at*1000);
                                return time.toLocaleString();
                            }
                        }
                    ]
                }
            }
        },
        mounted: function () {
            // 初始化表格
            this.listStat();
        },
        methods: {
            // 自定义列触发事件
            customCompFunc: function (params) {
                console.log(params, '触发了自动函数');
                this.$refs.prompt_modal.open({
                    title : '提示(详细json信息)',
                    body : params.rowData.content
                });
            },
            listStat: function () {
                let params = {
                    handle_user : this.handle_user,
                    handle_result : this.handle_result,
                    handle_type : this.handle_type
                };
                console.log(params);
                let vm = this;
                axios.post('/Api/BackendHandleLog/listLog', params, {responseType: 'json'}).then(function (response) {
                    console.log(response, '日志列表');
                    if (response.data.status === 0) {
                        vm.isLoading = false;
                        let list_range = Object.values(response.data.list_log);
                        // 重置页面数据
                        vm.iniTableData(list_range);
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: response.data.errors.msg
                        });
                    }
                }).catch(function(response){
                    console.log('获取选中的微信配置遇到错误', response);

                });
            },
            // 重置页面数据
            iniTableData: function (list_range) {
                this.tableRange = list_range;
                this.total = list_range.length - 1;
                this.getTableData();
            },
            // 重置当前页展示的数据
            getTableData: function () {
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize)
            },
            // 换页重置数据
            pageChange: function (pageIndex) {

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange: function (pageSize) {

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    // 操作组件
    Vue.component('detail_show_log', {
        template: "#detail_show_log",
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            detailShow : function () {
                let params = {rowData : this.rowData};
                this.$emit('on-custom-comp',params);
            }
        }
    });

</script>