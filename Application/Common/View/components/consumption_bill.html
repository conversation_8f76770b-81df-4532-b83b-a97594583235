<template id="consumption_bill_details">
    <div class="form-group">
        <label class="control-label col-sm-2">消费明细</label>
        <div class="col-sm-6 table-responsive">
            <table class="table text-nowrap table-bordered table-hover">
                <thead>
                <th> 公司名称</th>
                <th colspan="10">{{ customer_info.company }}</th>
                </thead>
                <thead>
                <tr>
                    <th>客户名称</th>
                    <th colspan="10">{{ customer_info.name }}</th>
                </tr>
                </thead>
                <!-- 循环渲染消费明细 -->
                <template v-for="alo_item in  list_product_alo_consumptions">
                    <thead>
                    <tr>
                        <th>账号名称</th>
                        <th colspan="10">{{ alo_item.account_name }}</th>
                    </tr>
                    </thead>

                    <!-- 循环计费片段 -->
                    <template v-for="item_bill in alo_item.list_bills">
                        <template v-if="cantExport(item_bill)">
                            <consumption_bill_cant_export_template
                                    :product_name="item_bill.product_name"></consumption_bill_cant_export_template>
                        </template>
                        <template v-else>
                            <!-- 通用按时间 -->
                            <consumption_bill_common_date_template v-if="item_bill.product_alo === '_1'"
                                                                   :date_item="item_bill.details"></consumption_bill_common_date_template>

                            <!-- 如果通用的按用量 && 固定价格 -->
                            <consumption_bill_common_fixed_template v-if="item_bill.product_alo === '_2_1'"
                                                                    :date_item="item_bill.details"></consumption_bill_common_fixed_template>
                            <!-- 通用的按用量 && 累进阶梯 -->
                            <consumption_bill_common_progression_template v-if="item_bill.product_alo === '_2_2'"
                                                                          :date_item="item_bill.details"></consumption_bill_common_progression_template>

                            <!-- 通用 && 达到阶梯 -->
                            <consumption_bill_common_reach_template v-if="item_bill.product_alo === '_2_3'"
                                                                    :date_item="item_bill.details"></consumption_bill_common_reach_template>

                            <!-- 区分运营商按时间 -->
                            <consumption_bill_operator_date_template v-if="item_bill.product_alo === '_1_2'"
                                                                     :date_item="item_bill.details"></consumption_bill_operator_date_template>

                            <!-- 区分运营商按用量 && 固定价格 -->
                            <consumption_bill_operator_fixed_template v-if="item_bill.product_alo === '_2_1_2'"
                                                                      :fixed_item="item_bill.details"></consumption_bill_operator_fixed_template>

                            <!-- 累进阶梯 区分运营商按用量 && 到达阶梯 -->
                            <consumption_bill_operator_complex_template v-if="algOperatorCommon(item_bill.product_alo)"
                                                                        :date_item="item_bill.details"></consumption_bill_operator_complex_template>
                        </template>
                    </template>
                </template>
            </table>
        </div>
    </div>
</template>
<script>
    Vue.component('consumption_bill_details_template', {
        props   : ['list_bill_section_group_alg', 'list_product_alo_consumptions', 'customer_info'],
        template: '#consumption_bill_details',
        mounted : function () {
            console.log('完成消费明细的初始化', this.list_product_alo_consumptions, this.customer_info);
        },
        methods : {
            // 判断应该遵循的算法
            algTouch         : function (product_alo_item) {
                let index = product_alo_item.indexOf('_');
                if (index === -1) {
                    return '';
                }

                return product_alo_item.substr(index);
            },
            // 不可以输出数据
            cantExport       : function (alo_item) {
                return alo_item.hasOwnProperty('export_consumption_details') && alo_item.export_consumption_details === false;
            },
            // 是否是运营商通用的那种 （累进、到达）
            algOperatorCommon: function (alo) {
                return alo === '_2_3_2' || alo === '_2_2_2'
            }
        }
    });
</script>