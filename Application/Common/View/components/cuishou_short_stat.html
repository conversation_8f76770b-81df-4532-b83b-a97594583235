<template id="cuishou_short_stat">
    <div>
        <div class="row">
            <prompt_modal ref="prompt_modal"></prompt_modal>
            <div class="panel panel-default">
                <div class="panel-heading">
                    邦信分快捷版数据统计
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.stop.prevent="listStat">
                        <div>
                            <div class="form-group">
                                <label class="control-label">开始时间：</label>
                                <input type="date" class="form-control div_right_margin" v-model="time_begin">
                            </div>
                            <div class="form-group">
                                <label class="control-label">结束时间：</label>
                                <input type="date" class="form-control div_right_margin" v-model="time_end">
                            </div>
                            <div class="form-group div_right_margin select2-style-adjust">
                                <ul class="list-inline">
                                    <li><label class="control-label">选择客户:</label></li>
                                    <li>
                                        <v-select :options="list_customer" label="name" @input="customerChange"
                                                  :placeholder="placeholder_customer" v-model="customer"
                                                  label="name"></v-select>
                                    </li>
                                </ul>
                            </div>
                            <div class="form-group div_right_margin select2-style-adjust">

                                <ul class="list-inline">
                                    <li><label class="control-label">账号签约状态：</label></li>
                                    <li>
                                        <v-select :options="list_contract_status" label="name"
                                                   @input="contractStatusChange"
                                                  v-model="contract_status"></v-select>
                                    </li>
                                </ul>
                            </div>
                            <div class="form-group pull-right">
                                <button type="submit" class="btn btn-primary">查询&nbsp;<i class="icon-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    :multiple-sort="tableConfig.multipleSort"
                    @sort-change="sortChange"
                    sort-always
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                              :page-size="pageSize"
                              :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>

    </div>
</template>

<template id="customer_field_short_stat">
    <a @click.stop.prevent="customerShow()" v-if="rowData.name !== '总计'" style="cursor: pointer;">{{ rowData.name}}</a>
    <span v-else>{{ rowData.name}}</span>
</template>
<template id="account_list_short">
    <ul class="list-inline">
        <li v-for="item in rowData.list_account" v-if="!!rowData.list_account">
            <a @click.stop.prevent="statDetail(item.account_id, item.account_name)" style="cursor: pointer;">{{ item.account_name }}&nbsp;</a>
        </li>
    </ul>
</template>
<script>
    Vue.component('cuishou-short-stat', {
        template: '#cuishou_short_stat',
        props: ['ini_params'],
        data: function () {
            return {
                time_begin: '',
                time_end: '',
                list_customer: [], // 客户列表
                customer: {name: '全部', customer_id: ''}, // 客户
                placeholder_customer: '请选择客户',
                list_contract_status: [
                    {name: '全部', value: ''},
                    {name: '已签约已付款', value: 1},
                    {name: '已签约未付款', value: 2},
                    {name: '未签约', value: 3},
                    {name: '特殊客户', value: 5},
                    {name: '其他', value: 4}
                ],
                contract_status: {name: '全部', value: ''},
                isLoading: false,
                pageIndex: 1,
                pageSize: 20,
                total: 0,
                tableRange: [],
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'customer_id',
                            title: '客户ID',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'name',
                            title: '客户名称',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            componentName: 'customer_field_short_stat'
                        },
                        {
                            field: 'name_account',
                            title: '账号名称',
                            width: 300,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            componentName: 'account_list_short'
                        },
                        {
                            field: 'sum_all',
                            title: '总查询量',
                            width: 100,
                            orderBy: 'desc',
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'sum_success',
                            title: '有效查询量',
                            width: 100,
                            orderBy: '',
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'rate_sum_success',
                            title: '有效查询率',
                            width: 100,
                            orderBy: '',
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        }
                    ]
                }
            }
        },
        mounted: function () {
            // 初始化参数
            this.initBaseParams();

            // 初始化客户列表
            this.initCustomerList();

            // 初始化表格
            this.listStat();
        },
        methods: {
            customerChange: function (customer) {
                if (!customer) {
                    this.customer = {name: '全部', customer_id: ''};
                }
            },
            contractStatusChange : function(contract_status){
                if (!contract_status) {
                    this.contract_status = {name: '全部', value: ''};
                }
            },
            sortChange: function (params) {
                // 执行排序的url
                let url = '/Api/BackendCuiShouShortStat/sortBy';

                // 排序的字段 && 方式
                let params_url = {
                    filter_field: '',
                    filter_order: '',
                    list_data: this.tableRange
                };

                Object.keys(params).forEach(function (key) {
                    if (params[key] === 'asc' || params[key] === 'desc') {
                        params_url.filter_field = key;
                        params_url.filter_order = params[key];
                    }
                });

                // 请求排序
                let vm = this;
                this.$http.post(url, params_url, {responseType: 'json'}).then(function (response) {
                    if (response.body.status === 0) {
                        vm.iniTableData(response.body.list_stat);
                    } else {
                        console.log('排序出错了，宝贝: ' + response);
                    }
                });
            },
            // 自定义列触发事件
            customCompFunc: function (params) {
                // 检查时间是否合法
                if (this.checkTime() === false) {
                    return false;
                }

                if (params.type === 'detail_show') {
                    let url_detail = '/Home/CuishouShortStat/detail?account_id=' + params.account_id + '&name=' + params.account_name
                        + '&stat_level=account' + '&customer_id=' + params.customer_id;
                    window.open(url_detail);
                } else if (params.type === 'customer_show') {
                    let url_detail = '/Home/CuishouShortStat/detail?customer_id=' + params.customer_id + '&name=' + params.name
                        + '&stat_level=customer';
                    window.open(url_detail);
                }
            },
            // 初始化参数
            initBaseParams: function () {
                let ini_params = JSON.parse(this.ini_params);
                this.time_begin = ini_params.begin;
                this.time_end = ini_params.end;
            },
            // 初始化客户列表
            initCustomerList: function () {
                let vm = this;
                axios.get('/Api/BackendCuiShouShortStat/customerList', {responseType: 'json'}).then(function (response) {
                    if (response.data.status === 0) {
                        vm.list_customer = response.data.list_customer;
                        vm.list_customer.unshift({name: '全部', customer_id: ''});
                    } else {
                        console.log('请求客户列表出错', response);
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: '网络故障, 请稍后再试'
                        });
                    }
                }).catch(function(response){
                    console.log('请求客户列表出错', response);
                    vm.$refs.prompt_modal.open({
                        title: '提示',
                        body: '网络故障, 请稍后再试'
                    });
                });
            },
            checkTime: function () {
                if (!this.time_begin) {
                    this.$refs.prompt_modal.open({
                        title: '提示',
                        body: '请选择开始时间'
                    });
                    return false;
                }

                if (!this.time_end) {
                    this.$refs.prompt_modal.open({
                        title: '提示',
                        body: '请选择结束时间'
                    });
                    return false;
                }
                return true;
            },
            listStat: function () {
                // 检查参数
                if (!this.checkTime()) {
                    return true;
                }

                let params = {
                    time_begin: this.time_begin,
                    time_end: this.time_end,
                    customer_id: !!this.customer ? this.customer.customer_id : '',
                    contract_status: !!this.contract_status ? this.contract_status.value : '',
                };
                console.log(params);
                let vm = this;
                axios.post('/Api/BackendCuiShouShortStat/listStat', params, {responseType: 'json'}).then(function (response) {
                    console.log(response);
                    if (response.data.status === 0) {
                        vm.isLoading = false;
                        let list_range = Object.values(response.data.list_stat);
                        // 重置页面数据
                        vm.iniTableData(list_range);
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: response.data.errors.msg
                        });
                    }
                });
            },
            // 重置页面数据
            iniTableData: function (list_range) {
                this.tableRange = list_range;
                this.total = list_range.length - 1;
                this.getTableData();
            },
            // 重置当前页展示的数据
            getTableData: function () {
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize)
            },
            // 换页重置数据
            pageChange: function (pageIndex) {

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange: function (pageSize) {

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    Vue.component('customer_field_short_stat', {
        template: "#customer_field_short_stat",
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            customerShow: function () {
                let params = {type: 'customer_show', customer_id: this.rowData.customer_id, name: this.rowData.name};
                this.$emit('on-custom-comp', params);
            }
        }
    });

    // 账号字段
    Vue.component('account_list_short', {
        template: '#account_list_short',
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            statDetail(account_id, account_name) {
                // 参数根据业务场景随意构造
                let params = {
                    type: 'detail_show',
                    account_id: account_id,
                    account_name: account_name,
                    customer_id: this.rowData.customer_id
                };
                this.$emit('on-custom-comp', params);
            }
        }
    })

</script>