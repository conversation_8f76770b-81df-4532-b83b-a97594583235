<template id="stat_overview_number">
    <div class="panel panel-default table-responsive">
        <prompt_modal ref="prompt_modal"></prompt_modal>
        <table class="table table-bordered table-striped table-hover">
            <thead>
            <tr>
                <th>日期</th>
                <th><a href="/Home/CuishouStat/index">邦信分详单版V1V1</a></th>
                <th><a href="/Stat/CuishouBillV2/index">邦信分详单版V2</a></th>
                <th><a href="/Home/CuishouPrivateStat/index">邦信分私有云</a></th>
                <th><a href="/Home/CuishouShortStat/index">邦信分快捷版</a></th>
                <th><a href="/Home/BmMatchingStat/index">邦秒配单号版</a></th>
                <th><a href="/Home/BmMatchingStat/index">邦秒配详单版</a></th>
                <th><a href="/Home/BangProductStat/index">邦企查</a></th>
                <!--<th><a href="/Home/BmCrawlerStat/index">邦秒爬</a></th>-->
                <th><a href="/Home/DataCheckStat/index">邦秒验</a></th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item, item_type) in list_stat" v-if="list_stat && item_type !== 'time_speed'">
                <th scope="row">{{ item.date_zh }}</th>
                <td>{{ item.cuishou_xiangdan }}</td>
                <td>{{ item.cuishou_v2 }}</td>
                <td>{{ item.cuishou_private}}</td>
                <td>{{ item.cuishou_short}}</td>
                <td>{{ item.pei_single}}</td>
                <td>{{ item.pei_xiangdan}}</td>
                <td>{{ item.bang}}</td>
                <!--<td>{{ item.crawler}}</td>-->
                <td>{{ item.data_validate}}</td>
            </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    Vue.component('stat-overview-number-template', {
        template: '#stat_overview_number',
        data : function(){
           return {
                list_stat : [],
            }
        },
        created : function(){
            let vm = this;
            axios.get('/Api/BackendStatOverview/amountTable').then(function(response){
                console.log(response, '数据概览 table');
                if (response.data.status === 0) {
                    vm.list_stat = response.data.list_stat
                } else {
                    vm.$refs.prompt_modal.open({title: '错误提示', body: response.data.errors.msg});
                }
            });
        }
    });

</script>