<template id="history_bill">
    <div class="form-group">
        <label class="control-label col-sm-2">结算单</label>
        <div class="col-sm-6 table-responsive">
            <table class="table text-nowrap table-bordered table-hover">
                <thead>
                    <th colspan="3" class="text-center">羽乐科技</th>
                </thead>
                <tbody>
                    <tr>
                        <th colspan="3" class="text-center">（北京羽乐创新科技有限公司）</th>

                    </tr>
                    <tr>
                        <th colspan="3" class="text-center">收费通知单</th>
                    </tr>
                    <tr>
                        <th>公司名称</th>
                        <th colspan="2">{{ history.customer_info.company }}</th>
                    </tr>
                    <tr>
                        <th>客户名称</th>
                        <th colspan="2">{{ history.customer_info.name }}</th>
                    </tr>
                    <tr>
                        <th>上月剩余金额</th>
                        <th colspan="2">{{ history.result.bill_month_start_residue_money }}</th>
                    </tr>
                    <tr>
                        <th>期间</th>
                        <th>充值金额</th>
                        <th>消耗金额</th>
                    </tr>
                    <tr>
                        <td> {{ history.result.bill_month_data.name }}</td>
                        <td> {{ history.result.bill_month_data.recharge }}</td>
                        <td> {{ history.result.bill_month_data.consume }}</td>
                    </tr>

                    <!-- 特殊的充值消费 -->
                    <tr v-for="item in history.result.bill_month_special_data">
                        <td>「{{ item.month }}」 {{item.name}}</td>
                        <td>{{ item.recharge }}</td>
                        <td>{{ item.consume }}</td>
                    </tr>

                    <tr>
                        <th>合计</th>
                        <th>{{ history.result.total.recharge }}</th>
                        <th>{{ history.result.total.consume }}</th>
                    </tr>
                    <tr>
                        <th>剩余金额</th>
                        <th colspan="2">{{ history.result.residue_money }}</th>
                    </tr>
                    <tr>
                        <th></th>
                        <th></th>
                        <th>北京羽乐创新有限公司</th>
                    </tr>
                    <tr>
                        <th></th>
                        <th></th>
                        <th>运营组</th>
                    </tr>
                    <tr>
                        <th></th>
                        <th></th>
                        <th>{{ history.today }}</th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
    Vue.component('history_bill_template', {
        props: ['history'],
        template: '#history_bill',
        mounted: function () {
            console.log(this.history, '结算单组件初始化完成!');
        },
        methods: {

            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber : function(num,cent = 2,isThousand = 1){
                num = num.toString().replace(/\$|\,/g,'');

                // 检查传入数值为数值类型
                if(isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num*Math.pow(10,cent)+0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num%Math.pow(10,cent);       // 求出小数位数值
                num = Math.floor(num/Math.pow(10,cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while(cents.length<cent)
                    cents = "0" + cents;

                if(isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++)
                        num = num.substring(0,num.length-(4*i+3))+','+ num.substring(num.length-(4*i+3));
                }

                if (cent > 0)
                    return (((sign)?'':'-') + num + '.' + cents);
                else
                    return (((sign)?'':'-') + num);
            }
        }
    });
</script>