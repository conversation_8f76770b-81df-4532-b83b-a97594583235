<template id="back_api_log_list"
    <div>
        <div class="row">
            <prompt_modal ref="prompt_modal"></prompt_modal>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <label>back api 原始数据查询</label>
                </div>
                <div class="panel-body">
                    <form class="form-inline">
                        <div style="margin-bottom: 10px">
                            <div class="form-group">
                                <label for="handle_type">选择场景</label>
                                <select v-model="log_type" id="handle_type" class="form-control">
                                    <option :value="item.slug" v-for="item in list_log_type">{{ item.name }}</option>
                                </select>
                            </div>
                        </div>
                        <template v-if="log_type === 'bill' ">
                            <div>
                                <div class="form-group item-margin-class">
                                    <label>产品ID</label>
                                    <input type="text" v-model.trim="params_bill.product_id" class="form-control">
                                </div>

                                <div class="form-group item-margin-class">
                                    <label>月份区间</label>
                                    <input type="month" v-model.trim="params_bill.month_begin" class="form-control"> --
                                    <input type="month" v-model.trim="params_bill.month_end" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>客户ID</label>
                                    <input type="text" v-model.trim="params_bill.customer_id" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>账号ID</label>
                                    <input type="text" v-model.trim="params_bill.account_id" class="form-control">
                                </div>
                            </div>
                            <div style="margin-top: 10px">
                                <div class="form-group item-margin-class">
                                    <label>UUID</label>
                                    <input type="text" v-model.trim="params_bill.uuid" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>计费配置ID</label>
                                    <input type="text" v-model.trim="params_bill.bill_config_id" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <button @click.prevent="searchBill()" class="btn btn-primary">查询&nbsp;<i
                                            class="icon-search"></i>
                                    </button>
                                </div>
                            </div>
                        </template>
                        <template v-if="log_type === 'log'">
                            <div>
                                <div class="form-group item-margin-class">
                                    <label for="handle_type">W-UUID</label>
                                    <input type="text" v-model.trim="params_log.w_uuid" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label for="handle_type">UUID</label>
                                    <input type="text" v-model.trim="params_log.uuid" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label for="handle_type">Action</label>
                                    <input type="text" v-model.trim="params_log.action" class="form-control">
                                </div>

                                <div class="form-group item-margin-class">
                                    <label for="handle_type">Type</label>
                                    <input type="text" v-model.trim="params_log.type" class="form-control">
                                </div>
                            </div>

                            <div style="margin-top: 10px">
                                <div class="form-group item-margin-class">
                                    <label for="handle_type">生成日期区间</label>
                                    <input type="date" v-model.trim="params_log.day_begin" class="form-control"> --
                                    <input type="date" v-model.trim="params_log.day_end" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <button @click.prevent="searchLog()" class="btn btn-primary">查询&nbsp;<i
                                            class="icon-search"></i>
                                    </button>
                                </div>
                            </div>
                        </template>
                        <template v-if="log_type === 'bill_day'">
                            <div>
                                <div class="form-group item-margin-class">
                                    <label>产品ID</label>
                                    <input type="text" v-model.trim="params_bill_day.product_id" class="form-control">
                                </div>

                                <div class="form-group item-margin-class">
                                    <label>月份区间</label>
                                    <input type="month" v-model.trim="params_bill_day.month_begin" class="form-control"> --
                                    <input type="month" v-model.trim="params_bill_day.month_end" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>客户ID</label>
                                    <input type="text" v-model.trim="params_bill_day.customer_id" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>账号ID</label>
                                    <input type="text" v-model.trim="params_bill_day.account_id" class="form-control">
                                </div>
                            </div>
                            <div style="margin-top: 10px">
                                <div class="form-group item-margin-class">
                                    <label>slug_id</label>
                                    <input type="text" v-model.trim="params_bill_day.slug_id" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>Date</label>
                                    <input type="date" v-model.trim="params_bill_day.date" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <button @click.prevent="searchBillDay()" class="btn btn-primary">查询&nbsp;<i
                                            class="icon-search"></i>
                                    </button>
                                </div>
                            </div>
                        </template>

                        <template v-if="log_type === 'bill_day_logs'">
                            <div>
                                <div class="form-group item-margin-class">
                                    <label>产品ID</label>
                                    <input type="text" v-model.trim="params_bill_day_log.product_id" class="form-control">
                                </div>

                                <div class="form-group item-margin-class">
                                    <label>月份区间</label>
                                    <input type="month" v-model.trim="params_bill_day_log.month_begin" class="form-control"> --
                                    <input type="month" v-model.trim="params_bill_day_log.month_end" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>客户ID</label>
                                    <input type="text" v-model.trim="params_bill_day_log.customer_id" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>账号ID</label>
                                    <input type="text" v-model.trim="params_bill_day_log.account_id" class="form-control">
                                </div>
                            </div>
                            <div style="margin-top: 10px">
                                <div class="form-group item-margin-class">
                                    <label>UUID</label>
                                    <input type="text" v-model.trim="params_bill_day_log.uuid" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>Date</label>
                                    <input type="date" v-model.trim="params_bill_day_log.date" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <button @click.prevent="searchBillDayLog()" class="btn btn-primary">查询&nbsp;<i
                                            class="icon-search"></i>
                                    </button>
                                </div>
                            </div>
                        </template>

                        <template v-if="log_type === 'statistic'">
                                <div class="form-group item-margin-class">
                                    <label>产品ID</label>
                                    <input type="text" v-model.trim="params_statistic.product_id" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>APIKEY</label>
                                    <input type="text" v-model.trim="params_statistic.apikey" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <label>节点</label>
                                    <input type="text" v-model.trim="params_statistic.node_area" class="form-control">
                                </div>

                                <div class="form-group item-margin-class">
                                    <label>日期区间</label>
                                    <input type="date" v-model.trim="params_statistic.day_begin" class="form-control"> --
                                    <input type="date" v-model.trim="params_statistic.day_end" class="form-control">
                                </div>
                                <div class="form-group item-margin-class">
                                    <button @click.prevent="searchStatistic()" class="btn btn-primary">查询&nbsp;<i
                                            class="icon-search"></i>
                                    </button>
                                </div>
                        </template>


                        <template v-if="log_type === 'stat_write'">
                            <div class="form-group item-margin-class">
                                <label>产品ID</label>
                                <input type="text" v-model.trim="params_stat_write.product_id" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>APIKEY</label>
                                <input type="text" v-model.trim="params_stat_write.apikey" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>LOG ID</label>
                                <input type="text" v-model.trim="params_stat_write.log_id" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>Success</label>
                                <input type="text" v-model.trim="params_stat_write.success" class="form-control" placeholder="成功 true 失败false">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>Province</label>
                                <input type="text" v-model.trim="params_stat_write.province" class="form-control" placeholder="省份需要契合的英文">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>Operator</label>
                                <input type="text" v-model.trim="params_stat_write.operator" class="form-control" placeholder="yidong liantong dianxin">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>Cralwer</label>
                                <!--bmp trd-->
                                <input type="text" v-model.trim="params_stat_write.cralwer" class="form-control" placeholder="">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>节点</label>
                                <input type="text" v-model.trim="params_stat_write.node_area" class="form-control">
                            </div>

                            <div class="form-group item-margin-class">
                                <label>日期区间</label>
                                <input type="date" v-model.trim="params_stat_write.day_begin" class="form-control"> --
                                <input type="date" v-model.trim="params_stat_write.day_end" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <button @click.prevent="searchStatWrite()" class="btn btn-primary">查询&nbsp;<i
                                        class="icon-search"></i>
                                </button>
                            </div>
                        </template>


                        <template v-if="log_type === 'cralwer_tels'">
                            <div class="form-group item-margin-class">
                                <label>产品ID</label>
                                <input type="text" v-model.trim="params_cralwer_tels.product_id" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>APIKEY</label>
                                <input type="text" v-model.trim="params_cralwer_tels.apikey" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <label>TEL</label>
                                <input type="text" v-model.trim="params_cralwer_tels.tel" class="form-control">
                            </div>

                            <div class="form-group item-margin-class">
                                <label>日期区间</label>
                                <input type="date" v-model.trim="params_cralwer_tels.day_begin" class="form-control"> --
                                <input type="date" v-model.trim="params_cralwer_tels.day_end" class="form-control">
                            </div>
                            <div class="form-group item-margin-class">
                                <button @click.prevent="searchCrawlerTel()" class="btn btn-primary">查询&nbsp;<i
                                        class="icon-search"></i>
                                </button>
                            </div>
                        </template>
                    </form>
                </div>
            </div>
            <div>
                <div v-if="total"  class="form-inline">
                    <div class="form-group">
                        <label class="control-label">共{{ total }}条 </label>

                    </div>
                    <div class="form-group">
                        <label class="control-label">第</label>
                         <input type="text" class="form-control" v-model="page">
                        <label class="control-label">页</label>
                    </div>
                    <div class="form-group">
                        <label class="control-label">每页</label>
                        <input type="text" class="form-control" v-model="page_size">
                        <label class="control-label">条</label>
                    </div>

                </div>
                <div>
                    <json-tree :data="data_show"></json-tree>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
    Vue.component('back_api_log_list_template', {
        template: '#back_api_log_list',
        props : ['log_api', 'bill_api', 'bill_day_api', 'bill_day_log_api', 'backend_api_stat_source_list', 'backend_api_stat_log', 'backend_api_crawler_tel'],
        data: function () {
            return {
                params_bill: {
                    bill_config_id: '', // 计费配置对应的ID
                    uuid: '',
                    month_begin: '',
                    month_end: '',
                    customer_id: '',
                    account_id: '',
                    product_id: '',
                },
                list_actions: {
                    'upstream': '上游数据入库相关',
                    'bill': '账单相关',

                },
                list_type: {
                'shortcut': '上游快捷版入库',
                    'verification' : '邦秒验'
                },
                params_log: {
                    uuid: '',
                    handle_user: '',
                    w_uuid: '',
                    day_begin: '', // 日志生成开始时间
                    day_end: '', // 日志生成结束时间
                    action : '', // 操作类型
                    type : '', // 类型
                },

                // 日账单相关
                params_bill_day : {
                    month_begin: '',
                    month_end: '',
                    customer_id: '',
                    account_id: '',
                    product_id: '',
                    date : '',  // 日期
                    slug_id : '', // 唯一标识
                },

                // 日账单日志
                params_bill_day_log : {
                    month_begin: '',
                    month_end: '',
                    customer_id: '',
                    account_id: '',
                    product_id: '',
                    date : '',  // 日期
                    uuid : '', // 唯一标识
                },

                // 统计表
                params_statistic : {
                    product_id: '',
                    apikey: '', // 账号apikey
                    day_begin: '', // 日志生成开始时间
                    day_end: '', // 日志生成结束时间
                    node_area : '', // 节点
                },

                // 统计字段入库
                params_stat_write: {
                    apikey : '',
                    product_id : '', // 产品ID
                    node_area : '', // 节点
                    log_id : '', // 日志ID
                    success : '', // true 成功, false失败
                    day_begin: '', // 日志生成开始时间
                    day_end: '', // 日志生成结束时间
                    province : '', // 省份
                    operator : '', // 运营商
                    cralwer : '', // 爬虫
                },

                // 爬虫电话信息
                params_cralwer_tels : {
                    product_id : '', // 产品ID
                    day_begin: '', // 日志生成开始时间
                    day_end: '', // 日志生成结束时间
                    apikey: '', // apikey
                    tel : '', // 电话
                },

                data_show : {}, // 要展示的数据
                level : 1, // json 展示的层级
                page : 1,
                page_size : 20,
                total : '',
                product_key : '5168b337cb7cdc2cd11675d634719ee9',
                log_type: 'bill',
                list_log_type: [
                    {name: '账单 bill_months', slug: 'bill'},
                    {name: 'handle log 日志', slug: 'log'},
                    {name: 'bill_day', slug: 'bill_day'},
                    {name: 'bill_day_logs', slug: 'bill_day_logs'},
                    {name: 'statistic', slug: 'statistic'},
                    {name: '统计字段入库日志', slug: 'stat_write'}
                ],
            }
        },
        mounted: function () {
        },
        methods: {
            // 爬虫号码
            searchCrawlerTel(){
                let params = this.params_cralwer_tels;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;
                axios.get(this.backend_api_crawler_tel, {params: params}).then(response=>{
                    console.log('数据统计回应', response);
                    this.data_show = response.data.lists.data;
                    this.total = response.data.lists.total;
                });
            },

            // 统计字段入库
            searchStatWrite(){
                let params = this.params_stat_write;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;
                axios.get(this.backend_api_stat_log, {params: params}).then(response=>{
                    console.log('数据统计回应', response);
                    this.data_show = response.data.lists.data;
                    this.total = response.data.lists.total;
                });
            },

            // 查询统计
            searchStatistic(){
                let params = this.params_statistic;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;
                axios.get(this.backend_api_stat_source_list, {params: params}).then(response=>{
                    console.log('数据统计回应', response);
                    this.data_show = response.data.lists.data;
                    this.total = response.data.lists.total;
                });
            },

            // 日账单日志
            searchBillDayLog(){
                let params = this.params_bill_day_log;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;
                axios.get(this.bill_day_log_api, {params: params}).then(response=>{
                    console.log('日账单相应', response);
                    this.data_show = response.data.lists.data;
                    this.total = response.data.lists.total;
                });

            },
            // 查询日账单
            searchBillDay(){
                let params = this.params_bill_day;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;
                axios.get(this.bill_day_api, {params: params}).then(response=>{
                    console.log('日账单相应', response);
                    this.data_show = response.data.lists.data;
                    this.total = response.data.lists.total;
                });
            },

            // 自定义列触发事件
            searchBill() {
                let params = this.params_bill;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;
                axios.get(this.bill_api, {params: params}).then(response=>{
                    console.log('账单相应', response);
                    this.data_show = response.data.lists.data;
                    this.total = response.data.lists.total;
                });
            },
            searchLog(){
                let params = this.params_log;
                params.key = this.product_key;
                params.page = this.page;
                params.page_size = this.page_size;

                axios.get(this.log_api, {params : params}).then(response=>{
                    console.log(response.data, '查询日志的相应');
                    if (response.data.status === 0) {
                        this.data_show = response.data.lists.data;
                        this.total = response.data.lists.total;
                    }
                });
            }
        }
    });

</script>