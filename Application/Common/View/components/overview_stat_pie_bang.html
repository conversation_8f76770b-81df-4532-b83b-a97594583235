<template id="stat_overview_pie_bang">
    <div>
        <prompt_modal ref="prompt_modal"></prompt_modal>
        <h5 class="title_column"><a href="/Home/BangProductStat/index">{{ type }}</a></h5>
        <div class="row">
            <ul class="list-inline select2-style-adjust">
                <li><label>请选择客户维度</label></li>
                <li>
                    <v-select :options="list_customer_dimension" @input="genPie" v-model="dimension_customer"
                              placeholder="客户维度"></v-select>
                </li>
            </ul>
            <ul class="list-inline select2-style-adjust">
                <li><label>请选择时间维度</label></li>
                <li>
                    <v-select :options="list_date_dimension" placeholder="时间维度" @input="genPie" v-model="dimension_date"></v-select>
                </li>
            </ul>
        </div>
        <div class="row">
            <bang-pie :labels="labels"  v-if="load" :data="data"></bang-pie>
        </div>
    </div>
</template>

<script>
    Vue.component('bang-pie', {
        extends: VueChartJs.Pie,
        props: ['labels', 'data'],
        data: function () {
            return {
                options : {responsive: true, maintainAspectRatio: false},
                data_options : {
                    labels: this.labels,
                    datasets: [
                        {
                            backgroundColor: [
                                '#800080', '#FF00FF', '#000080',
                                '#0000FF', '#008080', '#00FFFF',
                                '#008000', '#00FF00', '#808000',
                                '#FFFF00', '#800000', '#FF0000',
                                '#000000', '#808080', '#C0C0C0',
                                '#F08080', '#CD5C5C', '#9431B6'
                            ],
                            data:  this.data
                        }
                    ]
                }
            }
        },
        mounted() {
            this.renderChart(this.data_options, this.options)
        }
    });

    Vue.component('stat-overview-pie-bang-template', {
        template: '#stat_overview_pie_bang',
        data: function () {
            return {
                load : false,
                labels : [],
                data : [],
                list_stat: [],
                type: '邦企查',
                list_customer_dimension: [
                    {label: 'TOP3', value: 3},
                    {label: 'TOP5', value: 5},
                    {label: 'TOP10', value: 10},
                    {label: 'TOP15', value: 15},
                ],
                list_date_dimension: [
                    {label: '当天', value: 0},
                    {label: '近7天', value: 6},
                    {label: '近30天', value: 29},
                    {label: '近90天', value: 89},
                    {label: '近365天', value: 364},
                ],
                dimension_customer: {label: 'TOP3', value: 3},
                dimension_date: {label: '当天', value: 0},
            }
        },
        methods: {
            // 生成饼图
            genPie: function () {
                let queue_string = {
                    params: {
                        dimension_customer: this.dimension_customer.value,
                        dimension_date: this.dimension_date.value,
                        product_type: 'bang'
                    }
                }, vm = this;

                this.load = false;
                axios.get('/Api/BackendStatOverview/amountPie', queue_string).then(function (response) {
                    console.log(response, '邦企查饼图');
                    if (response.data.status === 0) {
                        vm.data = response.data.list_stat.list_customer_amount_cuted;
                        vm.labels = response.data.list_stat.list_customer_name_cuted;
                        vm.load = true;
                    } else {
                        vm.$refs.prompt_modal.open({title: '错误提示', body: response.data.errors.msg});
                    }
                });
            }
        },
        mounted: function () {
            this.genPie();
        },
    });

</script>