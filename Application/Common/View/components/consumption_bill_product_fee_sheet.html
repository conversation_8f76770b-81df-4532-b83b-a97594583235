<template id="consumption_bill_product_fee_sheet">
    <tbody>
    <tr>
        <td></td>
        <th>服务项目</th>
        <th colspan="10">
            {{ date_item.product_name }} - 结算单
        </th>
    </tr>
    <tr>
        <td></td>
        <th>上月剩余金额</th>
        <th colspan="10">{{ date_item.bill_month_prev_month_money }}</th>
    </tr>
    <tr>
        <th></th>
        <th>期间</th>
        <th>充值金额</th>
        <th colspan="10">消耗金额</th>
    </tr>
    <tr>
        <td></td>
        <td>{{ date_item.section_begin }} -- {{ date_item.section_end }}</td>
        <td>{{ date_item.recharge }}</td>
        <td colspan="10">{{ date_item.consume }}</td>
    </tr>
    <tr v-for="item in date_item.special">
        <td></td>
        <td>「{{ item.month }}」 {{ item.name }}</td>
        <td>{{ item.recharge }}</td>
        <td colspan="10">{{ item.consume }}</td>
    </tr>

    <tr v-for="item in date_item.special">
        <td></td>
        <th>合计</th>
        <td>{{ date_item.total_recharge }}</td>
        <td colspan="10">{{ date_item.total_consume }}</td>
    </tr>
    <tr v-for="item in date_item.special">
        <td></td>
        <th>剩余金额</th>
        <td colspan="10">0.00</td>
    </tr>
</template>
</tbody>
</template>
<script>

    Vue.component('consumption_bill_product_fee_sheet_template', {
        template: '#consumption_bill_product_fee_sheet',
        props   : ['date_item'],
        methods : {
            // 调用量跨过的区间
            numberSection: function (distribute) {
                return Object.keys(distribute).length;
            },

            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num   = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num   = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        }
    });
</script>