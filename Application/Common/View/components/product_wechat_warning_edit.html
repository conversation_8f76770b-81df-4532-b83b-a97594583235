<template id="product_wechat_warning_edit">
    <div class="row">
        <prompt_modal ref="prompt_template"></prompt_modal>
        <div class="panel panel-default">
            <div class="panel-heading">
                <label>编辑产品微信报警</label>
                <a href="/Home/WechatManage/listWechat" class="btn-sm btn btn-primary pull-right">返回列表</a>
            </div>
            <div class="panel-body">
                <form class="form-horizontal" @submit.prevent="update">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">选择场景:</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" v-model.trim="type" :disabled="disabled">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">选择产品:</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" v-model.trim="product_name" :disabled="disabled">
                        </div>
                    </div>

                    <div class="form-group select2-style-adjust">
                        <label class="col-sm-2 control-label">选择关联微信配置:</label>
                        <div class="col-sm-6">
                            <v-select v-model="wechat" multiple placeholder="选择关联的配置" label="name"
                                      :options="list_wechat"></v-select>
                        </div>
                    </div>
                    <div class="form-group col-sm-8">
                        <button class="btn-primary pull-right btn-sm" type="submit">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
    Vue.component('product_wechat_warning_edit_template', {
        template: '#product_wechat_warning_edit',
        props: ['product_id', 'edit_type', 'product_name'],
        data: function () {
            return {
                disabled: true,
                product: {},
                list_product: [],
                list_type: {
                    report: '日报'
                },
                type: {},
                list_slug: [],
                slug: {},
                list_wechat: [],
                wechat: [],
            }
        },
        mounted: function () {
            // 初始化微信配置
            this.initConfig();

            // 微信配置列表
            this.initWechatList();
        },
        methods: {
            // 初始化微信配置
            initConfig: function () {
                this.type = this.list_type[this.edit_type];

                // 初始化选中的条件
                let url = '/Api/BackendProductWechatWarning/wechatChoose';
                let params = {
                    product_id : this.product_id,
                    type : this.edit_type
                };
                let vm = this;
                axios.post(url, params).then(function (response) {
                    console.log(response);
                    if (response.data.status === 0) {
                        vm.wechat = response.data.list_wechat;
                    } else {
                        vm.$refs.prompt_template.open({
                            title : '获取微信配置报错',
                            body : response.data.errors.msg
                        });
                    }
                });
            },

            // 初始化微信配置
            initWechatList : function(){
                let url = '/Api/BackendProductWechatWarning/listWechat';
                let vm = this;
                axios.get(url).then(function (response) {
                    console.log(response);
                    if (response.data.status === 0) {
                        vm.list_wechat = response.data.list_wechat;
                    } else {
                        vm.$refs.prompt_template.open({
                            title : '获取微信配置报错',
                            body : response.data.errors.msg
                        });
                    }
                });
            },

            // 操作类型发生了变化
            changeOperateType: function () {
                if (!!this.type.slug) {
                    console.log(this.type, 'changed && true');
                    this.disabled = false;

                    // 格式化产品列表
                    this.initProductList();
                } else {
                    this.disabled = true;
                }
            },
            // 初始化产品列表
            initProductList: function () {
                let params = {
                    type: this.type.slug
                };

                let url = '/Api/BackendProductWechatWarning/listProductWithoutOpen';
                let vm = this;
                axios.post(url, params).then(function (response) {
                    if (response.data.status === 0) {
                        vm.list_product = response.data.list_products;
                    } else {
                        vm.$refs.prompt_template.open({
                            title: '获取没有开通' + vm.type.label + '场景的产品，失败',
                            body: response.data.errors.msg,
                        });
                    }
                });
            },

            // 检查条件
            checkParams: function () {
                if (!this.wechat[0]) {
                    this.$refs.prompt_template.open({
                        title: '提示',
                        body: '请选择关联的微信配置'
                    });
                    return false;
                }
                return true;
            },

            // 生成参数
            genParams: function () {
                let list_slug = [];
                this.wechat.forEach(function (element) {
                    list_slug.push(element.slug);
                });
                return {
                    list_slug: list_slug,
                    type: this.edit_type,
                    product_id: this.product_id
                };
            },

            update: function () {
                // 检查条件
                if (this.checkParams() === false) {
                    return false;
                }

                // 生成参数
                let params = this.genParams();
                let url = '/Api/BackendProductWechatWarning/update';
                let vm = this;
                axios.post(url, params).then(function (response) {
                    console.log(response);
                    if (response.data.status === 0) {
                        vm.niceCallback(response);
                    } else {
                        vm.errorCallBack(response);
                    }

                }).catch(function (response) {
                    vm.errorCallBack(response);
                });
            },
            // 成功回调函数
            niceCallback: function (response) {
                this.$refs.prompt_template.open({
                    title: '更新配置',
                    body: '成功',
                    btn_url_right: '/Home/ProductWechatWarning/index',
                    btn_name_right: '返回列表',
                    btn_name_lef: '留在当前页面',
                });
            },
            // 失败回调函数
            errorCallBack: function (response) {
                let msg = '更新失败 : ' + response.data.errors.msg;
                this.$refs.prompt_template.open({
                    title: '更新配置',
                    body: msg,
                });
            }
        }
    });

</script>