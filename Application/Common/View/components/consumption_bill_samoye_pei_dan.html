<template id="consumption_bill_samoye_pei_dan">

        <tbody>
        <tr>
            <td></td>
            <th>服务项目</th>
            <td colspan="4">
                邦秒配单号版
            </td>
        </tr>
        <tr>
            <td></td>
            <th>包年调用次数</th>
            <td colspan="4">
                {{ date_item.year_has_number }}
            </td>
        </tr>
        <tr>
            <td></td>
            <th>上月结转次数</th>
            <td colspan="4">
                {{ date_item.number_last_before_this_month }}
            </td>
        </tr>
        <tr>
            <td></td>
            <td>期间</td>
            <td colspan="4">有效调取量</td>
        </tr>
        <tr>
            <td></td>
            <td>{{ date_item.details.month_begin }} -- {{ date_item.details.month_end }}</td>
            <td colspan="4">{{ date_item.details.number }}</td>
        </tr>
        <tr>
            <td></td>
            <th>合计</th>
            <td colspan="4">{{ date_item.number_total_this_month }}</td>
        </tr>
        <tr>
            <td></td>
            <th>剩余调用次数</th>
            <td colspan="4">{{ date_item.number_last_now }}</td>
        </tr>
        </tbody>
</template>
<script>

    Vue.component('consumption_bill_samoye_pei_dan_cmp', {
        template: '#consumption_bill_samoye_pei_dan',
        props: ['date_item'],

    });
</script>