<script type="text/x-template" id="correct_comment">
    <div class="modal fade" id="modal_correct_comment" tabindex="-1" role="dialog" style="z-index: 8880">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header text-center" v-if="title">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" style="font-weight: bold;  word-wrap: break-word;word-break: break-all;">
                        {{ title }}
                    </h4>
                </div>

                <div class="modal-body text-center">
                    <textarea v-model.trim="comment" cols="30" rows="5" class="form-control"></textarea>
                </div>

                <!-- Modal Actions -->
                <div class="modal-footer">
                    <button type="button" :class="btn_class_left"
                            data-dismiss="modal"> {{ btn_name_left }}
                    </button>
                    <button type="button" :class="btn_class_right" @click.prevent="addComment" >{{ btn_name_right }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</script>

<script>
    Vue.component('correct_comment_cmp', {
        template: '#correct_comment',
        props: ['back_api_correct_add_comment'],
        data: function () {
            return {
                comment: '', // 备注
                product_key : '5168b337cb7cdc2cd11675d634719ee9', //

                modal_show: false,
                title: '增加备注',
                body: '',
                btn_name_left: '取消', // 左侧按钮的显示的文字,
                btn_name_right: '确定', // 左侧按钮的显示的文字,
                btn_class_left: 'btn btn-secondary', // 左侧按钮的类
                btn_class_right: 'btn btn-secondary btn-primary', // 右侧按钮的类
                btn_url_left: '', // 左侧按钮跳转的url
                btn_url_right: '', // 右侧按钮跳转的url
            }
        },
        methods: {
            // 初始化参数
            open: function (params) {
                this.uuid = params.uuid;
                this.operator = params.operator;
                this.comment = '';
                $('#modal_correct_comment').modal('toggle');
            },

            // 校验参数
            _validateParams(){
                if (!this.comment) {
                    this.$emit('exception', {title : '错误提示', body: '请输入备注'});
                    return false;
                }
                return true;
            },

            // 增加备注
            addComment(){
                // 校验参数
                if (!this._validateParams()) {
                    return;
                }

                // 参数
                let params = {
                    key: this.product_key,
                    uuid : this.uuid,
                    comment : this.comment,
                    operator : this.operator
                };

                // 请求
                axios.post(this.back_api_correct_add_comment, params).then(response=>{
                    console.log(response, '增加日志');
                    if (response.data.status === 0) {
                        // 通知新建备注事件
                        this.$emit('add', params);

                        // 关闭模态框
                        this.closeNow();
                    } else {
                        this.$emit('exception', {title : '错误提示', body: response.data.msg})
                    }
                });
            },
            // 关闭
            closeNow(){
                $('#modal_correct_comment').modal('toggle');
            }
        }
    });

</script>
