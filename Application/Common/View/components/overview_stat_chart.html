<template id="stat_overview_chart">
    <div>
        <prompt_modal ref="prompt_modal"></prompt_modal>
        <div class="container">
            <div class="panel panel-default">
                <div class="panel-body form-inline">
                    <div class="form-group">
                        <label class="control-label" for="start_time">开始时间</label>
                        <input type="date" id="start_time" v-model="start_time" @change="genChart" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="end_time">结束时间</label>
                        <input type="date" id="end_time" class="form-control" @change="genChart" v-model="end_time">
                    </div>
                    <div class="form-group">
                        <label class="control-label">选择产品</label>
                        <v-select :options="list_product" style="display:inline-block;" v-model="product_show" @input="productChange"
                                  label="product_name" multiple :placeholder="placeholder"></v-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <overview-line-chart :options="options" v-if="load"></overview-line-chart>
        </div>
    </div>
</template>

<script>
    Vue.component('overview-line-chart', {
        extends: VueChartJs.Line,
        props: ['options'],
        mounted: function () {
            this.renderChart(this.options, {responsive: true, maintainAspectRatio: false})
        }
    });

    Vue.component('stat-overview-chart-template', {
        template: '#stat_overview_chart',
        data: function () {
            return {
                counter: 0,
                options: [],
                load: false,
                start_time: '',
                end_time: '',
                placeholder: '选择产品',
                list_product: [
                    {product_name: '邦信分详单版V1', product_tag: 'cuishou_xiangdan'},
                    {product_name: '邦信分详单v2', product_tag: 'cuishou_v2'},
                    {product_name: '邦信分私有云', product_tag: 'cuishou_private'},
                    // {product_name: '邦秒配', product_tag: 'miao_pei'},
                    {product_name: '邦企查', product_tag: 'bang'},
                    {product_name: '邦秒验', product_tag: 'data_validate'},
                    {product_name: '邦秒配详单版', product_tag: 'pei_single'},
                    {product_name: '邦秒配单号版', product_tag: 'pei_xiangdan'},
                ],
                product_show: [
                    {product_name: '邦信分详单版V1', product_tag: 'cuishou_xiangdan'},
                    {product_name: '邦企查', product_tag: 'bang'},
                ]
            }
        },
        mounted: function () {
            // 初始化时间
            this.initTime();
        },
        methods: {
            // 产品发生了变化
            productChange: function () {
                this.counter += 1;
                if (this.counter !== 1) {
                    this.genChart();
                }
            },
            // 初始化时间
            initTime: function () {
                let vm = this;
                axios.get('/Api/BackendStatOverview/initTime').then(function (response) {
                    console.log(response, '初始化折线图时间');
                    if (response.data.status === 0) {
                        vm.start_time = response.data.init_time.start_time;
                        vm.end_time = response.data.init_time.end_time;

                        // 初始化折线图
                        vm.genChart();
                    }
                });
            },
            // 生成折线图
            genChart: function () {
                let vm = this;
                this.load = false;
                let config = this.genParams();
                axios.get('/Api/BackendStatOverview/amountChart', config).then(function (response) {
                    console.log(response, '折线图数据');
                    if (response.data.status === 0) {
                        vm.options = response.data.chart;
                        vm.load = true;
                    } else {
                        vm.$refs.prompt_modal.open({
                            title : '提示',
                            'body' : response.data.errors.msg
                        });
                    }
                });
            },
            genParams: function () {
                let list_product_type = this.product_show.map(function (item) {
                    return item.product_tag;
                });

                return {
                    params: {
                        start_time: this.start_time,
                        end_time: this.end_time,
                        list_product_type: list_product_type
                    }
                };
            }
        }
    });

</script>