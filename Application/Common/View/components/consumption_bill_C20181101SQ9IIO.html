<template id="consumption_bill_lianghuapai">
    <div class="form-group">
        <label class="control-label col-sm-2">消费明细</label>
        <div class="col-sm-6">
            <table class="table text-nowrap table-bordered table-hover">
                <thead>
                <th> 公司名称</th>
                <th colspan="11">{{ customer_info.company }}</th>
                </thead>
                <thead>
                <tr>
                    <th>客户名称</th>
                    <th colspan="11">{{ customer_info.name }}</th>
                </tr>
                </thead>
                <tbody>
                <!--邦信分详单版V1-->
                <tr>
                    <th></th>
                    <th>服务项目</th>
                    <th colspan="11">{{list_product_alo_consumptions.FA201812143COLWP.list_bills[101].product_name}}</th>
                </tr>
                <tr>
                    <td></td>
                    <td>上月结转余额</td>
                    <td colspan="11">{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[101].details.carry) }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td>期间</td>
                    <td>充值金额</td>
                    <td>单价</td>
                    <td>有效调用量</td>
                    <td>消耗金额</td>
                </tr>
                <tr>
                    <td></td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[101].details.section_begin}} -
                        {{list_product_alo_consumptions.FA201812143COLWP.list_bills[101].details.section_end}}
                    </td>
                    <td>0.00</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[101].details.price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[101].section_number.fee_number}}
                    </td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[101].details.money}}</td>
                </tr>
                <!--邦秒验-->
                <tr>
                    <th></th>
                    <th>服务项目</th>
                    <th colspan="11">邦秒验</th>
                </tr>
                <tr>
                    <td></td>
                    <td>上月结转余额</td>
                    <td colspan="11">{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[200].details.carry)}}</td>
                </tr>
                <tr>
                    <td></td>
                    <td>期间</td>
                    <td>充值金额</td>
                    <td colspan="3">消耗金额</td>
                </tr>
                <tr>
                    <td></td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[200].details.section_begin}} -
                        {{list_product_alo_consumptions.FA201812143COLWP.list_bills[200].details.section_end}}
                    </td>
                    <td>0.00</td>
                    </td>
                    <td colspan="3">{{list_product_alo_consumptions.FA201812143COLWP.list_bills[200].details.money}}</td>
                </tr>
                <!--手机号在网时长-->
                <tr>
                    <td></td>
                    <td>服务项目</td>
                    <td colspan="11">{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].product_name}}</td>
                </tr>
                <tr>
                    <td></td>
                    <td>期间</td>
                    <td>运营商</td>
                    <td>单价</td>
                    <td>有效调用量</td>
                    <td>消耗金额</td>
                </tr>
                <tr>
                    <td rowspan="3"></td>
                    <td rowspan="3">{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.section_begin}} -
                        {{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.section_end}}
                    </td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[0].operator}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[0].price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[0].number}}</td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[0].money) }}
                    </td>
                </tr>
                <tr>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[1].operator}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[1].price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[1].number}}</td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[1].money) }}
                    </td>
                </tr>
                <tr>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[2].operator}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[2].price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[2].number}}</td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.list_distribute[2].money) }}
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>合计</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[202].details.money) }}
                    </td>
                </tr>
                <!--手机号三要素验证-->
                <tr>
                    <td></td>
                    <td>服务项目</td>
                    <td colspan="11">{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].product_name}}</td>
                </tr>
                <tr>
                    <td></td>
                    <td>期间</td>
                    <td>运营商</td>
                    <td>单价</td>
                    <td>有效调用量</td>
                    <td>消耗金额</td>
                </tr>
                <tr>
                    <td rowspan="3"></td>
                    <td rowspan="3">{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.section_begin}} -
                        {{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.section_end}}
                    </td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[0].operator}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[0].price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[0].number}}</td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[0].money) }}</td>
                </tr>
                <tr>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[1].operator}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[1].price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[1].number}}</td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[1].money) }}</td>
                </tr>
                <tr>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[2].operator}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[2].price}}</td>
                    <td>{{list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[2].number}}</td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.list_distribute[2].money) }}</td>
                </tr>
                <tr>
                    <td></td>
                    <td>合计</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>{{ formatNumber(list_product_alo_consumptions.FA201812143COLWP.list_bills[213].details.money) }}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
<script>
    Vue.component('consumption_bill_lianghuapai_cmp', {
        props   : ['list_bill_section_group_alg', 'list_product_alo_consumptions', 'customer_info'],
        template: '#consumption_bill_lianghuapai',
        mounted : function () {
            console.log(this.list_product_alo_consumptions.FA201812143COLWP.list_bills);
//            this.list_bills = this.list_product_alo_consumptions.FA201812143COLWP.list_bills;
//            console.log(this.list_bills);
        },
        computed: {
            // 需要循环的次数
//            month_counter(){
//                console.log( "测试",  this.date_item, typeof  this.date_item.money_detail_this_month);
//                return this.date_item.money_detail_this_month.length;
//            }
        },
        methods: {
            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        }
    });
</script>