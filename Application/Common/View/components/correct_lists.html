<template id="correct_list">
    <div>
        <prompt_modal ref="prompt_modal_show"></prompt_modal>
        <correct_create_template ref="correct_create" @exception="listenExceptionEvent" @refresh="onRefresh"></correct_create_template>
        <correct_check_template ref="correct_check" :back_api_correct_comments="back_api_correct_comments" @exception="listenExceptionEvent"></correct_check_template>
        <correct_edit_template ref="correct_edit"  :new_comment="new_comment"  @comment="addComment" :back_api_correct_comments="back_api_correct_comments"   @exception="listenExceptionEvent"></correct_edit_template>
        <correct_comment_cmp ref="correct_comment" @add="createdNewComment"  :back_api_correct_add_comment="back_api_correct_add_comment" @exception="listenExceptionEvent"></correct_comment_cmp>

        <div class="panel panel-default row">
            <div class="panel-heading">
                <div>
                    <label>校正账单调用量</label>
                </div>
                <div v-if="titleShow">
                    <label style="color: yellowgreen">{{ titleShow }}</label>
                </div>
            </div>
            <div class="panel-body">
                <form class="form-inline">
                    <div class="form-group">
                        <label>选择月份</label>
                        <input type="month" class="form-control" v-model="month_begin">
                        <input type="month" class="form-control" v-model="month_end">
                    </div>
                    <div class="form-group select2-style-adjust">
                        <v-select :options="list_customers" label="name" v-model="customer"
                                  placeholder="选择客户"></v-select>
                    </div>
                    <div class="form-group select2-style-adjust">
                        <v-select :options="list_products" label="product_name" v-model="product"
                                  placeholder="选择产品"></v-select>
                    </div>
                    <div class="form-group select2-style-adjust">
                        <v-select :options="list_operators" v-model="operator" placeholder="选择操作人"></v-select>
                    </div>

                    <div class="form-group">
                        <button type="button" @click="search" class="btn btn-primary">查询</button>
                        <button type="button" @click="download" class="btn btn-success">下载</button>
                        <button type="button" @click="create" class="btn btn-warning">新建</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    column-width-drag
                    :vertical-resize-offset='60'
                    :sort-always=sort_always
                    style="width:100%"
                    :multiple-sort="false"
                    :min-height="350"
                    even-bg-color="#f2f2f2"
                    :title-rows="tableConfig.titleRows"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @sort-change="sortChange"
                    :paging-index="(pageIndex-1)*pageSize"
                    @on-custom-comp="customCompFunc"
            ></v-table>

            <div class="mt20 mb20 bold" style="margin-top:10px"></div>
            <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                          :page-size="pageSize"
                          :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
        </div>
    </div>
</template>
<script>
    Vue.component('correct_list_template', {
        template: '#correct_list',
        props: ['month_t_begin', 'month_t_end', 'list_t_customers', 'list_t_products', 'back_api_correct_numbers', 'back_api_correct_comments',
            'login_user', 'back_api_correct_numbers_create', 'back_api_correct_operators', 'api_download', 'back_api_correct_add_comment', 'backend_api_sorts'],
        data: function () {
            return {
                month_begin: this.month_t_begin,
                month_end: this.month_t_end,
                product_key: 'fbaace1340a8706863ed6ae17560355c', // product key鉴权使用

                // list_customers : [], // 客户列表
                customer: {}, // 选中的客户
                // list_products : [], // 产品列表
                product: {}, // 选中的产品
                list_operators: [], // 操作人列表
                operator: {}, // 选中的操作人
                total_stat: null, //

                new_comment : '', // 新建的备注

                sort_always: true,
                total: 0, // 总 条数
                tableRange: [], // table 数据
                pageIndex: 1,
                pageSize: 20,
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'standard_day',
                            title: '校正日期',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            orderBy: 'desc',
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'customer_name',
                            title: '客户名称',
                            width: 300,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'product_name',
                            title: '产品',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'account_name',
                            title: '账号',
                            width: 300,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'data.source_number',
                            title: '实际用量',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter(rowData) {
                                return rowData.type === 'common' ? rowData.data.source_number : (rowData.data.yd_source_number
                                    + ',' + rowData.data.lt_source_number + ',' + rowData.data.dx_source_number);
                            }
                        },
                        {
                            field: 'data.updated_number',
                            title: '更新后用量',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter(rowData) {
                                return rowData.type === 'common' ? rowData.data.updated_number : (rowData.data.yd_updated_number
                                    + ',' + rowData.data.lt_updated_number + ',' + rowData.data.dx_updated_number);
                            }
                        },
                        {
                            field: 'created_at',
                            title: '修复时间',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'operator',
                            title: '操作人',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'custome-adv',
                            title: '操作',
                            width: 200,
                            titleAlign: 'center',
                            columnAlign: 'center',
                            componentName: 'table-operation',
                            titleCellClassName: 'title_column',
                            isResize: true
                        }
                    ]
                }
            }
        },
        mounted: function () {
            // 初始化环境
            this.initEnv();

            // 初始化列表
            this.search();
        },

        computed: {
            // title展示
            titleShow() {
                let total = this.total_stat;
                return !total ? '' : `总实际调用量 ${total.source_number} 校正后用量 ${total.updated_number} 差为${total.source_number - total.updated_number}`;
            },

            list_customers() {
                return JSON.parse(this.list_t_customers);
            },
            list_products() {
                return JSON.parse(this.list_t_products);
            },

            fileName() {
                return `(校正账单调用量) ${this.month_begin} -- ${this.month_end}.xlsx`;
            },
        },
        methods: {
            // 新建刷新列表
            onRefresh(){
                this.search();
            },

            // 已经创建了新的备注
            createdNewComment(option){
                console.log('新建了一个备注', option);
                this.new_comment = option ? option : '';
            },

            // 增加备注
            addComment(option){
                option.operator = this.login_user;
                this.$refs.correct_comment.open(option);
            },

            // 自定义组件
            customCompFunc(params){
                // 查看
                if (params.type === 'check') {
                    this.$refs.correct_check.open(params.rowData);
                } else {
                    // 编辑
                    this.$refs.correct_edit.open(params.rowData);
                }

            },

            // 初始化环境
            initEnv() {
                axios.get(this.back_api_correct_operators, {params: {key: this.product_key}}).then(response => {
                    console.log(response, '获取操作人列表');
                    if (response.data.status === 0) {
                        this.list_operators = response.data.list.map(item => {
                            return {
                                label: item,
                                value: item
                            };
                        });
                    } else {
                        this.$refs.prompt_modal_show.open({
                            title: '提示',
                            body: '获取操作人列表失败 msg:' + response.data.msg
                        });
                    }
                });
            },

            // 监听说明事件
            listenExceptionEvent(option) {
                this.$refs.prompt_modal_show.open(option);
            },

            // 新建
            create() {
                this.$refs.correct_create.open({
                    login_user: this.login_user,
                    back_api_correct_numbers_create: this.back_api_correct_numbers_create
                });
            },

            // 下载
            download() {
                axios.post(this.api_download, {
                    list: this.tableRange,
                    key: this.product_key
                }, {responseType: 'blob'}).then(response => {
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', this.fileName); //or any other extension
                    document.body.appendChild(link);
                    link.click();
                });
            },
            // 查询参数
            searchParams() {
                // 当选中客户和客户ID冲突的时候给出提示

                return {
                    month_begin: this.month_begin,
                    month_end: this.month_end,
                    key: this.product_key,
                    customer_id: this.customer && Object.values(this.customer).length > 0 ? this.customer.customer_id : '',
                    product_id: this.product && Object.values(this.product).length > 0 ? this.product.product_id : '',
                    operator: this.operator && Object.values(this.operator).length > 0 ? this.operator.value : '',
                };

            },
            search: function () {
                let params = this.searchParams();

                axios.get(this.back_api_correct_numbers, {params}).then((response) => {
                    console.log(response, '查询事件');
                    if (response.data.status === 0) {
                        this.tableRange = response.data.list.list;
                        this.total_stat = response.data.list.total;

                        this.getTableData();
                    } else {
                        this.$refs.prompt_modal_show.open({
                            title: '提示',
                            body: '获取列表失败 msg:' + response.data.msg
                        });
                    }
                });
            },

            getTableData() {
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize);
                this.total = this.tableRange.length;
            },
            pageChange(pageIndex) {

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            pageSizeChange(pageSize) {

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
            sortChange(params) {
                Object.keys(params).forEach(key => {
                        if (params[key] === 'asc' || params[key] === 'desc') {
                            let item = {
                                list_source: this.tableRange,
                                field_sort_base: {
                                    field: key,
                                    sort_by: params[key]
                                },
                                key: this.product_key
                            };
                            axios.post(this.backend_api_sorts, item).then(response => {
                                console.log(response, '排序回应');
                                if (response.data.status === 0) {
                                    this.tableRange = Object.values(response.data.lists);
                                    this.getTableData();
                                } else {
                                    this.$refs.prompt_modal_show.open({
                                        title: '提示',
                                        body: '排序失败 msg:' + response.data.msg
                                    });
                                }
                            });
                        }
                    }
                )
            },

            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        },
        created() {
            this.getTableData();
        }
    });


    // 自定义列组件
    Vue.component('table-operation',{
        template:`<span>
        <a href="" @click.stop.prevent="update(rowData,index)" class="btn btn-warning btn-sm" v-if="canEdit">编辑</a>&nbsp;
        <a href="" @click.stop.prevent="check(rowData,index)" class="btn btn-default btn-sm">查看</a>
        </span>`,
        props:{
            rowData:{
                type:Object
            },
            field:{
                type:String
            },
            index:{
                type:Number
            }
        },
        computed : {
            // 是否有编辑的权限
            canEdit(){
                // 肥哦
                let today = new Date();
                let year = today.getFullYear(), month=today.getMonth();
                month = month < 10 ? '0' + month : month;
                let time = (year + '' + month).toString();

                return time === this.rowData.month;
            }
        },
        methods:{
            update(){
                // 参数根据业务场景随意构造
                let params = {type:'edit',rowData:this.rowData};
                this.$emit('on-custom-comp',params);
            },

            check(){
                // 参数根据业务场景随意构造
                let params = {type:'check',rowData: this.rowData};
                this.$emit('on-custom-comp',params);
            }
        }
    })

</script>