<template id="product_wechat_warning_list">
    <div>
        <div class="row">
            <prompt_modal ref="prompt_modal"></prompt_modal>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <label>各个产品各个场景需要触发的微信预警配置</label>
                    <a class="btn btn-primary pull-right btn-sm" href="/Home/ProductWechatWarning/create" target="_blank">新增配置</a>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.stop.prevent="listWechat">
                            <div class="form-group select2-style-adjust">
                                <v-select v-model="type" placeholder="选择场景" label="name" :options="list_type"></v-select>
                            </div>
                            <div class="form-group select2-style-adjust">
                                <v-select v-model="product" placeholder="请选择产品" label="product_name" :options="list_product"></v-select>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">查询&nbsp;<i class="icon-search"></i>
                                </button>
                            </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    :multiple-sort="tableConfig.multipleSort"
                    sort-always
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                              :page-size="pageSize"
                              :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>

    </div>
</template>

<script>
    Vue.component('product-wechat-warning-list-template', {
        template: '#product_wechat_warning_list',
        data: function () {
            return {
                type : '',
                product : {},
                list_product : [],
                list_type : [
                    {
                        name : '全部',
                        slug : ''
                    },
                    {
                        name : '日报',
                        slug : 'report'
                    }
                ],
                isLoading: false,
                pageIndex: 1,
                pageSize: 20,
                total: 0,
                tableRange: [],
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'product_name',
                            title: '产品名称',
                            width: 150,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'product_id',
                            title: '产品ID',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'type',
                            title: '类型',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'list_slug',
                            title: '当前场景的微信异常配置',
                            width: 400,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'operater',
                            title: '操作人',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                        },
                        {
                            field: 'updated_at',
                            title: '操作时间',
                            width: 200,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter :function (rowData) {
                                let date = new Date(1000*rowData.updated_at);
                                return date.toLocaleString();
                            }
                        },
                        {
                            field: 'what_ever',
                            title: '操作',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter : function(rowData,rowIndex,pagingIndex,field){
                                return '<a target="_blank" href="/Home/ProductWechatWarning/edit?product_id='+ rowData.product_id +'&type='+ rowData.type + '&product_name='+ rowData.product_name +'" class="btn btn-xs btn-info">编辑</a>';
                            }
                        }
                    ]
                }
            }
        },
        mounted: function () {
            // 初始化可选的产品列表
            this.initProductList();

            // 初始化表格
            this.listWechat();
        },
        methods: {
            // 初始化可选的产品列表
            initProductList : function(){
                let vm = this;
                axios.get('/Api/BackendProductWechatWarning/listProduct', {responseType:'json'}).then(function(repsonse){
                    if (repsonse.data.status === 0) {
                        vm.list_product = repsonse.data.list_product;
                    }

                });
            },

            // 自定义列触发事件
            customCompFunc: function (params) {

            },
            listWechat: function () {
                let params = {
                    type : !!this.type ? this.type.slug :'',
                    product_id : !!this.product ? this.product.product_id : ''
                };

                let vm = this;
                axios.post('/Api/BackendProductWechatWarning/listShow', params, {responseType: 'json'}).then(function (response) {
                    console.log(response);
                    if (response.data.status === 0) {
                        vm.isLoading = false;
                        let list_range = Object.values(response.data.list_show);
                        // 重置页面数据
                        vm.iniTableData(list_range);
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: response.data.errors.msg
                        });
                    }
                }).catch(function(response){
                    console.log('获取选中的微信配置遇到错误', response);

                });
            },
            // 重置页面数据
            iniTableData: function (list_range) {
                this.tableRange = list_range;
                this.total = list_range.length - 1;
                this.getTableData();
            },
            // 重置当前页展示的数据
            getTableData: function () {
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize)
            },
            // 换页重置数据
            pageChange: function (pageIndex) {

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange: function (pageSize) {

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });
</script>
