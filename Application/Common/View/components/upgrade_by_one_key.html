<template id="upgrade_by_one_key">
    <div>
        <prompt_modal_unique_template ref="prompt_modal" :unique_string="customer_id"></prompt_modal_unique_template>
        <button type="button" class="btn index-btn btn-warning btn-sm" @click="showModal">
            一键变更
        </button>

        <div class="modal fade" :id="modal_id" tabindex="-1" role="dialog" aria-labelledby="upgrade_key">
            <div class="modal-dialog" role="document" style="width: 95%;">
                <div class="modal-content" style="width: 100%;">
                    <div class="modal-header" style="width: 100%;">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><label>{{ title }}</label></h4>
                    </div>
                    <div class="modal-body" style="width: 90%;margin:0 auto;padding-bottom: 60px;">
                        <div style="font-size: 14px;color:red;width:100%;white-space: pre-wrap;line-height: 24px;padding: 0 0 16px;">*一键变更信息将会变更此客户下所有可用账号可用产品的状态，请仔细确认是否修改？</div>

                        <div class="row">
                            <ul class="list-inline select2-style-adjust">
                                <li><label class="show-align">账号状态：</label></li>
                                <li>
                                    <v-select v-model='status_account' :options="list_status_account"
                                              placeholder="账号状态"></v-select>
                                </li>
                            </ul>
                        </div>

                        <div class="row">
                            <ul class="list-inline select2-style-adjust">
                                <li><label class="show-align">产品签约状态:</label></li>
                                <li>
                                    <v-select v-model='status_product' :options="list_status_product"
                                              placeholder="产品状态"></v-select>
                                </li>
                            </ul>
                        </div>

                        <div class="row">
                            <ul class="list-inline">
                                <li><label>产品截至日期:</label></li>
                                <li><input type="date" v-model="end_time" class="form-control"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" data-dismiss="modal" @click="changeAtOnce">保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    Vue.component('upgrade_by_one_key_template', {
        props    : ['customer_id'],
        template : '#upgrade_by_one_key',
        data     : function () {
            return {
                //
                title               : '一键变更信息',
                list_status_product : [
                    {label : '已签约已付款', value : 1},
                    {label : '已签约未付费', value : 2},
                    {label : '未签约', value : 3},
                    {label : '特殊客户', value : 5},
                    {label : '其他', value : 4},
                ],
                list_status_account : [
                    {label : '可用', value : 1},
                    {label : '禁用', value : 0},
                ],
                status_product      : {},
                status_account      : {},
                end_time            : ''
            }
        },
        computed : {
            modal_id : function () {
                return 'upgrade_key_modal' + this.customer_id;
            }
        },
        methods  : {
            // 弹出模态框
            showModal : function () {
                $('#' + this.modal_id).modal('toggle');
            },

            //
            validateParams : function () {
                let status_product = !!this.status_product ? this.status_product.value : '',
                    status_account = !!this.status_account ? this.status_account.value : '',
                    end_time       = !!this.end_time ? this.end_time : '';
                return !status_account && status_account !== 0 && !status_product && !end_time;
            },

            setInitAfterRequest : function () {
                this.status_product = {};
                this.status_account = {};
                this.end_time       = ''
            },
            // 一键变更
            changeAtOnce        : function () {
                // 检查参数
                if (this.validateParams()) {
                    this.$refs.prompt_modal.open({
                        title : '提示',
                        body  : '您未变更任何信息，无法保存!'
                    });
                    return;
                }

                // 参数
                let params = {
                    status_product : !!this.status_product ? this.status_product.value : '',
                    status_account : !!this.status_account ? this.status_account.value : '',
                    end_time       : this.end_time,
                    customer_id    : this.customer_id
                }, vm      = this;

                axios.patch('{:U("onceButtonUpdate")}', params).then(function (response) {
                    console.log(response, '一键更新');
                    if (response.data.status === 0) {
                        vm.$refs.prompt_modal.open({
                            title : '提示',
                            body  : '更新成功'
                        });

                        // 之前老状态初始化
                        vm.setInitAfterRequest();
                    } else {
                        vm.$refs.prompt_modal.open({
                            title : '错误提示',
                            body  : response.data.errors?response.data.errors.msg:'无权限操作'
                        });
                    }
                });
            }
        }
    });

</script>
