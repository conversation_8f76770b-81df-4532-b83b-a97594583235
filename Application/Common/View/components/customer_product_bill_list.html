<template id="customer_product_bill_cmp">
    <div>
        <prompt_modal ref="prompt_modal_show"></prompt_modal>
        <div class="panel panel-default row">
            <div class="panel-heading">
                <label>客户产品对账单</label>
            </div>
            <div class="panel-body">
                <form class="form-inline">
                    <div class="form-group">
                        <label>开始月份</label>
                        <input type="month" class="form-control" v-model="month_begin">
                    </div>
                    <div class="form-group">
                        <label>结束月份</label>
                        <input type="month" class="form-control" v-model="month_end">
                    </div>

                    <div class="form-group select2-style-adjust">
                        <v-select :options="list_customers_label" label="name" v-model="customer_choose"
                                  placeholder="选择客户"></v-select>
                    </div>

                    <div class="form-group" style="float : right; ">
                        <button type="button" @click="search" class="btn btn-primary">查询</button>
                        <button type="button" @click="download" class="btn btn-success">下载</button>
                        <a href="/Home/FeeMonthCustomer/index" class="btn btn-warning">返回 </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    column-width-drag
                    :vertical-resize-offset='60'
                    :sort-always=sort_always
                    style="width:100%"
                    :multiple-sort="false"
                    :min-height="350"
                    even-bg-color="#f2f2f2"
                    :title-rows="tableConfig.titleRows"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @sort-change="sortChange"
                    :paging-index="(pageIndex-1)*pageSize"
            ></v-table>

            <div class="mt20 mb20 bold" style="margin-top:10px"></div>
            <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                          :page-size="pageSize"
                          :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
        </div>
    </div>
</template>
<script>
    Vue.component('custoemr_product_bill_template', {
        template: '#customer_product_bill_cmp',
        props   : ['month_t_begin', 'month_t_end', 'customer_id', 'list_customers', 'backend_customer_product_bill', 'backend_api_sorts', 'backend_customer_product_download'],
        data    : function () {
            return {
                month_begin    : this.month_t_begin,
                month_end      : this.month_t_end,
                customer_choose: {}, // 选中的客户
                product_key    : 'fbaace1340a8706863ed6ae17560355c', // product key鉴权使用
                list_accounts  : [], // 选中客户下辖的账号列表
                account_choose : {}, // 选中的账号

                sort_always: true,
                total      : 0, // 总 条数
                tableRange : [], // table 数据
                pageIndex  : 1,
                pageSize   : 20,
                tableConfig: {
                    multipleSort: false,
                    tableData   : [],
                    columns     : [
                        {
                            field             : 'product_name',
                            title             : '产品名称',
                            width             : 100,
                            columnAlign       : 'center',
                            isResize          : true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field             : 'account_name',
                            title             : '账号名称',
                            width             : 200,
                            columnAlign       : 'center',
                            isResize          : true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field             : 'section_number',
                            title             : '计费用量',
                            width             : 200,
                            columnAlign       : 'center',
                            isResize          : true,
                            titleCellClassName: 'title_column',
                            orderBy           : 'desc',
                            formatter         : (rowData, rowIndex, pagingIndex, field) => {
                                return this.formatNumber(rowData.section_number, 0);
                            }
                        },
                        {
                            field             : 'money',
                            title             : '费用',
                            width             : 200,
                            columnAlign       : 'center',
                            isResize          : true,
                            titleCellClassName: 'title_column',
                            orderBy           : 'desc',
                            formatter         : (rowData, rowIndex, pagingIndex, field) => {
                                return this.formatNumber(rowData.money);
                            }
                        },
                        {
                            field             : 'what_ever',
                            title             : '操作',
                            width             : 100,
                            columnAlign       : 'center',
                            isResize          : true,
                            titleCellClassName: 'title_column',
                            formatter         : (rowData, rowIndex, pagingIndex, field) => {
                                if (!rowData.operator) {
                                    return '';
                                }
                                return `
<a target="_blank" href="/Home/FeeMonthCustomer/monthBillList?customer_id=${this.customer_choose.customer_id}&product_id=${rowData.product_id}&product_name=${rowData.product_name}" class="btn btn-xs btn-info">月对账单</a>
<a target="_blank" href="/Home/FeeMonthCustomer/dayBillList?customer_id=${this.customer_choose.customer_id}&product_id=${rowData.product_id}&product_name=${rowData.product_name}" class="btn btn-xs btn-info">日对账单</a>
`;
                            }
                        }
                    ]
                }
            }
        },
        mounted : function () {
            // 初始化选中的客户
            this.initTheChooseCustomer();

            // 初始化列表
            this.search();
        },

        computed: {
            // 客户格式化
            list_customers_label(){
                return Object.values(JSON.parse(this.list_customers));
            },
            // 下载excel的文件名称
            fileName(){
                return `${this.customer_choose.name}(客户产品对账单) ${this.month_begin} -- ${this.month_end}.xlsx`;
            },
            // 是否展示账号的label
            displayAccountLabel(){
                return Object.values(this.account_choose).length > 0;
            }
        },
        methods : {
            // 初始化选中的客户
            initTheChooseCustomer(){
                this.customer_choose = this.list_customers_label.filter(item => item.customer_id === this.customer_id)[0];
            },
            // 下载
            download(){
                axios.post(this.backend_customer_product_download, {
                    list_bills: this.tableRange,
                    key       : this.product_key
                }, {responseType: 'blob'}).then(response => {
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', this.fileName); //or any other extension
                    document.body.appendChild(link);
                    link.click();
                });
            },
            // 查询参数
            searchParams(){
                // 如果没有选择客户 则不可以发送请求
                if (Object.values(this.customer_choose).length === 0) {
                    this.$refs.prompt_modal_show.open({
                        title: '提示',
                        body : '请选择客户'
                    });
                    return false;
                }

                return {
                    month_begin: this.month_begin,
                    month_end  : this.month_end,
                    key        : this.product_key,
                };

            },
            search: function () {
                // 参数
                let params = this.searchParams(),
                    api_url = `${this.backend_customer_product_bill}/${this.customer_choose.customer_id}`;
                if (params === false) {
                    return;
                }

                let vm = this;

                axios.get(api_url, {
                    params: params
                }).then(function (response) {
                    console.log(response, '查询事件');
                    if (response.data.status === 0) {
                        vm.tableRange = Object.values(response.data.lists);
                        console.log(vm.tableRange);
                        vm.getTableData();
                    } else {
                        vm.$refs.prompt_modal_show.open({
                            title: '提示',
                            body : '获取列表失败 msg:' + response.data.msg
                        });
                    }
                });
            },
            getTableData(){
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize);
                this.total = this.tableRange.length;
            },
            pageChange(pageIndex){

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            pageSizeChange(pageSize){

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
            sortChange(params){
                console.log(params, '排序');
                Object.keys(params).forEach(key => {
                        if (params[key] === 'asc' || params[key] === 'desc') {
                            let item = {
                                list_source    : this.tableRange,
                                field_sort_base: {
                                    field  : key,
                                    sort_by: params[key]
                                },
                                key            : this.product_key
                            };
                            axios.post(this.backend_api_sorts, item).then(response => {
                                console.log(response, '排序回应');
                                if (response.data.status === 0) {
                                    this.tableRange = Object.values(response.data.lists);
                                    this.getTableData();
                                } else {
                                    vm.$refs.prompt_modal_show.open({
                                        title: '提示',
                                        body : '排序失败 msg:' + response.data.msg
                                    });
                                }
                            });
                        }
                    }
                )
            },

            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (num === '--') {
                    return '--';
                }
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        },
        created(){
            this.getTableData();
        }
    });

</script>