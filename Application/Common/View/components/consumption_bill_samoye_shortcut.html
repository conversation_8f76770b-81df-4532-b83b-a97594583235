<template id="consumption_bill_samoye_shortcut">

        <tbody>
        <tr>
            <td></td>
            <th>服务项目</th>
            <td colspan="4">
                邦信分快捷版
            </td>
        </tr>
        <tr>
            <td></td>
            <th>上月结转余额</th>
            <td colspan="4">
                {{ date_item.money_before_this_month }}
            </td>
        </tr>
        <tr>
            <td></td>
            <td>期间</td>
            <td>充值金额</td>
            <td>单价</td>
            <td >有效查询字段量</td>
            <td >消耗金额</td>
        </tr>
        <tr>
            <td></td>
            <td>{{ date_item.details.month_begin }} -- {{ date_item.details.month_end }}</td>
            <td>{{ date_item.details.recharge }}</td>
            <td>{{ date_item.details.price }}</td>
            <td>{{ date_item.details.section_invoked_number }}</td>
            <td>{{ date_item.details.consume}}</td>
        </tr>
        <tr>
            <td></td>
            <td>合计</td>
            <td>{{ date_item.money_recharge_history }}</td>
            <td></td>
            <td ></td>
            <td >{{ date_item.money_consume_history}}</td>
        </tr>
        <tr>
            <td></td>
            <td>剩余金额</td>
            <td colspan="4">{{ date_item.money_last_now }}</td>
        </tr>
        </tbody>
</template>
<script>

    Vue.component('consumption_bill_samoye_shortcut_cmp', {
        template: '#consumption_bill_samoye_shortcut',
        props: ['date_item'],
    });
</script>