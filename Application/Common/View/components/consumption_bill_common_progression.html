<template id="consumption_bill_common_progression">
    <tbody>
    <tr>
        <td></td>
        <th>服务项目</th>
        <td colspan="10">
            {{ date_item.product_name }}
        </td>
    </tr>
    <tr>
        <td></td>
        <td>期间</td>
        <td>区间</td>
        <td>单价</td>
        <td v-if="parseInt(date_item.product_id)===210">有效查询字段量</td>
        <td v-if="parseInt(date_item.product_id)!==210">有效调用量</td>
        <td colspan="10">消耗金额</td>
    </tr>
    <template v-for="(distribute, key, index) in date_item.list_distribute">
        <tr v-if="showItem(date_item.list_distribute, distribute) ">
            <td></td>
            <td :rowspan="numberSection(date_item.list_distribute)"
                v-if="determineShowAsFirst(key, date_item.list_distribute)">{{ date_item.section_begin }} -- {{
                date_item.section_end }}
            </td>
            <td>{{ distribute.section_key }}</td>
            <td>{{ distribute.price }}</td>
            <td> {{ formatNumber(distribute.section_number, 0, 1) }}</td>
            <td colspan="10"> {{ formatNumber(distribute.money) }}</td>
        </tr>
    </template>
    </tbody>
</template>
<script>
    Vue.component('consumption_bill_common_progression_template', {
        template: '#consumption_bill_common_progression',
        props: ['date_item'],
        methods: {
            // 当前单元是否要展示的第一个
            determineShowAsFirst(section_key, list_distribute) {
                return section_key === this._firstSectionKey(list_distribute);
            },

            // 第一个展示的索引
            _firstSectionKey(list_distribute) {
                // 如果不存在section_number > 0的情况  则展示第一条
                let section_key = Object.keys(list_distribute).find((ele) => {
                    return list_distribute[ele]['section_number'] > 0;
                });

                return section_key ? section_key : Object.keys(list_distribute)[0];
            },

            // 是否展示当前的item
            showItem(list_distribute, distribute) {
                // 如果总条数和允许显示的条数是一致的 那么一定是显示的
                let length = this.numberSection(list_distribute);
                let counter = Object.keys(list_distribute).length;
                if (length === counter) {
                    return true;
                }

                return distribute.section_number > 0;
            },
            // 调用量跨过的区间
            numberSection(distribute) {
                // 如果长度是大于1, 那么 而且后来的调用量的是0 则只保留前面的长度
                let i = 0;
                Object.keys(distribute).forEach(item => {
                    if (distribute[item]['section_number'] > 0) {
                        i++;
                    }
                });

                // 至少展示一项
                return i ? i : 1;
            },

            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 2, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        }
    });
</script>