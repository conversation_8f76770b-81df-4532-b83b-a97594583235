<template type="text/x-template" id="correct_check" style="position: fixed; top:0">
    <div class="modal fade" id="correct_check_fade" tabindex="-1" role="dialog" style="z-index: 7777">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center" v-if="title">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" style="font-weight: bold; word-wrap: break-word;word-break: break-all;">
                        {{ title }}
                    </h4>
                </div>

                <div class="modal-body text-center">
                    <div class="form-horizontal">
                        <div class="form-group select2-style-adjust">
                            <label class="control-label col-sm-2">选择客户</label>
                            <div class="col-sm-8">
                                <input type="text" v-model.trim="customer_name" class="form-control" readonly>
                            </div>
                        </div>
                        <div class="form-group select2-style-adjust">
                            <label class="control-label col-sm-2">选择产品</label>
                            <div class="col-sm-8">
                                <input type="text" v-model.trim="product_name" class="form-control" readonly>
                            </div>
                        </div>

                        <div class="form-group select2-style-adjust" >
                            <label class="control-label col-sm-2">选择账号</label>
                            <div class="col-sm-8">
                                <input type="text" v-model.trim="account_name" class="form-control" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2">选择校正日期</label>
                            <div class="col-sm-8">
                                <input type="date" class="form-control" v-model.trim="standard_day" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">是否区分运营商</label>
                            <ul class="list-inline col-sm-3">
                                <li><input type="radio" value="common" v-model="type"  disabled>不区分</li>
                                <li><input type="radio" value="operator" v-model="type" disabled>区分</li>
                            </ul>
                        </div>

                        <template v-if="type === 'common'">
                            <div class="form-group">
                                <label class="control-label col-sm-2">实际用量</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" v-model.trim="source_number" readonly>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-sm-2">校正后用量</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" v-model.trim="updated_number" readonly>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="form-inline">
                                <div class="form-group" style="width: 50%; ">
                                    <label class="control-label col-sm-4">移动实际用量</label>
                                    <input type="text" class="form-control col-sm-6 " v-model.trim="yd_source_number" readonly>
                                </div>
                                <div class="form-group" style="width: 50%; ">
                                    <label class="control-label  col-sm-4">移动校正后用量</label>
                                    <input type="text" class="form-control col-sm-6" v-model.trim="yd_updated_number" readonly>
                                </div>
                            </div>
                            <div class="form-inline">
                                <div class="form-group" style="width: 50%; margin-top: 10px">
                                    <label class="control-label col-sm-4">联通实际用量</label>
                                    <input type="text" class="form-control col-sm-6 " v-model.trim="lt_source_number" readonly>
                                </div>
                                <div class="form-group" style="width: 50%; margin-top: 10px">
                                    <label class="control-label  col-sm-4">联通校正后用量</label>
                                    <input type="text" class="form-control col-sm-6" v-model.trim="lt_updated_number" readonly>
                                </div>
                            </div>

                            <div class="form-inline" style="margin: 10px 0">
                                <div class="form-group" style="width: 50%;">
                                    <label class="control-label col-sm-4">电信实际用量</label>
                                    <input type="text" class="form-control col-sm-6 " v-model.trim="dx_source_number" readonly>
                                </div>
                                <div class="form-group" style="width: 50%;">
                                    <label class="control-label  col-sm-4">电信校正后用量</label>
                                    <input type="text" class="form-control col-sm-6" v-model.trim="dx_updated_number" readonly>
                                </div>
                            </div>
                        </template>

                        <div class="form-group">
                            <label class="control-label col-sm-2">校正用量详情</label>
                            <div class="col-sm-8">
                                <textarea v-model.trim="details" id="details" cols="30" rows="3"
                                          class="form-control" readonly></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2">校正原因</label>
                            <div class="col-sm-8">
                                <textarea v-model.trim="reason" id="" cols="30" rows="3" readonly
                                          class="form-control"></textarea>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="control-label col-sm-2">备注信息列表</label>
                            <div class="col-sm-8">
                                <template v-for="comment in list_comments">
                                    <textarea cols="30" rows="3" readonly class="form-control col-sm-8" style="margin: 10px 0">{{ comment.comment }}
                                        -- {{ comment.operator ? comment.operator : ''}}
                                        -- {{ comment.created_at ? comment.created_at : '今天'}}
                                    </textarea>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Actions -->
                <div class="modal-footer">
                    <button type="button" :class="btn_class_left" data-dismiss="modal"> {{ btn_name_left }}</button>
                    <button type="button" :class="btn_class_right" data-dismiss="modal">{{ btn_name_right }}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    Vue.component('correct_check_template', {
        template: '#correct_check',
        props : ['back_api_correct_comments'],
        data: function () {
            return {
                modal_show: false,
                title: '校正账单用量 -- 查看',
                product_name: '', // 选中的产品
                customer_name: '', // 选中的客户
                account_name: '', // 选中的账号
                standard_day: '', //开始调整的日期
                type: 'common',  // 枚举值 common 普通 operator 运营商
                updated_number: '', //  common类型下更新后的调用量
                source_number: '', // common类型下的原始调用量
                yd_updated_number: '', // operator类型下更新后的移动调用量
                yd_source_number: '', //operator类型下的移动原始调用量
                lt_source_number: '', // operator类型下的联通原始调用量
                lt_updated_number: '', // operator类型下更新后的联通调用量
                dx_source_number: '', // operator类型下的电信原始调用量
                dx_updated_number: '', //operator类型下更新后的电信调用量
                details: '', // 操作详情
                reason: '', // 操作原因
                operator: '', // 操作人

                list_comments : [], // 备注列表
                uuid: '', // 唯一标识

                btn_name_left: '取消', // 左侧按钮的显示的文字,
                btn_name_right: '确定', // 左侧按钮的显示的文字,
                btn_class_left: 'btn btn-secondary', // 左侧按钮的类
                btn_class_right: 'btn btn-secondary btn-primary', // 右侧按钮的类
                btn_url_left: '', // 左侧按钮跳转的url
                btn_url_right: '', // 右侧按钮跳转的url
                backend_api: '', // create对应的api
                product_key: '5168b337cb7cdc2cd11675d634719ee9',  // 产品key
            }
        },
        methods: {
            initEnv(params){
                this.account_name = params.account_name;
                this.product_name = params.product_name;
                this.customer_name = params.customer_name;
                this.details = params.details;
                this.reason = params.reason;
                this.updated_number = params.data.updated_number ? params.data.updated_number : '';
                this.source_number = params.data.source_number ? params.data.source_number : '';
                this.type = params.type;
                this.yd_updated_number = params.data.yd_updated_number ?  params.data.yd_updated_number : '';
                this.lt_updated_number = params.data.lt_updated_number ?  params.data.lt_updated_number : '';
                this.dx_updated_number = params.data.dx_updated_number ?  params.data.dx_updated_number : '';
                this.yd_source_number = params.data.yd_source_number ?  params.data.yd_source_number : '';
                this.lt_source_number = params.data.lt_source_number ?  params.data.lt_source_number : '';
                this.dx_source_number = params.data.dx_source_number ?  params.data.dx_source_number : '';
                this.standard_day =params.standard_day.substr(0, 4) + '-' +  params.standard_day.substr(4, 2) + '-' + params.standard_day.substr(6, 2);
                this.uuid = params.uuid;

                // 获取备注列表
                this.initComment();
            },

            // 获取备注列表
            initComment() {
                axios.get(this.back_api_correct_comments + this.uuid, {params: {key: this.product_key}}).then(response => {
                    if (response.data.status === 0) {
                        this.list_comments = response.data.list;
                    } else {
                        this.$emit('exception', {body: response.data.msg, title: '错误提示(获取备注列表)'});
                    }
                });
            },

            // 初始化参数
            open: function (params) {
                // 初始化环境
                this.initEnv(params);

                $('#correct_check_fade').modal('toggle');
            },
        }
    });

</script>
