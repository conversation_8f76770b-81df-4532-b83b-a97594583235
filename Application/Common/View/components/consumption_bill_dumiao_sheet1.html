<template id="consumption_bill_dumiao_sheet1">
    <tbody>
    <tr>
        <td></td>
        <th>服务项目</th>
        <td colspan="10">
           邦信分快捷版
        </td>
    </tr>
    <tr>
        <td></td>
        <td>期间</td>
        <td>单次调用字段数</td>
        <td>单价</td>
        <td>有效查询字段量</td>
        <td>消耗金额</td>
    </tr>
    <tr v-for="(field_item, key) in date_item">
        <td v-if="key==0" :rowspan="date_item.length"></td>
        <td v-if="key==0" :rowspan="date_item.length">{{ field_item.section_begin }} -- {{ field_item.section_end }}</td>
        <td>{{field_item.name}}</td>
        <td>{{formatNumber(field_item.price,2)}}</td>
        <td>{{formatNumber(field_item.number)}}</td>
        <td>{{formatNumber(field_item.money, 2)}}</td>
    </tr>
    </tbody>
</template>
<script>

    Vue.component('consumption_bill_dumiao_sheet1_template', {
        template: '#consumption_bill_dumiao_sheet1',
        props: ['date_item'],
        methods: {
            // 是否是快捷版


            /**
             * 将数值四舍五入后格式化.
             *
             * @param num 数值(Number或者String)
             * @param cent 要保留的小数位(Number)
             * @param isThousand 是否需要千分位 0:不需要,1:需要(数值类型);
             * @return 格式的字符串,如'1,234,567.45'
             * @type String
             */
            formatNumber: function (num, cent = 0, isThousand = 1) {
                num = num.toString().replace(/\$|\,/g, '');

                // 检查传入数值为数值类型
                if (isNaN(num))
                    num = "0";

                // 获取符号(正/负数)
                sign = (num == (num = Math.abs(num)));

                num = Math.floor(num * Math.pow(10, cent) + 0.50000000001); // 把指定的小数位先转换成整数.多余的小数位四舍五入
                cents = num % Math.pow(10, cent);       // 求出小数位数值
                num = Math.floor(num / Math.pow(10, cent)).toString();  // 求出整数位数值
                cents = cents.toString();        // 把小数位转换成字符串,以便求小数位长度

                // 补足小数位到指定的位数
                while (cents.length < cent)
                    cents = "0" + cents;

                if (isThousand) {
                    // 对整数部分进行千分位格式化.
                    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                        num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3));
                }

                if (cent > 0)
                    return (((sign) ? '' : '-') + num + '.' + cents);
                else
                    return (((sign) ? '' : '-') + num);
            }
        },
    });
</script>