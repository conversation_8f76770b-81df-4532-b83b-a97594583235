<template id="stat_overview">
    <div style="width:100%;height:auto;">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body form-inline">
                    <div class="form-group">
                        <button type="button" class="btn btn-default" :class="{'btn-primary':whichPart('table')}" @click="setShowPart('table')">
                            产品有效调用量概况
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-default" :class="{'btn-primary':whichPart('chart')}" @click="setShowPart('chart')">
                            产品有效调用量趋势图
                        </button>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-default" :class="{'btn-primary':whichPart('pie')}" @click="setShowPart('pie')">
                            客户调用占比图
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="container" v-if="whichPart('table')">
            <stat-overview-number-template></stat-overview-number-template>
        </div>

        <div v-if="whichPart('chart')">
            <stat-overview-chart-template></stat-overview-chart-template>
        </div>

        <div v-if="whichPart('pie')">
            <stat-overview-pie-template></stat-overview-pie-template>
        </div>
    </div>
</template>

<script>
    Vue.component('stat-overview-template', {
        template: '#stat_overview',
        data: function () {
            return {show_type: 'table'}
        },
        methods : {
            setShowPart : function(show_type){
                this.show_type = show_type;
            },
            whichPart: function (show_type) {
                return this.show_type === show_type;
            }
        }
    });

</script>