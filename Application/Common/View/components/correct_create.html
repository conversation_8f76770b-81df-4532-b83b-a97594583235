<template type="text/x-template" id="correct_create" style="position: fixed; top:0">
    <div class="modal fade" id="correct_create_fade" tabindex="-1" role="dialog" style="z-index: 7777">

        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center" v-if="title">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" style="font-weight: bold; word-wrap: break-word;word-break: break-all;">
                        {{ title }}
                    </h4>
                </div>

                <div class="modal-body text-center">
                    <div class="form-horizontal">
                        <div class="form-group select2-style-adjust">
                            <label class="control-label col-sm-2">选择客户</label>
                            <v-select :options="list_customers" class="col-sm-8" label="name" v-model="customer"
                                      placeholder="选择客户" @input="customerChangedEvent"></v-select>
                        </div>
                        <div class="form-group select2-style-adjust" v-if="showProductList">
                            <label class="control-label col-sm-2">选择产品</label>
                            <v-select :options="list_products" class="col-sm-8" label="product_name"
                                      @input="productChangedEvent" v-model="product" placeholder="选择产品"></v-select>
                        </div>

                        <div class="form-group select2-style-adjust" v-if="showAccountList">
                            <label class="control-label col-sm-2">选择账号</label>
                            <v-select :options="list_accounts" class="col-sm-8" label="account_name" v-model="account"
                                      placeholder="选择账号"></v-select>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2">选择校正日期</label>
                            <div class="col-sm-8">
                                <input type="date" class="form-control" v-model.trim="standard_day">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">是否区分运营商</label>
                            <ul class="list-inline col-sm-3">
                                <li><input type="radio" value="common" v-model="type">不区分</li>
                                <li><input type="radio" value="operator" v-model="type">区分</li>
                            </ul>
                        </div>

                        <template v-if="type === 'common'">
                            <div class="form-group">
                                <label class="control-label col-sm-2">实际用量</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" v-model.trim="source_number">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-sm-2">校正后用量</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" v-model.trim="updated_number">
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="form-inline">
                                <div class="form-group" style="width: 50%; ">
                                    <label class="control-label col-sm-4">移动实际用量</label>
                                    <input type="text" class="form-control col-sm-6 " v-model.trim="yd_source_number">
                                </div>
                                <div class="form-group" style="width: 50%; ">
                                    <label class="control-label  col-sm-4">移动校正后用量</label>
                                    <input type="text" class="form-control col-sm-6" v-model.trim="yd_updated_number">
                                </div>
                            </div>
                            <div class="form-inline">
                                <div class="form-group" style="width: 50%; margin-top: 10px">
                                    <label class="control-label col-sm-4">联通实际用量</label>
                                    <input type="text" class="form-control col-sm-6 " v-model.trim="lt_source_number">
                                </div>
                                <div class="form-group" style="width: 50%; margin-top: 10px">
                                    <label class="control-label  col-sm-4">联通校正后用量</label>
                                    <input type="text" class="form-control col-sm-6" v-model.trim="lt_updated_number">
                                </div>
                            </div>

                            <div class="form-inline" style="margin: 10px 0">
                                <div class="form-group" style="width: 50%;">
                                    <label class="control-label col-sm-4">电信实际用量</label>
                                    <input type="text" class="form-control col-sm-6 " v-model.trim="dx_source_number">
                                </div>
                                <div class="form-group" style="width: 50%;">
                                    <label class="control-label  col-sm-4">电信校正后用量</label>
                                    <input type="text" class="form-control col-sm-6" v-model.trim="dx_updated_number">
                                </div>
                            </div>
                        </template>

                        <div class="form-group">
                            <label class="control-label col-sm-2">校正用量详情</label>
                            <div class="col-sm-8">
                                <textarea v-model.trim="details" id="details" cols="30" rows="5"
                                          class="form-control"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2">校正原因</label>
                            <div class="col-sm-8">
                                <textarea v-model.trim="reason" id="" cols="30" rows="5"
                                          class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Actions -->
                <div class="modal-footer">
                    <button type="button" :class="btn_class_left" data-dismiss="modal"> {{ btn_name_left }}</button>
                    <button type="button" :class="btn_class_right" @click.prevent="create">{{ btn_name_right }}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    Vue.component('correct_create_template', {
        template: '#correct_create',
        data: function () {
            return {
                modal_show: false,
                title: '选择要校正的账单',
                list_products: [], // 可供选择的产品列表
                list_customers: [], // 可供选择的客户列表
                list_accounts: [], // 可供选择的产品列表
                product: {}, // 选中的产品
                customer: {}, // 选中的客户
                account: {}, // 选中的账号
                standard_day: '', //开始调整的日期
                type: 'common',  // 枚举值 common 普通 operator 运营商
                updated_number: '', //  common类型下更新后的调用量
                source_number: '', // common类型下的原始调用量
                yd_updated_number: '', // operator类型下更新后的移动调用量
                yd_source_number: '', //operator类型下的移动原始调用量
                lt_source_number: '', // operator类型下的联通原始调用量
                lt_updated_number: '', // operator类型下更新后的联通调用量
                dx_source_number: '', // operator类型下的电信原始调用量
                dx_updated_number: '', //operator类型下更新后的电信调用量
                details: '', // 操作详情
                reason: '', // 操作原因
                operator: '', // 操作人

                btn_name_left: '取消', // 左侧按钮的显示的文字,
                btn_name_right: '确定', // 左侧按钮的显示的文字,
                btn_class_left: 'btn btn-secondary', // 左侧按钮的类
                btn_class_right: 'btn btn-secondary btn-primary', // 右侧按钮的类
                btn_url_left: '', // 左侧按钮跳转的url
                btn_url_right: '', // 右侧按钮跳转的url
                backend_api: '', // create对应的api
                product_key: '5168b337cb7cdc2cd11675d634719ee9',  // 产品key
            }
        },
        computed: {
            // 是否展示产品列表
            showProductList() {
                return this.customer && Object.values(this.customer).length > 0;
            },

            // 是否展示账号
            showAccountList() {
                return this.product && Object.values(this.product).length > 0;
            },
            // 是否选择账号
            selectedAccount() {
                return this.account && Object.values(this.account).length > 0;
            }
        },
        mounted() {
            // 初始化环境
            this._initEnv();

        },
        methods: {
            // 新建
            create() {
                // 参数
                let params = this._genParamsForCreate();
                if (params === false) {
                    return;
                }

                axios.post(this.backend_api, params).then(response => {
                    console.log(response, '新建校正账单用量');
                    if (response.data.status === 0) {
                        this.$emit('refresh');
                        this.closedNow();
                    } else {
                        this.$emit('exception', {
                            title: '提示',
                            body: response.data.msg
                        });
                    }
                });

            },
            //
            _genParamsForCreate() {
                // 是否选中了账号
                if (!this.selectedAccount) {
                    this.$emit('exception', {
                        title: '提示',
                        body: '请选择账号'
                    });
                    return false;
                }

                // 是否选中了产品
                if (!this.showAccountList) {
                    this.$emit('exception', {
                        title: '提示',
                        body: '请选择产品'
                    });
                    return false;
                }

                // 是否选择了客户
                if (!this.showProductList) {
                    this.$emit('exception', {
                        title: '提示',
                        body: '请选择客户'
                    });
                    return false;
                }

                return {
                    account_id: this.account.account_id,
                    customer_id: this.customer.customer_id,
                    product_id: this.product.product_id,
                    standard_day: this.standard_day, //开始调整的日期
                    type: this.type,  // 枚举值 common 普通 operator 运营商
                    updated_number: this.updated_number, //  common类型下更新后的调用量
                    source_number: this.source_number, // common类型下的原始调用量
                    yd_updated_number: this.yd_updated_number, // operator类型下更新后的移动调用量
                    yd_source_number: this.yd_source_number, //operator类型下的移动原始调用量
                    lt_source_number: this.lt_source_number, // operator类型下的联通原始调用量
                    lt_updated_number: this.lt_updated_number, // operator类型下更新后的联通调用量
                    dx_source_number: this.dx_source_number, // operator类型下的电信原始调用量
                    dx_updated_number: this.dx_updated_number, //operator类型下更新后的电信调用量
                    details: this.details, // 操作详情
                    reason: this.reason, // 操作原因
                    operator: this.operator, // 操作人
                    key: this.product_key, // 鉴权
                };

            },

            // 产品发生了变化
            productChangedEvent() {
                // 重置账号
                this.account = {};

                // 如果没有选中产品
                if (!this.showAccountList) {
                    return;
                }

                // 刷新账号列表
                this._refreshAccountList();
            },

            // 刷新账号列表
            _refreshAccountList() {
                let customer_id = this.customer.customer_id,
                    product_id = this.product.product_id;
                axios.get(`/Api/BackendCustomer/refreshAccountList`, {
                    params: {
                        customer_id,
                        product_id
                    }
                }).then(response => {
                    console.log('刷新账号列表', response);
                    if (response.data.status === 0) {
                        this.list_accounts = response.data.list;
                    } else {
                        console.log('刷新账号列表遇到了异常');
                    }

                });
            },

            // 客户发生变化事件
            customerChangedEvent() {
                // 清理掉选中的账号
                this.account = this.product = {};

                // 如果是没有选中客户
                if (!this.showProductList) {
                    return;
                }

                // 刷新产品列表
                this._refreshProductList();
            },

            // 刷新产品列表
            _refreshProductList() {
                let customer_id = this.customer.customer_id;
                axios.get(`/Api/BackendCustomer/refreshProductList?customer_id=${customer_id}`).then(response => {
                    console.log(response, '刷新产品列表');
                    if (response.data.status === 0) {
                        this.list_products = response.data.list;
                    } else {
                        console.log('刷新产品列表', '遇到异常');
                    }
                });
            },

            // 初始化环境
            _initEnv() {
                // 初始化客户列表
                this._initCustomers();
            },

            // 初始化客户列表
            _initCustomers() {
                axios.get('/Api/BackendCustomer/getBillCustomer').then(response => {
                    console.log('刷新客户列表', response);
                    if (response.data.status === 0) {
                        this.list_customers = response.data.list;
                    } else {
                        console.log('初始化客户列表遇到异常');
                    }
                });
            },

            // 初始化参数
            open: function (params) {
                this.operator = params.login_user;
                this.backend_api = params.back_api_correct_numbers_create;
                $('#correct_create_fade').modal('toggle');
            },

            // 关闭
            closedNow() {
                $('#correct_create_fade').modal('toggle');
                this.list_products = []; // 可供选择的产品列表
                this.list_accounts = []; // 可供选择的产品列表
                this.product = {}; // 选中的产品
                this.customer = {}; // 选中的客户
                this.account = {}; // 选中的账号
                this.standard_day = ''; //开始调整的日期
                this.type = 'common';  // 枚举值 common 普通 operator 运营商
                this.updated_number = ''; //  common类型下更新后的调用量
                this.source_number = ''; // common类型下的原始调用量
                this.yd_updated_number = ''; // operator类型下更新后的移动调用量
                this.yd_source_number = ''; //operator类型下的移动原始调用量
                this.lt_source_number = ''; // operator类型下的联通原始调用量
                this.lt_updated_number = ''; // operator类型下更新后的联通调用量
                this.dx_source_number = ''; // operator类型下的电信原始调用量
                this.dx_updated_number = ''; //operator类型下更新后的电信调用量
                this.details = ''; // 操作详情
                this.reason = ''; // 操作原因
            }
        }
    });

</script>
