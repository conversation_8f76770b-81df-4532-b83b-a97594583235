<template id="cuishou_private_detail">
    <div>
        <div class="row">
            <prompt_modal ref="prompt_modal"></prompt_modal>
            <div class="panel panel-default">
                <div class="panel-heading">
                    邦信分私有云数据统计
                    <!--{{ customer }}-->
                    <span v-if="show_selected">
                        (<span v-if="!!customer && customer.customer_id" style="font-weight: bold">客户: {{ customer.name }} &nbsp;</span><span
                            style="font-weight: bold" v-if="account && !!account.account_id">账号: {{ account.account_name }}</span>)
                    </span>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.stop.prevent="listStat">
                        <div class="form-group">
                            <label class="control-label">开始时间：</label>
                            <input type="date" class="form-control div_right_margin" v-model="time_begin">
                        </div>
                        <div class="form-group">
                            <label class="control-label">结束时间：</label>
                            <input type="date" class="form-control div_right_margin" v-model="time_end">
                        </div>
                        <div class="form-group div_right_margin select2-style-adjust">
                            <ul class="list-inline">
                                <li>
                                    <label class="control-label">选择客户：</label>
                                </li>
                                <li>
                                    <v-select :options="list_customer" label="name" :placeholder="placeholder_customer"
                                              :on-change="customerChange"      v-model="customer"></v-select>
                                </li>
                            </ul>
                        </div>

                        <div class="form-group div_right_margin select2-style-adjust">
                            <ul class="list-inline">
                                <li>
                                    <label class="control-label">选择账号：</label>
                                </li>
                                <li>
                                    <v-select :options="list_account" label="account_name" placeholder="选择账号"
                                              :on-change="accountChange"    v-model="account"></v-select>
                                </li>
                            </ul>
                        </div>

                        <div class="form-group pull-right">
                            <button type="submit" class="btn btn-primary">查询&nbsp;<i class="icon-search"></i></button>
                            <a href="/CuishouPrivateStat/index" class="btn btn-info">返回列表&nbsp; <i
                                    class="icon-reply"></i></a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    :multiple-sort="tableConfig.multipleSort"
                    @sort-change="sortChange"
                    sort-always
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total"
                              :page-size="pageSize"
                              :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>

    </div>
</template>

<script>
    Vue.component('cuishou-private-stat-detail', {
        template: '#cuishou_private_detail',
        props: ['ini_params'],
        data: function () {
            return {
                time_begin: '',
                time_end: '',
                list_customer: [], // 客户列表
                customer: {name: '全部', customer_id: ''}, // 客户
                placeholder_customer: '请选择客户',
                name: '',
                stat_level: '',
                customer_id: '',
                account_id: '',
                list_account: [],
                account: {account_name: '全部', account_id: ''},
                times_customer_changed : 0, // customer changed事件发生的次数, 用来判断是否清除选定的账号


                isLoading: false,
                pageIndex: 1,
                pageSize: 20,
                total: 0,
                tableRange: [],
                tableConfig: {
                    multipleSort: false,
                    tableData: [],
                    columns: [
                        {
                            field: 'date',
                            title: '日期',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'sum_counts',
                            title: '总查询量',
                            width: 100,
                            columnAlign: 'center',
                            orderBy: '',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'sum_valid_counts',
                            title: '有效查询量',
                            width: 100,
                            columnAlign: 'center',
                            orderBy: '',
                            isResize: true,
                            titleCellClassName: 'title_column'
                        },
                        {
                            field: 'min_status',
                            title: '是否有异常查询',
                            width: 100,
                            columnAlign: 'center',
                            isResize: true,
                            titleCellClassName: 'title_column',
                            formatter: function (rowData) {
                                return rowData.sum_counts === 0 ? '无调用' : rowData.min_status === 1 ? '<span style="color: green">正常</span>' : '<span style="color:red">异常</span>';
                            }
                        },
                    ]
                }
            }
        },
        computed: {
            // 是否显示选中的客户 && 账号
            show_selected: function () {
                // 如果没有选定账号 && 客户
                if (!this.account && !this.customer) {
                    return false;
                }

                // 两者选中了全部
                if (!!this.account && !!this.customer && this.account.account_id === '' && this.customer.customer_id === '') {
                    return false;
                }

                // 两者一个选择了全部， 一个没有选择
                if (!!this.account && !this.customer && this.account.account_id === '') {
                    return false;
                } else if (!!this.customer && !this.account && this.customer.customer_id === '') {
                    return false;
                }

                return true;
            }
        },
        mounted: function () {
            // 初始化参数
            this.initBaseParams();

            // 初始化客户列表
            this.initCustomerList();

            // 初始化账号列表
            this.initAccountList();
        },
        methods: {
            // 账号变动事件
            accountChange : function(account){
                this.account = !!account ? account :  {account_name: '全部', account_id: ''};
            },
            // 客户变动，联动账号事件
            customerChange : function(customer){
                this.customer = !!customer ? customer :  {name: '全部', customer_id: ''};

                // 这是一个基于当前应用场景的操作，进入此页面的都会进行两次的customer change event 所以从第三次开始每次变动都是要清除选中客户的
                this.times_customer_changed ++;

                if (this.times_customer_changed > 2) {
                    this.account = {account_name: '全部', account_id: ''};
                }
                console.log(customer, '第'+ this.times_customer_changed + '次触发了客户变动的事件');

                let vm = this;
                let params  = {
                    params : {customer_id : this.customer.customer_id},
                    responseType : 'json'
                };

                this.$http.get('/Api/CuishouPrivateStat/accountList', params).then(function (response) {
                    if (response.body.status === 0) {
                        vm.list_account = response.body.list_account;
                        vm.list_account.unshift({account_name: '全部', list_account: ''});
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: '网络故障, 请稍后再试'
                        });
                    }
                });

            },
            sortChange: function (params) {
                // 执行排序的url
                let url = '/Api/CuishouPrivateStat/sortBy';

                // 排序的字段 && 方式
                let params_url = {
                    filter_field: '',
                    filter_order: '',
                    list_data: this.tableRange
                };

                Object.keys(params).forEach(function (key) {
                    if (params[key] === 'asc' || params[key] === 'desc') {
                        params_url.filter_field = key;
                        params_url.filter_order = params[key];
                    }
                });

                // 请求排序
                let vm = this;
                this.$http.post(url, params_url, {responseType: 'json'}).then(function (response) {
                    if (response.body.status === 0) {
                        vm.iniTableData(response.body.list_stat);
                    } else {
                        console.log('排序出错了，宝贝: ' + response.body.msg);
                    }
                });
            },
            // 自定义列触发事件
            customCompFunc: function (params) {
                // 检查时间是否合法
                if (this.checkTime() === false) {
                    return false;
                }

                if (params.type === 'detail_show') {
                    let url_detail = '/Home/CuishouPrivateStat/detail?account_id=' + params.account_id + '&name=' + params.account_name
                        + '&stat_level=account';
                    window.open(url_detail);
                } else if (params.type === 'customer_show') {
                    let url_detail = '/Home/CuishouPrivateStat/detail?customer_id=' + params.customer_id + '&name=' + params.name
                        + '&stat_level=customer';
                    window.open(url_detail);
                }
            },
            // 初始化参数
            initBaseParams: function () {
                let ini_params = JSON.parse(this.ini_params);
                this.time_begin = ini_params.time_begin;
                this.time_end = ini_params.time_end;
                this.stat_level = ini_params.stat_level;
                this.name = ini_params.name;
                this.customer_id = !!ini_params.customer_id  ? ini_params.customer_id : '';
                this.account_id = !!ini_params.account_id  ? ini_params.account_id : '';
            },
            // 初始化客户列表
            initCustomerList: function () {
                let vm = this;
                this.$http.get('/Api/CuishouPrivateStat/customerList', {responseType: 'json'}).then(function (response) {
                    if (response.body.status === 0) {
                        vm.list_customer = response.body.list_customer;
                        vm.list_customer.unshift({name: '全部', customer_id: ''});
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: '网络故障, 请稍后再试'
                        });
                    }
                });

                // 如果是客户切入
                if (this.stat_level === 'customer') {
                    this.setCustomer();
                }
            },
            setCustomer : function(){
                let params = {
                    responseType: 'json',
                    params: {
                        customer_id: this.customer_id
                    }
                };
                let vm = this;
                this.$http.get('/Api/CuishouPrivateStat/customerShow', params).then(function (response) {
                    if (response.body.status === 0) {
                        vm.customer = response.body.customer;
                        // 初始化列表(只有此时完成了数据的初始化, 所以请求列表数据在此时调用)
                        vm.listStat();
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: '网络故障, 请稍后再试'
                        });
                    }
                });
            },
            setAccount : function(){
                let params = {
                    responseType: 'json',
                    params: {
                        account_id: this.account_id
                    }
                };
                let vm = this;
                this.$http.get('/Api/CuishouPrivateStat/accountShow', params).then(function (response) {
                    if (response.body.status === 0) {
                        vm.account = response.body.account;

                        // 请求对应的客户信息在调用列表数据的时候
                        vm.setCustomer();
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: '网络故障, 请稍后再试'
                        });
                    }
                });
            },
            // 初始化账号列表
            initAccountList: function () {
                let vm = this;
                let params  = {
                    params : {customer_id : this.customer_id},
                    responseType : 'json'
                };

                this.$http.get('/Api/CuishouPrivateStat/accountList', params).then(function (response) {
                    if (response.body.status === 0) {
                        vm.list_account = response.body.list_account;
                        vm.list_account.unshift({account_name: '全部', list_account: ''});
                    } else {
                        vm.$refs.prompt_modal.open({
                            title: '提示',
                            body: '网络故障, 请稍后再试'
                        });
                    }
                });

                // 如果是账号切入
                if (this.stat_level === 'account') {
                    vm.setAccount();
                }
            },
            checkTime: function () {
                if (!this.time_begin) {
                    this.$refs.prompt_modal.open({
                        title: '提示',
                        body: '请选择开始时间'
                    });
                    return false;
                }

                if (!this.time_end) {
                    this.$refs.prompt_modal.open({
                        title: '提示',
                        body: '请选择结束时间'
                    });
                    return false;
                }
                return true;
            },
            // 检查参数
            checkParams: function () {
                // 时间
                if (this.checkTime() === false) {
                    return false;
                }

                // 客户 && 账号是否存在
                return this.checkSelected();
            },
            checkSelected : function () {
                if (this.show_selected === false) {
                    this.$refs.prompt_modal.open({
                        title : '提示',
                        body: '请选择查询的客户或者账号'
                    });
                    return false;
                }
                return true;
            },
            listStat: function () {
                // 检查参数
                if (!this.checkParams()) {
                    return true;
                }

                let params = {
                    time_begin: this.time_begin,
                    time_end: this.time_end,
                    customer_id: !!this.customer ? this.customer.customer_id : '',
                    account_id: !!this.account ? this.account.account_id : '',
                };
                console.log(params);
                let vm = this;
                this.$http.post('/Api/CuishouPrivateStat/detailStat', params, {responseType: 'json'}).then(function (response) {
                    console.log(response);
                    if (response.body.status === 0) {
                        vm.isLoading = false;
                        let list_range = Object.values(response.body.list_stat);
                        // 重置页面数据
                        vm.iniTableData(list_range);
                    } else {
                        this.$refs.prompt_modal.open({
                            title: '提示',
                            body: response.body.msg
                        });
                    }
                })
            },
            // 重置页面数据
            iniTableData: function (list_range) {
                this.tableRange = list_range;
                this.total = list_range.length - 1;
                this.getTableData();
            },
            // 重置当前页展示的数据
            getTableData: function () {
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex - 1) * this.pageSize, (this.pageIndex) * this.pageSize)
            },
            // 换页重置数据
            pageChange: function (pageIndex) {

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange: function (pageSize) {

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            },
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });


</script>