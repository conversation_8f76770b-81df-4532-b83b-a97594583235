<script type="text/x-template" id="select2-template">
    <select class="form-control">
        <slot></slot>
    </select>
</script>

<script type="text/x-template" id="select-demo-template">
    <select2 :options="options" v-model="selected">
        <option  value="">{{ desc }}</option>
    </select2>
</script>

<script>
    Vue.component('select2', {
        props: ['options', 'value'],
        template: '#select2-template',
        mounted: function () {
            var vm = this
            $(this.$el)
            // init select2
                .select2({data: this.options})
                .val(this.value)
                .trigger('change')
                // emit event on change.
                .on('change', function () {
                    vm.$emit('input', this.value)
                })
        },
        watch: {
            value: function (value) {
                // update value
                $(this.$el)
                    .val(value)
                    .trigger('change')
            },
            options: function (options) {
                // update options
                $(this.$el).empty().select2({data: options})
            }
        },
        destroyed: function () {
            $(this.$el).off().select2('destroy')
        }
    });

    Vue.component('select_template', {
        template: '#select-demo-template',
        props: ['options', 'selected', 'description'],
        computed: {
            desc: function () {
                return this.description ? this.description : '全部';
            }
        },
        watch: {

            // selected 监听器如果发生变化的话  那么需要自定义触发事件，更改父组件的值
            selected: function (selected) {
                // update  value
                this.$emit('update:selected', selected)
            }
        }

        // 需要的属性值如下
        // data : function() {
        //     return  {
        //         selected: 2,
        //             options: [
        //             { id: 1, text: 'Hello'},
        //             { id: 2, text: 'World'}
        //         ]
        // }}
    });

</script>