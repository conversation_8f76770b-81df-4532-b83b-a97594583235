<?php

namespace Common\Controller;

/**
 * 控制器基类
 * 所有控制器(直接/间接)必须继承该类
 */
class BaseController extends \Think\Controller
{
    /*
     * 必须的配置
     * */
    protected $list_config = [
        'DHB_ITEMID', 'SITE_NAME', 'SITE_URL', 'LOGIN_SESSION_NAME', 'DB_NAME',
        'MONG<PERSON>', 'PWD_MONGO', 'MYSQL_CRSAPI', 'DUNNING_MONGO', 'RISK_LIST_MONGO',
        'CRS_API_CONFIG','MYSQL_CONFIG_ITAG_OLD', 'MEMCACHE_CONFIG_ITAG', 'REDIS_CRAWLER',
        'REDIS_CHANNEL_CONFIG', 'PREVIEW_URL', 'DB_FINANCE', 'DB_BANG',  'LIST_API_URL',
        'MAIL'
    ];

    public function _initialize()
    {
        //设置session有效期 防止页面不动登录超时
        session(array('expire' => 3600 * 24 * 5));
        //检查配置文件
        $this->checkConfig();
    }

    /**
     * 页面返回
     * @param  string $info 提示信息
     * @param  string $data 返回数据(跳转链接url 使用array('url'=>'') 跳转时间 使用array('times'=>))
     * @param  string $status 返回状态(stripos) error|success|tip_error|tip_success
     * @param  string $ajax 是否为ajax 默认为自动判断
     */
    public function __Return($info = '', $data = '', $status = 'tip_error', $ajax = 'auto')
    {
        if(I('post.approval_token')){
            $this->ajaxReturn(array('data' => $data, 'info' => $info, 'status' => $status), 'JSON', JSON_UNESCAPED_UNICODE);
        }
        $ajax = $ajax === 'auto' ? IS_AJAX : $ajax;
        if ($ajax === FALSE) {
            $jumpUrl = isset($data['url']) ? $data['url'] : '';
            $times = isset($data['times']) && is_numeric($data['times']) ? $data['times'] : FALSE;
            if (stripos($status, 'error') !== FALSE) {
                $this->error($info, $jumpUrl, $times);
            } else {
                $this->success($info, $jumpUrl, $times);
            }
        } else {
            $this->ajaxReturn(array('data' => $data, 'info' => $info, 'status' => $status), 'JSON', JSON_UNESCAPED_UNICODE);
        }
        exit;
    }

    /**
     * 检查配置文件
     */
    private function checkConfig()
    {
        array_walk($this->list_config,function($value){
            if (!C($value)) {
                throw new \Exception('检查配置文件中[' . $value . ']配置');
            }
        });
    }
}
