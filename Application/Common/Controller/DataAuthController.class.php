<?php

namespace Common\Controller;


use Account\Model\CustomerModel;
use Account\Model\CustomerSalesmanHistoryModel;
use Common\Model\SystemDeptGradeModel;
use Common\Model\SystemUserModel;
use PreSales\Model\PreSalesCustomerModel;

class DataAuthController
{
    //使用单例模式的目的是降低实例化类的时间，使得对象中的部分数据无须重复查询数据库
    protected static $obj = null;
    //允许查看的所有用户名
    protected $read_username = [];
    //允许查看的所有客户ID
    protected $read_customer_id = [];
    protected $dept_id          = '';        //当前用户的部门ID
    protected $data_auth        = [];      //当前用户的数据权限
    protected $username         = '';       //当前用户的账号名称
    protected $timeout          = 60;        //缓存时间

    protected $user_model;
    protected $dept_grade_model;
    protected $customer_model;

    protected $customerid_source_map = [];  // 允许查看的所有客户ID和来源的数组

    /**
     * 设置用户的名
     *
     * @access public
     *
     * @param $username string 用户账号名称
     *
     * @return void
     **/
    private function __construct()
    {
        $this->user_model       = new SystemUserModel();
        $this->dept_grade_model = new SystemDeptGradeModel();
        $this->customer_model   = new CustomerModel();

        $user_data      = $this->user_model->getCurrentUser();
        $this->username = $user_data['username'];
        $this->dept_id  = $user_data['dept_id'];
        // 0--全部权限, 1--个人权限, 2--本部门权限, 3--下级部门权限
        $this->data_auth = explode(',', $user_data['data_auth']);
    }

    /**
     * 单例模式实例化对象
     *
     * @access public
     *
     * @return $this
     **/
    public static function instance()
    {
        if (is_null(self::$obj)) {
            self::$obj = new self();
        }
        return self::$obj;
    }

    /**
     * 获取某部门的所有子级部门ID
     *
     * @access protected
     *
     * @param $dept_id string 部门ID
     *
     * @return array
     **/
    protected function getChildrenDeptId($dept_id)
    {
        $data = $this->dept_grade_model->where(
            [
                'grade_father_id|grade_grand_father_id' => $dept_id
            ]
        )->field('grade_dept_id')->select();
        return array_column($data, 'grade_dept_id');
    }

    /**
     * 获取某些部门下的用户账号名称
     *
     * @access protected
     *
     * @param $dept_id array|string 部门ID
     *
     * @return array
     **/
    protected function getUsernameByDeptId($dept_id)
    {
        if (is_array($dept_id)) {
            $dept_id = ['in', $dept_id];
            return $this->user_model->field('username')->where(compact('dept_id'))->select();
        } else {
            return $this->user_model->field('username')->where(compact('dept_id'))->select();
        }

    }

    /**
     * 如果用户不具备所有数据权限，则使用此方法获取该用户能够查看的那些用户的数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getReadUsername()
    {
        if (!empty($this->read_username)) {
            return $this->read_username;
        }
        $read_username = [];
        if (in_array(1, $this->data_auth)) {
            $read_username[] = $this->username;
        }
        if (!empty($this->dept_id)) {
            if (in_array(2, $this->data_auth)) {
                $currentDeptUsername = $this->getUsernameByDeptId($this->dept_id);
                $read_username       = array_merge($read_username, array_column($currentDeptUsername, 'username'));
            }
            if (in_array(3, $this->data_auth)) {
                //获取当前部门的下级部门
                $children_dept_id = $this->getChildrenDeptId($this->dept_id);
                if (!empty($children_dept_id)) {
                    $currentDeptUsername = $this->getUsernameByDeptId($children_dept_id);
                    $read_username       = array_merge($read_username, array_column($currentDeptUsername, 'username'));
                }
            }
        }
        if (empty($read_username)) {
            //如果登陆用户不存在数据权限（只存在于一种情况下：仅有下级部门的数据权限，但是登陆用户不存在下级部门|下级部门中不存在用户）
            $read_username = ['not find username'];
        }
        $this->read_username = $read_username;
        return $read_username;
    }

    /**
     * 获取用户当前可以查看的所有客户ID
     *
     * @access protected
     *
     * @return array
     **/
    protected function getReadCustomerId()
    {
        if (empty($this->read_customer_id)) {
            $is_delete   = 0;
            $customer_id = $this->customer_model->where($this->getCustomerWhere())->where(compact('is_delete'))->field(
                'customer_id'
            )->select();
            if (empty($customer_id)) {
                $this->read_customer_id = ['not find customer_id'];
            } else {
                $this->read_customer_id = array_column($customer_id, 'customer_id');
            }
        }
        return $this->read_customer_id;
    }

    /**
     * 获取某用户客户查看的所有客户ID
     *
     * @access public
     *
     * @param $username string 用户名
     *
     * @return array
     **/
    public function getReadCustomerIdsByUsername($username)
    {
        $this->username = $username;
        $user_data      = $this->user_model->getUserByUserName($username);
        $this->dept_id  = $user_data['dept_id'];
        // 0--全部权限, 1--个人权限, 2--本部门权限, 3--下级部门权限
        $this->data_auth = explode(',', $user_data['data_auth']);
        return $this->getReadCustomerId();
    }

    /**
     * 获取客户列表中的【客户】数据权限条件
     *
     * @access public
     *
     * @return array|string null代表所有数据权限
     **/
    public function getCustomerWhere()
    {
        if (empty($this->data_auth)) {
            return [];
        }
        if (in_array(0, $this->data_auth)) {
            return [];
        }
        $readUsername = '"' . implode('","', $this->getReadUsername()) . '"';
        return <<<WHERE
operator in ({$readUsername}) OR salesman in ({$readUsername}) OR channel_follower in ({$readUsername})
WHERE;
    }

    /**
     * 获取账号的客户来源权限
     *
     * @return array
     */
    public function getCustomerIdsSource(){

        if(empty($this->customerid_source_map)){
            $map = [];
            $ids = $this->getReadCustomerId();
            array_walk($ids,function($item)use(&$map){
                $map[$item] = [-1];
            });
            
            $readUsername = '"' . implode('","', $this->getReadUsername()) . '"';
            $where = "channel_follower in ({$readUsername})";
            $customerArr = $this->customer_model->where( $where )->where(compact('is_delete'))->field(
                'customer_id,source_id'
            )->select();
            //覆盖渠道跟进人对应的渠道权限
            if($customerArr){
                array_walk($customerArr,function($item)use(&$map){
                    $map[$item['customer_id']] = explode(',',$item['source_id']);
                });
            }
            $this->customerid_source_map = $map;
        }
        return $this->customerid_source_map;
    }

    /**
     *  校验客户来源权限
     *
     * @param [type] $customer_id
     * @param [type] $source
     * @return void
     */
    public function filterSourceAuth($customer_id,$source){
        $auth = $this->getCustomerIdsSource();
        if(!isset($auth[$customer_id])){
            return true;// 被过滤掉
        }
        if(in_array(-1,$auth[$customer_id])||in_array($source,$auth[$customer_id])){
			return false;// 有权限，不用过滤
		}else{
			return true;// 无权限，被过滤掉
		}
    }


    /**
     * 获取账号列表中的【账号】数据权限条件
     *
     * @access public
     *
     * @return array|string null代表所有数据权限
     **/
    public function getAccountWhere()
    {
        if (empty($this->data_auth)) {
            return [];
        }
        if (in_array(0, $this->data_auth)) {
            return [];
        }
        $readCustomerId = '"' . implode('","', $this->getReadCustomerId()) . '"';
        return <<<WHERE
    account.customer_id IN ({$readCustomerId})
WHERE;
    }

    /**
     * 获取通用模块中【用户】数据权限的条件
     *
     * @access public
     *
     * @return array|string
     **/
    public function getUserWhere()
    {
        if (empty($this->data_auth)) {
            return [];
        }
        if (in_array(0, $this->data_auth)) {
            return [];
        }
        $readUsername = '"' . implode('","', $this->getReadUsername()) . '"';
        return <<<WHERE
    username IN ({$readUsername})
WHERE;
    }

    /**
     * 获取【售前测试客户】的数据权限的条件
     *
     * @access public
     *
     * @return array|string
     **/
    public function getPreSalesCustomerWhere()
    {
        if (empty($this->data_auth)) {
            return [];
        }
        if (in_array(0, $this->data_auth)) {
            return [];
        }
        $readUsername = '"' . implode('","', $this->getReadUsername()) . '"';
        return <<<WHERE
    business_admin IN ({$readUsername})
WHERE;
    }

    /**
     * 获取【打款单】的数据权限的条件
     *
     * @access public
     *
     * @return array|string
     **/
    public function getRemiteWhere()
    {
        if (empty($this->data_auth)) {
            return [];
        }
        if (in_array(0, $this->data_auth)) {
            return [];
        }

        // $old_customer_ids = (new CustomerSalesmanHistoryModel())->getOldCustomerIds($this->username);
        // $read_customer_ids = array_merge($this->getReadCustomerId(),$old_customer_ids);
        $read_customer_ids = $this->getReadCustomerId();

        $readCustomerId = '"' . implode('","', $read_customer_ids) . '"';
        return <<<WHERE
    customer_id IN ({$readCustomerId})
WHERE;
    }

    /**
     * 获取【合同管理】的数据权限的条件
     *
     * @access public
     *
     * @return array|string
     **/
    public function getBargainWhere()
    {
        if (empty($this->data_auth)) {
            return [];
        }
        if (in_array(0, $this->data_auth)) {
            return [];
        }
        $readCustomerId = '"' . implode('","', $this->getReadCustomerId()) . '"';
        return <<<WHERE
    bargain.customer_id IN ({$readCustomerId})
WHERE;
    }

    /**
     * 校验用户是否存在的某个客户的权限
     *
     * @access public
     *
     * @param $customer_id string 客户ID
     *
     * @return void
     **/
    public function validAllowDoCustomer($customer_id)
    {
        if (empty($this->data_auth)) {
            return;
        }
        if (in_array(0, $this->data_auth)) {
            return;
        }
        if (empty($customer_id)) {
            throw new \Exception('客户ID不存在');
        }
        $is_delete = 0;
        $isExists  = $this->customer_model->where($this->getCustomerWhere())
            ->where(compact('customer_id', 'is_delete'))
            ->count();
        if (!$isExists) {
            throw new \Exception('该数据不存在或不具备该客户的数据权限');
        }
    }

    /**
     * 校验用户是否存在的某个售前测试客户的权限
     *
     * @access public
     *
     * @param $ps_customer_id string 售前测试客户ID
     *
     * @return void
     **/
    public function validAllowDoPreSalesCustomer($ps_customer_id)
    {
        if (empty($this->data_auth)) {
            return;
        }
        if (in_array(0, $this->data_auth)) {
            return;
        }
        if (empty($ps_customer_id)) {
            throw new \Exception('售前测试客户ID不存在');
        }
        $where       = $this->getPreSalesCustomerWhere();
        $delete_time = 0;
        $isExists    = (new PreSalesCustomerModel())->where($where)
            ->where(compact('ps_customer_id', 'delete_time'))
            ->count();
        if (!$isExists) {
            throw new \Exception('该数据不存在或不具备该客户的数据权限');
        }
    }

    /**
     * 查询拥有某客户数据的数据权限的用户（用户ID => [客户ID]）
     *
     * @access public
     *
     * @param $customer_id string|array 客户ID
     * @param $type        string 使用类型(daily_auth | balance_auth | surplus_auth | expire_auth)
     *
     * @return array
     **/
    public function mergeUserDataByCustomerIdDataAuth($customer_id, $type = '')
    {
        $customer_data = $this->getUserDataByCustomerIdDataAuth($customer_id, $type);
        //获取所有用户
        $user_data = [];
        array_walk(
            $customer_data,
            function ($customer_item, $customer_id) use (&$user_data) {
                array_walk(
                    $customer_item,
                    function ($user_item, $username) use (&$user_data, $customer_id) {
                        if (empty($user_data[$username])) {
                            $user_data[$username] = [
                                'username'     => $username,
                                'email'        => $user_item['email'],
                                'daily_auth'   => $user_item['daily_auth'],
                                'balance_auth' => $user_item['balance_auth'],
                                'surplus_auth' => $user_item['surplus_auth'],
                                'expire_auth'  => $user_item['expire_auth'],
                                'customer_id'  => []
                            ];
                        }
                        $user_data[$username]['customer_id'][] = $customer_id;
                    }
                );
            }
        );
        return $user_data;
    }

    /**
     * 查询拥有某客户数据的数据权限的用户 (客户ID => [用户ID])
     *
     * @access public
     *
     * @param $customer_id string|array 客户ID
     * @param $type        string 使用类型(daily_auth | balance_auth | surplus_auth | expire_auth)
     *
     * @return array
     **/
    public function getUserDataByCustomerIdDataAuth($customer_id, $type = '')
    {
        //查询客户数据
        $customer_id   = is_array($customer_id) ? ['in', $customer_id] : $customer_id;
        $customer_data = $this->customer_model->field('customer_id, operator, salesman')
            ->where(compact('customer_id'))
            ->select();
        //查询所有用户数据
        $disabled  = 1;
        $user_data = $this->user_model->where(compact('disabled'))->select();
        $user_data = array_column($user_data, null, 'username');
        //查询所有部门的等级关系并处理
        $grade_dept_data = [];
        array_walk(
            $this->dept_grade_model->select(),
            function ($grade_dept) use (&$grade_dept_data) {
                $this_dept_id = $grade_dept['grade_dept_id'];
                if (empty($grade_dept['grade_father_id'])) {
                    return true;
                }
                $father_dept_id = $this_dept_id . '_' . $grade_dept['grade_father_id'];
                $grand_dept_id  = $this_dept_id . '_' . $grade_dept['grade_grand_father_id'];
                if (!in_array($father_dept_id, $grade_dept_data)) {
                    $grade_dept_data[] = $father_dept_id;
                }
                if (!empty($grade_dept['grade_grand_father_id'])) {
                    $grade_dept_data[] = $grand_dept_id;
                }
                return true;
            }
        );
        //分为4个部分
        $result = [];
        array_walk(
            $customer_data,
            function ($customer) use (&$result, $user_data, $grade_dept_data, $type) {
                $result[$customer['customer_id']] = $this->getUserDataByPreCustomerIdDataAuth(
                    $customer,
                    $user_data,
                    $grade_dept_data,
                    $type
                );
            }
        );
        return $result;
    }

    /**
     * 查询某个客户所对应的拥有此客户权限的所有用户数据
     *
     * @access protected
     *
     * @param $customer_data   array 客户数据 [customer_id,operator,salesman]
     * @param $user_data       array 用户数据
     * @param $grade_dept_data array 部门分级数据
     * @param $type            string 使用方式
     *
     * @return array
     **/
    protected function getUserDataByPreCustomerIdDataAuth($customer_data, $user_data, $grade_dept_data, $type)
    {
        $result    = [];
        $user_data = array_walk(
            $user_data,
            function ($user) use (&$result, $type, $customer_data, $user_data, $grade_dept_data) {
                $data_auth   = explode(',', $user['data_auth']);
                $result_user = [
                    'username'     => $user['username'],
                    'email'        => $user['email'],
                    'daily_auth'   => $user['daily_auth'],
                    'balance_auth' => $user['balance_auth'],
                    'surplus_auth' => $user['surplus_auth'],
                    'expire_auth'  => $user['expire_auth']
                ];
                //拥有此权限的用户分为四类
                //拥有所有权限的用户
                $username = $user['username'];
                if (in_array(0, $data_auth)) {
                    if ($type && $user[$type]) {
                        $result[$username] = $result_user;
                    } elseif (!$type) {
                        $result[$username] = $result_user;
                    }
                    return true;
                }
                //本人的数据权限
                $operator = $customer_data['operator'];
                $salesman = $customer_data['salesman'];
                if (in_array(1, $data_auth)) {
                    if ($operator == $username || $salesman == $username) {
                        if ($type && $user[$type]) {
                            $result[$username] = $result_user;
                            return true;
                        } elseif (!$type) {
                            $result[$username] = $result_user;
                            return true;
                        }
                    }
                }
                //本部门的数据权限
                $dept_id          = $user['dept_id'];
                $operator_dept_id = $user_data[$operator]['dept_id'];
                $salesman_dept_id = $user_data[$salesman]['dept_id'];
                if (in_array(2, $data_auth)) {
                    if ($dept_id == $operator_dept_id || $dept_id == $salesman_dept_id) {
                        if ($type && $user[$type]) {
                            $result[$username] = $result_user;
                            return true;
                        } elseif (!$type) {
                            $result[$username] = $result_user;
                            return true;
                        }
                    }
                }
                //下级部门的数据权限
                if (in_array(3, $data_auth)) {
                    if (in_array($operator_dept_id . '_' . $dept_id, $grade_dept_data) || in_array(
                            $salesman_dept_id . '_' . $dept_id,
                            $grade_dept_data
                        )
                    ) {
                        if ($type && $user[$type]) {
                            $result[$username] = $result_user;
                            return true;
                        } elseif (!$type) {
                            $result[$username] = $result_user;
                            return true;
                        }
                    }
                }
                return true;
            }
        );
        return $result;
    }
}