<?php
namespace Common\Controller;
use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\PushModel;
use Account\Model\ProductModel;
/**
 * Created by PhpStorm.
 * User: yangge
 * Date: 2018/9/6
 * Time: 下午2:51
 */
class ChangeNoticeController extends \Common\Controller\BaseController {

    public $account_product_model;
    public $account_model;
    public $push;
    public $product;
    public $push_id;

    public function __construct()
    {
        parent::__construct();
        $this->account_product_model = new AccountProductModel();
        $this->account_model = new AccountModel();
        $this->product = new ProductModel();
        $this->push = new PushModel();
    }

    /*
     * @param $product_id 产品ID，$account_id 用户ID
     * 组装参数推送，写入数据库
     * 根据推送结果修改数据库
     *
     * */
    public function Notice($product_id,$account_id)
    {
        $account_product_info = $this->account_product_model->getAccountProductInfoByWhere(['account_id'=>$account_id,'product_id'=>$product_id], 'id as account_product_id,account_id, product_id, data, status, contract_status, end_time as p_end_time,daily_limit,month_limit,year_limit,total_limit,concurrency');
        $account_info = $this->account_model->getAccountInfoByWhere(['account_id'=>$account_id]);
        $result = [];
        $result['account_info'] = ['account_name'=>$account_info['account_name'],'apikey'=>$account_info['apikey'],'appsecret'=>$account_info['appsecret'],'end_time'=>$account_info['end_time'],'access_ip'=>unserialize($account_info['access_ip']),'status'=>$account_info['status']];
        $result['product_info'] = ['daily_limit'=>$account_product_info['daily_limit'],
            'month_limit'=>$account_product_info['month_limit'],'year_limit'=>$account_product_info['year_limit'],'total_limit'=>$account_product_info['total_limit'],'concurrency'=>$account_product_info['concurrency'],'end_time'=>$account_product_info['end_time'],'status'=>$account_product_info['status']];
        $result['product_tag'] = $product_id;
        $push = ['apikey'=>$account_info['apikey'],'product_id'=>$product_id,'input'=>json_encode($result),'push_number'=>1,'create_time'=>time()];
        $this->push_id = $this->push->createPush($push);
        $res = $this->curlPost($result,$product_id);
        if($res['status'] == '0'){
            $push_status = ['status'=>2,'output'=>json_encode($res),'update_time'=>time()];
        }elseif($res['status'] == '-1'){
            $push_status = ['status'=>1,'push_number'=>4,'output'=>'json_encode($res)','update_time'=>time()];
        }else{
            $push_status = ['status'=>1,'output'=>json_encode($res),'update_time'=>time()];
        }
        $this->push->updatePushInfo($this->push_id,$push_status);
        return true;
    }

    /*
     * @param $id  用户ID
     * 查询出用户名下所有产品，循环推送每个产品的信息
     *
     * */
    public function updateAccountForNotice($id)
    {
        $account_info = $this->account_model->getAccountInfoByWhere(['id'=>$id]);
        $account_product_info = $this->account_product_model->getProductListByAccountId($account_info['account_id']);
        foreach ($account_product_info as $key=>$val){
            $this->Notice($val['product_id'],$account_info['account_id']);
        }
    }

    /*
     * curl 发送数据
     * */
    public function curlPost($data,$product_id)
    {
        $product = $this->product->getProductInfo($product_id);
        if(empty($product['key']) || empty($product['push_url'])){
            return ['status'=>'-1','msg'=>'暂未配置推送地址'];
        }
        $data['key'] = $product['key'];
        $ch = curl_init($product['push_url']);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ));
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            return curl_error($ch);
        }
        curl_close($ch);
        return json_decode($result,true);
    }


}