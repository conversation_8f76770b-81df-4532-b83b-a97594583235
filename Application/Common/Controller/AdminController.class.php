<?php

namespace Common\Controller;

use Common\Common\Approval;
/**
 * 管理员父类
 * 需要权限校验的页面统一集成本类
 */
class AdminController extends \Common\Controller\BaseController
{
    //登录用户
    public $loginuser;
    //当前菜单
    public $currentmenu;
    //系统菜单
    public $systemmenu;
    //当前层级菜单
    public $currentmenus;

    /**
     * 初始化函数
     */
    public function _initialize()
    {
    	addBreakPoint('[admin]进入');
        parent::_initialize();
        addBreakPoint('[admin]初始化controller');
        try {
            //检查登录
            $this->checkLogin();
			addBreakPoint('[admin]检查登录');
            //获取用户角色
            $this->getUserRole();
			addBreakPoint('[admin]获取用户角色');
            //获取当前菜单(节点)
            $this->getCurrentMenu();
			addBreakPoint('[admin]获取当前菜单');
            //检查权限
            $this->checkPer();
			addBreakPoint('[admin]检查权限');
            //在非ajax调用的情况下获取系统菜单
            if (!IS_AJAX) {
                $this->getMenus();
            }
			addBreakPoint('[admin]系统菜单');
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }

        //增加访问日志
        $this->addAccessLog();
		addBreakPoint('[admin]增加访问日志');

        //校验操作是否需要审核
        (new Approval())->checkAddApprove($this->loginuser['username']);
        //全局模板变量赋值
        $this->assign('loginuser', $this->loginuser);
        $this->assign('currentmenu', $this->currentmenu);
        $this->SEO($this->currentmenu['name']);
    }

    /**
     * 增加访问日志
     */
    private function addAccessLog()
    {
        $uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : "";

        //去掉uri后面的参数
        $wenhaoPos = strpos( $uri, '?' );
        if( $wenhaoPos !== false ){
            $uri = substr($uri, 0, $wenhaoPos);
        }

        $request = I('param.');

        $accessLog['username'] = $this->loginuser['username'];
        $accessLog['uri'] = $uri;
        $accessLog['content'] = json_encode($request, JSON_UNESCAPED_UNICODE);
        $accessLog['created_at'] = time();
        $mod = D('SystemAccessLog');
        addBreakPoint('[admin]增加访问日志-SystemAccessLog');
        $mod->add($accessLog);
        addBreakPoint('[admin]增加访问日志-add完成');
    }

    /**
     * 页面 标题、关键字、 描述
     */
    final public function SEO($title = '', $keywords = '', $description = '')
    {
        $title = empty($title) ? C('SITE_NAME') : $title . ' - ' . C('SITE_NAME');
        $this->assign('SEO', ['title' => $title, 'keywords' => $keywords, 'description' => $description]);
    }

    /**
     * 获取系统菜单
     */
    private function getMenus()
    {
        $systemnodedb = D('SystemNode');
        addBreakPoint('[admin]系统菜单-SystemNode');
        //当前系统菜单(树形结构)
        $this->systemmenu = $systemnodedb->getSystemMenus();
        addBreakPoint('[admin]系统菜单-树形结构');
        //当前层级菜单
        $this->currentmenus = $systemnodedb->getMultiMenus($this->currentmenu['id']);
        addBreakPoint('[admin]系统菜单-当前层级菜单');

        $this->assign('systemmenu', $this->systemmenu);
        $this->assign('currentmenus', $this->currentmenus);
    }

    /**
     * 检查登录
     */
    private function checkLogin()
    {
        $systemuserdb = D('SystemUser');
        addBreakPoint('[admin]检查登录-SystemUser类');
        try {
            $this->loginuser = $systemuserdb->getCurrentUser();
            if (empty($this->loginuser)) {
                throw new \Exception('系统需要登录！');
            }
            if ($systemuserdb->checkDisabled($this->loginuser)) {
                throw new \Exception('登录账号已被禁用');
            }
        } catch (\Exception $e) {
            // exit;
            $systemuserdb->setUserLogout();
            ob_clean();

            header('Location: ' . U('System/Auth/login') . '?from=' . urlencode(U()));
            //$url = ;
            //halt($url);
            //$this->redirect('System/Auth/login', ['from' => urlencode(U())]);
            //$this->__Return($e->getMessage(), ['url' => $url]);
        }

    }

    /**
     * 获取当前菜单(节点)
     */
    private function getCurrentMenu()
    {
        $systemnodedb = D('SystemNode');
        addBreakPoint('[admin]获取当前菜单-SystemNodel类');
        //获取当前访问节点
        $this->currentmenu = $systemnodedb->getCurrentMenu();
        if (empty($this->currentmenu)) {
            throw new \Exception('当前访问节点不存在,请与开发人员联系');
        }
    }

    /**
     * 检查页面访问权限
     */
    private function checkPer()
    {
        //验证权限
        $mode = D('SystemNode');
        addBreakPoint('[admin]检查权限-SystemNode');
        if (!$mode->checkPerById($this->currentmenu['id'])) {
            throw new \Exception('没有访问该页面的权限');
        }
        return;
    }

    /**
     * 获取用户角色
     */
    private function getUserRole()
    {
        $mod = D('SystemUserRole');
        addBreakPoint('[admin]获取用户角色-SystemUserRole类');
        $role = $mod->getUserRole($this->loginuser['username']);
        if (empty($role)) {
            throw new \Exception('当前账号没有对应的用户角色');
        }
        $this->loginuser['role'] = $role;
        $this->loginuser['defaultroleid'] = D('SystemUser')->getDefaultRoleId($role);
        addBreakPoint('[admin]获取用户角色-getUserRole结束');
    }

    /**
     * json返回
     *
     * @param $response
     */
    public function ajaxResponse($response)
    {
        if (is_array($response)) {
            $response = json_encode($response, JSON_UNESCAPED_UNICODE);
        }
        header('Content-Type:application/json; charset=utf-8');
        exit($response);
    }
    
}