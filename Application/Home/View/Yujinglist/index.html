<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="start_time">报警日期：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>

                <div class="form-group">
                    <label class="control-label" for="start_time">报警级别：</label>
                    <select name="level" style="height: 33px;margin-top: 2px;border-radius:5px;">
                        <option value="0">全部</option>
                        <option value="1" <?php echo '1' == $input['level'] ? 'selected="selected"' : ''?>>极其严重</option>
                        <option value="2" <?php echo '2' == $input['level'] ? 'selected="selected"' : ''?>>严重</option>
                        <option value="3" <?php echo '3' == $input['level'] ? 'selected="selected"' : ''?>>关注</option>
                        <option value="4" <?php echo '4' == $input['level'] ? 'selected="selected"' : ''?>>提醒</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="control-label" for="start_time">是否已操作：</label>
                    <select name="haveo" style="height: 33px;margin-top: 2px;border-radius:5px;">
                        <option value="0" <?php echo '0' == $input['haveo'] ? 'selected="selected"' : ''?>>未操作</option>
                        <option value="1" <?php echo '1' == $input['haveo'] ? 'selected="selected"' : ''?>>已操作</option>
                    </select>
                </div>

                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </div>

            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <div class="panel-heading">
            <h5>
            <?php
            if($input['haveo'] == 0){
         ?>
            <input id="piliang_submit" type="button" class="btn btn-success btn-sm" value="批量处理">
            <?php } ?>
            勾选数量:<span id="fenpei_num">0</span>
            </h5>
        </div>

        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th><input type="checkbox" name="all" id="all" value=""/>全选</th>
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">报警来源</th>
                <th style="text-align:center;">报警负责人</th>
                <th style="text-align:center;">报警级别</th>
                <th style="text-align:center;">报警内容</th>
                <th style="text-align:center;">报警时间</th>
                <th style="text-align:center;">操作类型</th>
                <th style="text-align:center;">备注信息</th>
                <th style="text-align:center;">操作人</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td><input type="checkbox" name="id_arr" value="<?php echo ($vo['id']); ?>"/></td>
                    <td align="center">{$vo.id}</td>
                    <td align="center">
                        <switch name="vo.type">
                            <case value="1"><nobr>报警系统自身的报警</nobr></case>
                            <case value="2"><nobr>项目组推过来的报警</nobr></case>
                            <case value="3"><nobr>运维推过来的报警</nobr></case>
                        </switch>
                    </td>
                    <td align="center">{$vo.rname}</td>
                    <td align="center">
                        <switch name="vo.level">
                            <case value="1"><nobr>极其严重</nobr></case>
                            <case value="2"><nobr>严重</nobr></case>
                            <case value="3"><nobr>关注</nobr></case>
                            <case value="4"><nobr>提醒</nobr></case>
                        </switch>
                    </td>

                    <td align="center">{$vo.content}</td>
                    <td align="center">{$vo.ctime}</td>
                    <td align="center">
                        <switch name="vo.otype">
                            <case value="1"><nobr>暂停报警10分钟</nobr></case>
                            <case value="2"><nobr>暂停报警1小时</nobr></case>
                            <case value="3"><nobr>暂停报警半天</nobr></case>
                            <case value="4"><nobr>停报警1天</nobr></case>
                            <case value="6"><nobr>已处理</nobr></case>
                        </switch>
                    </td>
                    <td align="center">{$vo.note}</td>
                    <td align="center">{$vo.oname}</td>
                    <!--<td align="center">{$vo.ctime|date='Y-m-d',###}</td>-->
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">处理</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >邦秒验报警操作</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_form" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="alarm_id" id="alarm_id" value="" />
                    <div class="form-group" style="text-align: center">
                        <label for="note" style="vertical-align: top;">备注:</label>
                       <textarea rows="3" cols="40" name="note" id="note"></textarea>
                    </div>
                    <div class="form-group" style="text-align: center; ">
                        <button type="button" class="btn btn-lg" data-id="1">暂停报警10分钟</button>
                    </div>
                    <div class="form-group" style="text-align: center">
                        <button type="button" class="btn btn-lg" data-id="2">暂停报警1小时</button>
                    </div>
                    <div class="form-group" style="text-align: center">
                        <button type="button" class="btn btn-lg" data-id="3">暂停报警半天</button>
                    </div>
                    <div class="form-group" style="text-align: center">
                        <button type="button" class="btn btn-lg" data-id="4">暂停报警1天</button>
                    </div>
                    <div class="form-group" style="text-align: center">
                        <button type="button" class="btn btn-lg" data-id="6">已处理</button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
    <script type="text/javascript">

        var  num_global = 0;
        $('input[name="id_arr"]:checked').each(function(){
            num_global = num_global + 1;
        });
        $('#fenpei_num').html(num_global);

        $('#piliang_submit').on('click', function(){
            $('#piliang_submit').addClass('disabled');
            var num2 = $('#fenpei_num').html();
            if(num2 == 0){
                alert('请勾选需要的数据');
                $('#piliang_submit').removeClass('disabled');
                return false;
            }
            var arr = new Array();
            $('input[name="id_arr"]:checked').each(function(){
                arr.push($(this).val());
            });

            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Yujinglist/piliang",
                data:JSON.stringify({arr: arr}),
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#btn-dis').removeClass('disabled');
                        $('#identifier').modal('hide');
                        window.location.reload();
                    }else{
                        $('#btn-dis').removeClass('disabled');
                        alert(data.msg);
                    }
                }
            });

        });

    $('#all').on('click', function(){
        var isChecked = $(this).is(':checked');
        if(isChecked){
            //设置全选
            var i_num = 0;
            $('input[name="id_arr"]').each(function(){
                $(this).prop("checked", true);
                i_num = i_num + 1;
            });
            $('#fenpei_num').html(i_num);

        }else{
            //设置不全选
            $('input[name="id_arr"]').each(function(){
                $(this).removeAttr("checked", false);
            });
            $('#fenpei_num').html(0);

        }
    });

    $('input[name="id_arr"]').on('click', function(){
        var  num = 0;
        $('input[name="id_arr"]:checked').each(function(){
            num = num + 1;
        });
        $('#fenpei_num').html(num);
    });

    function edit(id){
        if(id == ''){
            return false;
        }
        $('#alarm_id').val(id);
        $("#file_in_modal").modal('show');
    }

    $(document).ready(function () {
        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        $('.btn-lg').on('click', function(){
            var note = $('#note').val();
            if(note == ''){
                //alert('备注不能为空');
                //return false;
                //note = '';
            }
            var otype = $(this).attr('data-id');
            var alram_id = $('#alarm_id').val();
            $.ajax({
                type: 'post',
                url: "{:U('/Home/Yujinglist/chuli')}",
                data: {
                    alram_id: alram_id,
                    otype: otype,
                    note: note
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }

                }
            });

        });

        //批量导入收款单
        $("#file_in_submit").click(function () {
            var excel_file = $('#excel_file').val();
            if (excel_file == '') {
                alert('请选择需要上传的Excel');
                return false;
            }
            //验证文件的大小
            let file = $("#excel_file")[0].files[0];
            let size = file.size;
            if (size>=500*1024) {
                alert('文件过大，请将文件分割后导入');
                return false;
            }
            //验证文件后缀
            let ext = file.name.split('.')[1];
            if (ext!='xls' && ext!='xlsx' && ext!='csv') {
                alert('文件格式不正确，请核对后导入');
                return false;
            }
            $('#file_in_modal').modal('hide');
            $("#loading").show();
            var uploadFileForm = new FormData($("#file_in_form")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Upload/add",
                data:uploadFileForm,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });

        });
    });

</script>
</body>
</html>
