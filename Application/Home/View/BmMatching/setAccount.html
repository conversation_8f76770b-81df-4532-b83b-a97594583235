    <div  id="bmmatching">
        <form class="form-horizontal" method="post" role="form" id="form_set_id">

            <div class="panel-body">

                <div class="form-group">
                    <label class="col-sm-2 control-label">账号名称</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" name="owner" value="{$account_info['owner']}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" for="set_choose_belongs_to_user">所属客户：</label>
                    <div class="col-sm-4" id="set_div_choose_belongs_to_user">
                        <select name="name" id="set_choose_belongs_to_user" class="form-control">
                            <option value="">暂不绑定</option>
                            <?php foreach($list_account_for_select2 as $account) {?>
                            <option value="<?= $account['name']; ?>" <?= ($account['id']==$account_product_info['account_id'])? 'selected="selected"' : ''?>><?= $account['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">账号状态</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="active">
                            <option value="1" <?=  (1 == $account_info['active']) ? 'selected="selected"' : ''?>>可用</option>
                            <option value="2" <?=  (2 == $account_info['active']) ? 'selected="selected"' : ''?>>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">签约状态</label>
                    <div class="col-sm-4">
                        <select name="contract_status" id="contract_status" class="form-control">
                            <option value="1" <?= ($account_info['contract_status'] == 1) ? 'selected' : '' ?>>已签约已付款</option>
                            <option value="2" <?= ($account_info['contract_status'] == 2) ? 'selected' : '' ?>>已签约未付费</option>
                            <option value="3" <?= ($account_info['contract_status'] == 3) ? 'selected' : '' ?>>未签约</option>
                            <option value="5" <?= ($account_info['contract_status'] == 5) ? 'selected' : '' ?>>特殊客户</option>
                            <option value="4" <?= ($account_info['contract_status'] == 4) ? 'selected' : '' ?>>其他</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">APIKEY</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="apikey" id="input_apikey" value="{$account_info['apikey']}" readonly>
                    </div>
                    <if condition="empty($account_info['apikey'])">
                        <div class="col-sm-2">
                            <button type="button" class="btn btn-default btn-block" onclick="edit_apikey('input_apikey',38)">生 成</button>
                        </div>
                    </if>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">PASSWORD</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" name="password" value="{$account_info['password']}" readonly>
                        </div>
                    </lable>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">截止日期</label>
                        <div class="col-sm-4">
                            <input type="date" class="form-control" id="validuntil" name="validuntil" value="{:date('Y-m-d',strtotime($account_info['validuntil']))}">
                        </div>
                </div>

                <?php foreach ($property_list as $key => $value): ?>
                <?php if(in_array($value['name'],['output_yscs','output_cs'])): ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="1" <?= ($account_property[$value['name']]['value'] == 1) ? 'checked' : '' ?>>是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="2"<?= ($account_property[$value['name']]['value']!=1) ? 'checked' : '' ?>>否
                        </label>
                    </div>
                </div>

                <?php elseif($value['name'] == 'encrypt_type'): ?>
                <div class="form-group">
                    <label  class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="1"
                            <?= (!isset($account_property[$value['name']]['value']) || $account_property[$value['name']]['value'] == 1) ? 'checked' : '' ?>>不加密
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="2"
                            <?= (isset($account_property[$value['name']]['value']) && $account_property[$value['name']]['value'] == 2) ? 'checked' : '' ?>>md5加密
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="3"
                            <?= (isset($account_property[$value['name']]['value']) && $account_property[$value['name']]['value'] == 3) ? 'checked' : '' ?>>SM35加密
                        </label>
                    </div>
                </div>

                <?php elseif($value['name'] == 'bind_domain' || $value['name'] == 'limit_access_ip'): ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-10">
                        <textarea type="text" class="form-control" name="{$value['name']}">{$account_property[$value['name']]['value']}</textarea>
                    </div>
                </div>
                <?php else: ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-10">
                        <if condition="$account_property[$value['name']]['value'] == '-1'">
                            <input type="text" class="form-control" name="{$value['name']}" value="" placeholder="无限制"/>
                            <else/>
                            <input type="text" class="form-control" name="{$value['name']}" value="{$account_property[$value['name']]['value']}"/>
                        </if>
                    </div>
                </div>
                <?php endif ?>
                <?php endforeach ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">接口输出字段</label>
                    <div class="col-sm-10 checkbox">
                        <?php foreach ($out_fields as $field) { ?>
                        <span>
                                <label class="checkbox-inline"><input type="checkbox" name="out_fields[]" value="<?= $field['id'] ?>" <?= isset($choosed_out_filed[$field['id']]) ? 'checked' : '' ?>><?= $field['remark'] ?></label>
                            </span>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <div class="alert alert-danger" role="alert">
                <p>注意：</p>
                <p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>
                <p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>
                <p>3.绑定域名和IP白名单多个时,换行填写</p>
                <p>4.限额无限制时置空</p>
            </div>

        </form>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};

        //避免搜索框不聚焦方法
        $("#set_choose_belongs_to_user").select2({
            dropdownParent: $("#set_div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#set_choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });

        // check validuntil
        $("#validuntil").change(
            function () {
                var time_choose = $(this).val();
                var today_str = (new Date()).toDateString();
                if ((Date.parse(time_choose + ' GMT +8') - Date.parse(today_str + ' GMT +8') < 0)) {
                    confirm('您选定的截至日期是无效的, 请问您确定要这样吗?');
                }
            }
        );
    });

    function creat_apikey(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/gen_apikey')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_apikey(id, length)
    {
        if(confirm('确定生成Apikey吗')) {
            creat_apikey(id, length);
        }
    }

</script>
