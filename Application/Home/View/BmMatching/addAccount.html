<form class="form-horizontal" role="form" id="form_set_id">
    <div class="form-group">
        <label class="col-sm-2 control-label">账号名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="owner" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" for="choose_belongs_to_user">所属客户：</label>
        <div class="col-sm-4" id="div_choose_belongs_to_user">
            <select name="name" id="choose_belongs_to_user" class="form-control">
                <option value="">暂不绑定</option>
                <?php foreach($list_account_for_select2 as $account) {?>
                <option value="<?= $account['name']; ?>"><?= $account['name']; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签约状态</label>
        <div class="col-sm-4">
            <select name="contract_status" id="contract_status" class="form-control">
                <option value="1">已签约已付款</option>
                <option value="2">已签约未付费</option>
                <option value="3" selected>未签约</option>
                <option value="5">特殊客户</option>
                <option value="4">其他</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">APIKEY</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="apikey" id="input_apikey" value="" readonly>
        </div>
        <div class="col-sm-2">
            <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_apikey',38)">生 成</button>
        </div>
    </div>


    <div class="form-group">
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="active">
                <option value="1" selected>可用</option>
                <option value="2">禁用</option>
            </select>
        </div>
        <label class="col-sm-2 control-label">截止日期</label>
        <div class="col-sm-4">
            <input type="date" class="form-control" name="validuntil" value=""/>
        </div>
    </div>

    <?php foreach ($field_list as $key => $value): ?>
    <?php if(in_array($value['name'],['output_yscs','output_cs'])): ?>
    <div class="form-group">
        <label class="col-sm-2 control-label">{$value['remark']}</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="{$value['name']}" value="1">是
            </label>
            <label class="radio-inline">
                <input type="radio" name="{$value['name']}" value="2" checked>否
            </label>
        </div>
    </div>
    <?php elseif($value['name'] == 'encrypt_type'): ?>
    <div class="form-group">
        <label  class="col-sm-2 control-label">{$value['remark']}</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="{$value['name']}" value="1" checked>不加密
            </label>
            <label class="radio-inline">
                <input type="radio" name="{$value['name']}" value="2">md5加密
            </label>
            <label class="radio-inline">
                <input type="radio" name="{$value['name']}" value="3">SM35
            </label>
        </div>
    </div>
    <?php elseif($value['name'] == 'bind_domain' || $value['name'] == 'limit_access_ip'): ?>
    <div class="form-group">
        <label class="col-sm-2 control-label">{$value['remark']}</label>
        <div class="col-sm-10">
            <textarea type="text" class="form-control" name="{$value['name']}"></textarea>
        </div>
    </div>
    <?php else: ?>
    <div class="form-group">
        <label class="col-sm-2 control-label">{$value['remark']}</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="{$value['name']}" value=""/>
        </div>
    </div>
    <?php endif ?>
    <?php endforeach ?>
    <div class="form-group">
        <label class="col-sm-2 control-label">接口输出字段</label>
        <div class="col-sm-10 checkbox">
            <?php foreach ($out_fields as $field) { ?>
            <span><label class="checkbox-inline"><input type="checkbox" name="out_fields[]" value="<?= $field['id'] ?>" <?= in_array($field['id'],['2','3','5','15','16','17','18']) ? checked : '' ?> ><?= $field['remark'] ?></label></span>
            <?php } ?>
        </div>
    </div>
</form>

<div class="alert alert-danger" role="alert">
    <p>注意：</p>
    <!--<p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>-->
    <!--<p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>-->
    <!--<p>3.推送地址由客户提供,信息(eg:通话详单)推送到该地址,无需求可置空</p>-->
    <!--<p>4.重定向地址由客户提供,接入h5时,授权成功后的跳转地址,无需求可置空</p>-->
    <p>1.绑定域名和IP白名单多个时,换行填写</p>
    <p>2.限额无限制时置空</p>
</div>
<script>
    $(function(){
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};

        creat_hash('input_apikey',38);

        //避免搜索框不聚焦方法
        $("#choose_belongs_to_user").select2({
            dropdownParent: $("#div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });
    });

    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/gen_apikey')}",
            type:'get',
            data:{"length":length},
            success:function(r){
            $("#"+id).val(r['data']);
        }
    });
    }

    function edit_hash(id, length)
    {
        if(confirm('重新生成Apikey？')) {
            creat_hash(id, length);
        }
    }
</script>

