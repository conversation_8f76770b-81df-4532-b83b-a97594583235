<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row-first { margin-bottom: 10px; }
        .row-first label, .row-second label { margin-left: 10px;}
    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
        <div id="breadcrumb_search_box">
            <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'addAccount\')}','添加邦秒配账号')" class="btn btn-primary btn-sm">添加邦秒配账号</a>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
        <form class="form-inline" action="{:U('')}" method="get" style="padding-bottom: 10px">
            <div class="row-first">
                <div class="form-group">
                    <label class="control-label">开通时间：</label>
                    <input type="date" name="begin" id="time_begin" class="form-control"
                           value="<?= (!isset($input['begin']) || !$input['begin']) ? '' : $input['begin']; ?>"/>
                    <input type="date" name="begin_e" id="time_begin_e" class="form-control"
                           value="<?= (!isset($input['begin_e']) || !$input['begin_e']) ? '' : $input['begin_e']; ?>"/>
                </div>
                <div class="form-group">
                    <label class="control-label">到期时间：</label>
                    <input type="date" name="end" id="time_end" class="form-control"
                           value="<?= (!isset($input['end']) || !$input['end']) ? '' : $input['end']; ?>"/>
                    <input type="date" name="end_e" id="time_end_e" class="form-control"
                           value="<?= (!isset($input['end_e']) || !$input['end_e']) ? '' : $input['end_e']; ?>"/>
                </div>
                <div class="form-group pull-right">
                    <ul class="list-inline">
                        <li>
                            <input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询">
                        </li>
                        <li>
                            <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="row-second">
                <div class="form-group">
                    <label class="control-label">账号状态</label>&nbsp;
                    <select class="form-control" name="active">
                        <option value="" selected>全部</option>
                        <option value="1" <?= ($input['active'] === '1') ? 'selected' : '' ;?>>可用</option>
                        <option value="2" <?= ($input['active'] === '2') ? 'selected' : '' ;?>>禁用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label">签约状态</label>&nbsp;
                    <select class="form-control" name="contract_status" id="contract_status">
                        <option value="" selected>全部</option>
                        <foreach name="contract_status" item="val" key="k">
                            <option value="{$k}" <?= ($input['contract_status'] == $k) ? 'selected' : '' ;?>>{$val}</option>
                        </foreach>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label">选择账号</label>&nbsp;
                    <select class="form-control" id="choose_user_sel" name="user_id">
                        <?php if (isset($input['owner'])) { ?>
                        <option value="<?= $input['user_id']; ?>" ><?= $input['owner']; ?></option>
                        <?php } ?>
                        <option value="" >全部</option>
                        <foreach name="user_list"  item="vo">
                            <option value="{$vo['id']}" >{$vo['owner']}</option>
                        </foreach>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="id">账号ID</label>&nbsp;
                    <input type="text" name="id" id="id" value="<?= isset($input['id']) ? $input['id'] : ''; ?>" class="form-control">
                </div>
                &nbsp;
                <div class="form-group">
                    <label class="control-label" for="apikey">APIKEY</label>&nbsp;
                    <input type="text" name="apikey" id="apikey" value="<?= isset($input['apikey']) ? $input['apikey'] : ''; ?>" class="form-control">
                </div>
            </div>
        </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">账号列表</h3></div>
        <table class="table table-hover table-bordered" style="table-layout:fixed">
            <thead>
            <tr>
                <th >账号ID</th>
                <th >账号名称</th>
                <th >所属客户</th>
                <th >状态</th>
                <th>签约状态</th>
                <th>APIKEY</th>
                <th>签名字符串</th>
                <th>接口调用示例</th>
                <th >Time</th>
                <th >操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($account_list as $account): ?>
            <tr>
                <td>{$account['id']}</td>
                <td style="word-wrap: break-word">{$account['owner']}</td>
                <td>{$account['account_product']['account_name']}</td>
                <td><?= ($account['active'] == 1) ? '可用' : '禁用' ;?></td>
                <td>
                    <?php
                        if(!empty($contract_status[$account['contract_status']])){
                            echo $contract_status[$account['contract_status']];
                        }
                    ?>
                </td>
                <td style="word-wrap:break-word">{$account['apikey']}</td>
                <td style="word-wrap:break-word">{$account['sig_str']}</td>
                <td style="word-wrap:break-word">{$account['api_example']}</td>
                <td>
                    <div>start : {$account['created_at'] ? date('Y-m-d H:i:s', $account['created_at']) : ''}</div>
                    <div>&nbsp;end :  {$account['validuntil']}</div>
                </td>
                <td>
                    <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'setAccount\',array(\'id\'=>$account[\'id\']))}','编辑邦秒配账号')" class="btn btn-primary btn-xs">编辑</a>
                    <a href="{:U('Home/BmMatching/configFee', ['id'=>$account['id']])}" class="btn btn-primary btn-xs">计费配置</a>
                </td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>

    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>
<script type="text/javascript">
    $(function () {
        $("#choose_user_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号',
            width : '200px'
        });

        // export file
        $("#file_export").click(function () {
            if (!checkTime()) {
                return false;
            }

            var params = genParamsForFile();
            var url_export = '/Home/BmMatchingFile/index' + params;
            console.log(url_export);
            $.fileDownload(url_export);
            return false;
        });
    });

    // 为导出文件生成参数
    function genParamsForFile() {
        var params = '';
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var status = $('#status').val();
        var contract_status = $('#contract_status').val();
        var choose_user_sel = $('#choose_user_sel').val();
        var id = $('#id').val();
        var apikey = $('#apikey').val();
        var time_begin_e = $('#time_begin_e').val();
        var time_end_e = $('#time_end_e').val();

        if (time_begin) {
            params += '&begin=' + time_begin;
        }
        if (time_end) {
            params += '&end=' + time_end;
        }
        if (time_begin_e) {
            params += '&begin_e=' + time_begin_e;
        }
        if (time_end_e) {
            params += '&end_e=' + time_end_e;
        }
        if (choose_user_sel) {
            params += '&user_id=' + choose_user_sel;
        }
        if (contract_status) {
            params += '&contract_status=' + contract_status;
        }
        if (status) {
            params += '&status=' + status;
        }
        if (id) {
            params += '&id=' + id;
        }
        if (apikey) {
            params += '&apikey=' + apikey;
        }

        // tidy url
        if (params) {
            params = params.replace('&', '?');
        }
        return params;
    }

    function checkTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_begin_e = $('#time_begin_e').val();
        var time_end = $('#time_end').val();
        var time_end_e = $('#time_end_e').val();
        // change time format for firefox
        time_end = time_end.replace(/\-/g, '\/');
        time_begin = time_begin.replace(/\-/g, '\/');
        time_end_e = time_end_e.replace(/\-/g, '\/');
        time_begin_e = time_begin_e.replace(/\-/g, '\/');

        // set default time
        if ((!time_begin && time_begin_e) || (time_begin && !time_begin_e)) {
            alert('开通时间不能为空');
            return false;
        }
        if ((!time_end && time_end_e) || (time_end && !time_end_e)) {
            alert('到期时间不能为空');
            return false;
        }

        //验证开通时间
        if (time_begin && time_begin_e) {
            // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
            var time_diff = new Date(Date.parse(time_begin_e)) - new Date(Date.parse(time_begin));
            // calculate the days between begin and end
            var day_diff = Math.floor(time_diff / 8.64e7);
            if (day_diff >= 365){
                alert('单次查询开通时间范围不能超过365天');
                return false;
            }else if(day_diff < 0){
                alert('开通时间范围不正确');
                return false;
            }
        }
        //验证到期时间
        if (time_end && time_end_e) {
            // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
            var time_diff = new Date(Date.parse(time_end_e)) - new Date(Date.parse(time_end));
            // calculate the days between begin and end
            var day_diff = Math.floor(time_diff / 8.64e7);
            if (day_diff >= 365){
                alert('单次查询到期时间范围不能超过365天');
                return false;
            }else if(day_diff < 0){
                alert('到期时间范围不正确');
                return false;
            }
        }
        return true;
    }
</script>
</body>
</html>
