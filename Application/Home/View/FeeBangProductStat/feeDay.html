<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row-first { margin-bottom: 10px; }
        .row-first label, .row-second label { margin-left: 10px;}
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" id="form_coverage">
                <div class="row-first">
                    <input type="hidden" id="product_id" name="product_id" value="<?= $input['product_id'] ?>">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin_date" id="time_begin" class="form-control"
                               value="<?= (!isset($input['begin_date']) || !$input['begin_date']) ? date('Y-m-d', strtotime('-1 days')) : $input['begin_date']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end_date" id="time_end" class="form-control"
                               value="<?= (!isset($input['end_date']) || !$input['end_date']) ? date('Y-m-d', strtotime('-1 days')) : $input['end_date']; ?>"/>
                    </div>

                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" class="btn btn-primary btn-sm" value="查询"></li>
                            <li>
                                <a class="btn btn-info btn-sm" id="you_know" href="{:U('/Home/FeeAccountStat/productList',['id'=>$input['account_id'],'product_id'=>$input['product_id'],'begin'=>$input['begin_date'],'end'=>$input['end_date']])}" role="button">返回</a>
                            </li>
                            <li>
                                <a href="javascript:;" onclick="download_file()" class="btn btn-success">导出</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">日对账单列表</h3></div>
        <table class="table table-hover table-bordered" style="table-layout: fixed">
            <thead>
            <tr>
                <th>日期</th>
                <th>计费用量</th>
                <th>费用（元）</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>总计</td>
                <td><?= isset($total_data['fee_amount']) ? $total_data['fee_amount'] : 0;?></td>
                <td><?= isset($total_data['fee_price']) ? $total_data['fee_price'] : "0.00";?></td>
            </tr>
            <?php foreach ($list as $key => $value): ?>
            <tr>
                <td>{$key}</td>
                <td><?= isset($value['fee_amount']) ? $value['fee_amount'] : 0 ?></td>
                <td><?= isset($value['fee_price']) ? sprintf('%.2f', $value['fee_price']) : "0.00" ?></td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>
</div>
<script type="text/javascript">
    //导出
    function download_file()
    {
        var product_id = $('input[name="product_id"]').val();
        var begin_date = $('input[name="begin_date"]').val();
        var end_date = $('input[name="end_date"]').val();
        var param = ''
        if (product_id) {
            param += '&product_id='+product_id;
        }
        if (begin_date) {
            param += '&begin_date='+begin_date;
        }
        if (end_date) {
            param += '&end_date='+end_date;
        }
        if (param) {
            param = param.replace('&', '?');
        }
        var url_export = '/Home/FeeBangProductStat/download' + param;
        $.fileDownload(url_export);
        return false;
    }
</script>
</body>
</html>