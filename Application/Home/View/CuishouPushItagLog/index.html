<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="panel-heading">
    <div class="row">
        <div class="container">
            <form class="form-inline" action="{:U('')}" method="get" id="form_log">
                <div class="form-group">
                    <label  class="control-label">开始时间：</label>
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>&nbsp;
                    <label  class="control-label">结束时间：</label>&nbsp;
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>&nbsp;
                </div>
                <br>
                <div class="form-group" style="margin:10px 0 0">
                    <label class="control-label">推送状态：</label>
                    <select class="form-control" name="itag_push_status">
                        <option value="">全部状态</option>
                        <option value="推送成功"<?= ($input['itag_push_status'] === '推送成功') ? 'selected' : '' ;?>>推送成功</option>
                        <option value="推送失败"<?= ($input['itag_push_status'] === '推送失败') ? 'selected' : '' ;?>>推送失败</option>
                        <option value="未推送"<?= ($input['itag_push_status'] === '未推送') ? 'selected' : '' ;?>>未推送</option>
                    </select>&nbsp;
                    <label class="control-label">Sid：</label>
                    <input type="text" class="form-control input-sm" name="sid" value="<?= isset($input['sid']) ? $input['sid'] : '' ;?>" placeholder="搜索Sid">&nbsp;
                    <label class="control-label">Cid：</label>
                    <input type="text" class="form-control input-sm" name="cid" value="<?= isset($input['cid']) ? $input['cid'] : '' ;?>" placeholder="搜索Cid">&nbsp;
                    <label class="control-label">推送次数：</label>
                    <input type="text" class="form-control input-sm" name="itag_push_number" value="<?= isset($input['itag_push_number']) ? $input['itag_push_number'] : '' ;?>" placeholder="搜索推送次数">&nbsp;
                    <label class="control-label">推送延迟时间：</label>
                    <input type="text" class="form-control input-sm" name="itag_push_delay_seconds" value="<?= isset($input['itag_push_delay_seconds']) ? $input['itag_push_delay_seconds'] : '' ;?>" placeholder="推送延迟">&nbsp;
                    <div class="form-group">
                        <button type="submit" class="btn btn-sm btn-primary">查询</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">推送列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>SID</th>
                <th>CID</th>
                <th>是否需要推送</th>
                <th>推送状态</th>
                <th>推送次数</th>
                <th>推送耗时(秒)</th>
                <th>推送延迟(秒)</th>
                <th>推送开始/结束时间</th>
                <th>客户端响应的字符串</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list_show as $log): ?>
            <tr>
                <td><a href="/Home/CuishouPushItagLog/msg?sid={$log['sid']}"><?= $log['sid']; ?></a></td>
                <td><?= $log['cid']; ?></td>
                <td><?= $log['itag_pushed_need']; ?></td>
                <td><?= $log['itag_push_status']; ?></td>
                <td><?= $log['itag_push_number']; ?></td>
                <td><?= $log['itag_push_take_seconds']; ?></td>
                <td><?= $log['itag_push_delay_seconds']; ?></td>
                <td>
                    start_time :<?= $log['itag_push_starttime']; ?> <br>
                    end_time &nbsp;:<?= $log['itag_push_end_time']; ?>
                </td>
                <td> <?= $log['itag_push_client_response_str']; ?></td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            <?= isset($page) ? $page : ''?>
        </ul>
    </nav>
</div>
<script type="text/javascript">
    $(function () {
        $("#province_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '爬虫渠道'
        });

        // check end time
        $("#form_log").submit(function () {
            // get begin time and end time
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var today_str = (new Date()).toDateString();

            // change time format for firefox
            time_end = time_end.replace(/\-/g, '\/');
            time_begin = time_begin.replace(/\-/g, '\/');

            // check begin time
            if (!time_begin && time_end) {
                alert('请选择开始时间');
                return false;
            }

            // check end time
            if (time_begin && !time_end) {
                alert('请选择结束时间');
                return false;
            }

            if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
                alert('请选择有效的结束时间');
                return false;
            }

            // set default time
            if (!time_begin) {
                time_begin = today_str;
            }

            if (!time_end) {
                time_end = today_str;
            }

            // check time
            var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
            if (time_diff < 0) {
                alert('开始时间必须小于结束时间');
                return false;
            }

            // calculate the days between begin and end
            var day_diff =  Math.floor(time_diff/8.64e7);

            //  the time should less than 31
            if (day_diff <= 30) {
                return true;
            } else {
                alert('单次查询时间范围不能超过31天');
                return false;
            }
            return true;
        });
    });
</script>
</body>