<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css" />
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">

                <div class="form-group">
                    <label  class="control-label">开始时间</label>&nbsp;
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group">
                    <label  class="control-label">结束时间</label>&nbsp;
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group">
                    <select class="form-control" id="choose_user_sel" name="id">
                        <option value="{$input['id']}">{$input['developer']}</option>
                    </select>
                </div>
                &nbsp;
                <div class="form-group">
                    <a class="btn btn-info" href="{:U('index')}" role="button">返回账号列表</a>
                </div>
                &nbsp;
                <div class="form-group">
                        <input type="submit" onclick="return checkTime();" class="btn btn-primary  btn-block" value="查询">
                </div>
                &nbsp;
                <div class="form-group">
                    <button type="button" id="file_export" class="btn btn-success  btn-block">导出 </button>
                </div>
            </form>

        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th>时间</th>
                <th>总查询量</th>
                <th>有效查询量</th>
                <th>总查得量</th>
                <th>本人查得量</th>
                <th>联系人查得量</th>
                <?php foreach($hit_type as $key => $hit) {?>
                <th><?=$hit?></th>
                <?php }?>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>总计</td>
                <td><?= isset($total_data['all_counts']) ? $total_data['all_counts'] : 0; ?></td>
                <td><?= isset($total_data['success_counts']) ? $total_data['success_counts']:0 ;?></td>
                <td><?= isset($total_data['get_counts']) ? $total_data['get_counts']:0 ;?></td>
                <td><?= isset($total_data['own_get_counts']) ? $total_data['own_get_counts']:0 ;?></td>
                <td><?= isset($total_data['input_get_counts']) ? $total_data['input_get_counts']:0 ;?></td>
                <?php foreach($hit_type as $key => $hit) {?>
                <td><?= isset($total_data[$key]) ? $total_data[$key] : 0 ?></td>
                <?php }?>
            </tr>
            <?php foreach ($list as $key => $stat) {?>
            <tr>
                <td><?= $key; ?></td>
                <td><?= isset($stat['all_counts']) ? $stat['all_counts']:0;?></td>
                <td><?= isset($stat['success_counts']) ? $stat['success_counts']:0 ;?></td>
                <td><?= isset($stat['get_counts']) ? $stat['get_counts']:0 ;?></td>
                <td><?= isset($stat['own_get_counts']) ? $stat['own_get_counts']:0 ;?></td>
                <td><?= isset($stat['input_get_counts']) ? $stat['input_get_counts']:0 ;?></td>
                <?php foreach($hit_type as $key => $hit) {?>
                <td><?= isset($stat[$key]) ? $stat[$key] : 0 ?></td>
                <?php }?>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">

    $(document).ready(function() {

        // choose cuishou user
        $("#choose_user_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号'
        });

        // export file
        $("#file_export").click(function () {
            if (!checkTime()){
                return false;
            }
            // init params
            var params = '';
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var choose_user_sel = $('#choose_user_sel').val();

            if (time_begin) {
                params = params + '&begin='+time_begin;
            }

            if (time_end) {
                params = params + '&end=' + time_end;
            }

            if (choose_user_sel) {
                params = params + '&id=' + choose_user_sel;
            }

            // tidy url
            if (params) {
                params=params.replace('&', '?');
            }

            var url_export = '/Home/RiskListStat/downloadDetail' + params;

            $.fileDownload(url_export);
            return false;
        });

    });

    function checkTime() {

        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();

        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }

        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }

        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }

        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }

        if (!time_end) {
            time_end = today_str;
        }

        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }

        // calculate the days between begin and end
        var day_diff =  Math.floor(time_diff/8.64e7);

        //  the time should less than 31
        if (day_diff > 365) {
            alert('单次查询时间范围不能超过365天');
            return false;
        }

        // check choose user
        var choose_user = $('#choose_user_sel').val();
        if (!choose_user) {
            alert('请选择账号');
            return false;
        }

        return true;
    }
</script>
</body>
</html>
