<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row_first {
            margin: 10px;
        }
        .container label {
            margin-left: 5px;
        }
        .row_second div {
            margin-left: 10px;
        }
        .button_group {
            margin-left: 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="/Home/RiskListStat/index" method="get" id="form_id">
                <div class="row_first">
                    <div class="form-group">
                        <label  class="control-label">开始时间</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label  class="control-label">结束时间</label>
                        <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label for="account_id">选择客户</label>
                        <select name="account_id" id="account_id">
                            <option value="">选择客户</option>
                            <?php foreach ($account_list as $account) { ?>
                            <option value="<?= $account['id']; ?>" <?= ($account['id'] == $input['account_id']) ? 'selected' : '' ?>> <?= $account['name'] ?></option>
                            <?php  } ?>
                        </select>
                    </div>
                    <div class="form-group pull-right button_group">
                        <button type="button" id="file_export" class="btn btn-success btn-sm">导出（列表）</button>
                    </div>
                    <div class="form-group pull-right">
                        <input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询">
                    </div>
                </div>
                <div class="row_second">
                    <div class="form-group">
                        <select class="form-control" name="contract_status" id="contract_status" onChange="return requestUser()">
                            <option value="0" selected>签约状态</option>
                            <option value="1" <?= ($input['contract_status'] == '1') ? 'selected' : '' ;?>>已签约已付款</option>
                            <option value="2" <?= ($input['contract_status'] == '2') ? 'selected' : '' ;?>>已签约未付款</option>
                            <option value="3" <?= ($input['contract_status'] == '3') ? 'selected' : '' ;?>>未签约</option>
                            <option value="4" <?= ($input['contract_status'] == '4') ? 'selected' : '' ;?>>其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <select class="form-control" name="status" id="status" onChange="return requestUser()">
                            <option value="" selected>账号状态</option>
                            <option value="1" <?= ($input['status'] == '1') ? 'selected' : '' ;?>>正常</option>
                            <option value="2" <?= ($input['status'] == '2') ? 'selected' : '' ;?>>禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <select class="form-control" id="choose_user_sel" name="id">
                            <option value="" >选择账号</option>
                            <?php foreach ($product_list as $product) {?>
                                <option value="<?= $product['id'] ?>"  <?= isset($input['id']) && $input['id'] == $product['id'] ? 'selected' : '' ?>><?= $product['developer'] ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <tr align="center">
                <th>账号ID</th>
                <th>账号名称</th>
                <th>客户名称</th>
                <th>总查询量</th>
                <th>有效查询量</th>
                <th>总查得量</th>
                <th>本人查得量</th>
                <th>联系人查得量</th>
                <?php foreach($hit_type as $key => $hit) {?>
                <th><?=$hit?></th>
                <?php }?>
            </tr>
            <tr>
                <td colspan="2"></td>
                <td style="text-align: center">总计</td>
                <td><?= isset($total_data['all_counts']) ? $total_data['all_counts'] : 0; ?></td>
                <td><?= isset($total_data['success_counts']) ? $total_data['success_counts']:0 ;?></td>
                <td><?= isset($total_data['get_counts']) ? $total_data['get_counts']:0 ;?></td>
                <td><?= isset($total_data['own_get_counts']) ? $total_data['own_get_counts']:0 ;?></td>
                <td><?= isset($total_data['input_get_counts']) ? $total_data['input_get_counts']:0 ;?></td>
                <?php foreach($hit_type as $key => $hit) {?>
                <td><?= isset($total_data[$key]) ? $total_data[$key] : 0 ?></td>
                <?php }?>
            </tr>
            <?php foreach ($list as $stat) { ?>
            <tr>
                <td>{$stat['id']}</td>
                <td>
                    <a href="{:U('detail',['id'=>$stat['id']])}">{$stat['developer']}</a>
                </td>
                <td><?= isset($stat['account_name']) ? $stat['account_name'] : 0 ?></td>
                <td><?= isset($stat['all_counts']) ? $stat['all_counts'] : 0 ?></td>
                <td><?= isset($stat['success_counts']) ? $stat['success_counts']:0 ;?></td>
                <td><?= isset($stat['get_counts']) ? $stat['get_counts'] : 0 ?></td>
                <td><?= isset($stat['own_get_counts']) ? $stat['own_get_counts']:0 ;?></td>
                <td><?= isset($stat['input_get_counts']) ? $stat['input_get_counts']:0 ;?></td>
                <?php foreach($hit_type as $key => $hit) {?>
                <td><?= isset($stat[$key]) ? $stat[$key] : 0 ?></td>
                <?php }?>
            </tr>
            <?php } ?>
        </table>
    </div>

    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>

</div>
</div>
<script type="text/javascript">
$(function() {
    // choose user
    $("#choose_user_sel").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择账号'
    });

    // 客户select2
    $("#account_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择客户'
    });

    // export file
    $("#file_export").click(function () {
        if (!checkTime()){
            return false;
        }
        var params = genParamsForFile();
        var url_export = '/Home/RiskListStat/downloadList' + params;
        $.fileDownload(url_export);
        return false;
    });
});

function genParamsForFile()
{
    // init params
    var params = '';
    var time_begin = $('#time_begin').val();
    var time_end = $('#time_end').val();
    var choose_user_sel = $('#choose_user_sel').val();
    var contract_status = $('#contract_status').val();
    var status = $('#status').val();
    var account_id = $('#account_id').val();

    if (time_begin) {
        params += '&begin='+time_begin;
    }
    if (time_end) {
        params += '&end=' + time_end;
    }
    if (choose_user_sel) {
        params += '&id=' + choose_user_sel;
    }
    if (contract_status) {
        params += '&contract_status=' + contract_status;
    }
    if (status) {
        params += '&status=' + status;
    }
    if (account_id) {
        params += '&account_id='+account_id;
    }

    // tidy url
    if (params) {
        params = params.replace('&', '?');
    }
    return params;
}

function requestUser()
{
    var contract_status = $('#contract_status').val();
    var status = $('#status').val();
    var url = '/Home/RiskListStat/clientList';

    $.post(url, {contract_status : contract_status, status:status}).success(function(response) {
        console.log(response);
        var client_info = response.info;
        var choose_user_sel = $('#choose_user_sel');
        // 保留上次select的第一个的option
        var ele_first = $('#choose_user_sel option').first();
        choose_user_sel.empty();
        choose_user_sel.append(ele_first);

        // 填充新的option
        for (var i in client_info) {
            var element = client_info[i];
            var ele_client = "<option value='"+ element.id +"'>"+ element.developer +"</option>";
            $('#choose_user_sel').append(ele_client);
        }

        // 重新渲染样式
        choose_user_sel.select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号'
        });
    });
}

function checkTime() {
    // get begin time and end time
    var time_begin = $('#time_begin').val();
    var time_end = $('#time_end').val();
    var today_str = (new Date()).toDateString();

    // change time format for firefox
    time_end = time_end.replace(/\-/g, '\/');
    time_begin = time_begin.replace(/\-/g, '\/');

    // check begin time
    if (!time_begin && time_end) {
        alert('请选择开始时间');
        return false;
    }

    // check end time
    if (time_begin && !time_end) {
        alert('请选择结束时间');
        return false;
    }

    if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
        alert('请选择有效的结束时间');
        return false;
    }

    // set default time
    if (!time_begin) {
        time_begin = today_str;
    }

    if (!time_end) {
        time_end = today_str;
    }

    // check time
    // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
    var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
    if (time_diff < 0) {
        alert('开始时间必须小于结束时间');
        return false;
    }

    // calculate the days between begin and end
    var day_diff =  Math.floor(time_diff/8.64e7);

    //  the time should less than 31
    if (day_diff <= 365) {
        return true;
    } else {
        alert('单次查询时间范围不能超过365天');
        return false;
    }
}

</script>
</body>
</html>
