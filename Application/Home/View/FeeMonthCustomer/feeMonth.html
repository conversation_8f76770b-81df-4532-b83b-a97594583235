<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__datepicker/bootstrap-datepicker.css">
    <style>
        .row-first { margin-bottom: 10px; }
        .row-first label, .row-second label { margin-left: 10px;}
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">
                <input type="hidden" name="product_id" value="{$input['product_id']}">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="start">开始月份：</label>
                        <input type="text" name="start" id="start" class="form-control" value="<?= (!isset($input['start']) || !$input['start']) ? date('Y-m', strtotime('-1 month')) : $input['start']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="end">结束月份：</label>
                        <input type="text" name="end" id="end" class="form-control" value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m', strtotime('-1 month')) : $input['end']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="customer_id">客户名称</label>
                        <select name="customer_id">
                            <option value="">请选择客户名称</option>
                            <foreach name="customer" item="vo">
                                <option value="{$vo['customer_id']}" <if condition="$input['customer_id'] == $vo['customer_id']">selected</if>>{$vo['name']}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="account_id">账号名称</label>
                        <select name="account_id">
                            <option value="">请选择账号名称</option>
                            <?php if ($customer[$input['customer_id']]['account_list']) {
                                foreach($customer[$input['customer_id']]['account_list'] as $key => $val) {
                            ?>
                            <option value="{$val['account_id']}" <?= ($input['account_id'] == $val['account_id']) ? 'selected' : '' ?>>{$val['account_name']}</option>
                            <?php }} ?>
                        </select>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" onclick="return check_time()" class="btn btn-primary btn-sm" value="查询"></li>
                            <li>
                                <a class="btn btn-info btn-sm" href="{:U('/Home/FeeMonthCustomer/detail',['customer_id'=>$input['customer_id']])}" role="button">返回</a>
                            </li>
                            <li>
                                <a href="javascript:;" onclick="download_file()" class="btn btn-success">导出</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">{$product_info['product_name']}产品月对账单详情</h3></div>
        <table class="table table-hover table-bordered" style="table-layout: fixed">
            <thead>
            <tr>
                <th>日期</th>
                <th>计费用量
                    <span class="v-table-sort-icon">
                        <i class="v-icon-up-dir <?= $input['order'] == 'fee_amount asc' ? 'checked' : ''?>" onclick="order_list('fee_amount asc')"></i>
                        &nbsp;
                        <i class="v-icon-down-dir <?= ($input['order'] == 'fee_amount desc') ? 'checked' : ''?>" onclick="order_list('fee_amount desc')"></i>
                    </span>
                </th>
                <th>费用（元）
                    <span class="v-table-sort-icon">
                        <i class="v-icon-up-dir <?= $input['order'] == 'fee_price asc' ? 'checked' : ''?>" onclick="order_list('fee_price asc')"></i>
                        &nbsp;
                        <i class="v-icon-down-dir <?= !isset($input['order']) || ($input['order'] == 'fee_price desc') ? 'checked' : ''?>" onclick="order_list('fee_price desc')"></i>
                    </span>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>总计</td>
                <td>{$total['fee_amount']|default=0}</td>
                <td>{$total['fee_price']|default=0|round=2}</td>
            </tr>
            <?php foreach ($list as $key => $value): ?>
            <tr>
                <td>{$value['month']}</td>
                <td>{$value['fee_amount']|default=0}</td>
                <td>{$value['fee_price']|default=0|round=2}</td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>
</div>
<script type="text/javascript" src="__JS__datepicker/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="__JS__datepicker/bootstrap-datepicker.zh-CN.js"></script>
<script type="text/javascript">
    $('select[name="customer_id"]').select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择客户名称',
        width: '200px'
    });
    $('select[name="account_id"]').select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择账号名称',
        width: '200px'
    });

    // 选择客户、账号联动
    $('select[name="customer_id"]').change(function() {
        var customer_id = $(this).val().toString();
        account_info(customer_id);
    });

    function account_info(customer_id)
    {
        var customer_list = '{$customer_json}';
        var customer_list = JSON.parse(customer_list);
        var account_list = customer_list[customer_id]['account_list'];
        var account_id = '{$input["account_id"]}';
        var html = '<option value="">请选择账号名称</option>';
        $.each(account_list, function(index, content) {
            var sel = (account_id == content.account_id) ? 'selected' : '';
            html += '<option value="'+content.account_id+'" '+sel+'>'+content.account_name+'</option>'
        });
        $('select[name="account_id"]').html(html);
    }
    $('input[name="start"]').datepicker({
        format : 'yyyy-mm',
        language: 'zh-CN',
        autoclose: true,
        startView: 'months',
        maxViewMode:'years',
        minViewMode:'months',
        onClose: function(dateText, inis) {
            $('input[name="start"]').val(dateText);
        }

    });
    $('input[name="end"]').datepicker({
        format : 'yyyy-mm',
        language: 'zh-CN',
        maxDate: '-1m',
        autoclose: true,
        startView: 'months',
        maxViewMode:'years',
        minViewMode:'months',
        onClose: function(dateText, inis) {
            $('input[name="end"]').val(dateText);
        }
    });

    function check_time()
    {
        var start = $('input[name="start"]').val();
        var end = $('input[name="end"]').val();
        if (start > end) {
            alert('请选择正确的月份');
            return false;
        }
    }

    function order_list(order)
    {
        var param = param_list();
        var param = param ? param+'&order='+order : '?order='+order;
        window.location.href = '/Home/FeeMonthCustomer/feeMonth.html'+param;
    }

    function param_list()
    {
        var start = '{$input["start"]}';
        var end = '{$input["end"]}';
        var customer_id = '{$input["customer_id"]}';
        var product_id = '{$input["product_id"]}';
        var account_id = '{$input["account_id"]}';
        var param = '';
        if (start) {
            param += '&start='+start;
        }
        if (end) {
            param += '&end='+end;
        }
        if (customer_id) {
            param += '&customer_id='+customer_id;
        }
        if (product_id) {
            param += '&product_id='+product_id;
        }
        if (account_id) {
            param += '&account_id='+account_id;
        }
        if (param) {
            param = param.replace('&', '?');
        }
        return param;
    }

    //导出
    function download_file()
    {
        var start = $('input[name="start"]').val();
        var end = $('input[name="end"]').val();
        var product_id = $('input[name="product_id"]').val();
        var customer_id = $('select[name="customer_id"]').val();
        var account_id = $('select[name="account_id"]').val();
        var param = '';
        if (start) {
            param += '&start='+start;
        }
        if (end) {
            param += '&end='+end;
        }
        if (product_id) {
            param += '&product_id='+product_id;
        }
        if (customer_id) {
            param += '&customer_id='+customer_id;
        }
        if (account_id) {
            param += '&account_id='+account_id;
        }
        if (param) {
            param = param.replace('&', '?');
        }
        var url_export = '/Home/FeeMonthCustomerFile/feeMonth' + param;
        $.fileDownload(url_export);
        return false;
    }
</script>
</body>
</html>
