<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__quill.js" type="text/javascript"></script>
    <!-- Quill JS Vue -->
    <script src="__JS__vue-quill-editor.js" type="text/javascript"></script>
    <!-- Include stylesheet -->
    <link rel="stylesheet" href="__CSS__quill.snow.css">
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<!-- 邮件发送对账单 -->
<include file="Common@components/email_bills"/>
<!-- 结算单 -->
<include file="Common@components/history_bill"/>
<!-- 消费明细 -->
<include file="Common@components/consumption_bill"/>
<!-- 消费明细 通用按时间组件 -->
<include file="Common@components/consumption_bill_common_date"/>
<!--  消费明细 如果通用的按用量 && 固定价格 -->
<include file="Common@components/consumption_bill_common_fixed"/>
<!-- 配置冲突 不可以导出账单 -->
<include file="Common@components/consumption_bill_cant_export"/>
<!--  通用的按用量 && 累进阶梯 -->
<include file="Common@components/consumption_bill_common_progression"/>
<!--  通用的按用量 && 到达阶梯 -->
<include file="Common@components/consumption_bill_common_reach"/>
<!-- 区分运营商按时间 -->
<include file="Common@components/consumption_bill_operator_date"/>
<!--  区分运营商 按固定价格 -->
<include file="Common@components/consumption_bill_operator_fixed"/>
<!-- 区分运营商 累进、到达-->
<include file="Common@components/consumption_bill_operator_complex"/>
<!-- 适配 萨摩耶客户账单 -->
<include file="Common@components/consumption_bill_C20181101XM4HQS"/>
<!-- 萨摩耶快捷版组件-->
<include file="Common@components/consumption_bill_samoye_shortcut"/>
<!-- 萨摩耶秒配详单版组件 -->
<include file="Common@components/consumption_bill_samoye_pei_dan"/>
<!-- 	深圳市恒信永利金融服务有限公司  -->
<include file="Common@components/consumption_bill_C2019032668WHR7"/>
<!-- 	深圳市恒信永利金融服务有限公司详单板本v2  -->
<include file="Common@components/consumption_bill_hengli_xiangdanv2"/>
<!-- 	深圳市恒信永利金融服务有限公司秒配单号版  -->
<include file="Common@components/consumption_bill_hengli_pei"/>
<!-- 凡普金科组件 -->
<include file="Common@components/consumption_bill_C20190417PX262B"/>
<!-- 凡普金科组件 sheet1  -->
<include file="Common@components/consumption_bill_fanpu_sheet1"/>
<!-- 特快 -->
<include file="Common@components/consumption_bill_C20181101PQ6OVN"/>
<!-- 	特快详单板本v2  -->
<include file="Common@components/consumption_bill_tekuai_xiangdan_v2"/>
<!-- 	特快秒配单号版  -->
<include file="Common@components/consumption_bill_tekuai_pei"/>
<!-- 卡牛 -->
<include file="Common@components/consumption_bill_C20181101YRXUX2"/>
<!-- 卡牛sheet1 -->
<include file="Common@components/consumption_bill_kaniu_sheet1"/>
<!-- 随手记 -->
<include file="Common@components/consumption_bill_C20181101LHVREG"/>
<!-- 随手记sheet1 -->
<include file="Common@components/consumption_bill_suishouji_sheet1"/>
<!-- 量化派sheet1 -->
<include file="Common@components/consumption_bill_C20181101SQ9IIO"/>
<!--人人贷-->
<include file="Common@components/consumption_bill_C20181101QU9C0N"/>
<!--骑呗|极速云-->
<include file="Common@components/consumption_bill_C20181101G8K9DA"/>
<include file="Common@components/consumption_bill_qibai_sheet1"/>
<!--读秒|广汽|杭银 （快捷版打包）-->
<include file="Common@components/consumption_bill_C201811012TPQQ8"/>
<include file="Common@components/consumption_bill_dumiao_sheet1"/>
<!--小象优品（存在产品结算单）-->
<include file="Common@components/consumption_bill_C201908074WYCOA"/>
<include file="Common@components/consumption_bill_product_fee_sheet"/>


<div class="container" id="app">
    <email-bill-template customer_id="<?= $customer_id ?>"
                         backend_api_email="<?=  $backend_api_email ?>"
                         backend_api_email_bill="<?=  $backend_api_email_bill ?>"
                         backend_api_history="<?= $backend_api_history ?>"></email-bill-template>
</div>
</body>
<script>
    new Vue({
        el: '#app'
    });

</script>
</html>
