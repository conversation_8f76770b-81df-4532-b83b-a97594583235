<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
        <div id="breadcrumb_search_box">
            <a href="javascript:;" onclick="DHB.INFO.set('{:U('bmMatchingAdd')}','添加客户')" class="btn btn-success">添加客户</a>
        </div>
    </div>

    <div class="form-group">
        <div class="btn-group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <if condition="empty($input['active'])">
                    全部
                    <elseif condition="$input['active'] == 1"/>
                    可用
                    <elseif condition="$input['active'] == 2"/>
                    禁用
                </if>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <li><a href="{:U('bmMatching',array('active'=>''))}">全部</a></li>
                <li><a href="{:U('bmMatching',array('active'=>1))}">可用</a></li>
                <li><a href="{:U('bmMatching',array('active'=>2))}">禁用</a></li>
            </ul>
        </div>
    </div>

</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">客户列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th width="5%">ID</th>
                <th width="20%">客户名称</th>
                <th width="20%">APIKEY</th>
                <th width="20%">截止日期</th>
                <th width="20%">添加时间</th>
                <th width="5%">状态</th>
                <th width="5%">操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($fields as $key => $value): ?>
            <tr>
                <td>{$value['id']}</td>
                <td>{$value['owner']}</td>
                <td>{$value['apikey']}</td>
                <td>{$value['validuntil']}</td>
                <td>{:date('Y-m-d H:i:s',$value['created_at'])}</td>
                <td>{$status_lists[$value['active']]}</td>
                <td>
                    <a href="javascript:;" onclick="DHB.INFO.set('{:U('bmMatchingSet',array('id'=>$value['id']))}','编辑账户')" class="btn btn-primary btn-xs">编辑</a>
                </td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>

    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>
</body>
</html>