<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
        <div id="breadcrumb_search_box">
            <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'bmCrawlingAdd\')}','添加客户')" class="btn btn-success">添加客户</a>
        </div>
    </div>

    <div class="form-group">
        <span class="form-control-static" style="color: #2aabd2;">状态:</span>
        <div class="btn-group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <if condition="empty($input['status'])">
                    邦秒爬
                    <elseif condition="$input['status'] == 1"/>
                    可用
                    <elseif condition="$input['status'] == 2"/>
                    禁用
                </if>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <li><a href="{:U('index',array('status'=>'','active'=>$input['active']))}">邦秒爬</a></li>
                <li><a href="{:U('index',array('status'=>1,'active'=>$input['active']))}">可用</a></li>
                <li><a href="{:U('index',array('status'=>2,'active'=>$input['active']))}">禁用</a></li>
            </ul>
        </div>
        &nbsp;
        <div class="btn-group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <if condition="empty($input['active'])">
                    邦秒配
                    <elseif condition="$input['active'] == 1"/>
                    可用
                    <elseif condition="$input['active'] == 2"/>
                    禁用
                </if>
                <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
                <li><a href="{:U('index',array('active'=>'','status'=>$input['status']))}">邦秒配</a></li>
                <li><a href="{:U('index',array('active'=>1,'status'=>$input['status']))}">可用</a></li>
                <li><a href="{:U('index',array('active'=>2,'status'=>$input['status']))}">禁用</a></li>
            </ul>
        </div>

    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title"><b>客户列表</b></h3></div>
        <table class="table table-hover table-bordered"  style='vertical-align: middle;text-align: center;'>
            <thead>
            <tr>
                <td rowspan="2">客户名称</td>
                <td colspan="2">邦秒爬</td>
                <td colspan="2">邦秒配</td>
                <td rowspan="2">操作</td>
            </tr>
            <tr>
                <td>状态</td>
                <td>添加时间</td>
                <td>状态</td>
                <td>添加时间</td>
            </tr>
            </thead>

            <tbody>
            <?php foreach ($rst as $key => $value): ?>
                <tr>
                <td>
                    <a href="javascript:;" onclick="DHB.INFO.view('{:U(\'accountMsg\',array(\'id\'=>$value[\'id\']))}','查看账户')">{$value['developer']}</a>
                </td>
                <td>{$status_lists[$value['status']]}</td>
                <IF condition="empty($value['created_at'])">
                    <td></td>
                    <ELSE/>
                    <td>{:date('Y-m-d H:i:s',$value['created_at'])}</td>
                </IF>
                <td>
                    {$status_lists[$value['active']]}
                </td>
                <IF condition="empty($value['created_time'])">
                    <td></td>
                    <ELSE/>
                    <td>
                        {:date('Y-m-d H:i:s',$value['created_time'])}
                    </td>
                </IF>
                <td>
                    <a href="javascript:;" onclick="DHB.INFO.view('{:U(\'setAccount\',array(\'id\'=>$value[\'id\']))}','编辑账户')" class="btn btn-primary btn-xs">编辑</a>
                </td>
            </tr>

            <?php endforeach ?>
            </tbody>
        </table>
    </div>

    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>
</body>
</html>