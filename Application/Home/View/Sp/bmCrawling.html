<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
    <include file="Common@Public/header" />
    <include file="Common@Public/dhb_info" />
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav" />
            <div id="breadcrumb_search_box">
                <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'bmCrawlingAdd\')}','添加客户')" class="btn btn-success">添加客户</a>
            </div>
        </div>

        <div class="form-group">
            <div class="btn-group">
                <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <if condition="empty($input['status'])">
                        全部
                        <elseif condition="$input['status'] == 1"/>
                        可用
                        <elseif condition="$input['status'] == 2"/>
                        禁用
                    </if>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="{:U('bmCrawling',array('status'=>''))}">全部</a></li>
                    <li><a href="{:U('bmCrawling',array('status'=>1))}">可用</a></li>
                    <li><a href="{:U('bmCrawling',array('status'=>2))}">禁用</a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="panel panel-default">
            <div class="panel-heading"><h3 class="panel-title">客户列表</h3></div>
            <table class="table table-hover table-bordered">
                <thead>
                    <tr>
                        <th >ID</th>
                        <th >客户名称</th>
                        <th>账户</th>
                        <th >状态</th>
                        <th>截止日期</th>
                        <th >添加时间</th>
                        <th >操作</th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($lists as $key => $value): ?>
                    <tr>
                        <td>{$value['id']}</td>
                        <td>{$value['developer']}</td>
                        <td>{$value['email']}</td>
                        <td>{$status_lists[$value['status']]}</td>
                        <td><?= $value['expiration_date'] ? date('Y-m-d H:i:s',$value['expiration_date']) : '' ?></td>
                        <td><?= $value['created_at'] ? date('Y-m-d H:i:s',$value['created_at']) : '' ?></td>
                        <td>
                            <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'setinfo\',array(\'id\'=>$value[\'id\']))}','编辑账户')" class="btn btn-primary btn-xs">编辑</a>
                        </td>
                    </tr>
                <?php endforeach ?>
                </tbody>
            </table>
        </div>

        <nav>
            <ul class="pagination">
                {$page}
            </ul>
        </nav>

    </div>
</body>
</html>