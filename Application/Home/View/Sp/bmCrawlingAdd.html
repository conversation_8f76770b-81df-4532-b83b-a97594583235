<div class="btn-group" role="group" aria-label="...">
    <button class="btn btn-primary" onclick="DHB.INFO.set('{:U(\'bmCrawlingAdd\')}','添加账户')">
        邦秒爬
    </button>
    <button class="btn btn-default" onclick="DHB.INFO.set('{:U(\'bmMatchingAdd\')}','添加账户')">
        邦秒配
    </button>
</div>
<hr/>

<form class="form-horizontal" role="form" id="form_set_id" data-info="<?= isset($info)?$info:'' ?>">
    <div class="form-group">
        <label class="col-sm-2 control-label">客户名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="developer" value="">
        </div>
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="status">
                <?php foreach ($status_lists as $key => $value): ?>
                <option value="{$key}">{$value}</option>
                <?php endforeach ?>
            </select>
        </div>
    </div>
    <div class="form-group access_sel">
        <label class="col-sm-2 control-label">账号邮箱</label>
        <div class="col-sm-4">
            <input type="email" class="form-control" name="email" value="">
        </div>

        <label class="col-sm-2 control-label">接入方式</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="source" class="source-radio" value="ui" checked>
                h5接入
            </label>
            <label class="radio-inline">
                <input type="radio" name="source" class="source-radio" value="api">
                api接入
            </label>
        </div>
    </div>

     <div class="form-group ui-dist">
         <label class="col-sm-2 control-label">授权期限</label>
         <if condition="empty($info['token_due_date'])">
             <div class="col-sm-4">
                 <input type="date" class="form-control" name="token_due_date" value="">
             </div>
             <else/>
             <div class="col-sm-4">
                 <input type="date" class="form-control" name="token_due_date" value="{:date('Y-m-d',$info['token_due_date'])}">
             </div>
         </if>
         <label class="col-sm-2 control-label">联系人页面</label>
         <div class="col-sm-4">
             <label class="radio-inline">
                 <input type="radio" name="contactor_page" value="N" checked>不接入
             </label>
             <label class="radio-inline">
                 <input type="radio" name="contactor_page" value="Y">接入
             </label>
         </div>
     </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">是否生成报告</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_report" value="0" checked>
                否
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_report" value="1">
                是
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否输出催收分参数</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_dunning" value="2" class="cuishou_radio" checked>
                否
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_dunning" value="1" class="cuishou_radio">
                是
            </label>
        </div>
    </div>
    <div class="form-group cuishou" style="display: none;">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">催收分APIKEY</label>
            <div class="col-sm-4">
                <input type="text" class="form-control" name="service_key" value="">
            </div>
    </div>
    <div class="form-group cuishou" style="display: none;">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">催收分PASSWORD</label>
        <div class="col-sm-4">
            <input type="text" name="service_secret" class="form-control" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">截止日期</label>
        <if condition="empty($info['expiration_date'])">
            <div class="col-sm-4">
                <input type="date" class="form-control" name="expiration_date" value="">
            </div>
            <else/>
            <div class="col-sm-4">
                <input type="date" class="form-control" name="expiration_date" value="{:date('Y-m-d',$info['expiration_date'])}">
            </div>
        </if>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">APPID</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="appid" id="input_appid" value="" readonly>
        </div>

        <IF condition="empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appid',32)">生 成</button>
            </div>
        </IF>

    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">APPSECRET</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="appsecret" id="input_appsecret" value="" readonly>
        </div>
        <IF condition="empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appsecret',64)">生 成</button>
            </div>
        </IF>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">推送地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="notify_url" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">重定向地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="close_redirect_url" value="">
        </div>
    </div>
</form>
<div class="alert alert-danger" role="alert">
    <p>注意：</p>
    <p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>
    <p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>
    <p>3.推送地址由客户提供,信息(eg:通话详单)推送到该地址,无需求可置空</p>
    <p>4.重定向地址由客户提供,接入h5时,授权成功后的跳转地址,无需求可置空</p>
    <p>5.输出催收参数时，应填写催收参数的apikey和password</p>
</div>

<script type="text/javascript">
    $(function(){
        var info = $('#form_set_id').attr('data-info');

        if(!info){
            creat_hash('input_appsecret',64);
            creat_hash('input_appid',32);
        }

        $("input:radio.source-radio").on('change', function(event) {
            event.preventDefault();
            $("div.ui-dist").remove();
            if ('ui' != $(this).val()) {
                $("div.ui-dist").remove();
            } else {
                createInput();
            }
        });
        $("input:radio.cuishou_radio").on('change', function(event) {
            if ('1' == $(this).val()) {
                $("div.cuishou").css({
                    display: 'block'
                });
            } else {
                $("div.cuishou").css({
                    display: 'none'
                });
            }
        });

    });
    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length)
    {
        if(confirm('修改appid,appsecret会使当前客户的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        }
    }

    function createInput()
    {
        var htm = '\
            <div class="form-group ui-dist"> \
                <label class="col-sm-2 control-label">授权期限</label> \
                <div class="col-sm-4"> \
                    <input type="date" class="form-control" name="token_due_date" value=""> \
                </div> \
                <label class="col-sm-2 control-label">联系人页面</label> \
                <div class="col-sm-4"> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" value="N" checked> \
                        不接入 \
                   </label> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" value="Y" <?php if("Y" == $info["contactor_page"]): ?>checked<?php endif;?>>接入 \
                   </label> \
               </div> \
            </div> \
'
        $("div.access_sel").after(htm);
    }

</script>