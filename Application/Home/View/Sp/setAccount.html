
        <ul id="myTab" class="nav nav-pills">
            <li class="nav-item active">
                <a href="#bmcrawling" class="nav-link active" data-toggle="tab" >邦秒爬</a>
            </li>
            <li class="nav-item">
                <a href="#bmmatching"  class="nav-link" data-toggle="tab" >邦秒配</a>
            </li>
        </ul>
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade in active" id="bmcrawling">
            <form class="form-horizontal" method="post" role="form" id="form_set_id">

                    <div class="panel-body">
                        <?php if ($info): ?>
                        <input type="hidden" name="id" value="{$info['id']}">
                        <?php endif ?>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">客户名称</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" name="developer" value="{$info['developer']}">
                            </div>
                            <label class="col-sm-2 control-label">账号状态</label>
                            <div class="col-sm-4">
                                <select class="form-control" name="status">
                                    <?php foreach ($status_lists as $key => $value): ?>
                                        <option value="{$key}"  <?php echo $key == $info['status'] ? 'selected="selected"' : ''?>>{$value}</option>
                                    <?php endforeach ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group access_sel">
                            <label class="col-sm-2 control-label">账号邮箱</label>
                            <div class="col-sm-4">
                                <input type="email" class="form-control" name="email" value="{$info['email']}">
                            </div>

                            <label class="col-sm-2 control-label">接入方式</label>
                            <div class="col-sm-4">
                                <label class="radio-inline">
                                    <input type="radio" name="source" class="source-radio" value="ui" checked>
                                    h5接入
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="source" class="source-radio" value="api" <?php if('api' == $info['source']):?>checked<?php endif;?>>
                                    api接入
                                </label>
                            </div>
                        </div>

                        <if condition="'ui' == $info['source']">
                            <div class="form-group ui-dist">
                                <label class="col-sm-2 control-label">授权期限</label>
                                <if condition="empty($info['token_due_date'])">
                                    <div class="col-sm-4">
                                        <input type="date" class="form-control" name="token_due_date" value="">
                                    </div>
                                    <else/>
                                    <div class="col-sm-4">
                                        <input type="date" class="form-control" name="token_due_date" value="{:date('Y-m-d',$info['token_due_date'])}">
                                    </div>
                                </if>
                                <label class="col-sm-2 control-label">联系人页面</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="contactor_page" value="N" <?php if('N' == $info['contactor_page']):?>checked<?php endif;?>>
                                        不接入
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="contactor_page" value="Y" <?php if('Y' == $info['contactor_page']):?>checked<?php endif;?>>接入
                                    </label>
                                </div>
                            </div>
                        </if>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">是否生成报告</label>
                            <div class="col-sm-4">
                                <label class="radio-inline">
                                    <input type="radio" name="need_report" value="0" <?= (1 != $info['need_report']) ? 'checked' : '' ?>>
                                    否
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="need_report" value="1" <?= (1 == $info['need_report']) ? 'checked' : '' ?>>
                                    是
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否输出催收分参数</label>
                            <div class="col-sm-4">
                                <label class="radio-inline">
                                    <input type="radio" name="need_dunning" value="2" class="cuishou_radio" <?= (!isset($info['need_dunning']) || (1 != $info['need_dunning'])) ? 'checked' : '' ?>>
                                    否
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="need_dunning" value="1" class="cuishou_radio" <?= (1 == $info['need_dunning']) ? 'checked' : '' ?>>
                                    是
                                </label>
                            </div>
                        </div>
                        <div class="form-group cuishou" style="display:<?= (isset($info['need_dunning']) && (1 == $info['need_dunning'])) ? 'block' : 'none' ?>">
                            <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">催收分APIKEY</label>
                            <div class="col-sm-4">
                                <input type="text" class="form-control" name="service_key" value="{$cuishou_info['service_key']}">
                            </div>
                        </div>
                        <div class="form-group cuishou" style="display: <?= (isset($info['need_dunning']) && (1 == $info['need_dunning'])) ? 'block' : 'none' ?>">
                            <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">催收分PASSWORD</label>
                            <div class="col-sm-4">
                                <input type="text" name="service_secret" class="form-control" value="{$cuishou_info['service_secret']}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">截止日期</label>
                            <if condition="empty($info['expiration_date'])">
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="expiration_date" value="">
                                </div>
                                <else/>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="expiration_date" value="{:date('Y-m-d',$info['expiration_date'])}">
                                </div>
                            </if>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">APPID</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="appid" id="input_appid" value="{$info['appid']}" readonly>
                            </div>
                            <if condition="empty($info['appid'])">
                                <div class="col-sm-2">
                                    <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appid',32)">生 成</button>
                                </div>
                            </if>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">APPSECRET</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="appsecret" id="input_appsecret" value="{$info['appsecret']}" readonly>
                            </div>

                            <if condition="empty($info['appsecret'])">
                                <div class="col-sm-2">
                                    <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appsecret',64)">生 成</button>
                                </div>
                            </if>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">推送地址</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="notify_url" value="{$info['notify_url']}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">重定向地址</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="close_redirect_url" value="{$info['close_redirect_url']}">
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-danger" role="alert">
                        <p>注意：</p>
                        <p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>
                        <p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>
                        <p>3.推送地址由客户提供,信息(eg:通话详单)推送到该地址,无需求可置空</p>
                        <p>4.重定向地址由客户提供,接入h5时,授权成功后的跳转地址,无需求可置空</p>
                        <p>5.输出催收参数时，应填写催收参数的apikey和password</p>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary submit_button" data-text="提交" data-submit-text="提交中..."
                                onclick="DHB.INFO.submit('{:U(\'setinfo\', array(\'id\'=>$value[\'id\']))}')">提交</button>
                    </div>
            </form>
            </div>

            <div class="tab-pane fade" id="bmmatching">
            <form class="form-horizontal" method="post" role="form" id="bmMatchingForm">

                <div class="panel-body">

                    <div class="form-group">
                        <label class="col-sm-2 control-label">客户名称</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="owner" value="{$lists['owner']}">
                        </div>

                        <label class="col-sm-2 control-label">账号状态</label>
                        <div class="col-sm-4">
                            <select class="form-control" name="active">
                                <?php foreach ($statuslists as $key => $value): ?>
                                <option value="{$key}" <?php echo $key == $lists['active'] ? 'selected="selected"' : ''?>>{$value}</option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">APIKEY</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" name="apikey" id="input_apikey" value="{$lists['apikey']}" readonly>
                        </div>
                        <if condition="empty($lists['apikey'])">
                            <div class="col-sm-2">
                                <button type="button" class="btn btn-default btn-block" onclick="edit_apikey('input_apikey',38)">生 成</button>
                            </div>
                        </if>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">截止日期</label>
                        <if condition="!empty($lists['validuntil'])">
                            <div class="col-sm-4">
                                <input type="date" class="form-control" name="validuntil" value="{:date('Y-m-d',strtotime($lists['validuntil']))}">
                            </div>
                            <else/>
                            <div class="col-sm-4">
                                <input type="date" class="form-control" name="validuntil" value="">
                            </div>
                        </if>
                    </div>

                    <?php foreach ($fieldlist as $key => $value): ?>
                    <?php if(in_array($value['name'],['output_yscs','output_cs'])): ?>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">{$value['remark']}</label>
                        <div class="col-sm-4">
                            <label class="radio-inline">
                                <input type="radio" name="{$value['name']}" value="1" checked>是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="{$value['name']}" value="2"<?php if($ctrlinfo[$value['name']]['value']==2):?>checked<?php endif;?>>否
                            </label>
                        </div>
                    </div>
                    <?php elseif($value['name'] == 'bind_domain' || $value['name'] == 'limit_access_ip'): ?>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">{$value['remark']}</label>
                        <div class="col-sm-10">
                            <textarea type="text" class="form-control" name="{$value['name']}">{$ctrlinfo[$value['name']]['value']}</textarea>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">{$value['remark']}</label>
                        <div class="col-sm-10">
                            <if condition="$ctrlinfo[$value['name']]['value'] == '-1'">
                                <input type="text" class="form-control" name="{$value['name']}" value="" placeholder="无限制"/>
                                <else/>
                                <input type="text" class="form-control" name="{$value['name']}" value="{$ctrlinfo[$value['name']]['value']}"/>
                            </if>
                        </div>
                    </div>
                    <?php endif ?>
                    <?php endforeach ?>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">接口输出字段</label>
                        <div class="col-sm-10 checkbox">
                            <?php foreach ($out_fields as $field) { ?>
                            <span>
                                <label class="checkbox-inline"><input type="checkbox" name="out_fields[]" value="<?= $field['id'] ?>" <?= isset($choose_fields[$field['id']]) ? 'checked' : '' ?>><?= $field['remark'] ?></label>
                            </span>
                            <?php } ?>
                        </div>
                    </div>
                </div>
                <div class="alert alert-danger" role="alert">
                    <p>注意：</p>
                    <p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>
                    <p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>
                    <p>3.绑定域名和IP白名单多个时,换行填写</p>
                    <p>4.限额无限制时置空</p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <if condition="!empty($lists['id'])">
                        <button type="button" class="btn btn-primary submit_button" data-text="提交" data-submit-text="提交中..." onclick="_submit('{:U(\'Home/Sp/bmMatchingSet\',array(\'id\'=>$lists[\'id\']))}')">提交</button>
                    <else/>
                        <button type="button" class="btn btn-primary submit_button" data-text="提交" data-submit-text="提交中..." onclick="_submit('{:U(\'Home/Sp/bmMatchingAdd\',array(\'auth_id\'=>$info[\'id\']))}')">提交</button>
                    </if>
                </div>
            </form>
            </div>
        </div>

<script type="text/javascript">

    $(function () {
        $("input:radio.cuishou_radio").on('change', function() {
            console.log('cuishou radio change');
            if ('1' == $(this).val()) {
                $("div.cuishou").css({
                    display: 'block'
                });
            } else {
                $("div.cuishou").css({
                    display: 'none'
                });
            }
        });
    });

    function _submit(url)
    {
        var postData = $("#bmMatchingForm").serializeArray();
        DHB.ajax({
            url:url,
            type:'post',
            data:postData,
            success: function() {
                window.location.reload();
            }
        });
    }

    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
            $("#"+id).val(r['data']);
        }
    });
    }

    function creat_apikey(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/gen_apikey')}",
            type:'get',
            data:{"length":length},
            success:function(r){
            $("#"+id).val(r['data']);
        }
    });
    }

    function edit_hash(id, length)
    {
        if(confirm('确定生成appid,appsecret吗,如果已授权会使当前客户的授权失效?')) {
            creat_hash(id, length);
        }
    }

    function edit_apikey(id, length)
    {
        if(confirm('确定生成Apikey吗')) {
            creat_apikey(id, length);
        }
    }

    $("input:radio.source-radio").on('change', function(event) {
        event.preventDefault();
        if ('ui' != $(this).val()) {
            $("div.ui-dist").remove();
        } else {
            $("div.ui-dist").remove();
            createInput();
        }
    });

    function createInput()
    {
        var htm = '\
            <div class="form-group ui-dist"> \
                <label class="col-sm-2 control-label">授权期限</label> \
                <div class="col-sm-4"> \
                    <input type="date" class="form-control" name="token_due_date" value=""> \
                </div> \
                <label class="col-sm-2 control-label">联系人页面</label> \
                <div class="col-sm-4"> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" value="N" checked> \
                        不接入 \
                   </label> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" value="Y" <?php if("Y" == $info["contactor_page"]): ?>checked<?php endif;?>>接入 \
                   </label> \
               </div> \
            </div> \
'
        $("div.access_sel").after(htm);
    }
</script>

