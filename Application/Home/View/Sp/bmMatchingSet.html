<form class="form-horizontal" role="form" id="form_set_id">
    <div class="form-group">
        <label class="col-sm-2 control-label">客户名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="owner" value="{$lists['owner']}">
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="active">
                <?php foreach ($statuslists as $key => $value): ?>
                <option value="{$key}" <?php echo $key == $lists['active'] ? 'selected="selected"' : ''?>>{$value}</option>
                <?php endforeach ?>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">APIKEY</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="apikey" id="input_apikey" value="{$lists['apikey']}" readonly>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">截止日期</label>
        <div class="col-sm-4">
            <input type="date" class="form-control" name="validuntil" value="{:date('Y-m-d',strtotime($lists['validuntil']))}"/>
        </div>
    </div>

    <?php foreach ($fieldlist as $key => $value): ?>
    <?php if(in_array($value['name'],['output_yscs','output_cs'])): ?>
        <div class="form-group">
            <label class="col-sm-2 control-label">{$value['remark']}</label>
            <div class="col-sm-4">
                <label class="radio-inline">
                    <input type="radio" name="{$value['name']}" value="1" <?php if($info[$value['name']]['value']==1):?>checked<?php endif;?>>是
                </label>
                <label class="radio-inline">
                    <input type="radio" name="{$value['name']}" value="2"<?php if($info[$value['name']]['value']==2):?>checked<?php endif;?>>否
                </label>
            </div>
        </div>
    <?php elseif($value['name'] == 'bind_domain' || $value['name'] == 'limit_access_ip'): ?>
        <div class="form-group">
            <label class="col-sm-2 control-label">{$value['remark']}</label>
            <div class="col-sm-10">
                <textarea type="text" class="form-control" name="{$value['name']}">{$info[$value['name']]['value']}</textarea>
            </div>
        </div>
    <?php else: ?>
        <div class="form-group">
            <label class="col-sm-2 control-label">{$value['remark']}</label>
            <div class="col-sm-10">
                <if condition="$info[$value['name']]['value'] == '-1'">
                    <input type="text" class="form-control" name="{$value['name']}" value="" placeholder="无限制"/>
                    <else/>
                    <input type="text" class="form-control" name="{$value['name']}" value="{$info[$value['name']]['value']}"/>
                </if>
            </div>
        </div>
    <?php endif ?>
    <?php endforeach ?>
</form>

<div class="alert alert-danger" role="alert">
    <p>注意：</p>
    <p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>
    <p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>
    <p>3.推送地址由客户提供,信息(eg:通话详单)推送到该地址,无需求可置空</p>
    <p>4.重定向地址由客户提供,接入h5时,授权成功后的跳转地址,无需求可置空</p>
    <p>5.绑定域名和IP白名单多个时,换行填写</p>
    <p>6.限额无限制时置空</p>
</div>