<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
    <include file="Common@Public/header" />
    <include file="Common@Public/dhb_info" />
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav" />
            <div id="breadcrumb_search_box">
                <a href="javascript:;" onclick="DHB.INFO.set('{:U('recharge',array('sid'=>$sp_info['id']))}','充值')"  class="btn btn-primary">充值</a>
                <a href="{:U('rechargerecord',array('sid'=>$sp_info['id']))}" class="btn btn-success">充值记录</a>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-heading"><h3 class="panel-title">{$sp_info['name']} 账户信息</h3></div>
            <table class="table table-hover table-bordered">
                <thead>
                    <tr>
                        <th>短信级别</th>
                        <th>剩余数量</th>
                        <th>充值数量</th>
                        <th>发送成功数量</th>
                        <th>发送失败数量</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($lists as $value): ?>
                        <tr>
                            <td>{$level_lists[$value['level']]}</td>
                            <td><?php echo $value['account'] - $value['success']?></td>
                            <td>{$value['account']}</td>
                            <td>{$value['success']}</td>
                            <td>{$value['failure']}</td>
                        </tr>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>