
<ul id="myTab" class="nav nav-tabs">
    <li class="active">
        <a href="#bmcrawling" data-toggle="tab">
            邦秒爬
        </a>
    </li>
    <li><a href="#bmmatching" data-toggle="tab">邦秒配</a></li>
</ul>

<div id="myTabContent" class="tab-content">
    <div class="tab-pane fade in active" id="bmcrawling">
        <form class="form-horizontal" method="post" role="form" id="form_set_id">

            <div class="panel-body">

                <div class="form-group">
                    <label class="col-sm-2 control-label">客户名称</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" name="developer" value="{$info['developer']}" readonly>
                    </div>
                    <label class="col-sm-2 control-label">账号状态</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="status" disabled>
                            <?php foreach ($status_lists as $key => $value): ?>
                            <option value="{$key}"  <?php echo $key == $info['status'] ? 'selected="selected"' : ''?>>{$value}</option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">账号邮箱</label>
                    <div class="col-sm-4">
                        <input type="email" class="form-control" name="email" value="{$info['email']}" readonly>
                    </div>
                    <label class="col-sm-2 control-label">截止日期</label>
                    <if condition="!empty($info['token_due_date'])">
                        <div class="col-sm-4">
                            <input type="date" class="form-control" name="token_due_date" value="{:date('Y-m-d',$info['token_due_date'])}" readonly>
                        </div>
                        <else/>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="token_due_date" value="" readonly>
                        </div>
                    </if>

                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">接入方式</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="source" value="ui" checked disabled>
                            h5接入
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="source" value="api" <?php if('api' == $info['source']):?>checked<?php endif;?> disabled>
                            api接入
                        </label>
                    </div>
                    <label class="col-sm-2 control-label">联系人页面</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="contactor_page" value="N" checked disabled>
                            不接入
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="contactor_page" value="Y" <?php if('Y' == $info['contactor_page']):?>checked<?php endif;?> disabled>
                            接入
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">是否生成报告</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="need_report" value="0" checked disabled>
                            否
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="need_report" value="1" <?php if(1 == $info['need_report']):?>checked<?php endif;?> disabled>
                            是
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否输出催收分参数</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="need_dunning" value="2" class="cuishou_radio" <?= (!isset($info['need_dunning']) || (1 != $info['need_dunning'])) ? 'checked' : '' ?> disabled>
                            否
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="need_dunning" value="1" class="cuishou_radio" <?= (1 == $info['need_dunning']) ? 'checked' : '' ?> disabled>
                            是
                        </label>
                    </div>
                </div>
                <div class="form-group cuishou" style="display:<?= (isset($info['need_dunning']) && (1 == $info['need_dunning'])) ? 'block' : 'none' ?>">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">催收分APIKEY</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" name="service_key" value="{$cuishou_info['service_key']}" readonly>
                    </div>
                </div>
                <div class="form-group cuishou" style="display:<?= (isset($info['need_dunning']) && (1 == $info['need_dunning'])) ? 'block' : 'none' ?>">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">催收分PASSWORD</label>
                    <div class="col-sm-4">
                        <input type="text" name="service_secret" class="form-control" value="{$cuishou_info['service_secret']}" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">APPID</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="appid" id="input_appid" value="{$info['appid']}" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">APPSECRET</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="appsecret" id="input_appsecret" value="{$info['appsecret']}" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">推送地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="notify_url" value="{$info['notify_url']}" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">重定向地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="close_redirect_url" value="{$info['close_redirect_url']}" readonly>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </form>
    </div>

    <div class="tab-pane fade" id="bmmatching">
        <form class="form-horizontal" method="post" role="form" id="bmMatchingForm">

            <div class="panel-body">

                <div class="form-group">
                    <label class="col-sm-2 control-label">客户名称</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" name="owner" value="{$lists['owner']}" readonly>
                    </div>

                    <label class="col-sm-2 control-label">账号状态</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="active" disabled>
                            <?php foreach ($statuslists as $key => $value): ?>
                            <option value="{$key}" <?php echo $key == $lists['active'] ? 'selected="selected"' : ''?>>{$value}</option>
                            <?php endforeach ?>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">APIKEY</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="apikey" id="input_apikey" value="{$lists['apikey']}" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">PASSWORD</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="sign"  value="{$lists['password']}" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">截止日期</label>
                    <if condition="!empty($lists['validuntil'])">
                        <div class="col-sm-4">
                            <input type="date" class="form-control" name="validuntil" value="{:date('Y-m-d',strtotime($lists['validuntil']))}" readonly>
                        </div>
                        <else/>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="validuntil" value="" readonly>
                        </div>
                    </if>
                </div>

                <?php foreach ($fieldlist as $key => $value): ?>
                <?php if($value['name'] == 'output_yscs' || $value['name'] == 'output_cs'): ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="1" checked disabled>是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="{$value['name']}" value="2"<?php if($ctrlinfo[$value['name']]['value']==2):?>checked<?php endif;?> disabled>否
                        </label>
                    </div>
                </div>
                <?php elseif($value['name'] == 'bind_domain' || $value['name'] == 'limit_access_ip'): ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-10">
                        <textarea type="text" class="form-control" name="{$value['name']}" disabled>{$ctrlinfo[$value['name']]['value']}</textarea>
                    </div>
                </div>
                <?php else: ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">{$value['remark']}</label>
                    <div class="col-sm-10">
                        <if condition="$ctrlinfo[$value['name']]['value'] == '-1'">
                            <input type="text" class="form-control" name="{$value['name']}" value="" placeholder="无限制" readonly/>
                            <else/>
                            <input type="text" class="form-control" name="{$value['name']}" value="{$ctrlinfo[$value['name']]['value']}" readonly/>
                        </if>
                    </div>
                </div>
                <?php endif ?>
                <?php endforeach ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label">接口输出字段</label>
                    <div class="col-sm-10 checkbox">
                        <?php foreach ($out_fields as $field) { ?>
                        <span>
                                <label class="checkbox-inline"><input type="checkbox" name="out_fields[]" value="<?= $field['id'] ?>" <?= isset($choose_fields[$field['id']]) ? 'checked' : '' ?>><?= $field['remark'] ?></label>
                            </span>
                        <?php } ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">签名字符串</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="sign"  value="<?= $lists['sig_str']; ?>" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">接口调用示例</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="sign"  value="<?= 'https://itag.dianhua.cn/itag/?apikey='.$lists['apikey'].'&app=myApp&app_ver=1.0&country=86&version=1&tel=10086&uid=NMKJ&sig=' . $lists['sig'] ?>" readonly>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>

        </form>
    </div>
</div>
