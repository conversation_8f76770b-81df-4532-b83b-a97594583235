<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>渠道日志统计</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<style>
    .layui-table-tool{
        display: none;
    }
</style>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form" method="POST">
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:200px">
            <label class="layui-form-label">开始时间</label>
            <div class="layui-input-block">
                <input type="text" name="date_start" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="date_start" value="{:date('Y-m-d')}">
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="width:200px">
            <label class="layui-form-label">结束时间</label>
            <div class="layui-input-block">
                <input type="text" name="date_end" placeholder="请选择结束时间" autocomplete="off" class="layui-input" id="date_end" value="{:date('Y-m-d')}">
            </div>
        </div>


        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>
    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="list_table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__bignumber.js-master/bignumber.min.js"></script>
<script type="application/javascript" src="__STATICS__jquery-dateFormat-master/dist/jquery-dateformat.min.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    //var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bill/income/customerStatementList";
    var url = "<?php echo $url?>";
    layui.laydate.render({
        elem  : '#date_start',
        type  : 'date',
    });
    layui.laydate.render({
        elem  : '#date_end',
        type  : 'date',
    });
    window.cache = {
        list_url         : url,
        list_order_field : "total_num",
        list_order_type  : "desc",
        list_where       : {
            month_section : "{:date('Y-m', strtotime('-1 months'))} - {:date('Y-m', strtotime('-1 months'))}"
        },
        list_title       : `渠道日志统计_${Common.datetime('yyyy-MM-dd')}`,
        list_form        : true,
        list_fields      : [[
            {
                field    : 'channel_name',
                title    : '渠道名称',
                minWidth : 100,
                align    : 'left',
            },
            {
                field : 'total_num',
                title : '总量',
                minWidth : 50,
                align : 'left',
            },
            {
                field    : 'success_num',
                title    : '成功量',
                align    : 'left',
                minWidth : 50
            },
            {
                field    : 'kekao_num',
                title    : '可靠性',
                align    : 'left',
                minWidth : 50
            },
            {
                field    : 'avg_time',
                title    : '平均响应时间',
                align    : 'left',
                minWidth : 50
            },
            {
                field    : 'avg_time_pre',
                title    : '上个周期平均响应时间',
                align    : 'left',
                minWidth : 200
            },
            {
                field    : 'pass_1s_num',
                title    : '超一秒个数',
                align    : 'left',
                minWidth : 50
            },
            {
                field    : 'mix_1s_num',
                title    : '超一秒占比',
                align    : 'left',
                minWidth : 50
            }
        ]]
    };

    layui.laydate.render({
        elem  : '#month_section',
        type  : 'month',
        range : true,
        value : window.cache.list_where.month_section
    });

    //加载表格
    Table.reloadTable({
        where          : window.cache.list_where,
        page           : false,
        limit          : 10000,
        defaultToolbar : ['filter', 'print', 'exports'],
        toolbar        : true,
    });
</script>