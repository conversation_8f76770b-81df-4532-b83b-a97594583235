<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('/Home/InterfacePrice/bmyNewConfig')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="start_time">计费日期：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>

                <div class="form-group">
                    <label class="control-label">渠道名称</label>
                        <select name="channelId" id="channel_search">
                            <option value="">请选择</option>
                            <?php
                                    foreach($channels as $value){
                                ?>
                            <option value="<?php echo $value['id'] ?>"><?php echo $value['label'];?></option>
                            <?php } ?>
                        </select>
                </div>

                <div class="form-group">
                    <label class="control-label">接口名称</label>
                        <select name="interfaceId" id="interface_search">
                        </select>
                </div>

                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group">
                    <button type="button" id="add_price" class="btn btn-success btn-sm">添加配置</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">渠道名称</th>
                <th style="text-align:center;">接口名称</th>
                <th style="text-align:center;" class="order_field" data-info="yd">移动</th>
                <th style="text-align:center;" class="order_field" data-info="liantong">联通</th>
                <th style="text-align:center;" class="order_field" data-info="dx">电信</th>
                <th style="text-align:center;">计费日期</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody id="interfacesPriceList">
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="priceConfigWindow">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">接口计费配置</h4>
            </div>

            <div class="modal-body" id="Dialogue_form_add">
                <form class="form-horizontal" action="#" method="post" id="DialogueFormAdd">

                    <div class="form-group">
                        <label class="col-sm-2 control-label">渠道名称</label>
                        <div class="col-sm-4">
                            <select name="product_name" id="channel_name_select">
                                <option value="">请选择</option>
                                <?php
                                    foreach($channels as $value){
                                ?>
                                <option value="<?php echo $value['id'] ?>"><?php echo $value['label'];?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">接口名称</label>
                        <div class="col-sm-4">
                            <select name="channel_name" id="interface_name_select">
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">计费日期</label>
                        <div class="col-sm-4">
                            <input type="date" name="start_date" id="date" class="form-control" value=""/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">计费模式</label>
                        <div class="col-sm-4">
                            <select id="price_model">
                                <option value="1">查得计费</option>
                                <option value="2">查询计费</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">计费配置</label>
                        <div class="col-sm-4">
                            <a type="button" class="btn btn-primary" id="add_price_item" >+添加</a>
                            <a type="button" class="btn btn-danger" id="del_price_item" >-删除</a>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label"></label>
                        <table class="table table-hover table-bordered" style="width: 500px">
                            <thead>
                                <tr>
                                    <th>运营商</th><th>加密方式</th><th>价格(分)</th>
                                </tr>
                            </thead>
                            <tbody id="price_tbody">
                            </tbody>

                        </table>
                    </div>

                    <input type="hidden" name="bot_id" id="bot_id_add" value="">
                    <input type="hidden" name="goal_type" id="goal_type_add" value="2">
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button" class="btn btn-primary btn-sm btn-submit add_submit_dialogue" id="add_price_submit" >添加</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>


<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var father_id = 200;
    var interfaceWithChannel;
    var interfacePriceConfigId = 0;
    var interfacePriceAddOrEdit = true;
    getInterfaceWithChannel();
    getInterfacePriceList();

    var priceTr = '<tr>\n' +
        '<td>\n' +
        '<select name="operator" style="width: 80px;">\n' +
        '<option value="ALL">不区分</option>\n' +
        '<option value="CMCC">移动</option>\n' +
        '<option value="CUCC">联通</option>\n' +
        '<option value="CTCC">电信</option>\n' +
        '</select>\n' +
        '</td>\n' +
        '<td>\n' +
        '<select name="encrypt_way" style="width: 80px;">\n' +
        '<option value="ALL">不区分</option>\n' +
        '<option value="CLEAR">明文</option>\n' +
        '<option value="MD5">MD5</option>\n' +
        '<option value="SHA256">SHA256</option>\n' +
        '</select>\n' +
        '</td>\n' +
        '<td>\n' +
        '<input type="number" name="price" value="" />\n' +
        '</td>\n' +
        '</tr>';

    $("#channel").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择渠道',
        width: '200px'
    });
    $("#product_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择产品',
        width: '200px'
    });
    $("#channel_name").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '请选择',
        width: '200px'
    });
    $("#channel_name_edit").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '请选择',
        width: '200px'
    });
    $("#product_name").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '请选择',
        width: '200px'
    });
    $("#product_name_edit").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '请选择',
        width: '200px'
    });

    $(document).ready(function () {
        $("#add_price").click(function () {
            interfacePriceAddOrEdit = true;
            $("#priceConfigWindow").modal('show');
            $('#channel_name_select').prop("disabled", false);
            $('#interface_name_select').prop("disabled", false);
            $("#price_tbody").html("");
            $("#price_tbody").append(priceTr);
        });

        $('#interfacesPriceList').on('click','.edit_price',function(){
            interfacePriceAddOrEdit = false;
            setPriceConfig($(this).attr('data-id'));
            $("#priceConfigWindow").modal('show');
        });

        $("#add_price_item").click(function () {
            $("#price_tbody").append(priceTr);
        });

        $("#del_price_item").click(function () {
            $("#price_tbody tr:last").remove();
        });

        $("#list_submit").click(function () {
            console.log("rwerwerw");
            getInterfacePriceList();
        });

        $("#channel_search").change(function () {
            interfaceSelectShow("interface_search", $(this).val());

        });

        $("#channel_name_select").change(function () {
            interfaceSelectShow("interface_name_select", $(this).val());
        });

        //提交 计费配置
        $("#add_price_submit").click(function () {
            if( interfacePriceAddOrEdit ){
                addInterfacePrice();
            }else{
                editInterfacePrice();
            }
        });
    });

    function getPriceConfigFroTbody()
    {
        var price_config = [];
        var price_model = $("#price_model").val();
        var trs = $("#price_tbody tr");
        trs.each(function () {
            let operator = $(this).find('td').eq(0).find('select').val();
            let encrypt_way = $(this).find('td').eq(1).find('select').val();
            let price = $(this).find('td').eq(2).find('input').val();
            price = price == '' ? 0 : price;

            price_config.push({operator, encrypt_way, price, price_model});
        });

        return price_config;
    }

    function checkConfig()
    {
        var channel_id = $('#channel_name_select').val();
        if( channel_id == '' ){
            alert('渠道不能为空')
            return false;
        }
        var interface_id = $('#interface_name_select').val();
        if( interface_id == '' ){
            alert('接口不能为空');
            return false;
        }

        var date = $('#date').val();
        if( date == '' ){
            alert('生效日期不能为空');
            return false;
        }

        return true;
    }

    function addInterfacePrice()
    {
        let checkResult = checkConfig();
        if( !checkResult ){
            return false;
        }

        var price_model = $("#price_model").val();

        $.ajax({
            type: 'post',
            url: "{:U('/Home/InterfacePrice/addInterfacePrice')}",
            data: {
                channel_id: $('#channel_name_select').val(),
                interface_id: $('#interface_name_select').val(),
                date: $('#date').val(),
                price_model: price_model,
                price_config: getPriceConfigFroTbody(),
                father_id: father_id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    alert(data.msg);
                    $('#priceConfigWindow').modal('hide');
                    window.location.reload();
                }else{
                    alert(data.msg);
                }
            }
        });
    }

    function editInterfacePrice()
    {
        let checkResult = checkConfig();
        if( !checkResult ){
            return false;
        }

        $.ajax({
            type: 'post',
            url: "{:U('/Home/InterfacePrice/editInterfacePrice')}",
            data: {
                id: interfacePriceConfigId,
                interface_id: $('#interface_name_select').val(),
                date: $('#date').val(),
                price_model: $("#price_model").val(),
                price_config: getPriceConfigFroTbody(),
            },
            success: function(data) {
                if(data.status == 'ok'){
                    $('#priceConfigWindow').modal('hide');
                    getInterfacePriceList();
                }else{
                    alert(data.msg);
                }
            }
        });
    }

    function getInterfaceWithChannel()
    {
        $.ajax({
            type: 'post',
            url: "{:U('/Home/InterfacePrice/getInterfaceWithChannel')}",
            data: {
                father_id: father_id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    interfaceWithChannel = data.data;
                }
            }
        });

    }

    function getInterfacePriceList()
    {
        $.ajax({
            type: 'post',
            url: "{:U('/Home/InterfacePrice/getInterfacePriceList')}",
            data: {
                father_id: father_id,
                channel_id: $("#channel_search").val(),
                interface_id: $("#interface_search").val(),
            },
            success: function(data) {
                $("#interfacesPriceList").html("");
                if(data.status == 'ok'){
                    let priceData = data.data;
                    for(i = 0, len = priceData.length; i < len; i++) {
                        let tr = '<tr style="">' +
                            '<td align="center">'+ priceData[i].clabel +'</td>' +
                            '<td align="center">'+ priceData[i].ilabel +'</td>' +
                            '<td align="center">'+ priceData[i].CMCC +'</td>' +
                            '<td align="center">'+ priceData[i].CUCC +'</td>' +
                            '<td align="center">'+ priceData[i].CTCC +'</td>' +
                            '<td align="center">'+ priceData[i].date +'</td>' +
                            '<td align="center"><span data-id="'+ priceData[i].id +'" role="button" class="btn btn-info btn-sm edit_price">编辑</span></td>' +
                            '</tr>';
                        $("#interfacesPriceList").append(tr);
                    }
                }
            }
        });

    }

    //回显价格配置
    function setPriceConfig(id)
    {
        interfacePriceConfigId = id;

        $.ajax({
            type: 'post',
            url: "{:U('/Home/InterfacePrice/getPriceConfigById')}",
            data: {
                id: id,
            },
            success: function(data) {
                if(data.status == 'ok'){
                    $('#channel_name_select').val(data.data.cid);
                    interfaceSelectShow("interface_name_select", data.data.cid);
                    $('#interface_name_select').val(data.data.iid);
                    $('#date').val(data.data.date);

                    $("#price_tbody").html("");
                    let price = JSON.parse(data.data.price);
                    for(i = 0, len = price.length; i < len; i++) {

                        $("#price_tbody").append(priceTr);
                        $("#price_tbody tr:last").find("select[name='operator']").val(price[i].operator);
                        $("#price_tbody tr:last").find("select[name='encrypt_way']").val(price[i].encrypt_way);
                        $("#price_tbody tr:last").find("input[name='price']").val(price[i].price);
                    }

                    //设置 select 只读
                    $('#channel_name_select').prop("disabled", true);
                    $('#interface_name_select').prop("disabled", true);

                }else{
                    alert(data.msg);
                }
            }
        });
    }

    function interfaceSelectShow(selectId, channelId)
    {
        $("#"+selectId).html("");
        var interfaces = interfaceWithChannel[ channelId ];
        for(i = 0, len = interfaces.length; i < len; i++) {
            let option = '<option value="'+ interfaces[i].id +'">'+ interfaces[i].label +'</option>';
            $("#"+selectId).append(option);
        }
    }

</script>
</body>
</html>
