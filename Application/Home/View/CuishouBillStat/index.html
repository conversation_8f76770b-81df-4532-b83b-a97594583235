<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__dataTables.css">
    <script src="__JS__jquery.dataTables.js"></script>
    <script src="__JS__jquery.dataTables.bootstrap.js"></script>
    <style>
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <dialog_template></dialog_template>
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="form_init">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="start_time">时间区间：</label>
                        <input type="date" name="start_time" id="start_time" class="form-control"
                               value="<?= (!isset($input['start_time']) || !$input['start_time']) ? date('Y-m-d') : $input['start_time']; ?>"/>
                        -
                        <input type="date" name="end_time" id="end_time" class="form-control"
                               value="<?= (!isset($input['end_time']) || !$input['end_time']) ? date('Y-m-d') : $input['end_time']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label for="customer_id">选择客户</label>
                        <select name="customer_id" id="customer_id">
                            <?= (!isset($input['customer_id']) || !$input['customer_id']) ? '' : '<option value="' . $input['customer_id'] . '">' . $customer[$input['customer_id']]['name'] . '</option>'?>
                            <option value="">选择客户</option>
                            <volist name="customer" id="vo">
                                <option value="{$vo.customer_id}">{$vo.name}</option>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="contract_status">签约状态：</label>
                        <select class="form-control" name="contract_status" id="contract_status">
                            <option value="" selected>查询全部</option>
                            <option value="1"
                            <?= ($input['contract_status'] == '1') ? 'selected' : '' ;?>>已签约已付款</option>
                            <option value="2"
                            <?= ($input['contract_status'] == '2') ? 'selected' : '' ;?>>已签约未付款</option>
                            <option value="3"
                            <?= ($input['contract_status'] == '3') ? 'selected' : '' ;?>>未签约</option>
                            <option value="4"
                            <?= ($input['contract_status'] == '4') ? 'selected' : '' ;?>>其他</option>
                            <option value="5"
                            <?= ($input['contract_status'] == '5') ? 'selected' : '' ;?>>特殊客户</option>
                        </select>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li>
                                <input id="searchBtn" type="button" class="btn btn-primary btn-sm" value="查询">
                            </li>
                            <li>
                                <button type="button" id="file_customer" class="btn btn-success btn-sm">导出（客户）</button>
                            <li>
                                <button type="button" id="file_account" class="btn btn-success btn-sm">导出（账号）</button>
                            </li>
                            <li>
                                <button type="button" id="file_day" class="btn btn-success btn-sm">导出（按天）</button>
                            </li>
                        </ul>
                    </div>

                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr>
                <th style="text-align: center;">客户ID</th>
                <th style="text-align: center;">客户名称</th>
                <th style="text-align: center;">账号名称</th>
                <th class="order_field" style="text-align: center;" data-info="all">
                    总调用量
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
                <th class="order_field" style="text-align: center;" data-info="succ">
                    有效调用量
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
                <th class="order_field" style="text-align: center;" data-info="succ">
                    有效调用率
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
                <th class="order_field" style="text-align: center;" data-info="sure">
                    催收覆盖率
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
                <th class="order_field" style="text-align: center;" data-info="notsure">
                    疑似催收覆盖率
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
                <th class="order_field" style="text-align: center;" data-info="both">
                    同时覆盖率
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
                <th class="order_field" style="text-align: center;" data-info="nall">
                    整体覆盖率
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                </th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td align="center">{$vo.customer_id}</td>
                    <td align="center">
                        <a href="{:U('detail', ['customer_id' => $vo['customer_id']])}">
                            {$vo.name}
                        </a>
                    </td>
                    <td align="center">
                        <eq name="key" value="0">
                            总计
                            <else />
                            <volist name="vo.account" id="vv">
                                <neq name="key" value="0">
                                    <span style="margin:0 8px;">|</span>
                                </neq><a href="{:U('detail')}?account_id={$vv.account_id}&customer_id={$vo.customer_id}">{$vv.account_name}</a>
                            </volist>
                        </eq>

                    </td>
                    <td align="center">{$vo.all}</td>
                    <td align="center">{$vo.succ}</td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['succ']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['sure']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['notsure']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['both']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['nall']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        // 客户select2
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            width: '150',
            placeholder: '选择客户'
        });
        iniOrder();
        $(".order_field").click(function () {
            order($(this).attr('data-info'))
        });
        $("#file_customer").click(function () {
            if (confirm('导出的数据将以客户为主题，并只导出符合当前应用的查询条件客户')) {
                location.href = "{:U('file')}?type=customer&" + location.search.substr(1);
            }
        });
        $("#file_account").click(function () {
            if (confirm('导出的数据将以账号为主题，并只导出符合当前应用的查询条件账号')) {
                location.href = "{:U('file')}?type=account&" + location.search.substr(1);
            }
        });
        $("#file_day").click(function () {
            alert('暂时不支持按天导出数据');
            return false;
            if (confirm('导出的数据将以账号与时间为主题，并只导出符合当前应用的查询条件账号')) {
                location.href = "{:U('file')}?type=day&" + location.search.substr(1);
            }
        });
        $("#searchBtn").click(function () {
            let start_time = $("#start_time").val();
            let end_time = $("#end_time").val();
            let start_date = Date.parse(start_time);
            let end_date = Date.parse(end_time);
            let cha = Math.abs(start_date - end_date);
            cha = Math.floor(cha / (24 * 3600 * 1000));
            if (cha>365) {
                alert('查询范围需要小于365天！！！');
                return false;
            }
            $("#form_init").submit();
        });
    });
    function iniOrder()
    {
        let params = GetRequest();
        let field = params.order;
        console.log(field);
        if (field) {
            let asc = params.asc;
            if (asc==1) {
                $('.order_field[data-info="' + field + '"]').find('.v-icon-up-dir').addClass('checked');
            } else {
                $('.order_field[data-info="' + field + '"]').find('.v-icon-down-dir').addClass('checked');
            }
        }
    }
    function order(field)
    {
        let params = GetRequest();
        if (params.asc==1) {
            params.asc = 0;
        } else {
            params.asc = 1;
        }
        params.order = field;
        let href = "{:U('index')}?";
        $.each(params, function(i, n) {
            href += (i + '=' + n + '&');
        });
        location.href = href.substr(0, href.length-1);
    }
    function GetRequest() {
        var url = location.search;
        var theRequest = new Object();
        if (url.indexOf("?") != -1) {
            var str = url.substr(1);
            strs = str.split("&");
            for(var i = 0; i < strs.length; i ++) {
                theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
            }
        }
        return theRequest;
    }
</script>
</body>
</html>
