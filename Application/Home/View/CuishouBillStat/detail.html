<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <script src="__JS__highcharts.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__dataTables.css">
    <script src="__JS__jquery.dataTables.js"></script>
    <script src="__JS__jquery.dataTables.bootstrap.js"></script>
    <style>
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <dialog_template></dialog_template>
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('detail')}" class="form-inline" method="get" id="form_init">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="start_time">开始时间：</label>
                        <input type="date" name="start_time" id="start_time" class="form-control"
                               value="<?= (!isset($input['start_time']) || !$input['start_time']) ? date('Y-m-d', strtotime('-1 month')) : $input['start_time']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="end_time">结束时间：</label>
                        <input type="date" name="end_time" id="end_time" class="form-control"
                               value="<?= (!isset($input['end_time']) || !$input['end_time']) ? date('Y-m-d') : $input['end_time']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label for="customer_id">选择客户</label>
                        <select name="customer_id" id="customer_id">
                            <?= (!isset($input['customer_id']) || !$input['customer_id']) ? '' : '<option value="' . $input['customer_id'] . '">' . $customer[$input['customer_id']]['name'] . '</option>'?>
                            <option value="">选择客户</option>
                            <volist name="customer" id="vo">
                                <option value="{$vo.customer_id}">{$vo.name}</option>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="account_id">选择账号</label>
                        <select name="account_id" id="account_id">
                            <?= (!isset($input['account_id']) || !$input['account_id']) ? '' : '<option value="' . $input['account_id'] . '">' . $account[$input['account_id']]['account_name'] . '</option>'?>
                            <option value="">选择账号</option>
                            <volist name="account" id="vo">
                                <option value="{$vo.account_id}">{$vo.account_name}</option>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input id="searchBtn" type="button" class="btn btn-primary btn-sm" value="查询"></li>
                            <li>
                                <button type="button" id="file" class="btn btn-success btn-sm">导出</button>
                            </li>
                            <!--<li><a href="javascript:void(0);" target="_self" onclick="return coverageRequest()"
                                   class="btn btn-info btn-sm">覆盖率走势</a></li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出（列表）</button>
                            </li>
                            <li>
                                <button type="button" id="file_export_day" class="btn btn-success btn-sm">导出（按天）</button>
                            </li>-->
                        </ul>
                    </div>

                </div>
            </form>
        </div>
    </div>
</div>
<div class="container" style="text-align: center;width: 100%;margin-bottom: 10px">
    <div id="coverage" style="min-width:80%;height:400px; "></div>
    <div class="message"></div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div style="width:100%;height:30px;background:#ccc;color:#333;font-size:14px;line-height:30px;text-indent: 10px;font-weight:bold;">
        {$title}
        </div>
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr>
                <th style="text-align: center;">时间</th>
                <th style="text-align: center;">总调用量</th>
                <th style="text-align: center;">有效调用量</th>
                <th style="text-align: center;">有效调用率</th>
                <th style="text-align: center;">催收覆盖率</th>
                <th style="text-align: center;">疑似催收覆盖率</th>
                <th style="text-align: center;">同时覆盖率</th>
                <th style="text-align: center;">整体覆盖率</th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td align="center">
                        <eq name="key" value="0">
                            总计
                            <else />
                            {$vo.amount_date}
                        </eq>

                    </td>
                    <td align="center">{$vo.all}</td>
                    <td align="center">{$vo.succ}</td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['succ']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['sure']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['notsure']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['both']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                    <td align="center">
                        <empty name="vo.all">
                            NA
                            <else />
                            <?php echo round(($vo['nall']/$vo['all']*100), 2); ?>%
                        </empty>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        // 客户select2
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            width: '180',
            placeholder: '选择客户'
        });
        $("#account_id").select2({
            allowClear: true,
            theme: "bootstrap",
            width: '180',
            placeholder: '选择账号'
        });
        $("#file").click(function () {
            if (confirm('是否导出所查询到的数据？')) {
                location.href = "{:U('file')}?type=detail&" + location.search.substr(1);
            }
        });
        $("#customer_id").change(function () {
            let customer_id = $(this).val();
            $.ajax({
                url : "{:U('detail')}",
                data : {
                    customer_id : customer_id
                },
                type : 'post',
                success : function (res) {
                    let display = '<option value="">选择账号</option>';
                    $.each(res, function (i, n) {
                        display += '<option value="' + n.account_id + '">' + n.account_name + '</option>';
                    });
                    $("#account_id").html(display);
                    $("#account_id").select2({
                        allowClear: true,
                        theme: "bootstrap",
                        width: '180',
                        placeholder: '选择账号'
                    });
                }
            });
        });
        genLineChart();
        $("#searchBtn").click(function () {
            let start_time = $("#start_time").val();
            let end_time = $("#end_time").val();
            let start_date = Date.parse(start_time);
            let end_date = Date.parse(end_time);
            let cha = Math.abs(start_date - end_date);
            cha = Math.floor(cha / (24 * 3600 * 1000));
            if (cha>365) {
                alert('查询范围需要小于365天！！！');
                return false;
            }
            $("#form_init").submit();
        });
    });
    // 生成折线图
    function genLineChart() {
        var xAxis_categories = {$chart.x|json_encode};
        var series = {$chart.y|json_encode};

        // 图表配置
        var options = {
            credits: {
                enabled: false
            },
            chart: {
                type: 'line'                          //指定图表的类型，默认是折线图（line）
            },
            title: {
                text: null                 // 标题
            },
            tooltip: {
                backgroundColor: '#FCFFC5',   // 背景颜色
                borderColor: 'black',         // 边框颜色
                borderRadius: 10,             // 边框圆角
                borderWidth: 1,               // 边框宽度
                shadow: true,                 // 是否显示阴影
                animation: true,             // 是否启用动画效果
                shared:true,                   // 共享提示框
                valueSuffix:'%',                   // 后缀
                style: {                      // 文字内容相关样式
                    color: "#ff0000",
                    fontSize: "12px",
                    fontWeight: "blod",
                    fontFamily: "Courir new"
                }
            },
            xAxis: {
                categories: xAxis_categories   // x 轴分类
            },
            yAxis: {
                title: {
                    text: '覆盖率(100%)'                // y 轴标题
                },
                min: 0,
                max: 100
            },
            series: series
        };
        // 图表初始化函数
        var chart = Highcharts.chart('coverage', options);
    }
</script>
</body>
</html>
