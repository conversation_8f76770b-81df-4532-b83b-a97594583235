<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row_first {
            margin: 10px;
        }
        .container label {
            margin-left: 5px;
        }
        .row_second div {
            margin-left: 10px;
        }
        .button_group {
            margin-left: 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="/Home/TelStatusStat/detail" method="get" id="form_id">
                <div class="form-group">
                    <label  class="control-label">开始时间</label>
                    <input type="date" name="begin" id="time_begin" class="form-control" value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d', strtotime('-1 days')) : $input['begin']; ?>"/>
                </div>
                <div class="form-group">
                    <label  class="control-label">结束时间</label>
                    <input type="date" name="end" id="time_end" class="form-control" value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                </div>
                <div class="form-group">
                    <label for="customer_id">选择客户</label>
                    <select class="form-control" name="customer_id" id="customer_id">
                        <option value="">选择客户</option>
                        <?php foreach ($customer_list as $value) { ?>
                        <option value="<?= $value['customer_id']; ?>" <?= ($value['customer_id'] == $input['customer_id']) ? 'selected' : '' ?>> <?= $value['name'] ?></option>
                        <?php  } ?>
                    </select>
                </div>
                <div class="form-group">
                    <select class="form-control" name="account_id" id="account_id">
                        <option value="0" selected>选择账号</option>
                        <?php foreach($customer_list[$input['customer_id']]['account_list'] as $key => $value) { ?>
                        <option value="<?= $value['account_id'] ?>" <?= ($input['account_id'] == $value['account_id']) ? 'selected' : '' ;?>><?= $value['account_name'] ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group button_group">
                    <input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询">
                </div>
                <!-- <div class="form-group button_group">
                    <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                </div> -->
                <div class="form-group button_group">
                    <a href="{:U('index')}" class="btn btn-info btn-sm" >返回列表</a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <tr align="center">
                <th width="100px">时间</th>
                <th>总查号码量</th>
                <th>错误号码量</th>
                <th>正确号码量</th>
                <th>查得号码量</th>
                <th>号码在用量</th>
                <th>号码停用量</th>
                <th>号码无法识别量</th>
                <th>号码为在用状态占正确号码的比例</th>
                <th>号码为停用状态占正确号码的比例</th>
                <th>号码为无法识别状态占正确号码的比例</th>
            </tr>
            <tr>
                <td style="text-align: center">总计</td>
                <td><?= isset($total['total']) ? $total['total'] : 'NA' ?></td>
                <td><?= isset($total['errorPhoneNum']) ? $total['errorPhoneNum'] : 'NA' ?></td>
                <td><?= isset($total['correctPhoneNum']) ? $total['correctPhoneNum'] : 'NA' ?></td>
                <td><?= isset($total['getPhoneNum']) ? $total['getPhoneNum'] : 'NA' ?></td>
                <td><?= isset($total['usePhoneNum']) ? $total['usePhoneNum'] : 'NA' ?></td>
                <td><?= isset($total['stopPhoneNum']) ? $total['stopPhoneNum'] : 'NA' ?></td>
                <td><?= isset($total['noKnowPhoneNum']) ? $total['noKnowPhoneNum'] : 'NA' ?></td>
                <td><?= isset($total['usePhoneRatio']) ? $total['usePhoneRatio'] : 'NA' ?></td>
                <td><?= isset($total['stopPhoneRatio']) ? $total['stopPhoneRatio'] : 'NA' ?></td>
                <td><?= isset($total['noKnowPhoneRatio']) ? $total['noKnowPhoneRatio'] : 'NA' ?></td>
            </tr>
            <?php foreach ($list as $key => $value) { ?>
            <tr>
                <td>{$key}</td>
                <td><?= isset($value['total']) ? $value['total'] : 'NA' ?></td>
                <td><?= isset($value['errorPhoneNum']) ? $value['errorPhoneNum'] : 'NA' ?></td>
                <td><?= isset($value['correctPhoneNum']) ? $value['correctPhoneNum'] : 'NA' ?></td>
                <td><?= isset($value['getPhoneNum']) ? $value['getPhoneNum'] : 'NA' ?></td>
                <td><?= isset($value['usePhoneNum']) ? $value['usePhoneNum'] : 'NA' ?></td>
                <td><?= isset($value['stopPhoneNum']) ? $value['stopPhoneNum'] : 'NA' ?></td>
                <td><?= isset($value['noKnowPhoneNum']) ? $value['noKnowPhoneNum'] : 'NA' ?></td>
                <td><?= isset($value['usePhoneRatio']) ? $value['usePhoneRatio'] : 'NA' ?></td>
                <td><?= isset($value['stopPhoneRatio']) ? $value['stopPhoneRatio'] : 'NA' ?></td>
                <td><?= isset($value['noKnowPhoneRatio']) ? $value['noKnowPhoneRatio'] : 'NA' ?></td>
            </tr>
            <?php } ?>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>

</div>
</div>
<script type="text/javascript">
$(function() {
    // choose user
    $("#customer_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择账号'
    });

    // export file
    $("#file_export").click(function () {
        if (!checkTime()){
            return false;
        }
        var params = genParamsForFile();
        var url_export = '/Home/TelStatusStat/downloadList' + params;
        $.fileDownload(url_export);
        return false;
    });
});

function genParamsForFile()
{
    // init params
    var params = '';
    var time_begin = $('#time_begin').val();
    var time_end = $('#time_end').val();
    var account_id = $('#account_id').val();
    var customer_id = $('#customer_id').val();

    if (time_begin) {
        params += '&begin='+time_begin;
    }
    if (time_end) {
        params += '&end=' + time_end;
    }
    if (account_id) {
        params += '&account_id=' + account_id;
    }
    if (customer_id) {
        params += '&customer_id='+customer_id;
    }

    // tidy url
    if (params) {
        params = params.replace('&', '?');
    }
    return params;
}

function checkTime() {
    // get begin time and end time
    var time_begin = $('#time_begin').val();
    var time_end = $('#time_end').val();
    var today_str = (new Date()).toDateString();

    // change time format for firefox
    time_end = time_end.replace(/\-/g, '\/');
    time_begin = time_begin.replace(/\-/g, '\/');

    // check begin time
    if (!time_begin && time_end) {
        alert('请选择开始时间');
        return false;
    }

    // check end time
    if (time_begin && !time_end) {
        alert('请选择结束时间');
        return false;
    }

    if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
        alert('请选择有效的结束时间');
        return false;
    }

    // set default time
    if (!time_begin) {
        time_begin = today_str;
    }

    if (!time_end) {
        time_end = today_str;
    }

    // check time
    // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
    var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
    if (time_diff < 0) {
        alert('开始时间必须小于结束时间');
        return false;
    }

    // calculate the days between begin and end
    var day_diff =  Math.floor(time_diff/8.64e7);

    //  the time should less than 31
    if (day_diff <= 365) {
        return true;
    } else {
        alert('单次查询时间范围不能超过365天');
        return false;
    }
}

</script>
</body>
</html>
