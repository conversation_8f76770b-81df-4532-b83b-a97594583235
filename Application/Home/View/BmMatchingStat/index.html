<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }

        .row_first {
            margin-bottom: 20px;
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<div class="container" id="list_bm_matching">
    <list_bang_template :init_params='<?= $params_init ?>'></list_bang_template>
    <dialog_template></dialog_template>
</div>

<!-- 查看详情的入口组件 -->
<script type="text/x-template" id="custom_show">
        <a href="" @click.stop.prevent="showDetail(rowData,index)" v-if="rowData.id">{{ rowData.owner}}</a>
        <a href="" @click.stop.prevent="statTotal(rowData,index)" v-else>{{ rowData.owner}}</a>
</script>
<script type="text/x-template" id="show_bang">
    <div>
        <div class="row">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span style="font-weight: bold">邦秒配统计列表</span>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.prevent="requestList">
                        <div class="row_first">
                            <div class="form-group">
                                <label for="begin">开始时间：</label>
                                <input type="date" id="begin" v-model="begin" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="end">结束时间：</label>
                                <input type="date" v-model="end" id="end" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="contract_status">签约状态：</label>
                                <select id="contract_status" v-model="contract_status" class="form-control">
                                    <option :value="item.value" v-for="item in list_contract_status">{{ item.name }}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status">账号状态：</label>
                                <select id="status" v-model="active" class="form-control">
                                    <option value="">账号状态</option>
                                    <option value="1">正常</option>
                                    <option value="2">禁用</option>
                                </select>
                            </div>
                            <div class="form-group pull-right">
                                <button type="submit" class="btn btn-sm btn-primary"><i class="icon-search"></i> 查询</button>
                                <button class="btn btn-info btn-sm" :disabled="disabled_list" @click.prevent="downloadFile('list')"> <i class="icon-download-alt"></i> 导出(列表)</button>
                                <button class="btn btn-info btn-sm" :disabled="disabled_day" @click.prevent="downloadFile('day')"> <i class="icon-download-alt"></i> 导出(按天)</button>
                            </div>
                        </div>
                        <div class="row-second">
                            <div class="form-group select2-style-adjust">
                                <ul class="list-inline">
                                    <li><label class="control-label">选择客户:</label></li>
                                    <li><v-select :options="list_account_options" v-model="account"></v-select></li>
                                </ul>
                            </div>
                            <div class="form-group select2-style-adjust">
                                <ul class="list-inline">
                                    <li><label class="control-label">选择账号:</label></li>
                                    <li><v-select :options="list_product_options" v-model="product"></v-select></li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    :multiple-sort="tableConfig.multipleSort"
                    @sort-change="sortChange"
                    sort-always
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total" :page-size="pageSize" :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>
    </div>
    </div>
</script>

<script>

    Vue.component('list_bang_template', {
        template: '#show_bang',
        props : ['init_params'],
        data: function () {
            return {
                disabled_day : false,
                disabled_list: false,
                list_contract_status :[
                    {name: '签约状态', value : ''},
                    {name: '已签约已付款', value:1},
                    {name: '已签约未付款', value:2},
                    {name: '未签约', value:3},
                    {name: '特殊客户', value:5},
                    {name: '其他', value:4}
                ],
                contract_status : '',
                active : '',
                account : {label : '全部客户', id : ''},
                id : '',
                begin:'',
                end : '',
                product : {id : '', label:'全部账号'},
                isLoading: false,
                pageIndex:1,
                pageSize:20,
                total : 0,
                tableRange : [
                ],
                tableConfig: {
                    multipleSort: false,
                    tableData: [
                    ],
                    columns: [
                        {field : 'id', title : '账号ID', width :100, columnAlign: 'center',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'owner', title : '账号名称', width: 100, columnAlign: 'center',isResize:true, titleCellClassName: 'title_column', componentName:'table-operation'},
                        {field: 'name_account', title : '客户名称', width: 100, columnAlign: 'center',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'itag', title : '单次查询量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'batch', title : '批量查询量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc', titleCellClassName: 'title_column'}
                    ]
                }
            }
        },
        mounted : function () {
            // 初始化页面
            this.initParams();

            this.requestList();
        },
        computed : {
            list_account_options : function () {
                var list_account = Object.values(this.init_params.list_account).map(function(item){
                    item.label = item.name;
                    return item;
                });

                list_account.unshift({label:'全部客户', id:''});
                return list_account;
            },
            list_product_options : function () {
                var list_product = Object.values(this.init_params.list_product).map(function(item){
                    item.label = item.owner;
                    return item;
                });
                list_product.unshift({id : '', label:'全部账号'});
                return list_product;
            }
        },
        methods: {
            downloadFile : function(type){
                if (this.checkTime() === false) {
                    return false;
                }
                // 转圈圈旋转
                this.isLoading = true;

                // 列表按钮禁用
                if (type === 'list') {
                    this.disabled_list = true;
                }
                if (type === 'day') {
                    this.disabled_day = true;
                }

                // url
                var url = '';
                if (type === 'day') {
                    url = '/Home/BmMatchingStat/exportByDay';
                } else {
                    url = '/Home/BmMatchingStatFile/index';
                }

                // 参数
                var params_request = '?begin=' + this.begin + '&end=' + this.end;
                params_request += this.product.id ? '&product_id=' + this.product.id : '';
                params_request += this.product.id ? '&id=' + this.product.id : '';
                params_request +=  this.account.id ? '&account_id=' + this.account.id : '';
                params_request += this.contract_status ? '&contract_status=' + this.contract_status : '';
                params_request += this.active ? '&active=' + this.active : '';

                var vm = this;
                $.fileDownload(url + params_request).done(function(){
                    vm.isLoading = false;
                    if (type === 'list') {
                        vm.disabled_list = false;
                    }
                    if (type === 'day') {
                        vm.disabled_day = false;
                    }

                }).fail(function(response){
                    console.log(response);
                    vm.isLoading = false;
                    if (type === 'list') {
                        vm.disabled_list = false;
                    }
                    if (type === 'day') {
                        vm.disabled_day = false;
                    }
                    modalExport('导出文件失败，请刷新后再试');
                });
                return false;
            },
            sortChange : function(params){
                // 执行排序的url
                var url =  '/Home/BmMatchingStat/sortBy';

                // 排序的字段 && 方式
                var params_url = {
                    filter_field : '',
                    filter_order : '',
                    list_data : this.tableRange
                };

                Object.keys(params).forEach(function(key){
                    if (params[key] === 'asc' || params[key] === 'desc') {
                        params_url.filter_field = key;
                        params_url.filter_order = params[key];
                    }
                });

                // 请求排序
                var vm = this;
                this.$http.post(url, params_url, {responseType:'json'}).then(function (response) {
                    if (response.body.status === 0) {
                        vm.iniTableData(response.body.list_stat);
                    } else {
                        console.log('排序出错了，宝贝: ' + response.body.msg);
                    }
                });
            },
            initParams : function(){
                var request_params = this.init_params.params_request;

                // 初始化 时间 && 协议 && 状态
                this.end = request_params.end;
                this.begin = request_params.begin;
                if (request_params.contract_status !== undefined) {
                    this.contract_status = request_params.contract_status;
                }
                if (request_params.active !== undefined) {
                    this.active = request_params.active;
                }

                // 初始化客户 && 产品
                if (request_params.account_id !== undefined) {
                    var account_id = request_params.account_id;
                    this.account = this.init_params.list_account[account_id];

                }
                if (request_params.id !== undefined) {
                    var product_id = request_params.id;
                    this.product = this.init_params.list_product[product_id];
                }
            },
            viewProfile: function (id) {
                console.log('view profile with id:', id)
            },

            // 自定义列触发事件
            customCompFunc : function(params) {
                // 检查参数
                if (this.checkTime() === false) {
                    return false;
                }

                if (params.type === 'show_detail') {
                    var url_detail = '/Home/BmMatchingStat/detail?id=' + params.rowData.id + '&begin=' + this.begin + '&end=' + this.end;
                    window.open(url_detail, '_blank');
                }
                if (params.type = 'show_total') {
                    // 请求的参数
                    var params_request = '?begin=' + this.begin + '&end=' + this.end;
                    params_request += this.product.id ? '&id=' + this.product.id : '';
                    params_request +=  this.account.id ? '&account_id=' + this.account.id : '';
                    params_request += this.contract_status ? '&contract_status=' + this.contract_status : '';
                    params_request += this.active ? '&active=' + this.active : '';

                    // 请求的url
                    var url = '/Home/BmMatchingStat/totalShow' + params_request;
                    window.open(url, '_blank');
                }
            },
            checkTime: function() {
                // get begin time and end time
                var time_begin = this.begin;
                var time_end = this.end;
                var today_str = (new Date()).toDateString();

                // change time format for firefox
                time_end = time_end.replace(/\-/g, '\/');
                time_begin = time_begin.replace(/\-/g, '\/');

                // check begin time
                if (!time_begin && time_end) {
                    modalExport('请选择开始时间');
                    return false;
                }

                // check end time
                if (time_begin && !time_end) {
                    modalExport('请选择结束时间');
                    return false;
                }

                if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
                    modalExport('请选择有效的结束时间');
                    return false;
                }

                // check time
                // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
                var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));

                if (time_diff < 0) {
                    modalExport('开始时间必须小于结束时间');
                    return false;
                }

                // calculate the days between begin and end
                var day_diff = Math.floor(time_diff / 8.64e7);

                //  the time should less than 31
                if (day_diff <= 364) {
                    return true;
                } else {
                    modalExport('单次查询时间范围不能超过365天');
                    return false;
                }
            },
            // 请求数据
            requestList: function () {
                if (this.checkTime() === false) {
                    return false;
                }

                this.isLoading = true;
                var url = '/Home/BmMatchingStat/statList';
                var params = {
                    params : {
                        begin: this.begin,
                        end: this.end,
                        id : (this.product === null || this.product.id === undefined ) ? '' : this.product.id,
                        account_id : (this.account === null || this.account.id === undefined) ? '' : this.account.id,
                        active:this.active,
                        contract_status : this.contract_status
                    },
                    responseType: 'json'
                };
                console.log(params);
                var vm = this;
                this.$http.get(url, params).then(function (response) {
                    console.log(response);
                    if (response.body.status === 0) {
                        vm.isLoading = false;
                        var list_range = response.body.list_stat_data;
                        // 重置页面数据
                        vm.iniTableData(list_range);
                    }
                });
            },
            // 重置页面数据
            iniTableData : function(list_range){
                this.tableRange = list_range;
                this.total = list_range.length;
                this.getTableData();
            },

            // 重置当前页展示的数据
            getTableData : function(){
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex-1)*this.pageSize,(this.pageIndex)*this.pageSize)
            },
            // 换页重置数据
            pageChange: function(pageIndex){

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange : function(pageSize){

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            }
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    // 自定义列组件，进入详情页 但是没必要这么麻烦,formatter选项就足够支撑了
    Vue.component('table-operation', {
        template: '#custom_show',
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            showDetail: function () {
                var params = {type: 'show_detail', index: this.index, rowData: this.rowData};
                this.$emit('on-custom-comp', params);
            },
            statTotal : function () {
                var params = {type: 'show_total', index: this.index, rowData: this.rowData};
                this.$emit('on-custom-comp', params);
            }
        }
    });

    new Vue({
        el: "#list_bm_matching"
    });

</script>
</body>
</html>
