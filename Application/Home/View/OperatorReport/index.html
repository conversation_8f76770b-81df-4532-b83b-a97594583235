<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('index')}" method="get" id="form_id">
                    <div class="form-group">
                        <label  class="control-label">日期区间</label>
                        <input type="date" name="start_time" id="start_time" class="form-control"  value="{$time.startTime|substr=###,0,10}"/>
                        —
                        <input type="date" name="end_time" id="end_time" class="form-control"  value="{$time.endTime|substr=###,0,10}"/>
                    </div>
                    <div class="form-group">
                        <label for="sid">报告编号</label>
                        <input type="text" name="sid" id="sid" class="form-control" value="{$Think.get.sid}" placeholder="请输入">
                    </div>
                    <div class="form-group">
                        <label for="tel">查询号码</label>
                        <input type="text" name="tel" id="tel" class="form-control" value="{$Think.get.tel}" placeholder="请输入">
                    </div>
                    <div class="form-group">
                        <label for="account">账号名称</label>
                        <select name="account" id="account" class="form-control">
                            <notempty name="Think.get.account">
                                <?= $account = I('get.account'); ?>
                                <option value="{$account}" selected>{$accountData[$account]}</option>
                            </notempty>
                            <option value="">账号名称</option>
                            <volist name="accountData" id="account">
                                <option value="{$key}">{$account}</option>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="apikey">账号APIKEY</label>
                        <input type="text" name="apikey" id="apikey" class="form-control" value="{$Think.get.apikey}" placeholder="请输入">
                    </div>
                    <div class="form-group">
                        <label for="record_status">详单状态</label>
                        <select class="form-control" name="record_status" id="record_status">
                            <option value="-1">全部</option>
                            <volist name="recordStatus" id="vo">
                                <if condition="(isset($_GET['record_status'])) AND ($_GET['record_status']==$key)">
                                    <option value="{$key}" selected>{$vo}</option>
                                    <else />
                                    <option value="{$key}">{$vo}</option>
                                </if>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="pdf_status">报告状态</label>
                        <select class="form-control" name="pdf_status" id="pdf_status">
                            <option value="-1">全部</option>
                            <volist name="pdfStatus" id="vo">
                                <if condition="(isset($_GET['pdf_status'])) AND ($_GET['pdf_status']==$key)">
                                    <option value="{$key}" selected>{$vo}</option>
                                    <else />
                                    <option value="{$key}">{$vo}</option>
                                </if>
                            </volist>
                        </select>
                    </div>
                <div class="form-group">
                    <input type="submit" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <button type="button" id="download_all" class="btn btn-success btn-sm">批量导出</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered">
            <tr align="center">
                <th>
                    <input type="checkbox" id="check_all">
                </th>
                <th>报告编号</th>
                <th>账号名称</th>
                <th>查询号码</th>
                <th>用户名称</th>
                <th>详单状态</th>
                <th>详单失败原因</th>
                <th>报告状态</th>
                <th>生成时间</th>
                <th style="width:90px;">操作</th>
            </tr>
            <volist name="data.data" id="vo">
            <tr>
                <td>
                    <eq name="vo.pdf_status" value="1">
                        <input type="checkbox" name="check_sid" value="{$vo.sid}" data-info="{$vo.apikey}">
                    </eq>
                </td>
                <td>{$vo.sid}</td>
                <td>{$accountData[$vo['apikey']]}</td>
                <td>{$vo.tel}</td>
                <td>{$vo.full_name}</td>
                <td>
                    <if condition="isset($vo['record_status'])" >
                        {$recordStatus[$vo[record_status]]}
                    </if>
                </td>
                <td>{$vo.record_msg}</td>
                <td>{$pdfStatus[$vo[pdf_status]]}</td>
                <td>{$vo.end_time|date="Y-m-d H:i:s",###}</td>
                <td>
                    <eq name="vo.pdf_status" value="1">
                        <a href="javascript:;" class="look" title="{$vo.sid}" data-info="{$vo.apikey}">查看</a>
                        <a href="javascript:;" class="download" title="{$vo.sid}" data-info="{$vo.apikey}">下载</a>
                    </eq>
                </td>
            </tr>
            </volist>
        </table>
    </div>

    <if condition="$data.page">
        <ul class="pagination">
            {$data.page}
        </ul>
    </if>

</div>
</div>
<script type="text/javascript">
    $(function() {
        $("#account").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '账号名称',
            width: '200px'
        });
        $("#form_id").submit(function () {
            var start_time = $("#start_time").val();
            var end_time = $("#end_time").val();
            if (!checkTime(start_time) || !checkTime(end_time)) {
                return false;
            }
            if (!checkApiKey()) {
                return false;
            }
            return true;
        });
        $("#check_all").click(function () {
            if ($(this).prop('checked')) {
                $('input[name="check_sid"]').prop('checked', true);
            } else {
                $('input[name="check_sid"]').prop('checked', false);
            }
        });
        $('input[name="check_sid"]').click(function () {
            $("#check_all").prop('checked', false);
        });
        $(".look").click(function () {
            var sid = $(this).attr('title');
            var apikey = $(this).attr('data-info');
            $.ajax({
                url : "{:U('Home/OperatorReport/look')}",
                type : 'post',
                data : {
                    sid : sid,
                    apikey : apikey
                },
                success : function (res) {
                    if (res.status==0) {
                        window.open(res.url);
                    } else {
                        alert('暂时不能查看该报告，原因：' + res.msg);
                    }
                },
                error : function () {
                    alert('服务端响应失败');
                }
            });
        });
        $('.download').click(function () {
            var sid = $(this).attr('title');
            var apikey = $(this).attr('data-info');
            $.ajax({
                url : "{:U('Home/OperatorReport/download')}",
                type : 'post',
                data : {
                    sid : sid,
                    apikey : apikey,
                    type : 'single'
                },
                success : function (res) {
                    if (res.status==0) {
                        window.open(res.msg);
                    } else {
                        alert('暂时不能下载该报告，原因：' + res.msg);
                    }
                },
                error : function () {
                    alert('服务端响应失败');
                }
            });
        });
        $("#download_all").click(function () {
            var pdfLength = $('input[name="check_sid"]:checked').length;
            if (pdfLength==0) {
                alert('请选择需要导出的数据');
                return false;
            } else if (pdfLength==1) {
                alert('一份文件不支持批量下载，请使用数据末尾的下载功能');
                return false;
            }
            var conf = confirm('批量导出可能花费较长时间，这取决于您要导出的数量，请耐心等待，确定是否继续？');
            if (conf) {
                loading();
                var apikey = $('input[name="check_sid"]:checked').eq(0).attr('data-info');
                $.ajax({
                    url : "{:U('Home/OperatorReport/download')}",
                    type : 'post',
                    data : {
                        apikey : apikey,
                        type : 'total',
                        sid : getCheckedSid()
                    },
                    success : function (res) {
                        if (res.status==0) {
                            $.fileDownload(res.msg);
                            closeLoading();
                            $('input[name="check_sid"]:checked').prop('checked', false);
                            $('input[name="check_sid"]').prop('checked', false);

                        } else {
                            alert('暂时不能下载该报告，原因：' + res.msg);
                        }
                    },
                    error : function () {
                        alert('服务端响应失败');
                    }
                })
            }
        });
    });
    //验证时间的范围
    function checkTime (time) {
        if (!time || time=='') {
            alert('请选择查询的时间区间');
            return false;
        }
        var year = time.substring(0,4);
        var month = time.substring(5,7);
        var day = time.substring(8,10);
        var before = Date.UTC(year, month-1, day, 0, 0, 0, 0);
        var date = new Date();
        var now = date.getTime();
        var differ = now - before;
        if (differ>**********) {
            alert('您的查询时间范围太大了，暂时不支持93天之前的数据查询');
            return false;
        } else if (differ<-86400) {
            alert('您的查询时间超过了当前时间，请确认您的时间范围');
            return false;
        }
        return true;
    }
    //验证查询的APIKEY与账号名称
    function checkApiKey()
    {
        var apiKey = $("#apikey").val();
        var account = $("#account").val();
        if (apiKey!='' && account!='' && apiKey!=account) {
            var cfr = confirm('您输入的APIKEY与选择的账号存在冲突，如果继续查找，系统将不会查询该账号下的运营商报告，是否继续查询');
            return cfr;
        }
        return true;
    }
    //获取已经选择的数据的SID
    function getCheckedSid()
    {
        var element = $('input[name="check_sid"]');
        var sid = new Array();
        element.each(function (i, n) {
            if ($(this).prop('checked')) {
                sid.push($(this).val());
            }
        });
        return sid.join(',');
    }
    //加载动画
    function loading()
    {
        var loadingHtml = '<div id="load" style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:100;background:rgba(100,100,100,0.3);">' +
            '<div class="lds-css ng-scope" style="top:50%;left:50%;position:fixed;margin:-100px 0 0 -100px;"> <div class="lds-spinner" style="100%;height:100%"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div> <style type="text/css">@keyframes lds-spinner { 0% { opacity: 1; } 100% { opacity: 0; } } @-webkit-keyframes lds-spinner { 0% { opacity: 1; } 100% { opacity: 0; } } .lds-spinner { position: relative; } .lds-spinner div { left: 96px; top: 30px; position: absolute; -webkit-animation: lds-spinner linear 1s infinite; animation: lds-spinner linear 1s infinite; background: #046ac2; width: 8px; height: 20px; border-radius: 60%; -webkit-transform-origin: 4px 70px; transform-origin: 4px 70px; } .lds-spinner div:nth-child(1) { -webkit-transform: rotate(0deg); transform: rotate(0deg); -webkit-animation-delay: -0.9375s; animation-delay: -0.9375s; } .lds-spinner div:nth-child(2) { -webkit-transform: rotate(22.5deg); transform: rotate(22.5deg); -webkit-animation-delay: -0.875s; animation-delay: -0.875s; } .lds-spinner div:nth-child(3) { -webkit-transform: rotate(45deg); transform: rotate(45deg); -webkit-animation-delay: -0.8125s; animation-delay: -0.8125s; } .lds-spinner div:nth-child(4) { -webkit-transform: rotate(67.5deg); transform: rotate(67.5deg); -webkit-animation-delay: -0.75s; animation-delay: -0.75s; } .lds-spinner div:nth-child(5) { -webkit-transform: rotate(90deg); transform: rotate(90deg); -webkit-animation-delay: -0.6875s; animation-delay: -0.6875s; } .lds-spinner div:nth-child(6) { -webkit-transform: rotate(112.5deg); transform: rotate(112.5deg); -webkit-animation-delay: -0.625s; animation-delay: -0.625s; } .lds-spinner div:nth-child(7) { -webkit-transform: rotate(135deg); transform: rotate(135deg); -webkit-animation-delay: -0.5625s; animation-delay: -0.5625s; } .lds-spinner div:nth-child(8) { -webkit-transform: rotate(157.5deg); transform: rotate(157.5deg); -webkit-animation-delay: -0.5s; animation-delay: -0.5s; } .lds-spinner div:nth-child(9) { -webkit-transform: rotate(180deg); transform: rotate(180deg); -webkit-animation-delay: -0.4375s; animation-delay: -0.4375s; } .lds-spinner div:nth-child(10) { -webkit-transform: rotate(202.5deg); transform: rotate(202.5deg); -webkit-animation-delay: -0.375s; animation-delay: -0.375s; } .lds-spinner div:nth-child(11) { -webkit-transform: rotate(225deg); transform: rotate(225deg); -webkit-animation-delay: -0.3125s; animation-delay: -0.3125s; } .lds-spinner div:nth-child(12) { -webkit-transform: rotate(247.5deg); transform: rotate(247.5deg); -webkit-animation-delay: -0.25s; animation-delay: -0.25s; } .lds-spinner div:nth-child(13) { -webkit-transform: rotate(270deg); transform: rotate(270deg); -webkit-animation-delay: -0.1875s; animation-delay: -0.1875s; } .lds-spinner div:nth-child(14) { -webkit-transform: rotate(292.5deg); transform: rotate(292.5deg); -webkit-animation-delay: -0.125s; animation-delay: -0.125s; } .lds-spinner div:nth-child(15) { -webkit-transform: rotate(315deg); transform: rotate(315deg); -webkit-animation-delay: -0.0625s; animation-delay: -0.0625s; } .lds-spinner div:nth-child(16) { -webkit-transform: rotate(337.5deg); transform: rotate(337.5deg); -webkit-animation-delay: 0s; animation-delay: 0s; } .lds-spinner { width: 200px !important; height: 200px !important; -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px); transform: translate(-100px, -100px) scale(1) translate(100px, 100px); } </style></div>' +
            '</div>';
        $("body").append(loadingHtml);
    }
    //关闭加载动画
    function closeLoading()
    {
        $("body").find('#load').remove();
    }
</script>
</body>
</html>
