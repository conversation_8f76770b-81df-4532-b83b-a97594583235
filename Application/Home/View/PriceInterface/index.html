<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__datepicker/bootstrap-datepicker.css">
    <style></style>
</head>
<body>
<!--<include file="Common@Public/header" />-->
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container add_form">
    <form class="form-horizontal" id="form" method="post">
        <div class="form-group">
            <label for="channel_id" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                渠道名称：
            </label>
            <div class="col-sm-5">
                <select name="channel_id" id="channel_id" class="form-control">
                    <option value="">选择渠道</option>
                    <?php
                    foreach($channel_info as $key=>$value){
                    $selected = '';
                    if($channel_id == $value['channel_id']){
                        $selected = 'selected';
                    }
                    ?>
                        <option <?php echo $selected;?> value="<?php echo $value['channel_id'];?>"><?php echo $value['label'];?></option>
                    <?php } ?>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="is_qufen" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                是否区分接口：
            </label>
            <div class="col-sm-3">
                <select name="is_qufen" id="is_qufen" class="form-control">
                    <option value="2">不区分</option>
                    <option value="1">区分</option>
                </select>
            </div>
        </div>
        <div class="form-group" id="interFaceSelect" hidden>
            <label for="interFaceSelect" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                需要配置的接口：
            </label>
            <div class="col-sm-3">
                <select name="select_interface" id="select_interface" class="form-control">
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="is_qufen_operator" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                是否区分运营商：
            </label>
            <div class="col-sm-3">
                <select name="is_qufen_operator" id="is_qufen_operator" class="form-control">
                    <option value="2">不区分</option>
                    <option value="1">区分</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="is_qufen_encrypt" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                是否区分加密：
            </label>
            <div class="col-sm-3">
                <select name="is_qufen_encrypt" id="is_qufen_encrypt" class="form-control">
                    <option value="2">不区分</option>
                    <option value="1">区分</option>
                </select>
            </div>
        </div>
        <div class="form-group" id="time_div">
            <label for="is_qufen" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                配置开始时间：
            </label>
            <div class="col-sm-3">
                <input type="text" name="start_time" id="start_time" class="form-control" value=""/>
            </div>
        </div>
        <!--<div class="form-group">
            <label for="product_id" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                产品名称：
            </label>
            <div class="col-sm-5">
                <select name="product_id" id="product_id" class="form-control">
                    <option value="">选择产品</option>
                    <option value="200">邦秒验</option>
                    <option value="210">邦信分-通信字段</option>
                    <option value="1000">邦信分-通信评分</option>
                    <option value="401">邦企查</option>
                </select>
            </div>
        </div>-->

        <hr>
        <div id="zuhe">
            <?php echo $input; ?>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="button" id="submit" class="btn btn-warning">保存</button>
            </div>
        </div>
    </form>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__ajaxform.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="text/javascript" src="__JS__datepicker/bootstrap-datepicker.js"></script>
<script type="text/javascript" src="__JS__datepicker/bootstrap-datepicker.zh-CN.js"></script>
<script type="application/javascript">
    $(function(){
        $("#channel_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择渠道',
            width: '200px'
        });
        $('input[name="start_time"]').datepicker({
            format : 'yyyymmdd',
            language: 'zh-CN',
            autoclose: true,
           // startView: 'months',
            //maxViewMode:'years',
           // minViewMode:'months',
            onClose: function(dateText, inis) {
                $('input[name="start_time"]').val(dateText);
            }

        });

        /*var product_id = $('#product_id').val();
        if(product_id != ''){
            $.ajax({
                type: 'post',
                url: "{:U('/Home/PriceInterface/index')}",
                data: {
                    flag:'change_product',
                    id: product_id,
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $('#channel_id').html(data.option);
                    }else{
                        $('#channel_id').html('<option value="">选择渠道</option>');
                        //alert(data.msg);
                    }
                }
            });
        }*/
        /*var channel_id = $('#channel_id').val();
        if(product_id != '' && channel_id != ''){
            var is_qufen = $('#is_qufen').val();
            var is_qufen_operator = $('#is_qufen_operator').val();
            var is_qufen_encrypt = $('#is_qufen_encrypt').val();
            $.ajax({
                type: 'post',
                url: "{:U('/Home/PriceInterface/index')}",
                data: {
                    flag:'change_channel',
                    id: channel_id,
                    is_qufen: is_qufen,
                    is_qufen_operator: is_qufen_operator,
                    is_qufen_encrypt: is_qufen_encrypt
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $('#zuhe').html(data.input);
                    }else{
                        $('#zuhe').empty();
                        alert(data.msg);
                    }
                }
            });
        }*/

    });

    /*$('#product_id').on('change', function(){
        var val = $(this).val();
        //var price_model = $("input[name='price_model']:checked").val();

        if(val != ''){
            $.ajax({
                type: 'post',
                url: "{:U('/Home/PriceInterface/index')}",
                data: {
                    flag:'change_product',
                    id: val,
                },
                success: function(data) {
                    if(data.status == 'ok'){
                       $('#channel_id').html(data.option);
                    }else{
                        $('#channel_id').html('<option value="">选择渠道</option>');
                        alert(data.msg);
                    }
                }
            });
        }
    });*/
    $('#channel_id,#is_qufen,#interFaceSelect,#is_qufen_operator,#is_qufen_encrypt').on('change', function(){
        //首先判断渠道是否为空
        var channel_id = $('#channel_id').val();
        if(channel_id == ''){
            return false;
        }
        var is_qufen = $('#is_qufen').val();
        var is_qufen_operator = $('#is_qufen_operator').val();
        var is_qufen_encrypt = $('#is_qufen_encrypt').val();
        var select_interface = $('#select_interface').val();
        if (is_qufen == 1){
            $('#time_div').hide();
        }else{
            $('#time_div').show();
            $('#interFaceSelect').hide();
            $('#select_interface').empty();
            select_interface = '';
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/PriceInterface/index')}",
            data: {
                flag:'change_channel',
                id: channel_id,
                is_qufen: is_qufen,
                is_qufen_operator: is_qufen_operator,
                is_qufen_encrypt: is_qufen_encrypt,
                select_interface: select_interface
            },
            success: function(data) {
                if(data.status == 'ok'){
                    $('#zuhe').html(data.input);
                    if (is_qufen == 1) {
                        $('#select_interface').html(data.interFaceSelect);
                        $('#interFaceSelect').show();
                    }
                }else{
                    $('#zuhe').empty();
                    alert(data.msg);
                }
            }
        });
    });
    $('#submit').on('click', function(){

        var if_time = '<?php echo $if_time; ?>';
        var is_qufen = $('#is_qufen').val();
        var price_length = $("#zuhe").find("input[name='price[]']").length;
        for (var i = 0; i < price_length; i++) {
            var price_val = $("#zuhe").find("input[name='price[]']").eq(i).val();
            if (price_val=="") {
                alert('价格不能为空');
                return false;
            }else{
                if(isNaN(price_val)){
                    alert('价格字段必须为数值类型');
                    return false;
                }
                if(price_val < 0){
                    alert('价格字段不能小于零');
                    return false;
                }
            }
        }

        if(is_qufen == 1){
            var d = new Date();
            var month = d.getMonth()+1;
            if(month < 10){
                month = '0'+month;
            }
            var date = d.getDate();
            if(date < 10){
                date = '0'+date;
            }
            var str = d.getFullYear()+""+month+""+date;
            for (var i = 0; i < price_length; i++) {
                var interface_val = $("#zuhe").find("input[name='price[]']").eq(i).attr('data-id');
                var arr_time = $("#zuhe").find("input[name='arr_time["+interface_val+"]']").val();
                if(arr_time == ''){
                    alert('接口时间不能为空');
                    return false;
                }
                var time_arr = arr_time.split('-');
                arr_time = time_arr[0]+time_arr[1]+time_arr[2];
                if(if_time == true){
                    if(arr_time < str){
                        alert('配置时间不能小于当前时间');
                        return false;
                    }
                }
            }
            var start_time = '';
        }else{
            var start_time = $('#start_time').val();
            if(start_time == ''){
                alert('配置开始时间不能为空');
                return false;
            }
        }

        var div_length = $("#zuhe").find(".jj").length;
        var arr = new Array();
        if(is_qufen == 1){
            for (var i=0; i<div_length; i++) {
                var interface_val = $("#zuhe").find("input[name='price[]']").eq(i).attr('data-id');
                var operator_val = $("#zuhe").find("input[name='operator[]']").eq(i).val();
                var encrypt_val = $("#zuhe").find("input[name='encrypt[]']").eq(i).val();
                var price_val = $("#zuhe").find("input[name='price[]']").eq(i).val();
                var price_model_val = $("#zuhe").find("select[name='price_model[]']").eq(i).val();
                var start_time2 = $("#zuhe").find("input[name='arr_time["+interface_val+"]']").val();
                var time_arr = start_time2.split('-');
                start_time2 = time_arr[0]+time_arr[1]+time_arr[2];
                arr.push(interface_val + '#'  + operator_val + '#' + encrypt_val + '#' + price_val + '#' + price_model_val + '#' + start_time2);
            }
        }else{
            for (var i=0; i<div_length; i++) {
                var operator_val = $("#zuhe").find("input[name='operator[]']").eq(i).val();
                var encrypt_val = $("#zuhe").find("input[name='encrypt[]']").eq(i).val();
                var price_val = $("#zuhe").find("input[name='price[]']").eq(i).val();
                var price_model_val = $("#zuhe").find("select[name='price_model[]']").eq(i).val();
                arr.push(operator_val + '#' + encrypt_val + '#' + price_val + '#' + price_model_val);
            }
        }
        if(arr.length == 0){
            return false;
        }

        //var price_model = $("input[name='price_model']:checked").val();
        //var product_id = $('#product_id').val();
        var channel_id = $('#channel_id').val();
        var is_qufen_operator = $('#is_qufen_operator').val();
        var is_qufen_encrypt = $('#is_qufen_encrypt').val();
        $.ajax({
            type: 'post',
            url: "{:U('/Home/PriceInterface/index')}",
            data: {
                flag:'add',
               // price_model: price_model,
                is_qufen: is_qufen,
              //  product_id: product_id,
                channel_id: channel_id,//不区分的时候获取所有接口id
                arr: arr,
                start_time: start_time
            },
            success: function(data) {
                if(data.status == 'ok'){
                    alert(data.msg);
                }else{

                    alert(data.msg);
                }
            }
        });

    });

</script>