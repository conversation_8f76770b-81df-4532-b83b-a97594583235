<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('item')}" class="form-inline" method="get" id="list_form"

                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加项目组</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">项目组名称</th>
                <th style="text-align:center;">节点列表</th>
                <th style="text-align:center;">apikey</th>
                <th style="text-align:center;">secret</th>
                <th style="text-align:center;">提醒人</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td align="center"><a href="{:U('/Home/TestCase/item/team_id/'.$vo['id'])}" target="_blank" class="btn btn-success btn-sm">{$vo.name}</a></td>
                    <td align="center"><a href="{:U('/Home/TestCase/node/team_id/'.$vo['id'])}" target="_blank" class="btn btn-primary btn-sm">节点列表</a></td>
                    <td align="center">{$vo.apikey}</td>
                    <td align="center">{$vo.secret}</td>
                    <td align="center">{$vo.admin}</td>
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">编辑</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >添加项目组</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add_team')}" id="file_in_form" method="post" enctype="multipart/form-data">

                    <div class="form-group">
                        <label for="name">项目名称:</label>
                        <input type="text" name="name" id="name" size="30" value="" />
                    </div>
                    <div class="form-group">
                        <label for="apikey">apikey&nbsp;&nbsp;:</label>
                        <input type="text" name="apikey" id="apikey" size="50" value="" />
                    </div>
                    <div class="form-group">
                        <label for="secret">secret&nbsp;&nbsp;&nbsp;:</label>
                        <input type="text" name="secret" id="secret" size="65" value="" />
                    </div>
                    <div class="form-group">
                        <label for="secret">提醒人&nbsp;&nbsp;&nbsp;:</label>
                        <input type="text" name="admin" id="admin" size="30" value="" placeholder="邮箱的英文名称"/>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_submit">增加</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >编辑项目组</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('edit')}" id="file_in_form2" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="id2" id="id2" value="" />
                    <div class="form-group">
                        <label for="name2">项目名称:</label>
                        <input type="text" name="name2" id="name2" size="30" value="" />
                    </div>
                    <div class="form-group">
                        <label for="apikey2">apikey&nbsp;&nbsp;:</label>
                        <input type="text" name="apikey2" id="apikey2" size="50" value="" />
                    </div>
                    <div class="form-group">
                        <label for="secret2">secret&nbsp;&nbsp;&nbsp;:</label>
                        <input type="text" name="secret2" id="secret2" size="65" value="" />
                    </div>
                    <div class="form-group">
                        <label for="secret2">提醒人&nbsp;&nbsp;&nbsp;:</label>
                        <input type="text" name="admin2" id="admin2" size="30" value="" placeholder="邮箱的英文名称"/>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_submit2">编辑</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_modal3" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >测试详情</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form3" method="post" enctype="multipart/form-data">
                    <label>设置的值:</label>
                    <div class="form-group" style="color:red;line-height:24px;" id="set_value">

                    </div>
                    <label>接口的值:</label>
                    <div class="form-group" style="color:red;line-height:24px;" id="api_value">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal4" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >执行记录</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form4" method="post" enctype="multipart/form-data">
                    <div class="form-group" style="color:red;line-height:24px;" id="admin_log">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>


<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {

        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        $("#file_in").click(function () {
            $("#file_in_modal").modal('show');
        });
        //添加测试用例
        $("#file_submit").click(function () {

            var name = $('#name').val();
            if (name == '') {
                alert('用例名称不能为空');
                return false;
            }
            var apikey = $('#apikey').val();
            if (apikey == '') {
                alert('apikey不能为空');
                return false;
            }
            var secret = $('#secret').val();
            if (secret == '') {
                alert('密钥不能为空');
                return false;
            }
            var reg = /[\da-zA-Z\.]/;
            var admin = $('#admin').val();
            if(admin == ''){
                alert('提醒人不能为空');
                return false;
            }
            if(!reg.test(admin)){
                alert('提醒人格式不正确');
                return false;
            }
            $("#loading").show();

            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/add_team')}",
                data: {
                    name: name,
                    apikey: apikey,
                    secret: secret,
                    admin: admin
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $("#loading").hide();
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });


        });

        $("#file_submit2").click(function () {

            var id2 = $('#id2').val();
            if(id2 == ''){
                alert('id不能为空');
                return false;
            }

            var name2 = $('#name2').val();
            if (name2 == '') {
                alert('用例名称不能为空');
                return false;
            }

            var apikey2 = $('#apikey2').val();
            if (apikey2 == '') {
                alert('apikey不能为空');
                return false;
            }
            var secret2 = $('#secret2').val();
            if (secret2 == '') {
                alert('密钥不能为空');
                return false;
            }
            var reg = /[\da-zA-Z\.]/;
            var admin2 = $('#admin2').val();
            if(admin2 == ''){
                alert('提醒人不能为空');
                return false;
            }
            if(!reg.test(admin2)){
                alert('提醒人格式不正确');
                return false;
            }

            $("#loading").show();
            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/edit_team')}",
                data: {
                    id2: id2,
                    name2: name2,
                    apikey2: apikey2,
                    secret2: secret2,
                    admin2: admin2
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $("#loading").hide();
                        $('#file_in_modal2').modal('hide');
                        window.location.reload();
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });

        });

    });
    //编辑
    function edit(id){
        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getEditTeam')}",
            data: {
                id: id,
            },
            success: function(data) {
                $('#id2').val(data.id);
                $('#name2').val(data.name);
                $('#apikey2').val(data.apikey);
                $('#secret2').val(data.secret);
                $('#admin2').val(data.admin);
                $('#file_in_modal2').modal('show');
            }
        });
    }
    /**
     * 点击详情页面
     */
    function showPage(id){

        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getEdit')}",
            data: {
                id: id,
            },
            success: function(data) {
                $('#set_value').text(data.json_result);
                $('#api_value').text(data.api_result);
                $('#file_in_modal3').modal('show');
            }
        });

    }
    /**
     * 执行测试
     */
    function run(id){
        if(id == ''){
            alert('id不能为空');
            return false;
        }
        $("#loading").show();
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/run_item')}",
            data: {
                id: id,
            },
            success: function(data) {
                $("#loading").hide();
                if(data.status == 'ok'){
                    alert(data.msg);
                    $("#loading").hide();
                    window.location.reload();
                }else{
                    alert(data.msg);
                    $("#loading").hide();
                    window.location.reload();
                }
            }
        });
    }
</script>
</body>
</html>
