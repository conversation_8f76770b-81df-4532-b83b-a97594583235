<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('/Home/TestCase/index/item_id/'.$input['item_id'])}" class="form-inline" method="get" id="list_form">
                <!--<div class="form-group">
                    <label class="control-label" for="start_time">添加日期：</label>
                    <input type="text" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="text" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>-->
                <!--<div class="form-group">
                    <label class="control-label" for="node_search">节点：</label>
                    <select class="form-control" name="node_search" id="node_search">
                        <option value="">全部</option>
                        <option value="北京" <?php echo '北京' == $input['node_search'] ? 'selected="selected"' : ''?>>北京</option>
                        <option value="深圳" <?php echo '深圳' == $input['node_search'] ? 'selected="selected"' : ''?>>深圳</option>
                    </select>
                </div>-->

                <!--<div class="form-group">
                    <label class="control-label" for="item_search">项目名称：</label>
                    <select class="form-control" name="item_search" id="item_search">
                        <option value="">全部</option>
                        <?php  foreach($item_select as $key=>$value){ ?>
                            <option value="<?php echo $value['item'];?>" <?php echo $value['item'] == $input['item_search'] ? 'selected="selected"' : ''?>><?php echo $value['item']; ?></option>
                        <?php  } ?>
                    </select>
                </div>-->
                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加用例</button>
                </div>
                <div class="form-group" style="width: 800px;;">

                </div>
                <div class="form-group">
                    <label class="control-label" for="node_search">选择节点：</label>
                    <select  name="node_search" id="node_search">
                        <option value="" >请选择</option>
                        <?php
                                foreach($node_info as $value){
                            ?>
                        <option value="<?php echo $value['id']?>" ><?php echo $value['name']; ?></option>
                        <?php } ?>
                    </select>
                </div>

                <!--<div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>-->

                <div class="form-group">
                    <button type="button" id="run_case" class="btn btn-primary btn-sm">批量执行</button>
                </div>
                &nbsp;&nbsp;
                <div class="form-group">
                    <button type="button" id="show" onclick="showAdmin('<?php echo $input['item_id']; ?>')" class="btn btn-success btn-sm">操作记录</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <div class="panel-heading"><h3 class="panel-title">总的金额:<?php echo $total_sum['total_sum']; ?></h3></div>
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th>编号</th>
                <th style="text-align:center;">用例名称</th>
                <th style="text-align:center;">接口地址</th>
                <th style="text-align:center;">费用</th>
                <th style="text-align:center;">get参数</th>
                <th style="text-align:center;">post参数</th>
                <th style="text-align:center;">apikey</th>
                <th style="text-align:center;">secret</th>
                <th style="text-align:center;">请求方式</th>
                <th style="text-align:center;">操作时间</th>
                <th style="text-align:center;">详情</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>

            <?php
                $i = 1;
                foreach ($data as $vo) {
            ?>

                <tr>
                    <td><?php echo $i;?><input type="hidden" name="id_arr" value="{$vo.id}" /></td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{$vo.url}</td>
                    <td align="center">{$vo.money}</td>
                    <td align="center" style="word-wrap:break-word;word-break:break-all;">{$vo.get_param}</td>
                    <td align="center" style="word-wrap:break-word;word-break:break-all;">{$vo.post_param}</td>
                    <td align="center" style="word-wrap:break-word;word-break:break-all;">{$vo.apikey}</td>
                    <td align="center" style="word-wrap:break-word;word-break:break-all;">{$vo.secret}</td>
                    <td align="center">

                        <switch name="vo.is_json">
                            <case value="1">json</case>
                            <case value="2">form</case>
                        </switch>
                    </td>
                    <td align="center" style="word-wrap:break-word;word-break:break-all;">{$vo.created_at}</td>
                    <td align="center">

                            <switch name="vo.result">
                                <case value="0"><a href="javascript:void(0);" class="btn btn-primary btn-sm">未测试</a></case>
                                <case value="1"><a href="javascript:void(0);" class="btn btn-success btn-sm" onclick="showPage('<?php echo $vo['id'] ?>', 1)">成功</a></case>
                                <case value="2"><a href="javascript:void(0);" class="btn btn-danger btn-sm" onclick="showPage('<?php echo $vo['id'] ?>', 2)">失败</a></case>
                            </switch>
                    </td>
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">编辑</a>
                        <a href="javascript:void(0);" class="btn btn-primary btn-sm" onclick="run('<?php echo $vo['id'] ?>')">执行</a>
                    </td>

                </tr>
            <?php
                $i++;
                }
            ?>

            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >添加测试用例</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_form" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="item_id" id="item_id" value="<?php echo $input['item_id'] ?>" />
                    <!--<div class="form-group">
                        <label>项目名称:</label>
                        <?php echo $item_info['item']; ?>
                    </div>-->
                    <div class="form-group">
                        <label for="name"  class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>用例名称:</label>
                        <input type="text" name="name" id="name" size="30" value="" />
                    </div>
                    <div class="form-group">
                        <label for="url" class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>接口地址:</label>
                        <input type="text" name="url" id="url" size="30" value="" placeholder="格式:index/index" />
                    </div>
                    <div class="form-group">
                        <label for="apikey" class="col-sm-2 control-label" style="text-align:left">apikey:</label>
                        <input type="text" name="apikey" id="apikey" size="50" value="" placeholder="此处填,则使用该apikey" />
                    </div>
                    <div class="form-group">
                        <label for="secret" class="col-sm-2 control-label" style="text-align:left">secret:</label>
                        <input type="text" name="secret" id="secret" size="65" value="" placeholder="此处填,则使用该secret" />
                    </div>
                    <div class="form-group">
                        <label for="money" class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>测试费用:</label>
                        <input type="text" name="money" id="money" size="10" value="" />
                    </div>
                    <div class="form-group">
                        <label for="get_param" class="col-sm-2 control-label" style="text-align:left; vertical-align: top;">get参数:</label>
                        <textarea rows="2" cols="80" name="get_param" id="get_param" placeholder="格式:a=1&b=2&c=3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="is_json1" class="col-sm-2 control-label" style="text-align:left;"><span style="color:red">*</span>提交方式:</label>
                        <input type="radio" name="is_json" id="is_json1" value="1" checked />json方式
                        <input type="radio" name="is_json" id="is_json2" value="2" />form表单
                    </div>
                    <div class="form-group">
                        <label for="post_param" class="col-sm-2 control-label" style="text-align:left; vertical-align: top;">post参数:</label>
                        <textarea rows="5" cols="80" name="post_param" id="post_param" placeholder="如果是json则传压缩的json格式, 如果是form表单则传key1=value1&key2=value2形式"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="json_result" class="col-sm-2 control-label" style="text-align:left; vertical-align: top;"><span style="color:red">*</span>结果取值:</label>
                        <textarea rows="7" cols="80" name="json_result" id="json_result" placeholder="压缩的json格式"></textarea>
                    </div>
                    <div class="form-group" style="color:red;line-height:24px;">
                        * 类型匹配格式:@integer,主要包括integer,string,boolean<br/>
                        * 范围匹配格式:1#100<br/>
                        * 如果是值匹配直接写入值即可<br/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_submit">增加</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_modal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >编辑测试用例</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('edit')}" id="file_in_form2" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="id2" id="id2" value="" />
                    <input type="hidden" name="item_id2" id="item_id2" value="" />
                    <!--<div class="form-group">
                        <label>项目名称:</label>
                        <span id="item2"></span>
                    </div>-->

                    <div class="form-group">
                        <label for="name2" class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>用例名称:</label>
                        <input type="text" name="name2" id="name2" size="30" value="" />
                    </div>
                    <div class="form-group">
                        <label for="url2" class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>接口地址:</label>
                        <input type="text" name="url2" id="url2" size="30" value="" placeholder="格式:index/index" />
                    </div>
                    <div class="form-group">
                        <label for="apikey2" class="col-sm-2 control-label" style="text-align:left">apikey:</label>
                        <input type="text" name="apikey2" id="apikey2" size="50" value="" placeholder="此处填,则使用该apikey" />
                    </div>
                    <div class="form-group">
                        <label for="name" class="col-sm-2 control-label" style="text-align:left">secret:</label>
                        <input type="text" name="secret2" id="secret2" size="65" value="" placeholder="此处填,则使用该secret" />
                    </div>
                    <div class="form-group">
                        <label for="money2" class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>测试费用:</label>
                        <input type="text" name="money2" id="money2" size="10" value="" />
                    </div>
                    <div class="form-group">
                        <label for="get_param2" class="col-sm-2 control-label" style="text-align:left;vertical-align: top;">get参数:</label>
                        <textarea rows="2" cols="80" name="get_param2" id="get_param2" placeholder="格式:a=1&b=2&c=3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="is_json11" class="col-sm-2 control-label" style="text-align:left"><span style="color:red">*</span>提交方式:</label>
                        <input type="radio" name="is_json2" id="is_json11" value="1" checked />json方式
                        <input type="radio" name="is_json2" id="is_json22" value="2" />form表单
                    </div>
                    <div class="form-group">
                        <label for="post_param2" class="col-sm-2 control-label" style="text-align:left;vertical-align: top;">post参数:</label>
                        <textarea rows="5" cols="80" name="post_param2" id="post_param2" placeholder="如果是json则传压缩的json格式, 如果是form表单则传key1=value1&key2=value2形式"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="json_result2" class="col-sm-2 control-label" style="text-align:left; vertical-align: top;"><span style="color:red">*</span>结果取值:</label>
                        <textarea rows="7" cols="80" name="json_result2" id="json_result2" placeholder="压缩的json格式"></textarea>
                    </div>
                    <div class="form-group" style="color:red;line-height:24px;">
                        * 类型匹配格式:@integer,主要包括integer,string,boolean<br/>
                        * 范围匹配格式:1#100<br/>
                        * 如果是值匹配直接写入值即可<br/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_submit2">编辑</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_modal3" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog  modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >测试详情</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form3" method="post" enctype="multipart/form-data">
                    <label for="money2">设置的值:</label>
                    <div class="form-group" style="color:red;line-height:24px; word-wrap:break-word;word-break:break-all;" id="set_value">

                    </div>
                    <label for="money2">接口的值:</label>
                    <div class="form-group" style="color:red;line-height:24px; word-wrap:break-word;word-break:break-all;" id="api_value">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal4" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >执行记录</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form4" method="post" enctype="multipart/form-data">
                    <div class="form-group" style="color:red;line-height:24px;" id="admin_log">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal5" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >批量运行</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form5" method="post">
                    <div class="form-group">
                        <label for="node_select">节点名称:</label>
                        <select  name="node_select" id="node_select">
                            <option value="" >请选择</option>
                            <?php
                                foreach($node_info as $value){
                            ?>
                                <option value="<?php echo $value['id']?>" ><?php echo $value['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="node_select">勾选数量:</label>
                        <span id="select_num">0</span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="run_case_submit">执行</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_modal6" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >单独执行</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form6" method="post">
                    <div class="form-group">
                        <label for="node_select">节点名称:</label>
                        <select  name="node_select6" id="node_select6">
                            <option value="" >请选择</option>
                            <?php
                                foreach($node_info as $value){
                            ?>
                            <option value="<?php echo $value['id']?>" ><?php echo $value['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="hidden" name="test_case_id6" id="test_case_id6" value="" />
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="run_case_submit6">执行</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal7" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >执行记录</h4>
            </div>
            <div class="modal-body">
                总条数:&nbsp;&nbsp;<span class="form-group" style="color:black;line-height:24px;" id="div_total"></span>
                剩余条数:&nbsp;&nbsp;<span class="form-group" style="color:blue;line-height:24px;" id="div_num"></span>
                成功条数:&nbsp;&nbsp;<span class="form-group" style="color:green;line-height:24px;" id="div_succ">0</span>
                失败条数:&nbsp;&nbsp;<span class="form-group" style="color:red;line-height:24px;" id="div_fail">0</span>
                &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp; &nbsp;&nbsp;
                <span class="form-group" style="color:green;line-height:24px;" id="div_finish"></span>

            </div>
            <div class="panel panel-default table-responsive">
                <table class="table table-bordered table-striped table-hover">
                    <thead>
                        <tr align="center">
                            <th style="text-align:center;">用例名称</th>
                            <th style="text-align:center;">设置的值</th>
                            <th style="text-align:center;">接口的值</th>
                        </tr>
                    </thead>
                    <tbody id="append_body">

                    </tbody>
                </table>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>



<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">

    $('#file_in_modal7').on('hide.bs.modal', function () {
        window.location.reload();
    })


    var num_global = 0;
    $('input[name="id_arr"]:checked').each(function(){
        num_global = num_global + 1;
    });
    $('#fenpei_num').html(num_global);
    $('#select_num').html(num_global);

    $('#all').on('click', function(){
        var isChecked = $(this).is(':checked');
        if(isChecked){
            //设置全选
            var i_num = 0;
            $('input[name="id_arr"]').each(function(){
                $(this).prop("checked", true);
                i_num = i_num + 1;
            });
            $('#fenpei_num').html(i_num);
            $('#select_num').html(i_num);

        }else{
            //设置不全选
            $('input[name="id_arr"]').each(function(){
                $(this).removeAttr("checked", false);
            });
            $('#fenpei_num').html(0);
            $('#select_num').html(0);

        }
    });

    $('input[name="id_arr"]').on('click', function(){
        var  num = 0;
        $('input[name="id_arr"]:checked').each(function(){
            num = num + 1;
        });
        $('#fenpei_num').html(num);
        $('#select_num').html(num);
    });

    $(document).ready(function () {

        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        $("#file_in").click(function () {
            $("#file_in_modal").modal('show');
        });
        //添加测试用例
        $("#file_submit").click(function () {
            /*var item = $('#item').val();
            if (item == '') {
                alert('请选择项目名称');
                return false;
            }

            var node = $('#node').val();
            if (node == '') {
                alert('请请选择节点名称');
                return false;
            }*/

            var apikey = $('#apikey').val();
            var secret = $('#secret').val();
            var is_json = $('input[name="is_json"]:checked').val();

            var item_id = $('#item_id').val();
            var name = $('#name').val();
            if (name == '') {
                alert('用例名称不能为空');
                return false;
            }
            var url = $('#url').val();
            if (url == '') {
                alert('接口地址不能为空');
                return false;
            }

            var money = $('#money').val();
            if (money == '') {
                alert('测试费用不能为空');
                return false;
            }

            var get_param = $('#get_param').val();
            var post_param = $('#post_param').val();
            var json_result = $('#json_result').val();
            /*if(get_param != ''){
                get_param = encodeURI(get_param);
            }*/
            if(post_param != ''){
                post_param = encodeURI(post_param);
            }

            if (json_result == '') {
                alert('结果取值不能为空');
                return false;
            }
            /*var mode = $('input[name="mode"]:checked').val();*/
            $("#loading").show();


            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/add')}",
                data: {
                    item_id: item_id,
                    name: name,
                    url: url,
                    money: money,
                    get_param: get_param,
                    post_param: post_param,
                    json_result: encodeURI(json_result),
                    apikey: apikey,
                    secret: secret,
                    is_json: is_json
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $("#loading").hide();
                        //$('#file_in_modal').modal('hide');
                       // window.location.reload();
                        alert(data.msg);
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });


        });

        $("#file_submit2").click(function () {

            var id2 = $('#id2').val();
            if(id2 == ''){
                alert('id不能为空');
                return false;
            }

            var apikey2 = $('#apikey2').val();
            var secret2 = $('#secret2').val();
            var is_json2 = $('input[name="is_json2"]:checked').val();
            var item_id2 = $('#item_id2').val();

            var name2 = $('#name2').val();
            if (name2 == '') {
                alert('用例名称不能为空');
                return false;
            }
            var url2 = $('#url2').val();
            if (url2 == '') {
                alert('接口地址不能为空');
                return false;
            }

            var money2 = $('#money2').val();
            if (money2 == '') {
                alert('测试费用不能为空');
                return false;
            }

            var get_param2 = $('#get_param2').val();
            var post_param2 = $('#post_param2').val();
            var json_result2 = $('#json_result2').val();
           /* if(get_param2 != ''){
                get_param2 = encodeURI(get_param2);
            }*/
            if(post_param2 != ''){
                post_param2 = encodeURI(post_param2);
            }
            if (json_result2 == '') {
                alert('结果取值不能为空');
                return false;
            }
            $("#loading").show();


            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/edit')}",
                data: {
                    id2: id2,
                    item_id2: item_id2,
                    name2: name2,
                    url2: url2,
                    money2: money2,
                    get_param2: get_param2,
                    post_param2: post_param2,
                    json_result2: encodeURI(json_result2),
                    apikey2: apikey2,
                    secret2: secret2,
                    is_json2: is_json2
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $("#loading").hide();
                        $('#file_in_modal2').modal('hide');
                        window.location.reload();
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });

        });

        //批量执行
        $('#run_case').on('click', function(){

            var node_search = $('#node_search').val();
            if(node_search == ''){
                alert('请选择相应的节点');
                return false;
            }
            var arr = new Array();
            $('input[name="id_arr"]').each(function(){
                arr.push($(this).val());
            });
            if(arr == ''){
                alert('没有数据');
                return false;
            }
            var len = arr.length;
            var i = 0;
            var div_succ = 0;
            var div_fail = 0;
            $('#div_total').html(len);
            $('#div_num').html(len);
            $('#file_in_modal7').modal('show');
            digui(arr, node_search, i, len, div_succ, div_fail)

        });

        $('#run_case_submit').on('click', function(){
            var node_select = $('#node_select').val();
            if(node_select == ''){
                alert('请选择相应的节点');
                return false;
            }

            var num2 = $('#select_num').html();
            if(num2 == 0){
                alert('请勾选需要的数据');
                return false;
            }

            var arr = new Array();
            $('input[name="id_arr"]:checked').each(function(){
                arr.push($(this).val());
            });
            $("#file_in_modal5").modal('hide');
            $('#file_in_modal7').modal('show');
            var len = arr.length;
            var i = 0;
            digui(arr, node_select, i, len)
            /*for (var index in arr){
                $.ajax({
                    type: 'post',
                    url: "{:U('/Home/TestCase/run_item')}",
                    async:false,
                    data: {
                        id: index,
                        node: node_select,
                    },
                    success: function(data) {
                        if(data.status == 'ok'){
                            var str = '<div class="form-group" style="color:red;line-height:24px;">' + data.msg + '</div>';
                            $('#file_in_form7').append(str);
                        }else{

                        }
                    }
                });
            }*/
        });


    });
    //递归处理
    function digui(arr, node_select, i, len, div_succ, div_fail){
        if(i < len){
            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/run_item')}",
                async:true,
                data: {
                    id: arr[i],
                    i: i,
                    node: node_select,
                    item_id: '<?php echo $input['item_id'] ?>'
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        div_succ++;
                        //var str = '<tr> <td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'+data.name+'</td><td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'+data.res1+'</td> <td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'+data.res2+'</td> </tr>';
                        //$('#append_body').append(str);
                    }else{
                        div_fail++;
                        var str = '<tr> <td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">'+data.name+'</td><td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">'+data.res1+'</td> <td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">'+data.res2+'</td> </tr>';
                        $('#append_body').append(str);
                    }
                    i++;
                    var div_num = len - i;
                    $('#div_num').html(div_num);
                    $('#div_succ').html(div_succ);
                    $('#div_fail').html(div_fail);
                    digui(arr, node_select, i, len, div_succ, div_fail);
                }
            });
        }else{
            $('#div_finish').html('数据处理完成');
            return false;
        }

    }

    //编辑
    function edit(id){
        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getEdit')}",
            data: {
                id: id,
            },
            success: function(data) {
                $('#id2').val(data.id);
                $('#item_id2').val(data.test_item_id);
                $('#apikey2').val(data.apikey);
                $('#secret2').val(data.secret);
               /* $("#item2 option[value='"+data.item +"']").attr("selected",true);
                $("#node2 option[value='"+data.node +"']").attr("selected",true);*/
                //$('#item2').html(data.item);
                //$('#node2').html(data.node);
                $('#name2').val(data.name);
                $('#money2').val(data.money);
                $('#url2').val(data.url);
                if(data.is_json == 1){
                    $('#is_json11').prop('checked', 'checked');
                }else{
                    $('#is_json22').prop('checked', 'checked');
                }
                $('#get_param2').val(data.get_param);
                $('#post_param2').val(data.post_param);
                $('#json_result2').val(data.json_result);
                $('#file_in_modal2').modal('show');
            }
        });
    }
    /**
     * 点击详情页面
     */
    function showPage(id, color){

        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getEdit')}",
            data: {
                id: id,
            },
            success: function(data) {
                if(color == 1){
                    $('#set_value').css('color', 'green');
                    $('#api_value').css('color', 'green');
                }else{
                    $('#set_value').css('color', 'red');
                    $('#api_value').css('color', 'red');
                }
                $('#set_value').text(data.json_result);
                $('#api_value').text(data.api_result);
                $('#file_in_modal3').modal('show');
            }
        });

    }
    /**
     * 执行测试
     */
    function run(id){
        if(id == ''){
            alert('id不能为空');
            return false;
        }
        $('#test_case_id6').val(id);
        $('#file_in_modal6').modal('show');

    }

    $('#run_case_submit6').on('click', function(){
        var node_select6 = $('#node_select6').val();
        if(node_select6 == ''){
            alert('请选择节点');
            return false;
        }
        var test_case_id6 = $('#test_case_id6').val();
        $('#file_in_modal6').modal('hide');
        $("#loading").show();
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/run')}",
            data: {
                id: test_case_id6,
                test_node_id: node_select6
            },
            success: function(data) {
                $("#loading").hide();
                if(data.status == 'ok'){
                    alert(data.msg);
                    $("#loading").hide();
                    window.location.reload();
                }else{
                    alert(data.msg);
                    $("#loading").hide();
                    window.location.reload();
                }
            }
        });

    });

    function showAdmin(id){
        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getAdmin')}",
            data: {
                type: 'item',
                id: id,
            },
            success: function(data) {
                $('#admin_log').html(data.str);
                $('#file_in_modal4').modal('show');
            }
        });
    }

</script>
</body>
</html>
