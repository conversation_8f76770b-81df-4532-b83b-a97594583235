<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('/Home/TestCase/item/team_id/'.$input['test_team_id'])}" class="form-inline" method="get" id="list_form">
                <!--<div class="form-group">
                    <label class="control-label" for="start_time">添加日期：</label>
                    <input type="text" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="text" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>-->
                <!--<div class="form-group">
                    <label class="control-label" for="node_search">节点：</label>
                    <select class="form-control" name="node_search" id="node_search">
                        <option value="">全部</option>
                        <option value="北京" <?php echo '北京' == $input['node_search'] ? 'selected="selected"' : ''?>>北京</option>
                        <option value="深圳" <?php echo '深圳' == $input['node_search'] ? 'selected="selected"' : ''?>>深圳</option>
                    </select>
                </div>-->

                <!-- <div class="form-group">
                     <label class="control-label" for="item_search">项目名称：</label>
                     <input type="text" name="item_search" id="item_search" value="{$input.item_search}" />
                     <select class="form-control" name="item_search" id="item_search">
                         <option value="">全部</option>
                         <option value="邦秒验" <?php echo '邦秒验' == $input['item_search'] ? 'selected="selected"' : ''?>>邦秒验</option>
                         <option value="邦企查" <?php echo '邦秒验' == $input['item_search'] ? 'selected="selected"' : ''?>>邦企查</option>
                         <option value="金盾" <?php echo '金盾' == $input['item_search'] ? 'selected="selected"' : ''?>>金盾</option>
                         <option value="邦信分快捷版" <?php echo '邦信分快捷版' == $input['item_search'] ? 'selected="selected"' : ''?>>邦信分快捷版</option>
                         <option value="邦信分私有云" <?php echo '邦信分私有云' == $input['item_search'] ? 'selected="selected"' : ''?>>邦信分私有云</option>
                     </select>
                 </div>-->

                <!--<div class="form-group">
                    <label class="control-label" for="name_search">用例名称：</label>
                    <input type="text" name="name_search" id="name_search" class="form-control" value="{$input.name_search}"/>
                </div>
                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>-->

                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加用例组</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">用例组名称</th>
                <th style="text-align:center;">操作记录</th>
                <th style="text-align:center;">时间</th>
                <th style="text-align:center;">操作人</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center"><a href="{:U('/Home/TestCase/index/item_id/'.$vo['id'])}" class="btn btn-success btn-sm">{$vo.name}</a></td>
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="showAdmin('<?php echo $vo['id'] ?>')">操作记录</a>
                    </td>
                    <td align="center">{$vo.created_at}</td>
                    <td align="center">{$vo.admin}</td>
                    <!--<td align="center">

                        <switch name="vo.is_test">
                            <case value="1"><a href="javascript:void(0);" class="btn btn-success btn-sm" onclick="showRun('<?php echo $vo['id'] ?>')">已测试</a></case>
                            <case value="2"><a href="javascript:void(0);" class="btn btn-success btn-sm">未测试</a></case>
                        </switch>
                    </td>-->
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">编辑</a>
                        <!--<a href="javascript:void(0);" class="btn btn-primary btn-sm" onclick="run('<?php echo $vo['id'] ?>')">执行</a>-->
                    </td>

                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >添加用例组</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_form" method="post" enctype="multipart/form-data">
                    <!--<div class="form-group">
                        <label for="item">项目名称:</label>
                        <select  name="item" id="item">
                            <option value="" >请选择</option>
                            <option value="邦秒验" >邦秒验</option>
                            <option value="邦企查" >邦企查</option>
                            <option value="金盾" >金盾</option>
                            <option value="邦信分快捷版" >邦信分快捷版</option>
                            <option value="邦信分私有云" >邦信分私有云</option>
                        </select>
                        <input type="text" name="item" id="item" size="30" value="" />
                    </div>-->
                    <!--<div class="form-group">
                        <label for="node">节点名称:</label>
                        <select  name="node" id="node">
                            <option value="" >请选择</option>
                            <option value="北京" >北京</option>
                            <option value="深圳" >深圳</option>
                        </select>
                    </div>-->
                    <div class="form-group">
                        <label for="name">用例组名称:</label>
                        <input type="text" name="name" id="name" size="30" value="" />
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_submit">增加</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_modal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >编辑用例组</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('edit')}" id="file_in_form2" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="id2" id="id2" value="" />
                    <!--<div class="form-group">
                        <label for="item2">项目名称:</label>
                        <select  name="item2" id="item2">
                            <option value="" >请选择</option>
                            <option value="邦秒验" >邦秒验</option>
                            <option value="邦企查" >邦企查</option>
                            <option value="金盾" >金盾</option>
                            <option value="邦信分快捷版" >邦信分快捷版</option>
                            <option value="邦信分私有云" >邦信分私有云</option>
                        </select>
                        <input type="text" name="item2" id="item2" size="30" value="" />
                    </div>-->
                   <!-- <div class="form-group">
                        <label for="node2">节点名称:</label>
                        <select  name="node2" id="node2">
                            <option value="" >请选择</option>
                            <option value="北京" >北京</option>
                            <option value="深圳" >深圳</option>
                        </select>
                    </div>-->
                    <div class="form-group">
                        <label for="name2">用例组名称:</label>
                        <input type="text" name="name2" id="name2" size="30" value="" />
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_submit2">编辑</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_modal3" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >测试详情</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form3" method="post" enctype="multipart/form-data">
                    <label>设置的值:</label>
                    <div class="form-group" style="color:red;line-height:24px;" id="set_value">

                    </div>
                    <label>接口的值:</label>
                    <div class="form-group" style="color:red;line-height:24px;" id="api_value">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal4" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >执行记录</h4>
            </div>
            <div class="modal-body">
                <form action="#" id="file_in_form4" method="post" enctype="multipart/form-data">
                    <div class="form-group" style="color:red;line-height:24px;" id="admin_log">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>



<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {

        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        $("#file_in").click(function () {
            $("#file_in_modal").modal('show');
        });
        //添加测试用例
        $("#file_submit").click(function () {
           /* var item = $('#item').val();
            if (item == '') {
                alert('请选择项目名称');
                return false;
            }*/

            /*var node = $('#node').val();
            if (node == '') {
                alert('请请选择节点名称');
                return false;
            }*/
            var name = $('#name').val();
            if (name == '') {
                alert('用例名称不能为空');
                return false;
            }
            var test_team_id = "<?php echo $input['test_team_id'] ?>";
            $("#loading").show();

            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/add_item')}",
                data: {
                    test_team_id: test_team_id,
                    name: name
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $("#loading").hide();
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });


        });

        $("#file_submit2").click(function () {

            var id2 = $('#id2').val();
            if(id2 == ''){
                alert('id不能为空');
                return false;
            }
            /*var item2 = $('#item2').val();
            if (item2 == '') {
                alert('请选择项目名称');
                return false;
            }*/

            /*var node2 = $('#node2').val();
            if (node2 == '') {
                alert('请请选择节点名称');
                return false;
            }*/
            var name2 = $('#name2').val();
            if (name2 == '') {
                alert('用例名称不能为空');
                return false;
            }

            $("#loading").show();
            $.ajax({
                type: 'post',
                url: "{:U('/Home/TestCase/edit_item')}",
                data: {
                    id2: id2,
                    name2: name2
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        $("#loading").hide();
                        $('#file_in_modal2').modal('hide');
                        window.location.reload();
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });

        });

    });
    //编辑
    function edit(id){
        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getEditItem')}",
            data: {
                id: id,
            },
            success: function(data) {
                $('#id2').val(data.id);
                $("#item2 option[value='"+data.item +"']").attr("selected",true);
                //$("#node2 option[value='"+data.node +"']").attr("selected",true);
                $('#item2').val(data.item);
                $('#name2').val(data.name);
                $('#file_in_modal2').modal('show');
            }
        });
    }
    /**
     * 点击详情页面
     */
    function showPage(id){

        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getEdit')}",
            data: {
                id: id,
            },
            success: function(data) {
                $('#set_value').text(data.json_result);
                $('#api_value').text(data.api_result);
                $('#file_in_modal3').modal('show');
            }
        });

    }
    /**
     * 执行测试
     */
    function run(id){
        if(id == ''){
            alert('id不能为空');
            return false;
        }
        $("#loading").show();
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/run_item')}",
            data: {
                id: id,
            },
            success: function(data) {
                $("#loading").hide();
                if(data.status == 'ok'){
                    alert(data.msg);
                    $("#loading").hide();
                    window.location.reload();
                }else{
                    alert(data.msg);
                    $("#loading").hide();
                    window.location.reload();
                }
            }
        });
    }

    /**
     * 点击查看人员页面
     */
    function showAdmin(id){
        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getAdmin')}",
            data: {
                type: 'item',
                id: id
            },
            success: function(data) {
                $('#admin_log').html(data.str);
                $('#file_in_modal4').modal('show');
            }
        });
    }
    /**
     * 查看执行记录
     */
    function showRun(id){
        if(id == ''){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/TestCase/getRunLog')}",
            data: {
                id: id,
            },
            success: function(data) {
                $('#admin_log').html(data.str);
                $('#file_in_modal4').modal('show');
            }
        });
    }

</script>
</body>
</html>
