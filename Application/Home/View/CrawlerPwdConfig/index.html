<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
        <div id="breadcrumb_search_box">
            <a href="javascript:;" onclick="DHB.INFO.set('{:U('/Home/CrawlerPwdConfig/add')}','添加渠道')"
               class="btn btn-success">添加渠道</a>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" style="padding-bottom: 10px">
                <div class="form-group">
                    <select class="form-control" name="area" id="area" style="width: 120px">
                        <option value="" selected>地区</option>
                        <?php foreach ($crawler_areas as $area) {?>
                        <option value="<?= $area?>" <?= (isset($input['area']) && $input['area'] == $area) ? 'selected' : ''?>><?= $area?></option>
                        <?php }?>
                    </select>
                </div>
                <div class="form-group">
                    <select class="form-control" name="flow_type" id="flow_type" style="width: 120px">
                        <option value="" selected>运营商</option>
                        <foreach name="flow_map" item="val" key="k">
                            <option value="{$k}" <?= ($input['flow_type'] == $k) ? 'selected' : '' ;?>>{$k}</option>
                        </foreach>
                    </select>
                </div>
                <div class="form-group">
                    <select class="form-control" id="channel" name="channel" style="width: 120px">
                        <option value="">渠道名称</option>
                        <?php foreach($channel_list as $channel) {?>
                        <option value="<?= $channel; ?>" <?= (isset($input['channel']) && $input['channel'] == $channel) ? 'selected' : ''?> ><?= $channel; ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group" style="margin-left: 100px">
                    <input type="submit" class="btn btn-primary btn-sm" value="查询">
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">爬虫列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>爬虫名称</th>
                <th>渠道名称</th>
                <th>渠道地址</th>
                <th>更新时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list as $key => $value): ?>
                <tr>
                    <td>{$value['crawler_name']}</td>
                    <td>{$value['channel_name']}</td>
                    <td>{$value['channel_path']}</td>
                    <td>{$value['update_time']}</td>
                    <td>
                        <a href="javascript:;" onclick="DHB.INFO.set('{:U('/Home/CrawlerPwdConfig/edit',['id'=>$value['id']])}','编辑渠道')" class="btn btn-info btn-xs">编 辑</a>
                        <a href="javascript:;" onclick="del({$value['id']}, this)" class="btn btn-danger btn-xs">删 除</a>
                    </td>
                </tr>
                <?php endforeach ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>
</div>
<script type="text/javascript">
    $("#area").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '地区'
    });

    function del(id, obj)
    {
        if (!confirm('渠道删除后无法恢复，是否确定删除？')) {
            return false;
        }
        DHB.ajax({
            url:'/Home/CrawlerPwdConfig/del',
            type:'get',
            data:{"id":id},
            success:function(data){
                $(obj).parent().parent().remove();
            }
        })
    }
</script>
</body>
</html>