<form class="form-horizontal" role="form" id="form_set_id" style="margin-bottom: 160px">
    <div class="form-group">
        <label class="col-sm-2 control-label" for="crawler_name"><span style="color:red;"> * </span> 爬虫名称：</label>
        <div class="col-sm-8" id="div_crawler_name">
            <select name="crawler_name" id="crawler_name" class="form-control">
                <option value="">选择爬虫名称</option>
                <?php foreach($crawler_list as $value) {?>
                <option value="<?= $value; ?>" <?= (isset($info['crawler_name']) && $info['crawler_name'] == $value) ? 'selected readonly' : ''?> ><?= $value; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 渠道名称：</label>
        <div class="col-sm-8">
            <select class="form-control" name="channel_name">
                <option value="">选择渠道名称</option>
                <?php foreach($channel_list as $value) {?>
                <option value="<?= $value; ?>" <?= (isset($info['channel_name']) && $info['channel_name'] == $value) ? 'selected readonly' : ''?> ><?= $value; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 渠道地址：</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="channel_path" value="<?= isset($info['channel_path']) ? $info['channel_path'] : ''?>">
        </div>
    </div>
</form>
<script type="text/javascript">
    $.fn.modal.Constructor.prototype.enforceFocus = function() {};
    //选择所属客户
    $("#crawler_name").select2({
        dropdownParent: $("#div_crawler_name"),
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择爬虫名称',
        width : '100%'
    });
</script>