<form class="form-horizontal" role="form" id="form_set_id" data-info="<?= isset($info) ? $info : '' ?>">
    <div class="form-group">
        <label class="col-sm-2 control-label">账号名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="developer" value="<?= isset($info['developer']) ? $info['developer'] : ''?>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" for="choose_belongs_to_user">所属客户：</label>
        <div class="col-sm-4" id="div_choose_belongs_to_user">
            <select name="name" id="choose_belongs_to_user" class="form-control">
                <option value="">暂不绑定</option>
                <?php foreach($account_list as $account) {?>
                <option value="<?= $account['name']; ?>" <?= (isset($account_info['account_id']) && $account_info['account_id'] == $account['id']) ? 'selected' : ''?> ><?= $account['name']; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="status">
                <option value="1" <?= (isset($info['status']) && $info['status'] == 1) ? 'selected' : ''?>>可用</option>
                <option value="2" <?= (isset($info['status']) && $info['status'] == 2) ? 'selected' : ''?>>禁用</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签约状态</label>
        <div class="col-sm-4">
            <select name="contract_status" id="contract_status" class="form-control">
                <?php foreach($contract_status as $key => $status) {?>
                <option value="<?= $key; ?>" <?= (isset($info['contract_status']) && ($info['contract_status'] == $key)) || (!isset($info['contract_status']) && ($key == 3)) ? 'selected' : '' ?>><?= $status; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">截止日期</label>
            <div class="col-sm-4">
                <input type="date" class="form-control" name="validuntil" value="<?= isset($info['validuntil']) ? $info['validuntil'] : ''?>">
            </div>
        </if>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">限额方式</label>
        <div class="col-sm-10">
            <label class="radio-inline">
                <input type="radio" name="limit_type" value="1" <?= ((isset($info['limit_type']) && $info['limit_type'] == 1) || !isset($info)) ? 'checked' : '';?>>日限额
            </label>
            <label class="radio-inline">
                <input type="radio" name="limit_type" value="2" <?= (isset($info['limit_type']) && $info['limit_type'] == 2) ? 'checked' : '';?>>月限额
            </label>
            <label class="radio-inline">
                <input type="radio" name="limit_type" value="3" <?= (isset($info['limit_type']) && $info['limit_type'] == 3) ? 'checked' : '';?>>年限额
            </label>
            <label class="radio-inline">
                <input type="radio" name="limit_type" value="-1" <?= (isset($info['limit_type']) && $info['limit_type'] == -1) ? 'checked' : '';?>>不限额
            </label>
        </div>
    </div>
    <div class="form-group" id="limit_num" <?= (isset($info['limit_type']) && $info['limit_type'] == -1) ? 'style="display:none"' : '';?>>
        <label class="col-sm-2 control-label">限额用量</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="limit_num" value="<?= isset($info['limit_num']) ? $info['limit_num'] : ''?>" placeholder=""/>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">AKIKEY</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="apikey" id="input_appid" value="<?= isset($info['apikey']) ? $info['apikey'] : ''?>" readonly>
        </div>

        <IF condition="!isset($info['id']) || empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appid',32)">生 成</button>
            </div>
        </IF>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">APPSECRET</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="password" id="input_password" value="<?= isset($info['password']) ? $info['password'] : ''?>" readonly>
        </div>
        <IF condition="!isset($info['id']) || empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_password',64)">生 成</button>
            </div>
        </IF>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">IP白名单</label>
        <div class="col-sm-4">
            <textarea type="text" class="form-control" name="access_ip" rows="10"><?= isset($info['access_ip']) ? $info['access_ip'] : '' ?></textarea>
        </div>
    </div>
</form>
<script type="text/javascript">
    $(function(){
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};
        //避免搜索框不聚焦方法
        $("#choose_belongs_to_user").select2({
            dropdownParent: $("#div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });

        var info = $('#form_set_id').attr('data-info');

        if(!info){
            creat_hash('input_password', 64);
            creat_hash('input_appid', 32);
        }

        $("input:radio[name='limit_type']").on('change', function() {
            if ('-1' == $(this).val()) {
                $("#limit_num").css({
                    display: 'none'
                });
            } else {
                $("#limit_num").css({
                    display: 'block'
                });
            }
        });

    });

    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length)
    {
        if(confirm('修改appid,appsecret会使当前账号的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        }
    }
</script>
