<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<div class="container">
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <include file="Common@Public/nav"/>
    <input type="hidden" name="id" value="{$id}">
    <input type="hidden" name="step_num" value="<?= isset($info['step_num']) ? $info['step_num'] : ''?>">
    <input type="hidden" name="del_step" value="">
    <div class="form-group pull-right" style="margin-right: 200px">
        <a class="btn btn-info" href="{:U('index')}" role="button">返回账号列表</a>
    </div>
    <div class="row col-md-offset-2">
        <form id="form" class="form-horizontal" role="form" action="/Home/RiskList/feeConfig/id/{$id}" method="post" data-info="<?= isset($info) && !empty($info) ? 1 : 0 ?>">
            <div class="form-group">
                <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 计费依据</label>
                <div class="col-sm-4">
                    <label class="radio-inline">
                        <input type="radio" name="fee_basis" value="1" <?= ((isset($info['fee_basis']) && $info['fee_basis'] == 1) || !isset($info) || empty($info))  ? 'checked' : '';?>>查得量
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 计费方式</label>
                <div class="col-sm-4">
                    <label class="radio-inline">
                        <input type="radio" name="fee_method" value="1" <?= (isset($info['fee_method']) && $info['fee_method'] == 1) ? 'checked' : '' ?>>按时间
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="fee_method" value="2" <?= (isset($info['fee_method']) && $info['fee_method'] == 2) ? 'checked' : '' ?>>按用量
                    </label>
                </div>
            </div>
            <div id="fee_time_rule" <?= ((isset($info['fee_method']) && $info['fee_method'] != 1) || !isset($info) || empty($info)) ? 'style="display: none;"' : '' ?>>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 时间计费规则</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="fee_time_rule" value="1" <?= (isset($info['fee_time_rule']) && $info['fee_time_rule'] == 1) ? 'checked' : '';?>>包日
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="fee_time_rule" value="2" <?= (isset($info['fee_time_rule']) && $info['fee_time_rule'] == 2) ? 'checked' : '';?>>包月
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="fee_time_rule" value="3" <?= ((isset($info['fee_time_rule']) && $info['fee_time_rule'] == 3) || !isset($info) || empty($info)) ? 'checked' : '';?>>包年
                        </label>
                    </div>
                </div>
                <?php
                    $time_rule = isset($info['fee_time_rule']) ? (int)$info['fee_time_rule'] : 0;
                    switch ($time_rule) {
                        case 1:
                            $time_unit = '元/日';
                            break;
                        case 2:
                            $time_unit = '元/月';
                            break;
                        case 3:
                            $time_unit = '元/年';
                            break;
                        default:
                            $time_unit = '元/年';
                            break;
                    }
                ?>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 本人查得</label>
                    <div class="col-sm-6">
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="fee_time_own" value="<?= isset($info['fee_price'][0]) ? $info['fee_price'][0] : ''?>">
                        </div>
                        <span class="time_unit">{$time_unit}</span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 联系人查得</label>
                    <div class="col-sm-6">
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="fee_time_input" value="<?= isset($info['fee_price'][1]) ? $info['fee_price'][1] : ''?>">
                        </div>
                        <span class="time_unit">{$time_unit}</span>
                    </div>
                </div>
            </div>
            <div id="fee_amount_rule" <?= ((isset($info['fee_method']) && $info['fee_method'] != 2) || !isset($info) || empty($info)) ? 'style="display: none;"' : '' ?>>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 用量计费规则</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="fee_amount_rule" value="1" <?= ((isset($info['fee_amount_rule']) && $info['fee_amount_rule'] == 1) || !isset($info) || empty($info)) ? 'checked' : '';?>>固定单价
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="fee_amount_rule" value="2" <?= (isset($info['fee_amount_rule']) && $info['fee_amount_rule'] == 2) ? 'checked' : '';?>>阶梯计价
                        </label>
                    </div>
                </div>
                <div id="fee_fixed" <?= (isset($info['fee_amount_rule']) && $info['fee_amount_rule'] != 1) ? 'style="display: none;"' : '';?>>
                    <div class="form-group">
                        <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 本人查得</label>
                        <div class="form-inline col-sm-8">
                            <input type="text" class="form-control form-inline" name="amount_own_price" value="<?= isset($info['fee_price'][0][0]) ? $info['fee_price'][0][0] : '';?>"> /
                            <input type="text" name="amount_own_num" class="form-control" value="<?= isset($info['fee_price'][0][1]) ? $info['fee_price'][0][1] : '';?>"> 元/用量
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 联系人查得</label>
                        <div class="form-inline col-sm-8">
                            <input type="text" class="form-control" name="amount_input_price" value="<?= isset($info['fee_price'][1][0]) ? $info['fee_price'][1][0] : '';?>"> /
                            <input type="text" class="form-control" name="amount_input_num" value="<?= isset($info['fee_price'][1][1]) ? $info['fee_price'][1][1] : '';?>"> 元/用量
                        </div>
                    </div>
                </div>
                <div id="fee_step_rule" <?= ((isset($info['fee_step_rule']) && !$info['fee_step_rule']) || !isset($info) || empty($info)) ? 'style="display: none;"' : '';?>>
                    <div class="form-group">
                        <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 阶梯周期</label>
                        <div class="col-sm-4">
                            <label class="radio-inline">
                                <input type="radio" name="fee_step_rule" value="1" <?= ((isset($info['fee_step_rule']) && $info['fee_step_rule'] == 1) || !isset($info) || empty($info)) ? 'checked' : '';?>>无周期
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="fee_step_rule" value="2" <?= (isset($info['fee_step_rule']) && $info['fee_step_rule'] == 2) ? 'checked' : '';?>>自然日
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="fee_step_rule" value="3" <?= (isset($info['fee_step_rule']) && $info['fee_step_rule'] == 3) ? 'checked' : '';?>>自然月
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="fee_step_rule" value="4" <?= (isset($info['fee_step_rule']) && $info['fee_step_rule'] == 4) ? 'checked' : '';?>>自然年
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 区间价格</label>
                        <div class="col-sm-4">
                            <a id="step_add" href="#fee_step" onclick="fee_step()" data-toggle="modal" class="btn btn-success btn-xs">添加</a>
                        </div>
                    </div>
                    <div class="form-group step_list" <?= (!isset($info['fee_step_rule']) || !$info['fee_step_rule']) ? 'style="display:none"' : ''?>>
                        <div class="col-sm-offset-1 col-sm-6">
                        <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                            <thead class="center">
                            <tr>
                                <th>左边界</th>
                                <th>右边界</th>
                                <th>本人查得</th>
                                <th>联系人查得</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody id="step_list">
                            <?php
                            if (isset($info['fee_step_rule']) && $info['fee_step_rule']) {
                                foreach ($info['fee_price'] as $key => $val) {?>
                            <tr id="step_<?= ($key+1)?>">
                                <td><?= $val[0]?></td>
                                <td><?= $val[1]?></td>
                                <td><?= $val[2]?></td>
                                <td><?= $val[3]?></td>
                                <td><a href="#fee_step" data-toggle="modal" onclick="edit_step(<?= ($key+1)?> ,this)">编辑</a> <a href="javascript:;" onclick="del_step(<?= ($key+1)?>, this)">删除</a></td>
                            </tr>
                            <?php }}?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label"><span style="color:red;"> * </span> 正式计费开始时间</label>
                <div class="col-sm-4">
                    <input type="date" name="start_date" class="form-control" style="width: 300px" value="<?= isset($info['start_date']) ? $info['start_date'] : date('Y-m-d')?>">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label"> 备注</label>
                <div class="col-sm-4">
                    <textarea id="remarks" rows="10" cols="35"><?= isset($info['remarks']) ? $info['remarks'] : '' ?></textarea>
                </div>
            </div>
            <div class="col-sm-offset-2 form-inline">
                <input type="button" onclick="save_fee()" class="btn btn-xm btn-primary" value="保 存" />
                <a href="/Home/RiskList/index.html" class="btn btn-xm btn-danger" style="margin-left: 50px" > 返 回</a>
            </div>
        </form>
    </div>
</div>
<div id="fee_step" class="modal fade" tabindex="-1" style="margin-top: 10%">
    <input type="hidden" name="step_id" value="0">
    <input type="hidden" name="last_flag" value="0">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">区间价格设置</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group col-sm-12">
                        <label for="step_left" class="col-sm-2 control-label">左边界：</label>
                        <div class="col-sm-8">
                            <input type="text" name="step_left" class="form-control">
                        </div>
                    </div>
                    <div class="form-group col-sm-12">
                        <label for="step_right" class="col-sm-2 control-label">右边界：</label>
                        <div class="col-sm-8">
                            <input type="text" name="step_right" class="form-control">
                        </div>
                    </div>
                    <div class="form-group col-sm-12">
                        <label for="step_own" class="col-sm-2 control-label">本人查得：</label>
                        <div class="form-inline col-sm-10">
                            <input type="text" name="step_own_price" class="form-control" style="width: 150px" placeholder=""> / <input type="text" name="step_own_num" class="form-control" style="width: 150px" placeholder=""> 元/用量
                        </div>
                    </div>
                    <div class="form-group col-sm-12">
                        <label for="step_input" class="col-sm-2 control-label">联系人查得：</label>
                        <div class="form-inline col-sm-10">
                            <input type="text" name="step_input_price" class="form-control" style="width: 150px" placeholder=""> / <input type="text" name="step_input_num" class="form-control" style="width: 150px" placeholder=""> 元/用量
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-danger" data-dismiss="modal">取消</button>
                <button onclick="save_step()" class="btn btn-sm btn-primary">确定</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>
<script type="text/javascript">
    var info = $('#form').attr('data-info');
    if(info == 1){
        $('input[type="radio"]').attr('disabled', true);
    }

    $('input[name="fee_method"]').click(function() {
        var fee = $(this).val();
        if (fee == 1) { //时间计费规则
            $('#fee_time_rule').show();
            $('#fee_amount_rule').hide();
        } else if (fee) { //用量计费
            $('#fee_time_rule').hide();
            $('#fee_amount_rule').show();
            $('input[name="fee_amount_rule"][value="1"]').attr('checked', true);
            $('#fee_fixed').show();
            $('#fee_step_rule').hide();
        }
    });

    $('input[name="fee_time_rule"]').click(function() {
        var time_rule = $(this).val();
        if (time_rule == 1) {
            $('.time_unit').html('元/日');
        } else if (time_rule == 2) {
            $('.time_unit').html('元/月');
        } else {
            $('.time_unit').html('元/年');
        }
    });

    $('input[name="fee_amount_rule"]').click(function() {
        var con_rule = $(this).val();
        if (con_rule == 1) { //固定单价
            $('#fee_fixed').show();
            $('#fee_step_rule').hide();
        } else if (con_rule == 2) { //阶梯单价
            $('#fee_fixed').hide();
            $('input[name="fee_step_rule"][value="1"]').attr('checked', true);
            $('#fee_step_rule').show();
        }
    });

    //保存计费配置
    function save_fee()
    {
        var info = check_param();
        if (!info) {
            return false;
        }
        var id = $('input[name="id"]').val();
        DHB.ajax({
            url: "/Home/RiskList/feeConfig/id/"+id,
            type: 'post',
            data: info,
            success: function(data) {
                alert(data.info);
                location.reload();
                return true;
            },
            error: function(data) {
                alert(data.info);
                return false;
            }
        });
    }

    //所有参数判断
    function check_param()
    {
        var fee_basis = parseInt($('input[name="fee_basis"]:checked').val());
        var fee_method = parseInt($('input[name="fee_method"]:checked').val());
        var start_date = $('input[name="start_date"]').val();
        var remarks = $('#remarks').val();
        if (!fee_basis) {
            alert('请选择计费依据');
            return false;
        }
        if (!fee_method) {
            alert('请选择计费方式');
            return false;
        }
        if (fee_method == 1) {
            var info = check_time_param();
        } else if (fee_method == 2) {
            var info = check_amount_param();
        }
        if (!info) {
            return false;
        }
        if (remarks.length > 1000) {
            alert('备注不可超过1000个字符');
            return false;
        }
        var base = {"fee_basis": fee_basis, "fee_method": fee_method, "start_date": start_date, "remarks": remarks};
        var param = $.extend(base, info);
        return param;
    }

    //按时间计费规则参数
    function check_time_param()
    {
        var fee_time_rule = parseInt($('input[name="fee_time_rule"]:checked').val());
        var fee_time_own = $('input[name="fee_time_own"]').val();
        var fee_time_input = $('input[name="fee_time_input"]').val();
        if (!fee_time_rule) {
            alert('请选择时间计费规则');
            return false;
        }
        var reg = /^\d+(\.\d{2})?$/;
        if (!reg.test(fee_time_own)) {
            alert('请输入本人查得价格，包含两位小数');
            return false;
        }
        if (!reg.test(fee_time_input)) {
            alert('请输入联系人查得价格，包含两位小数');
            return false;
        }
        return {"fee_time_rule": fee_time_rule, "fee_time_own": fee_time_own, "fee_time_input": fee_time_input};
    }

    //用量计费判断
    function check_amount_param()
    {
        var fee_amount_rule = parseInt($('input[name="fee_amount_rule"]:checked').val());
        if (!fee_amount_rule) {
            alert('请选择用量计费规则');
            return false;
        }
        if (fee_amount_rule == 1) { //固定计价
            var fixed = check_fixed_param();
            if (!fixed) {
                return false;
            }
            return $.extend({"fee_amount_rule": fee_amount_rule}, fixed);
        } else if (fee_amount_rule == 2) { //阶梯计价
            var fee_step_rule = parseInt($('input[name="fee_step_rule"]:checked').val());
            if (!fee_step_rule) {
                alert('请选择阶梯周期');
                return false;
            }
            var fee_price = check_step_param();
            if (!fee_price) {
                return false;
            }
            return {"fee_amount_rule": fee_amount_rule, 'fee_step_rule' : fee_step_rule, 'fee_price': fee_price};
        }
        return false;
    }

    //固定单价
    function check_fixed_param()
    {
        var amount_own_price = parseInt($('input[name="amount_own_price"]').val());
        var amount_own_num = parseInt($('input[name="amount_own_num"]').val());
        var amount_input_price = parseInt($('input[name="amount_input_price"]').val());
        var amount_input_num = parseInt($('input[name="amount_input_num"]').val());
        if (amount_own_num <= 0 || amount_own_price < 0) {
            alert('请输入正确的本人查得固定单价');
            return false;
        }
        if (amount_input_price < 0 || amount_input_num <= 0) {
            alert('请输入正确的联系人查得固定单价');
            return false;
        }
        return {"amount_own_num": amount_own_num, "amount_own_price": amount_own_price, "amount_input_price": amount_input_price, "amount_input_num": amount_input_num};
    }

    //区间价格判断
    function check_step_param()
    {
        var one = parseInt($('#step_list tr:eq(0) td:eq(0)').html());
        var last_right = parseInt($('#step_list').find('tr:last').children('td').eq(1).html());
        if (one != 1) {
            alert('第一行左边界必须为1');
            return false;
        }
        if (last_right != -1) {
            alert('最后一行右边界必须为-1');
            return false;
        }
        var tr_list = $("#step_list").children("tr");
        var fee_price = [];
        for (var i = 0; i < tr_list.length; i++) {
            var pre = parseInt(tr_list.eq(i).children('td').eq(1).html());
            var next = parseInt(tr_list.eq(i+1).children('td').eq(0).html());
            if (i < (tr_list.length - 1) && next != (pre + 1)) {
                alert('前一行的右边界必须与后一行的左边界相差1');
                return false;
            }
            var td_list = [];
            tr_list.eq(i).find("td").each(function() {
                if ($(this).text() != '编辑 删除') {
                    td_list.push($.trim($(this).text()));
                }
            });
            fee_price.push(td_list);
        }
        if (!fee_price) {
            alert('请添加区间价格');
            return false;
        }
        return fee_price;
    }

    //添加前处理
    function fee_step()
    {
        $('.modal input').val('');
        $('input[name="step_left"]').attr('readonly', true);
        var last_right = $('#step_list').find('tr:last').children('td').eq(1).html();
        if (last_right == undefined || last_right == -1) {
            $('input[name="step_left"]').val(1);
        } else {
            var num = parseInt(last_right)+1;
            $('input[name="step_left"]').val(num);
        }
    }

    //编辑前处理
    function edit_step(id, obj)
    {
        $('.modal input').val('');
        var td_list = $(obj).parent().parent();
        var step_left = parseInt(td_list.children('td').eq(0).html());
        var step_right = parseInt(td_list.children('td').eq(1).html());
        var step_own = td_list.children('td').eq(2).html().split("/");
        var step_input = td_list.children('td').eq(3).html().split("/");
        $('input[name="step_left"]').val(step_left);
        $('input[name="step_right"]').val(step_right);
        $('input[name="step_own_price"]').val(step_own[0]);
        $('input[name="step_own_num"]').val(step_own[1]);
        $('input[name="step_input_price"]').val(step_input[0]);
        $('input[name="step_input_num"]').val(step_input[1]);
        $('input[name="step_id"]').val(id);
        var last_flag = (step_right == -1) ? 1 : 0;
        $('input[name="last_flag"]').val(last_flag);

    }

    //保存区间价格
    function save_step()
    {
        var id = parseInt($('input[name="step_id"]').val());
        var last_flag = $('input[name="last_flag"]').val()
        var step_left = parseInt($('input[name="step_left"]').val());
        var step_right = parseInt($('input[name="step_right"]').val());
        var step_own_num = parseInt($('input[name="step_own_num"]').val());
        var step_own_price = parseInt($('input[name="step_own_price"]').val());
        var step_input_num = parseInt($('input[name="step_input_num"]').val());
        var step_input_price = parseInt($('input[name="step_input_price"]').val());
        var last_right = parseInt($('#step_list').find('tr:last').children('td').eq(1).html());
        if (last_right == -1 && !id && !last_flag) {
            $('#fee_step').modal('hide');
            alert('已设置了右边界无穷大，不可再添加');
            return false;
        }
        if (step_left <= 0 || step_left == NaN) {
            alert('请输入正确的左边界数值');
            return false;
        }
        if ((step_right <= 0 && step_right != -1) || step_right == NaN) {
            alert('请输入正确的右边界数值');
            return false;
        }
        if (step_left >= step_right && step_right != -1) {
            alert('请输入正确的右边界数值');
            return false;
        }
        if (step_own_num <= 0 || step_own_price < 0 || step_own_num == NaN || step_own_price == NaN) {
            alert('请输入正确的本人查得单价');
            return false;
        }
        if (step_input_price < 0 || step_input_num <= 0 || step_input_price == NaN || step_input_num == NaN) {
            alert('请输入正确的联系人查得单价');
            return false;
        }
        var tr_length = $("#step_list").children("tr").length;
        var i = id ? id : parseInt(tr_length) + 1;
        $('#fee_step').modal('hide');
        var html = '<td>'+step_left+
                   '</td><td>'+step_right+
                   '</td><td>'+step_own_price+'/'+step_own_num+
                   '</td><td>'+step_input_price+'/'+step_input_num+
                   '</td><td><a href="#fee_step" data-toggle="modal" onclick="edit_step('+i+',this)">编辑</a> <a href="javascript:;" onclick="del_step('+i+',this)">删除</a></td>';
        if (!id) {
            $('.step_list').show();
            $('#step_list').append('<tr id="step_'+i+'">'+html+'</tr>');
        } else {
            $('#step_'+id).html(html);
        }
    }

    //删除
    function del_step(id, obj)
    {
        if (!confirm('确定要删除该区间设置吗？')) {
            return false;
        }
        $('#step_'+id).remove();
    }

</script>
</body>
</html>