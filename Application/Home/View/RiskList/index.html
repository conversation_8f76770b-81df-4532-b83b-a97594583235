<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row-first { margin-bottom: 10px; }
        .row-first label, .row-second label { margin-left: 10px;}
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
        <div id="breadcrumb_search_box">
            <a href="javascript:;" onclick="DHB.INFO.set('{:U('/Home/RiskList/add')}','添加风险名单账号')"
               class="btn btn-success">添加风险名单账号</a>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" style="padding-bottom: 10px">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label">开通时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"
                               value="<?= (!isset($input['begin']) || !$input['begin']) ? '' : $input['begin']; ?>"/>
                        <input type="date" name="begin_e" id="time_begin_e" class="form-control"
                               value="<?= (!isset($input['begin_e']) || !$input['begin_e']) ? '' : $input['begin_e']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label">到期时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control"
                               value="<?= (!isset($input['end']) || !$input['end']) ? '' : $input['end']; ?>"/>
                        <input type="date" name="end_e" id="time_end_e" class="form-control"
                               value="<?= (!isset($input['end_e']) || !$input['end_e']) ? '' : $input['end_e']; ?>"/>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li>
                                <input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询">
                            </li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="row-second">
                    <div class="form-group">
                        <label class="control-label">账号状态</label>&nbsp;
                        <select class="form-control" name="status" id="status">
                            <option value="" selected>全部</option>
                            <option value="1"
                            <?= ($input['status'] === '1') ? 'selected' : '' ;?>>可用</option>
                            <option value="2"
                            <?= ($input['status'] === '2') ? 'selected' : '' ;?>>禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label">签约状态</label>&nbsp;
                        <select class="form-control" name="contract_status" id="contract_status">
                            <option value="" selected>全部</option>
                            <foreach name="contract_status" item="val" key="k">
                                <option value="{$k}" <?= ($input['contract_status'] == $k) ? 'selected' : '' ;?>>{$val}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label">选择客户</label>&nbsp;
                        <select class="form-control" id="account_id" name="account_id">
                            <option value="">全部</option>
                            <?php foreach($account_list as $account) {?>
                            <option value="<?= $account['id']; ?>" <?= (isset($input['account_id']) && $input['account_id'] == $account['id']) ? 'selected' : ''?> ><?= $account['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label">选择账号</label>&nbsp;
                        <select class="form-control" id="choose_user_sel" name="user_id">
                            <option value="">全部</option>
                            <foreach name="user_list" item="vo">
                                <option value="{$vo['id']}" <?= (isset($input['user_id']) && ($input['user_id'] == $vo['id'])) ? 'selected' : ''?>>{$vo['developer']}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="id">账号ID</label>&nbsp;
                        <input type="text" name="id" id="id" value="<?= isset($input['id']) ? $input['id'] : ''; ?>" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="apikey">APIKEY</label>&nbsp;
                        <input type="text" name="apikey" id="apikey" value="<?= isset($input['apikey']) ? $input['apikey'] : ''; ?>" class="form-control">
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">账号列表</h3></div>
        <table class="table table-hover table-bordered" style="table-layout: fixed">
            <thead>
            <tr>
                <th>账号ID</th>
                <th>账号名称</th>
                <th>所属客户</th>
                <th>账号状态</th>
                <th>签约状态</th>
                <th>APIKEY</th>
                <th>Time</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list as $key => $value): ?>
            <tr>
                <td>{$value['id']}</td>
                <td style="word-wrap: break-word">{$value['developer']}</td>
                <td>{$value['account_name']}</td>
                <td><?= ($value['status'] == 1) ? '可用' : '禁用' ;?></td>
                <td>
                    <?php
                        if(!empty($contract_status[$value['contract_status']])){
                            echo $contract_status[$value['contract_status']];
                        }
                    ?>
                </td>
                <td style="word-wrap: break-word">{$value['apikey']}</td>
                <td>
                    <div>start : <?= $value['created_at'] ? date('Y-m-d H:i:s', $value['created_at']) : '' ?></div>
                    <div>&nbsp;end : <?= $value['validuntil'] ? date('Y-m-d H:i:s', $value['validuntil']) : '' ?></div>
                </td>
                <td>
                    <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'/Home/RiskList/edit\',array(\'id\'=>$value[\'id\']))}','编辑风险名单账号')" class="btn btn-info btn-xs">编辑</a>
                    <a href="{:U('/Home/RiskList/feeConfig', array('id' => $value['id']))}" class="btn btn-primary btn-xs">计费配置</a>
                </td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>

    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>

<script type="text/javascript">
$(function () {
    $("#choose_user_sel").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择账号',
        width: '200px'
    });

    $("#account_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择客户',
        width: '200px'
    });

    // export file
    $("#file_export").click(function () {
        if (!checkTime()) {
            return false;
        }
        var params = genParamsForFile();
        var url_export = '/Home/RiskList/download' + params;
        $.fileDownload(url_export);
        return false;
    });
});
// 为导出文件生成参数
function genParamsForFile() {
    var params = '';
    var time_begin = $('#time_begin').val();
    var time_end = $('#time_end').val();
    var time_begin_e = $('#time_begin_e').val();
    var time_end_e = $('#time_end_e').val();
    var status = $('#status').val();
    var contract_status = $('#contract_status').val();
    var choose_user_sel = $('#choose_user_sel').val();
    var id = $('#id').val();
    var apikey = $('#apikey').val();
    var account_id = $('#account_id').val();

    if (time_begin) {
        params += '&begin=' + time_begin;
    }
    if (time_end) {
        params += '&end=' + time_end;
    }
    if (time_begin_e) {
        params += '&begin_e=' + time_begin_e;
    }
    if (time_end_e) {
        params += '&end_e=' + time_end_e;
    }
    if (choose_user_sel) {
        params += '&user_id=' + choose_user_sel;
    }
    if (contract_status) {
        params += '&contract_status=' + contract_status;
    }
    if (status) {
        params += '&status=' + status;
    }
    if (id) {
        params += '&id=' + id;
    }
    if (apikey) {
        params += '&apikey=' + apikey;
    }
    if (account_id) {
        params += '&account_id='+account_id;
    }

    // tidy url
    if (params) {
        params = params.replace('&', '?');
    }
    return params;
}

function checkTime() {
    // get begin time and end time
    var time_begin = $('#time_begin').val();
    var time_end = $('#time_end').val();
    var today_str = (new Date()).toDateString();

    // change time format for firefox
    time_end = time_end.replace(/\-/g, '\/');
    time_begin = time_begin.replace(/\-/g, '\/');

    // set default time
    if (!time_begin) {
        time_begin = today_str;
    }

    if (!time_end) {
        time_end = today_str;
    }

    // check time
    // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
    var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
    if (time_diff < 0) {
        alert('开始时间必须小于结束时间');
        return false;
    }

    // calculate the days between begin and end
    var day_diff = Math.floor(time_diff / 8.64e7);

    //  the time should less than 31
    if (day_diff <= 364) {
        return true;
    } else {
        alert('单次查询时间范围不能超过365天');
        return false;
    }
}
</script>
</body>
</html>
