<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="product_id_search">产品id：</label>
                    <input type="input" name="product_id_search" id="product_id_search" class="form-control" value="{$input.product_id_search}"/>
                </div>

                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加</button>
                </div>

            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">产品id</th>
                <th style="text-align:center;">值类型</th>
                <th style="text-align:center;">取值</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.pid}</td>
                    <td align="center">
                        <switch name="vo.type">
                            <case value="0"><nobr>未知</nobr></case>
                            <case value="1"><nobr>等于</nobr></case>
                            <case value="2"><nobr>大于</nobr></case>
                            <case value="3"><nobr>小于</nobr></case>
                            <case value="4"><nobr>区间</nobr></case>
                            <case value="5"><nobr>大于等于</nobr></case>
                            <case value="5"><nobr>小于等于</nobr></case>
                        </switch>
                    </td>
                    <td align="center">{$vo.value}</td>
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">编辑</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>

<div class="modal fade" id="file_in_modal2">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">编辑</h4>
            </div>
            <div class="modal-body" style="font-size: 12px;">
                <form class="form-horizontal" id="formCar2" method="post" >

                    <input type="hidden" id="id2" name="id2" value="">

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">产品id<span style="color:red">*</span>：</label>
                            <input type="input" id="pid2" name="pid2" maxlength="20" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">取值类型<span style="color:red">*</span>：</label>
                            <select name="type2" id="type2">
                                <option value="">请选择</option>
                                <option value="1">等于</option>
                                <option value="2">大于</option>
                                <option value="3">小于</option>
                                <option value="4">区间</option>
                                <option value="5">大于等于</option>
                                <option value="6">小于等于</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11" id="value_eq2" style="display: none;">
                            <label class="col-sm-3 control-label" style="text-align:left">取值<span style="color:red">*</span>：</label>
                            <input type="input" id="val2" name="val2" maxlength="11" value="" size="20">
                        </div>
                    </div>

                    <div class="form-group" id="value_in2" style="display: none;">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">取值范围<span style="color:red">*</span>：</label>
                            <input type="input" id="val_min2" name="val_min2" maxlength="11" value="" size="20">
                            -
                            <input type="input" id="val_max2" name="val_max2" maxlength="11" value="" size="20">
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button"  class="btn btn-primary btn-sm btn-submit" id="btn-dis2">提交</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div class="modal fade" id="file_in_modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">添加</h4>
            </div>
            <div class="modal-body" style="font-size: 12px;">
                <form class="form-horizontal" id="formCar" method="post" >

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">产品id<span style="color:red">*</span>：</label>
                            <input type="input" id="pid" name="pid" maxlength="20" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">取值类型<span style="color:red">*</span>：</label>
                            <select name="type" id="type">
                                <option value="">请选择</option>
                                <option value="1">等于</option>
                                <option value="2">大于</option>
                                <option value="3">小于</option>
                                <option value="4">区间</option>
                                <option value="5">大于等于</option>
                                <option value="6">小于等于</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11" id="value_eq" style="display: none;">
                            <label class="col-sm-3 control-label" style="text-align:left">取值<span style="color:red">*</span>：</label>
                            <input type="input" id="val" name="val" maxlength="11" value="" size="20">
                        </div>
                    </div>

                    <div class="form-group" id="value_in" style="display: none;">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">取值范围<span style="color:red">*</span>：</label>
                            <input type="input" id="val_min" name="val_min" maxlength="11" value="" size="20">
                            -
                            <input type="input" id="val_max" name="val_max" maxlength="11" value="" size="20">
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button"  class="btn btn-primary btn-sm btn-submit" id="btn-dis">提交</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">

    function del(id){
        if(id == ''){
            return false;
        }
        if(!confirm('确定要删除吗')){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/Server/del')}",
            data: {
                id: id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    alert(data.msg);
                    window.location.reload();
                }else{
                    alert(data.msg);
                }

            }
        });
    }

    function edit(id){
        if(id == ''){
            return false;
        }
        $('#id2').val(id);
        $.ajax({
            type: 'post',
            url: "{:U('/Home/ConfigProduct/getEdit')}",
            data: {
                id2: id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    $('#pid2').val(data.data.pid);
                    $("#type2 option[value='"+data.data.type+"']").prop("selected",true);

                    if(data.data.type == 4){
                        $('#value_in2').css({display:'block'});
                        $('#value_eq2').css({display:'none'});
                        $('#val_min2').val(data.data.val_min);
                        $('#val_max2').val(data.data.val_max);
                        $('#val2').val('');
                    }else{
                        $('#value_in2').css({display:'none'});
                        $('#value_eq2').css({display:'block'});
                        $('#val2').val(data.data.value);
                        $('#val_min2').val('');
                        $('#val_max2').val('');
                    }
                    $("#file_in_modal2").modal('show');
                }else{
                    alert(data.msg);
                }

            }
        });

    }

    $(document).ready(function () {

        var type = $('#type').val();
        if(type == 4){
            $('#value_in').css({display:'block'});
            $('#value_eq').css({display:'none'});
        }else{
            $('#value_in').css({display:'none'});
            $('#value_eq').css({display:'block'});
        }

        $('#type').on('change', function(){
            var v = $(this).val();
            if(v == 4){
                $('#value_in').css({display:'block'});
                $('#value_eq').css({display:'none'});
            }else{
                $('#value_in').css({display:'none'});
                $('#value_eq').css({display:'block'});
            }
        });

        $('#type2').on('change', function(){
            var v = $(this).val();
            if(v == 4){
                $('#value_in2').css({display:'block'});
                $('#value_eq2').css({display:'none'});
            }else{
                $('#value_in2').css({display:'none'});
                $('#value_eq2').css({display:'block'});
            }
        });


        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        $('#file_in').on('click', function(){
            $("#file_in_modal").modal('show');
        });
        $('#btn-dis').on('click', function(){
            var pid = $('#pid').val();
            if (pid == ''){
                alert('产品id不能为空');
                return false;
            }
            if(isNaN(pid)){
                alert('产品id必须为数字类型');
                return false;
            }
            var type = $('#type').val();
            if (type == ''){
                alert('取值类型不能为空');
                return false;
            }
            if(type == 4){
                var val_min = $('#val_min').val();
                if (val_min == ''){
                    alert('最小值不能为空');
                    return false;
                }
                if(isNaN(val_min)){
                    alert('最小值必须为数字类型');
                    return false;
                }
                var val_max = $('#val_max').val();
                if (val_max == ''){
                    alert('最大值不能为空');
                    return false;
                }
                if(isNaN(val_max)){
                    alert('最大值必须为数字类型');
                    return false;
                }
            }else{
                var val = $('#val').val();
                if (val == ''){
                    alert('取值不能为空');
                    return false;
                }
                if(isNaN(val)){
                    alert('取值必须为数字类型');
                    return false;
                }
            }

            var formCar = new FormData($("#formCar")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/ConfigProduct/add",
                data:formCar,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });

        $('#btn-dis2').on('click', function(){
            var id = $('#id2').val();
            if(id == ''){
                return false;
            }

            var pid = $('#pid2').val();
            if (pid == ''){
                alert('产品id不能为空');
                return false;
            }
            if(isNaN(pid)){
                alert('产品id必须为数字类型');
                return false;
            }
            var type = $('#type2').val();
            if (type == ''){
                alert('取值类型不能为空');
                return false;
            }
            if(type == 4){
                var val_min = $('#val_min2').val();
                if (val_min == ''){
                    alert('最小值不能为空');
                    return false;
                }
                if(isNaN(val_min)){
                    alert('最小值必须为数字类型');
                    return false;
                }
                var val_max = $('#val_max2').val();
                if (val_max == ''){
                    alert('最大值不能为空');
                    return false;
                }
                if(isNaN(val_max)){
                    alert('最大值必须为数字类型');
                    return false;
                }
            }else{
                var val = $('#val2').val();
                if (val == ''){
                    alert('取值不能为空');
                    return false;
                }
                if(isNaN(val)){
                    alert('取值必须为数字类型');
                    return false;
                }
            }

            var formCar = new FormData($("#formCar2")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/ConfigProduct/edit",
                data:formCar,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });

    });

</script>
</body>
</html>
