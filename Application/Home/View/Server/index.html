<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="owner_search">IP：</label>
                    <input type="input" name="ip_search" id="ip_search" class="form-control" value="{$input.ip_search}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="owner_search">服务器所有者：</label>
                    <input type="input" name="owner_search" id="owner_search" class="form-control" value="{$input.owner_search}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="owner_search">服务器所属项目：</label>
                    <input type="input" name="product_search" id="product_search" class="form-control" value="{$input.product_search}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="owner_search">服务器类型：</label>
                    <select id="type_search" name="type_search">
                        <option value="1">深圳线上</option>
                        <option value="2">北京线上</option>
                        <option value="3">无重要项目的线上机器</option>
                    </select>
                </div>

                <input type="hidden" id="search_id" name="search_id" value="1" />
                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加</button>
                </div>

            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">IP</th>
                <th style="text-align:center;">物理机名称</th>
                <th style="text-align:center;">服务器名称</th>
                <th style="text-align:center;">服务器所有者</th>
                <th style="text-align:center;">服务器用途</th>
                <th style="text-align:center;">所属项目</th>
                <th style="text-align:center;">服务器类型</th>
                <th style="text-align:center;">CPU配置(核数)</th>
                <th style="text-align:center;">内存容量</th>
                <th style="text-align:center;">root硬盘容量</th>
                <th style="text-align:center;">硬盘容量</th>
<!--                <th style="text-align:center;">cpu负载</th>-->
<!--                <th style="text-align:center;">cpu峰值</th>-->
<!--                <th style="text-align:center;">cpu空闲</th>-->
<!--                <th style="text-align:center;">内存剩余</th>-->
<!--                <th style="text-align:center;">内存剩余峰值</th>-->
<!--                <th style="text-align:center;">物理机内存</th>-->
<!--                <th style="text-align:center;">物理机剩余内存</th>-->
                <th style="text-align:center;">是否回收</th>
                <th style="text-align:center;">备注</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody id="tbody">
            <volist name="data" id="vo">
                <tr id="data_{$vo.id}">
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.ip}</td>
                    <td align="center">{$vo.real_name}</td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{$vo.owner}</td>
                    <td align="center">{$vo.useto}</td>
                    <td align="center">{$vo.product}</td>
                    <td align="center">
                        <switch name="vo.type">
                            <case value="0"><nobr>未知</nobr></case>
                            <case value="1"><nobr>深圳线上</nobr></case>
                            <case value="2"><nobr>北京线上</nobr></case>
                            <case value="3"><nobr>无重要项目的线上机器</nobr></case>
                        </switch>
                    </td>
                    <td align="center">{$vo.cpu}</td>
                    <td align="center">{$vo.mem}</td>
                    <td align="center">{$vo.disk_root}</td>
                    <td align="center">{$vo.disk}</td>


 <!--                    <td align="center">{$vo.cpu_use}</td>-->
<!--                    <td align="center">{$vo.cpu_high}</td>-->
<!--                    <td align="center">{$vo.cpu_idle}</td>-->
<!--                    <td align="center">{$vo.mem_rem}</td>-->
<!--                    <td align="center">{$vo.mem_high}</td>-->
<!--                    <td align="center">{$vo.real_mem}</td>-->
<!--                    <td align="center">{$vo.real_mem_rem}</td>-->
                    <td align="center" id="status_{$vo.id}">
                        <switch name="vo.status">
                            <case value="0"><nobr>未知</nobr></case>
                            <case value="1"><nobr>正常</nobr></case>
                            <case value="-1"><nobr>回收</nobr></case>
                        </switch>
                    </td>
                    <td align="center">{$vo.notes}</td>
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">编辑</a>
                        <?php
                        if($vo['status'] == 1 || $vo['status']== 0){ ?>
                        <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" onclick="del('<?php echo $vo['id'] ?>')">删除</a>
                        <?php }else { ?>
                        <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" disabled >删除</a>
                        <?php } ?>

                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>

<div class="modal fade" id="file_in_modal2">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">编辑</h4>
            </div>
            <div class="modal-body" style="font-size: 12px;">
                <form class="form-horizontal" id="formCar2" method="post" >
                    <input type="hidden" id="id2" name="id2" value="" />
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">服务器类型<span style="color:red">*</span>：</label>
                            <input type="radio" id="type1" name="type2" value="1" checked/>
                            深圳线上
                            &nbsp;&nbsp;
                            <input type="radio" id="type2" name="type2" value="2"/>
                            北京线上
                            &nbsp;&nbsp;
                            <input type="radio" id="type3" name="type2" value="3"/>
                            无重要项目的线上机器
                            &nbsp;&nbsp;
                            <!--<input type="radio" id="type0" name="type2" value="0"/>
                            未知
                            &nbsp;&nbsp;-->
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">正常or回收<span style="color:red">*</span>：</label>
                            <input type="radio" id="status1" name="status2" value="1" checked/>
                            正常
                            &nbsp;&nbsp;
                            <input type="radio" id="status2" name="status2" value="-1"/>
                            回收
                           <!-- &nbsp;&nbsp;
                            <input type="radio" id="status0" name="status2" value="0"/>
                            未知-->
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">IP<span style="color:red">*</span>：</label>
                            <span class="col-sm-2">ip地址:</span>
                            <input class="col-sm-2" type="input" id="ip2" name="ip2" maxlength="20" value="">
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">机器名称<span style="color:red">*</span>：</label>
                            <span class="col-sm-2">物理机名称:</span>
                            <input class="col-sm-2" type="input" id="real_name2" name="real_name2" value="">
                            &nbsp;&nbsp;
                            <span class="col-sm-2">服务器名称:</span>
                            <input class="col-sm-2" type="input" id="name2" name="name2" value="">

                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">机器配置：</label>
                            <span class="col-sm-2">cpu(核数):</span>
                            <input class="col-sm-2" type="input" id="cpu2" name="cpu2" maxlength="11" value="" size="5">
                            &nbsp;&nbsp;
                            <span class="col-sm-2">内存容量:</span>
                            <input class="col-sm-2" type="input" id="mem2" name="mem2" maxlength="11" value="" size="5">

                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left"></label>
                            <span class="col-sm-2">root硬盘容量:</span>
                            <input class="col-sm-2" type="input" id="disk_root2" name="disk_root2" maxlength="11" value="" size="5">
                            &nbsp;&nbsp;
                            <span class="col-sm-2">硬盘容量:</span>
                            <input class="col-sm-2" type="input" id="disk2" name="disk2" maxlength="11" value="" size="5">
                            &nbsp;&nbsp;
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">服务器信息<span style="color:red">*</span>：</label>
                            <span class="col-sm-2">服务器所有者:</span>
                            <input class="col-sm-2" type="input" id="owner2" name="owner2" value="" size="15">
                            <span class="col-sm-2">服务器用途:</span>
                            <input class="col-sm-2" type="input" id="useto2" name="useto2" value="" size="15">

                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left"></label>
                            <span class="col-sm-2">服务器所属项目:</span>
                            <input class="col-sm-2" type="input" id="product2" name="product2" value="" size="15">
                            <span class="col-sm-2">服务器归属项目：</span>
                            <select class="col-sm-2" id="father_id2" name="father_id">
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">备注：</label>
                            <textarea id="notes2" name="notes2" cols="60" rows="2" maxlength="200"></textarea>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button"  class="btn btn-primary btn-sm btn-submit" id="btn-dis2">提交</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div class="modal fade" id="file_in_modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">添加</h4>
            </div>
            <div class="modal-body" style="font-size: 12px;">
                <form class="form-horizontal" id="formCar" method="post" >
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">服务器类型<span style="color:red">*</span>：</label>
                            <input type="radio" name="type" value="1" checked/>
                            深圳线上
                            &nbsp;&nbsp;
                            <input type="radio" name="type" value="2"/>
                            北京线上
                            &nbsp;&nbsp;
                            <input type="radio" name="type" value="3"/>
                            无重要项目的线上机器
                            &nbsp;&nbsp;
                            <!--<input type="radio" name="type" value="0"/>
                            未知
                            &nbsp;&nbsp;-->
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">正常or回收<span style="color:red">*</span>：</label>
                            <input type="radio" name="status" value="1" checked/>
                            正常
                            &nbsp;&nbsp;
                            <input type="radio" name="status" value="-1"/>
                            回收
                            <!--&nbsp;&nbsp;
                            <input type="radio" name="status" value="0"/>
                            未知-->
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">IP<span style="color:red">*</span>：</label>
                            <span class="col-sm-2">ip地址:</span>
                            <input class="col-sm-2" type="input" id="ip" name="ip" maxlength="20" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">机器名称<span style="color:red">*</span>：</label>
                            <span class="col-sm-2">物理机名称:</span>
                            <input class="col-sm-2" type="input" id="real_name" name="real_name" value="">
                            &nbsp;&nbsp;
                            <span class="col-sm-2">服务器名称:</span>
                            <input class="col-sm-2" type="input" id="name" name="name" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">机器配置：</label>
                            <span class="col-sm-2"> cpu(核数):</span>
                            <input  class="col-sm-2" type="input" id="cpu" name="cpu" maxlength="11" value="" size="5">
                            &nbsp;&nbsp;
                            <span class="col-sm-2">内存容量:</span>
                            <input class="col-sm-2" type="input" id="mem" name="mem" maxlength="11" value="" size="5">
                            &nbsp;
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left"></label>

                            <span class="col-sm-2">root硬盘容量:</span>
                            <input class="col-sm-2" type="input" id="disk_root" name="disk_root" maxlength="11" value="" size="5">
                            &nbsp;&nbsp;
                            <span class="col-sm-2">硬盘容量:</span>
                            <input class="col-sm-2" type="input" id="disk" name="disk" maxlength="11" value="" size="5">
                            &nbsp;&nbsp;
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">服务器信息<span style="color:red">*</span>：</label>
                            <span class="col-sm-2">服务器所有者:</span>
                            <input class="col-sm-2" type="input" id="owner" name="owner" value="" size="15">
                            <span class="col-sm-2">服务器用途:</span>
                            <input class="col-sm-2" type="input" id="useto" name="useto" value="" size="15">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left"></label>
                            <span class="col-sm-2">服务器所属项目:</span>
                            <input class="col-sm-2" type="input" id="product" name="product" value="" size="15">
                            <span class="col-sm-2">服务器归属项目：</span>
                            <select class="col-sm-2" id="father_id" name="father_id">
                                <option value="0" checked>无</option>
                                <option value="200">邦秒验</option>
                                <option value="210">邦信分-通信字段</option>
                                <option value="615">号码风险等级</option>
                                <option value="1000">邦信分-通信评分</option>
                                <option value="10000">号码分</option>
                                <option value="1100">日志系统</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-2 control-label" style="text-align:left">备注：</label>
                            <textarea id="notes" name="notes" cols="60" rows="2" maxlength="200"></textarea>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button"  class="btn btn-primary btn-sm btn-submit" id="btn-dis">提交</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">

    function del(id){
        if(id == ''){
            return false;
        }
        if(!confirm('确定要删除吗')){
            return false;
        }
        $.ajax({
            type: 'post',
            url: "{:U('/Home/Server/del')}",
            data: {
                id: id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    alert(data.msg);
                   // window.location.reload();
                    $('#status_'+id).html('回收');
                    $('#data_'+id).children('td').find('a').eq(1).attr("disabled",true);
                    $('#data_'+id).children('td').find('a').eq(1).removeAttr("onclick");
                }else{
                    alert(data.msg);
                }

            }
        });
    }

    function edit(id){
        if(id == ''){
            return false;
        }
        $('#id2').val(id);
        $.ajax({
            type: 'post',
            url: "{:U('/Home/Server/getEdit')}",
            data: {
                id2: id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    if(data.data.type == 1){
                        $('#type1').prop('checked', 'checked');
                    }else if(data.data.type == 2){
                        $('#type2').prop('checked', 'checked');
                    }else if(data.data.type == 3){
                        $('#type3').prop('checked', 'checked');
                    }else{
                        $('#type0').prop('checked', 'checked');
                    }

                    // if(data.data.father_id == 0){
                    //     $('#type1').prop('checked', 'checked');
                    // }else if(data.data.type == 2){
                    //     $('#type2').prop('checked', 'checked');
                    // }else if(data.data.type == 3){
                    //     $('#type3').prop('checked', 'checked');
                    // }else{
                    //     $('#type0').prop('checked', 'checked');
                    // }
                    // console.log(data.data.father_id);
                    // return;
                    $('#father_id2').empty();
                    var father_ids = [[0,'无'],[200,'邦秒验'],[210,'邦信分-通信字段'],[615,'号码风险等级'],[1000,'邦信分-通信评分'],[10000,'号码分'],[1100,'日志系统']];
                    var option = '';
                    for ($i= 0;$i<father_ids.length;$i++){
                       // console.log(father_ids[$i][0]);
                        if (data.data.father_id == father_ids[$i][0]){
                            option += "<option value=" + father_ids[$i][0] + " selected>" + father_ids[$i][1]+ "</option>";
                        }else{
                            option += "<option value=" + father_ids[$i][0] + ">" + father_ids[$i][1]+ "</option>";
                        }
                    }
                    $('#father_id2').append(option);

                    if(data.data.status == 1){
                        $('#status1').prop('checked', 'checked');
                    }else if(data.data.status == -1){
                        $('#status2').prop('checked', 'checked');
                    }else{
                        $('#status0').prop('checked', 'checked');
                    }
                    $('#ip2').val(data.data.ip);
                    $('#real_name2').val(data.data.real_name);
                    $('#name2').val(data.data.name);
                    $('#cpu2').val(data.data.cpu);
                    $('#mem2').val(data.data.mem);
                    $('#disk_root2').val(data.data.disk_root);
                    $('#disk2').val(data.data.disk);
                    $('#owner2').val(data.data.owner);
                    $('#useto2').val(data.data.useto);
                    $('#product2').val(data.data.product);
                    $('#notes2').val(data.data.notes);
                    $("#file_in_modal2").modal('show');
                }else{
                    alert(data.msg);
                }

            }
        });

    }

    $(document).ready(function () {

        $("#list_submit").click(function () {
            //$("#list_form").submit();
            var list_submit = new FormData($("#list_form")[0]);
            var str = '';
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Server/index",
                data:list_submit,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        for (var k in data.data){
                            if(data.data[k].status == -1){
                                str += '<tr id="data_'+data.data[k].id+'"> <td align="center">'+data.data[k].id+'</td> <td align="center">'+data.data[k].ip+'</td> <td align="center">'+data.data[k].real_name+'</td> <td align="center">'+data.data[k].name+'</td> <td align="center">'+data.data[k].owner+'</td> <td align="center">'+data.data[k].useto+'</td> <td align="center">'+data.data[k].product+'</td> <td align="center">'+data.data[k].type_name+'</td> <td align="center">'+data.data[k].cpu+'</td> <td align="center">'+data.data[k].mem+'</td> <td align="center">'+data.data[k].disk_root+'</td> <td align="center">'+data.data[k].disk+'</td> <td align="center">'+data.data[k].cpu_use+'</td> <td align="center">'+data.data[k].cpu_high+'</td> <td align="center">'+data.data[k].cpu_idle+'</td> <td align="center">'+data.data[k].mem_rem+'</td> <td align="center">'+data.data[k].mem_high+'</td> <td align="center">'+data.data[k].real_mem+'</td> <td align="center">'+data.data[k].real_mem_rem+'</td> <td align="center" id="status_'+data.data[k].id+'">'+data.data[k].status_name+'</td> <td align="center">'+data.data[k].notes+'</td> <td align="center"> <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('+data.data[k].id+')">编辑</a> <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" disabled="disabled">删除</a> </td> </tr>';
                            }else{
                                str += '<tr id="data_'+data.data[k].id+'"> <td align="center">'+data.data[k].id+'</td> <td align="center">'+data.data[k].ip+'</td> <td align="center">'+data.data[k].real_name+'</td> <td align="center">'+data.data[k].name+'</td> <td align="center">'+data.data[k].owner+'</td> <td align="center">'+data.data[k].useto+'</td> <td align="center">'+data.data[k].product+'</td> <td align="center">'+data.data[k].type_name+'</td> <td align="center">'+data.data[k].cpu+'</td> <td align="center">'+data.data[k].mem+'</td> <td align="center">'+data.data[k].disk_root+'</td> <td align="center">'+data.data[k].disk+'</td> <td align="center">'+data.data[k].cpu_use+'</td> <td align="center">'+data.data[k].cpu_high+'</td> <td align="center">'+data.data[k].cpu_idle+'</td> <td align="center">'+data.data[k].mem_rem+'</td> <td align="center">'+data.data[k].mem_high+'</td> <td align="center">'+data.data[k].real_mem+'</td> <td align="center">'+data.data[k].real_mem_rem+'</td> <td align="center" id="status_'+data.data[k].id+'">'+data.data[k].status_name+'</td> <td align="center">'+data.data[k].notes+'</td> <td align="center"> <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('+data.data[k].id+')">编辑</a> <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" onclick="del('+data.data[k].id+')">删除</a> </td> </tr>';
                            }
                        }
                        $('#tbody').html(str);

                    }else{
                        var str2 = '<tr><td colspan="22" style="text-align: center">未查到符合条件的数据</td></tr>';
                        $('#tbody').html(str2);
                    }
                }
            });

        });

        $('#file_in').on('click', function(){
           $("#file_in_modal").modal('show');
        });
        $('#btn-dis').on('click', function(){
            var ip = $('#ip').val();
            if (ip == ''){
                alert('ip不能为空');
                return false;
            }
            var real_name = $('#real_name').val();
            if (real_name == ''){
                alert('物理机名称不能为空');
                return false;
            }
            var name = $('#name').val();
            if (name == ''){
                alert('服务器名称不能为空');
                return false;
            }
            var cpu = $('#cpu').val();
            if(cpu != ''){
                //判断是不是数字
                if(isNaN(cpu)){
                    alert('核数必须为数字类型');
                    return false;
                }
            }
            var mem = $('#mem').val();
            if(mem != ''){
                //判断是不是数字
                if(isNaN(mem)){
                    alert('内存容量必须为数字类型');
                    return false;
                }
            }
            var disk_root = $('#disk_root').val();
            if(disk_root != ''){
                //判断是不是数字
                if(isNaN(disk_root)){
                    alert('root硬盘容量必须为数字类型');
                    return false;
                }
            }
            var disk = $('#disk').val();
            if(disk != ''){
                //判断是不是数字
                if(isNaN(disk)){
                    alert('硬盘容量必须为数字类型');
                    return false;
                }
            }
            var owner = $('#owner').val();

            if(owner == ''){
                alert('服务器所有者不能为空');
                return false;
            }

            var useto = $('#useto').val();
            if(useto == ''){
                alert('服务器用途不能为空');
                return false;
            }
            var product = $('#product').val();
            if(product == ''){
                alert('服务器所属项目不能为空');
                return false;
            }
            var formCar = new FormData($("#formCar")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Server/add",
                data:formCar,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        //alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        //window.location.reload();
                        if(data.data.status == -1){
                            var str = '<tr data-id="data_'+data.data.id+'"> <td align="center">'+data.data.id+'</td> <td align="center">'+data.data.ip+'</td> <td align="center">'+data.data.real_name+'</td> <td align="center">'+data.data.name+'</td> <td align="center">'+data.data.owner+'</td> <td align="center">'+data.data.useto+'</td> <td align="center">'+data.data.product+'</td> <td align="center">'+data.data.type_name+'</td> <td align="center">'+data.data.cpu+'</td> <td align="center">'+data.data.mem+'</td> <td align="center">'+data.data.disk_root+'</td> <td align="center">'+data.data.disk+'</td>  <td align="center" id="status_'+data.data.id+'">'+data.data.status_name+'</td> <td align="center">'+data.data.notes+'</td> <td align="center"> <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('+data.data.id+')">编辑</a> <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" disabled="disabled">删除</a> </td> </tr>';
                        }else {
                            var str = '<tr data-id="data_'+data.data.id+'"> <td align="center">'+data.data.id+'</td> <td align="center">'+data.data.ip+'</td> <td align="center">'+data.data.real_name+'</td> <td align="center">'+data.data.name+'</td> <td align="center">'+data.data.owner+'</td> <td align="center">'+data.data.useto+'</td> <td align="center">'+data.data.product+'</td> <td align="center">'+data.data.type_name+'</td> <td align="center">'+data.data.cpu+'</td> <td align="center">'+data.data.mem+'</td> <td align="center">'+data.data.disk_root+'</td> <td align="center">'+data.data.disk+'</td> <td align="center" id="status_'+data.data.id+'">'+data.data.status_name+'</td> <td align="center">'+data.data.notes+'</td> <td align="center"> <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('+data.data.id+')">编辑</a> <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" onclick="del('+data.data.id+')">删除</a> </td> </tr>';
                        }
                        $('tbody').prepend(str);
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });

        $('#btn-dis2').on('click', function(){
            var id = $('#id2').val();
            if(id == ''){
                return false;
            }
            var ip = $('#ip2').val();
            if (ip == ''){
                alert('ip不能为空');
                return false;
            }
            var real_name = $('#real_name2').val();
            if (real_name == ''){
                alert('物理机名称不能为空');
                return false;
            }
            var name = $('#name2').val();
            if (name == ''){
                alert('服务器名称不能为空');
                return false;
            }
            var cpu = $('#cpu2').val();
            if(cpu != ''){
                //判断是不是数字
                if(isNaN(cpu)){
                    alert('核数必须为数字类型');
                    return false;
                }
            }
            var mem = $('#mem2').val();
            if(mem != ''){
                //判断是不是数字
                if(isNaN(mem)){
                    alert('内存容量必须为数字类型');
                    return false;
                }
            }
            var disk_root = $('#disk_root2').val();
            if(disk_root != ''){
                //判断是不是数字
                if(isNaN(disk_root)){
                    alert('root硬盘容量必须为数字类型');
                    return false;
                }
            }
            var disk = $('#disk2').val();
            if(disk != ''){
                //判断是不是数字
                if(isNaN(disk)){
                    alert('硬盘容量必须为数字类型');
                    return false;
                }
            }

            var owner = $('#owner2').val();

            if(owner == ''){
                alert('服务器所有者不能为空');
                return false;
            }

            var useto = $('#useto2').val();
            if(useto == ''){
                alert('服务器用途不能为空');
                return false;
            }
            var product = $('#product2').val();
            if(product == ''){
                alert('服务器所属项目不能为空');
                return false;
            }

            var formCar = new FormData($("#formCar2")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Server/edit",
                data:formCar,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        //alert(data.msg);
                        $('#file_in_modal2').modal('hide');
                       // window.location.reload();
                        if(data.data.status == -1){
                            var str = '<td align="center">'+data.data.id+'</td> <td align="center">'+data.data.ip+'</td> <td align="center">'+data.data.real_name+'</td> <td align="center">'+data.data.name+'</td> <td align="center">'+data.data.owner+'</td> <td align="center">'+data.data.useto+'</td> <td align="center">'+data.data.product+'</td> <td align="center">'+data.data.type_name+'</td> <td align="center">'+data.data.cpu+'</td> <td align="center">'+data.data.mem+'</td> <td align="center">'+data.data.disk_root+'</td> <td align="center">'+data.data.disk+'</td><td align="center" id="status_'+data.data.id+'">'+data.data.status_name+'</td> <td align="center">'+data.data.notes+'</td> <td align="center"> <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('+data.data.id+')">编辑</a> <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" disabled="disabled">删除</a> </td>';
                        }else{
                            var str = '<td align="center">'+data.data.id+'</td> <td align="center">'+data.data.ip+'</td> <td align="center">'+data.data.real_name+'</td> <td align="center">'+data.data.name+'</td> <td align="center">'+data.data.owner+'</td> <td align="center">'+data.data.useto+'</td> <td align="center">'+data.data.product+'</td> <td align="center">'+data.data.type_name+'</td> <td align="center">'+data.data.cpu+'</td> <td align="center">'+data.data.mem+'</td> <td align="center">'+data.data.disk_root+'</td> <td align="center">'+data.data.disk+'</td><td align="center" id="status_'+data.data.id+'">'+data.data.status_name+'</td> <td align="center">'+data.data.notes+'</td> <td align="center"> <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('+data.data.id+')">编辑</a> <a href="javascript:void(0);" class="btn btn-info btn-danger btn-sm" onclick="del('+data.data.id+')">删除</a> </td>';
                        }
                        $('#data_'+id).html(str);
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });

    });

</script>
</body>
</html>
