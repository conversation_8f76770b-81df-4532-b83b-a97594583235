<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom: 15px;
        }

        .index-btn {
            margin: 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form id="form_init" action="/Home/FeeMonthProduct/expendList" method="get" class="form-inline">
                <div class="form-group">
                    <label for="customer_id">选择客户：</label>
                    <select name="customer_id" id="customer_id" class="form-control">
                        <option value="">全部</option>
                        {$customer_option}
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="start_month">日期区间：</label>
                    <input type="month" name="start_month" id="start_month" class="form-control"
                           value="{$params.start_month}"/>
                    -
                    <input type="month" name="end_month" id="end_month" class="form-control"
                           value="{$params.end_month}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="min_money">金额区间：</label>
                    <input type="text" name="min_money" id="min_money" class="form-control"
                           value="{$params.min_money}"/>
                    -
                    <input type="text" name="max_money" id="max_money" class="form-control"
                           value="{$params.max_money}"/>
                </div>
                <div class="form-group">
                    <label for="customer_id">类型：</label>
                    <select name="type" id="type" class="form-control">
                        <option value="">全部</option>
                        <option value="1">赠送</option>
                        <option value="2">消耗</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="start_date">操作时间区间：</label>
                    <input type="date" name="start_date" id="start_date" class="form-control"
                           value="{$params.start_date}"/>
                    -
                    <input type="date" name="end_date" id="end_date" class="form-control"
                           value="{$params.end_date}"/>
                </div>
                <div class="form-group">
                    <input type="submit" id="submit_btn" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <a id="file_export" class="btn btn-success btn-sm">返回</a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue">
            <thead>
            <tr>
                <th width="150">客户ID</th>
                <th>客户名称</th>
                <th>类型</th>
                <th>日期</th>
                <th>标题</th>
                <th>金额</th>
                <th>产品</th>
                <th>操作时间</th>
                <th width="280">操作</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td></td>
                <td>合计</td>
                <td></td>
                <td></td>
                <td></td>
                <td>{$total.sum}</td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            <volist name="list" id="v">
                <tr>
                    <td>{$v.customer_id}</td>
                    <td>{$v.customer_name}</td>
                    <td><?php echo 1 == $v['type']?'赠送':'消耗'; ?></td>
                    <td>{$v.start_date}</td>
                    <td>{$v.name}</td>
                    <td>{$v.money}</td>
                    <td>{$v.remark}</td>
                    <td>
                        <if condition="$v['update_time'] != 0">{$v.update_time|date='Y-m-d H:i:s',###}
                            <else/>
                            {$v.create_time|date='Y-m-d H:i:s',###}
                        </if>
                    </td>
                    <td>
                        <a href="/Account/Customer/edit?customer_id={$v.customer_id}&callback_url=%2FAccount%2FCustomer%2Findex.html">跳转至客户编辑页</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
</div>
<div class="container">
    <nav>
        <ul class="pagination">
            {$page->show()}
        </ul>
    </nav>
</div>

<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="text/javascript">
    $(function () {
        $("#name").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width: '400px'
        });
    });
</script>
</body>
</html>
