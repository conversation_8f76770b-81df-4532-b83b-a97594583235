<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <script src="__JS__bootstrap-datetimepicker.min.js"></script>
    <script src="__JS__bootstrap-datetimepicker.zh-CN.js"></script>
    <link rel="stylesheet" href="__CSS__bootstrap-datetimepicker.min.css">
    <style>
        .table_title {
            width: 100%;
            min-height: 40px;
            line-height: 40px;
            text-indent: 10px;
            font-size: 14px;
            color: red;
        }

        .table_title b {
            margin: 0 10px;
            font-size: 16px;
        }

        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }

        #loading {
            width: 100%;
            height: 100%;
            position: fixed;
            background: rgba(200, 200, 200, 0.2);
            z-index: 100;
            top: 0;
            left: 0;
            display: none;
        }

        .not_null {
            color: red;
            margin-right: 10px;
        }

        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }

        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }

        .lds-spinner {
            position: fixed;
        }

        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top: -20px;
            margin-left: -6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }

        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }

        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }

        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }

        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }

        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }

        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }

        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }

        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }

        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }

        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }

        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }

        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }

        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }

        .add_image, .edit_image {
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow: hidden;
        }

        .add_image::after, .edit_image::after {
            display: block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }

        .proof {
            width: 100px;
            height: 100px;
            border: 1px solid #ccc;
            cursor: pointer;
        }

        .panel-body .form-inline .form-group {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="start_time">交易日期：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control"
                           value="{$input.start_time}"/>
                    -
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>
                <div class="form-group">
                    <label for="min_money">金额（元）：</label>
                    <input type="text" name="min_money" id="min_money" class="form-control" value="{$input.min_money}"
                           placeholder="最小金额" maxlength="16" autocomplete="off"/>
                    -
                    <input type="text" name="max_money" id="max_money" class="form-control" value="{$input.max_money}"
                           placeholder="最大金额" maxlength="16" autocomplete="off"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="name">付款方名称：</label>
                    <select name="name" id="name" class="form-control">
                        <option value="">选择付款方</option>
                        {$input.name_option}
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="receipt_serial">流水号：</label>
                    <input type="text" name="receipt_serial" id="receipt_serial" class="form-control" maxlength="200"
                           value="{$input.receipt_serial}" placeholder="流水号" autocomplete="off"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="status">状态：</label>
                    <select class="form-control" name="status" id="status">
                        <option value="-1">全部</option>
                        <option value="1"
                        <if condition="in_array($input['status'], [0, 1])"> selected</if>
                        >未认款</option>
                        <option value="2"
                        <if condition="$input['status'] == 2"> selected</if>
                        >已认款</option>
                        <option value="3"
                        <if condition="$input['status'] == 3"> selected</if>
                        >已拆单</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="status">来源：</label>
                    <select class="form-control" name="source" id="source">
                        {$input.source_select}
                    </select>
                </div>

                <div class="form-group">
                    <label class="control-label" for="customer_id">选择客户：</label>
                    <select class="form-control" name="customer_id" id="customer_id">
                        {$input.customer_select}
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="payment_type">客户类型：</label>
                    <select class="form-control" name="payment_type" id="payment_type">
                        <option value="-1">全部</option>
                        <option value="1"
                        <if condition="$input['payment_type'] == 1"> selected</if>
                        >预付费</option>
                        <option value="2"
                        <if condition="$input['payment_type'] == 2"> selected</if>
                        >后付费</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="show_balance">仅展示存在余额的收款单：</label>
                    <select class="form-control" name="show_balance" id="show_balance">
                        <option value="-1">否</option>
                        <option value="1"
                        <if condition="$input['show_balance'] == 1"> selected</if>
                        >是</option>
                    </select>
                </div>
                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <button type="button" id="add" class="btn btn-success btn-sm">新增收款单</button>
                </div>
                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">批量导入收款单</button>
                </div>
                <div class="form-group">
                    <button type="button" id="file_in_wangshang" class="btn btn-success btn-sm">批量导入收款单(网商银行)</button>
                </div>
                <div class="form-group">
                    <button type="button" id="file_out" class="btn btn-success btn-sm">导出</button>
                </div>
                <div class="form-group">
                    <button type="button" id="file_out_50" class="btn btn-success btn-sm">导出(企服)</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="table_title">
        数据统计：<b>{$count}</b>条打款记录，合计金额：<b>{$total_money|round=###,2}</b>（其中已认款金额：<b>{$affirm_money|round=###,2}</b>）
    </div>
    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">流水号</th>
                <th style="text-align:center;">客户名称</th>
                <th style="text-align:center;">付费方式</th>
                <th style="text-align:center;">公司名称</th>
                <th style="text-align:center;">商务跟进人</th>
                <th style="text-align:center;">区域</th>
                <th style="text-align:center;">付款方名称</th>
                <th style="text-align:center;">付款方账号</th>
                <th style="text-align:center;">金额（元）</th>
                <th style="text-align:center;">余额（元）</th>
                <th style="text-align:center;">付方开户行名</th>
                <th style="text-align:center;">交易日期</th>
                <th style="text-align:center;">状态</th>
                <!--                <th style="text-align:center;">操作人</th>-->
                <th style="text-align:center;">来源</th>

                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="list_data" id="vo">
                <tr>
                    <td align="center">{$vo['receipt_serial']}</td>
                    <td align="center">{$vo.customer_name}</td>
                    <td align="center">{$vo.payment_type_name}</td>
                    <td align="center">{$vo.company}</td>
                    <td align="center">{$vo.salesman}</td>
                    <td align="center">{$vo.dept_name}</td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{:autoEnter($vo['account'])}</td>
                    <td align="center">{$vo.money|round=###,2}</td>
                    <td align="center">{$balance_list[$vo['receipt_serial']]['remit_balance']|round=###,2}</td>
                    <td align="center">{$vo.bank}</td>
                    <td align="center">{$vo.remit_date|date='Y-m-d',###}</td>
                    <td align="center">
                        <switch name="vo.status">
                            <case value="0">
                                <nobr>未认款</nobr>
                            </case>
                            <case value="1">
                                <nobr>未认款</nobr>
                            </case>
                            <case value="2">
                                <nobr>已认款</nobr>
                            </case>
                            <case value="3">
                                <nobr>已拆单</nobr>
                            </case>
                        </switch>
                    </td>
                    <td align="center">{$vo.source_label}</td>

                    <!--                    <td align="center">-->
                    <!--                        <nobr>{$user_data[$vo['admin']]['realname']}</nobr>-->
                    <!--                    </td>-->
                    <td align="center">
                        <if condition="$vo['admin'] == 'push_receipt' && in_array($loginuser['username'],['wei.xiu','chang.liu'])">
                            <a onclick="return confirm('确认重新推送认款么?  流水单号为【{$vo.receipt_serial}】');" href="{:U('re_push')}?receipt_serial={$vo.receipt_serial}" target="_blank" style="cursor:pointer;"><nobr>推送认款</nobr></a>
                            |
                            <a onclick="return confirm('确认重新推送认款撤销么?  流水单号为【{$vo.receipt_serial}】');" href="{:U('re_push_cancle')}?receipt_serial={$vo.receipt_serial}" target="_blank" style="cursor:pointer;"><nobr>推送认款撤销</nobr></a>
                        </if>
                        <if condition="$vo['status'] == 0 || $vo['status']== 1">
                            <a class="del" href="{:U('del')}?serial={$vo.receipt_serial}"
                               onclick="return confirm('确认删除流水单号为【{$vo.receipt_serial}】的收款单吗？');"
                               style="cursor:pointer;">
                                <nobr>删除</nobr>
                            </a>
                            <!--                            <a onclick="add_remit('{$vo.receipt_serial}', 1)" style="cursor:pointer;">-->
                            <!--                                <nobr>提交打款单</nobr>-->
                            <!--                            </a>-->

                            <a onclick="add_remit('{$vo.receipt_serial}', 2)" style="cursor:pointer;">
                                <nobr>认款</nobr>
                            </a>
                            <a onclick="add_remit_rel_invoice('{$vo.receipt_serial}', 2)" style="cursor:pointer;">
                                <nobr>认票</nobr>
                            </a>
                        </if>
                        <eq name="vo['status']" value="2">
                            <a onclick="split_price('{$vo.receipt_serial}', '{$vo.money}', '{$vo.payment_type}', '{$vo.customer_id}', '{$vo.source}')"
                               style="cursor:pointer;">
                                <nobr>拆单</nobr>
                            </a>
                            <a onclick="cancel_remit('{$vo.receipt_serial}')"
                               style="cursor:pointer;">
                                <nobr>撤销认款</nobr>
                            </a>
                        </eq>
                        <eq name="vo['status']" value="3">
                            <a onclick="split_price('{$vo.receipt_serial}', '{$vo.money}', '{$vo.payment_type}', '{$vo.customer_id}', '{$vo.source}')"
                               style="cursor:pointer;">
                                <nobr>重新拆单</nobr>
                            </a>
                        </eq>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<div class="modal fade" id="add_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="exampleModalLabel">请填写收款信息</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="add_form" method="post"></form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="add_submit">增加</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">请上传Excel文件</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <input type="hidden" name="type" value="file_in">
                        <label for="add_receipt_serial">选择Excel文件</label>
                        <input type="file" name="file" id="excel_file"/>
                    </div>
                    <div class="form-group" style="color:red;line-height:24px;">
                        * Excel文件的大小需要小于500KB，如果大于500KB，需要分多次上传<br/>
                        * Excel文件只支持xlsx或xls两种文件格式<br/>
                        * Excel文件的内容须严格按照以下规则填写，数据与数据之间不可存在空行，字段的位置不可随意更改。<br/>
                        <span style="color:orange;">
                        * ## D列为【交易日期】，规则为Y-m-d<br/>
                        * ## I列为【交易金额（元）】，小数类型，最大支持999999999.999999<br/>
                        * ## L列为【流水单号】，10-20位数字+字母的组合体<br/>
                        * ## T列为【付款方名称】，最大支持50位字符<br/>
                        * ## U列为【付款方账号】，最大支持50位字符<br/>
                        * ## W列为【付款开户行名】，最大支持50位字符<br/>
                        </span>
                        * Excel文件第1-9行不可填写数据，系统将从第10行开始批量导入<br/>
                        * 点击下载 <a href="/statics/template/receipt_v5.xlsx">Excel模板</a><br/>
                        * 批量导入所需时间可能较长，请耐心等待<br/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_submit">增加</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_wangshang_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">请上传Excel文件</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_wangshang_form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <input type="hidden" name="type" value="file_in">
                        <label for="add_receipt_serial">选择Excel文件</label>
                        <input type="file" name="file" id="excel_wangshang_file"/>
                    </div>
                    <div class="form-group" style="color:red;line-height:24px;">
                        * 网商银行回款数据导入<br/>
                        * Excel文件的大小需要小于500KB，如果大于500KB，需要分多次上传<br/>
                        * Excel文件只支持xlsx或xls两种文件格式<br/>
                        * Excel文件的内容须严格按照以下规则填写，数据与数据之间不可存在空行，字段的位置不可随意更改。<br/>
                        <span style="color:orange;">
                        * ## A列为【流水单号】，10-50位数字+字母的组合体<br/>
                        * ## C列为【交易日期】，规则为Y-m-d H:i:s<br/>
                        * ## E列为【交易金额（元）】，小数类型，最大支持999999999.999999<br/>
                        * ## H列为【付款方名称】，最大支持50位字符<br/>
                        * ## I列为【付款方账号】，最大支持50位字符<br/>
                        * ## J列为【付款开户行名】，最大支持50位字符<br/>
                        </span>
                        * Excel文件第1-4行不可填写数据，系统将从第5行开始批量导入<br/>
                        * 点击下载 <a href="/statics/template/receipt_wangshang_v1.xlsx">Excel模板</a><br/>
                        * 批量导入所需时间可能较长，请耐心等待<br/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_wangshang_submit">增加</button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="file_in_alert" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     style="position:absolute;overflow-y:auto;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body" id="file_in_alert_body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="know">我知道了</button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="modal fade" id="add_remit_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         style="position:absolute;overflow-y:auto;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">验证认款信息</h4>
                </div>
                <div class="modal-body">
                    <form action="{:U('add_remit')}" id="add_remit_form" method="post"
                          enctype="multipart/form-data">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="add_remit_submit">认款</button>
                </div>
            </div>
        </div>
    </div>

    <!--认票-->
    <div class="modal fade" id="add_remit_rel_invoice_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         style="position:absolute;overflow-y:auto;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">验证认票信息</h4>
                </div>
                <div class="modal-body">
                    <form action="{:U('add_remit_rel_invoice')}" id="add_remit_rel_invoice_form" method="post" enctype="multipart/form-data"></form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="add_remit_rel_invoice_submit">认票</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 朴道来源收款拆分-->
    <div  class="modal fade receipt_split_modal" id="receipt_split_modal" tabindex="-1" role="dialog" aria-labelledby="resplModalLabel"
    style="position:absolute;overflow-y:auto;">
   <div class="modal-dialog" style="width:1000px" role="document">
       <div class="modal-content">
           <div class="modal-header">
               <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                       aria-hidden="true">&times;</span></button>
               <h4 class="modal-title">朴道客户确认</h4>
           </div>
           <div class="modal-body" id="receipt_split_form">
           </div>
           <div class="modal-footer">
               <!-- <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
               <button type="button" class="btn btn-primary" id="receipt_split_submit">确认</button> -->
           </div>
       </div>
   </div>
</div>
</div>

<div class="container">
    <div class="modal fade" id="split_price_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         style="position:absolute;overflow-y:auto;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">提交拆单信息</h4>
                </div>
                <div class="modal-body">
                    <form action="" id="split_price_form" method="post"
                          enctype="multipart/form-data">
                    </form>
                    <input type="hidden" name="total_money" id="total_money" value="">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="split_price_submit">提交</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;">
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
                <div></div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">

    $(document).ready(function () {
        $("#name").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择打款方',
            width: '200px'
        });

        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择打款方',
            width: '200px'
        });

        //提交增加的数据
        $("#add_remit_submit").click(function () {
            //验证打款方
            if (valid_empty($('#add_name'))) {
                alert('请填写打款方');
                return false;
            }

            let add_money_obj = $('#add_money');
            //打款金额
            if (valid_empty(add_money_obj)) {
                alert('请填写打款金额');
                return false;
            }
            //金额格式验证
            if (!valid_money_format(add_money_obj.val())) {
                alert('打款金额格式不正确');
                return false;
            }
            //打款银行
            if (valid_empty($('#add_bank'))) {
                alert('请填写打款银行');
                return false;
            }
            //打款日期
            if (valid_empty($('#add_remit_date'))) {
                alert('请选择付款日期');
                return false;
            }
            //打款日期不可超过当前时间
            if (valid_date($("#add_remit_date").val())) {
                alert("请选择小于当前时间的的打款日期");
                return false;
            }

            let add_remit_serial_obj = $("#add_remit_serial");
            //验证流水号
            if (!valid_empty(add_remit_serial_obj)) {
                //验证流水号格式
                if (!valid_receipt_serial_format(add_remit_serial_obj.val())) {
                    alert('流水号格式不正确');
                    return false;
                }
                //流水号唯一性验证
                if (valid_serial_exists(add_remit_serial_obj.val())) {
                    alert('该流水单号已存在,请核对后重试');
                    return false;
                }
            }
            //验证凭证
//            if (valid_empty($("#add_proof_image"))) {
//                alert('请选择凭证图片');
//                return false;
//            }
            $("#add_remit_form").submit();
        });
        //切换客户修改公司名称
        $("#add_remit_form").on('change', '#add_customer_id', function () {
            var customer_id = $(this).val();
            let data = {
                customer_id: customer_id,
                type: 'option_customer'
            };
            $.ajax({
                url: "{:U('add_remit')}",
                data: data,
                async: false,
                type: 'post',
                success: function (res) {
                    console.log(res)
                    let company = res.company;
                    var realname = res.realname;
                    var dept_name = res.dept_name;
                    console.log(realname, dept_name);
                    $("#add_remit_form").find("#add_company").html(company);
                    $("#add_remit_form").find("#salesman").html(realname);
                    $("#add_remit_form").find("#dept_name").html(dept_name);
                }
            });
        }).on('click', '#upload_image', function () {
            //单图上传并展示缩略图
            $("#add_proof_image").trigger('click');
        }).on('click', '#add_proof_image', function (event) {
            //阻止事件冒泡
            event.stopImmediatePropagation();
        }).on('change', "#add_proof_image", function () {
            let file = $(this)[0].files[0];
            if (typeof file == 'undefined') {
                $("#upload_image").find('img').remove();
                alert('请选择需要上传的凭证图片');
                return false;
            }
            let size = file.size;
            let ext = file.name.split('.')[1];
            if (size > 2 * 1024 * 1024) {
                alert('您上传的图片文件过大，请处理后重新上传');
                $(this).val('');
                return false;
            }
            if (ext != 'gif' && ext != 'jpg' && ext != 'png' && ext != 'jpeg') {
                alert("您上传的图片格式暂不支持，请处理后重新上传");
                $(this).val('');
                return false;
            }
            let reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = function (e) {
                let image_html = '<image src="' + this.result + '" style="height:100%;" />';
                if ($("#upload_image").find("img").length > 0) {
                    $("#upload_image").find('img').remove();
                }
                $("#upload_image").append(image_html);
            };
        });
        $("#loading_modal").modal();
        $("#add").click(function () {
            $.get("{:U('add')}", function (res) {
                $("#add_form").html(res);
                $("#add_modal").modal({});
            });
        });
        //条件查询
        $("#list_submit").click(function () {
            //验证金额是否符合标准
            if (!valid_empty($("#max_money").val())) {
                if (!valid_money_format($("#max_money").val())) {
                    alert('最大金额格式不符合标准');
                    return false;
                }
            }
            if (!valid_empty($("#min_money").val())) {
                if (!valid_money_format($("#min_money").val())) {
                    alert('最小金额格式不符合标准');
                    return false;
                }
            }
            $("#list_form").submit();
        });
        //提交增加的数据
        $("#add_submit").click(function () {
            //验证流水号
            if (valid_empty($('#add_receipt_serial'))) {
                alert('请填写流水号');
                return false;
            }
            //验证流水号格式
            if (!valid_receipt_serial_format($("#add_receipt_serial").val())) {
                alert('流水号格式不正确');
                return false;
            }
            //验证流水号是否存在
            if (valid_receipt_serial_exists()) {
                alert('该流水单号已存在，请核对后重试');
                return false;
            }
            //付款方名称
            if (valid_empty($('#add_name'))) {
                alert('请填写付款方名称');
                return false;
            }
            //付款方账号
            if (valid_empty($('#add_account'))) {
                alert('请填写付款方账号');
                return false;
            }
            //交易金额
            if (valid_empty($('#add_money'))) {
                alert('请填写交易金额');
                return false;
            }
            //金额格式验证
            if (!valid_money_format($('#add_money').val())) {
                alert('交易金额格式不正确');
                return false;
            }
            //付款开户行名
            if (valid_empty($('#add_bank'))) {
                alert('请填写付款开户行名');
                return false;
            }
            //付款日期
            if (valid_empty($('#add_remit_date'))) {
                alert('请选择付款日期');
                return false;
            }
            //验证付款日期是否超过当前时间
            if (valid_date($("#add_remit_date").val())) {
                alert("请选择小于当前时间的的付款日期");
                return false;
            }
            $("#add_form").submit();
        });
        //批量导入收款单（模态框）
        $("#file_in").click(function () {
            $("#file_in_modal").modal({});
        });
        $("#file_in_wangshang").click(function () {
            $("#file_in_wangshang_modal").modal({});
        });
        //批量导入收款单
        $("#file_in_submit").click(function () {
            if (valid_empty($("#excel_file"))) {
                alert('请选择需要上传的Excel');
                return false;
            }
            //验证文件的大小
            let file = $("#excel_file")[0].files[0];
            let size = file.size;
            if (size >= 500 * 1024) {
                alert('文件过大，请将文件分割后导入');
                return false;
            }
            //验证文件后缀
            let ext = file.name.split('.')[1];
            if (ext != 'xls' && ext != 'xlsx') {
                alert('文件格式不正确，请核对后导入');
                return false;
            }
            $('#file_in_modal').modal('hide');
            $("#loading").show();
            //解决执行ajax后上述的动作不执行的BUG
            setTimeout(() => {
                let formData = new FormData();
                formData.append('type', 'file_in');
                formData.append('file', file);
                $.ajax({
                    url: "{:U('add')}",
                    data: formData,
                    type: 'post',
                    async: false,
                    cache: false,
                    processData: false,
                    contentType: false,
                    success: function (res) {
                        $("#loading").hide();
                        $("#file_in_alert_body").html(res);
                        $("#file_in_alert").modal();
                    },
                    error: function () {
                        $("#loading").hide();
                        alert('导入失败');
                    }
                });
            }, 10);
        });


       //批量导入收款单 网商银行模板
        $("#file_in_wangshang_submit").click(function () {
            if (valid_empty($("#excel_wangshang_file"))) {
                alert('请选择需要上传的Excel');
                return false;
            }
            //验证文件的大小
            let file = $("#excel_wangshang_file")[0].files[0];
            let size = file.size;
            if (size >= 500 * 1024) {
                alert('文件过大，请将文件分割后导入');
                return false;
            }
            //验证文件后缀
            let ext = file.name.split('.')[1];
            if (ext != 'xls' && ext != 'xlsx') {
                alert('文件格式不正确，请核对后导入');
                return false;
            }
            $('#file_in_wangshang_modal').modal('hide');
            $("#loading").show();
            //解决执行ajax后上述的动作不执行的BUG
            setTimeout(() => {
                let formData = new FormData();
                formData.append('type', 'file_in_wangshang');
                formData.append('file', file);
                $.ajax({
                    url: "{:U('add')}",
                    data: formData,
                    type: 'post',
                    async: false,
                    cache: false,
                    processData: false,
                    contentType: false,
                    success: function (res) {
                        $("#loading").hide();
                        $("#file_in_alert_body").html(res);
                        $("#file_in_alert").modal();
                    },
                    error: function () {
                        $("#loading").hide();
                        alert('导入失败');
                    }
                });
            }, 10);
        });
        $("#know").click(function () {
            location.href = location.href;
        });
        //导出数据
        $("#file_out").click(function () {
            let conf = confirm('请确认是否导出选查询到的所有数据');
            if (conf) {
                window.open("{:U('file_out')}" + location.search);
            }
        });
        //导出数据
        $("#file_out_50").click(function () {
            let conf = confirm('请确认是否导出选查询到的所有数据');
            if (conf) {
                window.open("{:U('file_out')}" + location.search + '&excelOutType=1');
            }
        });
    });

    //验证制定的数据是否为空
    function valid_empty(val) {
        if (val) {
            return false;
        }
        return true;
    }

    //流水号格式验证
    function valid_receipt_serial_format(serial) {
        let regex = /^\w{10,50}$/;
        return regex.test(serial);
    }

    //流水号是否存在验证
    function valid_receipt_serial_exists() {
        let is_exists = false;
        $.ajax({
            url: "{:U('add')}",
            data: {
                serial: $("#add_receipt_serial").val(),
                type: 'valid_serial'
            },
            async: false,
            type: 'post',
            success: function (res) {
                if (res.result == 1) {
                    is_exists = true;
                }
            }
        });
        return is_exists;
    }

    //交易金额格式验证
    function valid_money_format(money) {
        money = money.trim();
        let regex = /^(\-?)\d{1,9}(\.\d{1,6})?$/;
        if (regex.test(money)) {
            return true;
        }
        return false;
    }

    //验证某个时间是否超过当前时间
    function valid_date(value) {
        let date = new Date();
        let value_arr = value.split('-');
        date.setFullYear(value_arr[0]);
        date.setMonth(value_arr[1] - 1);
        date.setDate(value_arr[2]);
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        date.setMilliseconds(0);
        let date_time = date.getTime();
        let current = new Date();
        let current_time = current.getTime();
        return date_time > current_time;
    }

    // 认款 或 打款单
    function add_remit(receipt_serial, type) {
        if (type == 1) {
            $("#add_remit_modal .modal-title").html('提交打款单信息');
            $("#add_remit_submit").html('提交');
        } else {
            $("#add_remit_modal .modal-title").html('验证认款信息');
            $("#add_remit_submit").html('认款');
        }

        $("#add_remit_modal").modal();
        $.ajax({
            type: "get",
            data: {
                receipt_serial: receipt_serial
            },
            url: "{:U('add_remit')}",
            success: function (response) {
                $("#add_remit_form").html(response);
                $("#add_customer_id").select2({
                    allowClear: true,
                    theme: "bootstrap",
                    width: '100%',
                    dropdownParent: $("#add_remit_modal")
                });
                
                // 注册切换来源事件
                $("#add_remit_source").change(function () {
                    // if(this.value==1){
                        let conf = confirm('请确认是否进行拆分客户');
                        if (conf) {
                            handleSourceSelect(this.value,receipt_serial);
                        }
                    // }

                });
            }
        })
    }

    // 切换为朴道后触发朴道认款逻辑，关闭单一认款单 拆分到多个客户
    function handleSourceSelect(source,receipt_serial){
        // if(source==1){
            $("#add_remit_modal").modal('hide');
            $("#receipt_split_modal").modal();
            $.ajax({
            type: "get",
            data: {
                receipt_serial: receipt_serial,
                source:source
            },
            url: "{:U('receipt_split')}",
            success: function (response) {
                $("#receipt_split_form").html(response);

            }
        })
        // }
    }

    //流水号是否存在验证
    function valid_serial_exists(serial) {
        //设置需要排除的打款ID（编辑时不需要验证的ID值）
        let data = {
            serial: serial,
            type: 'valid_serial'
        };
        let is_exists = false;
        $.ajax({
            url: "{:U('add_remit')}",
            data: data,
            async: false,
            type: 'post',
            success: function (res) {
                if (res.result == 1) {
                    is_exists = true;
                }
            }
        });
        return is_exists;
    }

    // 拆单信息
    function split_price(receipt_serial, total_money, payment_type, customer_id,source) {
        let hasChild = false;
        $.ajax({
            type: 'get',
            data: {receipt_serial: receipt_serial,check_child:1,source:source},
            url: '{:U("split_price")}',
            async:false,
            success: function (response) {
                if(response=='hasChild'){
                    alert('存在征信机构需要拆分的认款单，不可拆单');    
                    hasChild= true;           
                }
               
            }
        })
        if(!hasChild){
            $('#split_price_modal').modal();
            $('#total_money').val(myFixed(total_money,2));
            $.ajax({
                type: 'get',
                data: {receipt_serial: receipt_serial, payment_type: payment_type, customer_id: customer_id,source:source},
                url: '{:U("split_price")}',
                success: function (response) {
                    // console.log(response)
                    $("#split_price_form").html(response);
                    $(".total_money").html(myFixed(parseFloat(total_money),2));
                    $("#product_id").select2({
                        allowClear: true,
                        theme: "bootstrap",
                        width: '100%',
                        dropdownParent: $("#split_price_modal")
                    });
                    $('.datetimepicker').datetimepicker({
                        language: 'zh-CN',//显示中文
                        format: 'yyyy-mm',//显示格式
                        startView: 3, // 设置只显示月份
                        minView: 3,//设置只显示到月份
                        // initialDate: new Date(),//初始化当前日期
                        monthsTitle: '请选择月份',
                        autoclose: true,//选中自动关闭
                    });
                }
            })
        }
    }

    // 添加拆分信息
    $('#split_price_submit').click(function () {
        var receipt_serial = $('#sp_receipt_serial').val();
        var payment_type = $('#sp_payment_type').val();
        //console.log(receipt_serial)
        var tr = $('#split_price_list tbody').find('tr');
        var data = [];
        var td_total_money = 0;
        $.each(tr, function (index, content) {
            var product_id = $(content).find('td:eq(0)').text();
            var product_name = $(content).find('td:eq(1)').text();
            var month = $(content).find('td:eq(2)').text();
            var money = parseFloat($(content).find('td:eq(3)').text());
            td_total_money += money;
            data.push({
                "product_id": product_id,
                "product_name": product_name,
                "month": month,
                "money": money,
                'receipt_serial': receipt_serial
            });
        })
        td_total_money = td_total_money.toFixed(6);
        var total_money = parseFloat($('#total_money').val()).toFixed(6);
        //console.log(td_total_money)
        //console.log(total_money)
        total_money = parseFloat(total_money);
        td_total_money = parseFloat(td_total_money);
        if( payment_type == 1){
            console.log(total_money,td_total_money,typeof total_money,typeof td_total_money);
            if (total_money < td_total_money) {
                alert('总金额不可小于拆分金额');
                return false;
            }
        }else{
            if (total_money != td_total_money) {
                alert('拆分金额与总金额不一致');
                return false;
            }
        }

        setTimeout(() => {
            $.ajax({
                url: "{:U('split_price')}",
                data: {"data": data},
                type: 'post',
                async: false,
                success: function (res) {
                    window.location.reload();
                },
                error: function () {
                    alert('添加失败');
                }
            });
        }, 10);
    })


    function cancel_remit(receipt_serial){
        let msg = "确定要撤销 "+receipt_serial+" 认款吗？\n\n请确认！";
        if (confirm(msg) === true){
            $.ajax({
                url: "{:U('cancel_remit')}",
                data: {receipt_serial: receipt_serial},
                type: 'post',
                async: false,
                success: function (res) {
                    window.location.reload();
                },
                    error: function () {
                    alert('删除失败');
                }
            });
        }else{
            return false;
        }
    }



    /**
     * 保留小数点几位数, 自动补零, 四舍五入
     * @param num: 数值
     * @param digit: 小数点后位数
     * @returns string
     */ 
    function myFixed(num, digit) {
        if(Object.is(parseFloat(num), NaN)) {
            return 0.00;
        }
        num = parseFloat(num);
        return (Math.round((num + Number.EPSILON) * Math.pow(10, digit)) / Math.pow(10, digit)).toFixed(digit);
    }

    function add_remit_rel_invoice(receipt_serial) {
        $("#add_remit_rel_invoice_modal").modal();
        $.ajax({
            type: "get",
            data: {
                receipt_serial: receipt_serial
            },
            url: "{:U('add_remit_rel_invoice')}",

            success: function (response) {
                $("#add_remit_rel_invoice_form").html(response);
                let add_remit_rel_invoice_modal_selector = $("#add_remit_rel_invoice_modal");

                $("#rri_customer_id").select2({
                    allowClear: true,
                    theme: "bootstrap",
                    width: '100%',
                    dropdownParent: add_remit_rel_invoice_modal_selector
                });
                $("#rri_invoice_id").select2({
                    allowClear: true,
                    theme: "bootstrap",
                    width: '100%',
                    dropdownParent: add_remit_rel_invoice_modal_selector
                });
            }
        })
    }

    $("#add_remit_rel_invoice_form").on('change', '#rri_source', function () {
        let source = $(this).val();
        // console.log("#add_remit_rel_invoice_form > #rri_source on change value:",source);

        let rri_customer_form_group_obj = $("#rri_customer_id_form_group");
        if(source === 0 || source === '0'){
            //如果切换的渠道是羽乐科技 则展示客户选择列表
            rri_customer_form_group_obj.show();
        }else{
            //如果切换的渠道不是羽乐科技 则隐藏客户选择列表
            rri_customer_form_group_obj.hide();
        }

        let customer_id = $("#rri_customer_id").val();
        get_unrel_invoice_list(source,customer_id);

    }).on("change","#rri_customer_id",function (){
        // console.log("change rri_customer_id event handle");
        let customer_id = $(this).val();
        let source = $("#rri_source").val();
        get_unrel_invoice_list(source,customer_id);
    }).on("change","#rri_invoice_id",function (){
        let invoice_id = $("#rri_invoice_id").val();
        // console.log("change rri_invoice_id event handle",invoice_id);
        get_invoice_consume_list(invoice_id)
    })
    //提交认票
    $("#add_remit_rel_invoice_submit").on("click",function(){
        console.log("add_remit_rel_invoice_submit click submit!!!!!!!!!!!");

        let source         = $("#rri_source").val();
        let invoice_id     = $("#rri_invoice_id").val();
        let customer_id    = $("#rri_customer_id").val();
        let receipt_serial = $("#rri_receipt_serial").val();

        let data = {
            source: source,
            customer_id: customer_id,
            invoice_id: invoice_id,
            receipt_serial: receipt_serial,
            user_cookie: getCookie('PHPSESSID'),
        };
        console.log("data:",data);
        $.ajax({
            url: "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/invoice/relate",
            data: data,
            async: false,
            type: 'post',
            success: function (res) {
                console.log(res)
                window.location.reload();
                // if(res.data.status === 0) {
                //     console.log("成功");
                // }
            }
        });
    })

    //获取发票列表 如果渠道不是羽乐科技 传入的customer_id没有作用
    function get_unrel_invoice_list(source,customer_id){
        let data = {
            source: source,
            customer_id: customer_id,
            user_cookie: getCookie('PHPSESSID'),
        };

        let tr_tds = `<tr><td>发票流水号</td><td>客户</td><td>产品</td><td>月份</td><td>消耗金额</td><td>开票金额</td></tr>`;
        $("#rii_invoice_consume_info_table").html(tr_tds);

        $.ajax({
            url: "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/invoice/unrel_invoice_list",
            data: data,
            async: false,
            type: 'post',
            success: function (res) {
                // console.log(res)

                let options = '<option value="">请选择发票</option>';
				for ( let i = 0 ; i < res.data.length ; i++ ) {
					options = options +
						'<option value="' + res.data[i]['invoice_id'] + '">' +
                            res.data[i]['invoice_company'] + '&nbsp;&nbsp;' +
							res.data[i]['invoice_id'] + '&nbsp;&nbsp;' +
                            res.data[i]['money'] + '&nbsp;&nbsp;' +
                            res.data[i]['invoice_balance'] + '&nbsp;&nbsp;' +
                            res.data[i]['remit_status_text'] +
						'</option>';
				}
                $("#rri_invoice_id").html(options);
            }
        });
    }


    function get_invoice_consume_list(invoice_id){
        let data = {
            invoice_id: invoice_id,
            user_cookie: getCookie('PHPSESSID'),
        };
        $.ajax({
            url: "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/invoice/invoice_consume_list",
            data: data,
            async: false,
            type: 'post',
            success: function (res) {
                console.log(res)

                let tr_tds = `<tr>
                                <td>发票流水号</td>
                                <td>客户</td>
                                <td>产品</td>
                                <td>月份</td>
                                <td>消耗金额</td>
                                <td>开票金额</td>
                              </tr>`;

				for ( let i = 0 ; i < res.data.length ; i++ ) {
					tr_tds = tr_tds + '<tr>'+
						'<td>' + res.data[i]['invoice_id'] + '</td>'+
						'<td>' + res.data[i]['customer_name'] + '</td>'+
						'<td>' + res.data[i]['product_name'] + '</td>'+
						'<td>' + res.data[i]['month'] + '</td>'+
						'<td>' + res.data[i]['consume_money'] + '</td>'+
						'<td>' + res.data[i]['invoice_money'] + '</td>'+
                    '</tr>';
				}
                $("#rii_invoice_consume_info_table").html(tr_tds);
            }
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }

</script>
</body>
</html>
