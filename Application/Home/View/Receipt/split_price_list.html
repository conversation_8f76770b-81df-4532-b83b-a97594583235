<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner {
        width: 256px;
    }
    .el-table{
        font-size: 14px;
    }
    .table_title {
        width: 100%;
        min-height: 40px;
        line-height: 40px;
        text-indent: 10px;
        font-size: 14px;
        color: red;
    }

    .table_title b {
        margin: 0 10px;
        font-size: 16px;
    }
</style>

<div id="app">
    <div class="container" id="cuishou_list_app">
        <div class="panel panel-default">
            <div class="panel-body">

                <el-form :inline="true" :model="searchForm" label-width="100px" class="demo-form-inline">
                    <el-form-item label="流水号">
                        <el-input v-model="searchForm.receipt_serial" placeholder="请输入流水号"></el-input>
                    </el-form-item>
                    <el-form-item label="产品">
                        <el-select v-model="searchForm.product_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in productList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="客户名称">
                        <el-select v-model="searchForm.customer_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in customerList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="拆分月份">
                        <el-col :span="11">
                            <el-date-picker type="month" placeholder="选择月份" v-model="searchForm.month" value-format="yyyyMM"></el-date-picker>
                        </el-col>
                    </el-form-item>

                    <el-form-item label="交易日期">
                        <el-col :span="11">
                            <el-date-picker
                                    v-model="searchForm.remit_date"
                                    type="daterange"
                                    align="right"
                                    unlink-panels
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    value-format="yyyyMMdd">
                            </el-date-picker>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="来源">
                        <el-select v-model="searchForm.source" filterable clearable placeholder="全部" style="width: 100px">
                            <el-option
                                    v-for="item in sourceInfo"
                                    :key="item.source"
                                    :label="item.name"
                                    :value="item.source">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchTableData()">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="success" @click="fileDownload()">导出</el-button>
                    </el-form-item>
                </el-form>

            </div>
        </div>
    </div>

    <div class="container">
        <div class="table_title">
            数据统计：<b class="split_num"></b>条拆分记录，合计金额：<b class="total_money"></b>（其中已拆分金额：<b class="split_money"></b>）
        </div>
        <div id="app_body">
            <template>
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            prop="receipt_serial"
                            label="流水号"
                            width="160">
                    </el-table-column>
                    <el-table-column
                    prop="customer_id"
                    label="客户ID"
                    width="160">
                    </el-table-column>
                    <el-table-column
                            prop="customer_name"
                            label="客户名称"
                            width="100"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="company"
                            label="公司名称"
                            width="280"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="product_name"
                            label="产品名称"
                            width="140"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="month"
                            label="月份"
                            width="100"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="money"
                            label="拆分金额"
                            width="120"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="remit_date"
                            label="交易日期"
                            width="100"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="source_name"
                            label="来源"
                            width="80"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="salesname"
                            label="销售"
                            width="100"
                    >
                    </el-table-column>
                    <el-table-column
                    prop="area_person"
                    label="区域"
                    width="80"
                    >
                    </el-table-column>
            
                </el-table>
            </template>
        </div>

        <div class="block" style="margin-bottom: 16px;">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40, 50]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalNum">
            </el-pagination>
        </div>
    </div>

</div>

<script type="application/javascript">

    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/remit/getSplitPrice";

    var vm = new Vue({
        el: '#app',
        data: {
            tableData: [],
            totalNum: 0,
            pageSize: 10,
            currentPage: 1,
            productList:[],
            customerList: [],
            pickerOptions: {},
            getUrl: url,
            formLabelWidth: '120px',
            searchForm: {
                receipt_serial: '',
                product_id: '',
                customer_id: '',
                month: '',
                source:''
            },
            sourceInfo:[],
            rules: {
                product_id: [
                    {required: true, message: '请选择产品', trigger: 'change'}
                ],
                customer_id: [
                    {required: true, message: '请选择客户', trigger: 'change'}
                ],
            }
        },
        created: function () {
            this.getTableData();
            this.getProductSelectData();
            this.getCustomerSelectData();
        },
        methods: {
            getTableData: function () {
                var self = this;
                var receipt_serial = this.searchForm.receipt_serial;
                var product_id = this.searchForm.product_id;
                var customer_id = this.searchForm.customer_id;
                var month = this.searchForm.month;
                var remit_date = this.searchForm.remit_date;
                var source = this.searchForm.source;


                //console.log(month)
                var where = {limit: this.pageSize, page: this.currentPage};
                if (receipt_serial) {
                    where.receipt_serial = receipt_serial;
                }
                if (product_id) {
                    where.product_id = product_id;
                }
                if (customer_id) {
                    where.customer_id = customer_id;
                }
                if (month) {
                    where.month = month;
                }
                if (remit_date) {
                    where.remit_date = remit_date;
                }
                if (source !== '') {
                    where.source = source;
                }
                axios.post(url, where).then(function (response) {
                    //console.log(response.data.data);
                    self.tableData = response.data.data.list;
                    self.totalNum = response.data.data.count;
                    $('.split_num').html(response.data.data.count);
                    $('.total_money').html(parseFloat(response.data.data.total_money).toFixed(2));
                    $('.split_money').html(parseFloat(response.data.data.split_money).toFixed(2));
                }).catch(function (error) {
                    console.log(error);
                });

            },
            getProductSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/mainProduct?default=null&options=false";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.productList.push({value: key, label: name});
                        //console.log(key)
                    });
                    self.productList.push({value: '0', label: '全部'});
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getCustomerSelectData:function(){
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                axios.get(url, {
                    params:{customer:true,source:true}
                }).then(function (response) {

                    Object.getOwnPropertyNames(response.data.data.customer).forEach(function(key){
                        var name = response.data.data.customer[key];
                        self.customerList.push({value:key,label:name});
                        self.sourceInfo = response.data.data.source;
                    });
                    self.customerList.push({value:'all',label:'全部'});

                }).catch(function (error) {
                    console.log(error);
                });

            },
            handleSizeChange(val) {
                //console.log(`每页 ${val} 条`);
                this.pageSize = val;
                this.currentPage = 1;
                this.getTableData();
            },
            handleCurrentChange(val) {
                //console.log(`当前页: ${val}`);
                this.currentPage = val;
                this.getTableData();
            },
            searchTableData: function () {
                this.currentPage = 1;
                this.getTableData();
            },
            fileDownload: function () {
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/remit/download";
                var receipt_serial = this.searchForm.receipt_serial;
                var product_id = this.searchForm.product_id;
                var customer_id = this.searchForm.customer_id;
                var month = this.searchForm.month;
                var remit_date = this.searchForm.remit_date;
                var source = this.searchForm.source;

                var where = '';
                if (receipt_serial) {
                    where += '&receipt_serial='+receipt_serial;
                    // where.receipt_serial = receipt_serial;
                }
                if (product_id) {
                    where += '&product_id='+product_id;
                    // where.product_id = product_id;
                }
                if (customer_id) {
                    where += '&customer_id='+customer_id;
                    // where.customer_id = customer_id;
                }
                if (month) {
                    where += '&month='+month;
                    // where.month = month;
                }
                if (remit_date) {
                    where += '&remit_date='+remit_date;
                    // where.remit_date = remit_date;
                }
                if (source !== '') {
                    where += '&source='+source;
                    // where.source = source;
                }
                window.location.href="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/remit/download?"+where;
            }
        }

    })

</script>
</body>
</html>
