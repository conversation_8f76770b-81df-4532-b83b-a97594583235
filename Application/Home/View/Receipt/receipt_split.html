<link rel="stylesheet" type="text/css" href="__JS__vue/index.css" />
<script type="application/javascript" src="__JS__/vue/vue.js"></script>
<script type="application/javascript" src="__JS__/vue/index.js"></script>
<script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner {
        width: 256px;
    }

    .el-date-editor .el-range-separator {
        width: 10%;
    }

    .table_title {
        width: 100%;
        min-height: 40px;
        line-height: 40px;
        text-indent: 10px;
        font-size: 14px;
        color: red;
    }

    .table_title b {
        margin: 0 10px;
        font-size: 16px;
    }

    input[type="file"]{
        display:none;
    }

    /* 弹出框下部有数据拉不上来所以加个padding-bottom */
    #app_body .el-table__body-wrapper {
        padding-bottom: 20px;
    }
</style>

<div id="app">
    <div class="container" id="splitrecpt_list_app">
        <div class="panel panel-default">
            <div class="panel-body">

                <el-form :inline="true" :model="searchForm" label-width="50px" class="demo-form-inline" size="mini">

                    <el-form-item label="月份">
                        <el-date-picker v-model="searchForm.remit_date" type="monthrange" range-separator="至"
                            start-placeholder="开始月份" end-placeholder="结束月份" value-format="yyyyMM">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-input placeholder="客户名称" v-model="searchForm.name"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchTableData()">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-upload
                                type="warning"
                          class="upload-excel"
                          :action="upload_excel_url"
                          :data="searchForm"
                          :before-upload="beforeUpload"
                          :on-success="getExcelTableData"
                          :limit="1"
                          :file-list="fileList">
                            <el-button type="primary" size="mini">上传拆分数据</el-button>
                        </el-upload>
                    </el-form-item>
                </el-form>

            </div>
        </div>
    </div>

    <div class="container">

        <div id="app_body">
            <div class="table_title">
                收款单金额：<b class="receipt_money"></b>
                已拆金额：<b class="split_money"></b>
                待拆金额：<b class="unsplit_money"></b>

                <!-- 数据统计：<b class="split_num"></b>条记录， -->
                列表合计金额：<b class="total_money"></b>
                （已选金额：<b class="selected_money"></b>）
            </div>
            <template>
                <el-table ref="multipleTable" :data="tableData" border height="300" v-loading="loading"
                    style="width: 100%" @selection-change="customerSelectionChange">
                    <el-table-column type="selection" width="55">
                    </el-table-column>
                    <el-table-column prop="customer_id" label="客户id">
                    </el-table-column>
                    <el-table-column prop="customer_name" label="客户名称">
                    </el-table-column>
                    <el-table-column prop="money" label="消费金额">
                        <template slot-scope="scope">
                            <el-input placeholder="请输入金额" v-model="scope.row.money" style="width:75%"
                                @input="money_change(scope.row)"></el-input>
                        </template>

                    </el-table-column>

                </el-table>
                <el-row type="flex" class="row-bg" justify="center">

                    <el-button @click.native="cancelHide()">取消</el-button>
                    <el-button @click="confirmSplit()" type="primary">确认</el-button>
                </el-row>

            </template>

        </div>

        <div class="block" style="margin-bottom: 16px;">
            <!-- <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40, 50]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalNum">
            </el-pagination> -->
        </div>
    </div>

</div>

<script type="application/javascript">

    const url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/receipt/getSplitCustomer";
    const upload_excel_url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/receipt/splitByExcel";

    var vm = new Vue({
        el: '#app',
        data: {
            tableData: [],
            totalNum: 0,
            pageSize: 10,
            currentPage: 1,
            productList: [],
            customerList: [],
            pickerOptions: {},
            getUrl: url,
            formLabelWidth: '120px',
            searchForm: {
                receipt_serial: '{$receipt_serial}',
                product_id: '',
                customer_id: '',
                source: '{$source}',
                name:'',
            },
            upload_excel_url:upload_excel_url,
            fileList:[],
            rules: {

                remit_date: [
                    { required: true, message: '请选择月份', trigger: 'change' }
                ],
                // product_id: [
                //     {required: true, message: '请选择产品', trigger: 'change'}
                // ],
                // customer_id: [
                //     {required: true, message: '请选择客户', trigger: 'change'}
                // ],
            },
            loading: false,
            customer_money_map: [],
            selected_money: 0.0000,
        },
        created: function () {
            this.getTableData();
            this.getProductSelectData();
            this.getCustomerSelectData();
        },
        methods: {
            getTableData: function () {
                var self = this;
                var receipt_serial = this.searchForm.receipt_serial;
                var product_id = this.searchForm.product_id;
                var customer_id = this.searchForm.customer_id;
                var remit_date = this.searchForm.remit_date;
                var source = this.searchForm.source;
                var name = this.searchForm.name;


                var where = { source: source, receipt_serial: receipt_serial, remit_date: remit_date,name:name };
                where.source = source;
                if (receipt_serial) {
                    where.receipt_serial = receipt_serial;
                }

                if (remit_date) {
                    where.remit_date = remit_date;
                }
                axios.post(url, where).then(function (response) {
                    //console.log(response.data.data);
                    self.tableData = response.data.data.list;
                    self.totalNum = response.data.data.count;
                    $('.split_num').html(response.data.data.count);
                    $('.total_money').html(parseFloat(response.data.data.total_money).toFixed(4));
                    $('.split_money').html(parseFloat(response.data.data.split_money).toFixed(4));
                    $('.unsplit_money').html(parseFloat(response.data.data.unsplit_money).toFixed(4));
                    $('.receipt_money').html(parseFloat(response.data.data.receipt_money).toFixed(4));
                }).catch(function (error) {
                    console.log(error);
                });

            },
            beforeUpload:function(){
                this.loading = true;
                return true;
            },
            getExcelTableData:function(response){
                if(response.status != 0){
                    this.$message.error(response.msg);
                }else {
                    console.log("getExcelTableData:", response);
                    this.tableData = response.data.list;
                    this.totalNum = response.data.count;
                    $('.split_num').html(response.data.count);
                    $('.total_money').html(parseFloat(response.data.total_money).toFixed(4));
                    $('.split_money').html(parseFloat(response.data.split_money).toFixed(4));
                    $('.unsplit_money').html(parseFloat(response.data.unsplit_money).toFixed(4));
                    $('.receipt_money').html(parseFloat(response.data.receipt_money).toFixed(4));
                    this.$refs.multipleTable.toggleAllSelection();
                }
                this.loading = false;
            },
            getProductSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/mainProduct?default=null&options=false";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.productList.push({ value: key, label: name });
                    });
                    self.productList.push({ value: '0', label: '全部' });
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getCustomerSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                axios.get(url, {
                    params: { customer: true }
                }).then(function (response) {

                    Object.getOwnPropertyNames(response.data.data.customer).forEach(function (key) {
                        var name = response.data.data.customer[key];
                        self.customerList.push({ value: key, label: name });
                    });
                    self.customerList.push({ value: 'all', label: '全部' });

                }).catch(function (error) {
                    console.log(error);
                });

            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getTableData();
            },
            handleCurrentChange(val) {
                //console.log(`当前页: ${val}`);
                this.currentPage = val;
                this.getTableData();
            },
            searchTableData: function () {
                this.currentPage = 1;
                this.getTableData();
            },
            // 监听多选按钮
            customerSelectionChange: function (val) {
                var select_money = 0.00;
                if (val.length == 0) {
                    this.customer_money_map = [];
                } else {
                    this.customer_money_map = [];
                    for (let index = 0; index < val.length; index++) {
                        var element = val[index];
                        this.customer_money_map.push({ customer_id: element.customer_id, money: element.money });
                        // console.log(select_money);
                        select_money = this.accAdd(select_money, element.money);
                    }
                }
                this.selected_money = select_money.toFixed(4);
                this.updateSelectSplit(select_money.toFixed(4));
            },
            hideSelf: function () {
                $("#receipt_split_modal").modal('hide');
                $(".receipt_split_modal").modal('hide');
                location.reload();
            },
            cancelHide: function () {
                $("#receipt_split_modal").modal('hide');
                $(".receipt_split_modal").modal('hide');
            },
            // 手动修改金额
            money_change(row) {
                var self = this;
                let select_money = 0.00;
                // 有勾选的 更新金额
                if (this.customer_money_map.length > 0) {
                    this.customer_money_map.forEach(function (val, index) {                        
                        if (val.customer_id === row.customer_id) {
                            val.money = row.money;
                        }                        
                        // 重新计算已选
                        select_money = self.accAdd(select_money, val.money);
                    })
                }
                this.updateSelectSplit(select_money);
            },
            updateSelectSplit:function(select_money){
                var re_money = parseFloat($('.receipt_money').html());
                var sp_money = parseFloat($('.split_money').html());
                
                // 更新已选金额
                $('.selected_money').html(select_money);
                //更新待拆金额
                var unsplit_money = (re_money - sp_money - select_money) > 0 ? (re_money - sp_money - select_money).toFixed(4) : 0.0000;
                $('.unsplit_money').html(unsplit_money);

            },
            confirmSplit: function () {
                // this.loading = true;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/receipt/split";
                var self = this;

                //拆分数据
                var where = { source: 0, receipt_serial: '', customer_money_map: [] };
                where.source = this.searchForm.source;
                where.receipt_serial = this.searchForm.receipt_serial;
                where.customer_money_map = this.customer_money_map;

                //判断金额 选的金额收款单本身金额 给出提示
                var se_money = parseFloat($('.selected_money').html()).toFixed(2);
                var re_money = parseFloat($('.receipt_money').html());
                var sp_money = parseFloat($('.split_money').html());

                //计算未拆金额
                var unsp_money = (re_money - sp_money).toFixed(2);
                // if (parseFloat(unsp_money) <= 0) {
                //     self.$message({
                //         type: 'warning',
                //         message: '待拆金额小于或等于零，无需拆分！',
                //     });
                //     return;
                // }
                if (se_money != unsp_money) {
                    // notice
                    self.$confirm('已选金额与未拆金额不一致, 是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.post(url, where).then(function (response) {
                            if (response.data.status == 0) {
                                self.$message({
                                    type: 'success',
                                    message: '拆分成功!'
                                });
                                self.hideSelf();
                            } else {
                                self.$message({
                                    type: 'warning',
                                    message: response.data.message,
                                });
                            }
                        }).catch(function (error) {
                            self.$message({
                                type: 'warning',
                                message: error,
                            });
                        });

                    }).catch(() => {
                        // self.$message({
                        // type: 'info',
                        // message: '已取消拆分'
                        // });          
                    });
                } else {
                    axios.post(url, where).then(function (response) {
                        if (response.data.status == 0) {
                            self.$message({
                                type: 'success',
                                message: '拆分成功!'
                            });
                            self.hideSelf();
                        } else {
                            self.$message({
                                type: 'warning',
                                message: response.data.message,
                            });
                        }

                    }).catch(function (error) {
                        self.$message({
                            type: 'warning',
                            message: error,
                        });
                    });
                }



            },
            // 浮点数相加
            accAdd: function (arg1, arg2) {
                let r1, r2, m;
                try {
                    r1 = arg1.toString().split(".")[1].length
                } catch (e) {
                    r1 = 0
                }
                try {
                    r2 = arg2.toString().split(".")[1].length
                } catch (e) {
                    r2 = 0
                }
                m = Math.pow(10, Math.max(r1, r2))
                return (arg1 * m + arg2 * m) / m
            }
        }

    })

</script>