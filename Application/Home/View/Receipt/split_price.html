
    <div class="form-group" id="auto_money_data">
        <label class="add_label" for="month"><span class="not_null">*</span>请先选择月份：</label>
        <volist name="data.money_data" id="vo">
            <div class="checkbox">
                <label><input type="checkbox" {$vo['checked'] ? 'checked' : ''} data-toggle="collapse" data-target="#month_{$vo.month}"/>
                    {$vo.month}：总消耗 {$vo.money}，<span style="color:red;">其中未认款（{$vo.unremit_money}）</span> </label>
            </div>
            <style>
                .collapse {
                    display: none;
                }
            </style>
            <div id="month_{$vo.month}" class="collapse {$vo['checked'] ? 'in' : ''}" style="padding-left:20px;">
                <label class="add_label" for="product_id"><span class="not_null">*</span>请选择产品：</label>
                <volist name="vo.product" id="vv">
                    <div class="checkbox">
                        <label><input name="month_father_money" type="checkbox"
                                      value="{$vo.month}_{$vv.father_id}_{$vv.unremit_money}_{$data['product_data'][$vv['father_id']]}" {$vv['checked'] ? 'checked' : ''} onclick="checkBox(this)"/>
                            {$data['product_data'][$vv['father_id']]}：总消耗 {$vv.money}， <span style="color:red;">其中未认款（{$vv.unremit_money}）</span></label>
                    </div>
                </volist>
            </div>
        </volist>
    </div>


<input type="hidden" id="sp_receipt_serial" name="sp_receipt_serial" value="{$data.receipt_serial}">
<input type="hidden" id="sp_payment_type" name="payment_type" value="{$data.payment_type}">

<div class="table_title">
    拆分总金额：<b class="total_money"></b>（其中已拆分金额：<b class="splite_money">{$data.money|round=###,2}</b>）
</div>
<div class="panel panel-default table-responsive">
    <table id="split_price_list" class="table table-bordered table-striped table-hover">
        <thead>
        <tr align="center">
            <th style="text-align:center;">产品ID</th>
            <th style="text-align:center;">产品名称</th>
            <th style="text-align:center;">月份</th>
            <th style="text-align:center;">拆分金额</th>
            <th style="text-align:center;">操作</th>
        </tr>
        </thead>
        <tbody>
        <volist name="data.list_data" id="vo">
            <tr>
                <td align="center">{$vo.product_id}</td>
                <td align="center">{$vo.product_name}</td>
                <td align="center">{$vo.month}</td>
                <td align="center">{$vo.money|round=###,2}</td>
                <td align="center">

                        <a onclick="edit_split_price(this, '{$vo.id}', 1)"
                           style="cursor:pointer;">
                            <nobr>编辑</nobr>
                        </a>

                </td>
            </tr>
        </volist>
        </tbody>
    </table>
</div>

<script>

    $('#auto_money_data').find('div').each(function () {
        var id = $(this).attr('id');
        if (id !== undefined) {
            $('#' + id).on('hidden.bs.collapse', function () {
                // 隐藏之后全部取消
                var checkbox = $('#'+id).find('input[type="checkbox"]:checked');
                var tr = $('#split_price_list tbody').find('tr');
                if (tr.length > 0 && checkbox.length > 0) {
                    $.each(checkbox, function (i, c) {
                        var info = $(c).val().split('_');
                        var month = info[0]
                        var product_id = info[1];
                        $.each(tr, function (index, content) {
                            var td_product_id = $(content).find('td:eq(0)').text();
                            var td_month = $(content).find('td:eq(2)').text();
                            if (td_product_id == product_id && month == td_month) {
                                var money = parseFloat($(content).find('td:eq(3)').text());
                                var br_money = parseFloat($('.splite_money').text());
                                var br_total_money = (br_money - money).toFixed(2);
                                $('.splite_money').html(br_total_money);
                                $(content).remove();
                            }
                        })
                    })
                }
                $('#'+id).find('input[type="checkbox"]').attr('checked', false);
            });
        }
    })

    function checkBox(obj) {
        // 2022-03_210_1230.233333_邦信分-通信字段
        var checked = $(obj).is(':checked');

        var info = $(obj).val().split('_');
        var month = info[0]
        var product_id = info[1];
        var money = info[2];
        var product_name = info[3]

        var tr = $('#split_price_list tbody').find('tr');
        var is_flag = false;
        if (tr.length > 0) {
            $.each(tr, function (index, content) {
                var td_product_id = $(content).find('td:eq(0)').text();
                var td_month = $(content).find('td:eq(2)').text();
                if (td_product_id == product_id && month == td_month) {
                    is_flag = true;
                    if (checked == false) {
                        var money = parseFloat($(content).find('td:eq(3)').text());
                        var br_money = parseFloat($('.splite_money').text());
                        var br_total_money = (br_money - money).toFixed(2);
                        $('.splite_money').html(br_total_money);
                        $(content).remove();
                        return false;
                    } else {
                        $(obj).attr('checked', false);
                        alert('列表中已存在');
                        return false;
                    }
                }
            })
        }

        var br_money = parseFloat($('.splite_money').text());
        var total_money = parseFloat($('.total_money').text());
        var br_total_money = (parseFloat(money) + br_money).toFixed(2);
        // if (total_money < br_total_money) {
        //     alert('拆分金额大于总金额，不可添加');
        //     is_flag = true;
        //     $(obj).attr('checked', false);
        //     return false;
        // }

        if (is_flag == false) {
            // 追加数据
            appendHtml(product_id, product_name, month, money);
            $('.splite_money').html(br_total_money);
        }
    }

    // 添加拆分信息
    $('#add_split_price').click(function () {
        var product_id = $('#product_id').val();
        var product_name = $('#product_id option:selected').text();
        var month = $('#month').val();
        var money = $('#money').val();
        if (valid_empty(product_id)) {
            alert('请选择产品');
            return false;
        }
        if (valid_empty(month)) {
            alert('请选择月份');
            return false;
        }
        //拆分金额
        if (valid_empty(money)) {
            alert('请填写拆分金额');
            return false;
        }
        //金额格式验证
        if (!valid_money_format(money)) {
            alert('拆分金额格式不正确');
            return false;
        }

        var tr = $('#split_price_list tbody').find('tr');
        var is_flag = false;
        if (tr.length > 0) {
            $.each(tr, function (index, content) {
                var td_product_id = $(content).find('td:eq(0)').text();
                var td_month = $(content).find('td:eq(2)').text();
                if (td_product_id == product_id && month == td_month) {
                    is_flag = true;
                    alert('列表中已存在');
                    return false;
                }
            })
        }

        var br_money = parseFloat($('.splite_money').text());
        var total_money = parseFloat($('.total_money').text());
        var br_total_money = (parseFloat(money) + br_money).toFixed(2);
        if (total_money < br_total_money) {
            alert('拆分金额大于总金额，不可添加');
            return false;
        }

        if (is_flag == false) {
            appendHtml(product_id, product_name, month, money);
            $('.splite_money').html(br_total_money);
        }
    });


    function appendHtml(product_id, product_name, month, money) {
        var payment_type = $('input[name="payment_type"]').val();

        var html = '<tr>\n' +
            '                <td align="center">' + product_id + '</td>\n' +
            '                <td align="center">' + product_name + '</td>\n' +
            '                <td align="center">' + month + '</td>\n' +
            '                <td align="center">' + money + '</td>\n' +
            '                <td align="center"> ';
        html += '<a class="edit" onclick="edit_split_price(this, 0, 1)"> 编辑 </a>';
        if (payment_type == 1) {
            html += '<a class="del" onclick="del_split_price(this, 0)"> 删除 </a>';
        }

        html += '</td>\n' +
            '            </tr>';
        $('#split_price_list tbody').append(html);
    }

    //验证制定的数据是否为空
    function valid_empty(val) {
        if (val == '') {
            return true;
        }
        return false;
    }

    //交易金额格式验证
    function valid_money_format(money) {
        let regex = /^(\-?)\d{1,9}(\.\d{1,6})?$/;
        if (regex.test(money)) {
            return true;
        }
        return false;
    }

    function del_split_price(obj, id) {
        if (id == 0) {
            del_splite_money(obj)
            return false;
        }
        if (!confirm('确认要删除吗？')) {
            return false;
        }
        $.ajax({
            url: "{:U('del_split_price')}",
            data: {"id": id},
            type: 'post',
            async: false,
            success: function (res) {
                del_splite_money(obj);
                //console.log(money,br_total_money,br_money)
            },
            error: function () {
                alert('添加失败');
            }
        });
    }

    // 编辑
    function edit_split_price(obj, id, type) {
        var br_money = parseFloat($('.splite_money').text());
        if (type == 1) {
            var money = parseFloat($(obj).parent().parent().find('td:eq(3)').text());
            $(obj).parent().parent().find('td:eq(3)').html('<input type="text" name="edit_money" value="0">');
            $(obj).parent().parent().find('td:eq(4)').html('<a class="edit" onclick="edit_split_price(this, 0, 2)"> 确认 </a>');
            var br_total_money = (br_money - money).toFixed(2);
        } else {
            var money = parseFloat($(obj).parent().parent().find('td:eq(3)').find('input').val());

            var br_total_money = (br_money + money).toFixed(2);
            var total_money = parseFloat($('.total_money').text());
            if (total_money < br_total_money) {
                alert('拆分金额大于总金额，不可编辑');
                return false;
            }

            $(obj).parent().parent().find('td:eq(3)').html(money);
            $(obj).parent().parent().find('td:eq(4)').html('<a class="edit" onclick="edit_split_price(this, 0, 1)"> 编辑 </a>');
        }
        $('.splite_money').html(br_total_money);
    }

    // 删除
    function del_splite_money(obj) {
        var money = parseFloat($(obj).parent().parent().find('td:eq(3)').text());
        var br_money = parseFloat($('.splite_money').text());
        var br_total_money = (br_money - money).toFixed(2);
        $('.splite_money').html(br_total_money);
        $(obj).parent().parent().remove();
    }
</script>