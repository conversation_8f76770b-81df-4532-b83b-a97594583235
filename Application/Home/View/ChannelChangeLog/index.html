<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="panel-heading">
    <div class="row">
        <div class="container">
            <form class="form-inline" action="{:U('')}" method="get" id="form_log">
                <div class="form-group">
                    <label  class="control-label">开始时间：</label>
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? '' : $input['begin']; ?>"/>&nbsp;
                    <label  class="control-label">结束时间：</label>&nbsp;
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? '' : $input['end']; ?>"/>&nbsp;

                    <label class="control-label">运营商：</label>
                    <select class="form-control" name="flow_type">
                        <option value="">全部</option>
                        <option value="10086"<?= ($input['flow_type'] === '10086') ? 'selected' : '' ;?>>移动</option>
                        <option value="10010"<?= ($input['flow_type'] === '10010') ? 'selected' : '' ;?>>联通</option>
                        <option value="189" <?= ($input['flow_type'] === '189') ? 'selected' : '' ;?>>电信</option>
                    </select>&nbsp;

                    <label class="control-label">地区：</label>
                    <select class="form-control" name="area" id="area">
                        <?php if (isset($input['area']) && $input['area']) {?>
                        <option value="<?= $input['area']; ?>" selected><?= $input['area']; ?></option>
                        <?php } ?>

                        <option value="">全部</option>
                        <?php foreach($crawler_areas as $area) {?>
                        <option value="<?= $area; ?>" class="form-control"><?= $area; ?></option>
                        <?php } ?>
                    </select>&nbsp;

                    <label class="control-label">渠道：</label>
                    <select class="form-control" name="nchannel">
                        <option value="">全部</option>
                        <?php foreach ($channel_list as $key => $val) {?>
                        <option value="<?= $key?>" <?= $input['nchannel'] == $key ? 'selected' : ''; ?>><?=$val ?></option>
                        <?php } ?>
                    </select>&nbsp;
                <div class="form-group">
                    <button type="submit" class="btn btn-sm btn-primary">查询</button>
                </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">切换列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>序号</th>
                <th>地区</th>
                <th>运营商</th>
                <th>切换前通道</th>
                <th>切换后渠道</th>
                <th>切换时间</th>
                <th>切换原因</th>
                <th>操作人</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list_show as $key=>$log): ?>
            <tr>
                <td><?= $key+1; ?></td>
                <td><?= $log['area']; ?></td>
                <td><?= isset($flow_type_list[$log['flow_type']]) ? $flow_type_list[$log['flow_type']] : $log['flow_type']; ?></td>
                <td><?= isset($channel_list[$log['ochannel']]) ? $channel_list[$log['ochannel']] : $log['ochannel']; ?></td>
                <td><?= isset($channel_list[$log['nchannel']]) ? $channel_list[$log['nchannel']] : $log['nchannel']; ?></td>
                <td><?= date('Y-m-d H:i:s', $log['created_at']); ?></td>
                <td style="word-break:break-all; width:200px;"><?= $log['reason']; ?></td>
                <td><?= $log['admin']; ?></td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            <?= $page->show(); ?>
        </ul>
    </nav>
</div>
<script type="text/javascript">
    $(function () {
        $("#area").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '地区'
        });

        // check end time
        $("#form_log").submit(function () {
            // get begin time and end time
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var today_str = (new Date()).toDateString();

            // change time format for firefox
            time_end = time_end.replace(/\-/g, '\/');
            time_begin = time_begin.replace(/\-/g, '\/');

            // check begin time
            if (!time_begin && time_end) {
                alert('请选择开始时间');
                return false;
            }

            // check end time
            if (time_begin && !time_end) {
                alert('请选择结束时间');
                return false;
            }

            if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
                alert('请选择有效的结束时间');
                return false;
            }

            // set default time
            if (!time_begin) {
                time_begin = today_str;
            }

            if (!time_end) {
                time_end = today_str;
            }

            // check time
            var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
            if (time_diff < 0) {
                alert('开始时间必须小于结束时间');
                return false;
            }

            // calculate the days between begin and end
            var day_diff =  Math.floor(time_diff/8.64e7);

            //  the time should less than 31
            if (day_diff <= 30) {
                return true;
            } else {
                alert('单次查询时间范围不能超过31天');
                return false;
            }
            return true;
        });
    });
</script>
</body>
</html>