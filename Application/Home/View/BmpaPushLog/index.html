<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__dataTables.css">
    <script src="__JS__jquery.dataTables.js"></script>
    <script src="__JS__jquery.dataTables.bootstrap.js"></script>
    <style>
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <dialog_template></dialog_template>
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="form_init">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="start_time">时间区间：</label>
                        <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                        -
                        <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                    </div>
                    <div class="form-group">
                        <label for="channel">运营商：</label>
                        <select class="form-control" name="channel" id="channel">
                            {$input.channel_option}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="push_status">详单推送状态：</label>
                        <select class="form-control" name="push_status" id="push_status">
                            {$input.push_status_option}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="auth_status">授权成功状态：</label>
                        <select class="form-control" name="auth_status" id="auth_status">
                            {$input.auth_status_option}
                        </select>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li>
                                <input id="searchBtn" type="button" class="btn btn-primary btn-sm" value="查询">
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="row_second">
                    <div class="form-group">
                        <label class="control-label" for="make_status">报告生成状态：</label>
                        <select class="form-control" name="make_status" id="make_status">
                            {$input.make_status_option}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="status_status">详单状态推送状态：</label>
                        <select class="form-control" name="status_status" id="status_status">
                            {$input.status_status_option}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="sid">SID：</label>
                        <input type="text" value="{$input.sid}" class="form-control" name="sid" autocomplete="off" placeholder="请填写完整的SID" id="sid" maxlength="100">
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="tel">Tel：</label>
                        <input type="text" value="{$input.tel}" class="form-control" name="tel" autocomplete="off" placeholder="请填写完整的Tel" id="tel" maxlength="20">
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="cid">Cid：</label>
                        <input type="text" value="{$input.cid}" class="form-control" name="cid" autocomplete="off" placeholder="请填写完整的Cid" id="cid" maxlength="100">
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th>SID</th>
                <th>Tel</th>
                <th>地区</th>
                <th>运营商</th>
                <th>CID</th>
                <th>详单推送状态</th>
                <th>授权成功状态</th>
                <th>报告生成状态</th>
                <th>详单状态推送状态</th>
                <th>推送开始结束时间</th>
             </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td>
                        <a href="{:U('detail')}?sid={$vo.sid}&callback_url={$Think.server.REQUEST_URI|urlencode}">{$vo.sid}</a>
                    </td>
                    <td>{$vo.tel}</td>
                    <td>{$vo.city}</td>
                    <td>{$vo.carrier}</td>
                    <td>{$vo.cid}</td>
                    <td>
                        {$status[$vo['push_call_log']]}
                        &nbsp;
                        <a href="{:U('send')}?type=call_log&sid={$vo.sid}&callback={$Think.server.REQUEST_URI|urlencode}" class="btn btn-primary btn-sm" onclick="return confirm('是否重新推送？')">重推</a>
                    </td>
                    <td>
                        {$status[$vo['push_auth']]}
                            &nbsp;&nbsp;
                            <a href="{:U('send')}?type=auth&sid={$vo.sid}&callback={$Think.server.REQUEST_URI|urlencode}" class="btn btn-primary btn-sm" onclick="return confirm('是否重新推送？')">重推</a>
                    </td>
                    <td>
                        {$status[$vo['push_report_status']]}
                            &nbsp;&nbsp;
                            <a href="{:U('send')}?type=report_status&sid={$vo.sid}&callback={$Think.server.REQUEST_URI|urlencode}" class="btn btn-primary btn-sm" onclick="return confirm('是否重新推送？')">重推</a>
                    </td>
                    <td>
                        {$status[$vo['push_call_log_status']]}
                            &nbsp;&nbsp;
                            <a href="{:U('send')}?type=call_log_status&sid={$vo.sid}&callback={$Think.server.REQUEST_URI|urlencode}" class="btn btn-primary btn-sm" onclick="return confirm('是否重新推送？')">重推</a>
                    </td>
                    <td>
                        开始时间：{$vo.start_time|date='Y-m-d H:i:s', ###}
                        <br/>
                        结束时间：{$vo.end_time|date='Y-m-d H:i:s', ###}
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        $("#searchBtn").click(function () {
            let start_time = $("#start_time").val();
            let end_time = $("#end_time").val();
            let start_date = Date.parse(start_time);
            let end_date = Date.parse(end_time);
            let cha = Math.abs(start_date - end_date);
            cha = Math.floor(cha / (24 * 3600 * 1000));
            if (cha>31) {
                alert('查询范围需要小于31天！！！');
                return false;
            }
            $("#form_init").submit();
        });
    });

</script>
</body>
</html>
