<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__dataTables.css">
    <script src="__JS__jquery.dataTables.js"></script>
    <script src="__JS__jquery.dataTables.bootstrap.js"></script>
    <style>
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        td{
            text-align: center;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <dialog_template></dialog_template>
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="form_init">
                <div class="row-first">
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li>
                                <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm">返回列表</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <volist name="data" id="vo">
                <tr>
                    <td>{$key}</td>
                    <td>
                        <if condition="is_array($vo)">
                            {$vo|json_encode=###,JSON_UNESCAPED_UNICODE}
                            <else/>
                            {$vo}
                        </if>
                    </td>
                </tr>
            </volist>
        </table>

    </div>
</div>
</div>
</body>
</html>
