<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css" />
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <include file="public" />
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">

                <div class="form-group">
                    <label  class="control-label">开始时间</label>&nbsp;
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d', strtotime('-1 month')) : $input['begin']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group">
                    <label  class="control-label">结束时间</label>&nbsp;
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group">
                    <select class="form-control" name="flow_type" id="flow_type_sel">
                        <option value="" selected>运营商</option>
                        <option value="10086" <?= ($input['flow_type'] === '10086') ? 'selected' : '' ;?>>移动</option>
                        <option value="10010" <?= ($input['flow_type'] === '10010') ? 'selected' : '' ;?>>联通</option>
                        <option value="189" <?= ($input['flow_type'] === '189') ? 'selected' : '' ;?>>电信</option>
                    </select>
                </div>
                &nbsp;
                <div class="form-group">
                    <select class="form-control" name="crawler_channel" id="crawler_channel">
                        <option value="" >全部渠道</option>
                        <?php foreach ($crawler_channel_list as $index => $crawler_channel) { ?>
                        <option value="<?= $index ;?>" <?= (isset($input['crawler_channel']) && $input['crawler_channel'] == $index) ? 'selected' : '' ?>><?= $crawler_channel;?></option>
                        <?php } ?>
                    </select>
                </div>
                &nbsp;
                <div class="form-group">
                    <select class="form-control" name="province" id="province_sel">
                        <option value="">地区</option>
                        <?php foreach ($crawler_areas as $province) {?>
                        <option value="<?= $province;?>" <?= (isset($input['province']) && $input['province'] == $province) ? 'selected' : ''?>><?= $province; ?></option>
                        <?php } ?>
                    </select>
                </div>
                &nbsp;
                <div class="form-group"  >
                    <select class="form-control" id="choose_user_sel" name="id">
                        <option value="" >选择账号</option>
                        <foreach name="user_list"  item="user">
                            <option value="{$user['id']}" <?= (isset($input['id']) && $input['id'] == $user['id']) ? 'selected' : '' ?> >{$user['developer']}</option>
                        </foreach>
                    </select>
                </div>
                &nbsp;
                <div class="form-group">
                    <a class="btn btn-info" href="{:U('index')}" role="button">返回列表</a>
                </div>
                &nbsp;
                <div class="form-group">
                        <input type="submit" onclick="return checkTime();" class="btn btn-primary  btn-block" value="查询">
                </div>
                &nbsp;
                <div class="form-group">
                    <button type="button" id="file_export" class="btn btn-success  btn-block">导出 </button>
                </div>
                <input type="hidden" value="{:I('get.field')}" id="field" name="field">
                <input type="hidden" value="{:I('get.order')}" id="order" name="order">
            </form>

        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th >时间</th>
                <th style="cursor:pointer;" field="total_nums">尝试授权量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="authen_nums">授权成功量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="authen_pct">授权成功率<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="crawl_nums">爬取成功量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="crawl_pct">爬取成功率<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="report_nums">报告生成量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="tel_num">爬取成功号码量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="pwd_rt_total">尝试重置密码量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="pwd_rt_success">重置密码成功量<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="pwd_rt_pct">重置密码成功率<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
                <th style="cursor:pointer;" field="log_loss_pct">详单缺失率<span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>总计</td>
                <td><?= isset($total_data['total_nums']) ? $total_data['total_nums'] : 0; ?></td>
                <td><?= isset($total_data['authen_nums']) ? $total_data['authen_nums'] : 0; ?></td>
                <td><?= isset($total_data['authen_pct']) ? $total_data['authen_pct'] : '0.00%'; ?></td>
                <td><?= isset($total_data['crawl_nums']) ? $total_data['crawl_nums'] : 0; ?></td>
                <td><?= isset($total_data['crawl_pct']) ? $total_data['crawl_pct'] : '0.00%'; ?></td>
                <td><?= isset($total_data['report_nums']) ? $total_data['report_nums'] : 0; ?></td>
                <td><?= isset($total_data['tel_num']) ? $total_data['tel_num'] : 0; ?></td>
                <td><?= isset($total_data['pwd_rt_total']) ? $total_data['pwd_rt_total'] : 0; ?></td>
                <td><?= isset($total_data['pwd_rt_success']) ? $total_data['pwd_rt_success'] : 0; ?></td>
                <td><?= isset($total_data['pwd_rt_pct']) ? $total_data['pwd_rt_pct'] : '0.00%'; ?></td>
                <td><?= isset($total_data['log_loss_pct']) ? $total_data['log_loss_pct'] : '0.00%'; ?></td>
            </tr>

            <?php foreach ($date_stat_show as $key => $stat) {?>
            <tr>
                <td><?= $key; ?></td>
                <td><?= isset($stat['total_nums']) ? $stat['total_nums'] : 0; ?></td>
                <td><?= isset($stat['authen_nums']) ? $stat['authen_nums'] : 0; ?></td>
                <td><?= isset($stat['authen_pct']) ? $stat['authen_pct'] : '0.00%'; ?></td>
                <td><?= isset($stat['crawl_nums']) ? $stat['crawl_nums'] : 0; ?></td>
                <td><?= isset($stat['crawl_pct']) ? $stat['crawl_pct'] : '0.00%'; ?></td>
                <td><?= isset($stat['report_nums']) ? $stat['report_nums'] : 0; ?></td>
                <td><?= isset($stat['tel_num']) ? $stat['tel_num'] : 0; ?></td>
                <td><?= isset($stat['pwd_rt_total']) ? $stat['pwd_rt_total'] : 0; ?></td>
                <td><?= isset($stat['pwd_rt_success']) ? $stat['pwd_rt_success'] : 0; ?></td>
                <td><?= isset($stat['pwd_rt_pct']) ? $stat['pwd_rt_pct'] : '0.00%'; ?></td>
                <td><?= isset($stat['log_loss_pct']) ? $stat['log_loss_pct'] : '0.00%'; ?></td>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">

    $(document).ready(function() {

        // choose cuishou user
        $("#choose_user_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号'
        });

        // choose province
        $("#province_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择地区',
            width : '100px'
        });

        // export file
        $("#file_export").click(function () {
            if (!checkTime()){
                return false;
            }

            // init params
            var params = '';
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var flow_type = $('#flow_type_sel').val();
            var crawler_channel = $('#crawler_channel').val();
            var province_sel = $('#province_sel').val();
            var choose_user_sel = $('#choose_user_sel').val();

            if (time_begin) {
                params = params + '&begin='+time_begin;
            }
            if (time_end) {
                params = params + '&end=' + time_end;
            }
            if (flow_type) {
                params = params + '&flow_type=' + flow_type;
            }
            if (crawler_channel) {
                params = params + '&crawler_channel=' + crawler_channel;
            }
            if (province_sel) {
                params = params + '&province=' + province_sel;
            }
            if (choose_user_sel) {
                params = params + '&id=' + choose_user_sel;
            }

            // tidy url
            if (params) {
                params=params.replace('&', '?');
            }

            var url_export = '/Home/BmCrawlerStatFile/detail' + params;

            $.fileDownload(url_export);
            return false;
        });

    });

    function checkTime() {

        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();

        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }

        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }

        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }

        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }

        if (!time_end) {
            time_end = today_str;
        }

        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }

        // calculate the days between begin and end
        var day_diff =  Math.floor(time_diff/8.64e7);

        //  the time should less than 31
        if (day_diff > 365) {
            alert('单次查询时间范围不能超过365天');
            return false;
        }

        // check choose user
        var choose_user = $('#choose_user_sel').val();
        if (!choose_user) {
            alert('请选择账号');
            return false;
        }
        return true;
    }
    showHtmlByOrder();
</script>
</body>
</html>
