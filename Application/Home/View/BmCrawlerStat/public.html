<style>
    .spinner {
        margin: 100px auto;
        width: 100px;
        height: 80px;
        text-align: center;
        font-size: 10px;
        position:absolute;
        top:50%;
        margin-top:-40px;
        left:50%;
        margin-left:-50px;
    }
    .spinner > div {
        background-color: #31b0d5;
        height: 100%;
        width: 9px;
        display: inline-block;

        -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
        animation: stretchdelay 1.2s infinite ease-in-out;
    }

    .spinner .rect2 {
        -webkit-animation-delay: -1.1s;
        animation-delay: -1.1s;
    }

    .spinner .rect3 {
        -webkit-animation-delay: -1.0s;
        animation-delay: -1.0s;
    }

    .spinner .rect4 {
        -webkit-animation-delay: -0.9s;
        animation-delay: -0.9s;
    }

    .spinner .rect5 {
        -webkit-animation-delay: -0.8s;
        animation-delay: -0.8s;
    }

    @-webkit-keyframes stretchdelay {
        0%, 40%, 100% { -webkit-transform: scaleY(0.4) }
        20% { -webkit-transform: scaleY(1.0) }
    }

    @keyframes stretchdelay {
        0%, 40%, 100% {
            transform: scaleY(0.4);
            -webkit-transform: scaleY(0.4);
        }  20% {
               transform: scaleY(1.0);
               -webkit-transform: scaleY(1.0);
           }
    }
</style>
<script>
    $(function () {
        //全表排序查询
        $("th[field]").click(function () {
            buildLoadingAnimate();
            //查看排序的字段
            var field = $(this).attr('field');
            //查看当前排序是否为按这个字段的排序
            var desc = $(this).find('.v-icon-down-dir').hasClass('checked');
            var order;
            if (desc) {
                order = 'field=' + field + '&order=asc';
            } else {
                order = 'field=' + field + '&order=desc';
            }
            var search = location.search;
            var url = delParam(['field', 'order']);
            if (url.indexOf('?') == -1) {
                location.href = url + '?' + order;
            } else {
                location.href = url + '&' + order;
            }
        });
    });
    //删除url中指定的参数
    function delParam(paramKey) {
        var url = window.location.href;    //页面url
        var urlParam = window.location.search.substr(1);   //页面参数
        var beforeUrl = url.substr(0, url.indexOf("?"));   //页面主地址（参数之前地址）
        var nextUrl = "";

        var arr = new Array();
        if (urlParam != "") {
            var urlParamArr = urlParam.split("&"); //将参数按照&符分成数组
            for (var i = 0; i < urlParamArr.length; i++) {
                var paramArr = urlParamArr[i].split("="); //将参数键，值拆开
                //如果键雨要删除的不一致，则加入到参数中
                if ($.inArray(paramArr[0], paramKey) == -1) {
                    arr.push(urlParamArr[i]);
                }
            }
        }
        if (arr.length > 0) {
            nextUrl = "?" + arr.join("&");
        }
        url = beforeUrl + nextUrl;
        return url;
    }
    //排序按钮展示效果
    function showHtmlByOrder()
    {
        var field = $("#field").val();
        var order = $("#order").val();
        if (order == 'desc') {
            $('th[field="' + field + '"]').find('.v-icon-down-dir').addClass('checked');
        } else if (order == 'asc') {
            $('th[field="' + field + '"]').find('.v-icon-up-dir').addClass('checked');
        }
    }
    //加载动画
    function buildLoadingAnimate()
    {
        var display = '<div style="width:100%;height:100%;position:fixed;top:0;left:0;background:rgba(0,0,0,0.5);z-index:100;" id="loading"><div class="spinner"> <div class="rect1"></div><div class="rect2"></div><div class="rect3"></div><div class="rect4"></div><div class="rect5"></div></div></div>';
        $("body").append(display);
    }
</script>