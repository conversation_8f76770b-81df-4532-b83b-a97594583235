<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">

    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" id="form_init">
                <div class="row-first">
                    <input type="hidden" id="id" name="id" value="<?= $input['id'] ?>">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"
                               value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d',strtotime('-1 day')) : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control"
                               value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d',strtotime('-1 day')) : $input['end']; ?>"/>
                    </div>

                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" class="btn btn-primary btn-sm"
                                       value="查询"></li>
                            </li>
                            <li>
                                <a class="btn btn-info btn-sm" id="you_know" href="{:U('/FeeAccountStat/index',['begin'=>$input['begin'],'end'=>$input['end']])}" role="button">返回</a>
                            </li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <tr align="center">
                <th>产品账号ID</th>
                <th>产品账号名称</th>
                <th>产品账号类型</th>
                <th>计费依据</th>
                <th>计费方式</th>
                <th>计费用量</th>
                <th>费用（元）</th>
            </tr>
            <tr>
                <td></td>
                <td>总计</td>
                <td></td>
                <td></td>
                <td></td>
                <td><?= $list_stat['amount_total'] ?></td>
                <td><?= $list_stat['fee_total'] ?></td>
            </tr>

            <?php
            if($list_stat['data']){
                foreach ($list_stat['data'] as $stat) {
            ?>
                <tr>
                    <td>{$stat['product_id']}</td>
                    <td>
                        <?php if($stat['fee_method']=='按用量'){
                                if ($stat['type_id'] == 6) {
                        ?>
                        <a href="{:U('/Home/FeeRiskListStat/feeDay',['account_id'=>$input['id'],'product_id'=>$stat['product_id'],'begin_date'=>$input['begin'],'end_date'=>$input['end']])}">{$stat['product_name']}</a>
                        <?php } elseif ($stat['type_id'] == 4) { ?>
                        <a href="{:U('/Home/FeeBangProductStat/feeDay',['account_id'=>$input['id'],'product_id'=>$stat['product_id'],'begin_date'=>$input['begin'],'end_date'=>$input['end']])}">{$stat['product_name']}</a>
                        <?php } else { ?>
                        <a href="{:U('productDetail',['id'=>$input['id'],'product_id'=>$stat['product_id'],'type_id'=>$stat['type_id'],'begin'=>$input['begin'],'end'=>$input['end']])}">{$stat['product_name']}</a>
                        <?php }} else { echo $stat['product_name'];} ?>
                    </td>
                    <td><?= $stat['product_type'] ;?></td>
                    <td><?= $stat['fee_basis'] ;?></td>
                    <td><?= $stat['fee_method'] ;?></td>
                    <td><?php if($stat['fee_method']=='按用量'){echo $stat['amount_total'];}else{echo 'NA';} ?></td>
                    <td><?= $stat['fee_total']; ?></td>
                </tr>
            <?php }} ?>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">
    $(function () {
        // export file
        $("#file_export").click(function () {
            var params = genParamsForFile();
            var url_export = '/Home/FeeAccountStatFile/productList' + params;
            $.fileDownload(url_export);
            return false;
        });
    });

    // 为导出文件生成参数
    function genParamsForFile() {
        var params = '';
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var id = $('#id').val();

        if (time_begin) {
            params += '&begin=' + time_begin;
        }
        if (time_end) {
            params += '&end=' + time_end;
        }
        if (id) {
            params += '&id=' + id;
        }
        // tidy url
        if (params) {
            params = params.replace('&', '?');
        }
        return params;
    }
</script>
</body>
</html>
