<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__highlight.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="index" method="post">

                <div class="form-group">
                    <label class="control-label">查询类型</label>&nbsp;
                    <select class="form-control" name="collection" id="collection">
                        <option value="user_info" <?= ($input['collection'] === 'user_info') ? 'selected' : '' ;?>>user_info</option>
                        <option value="report" <?= ($input['collection'] === 'report') ? 'selected' : '' ;?>>report</option>
                        <option value="call_log" <?= ($input['collection'] === 'call_log') ? 'selected' : '' ;?>>call_log</option>
                        <option value="phone_bill" <?= ($input['collection'] === 'phone_bill') ? 'selected' : '' ;?>>phone_bill</option>
                        <option value="sid_info" <?= ($input['collection'] === 'sid_info') ? 'selected' : '' ;?>>sid_info</option>
                        <option value="calls_push_log" <?= ($input['collection'] === 'calls_push_log') ? 'selected' : '' ;?>>calls_push_log</option>
                        <option value="sms" <?= ($input['collection'] === 'sms') ? 'selected' : '' ;?>>sms</option>
                        <option value="network" <?= ($input['collection'] === 'network') ? 'selected' : '' ;?>>network</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="control-label">Sid</label>
                    <input type="text" name="sid" class="form-control" id="sid" value="<?= isset($input['sid']) ? $input['sid'] : ''; ?>">
                </div>
                &nbsp;

                <div class="form-group">
                    <input type="submit" onclick="return checkParams()" class="btn btn-primary  btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <button class="btn btn-sm btn-success" onclick="return fileDown()" style="float: right;">导出</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <?php foreach($msg as $v) { ?>
        <pre class="prettyprint lang-javascript" style="background:#fff">
            <?= htmlentities($v) ?>
        </pre>
    <?php } ?>
</div>
</body>
<script type="application/javascript">
    function checkParams () {
        var list_collection= ['user_info', 'report', 'call_log', 'phone_bill', 'sid_info', 'calls_push_log', 'sms', 'network'];
        var collection = $('#collection').val();
        var sid = $("#sid").val();

        if (!sid) {
            alert('请填写要查询的sid');
            return false;
        }

        if (list_collection.indexOf(collection) === -1) {
            alert('查询类型错误');
            return false;
        }
        return true;
    }

    function fileDown() {
        if (!checkParams()) {
            return false;
        }
        var sid = $('#sid').val();
        var collection = $('#collection').val();
        var params = {sid:sid, collection:collection, request_type : 'file_down'};
        var url_export = '/Home/CrawlerInfoList/index';

        $.fileDownload(url_export, {
            httpMethod : 'POST',
            data : params,
            failCallback : function (responseHtml, url) {
                alert('查询结果为空, 请检查参数');
            }
        });
        return false;
    }
</script>
</html>
