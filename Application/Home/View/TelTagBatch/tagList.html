<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" type="text/css" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-horizontal" action="{:U('')}" method="post">
                <div class="form-group" style="margin-bottom: 0px;">
                    <label class="col-sm-2 control-label" style="font-size:16px;">选择标签</label>
                    <div class="input-group col-sm-6">
                        <select id="itag_sel" class="form-control" name="itag_id">
                            <?php if (isset($input['itag'])) { ?>
                            <option value="{$input['itag_id']}" >{$input['itag']}</option>
                            <?php } ?>
                            <option value="" >请选择标签</option>
                            <foreach name="itag_list"  item="itag">
                                <option value="{$itag['id']}" >{$itag['itag']}</option>
                            </foreach>

                        </select>
                        <span class="input-group-btn">
						<button class="btn btn-primary" type="submit" onclick="return checkItag()">
							<span class="glyphicon glyphicon-search"><span style="margin-left:10px; font-size:16px;">查询</span></span>
						</button>
					    </span>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <tr align="center" style="background-color: #f5f5f5;">
                <th>itag_id</th>
                <th>标签名称</th>
                <th>号码数量</th>
            </tr>
            <?php if (isset($input)) {?>
            <tr>
                <td><?= $input['itag_id'] ;?></td>
                <td><?= $input['itag'] ;?></td>
                <td><?= $itag_stat ;?></td>
            </tr>
            <?php } ?>

        </table>
    </div>
</div>
</div>
<script type="text/javascript">

    $(document).ready(function() {
        $("#itag_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '请选择标签'
        });
    });

    // check itag
    function checkItag() {
        var itag_sel = $('#itag_sel').val();

        // check itag_id
        if (!itag_sel) {
            alert('请选择标签');
            return false;
        }
        return true;
    }
</script>
</body>
</html>
