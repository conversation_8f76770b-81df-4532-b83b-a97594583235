<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-body">
        <form class="form-horizontal" action="{:U('')}" id="tel_tag_form">

            <div class="form-group">
                <label  class="col-sm-2 control-label">选择文件</label>
                <div class="col-sm-5">
                        <input type="file" name="tel_list"/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label">标签</label>
                <div class="col-sm-5" >
                    <select class="form-control" name="itag_id" id="itag_sel">
                        <foreach name="tag_list"  item="tag">
                            <option value="{$tag['id']}" >{$tag['itag']}</option>
                        </foreach>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label">类型</label>
                <div class="col-sm-5">
                        <input type="radio" name="up_status" value="1" checked> 上线 &nbsp;
                        <input type="radio" name="up_status" value="0"> 下线
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button type="submit" style="width: 80%" class="btn btn-primary  btn-block">上传</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){

        $("#itag_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '请选择标签'
        });

        // submit form
        $("#tel_tag_form").submit(function(){
            event.preventDefault();
            var formData = new FormData($(this)[0]);
            $.ajax({
                url: $(this).attr("action"),
                type: 'POST',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (upload_data) {
                    alert(upload_data.info);
                }
            });
        });
    });
</script>
</body>
</html>
