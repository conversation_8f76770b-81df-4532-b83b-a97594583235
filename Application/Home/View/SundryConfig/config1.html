<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <style>
        textarea {
            width  : 100%;
            height : 500px !important;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="update_account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="{$editUrl}" method="post" class="form-horizontal" id="form_product">

                            <div class="form-group">
                                <label class="control-label col-md-3">配置名称：</label>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" value="{$data.name}" readonly disabled>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="config" class="control-label col-md-3">配置内容：</label>
                                <div class="col-md-4">
                                    <textarea id="config" name="config" class="form-control">{$data.config}</textarea>
                                </div>
                            </div>
                            <div class="pull-right">
                                <input type="submit" class="btn btn-primary btn-sm" value="更新">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $(function () {
        // 表单提交事件
        formSubmit();

    });

    // 表单提交事件
    function formSubmit() {
        $('#form_product').submit(function () {
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request  = $(that).attr('action');
        var url_redirect = "{$editUrl}";

        $.post(url_request, data_request).success(function (response) {
            if (response.status !== 0) {
                alert(response.info);
                return '';
            }
            alert('编辑成功');
            window.location.href = url_redirect;
        }).error(function (response) {
            alert('系统出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        return true;
    }

</script>
</body>
</html>
