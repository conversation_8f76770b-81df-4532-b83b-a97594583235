
<div class="container">

    <div class="panel-body">
        <form class="form-horizontal" id="form_set_id" role="form">
            <input type="hidden"  name="ochannel"  value="<?= $selchannel ?>" />
            <div class="form-group channel-sel">
                <label for="channel" class="col-sm-2 control-label">选择服务器</label>
                <div class="col-sm-10">
                <?php if ($channel && is_array($channel)):
                        foreach ($channel as $key => $value):
                            if ($surpport == '未支持' && in_array($key, ['loc', 'ext'])):
                                continue;
                            endif;
                ?>
                    <label class="radio-inline">
                        <input type="radio" name="channel" class="channel-radio" value="{$key}" <?php if($key == $selchannel):?>checked<?php endif;?>> {$value}
                    </label>
                <?php
                        endforeach;
                    endif;
                ?>
                </div>
            </div>

            <div class="form-group">
                <label for="desc" class="col-sm-2 control-label">服务器说明</label>
                <div class="col-sm-10">
                    <input type="text" name="des" class="form-control" id="desc" placeholder="简单描述...">
                </div>
            </div>

            <div class="form-group">
                <label for="desc" class="col-sm-2 control-label">切换理由</label>
                <div class="col-sm-10">
                    <input type="text" name="channel_reason" class="form-control" placeholder="切换理由...">
                </div>
            </div>

        </form>

    </div>
    <div class="panel-footer">
        <div class="alert alert-warning" role="alert">
            <ul>
                <li>说明：</li>
                <li>服务器分<?= count($channel) ?>种：<?= implode('、', array_values($channel));?>，默认为羽乐科技。</li>
                <li>选择羽乐科技备用服务器时，服务地址必选</li>
            </ul>
        </div>
    </div>

</div>
<script type="text/javascript">
    $(function(){
        if ('ext' != $('input[name="ochannel"]').val()) {
            $("div.ext-dist").remove();
        } else {
            $("div.ext-dist").remove();
            createDistInput();
        }

        $("input:radio.channel-radio").on('change', function(event) {
            event.preventDefault();
            if ('ext' != $(this).val()) {
                $("div.ext-dist").remove();
            } else {
                $("div.ext-dist").remove();
                createDistInput();
            }
        });
    });

    function createDistInput()
    {
        var htm = '<div class="form-group ext-dist"> \
                <label for="dist" class="col-sm-2 control-label">服务地址</label> \
                <div class="col-sm-10"> \
                  <input type="text" class="form-control" name="dist" id="dist" value="{$yulore_spare}" placeholder="输入爬虫服务地址"> \
                </div> \
              </div>';
        $("div.channel-sel").after(htm);
    }

    /*锁定当前单选按钮的状态*/

    var channel = $("#channel").val();
    if(channel == 'loc') {
        $("input[name='channel'][value='loc']").attr("checked",true);
    }
    if(channel == 'ext') {
        $("input[name='channel'][value='ext']").attr("checked",true);
    }
    if(channel == 'thd') {
        $("input[name='channel'][value='thd']").attr("checked",true);
    }

</script>
