<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <style>
        .panel-heading .btn-group{
            margin-bottom:  5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="panel-heading">
    <div class="btn-group">
        <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <if condition="$flow_type == 'all'">
                运营商
                <else/>
                {$flow_map[$flow_type]}
            </if>
            <span class="caret"></span>
        </button>
        <ul class="dropdown-menu">
            <li><a href="{:U('lists',array('flow_type'=>'all','channel'=>$channelCheck,'whsupport'=>$whsupport))}">运营商</a></li>
            <li><a href="{:U('lists',array('flow_type'=>'10086','channel'=>$channelCheck,'whsupport'=>$whsupport))}">移动</a></li>
            <li><a href="{:U('lists',array('flow_type'=>'10010','channel'=>$channelCheck,'whsupport'=>$whsupport))}">联通</a></li>
            <li><a href="{:U('lists',array('flow_type'=>'189','channel'=>$channelCheck,'whsupport'=>$whsupport))}">电信</a></li>
        </ul>
    </div>

    <div class="btn-group">
        <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <if condition="$channelCheck == 'all'">
                服务器
                <else/>
                {$channel[$channelCheck]}服务器
            </if>
            <span class="caret"></span>
        </button>
        <ul class="dropdown-menu">
            <li><a href="{:U('lists',array('flow_type'=>$flow_type,'channel'=>'all','whsupport'=>$whsupport))}">服务器</a></li>
            <?php foreach ($channel as $key => $value) { ?>
            <li><a href="{:U('lists',array('flow_type'=>$flow_type,'channel'=>$key,'whsupport'=>$whsupport))}">{$value}服务器</a></li>
            <?php } ?>
        </ul>
    </div>

    <div class="btn-group">
        <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <if condition="$whsupport == 'all'">
                是否支持
                <else/>
                {$whsupport}
            </if>
            <span class="caret"></span>
        </button>
        <ul class="dropdown-menu">
            <li><a href="{:U('lists',array('flow_type'=>$flow_type,'whsupport'=>'all','channel'=>$channelCheck))}">是否支持</a></li>
            <li><a href="{:U('lists',array('flow_type'=>$flow_type,'whsupport'=>'已支持','channel'=>$channelCheck))}">已支持</a></li>
            <li><a href="{:U('lists',array('flow_type'=>$flow_type,'whsupport'=>'未支持','channel'=>$channelCheck))}">未支持</a></li>
        </ul>
    </div>

    <?php if($show_channel_one_key) {?>
    <div class="btn-group">
        <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                一键切换服务器
            <span class="caret"></span>
        </button>
        <ul class="dropdown-menu">
            <li>
            <?php foreach ($channel as $key => $value) { ?>
                <a href="javascript:void(0);" onclick="ChannelKeySwitch($(this))" channel="<?= $key ?>" intro="一键切<?= $value ?>">一键切<?= $value ?> </a>
            <?php } ?>
            </li>
        </ul>
    </div>
    <?php } ?>
</div>
<input type="hidden" id="whesupport" value="<?php echo isset($_GET['whsupport'])?$_GET['whsupport']:'all' ?>"/>
<div class="container">
    <div class="panel panel-default table-responsive">
        <div class="panel-heading"><h3 class="panel-title">爬虫列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>爬虫名称</th>
                <th>BMP支持</th>
                <th>服务器</th>
                <th>服务地址</th>
                <th>爬虫渠道</th>
                <th>重置密码渠道</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list as $key => $value): ?>
                <tr>
                    <td>{$value['name']}</td>
                    <td>{$value['surpport']}</td>
                    <td>{$value['channel_name']}</td>
                    <td>{$value['dist']}</td>
                    <td>{$value['crawler_channel']}</td>
                    <td>{$value['pwd_channel']}</td>
                    <td>
                        <div>
                            <a href="javascript:;" onclick="DHB.INFO.set('{:U('/Home/Crawler/channelSwitch',['province'=>$value['area'],'flow_type'=>$value['flow_type'],'channel'=>$value['channel'], 'surpport' => $value['surpport']])}','切换服务器')" class="btn btn-info btn-xs">切换服务器</a>
                            <a href="javascript:;" onclick="channel_info('{$value[\'flow_type\']}','{$value[\'area\']}', 1)" class="btn btn-primary btn-xs <?php if ($value['surpport'] == '未支持'): ?> disabled <?php endif ?>">切换渠道</a>
                            <a href="javascript:;" onclick="pwd_channel('{$value[\'flow_type\']}', '{$value[\'area\']}', 1)" class="btn btn-success btn-xs">切换密码重置渠道</a>
                            <a href="#switch" onclick="switch_info('{$value[\'flow_type\']}', '{$value[\'area\']}', '{$value[\'channel\']}', '{$value[\'surpport\']}', 1)" data-toggle="modal" class="btn btn-danger btn-xs">切换支持状态</a>
                        </div>
                    </td>
                </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>
</div>

<div id="channel" class="modal fade" tabindex="-1" style="margin-top: 16%">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="channel_type">切换爬虫渠道</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group">
                        <label for="channel_url" class="col-sm-2 control-label"><span style="color:red;"> * </span> 渠道：</label>
                        <div class="col-sm-10" id="channel_name">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-danger" data-dismiss="modal">关闭</button>
                <button id="channel_submit" class="btn btn-sm btn-primary">提交</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>

<div id="switch" class="modal fade" tabindex="-1" style="margin-top: 16%">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">切换支持状态</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group">
                        <label for="channel_url" class="col-sm-2 control-label"><span style="color:red;"> * </span> 支持状态</label>
                        <div class="col-sm-10">
                            <label class="radio-inline">
                                <input type="radio" name="surpport" class="channel-radio" value="已支持"> 已支持
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="surpport" class="channel-radio" value="未支持"> 未支持
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-sm btn-danger" data-dismiss="modal">关闭</button>
                    <button id="switch_submit" class="btn btn-sm btn-primary">提交</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal -->
    </div>
</div>
<script type="text/javascript">
    //页面加载则执行的函数  预先判断,是否支持
    $(document).ready(function() {
        var whe = $("#whesupport").val();
        if(whe == '未支持') {
            $("input[name='whsupport'][value='未支持']").attr("checked",true);
        }
        if(whe == '已支持') {
            $("input[name='whsupport'][value='已支持']").attr("checked",true);
        }
        if(whe == 'all') {
            $("input[name='whsupport'][value='all']").attr("checked",true);
        }

    });

    // change all the channels by one click
    function ChannelKeySwitch(that) {
        var channel =  $(that).attr('channel');
        var intro = $(that).attr('intro');
        var support = $("#whesupport").val();
        var flow_type = '{$flow_type}';
        var sel_channel = '{$channelCheck}';

        if (!confirm('您确定要' + intro + '？')) {
            return false;
        }

        // check reason
        var reason = prompt('请填写切换服务器的理由');
        if (!reason) {
            alert('您未填写切换理由');
            return false;
        }

        reason = reason.trim();
        if (reason.length  === 0) {
            alert('您未填写切换理由');
            return false;
        }

        if (reason.length > 200) {
            alert('理由长度超过限制, 请保持在200以内');
            return false;
        }

        // 切备用,填写地址信息
        var ext_url = '';
        if (channel === 'ext') {
            ext_url = prompt('请填写服务地址({$yulore_spare})', '{$yulore_spare}');
            ext_url = ext_url.trim();
            if (ext_url.length === 0) {
                alert('Sorry, 必须填写服务地址');
                return false;
            }
        }

        // channel
        $.get('/Home/ChannelAKeySwitch/ChannelKeySwitch', {
            channel : channel,
            sel_channel : sel_channel,
            support : support,
            flow_type : flow_type,
            reason : reason,
            ext_url : ext_url
        }).success(function (response) {
            alert(response.info);
        }).error(function(response) {
            alert(response.info);
        });
        setTimeout('window.location.href = location.href', 500);
    }

    //渠道切换
    function channel_info(flow_type, area, type)
    {
        if (type == 1) {
            $('#channel_type').html('切换爬虫渠道');
            $('#channel_submit').attr('onclick', 'channel_info("'+flow_type+'", "'+area+'",2)');
            var info = {"type": type, "flow_type": flow_type, "area": area};
        }
        if (type == 2) {
            var channel_name = $('input[name="channel_name"]:checked').val();
            var info = {"type": type, "flow_type": flow_type, "area": area, 'channel_name': channel_name};
        }
        $.post('/Home/Crawler/crawlerChannel', info).success(function(data) {
            if (data.status == 'error') {
                alert(data.info);
                return false;
            }
            if (type == 1) {
                $('#channel').modal('show');
                var html = '';
                $.each(data.data, function(index, content) {
                    html += '<label class="radio-inline">\
                                <input type="radio" name="channel_name" class="channel-radio" value="'+content.channel_name+'" '+(content.channel_flag == 1 ? 'checked' : '')+'> '+content.channel_name+'\
                            </label>';
                });
                $('#channel_name').html(html);
            } else {
                alert(data.info);
                $('#channel').modal('hide');
            }
        }).error(function(data) {
            alert(data.info);
        })
        if (type == 2) {
            location.reload();
        }
    }

    //重置密码渠道切换
    function pwd_channel(flow_type, area, type)
    {
        if (type == 1) {
            $('#channel_type').html('切换密码重置渠道');
            $('#channel_submit').attr('onclick', 'pwd_channel("'+flow_type+'", "'+area+'",2)');
            var info = {"type": type, "flow_type": flow_type, "area": area};
        }
        if (type == 2) {
            var channel_name = $('input[name="channel_name"]:checked').val();
            var info = {"type": type, "flow_type": flow_type, "area": area, 'channel_name': channel_name};
        }
        $.post('/Home/Crawler/pwdChannel', info).success(function(data) {
            if (data.status == 'error') {
                alert(data.info);
                return false;
            }
            if (type == 1) {
                $('#channel').modal('show');
                var html = '';
                $.each(data.data, function(index, content) {
                    html += '<label class="radio-inline">\
                                <input type="radio" name="channel_name" class="channel-radio" value="'+content.channel_name+'" '+(content.channel_flag == 1 ? 'checked' : '')+'> '+content.channel_name+'\
                            </label>';
                });
                $('#channel_name').html(html);
            } else {
                alert(data.info);
                $('#channel').modal('hide');
            }
        }).error(function(data) {
            alert(data.info);
        })
        if (type == 2) {
            location.reload();
        }
    }

    //支持状态切换
    function switch_info(flow_type, area, channel, surpport, type)
    {
        if (type == 1) {
            $('input[name="surpport"][value="'+surpport+'"]').attr('checked', true);
            $('#switch_submit').attr('onclick', 'switch_info("'+flow_type+'", "'+area+'", "'+channel+'", "", 2)');
        }

        if (type == 2) {
            var surpport_status = $('input[name="surpport"]:checked').val();
            if (surpport_status == '未支持') {
                if (!confirm('状态切换至未支持，服务器将自动切换至信德服务器，是否确认操作？')) {
                    return false;
                }
            }
            $.post('/Home/Crawler/switchBack', {
                flow_type: flow_type,
                area: area,
                channel: channel,
                surpport: surpport_status
            }).success(function(data) {
                alert(data.info);
            }).error(function(data) {
                alert(data.info);
            })
            location.reload();
        }
    }

</script>
</body>
</html>