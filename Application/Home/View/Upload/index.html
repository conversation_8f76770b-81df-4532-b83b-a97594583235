<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="start_time">交易日期：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="status">状态：</label>
                    <select class="form-control" name="status" id="status">
                        <option value="-1" >全部</option>
                        <if condition="$input['status'] == 1">
                            <option value="1" selected>已使用</option>
                            <option value="2" >未使用</option>
                            <elseif condition="$input['status'] == 2"/>
                            <option value="1" >已使用</option>
                            <option value="2" selected>未使用</option>
                            <else/>
                            <option value="1" >已使用</option>
                            <option value="2" >未使用</option>
                        </if>
                    </select>
                </div>
                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">上传文件</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">上传时间</th>
                <th style="text-align:center;">操作者</th>
                <th style="text-align:center;">文件名称</th>
                <th style="text-align:center;">深圳状态</th>
                <th style="text-align:center;">北京状态</th>
            </tr>
            </thead>
            <tbody>
            <volist name="list_data" id="vo">
                <tr>

                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.created_at|date='Y-m-d',###}</td>
                    <td align="center">{$vo.admin}</td>
                    <td align="center">{$vo.file_name}</td>
                    <td align="center">
                        <switch name="vo.status">
                            <case value="1"><nobr>已使用</nobr></case>
                            <case value="2"><nobr>未使用</nobr></case>
                        </switch>
                    </td>
                    <td align="center">
                        <switch name="vo.bj">
                            <case value="1"><nobr>已使用</nobr></case>
                            <case value="2"><nobr>未使用</nobr></case>
                        </switch>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >请上传Excel文件</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <input type="hidden" name="type" value="file_in">
                        <label for="add_receipt_serial">选择Excel文件</label>
                        <input type="file" name="file" id="excel_file" />
                    </div>
                    <div class="form-group" style="color:red;line-height:24px;">
                        * Excel文件的大小需要小于500KB，如果大于500KB，需要分多次上传<br/>
                        * Excel文件只支持xlsx或csv两种文件格式<br/>
                        * Excel文件第一行不可填写数据，系统将从第二行开始批量导入<br/>
                        * 点击下载 <a href="/statics/template/upload.csv">Excel模板</a><br/>
                        * 批量导入所需时间可能较长，请耐心等待<br/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_submit">增加</button>
            </div>
        </div>
    </div>
</div>



<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        //批量导入收款单（模态框）
        $("#file_in").click(function () {
            $("#file_in_modal").modal('show');
        });
        //批量导入收款单
        $("#file_in_submit").click(function () {
            var excel_file = $('#excel_file').val();
            if (excel_file == '') {
                alert('请选择需要上传的Excel');
                return false;
            }
            //验证文件的大小
            let file = $("#excel_file")[0].files[0];
            let size = file.size;
            if (size>=500*1024) {
                alert('文件过大，请将文件分割后导入');
                return false;
            }
            //验证文件后缀
            let ext = file.name.split('.')[1];
            if (ext!='xls' && ext!='xlsx' && ext!='csv') {
                alert('文件格式不正确，请核对后导入');
                return false;
            }
            $('#file_in_modal').modal('hide');
            $("#loading").show();
            var uploadFileForm = new FormData($("#file_in_form")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Upload/add",
                data:uploadFileForm,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });

        });
    });

</script>
</body>
</html>
