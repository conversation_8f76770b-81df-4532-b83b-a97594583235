<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('billNotes')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="start_time">生效时间：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>
                <div class="form-group">
                    <label class="control-label" for="search_customer_id">客户名称：</label>
                    <select class="form-control" name="search_customer_id" id="search_customer_id">
                        <option value="" >全部</option>
                        <?php foreach($customer_info as $value) {?>

                            <option <?php echo  $input['search_customer_id'] == $value['customer_id'].'#'.$value['name'] ? 'selected' : ''; ?> value="<?php echo $value['customer_id'].'#'.$value['name']?>"><?php echo $value['name']?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加数据</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">备注日期</th>
                <th style="text-align:center;">客户ID</th>
                <th style="text-align:center;">客户名称</th>
                <th style="text-align:center;">备注</th>
                <th style="text-align:center;">添加时间</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="list_data" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.cday}</td>
                    <td align="center">{$vo.customer_id}</td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{$vo.notes}</td>
                    <td align="center">{$vo.created_at|date='Y-m-d H:i:s',###}</td>
                    <td align="center">
                        <a href="javascript:void(0)"  onclick="editGroup({$vo['id']})"  class="btn btn-primary">编辑</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>


<div class="modal fade" id="file_in_modal" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >账单备注添加</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" action="{:U('addBillNotes')}" id="file_in_form" method="post">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注日期:</label>
                        <div class="col-sm-4">
                            <input type="date" name="cday" id="cday" value="" size="30"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">客户名称:</label>
                        <div class="col-sm-4">
                            <select class="form-control" name="name" id="name" style="width: 300px;">
                                <option value="">请选择</option>
                                <?php foreach($customer_info as $value) {?>
                                <option value="<?php echo $value['customer_id'].'#'.$value['name']?>"><?php echo $value['name']?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">账单备注:</label>
                        <div class="col-sm-4">
                            <textarea id="notes" name="notes" rows="5" cols="39" maxlength="200"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_submit">增加</button>
            </div>
        </div>
    </div>
</div>

<!--编辑-->

<div class="modal fade" id="file_in_modal2" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >账单备注编辑</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" action="{:U('editBillNotes')}" id="file_in_form2" method="post">
                    <input type="hidden" name="id2" id="id2" value="" />
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注日期:</label>
                        <div class="col-sm-4">
                            <input type="date" name="cday2" id="cday2" value="" size="30"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">客户名称:</label>
                        <div class="col-sm-4">
                            <select class="form-control" name="name2" id="name2" style="width: 300px;">

                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">账单备注:</label>
                        <div class="col-sm-4">
                            <textarea id="notes2" name="notes2" rows="5" cols="39" maxlength="200"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_submit2">编辑</button>
            </div>
        </div>
    </div>
</div>



<script type="text/javascript">
    function editGroup(id){
        if(id == ''){
            return false;
        }
        $('#id2').val(id);
        $("#name2").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '客户名称',
            width: '200px',
        });
        var url = "/Home/Upload/getEditBillNotes";
        $.ajax({
            type: 'post',
            url: url,
            data: {id: id},
            success: function(data) {
                if(data.status == 'ok'){
                    $('#cday2').val(data.data.cday);
                    $('#notes2').val(data.data.notes);
                    //$("#name2 option[value='"+data.data.name+"']").prop("selected","selected");
                    $('#name2').html(data.data.name);
                }
            }
        });

        $("#file_in_modal2").modal('show');
    }

    $(document).ready(function () {
        $("#search_customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '客户名称',
            width: '200px'
        });

        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        //批量导入收款单（模态框）
        $("#file_in").click(function () {

            $("#name").select2({
                allowClear: true,
                theme: "bootstrap",
                placeholder: '客户名称',
                width: '200px',
            });

            $("#file_in_modal").modal('show');
        });
        //批量导入收款单
        $("#file_in_submit").click(function () {
            var cday = $('#cday').val();
            if (cday == '') {
                alert('备注时间不能为空');
                return false;
            }
            var name = $('#name').val();
            if (name == '') {
                alert('请选择客户');
                return false;
            }
            var notes = $('#notes').val();
            if (notes == '') {
                alert('备注不能为空');
                return false;
            }

            $('#file_in_modal').modal('hide');
            var uploadFileForm = new FormData($("#file_in_form")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Upload/addBillNotes",
                data:uploadFileForm,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });

        });

        //编辑
        $("#file_in_submit2").click(function () {
            var id = $('#id2').val();
            if (id == '') {
                alert('id不能为空');
                return false;
            }
            var cday = $('#cday2').val();
            if (cday == '') {
                alert('备注时间不能为空');
                return false;
            }
            var name = $('#name2').val();
            if (name == '') {
                alert('请选择客户');
                return false;
            }
            var notes = $('#notes2').val();
            if (notes == '') {
                alert('备注不能为空');
                return false;
            }

            $('#file_in_modal2').modal('hide');
            var uploadFileForm = new FormData($("#file_in_form2")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Upload/editBillNotes",
                data:uploadFileForm,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal2').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });

        });
    });

</script>
</body>
</html>
