<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<div class="container" id="list_bang">
    <list_bang_template :init_params='<?= $init_params ?>'></list_bang_template>
    <dialog_template></dialog_template>
</div>

<!-- 查看详情的入口组件 -->
<script type="text/x-template" id="custom_a">
    <a href="" @click.stop.prevent="show(rowData,index)" class="">{{ rowData.name}}</a>
</script>
<script type="text/x-template" id="show_bang">
    <div>
        <div class="row">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span style="font-weight: bold">邦企查统计详情</span>
                    <span class="pull-right"><a href="/Home/BangProductStat/index" class="btn btn-info btn-sm"><i class=" icon-share-alt"></i> 返回列表</a></span>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.prevent="requestList">
                            <div class="form-group">
                                <label for="begin">开始时间：</label>
                                <input type="date" id="begin" v-model="begin" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="end">结束时间：</label>
                                <input type="date" v-model="end" id="end" class="form-control">
                            </div>
                            <div class="form-group select2-style-adjust">
                                <ul class="list-inline">
                                    <li><label class="control-label">选择账号:</label></li>
                                    <li><v-select :options="list_product_options" v-model="product"></v-select></li>
                                </ul>
                            </div>
                            <div class="form-group pull-right">
                                <button type="submit" class="btn btn-sm btn-primary"><i class="icon-search"></i> 查询</button>
                                <button class="btn btn-info btn-sm" @click.prevent="downloadFile" :disabled="disabled"><i class="icon-download-alt"></i> 导出</button>
                            </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    :multiple-sort="tableConfig.multipleSort"
                    @sort-change="sortChange"
                    sort-always
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total" :page-size="pageSize" :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>
    </div>
    </div>
</script>

<script>
    Vue.component('list_bang_template', {
        template: '#show_bang',
        props : ['init_params'],
        data: function () {
            return {
                list_contract_status :[
                    {name: '签约状态', value : ''},
                    {name: '已签约已付款', value:1},
                    {name: '已签约未付款', value:2},
                    {name: '未签约', value:3},
                    {name: '特殊客户', value:5},
                    {name: '其他', value:4}
                ],
                disabled : false,
                contract_status : '',
                status : '',
                id : '',
                begin:'',
                end : '',
                product : {id : '', label:'全部账号'},

                isLoading: false,
                pageIndex:1,
                pageSize:20,
                total : 0,
                tableRange : [
                ],
                tableConfig: {
                    multipleSort: false,
                    tableData: [
                    ],
                    columns: [
                        {field : 'day_time', title : '日期', width :100, columnAlign: 'center',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'total_num', title : '总查询量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'valid_name_or_address_num', title : '总查得量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'valid_name_total', title : '名称查得量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'valid_address_total', title : '地址查得量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'tel_right', title : '正确号码量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'name_right', title : '正确名称量', width: 100, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'tel_wrong', title : '错误号码量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'name_wrong', title : '错误名称量', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'valid_num', title : '名称查询有效查询量', width: 200, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'valid_address_num', title : '地址查询有效查询量', width: 200, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_cover_chance', title : '名称覆盖率', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'address_cover_chance', title : '地址覆盖率', width: 100, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_address_cover_chance', title : '名称&地址覆盖率', width: 200, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'name_is_same_chance', title : '名称一致率', width: 100, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'name_blur_num_chance', title : '名称模糊一致率', width: 200, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'name_not_same_chance', title : '名称不一致率', width: 180, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_not_right_chance', title : '名称无法匹配率', width: 200, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'address_is_same_chance', title : '地址一致率', width: 180, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'address_blur_num_chance', title : '地址模糊一致率', width: 180, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'address_not_same_chance', title : '地址不一致率', width: 180, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'address_not_right_chance', title : '地址无法匹配率', width: 200, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_dianhua_num', title : '名称比对羽乐科技查得量', width: 260, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_dianhua_num_chance', title : '名称比对羽乐科技查得占比', width: 260, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'name_dianhua_no_phone_num', title : '名称比对企查查查得量', width: 260, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_dianhua_no_phone_num_chance', title : '名称比对企查查查得占比', width: 260, columnAlign: 'center',isResize:true, orderBy:'asc',titleCellClassName: 'title_column'},
                        {field: 'name_tianyancha_num', title : '名称比对天眼查查得量', width: 260, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_tianyancha_num_chance', title : '名称比对天眼查查得占比', width: 260, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_yscredit_num', title : '名称比对有数金服工商接口查得量', width: 260, columnAlign: 'center',isResize:true,orderBy:'asc', titleCellClassName: 'title_column'},
                        {field: 'name_yscredit_num_chance', title : '名称比对有数金服工商接口查得占比', width: 260, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'name_ysfuzzycredit_num', title : '名称比对有数金服模糊查询查得量', width: 260, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'name_ysfuzzycredit_num_chance', title : '名称比对有数金服模糊查询查得占比', width: 260, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'name_webengine_num', title : '名称比对搜索引擎查得量', width: 260, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                        {field: 'name_webengine_num_chance', title : '名称比对搜索引擎查得占比', width: 260, columnAlign: 'center',orderBy:'asc',isResize:true, titleCellClassName: 'title_column'},
                    ]
                }
            }
        },
        mounted : function () {
            // 初始化页面
            this.initParams();

            this.requestList();
        },
        computed : {
            list_product_options : function () {
                var list_product = Object.values(this.init_params.list_product).map(function(item){
                    item.label = item.name;
                    return item;
                });
                list_product.unshift({id : '', label:'全部账号'});
                return list_product;
            }
        },
        methods: {
            // 下载文件
            downloadFile : function(){
                if (this.checkParams() === false) {
                    return false;
                }

                this.disabled = true;
                // 参数
                var url_export = '/Home/BangProductStat/downloadFile';
                var params = {
                    type: 'detail',
                    begin: this.begin,
                    end: this.end,
                    product_id : (this.product === null || this.product.id === undefined ) ? '' : this.product.id
                };

                var vm = this;
                vm.isLoading = true;
                $.fileDownload(url_export, {
                    data : params,
                    httpMethod : 'POST'
                }).done(function(){
                    vm.isLoading = false;
                    vm.disabled = false;
                }).fail(function (response) {
                    console.log(response);
                    vm.isLoading = false;
                    vm.disabled = false;
                    modalExport('导出文件失败，请刷新后再试');
                });
                return false;
            },
            // 排序
            sortChange : function(params){
                // 执行排序的url
                var url =  '/Home/BangProductStat/sortBy';

                // 排序的字段 && 方式
                var params_url = {
                    filter_field : '',
                    filter_order : '',
                    list_data : this.tableRange
                };

                Object.keys(params).forEach(function(key){
                    if (params[key] === 'asc' || params[key] === 'desc') {
                        params_url.filter_field = key;
                        params_url.filter_order = params[key];
                    }
                });

                // 请求排序
                var vm = this;
                this.$http.post(url, params_url, {responseType:'json'}).then(function (response) {
                    if (response.body.status === 0) {
                        vm.iniTableData(response.body.list_stat);
                    } else {
                        console.log('排序出错了，宝贝: ' + response.body.msg);
                    }
                });
            },
            initParams : function(){
                var request_params = this.init_params.request_params;

                // 初始化 时间 && 协议 && 状态
                this.end = request_params.end;
                this.begin = request_params.begin;

                // 产品
                if (request_params.id !== undefined) {
                    var product_id = request_params.id;
                    this.product = this.init_params.list_product[product_id];
                }
            },
            viewProfile: function (id) {
                console.log('view profile with id:', id)
            },

            // 自定义列触发事件
            customCompFunc : function(params) {
                console.log(params);
                if (params.type === 'show') {
                }
            },
            checkTime: function() {
                // get begin time and end time
                var time_begin = this.begin;
                var time_end = this.end;
                var today_str = (new Date()).toDateString();

                // change time format for firefox
                time_end = time_end.replace(/\-/g, '\/');
                time_begin = time_begin.replace(/\-/g, '\/');

                // check begin time
                if (!time_begin && time_end) {
                    modalExport('请选择开始时间');
                    return false;
                }

                // check end time
                if (time_begin && !time_end) {
                    modalExport('请选择结束时间');
                    return false;
                }

                if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
                    modalExport('请选择有效的结束时间');
                    return false;
                }

                // check time
                // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
                var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));

                if (time_diff < 0) {
                    modalExport('开始时间必须小于结束时间');
                    return false;
                }

                // calculate the days between begin and end
                var day_diff = Math.floor(time_diff / 8.64e7);

                //  the time should less than 31
                if (day_diff <= 364) {
                    return true;
                } else {
                    modalExport('单次查询时间范围不能超过365天');
                    return false;
                }
            },

            // 请求前检查参数
            checkParams : function(){
                // 检查时间
                if (this.checkTime() === false) {
                    return false;
                }
                // 检查产品
                return this.checkProduct();
            },

            // 检查产品
            checkProduct: function(){
                // 检查product_id
                if (this.product === null || !this.product.id) {
                    modalExport('请选择查询的账号');
                    return false;
                }
                return true;
            },

            // 请求数据
            requestList: function () {
                if (this.checkParams() === false) {
                    return false;
                }

                this.isLoading = true;
                var url = '/Home/BangProductStat/detail';
                var params = {
                    begin: this.begin,
                    end: this.end,
                    product_id : (this.product === null || this.product.id === undefined ) ? '' : this.product.id
                };
                console.log(params);
                var vm = this;
                this.$http.post(url, params, {responseType: 'json'}).then(function (response) {
                    console.log(response);
                    if (response.body.status === 0) {
                        vm.isLoading = false;
                        if (response.body.list_stat.verify_api === false) {
                            modalExport('API出错, 请联系开发人员');
                        }

                        var list_range = response.body.list_stat.list_day_data;

                        // 重置页面数据
                        vm.iniTableData(list_range);
                    }
                });
            },
            // 重置页面数据
            iniTableData : function(list_range){
                this.tableRange = list_range;
                this.total = list_range.length;
                this.getTableData();
            },

            // 重置当前页展示的数据
            getTableData : function(){
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex-1)*this.pageSize,(this.pageIndex)*this.pageSize)
            },
            // 换页重置数据
            pageChange: function(pageIndex){

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange : function(pageSize){

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            }
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    // 自定义列组件，进入详情页 但是没必要这么麻烦,formatter选项就足够支撑了
    Vue.component('table-operation', {
        template: '#custom_a',
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            show: function () {
                var params = {type: 'show', index: this.index, rowData: this.rowData};
                console.log(this.rowData);
                this.$emit('on-custom-comp', params);
            }
        }
    });

    new Vue({
        el: "#list_bang"
    });


</script>

</body>
</html>
