<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
        .container input {
            color:#31708f;
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<div class="container" id="list_upgrade">
    <msg_upgrade_template :upgrade_special='<?= $upgrade_special ?>'></msg_upgrade_template>
    <dialog_template></dialog_template>
</div>
<script type="text/x-template" id="msg_gray_upgrade">
    <div>
        <div class="row text-info">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span>发布版本</span> <a class="btn btn-sm btn-primary  pull-right"
                                         href="/Home/GrayUpgrade/index">列表</a>
                </div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label for="upgrade_name" class="col-md-2 control-label">版本名称：</label>
                            <div class="col-md-4">
                                <input type="text" id="upgrade_name" v-model="upgrade_item.upgrade_name"
                                       class="form-control"  readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="upgrade_key" class="col-md-2 control-label">策略名称（Redis key）：</label>
                            <div class="col-md-4">
                                <input type="text" id="upgrade_key" v-model="upgrade_item.upgrade_key"
                                       class="form-control" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="upgrade_body" class="col-md-2 control-label">策略值：</label>
                            <div class="col-md-8">
                                <textarea v-model="upgrade_item.upgrade_body" id="upgrade_body" cols="70" rows="5"
                                          disabled>
                                </textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="upgrade_body" class="control-label col-md-2">备注：</label>
                            <div class="col-md-8">
                                <textarea v-model="upgrade_item.remark" id="remark" cols="70" rows="5" disabled>
                                </textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-md-offset-2">
                                <a href="/Home/GrayUpgrade/index" class="btn btn-sm btn-primary">返回列表</a>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</script>

<script>
    Vue.component('msg_upgrade_template', {
        template: '#msg_gray_upgrade',
        props: ['upgrade_special'],
        computed: {
            upgrade_item: function () {
                if ((typeof this.upgrade_special) === 'string') {
                    return JSON.parse(this.upgrade_special);
                }
                return this.upgrade_special;
            }
        },
        created: function () {
            console.log(this.upgrade_item);
        }
    });

    new Vue({
        el: "#list_upgrade"
    });


</script>

</body>
</html>
