<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<div class="container" id="list_upgrade">
    <add_upgrade_template></add_upgrade_template>
    <dialog_template></dialog_template>
</div>
<script type="text/x-template" id="add_gray_upgrade">
    <div>
        <div class="row">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span>发布版本</span> <a class="btn btn-sm btn-primary  pull-right" href="/Home/GrayUpgrade/index">列表</a>
                </div>
                <div class="panel-body">
                    <form class="form-horizontal" @submit.prevent="requestAdd">
                        <div class="form-group">
                            <label for="upgrade_name" class="col-md-2 control-label">版本名称：</label>
                            <div class="col-md-4">
                                <input type="text" id="upgrade_name" v-model="form_body.upgrade_name" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="upgrade_key" class="col-md-2 control-label">策略名称（Redis key）：</label>
                            <div class="col-md-4">
                                <input type="text" id="upgrade_key" v-model="form_body.upgrade_key" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="upgrade_body" class="col-md-2 control-label">策略值：</label>
                            <div class="col-md-8">
                                <textarea v-model="form_body.upgrade_body" id="upgrade_body" cols="70" rows="5">
                                </textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="upgrade_body" class="control-label col-md-2">备注：</label>
                            <div class="col-md-8">
                                <textarea v-model="form_body.remark" id="remark" cols="70" rows="5">
                                </textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-md-offset-2">
                                <input class="btn btn-sm btn-primary" type="submit" value="添加">
                                <a href="/Home/GrayUpgrade/index" class="btn btn-sm btn-info">返回列表</a>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</script>

<script>
    Vue.component('add_upgrade_template', {
        template: '#add_gray_upgrade',
        data: function () {
            return {
                form_body : {
                    upgrade_name: '',
                    upgrade_key: '',
                    upgrade_body : '',
                    remark : ''
                }
            }
        },
        methods: {
            requestAdd: function () {
                // 检查参数
                var result_check = this.checkParam();
                if (result_check === false) {
                    return false;
                }

                // 发送请求
                var url = '/Home/GrayUpgrade/add';
                var vm = this;
                this.$http.post(url, vm.form_body, {responseType: 'json'}).then(function (response) {
                    if (response.body.success === false) {
                        modalExport(response.body.msg);
                        return false;
                    } else {
                        modalExport(response.body.msg);
                        window.location.href = '/Home/GrayUpgrade/index';

                    }
                }, function (reason) {
                    console.log(reason)
                });
            },
            checkParam : function () {
                if (this.form_body.upgrade_name.trim() === '') {
                    modalExport('请输入版本名称');
                    return false;
                }
                if (this.form_body.upgrade_name.trim().length > 100) {
                    modalExport('版本名称的长度必须小于100');
                    return false;
                }
                if (this.form_body.upgrade_body.trim() === '') {
                    console.log(this.form_body.upgrade_body);
                    modalExport('请输入策略值');
                    return false;
                }
                if (this.form_body.upgrade_body.trim().length > 2000) {
                    modalExport('策略值的长度必须小于2000');
                    return false;
                }
                if (this.form_body.upgrade_key.trim() === '') {
                    modalExport('请输入策略名称');
                    return false;
                }
                if (this.form_body.upgrade_key.trim().length >200) {
                    modalExport('策略名称长度必须小于200');
                    return false;
                }
                return true;
            }
        }
    });

    new Vue({
        el: "#list_upgrade"
    });


</script>

</body>
</html>
