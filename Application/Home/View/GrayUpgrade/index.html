<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }

        .form-group label {
            margin-left: 10px;
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<div class="container" id="list_upgrade">
    <list_upgrade_template></list_upgrade_template>
</div>
<script type="text/x-template" id="show_gray_upgrade">
    <div>
        <div class="row">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span style="font-weight: bold">已发布版本列表</span> <a class="btn btn-sm btn-primary  pull-right"
                                                                      href="/Home/GrayUpgrade/add"><i
                        class="icon-plus"></i>发布新版本</a>
                </div>
                <div class="panel-body">
                    <form class="form-inline" @submit.prevent="requestList">
                        <div class="form-group">
                            <label for="upgrade_name">版本名称：</label>
                            <input type="text" id="upgrade_name" v-model="upgrade_name" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="upgrade_key">版本KEY：</label>
                            <input type="text" id="upgrade_key" v-model="upgrade_key" class="form-control">
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-sm btn-primary"><i class="icon-search"></i> 查询</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    @on-custom-comp="customCompFunc"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total" :page-size="pageSize" :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>
    </div>
    </div>
</script>

<script>

    Vue.component('list_upgrade_template', {
        template: '#show_gray_upgrade',
        data: function () {
            return {
                upgrade_name: '',
                upgrade_key: '',
                isLoading: false,
                pageIndex:1,
                pageSize:20,
                total : 0,
                tableRange : [
                ],
                tableConfig: {
                    multipleSort: false,
                    tableData: [
                    ],
                    columns: [
                        {field : 'id', title : 'ID', width :100, columnAlign: 'center', isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'upgrade_name', title : '版本名称', width: 100, columnAlign: 'center', isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'upgrade_key', title : 'Redis key', width: 100, columnAlign: 'center', isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'remark', title: '备注',width: 90, columnAlign: 'center', isFrozen: false,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'handle_person', title: '操作人',width: 90, columnAlign: 'center', isFrozen: false,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'created_at', title: '操作时间',width: 90, columnAlign: 'center', isFrozen: false,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'custome-adv', title: '操作',width: 200, titleAlign: 'center',columnAlign:'center',componentName:'table-operation',isResize:true, titleCellClassName: 'title_column'}
                    ]
                }
            }
        },
        created : function () {
          this.requestList();
        },
        methods: {
            viewProfile: function (id) {
                console.log('view profile with id:', id)
            },

            // 自定义列触发事件
            customCompFunc : function(params) {
                if (params.type === 'show') {
                    var url = '/Home/GrayUpgrade/showMsg?id=' + params.rowData.id;
                    window.open(url);
                }
            },
            // 请求数据
            requestList: function () {
                this.isLoading = true;
                var url = '/Home/GrayUpgrade/index';
                var params = {upgrade_name: this.upgrade_name, upgrade_key: this.upgrade_key};
                var vm = this;
                this.$http.post(url, params, {responseType: 'json'}).then(function (response) {
                    if (response.body.success) {
                        vm.isLoading = false;
                        vm.tableRange = response.body.data;
                        vm.total = response.body.data.length;
                        vm.getTableData();
                    }
                });
            },
            // 重置当前页展示的数据
            getTableData : function(){
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex-1)*this.pageSize,(this.pageIndex)*this.pageSize)
            },
            // 换页重置数据
            pageChange: function(pageIndex){

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange : function(pageSize){

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            }
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    // 自定义列组件
    Vue.component('table-operation', {
        template: '<span>\n' +
        '        <a href="" @click.stop.prevent="show(rowData,index)" class="btn btn-xs btn-info"><i class=" icon-eye-open"></i>查看</a>&nbsp;\n' +
        '        </span>',
        props: {
            rowData: {
                type: Object
            },
            field: {
                type: String
            },
            index: {
                type: Number
            }
        },
        methods: {
            show: function () {
                var params = {type: 'show', index: this.index, rowData: this.rowData};
                this.$emit('on-custom-comp', params);
            }
        }
    })

    new Vue({
        el: "#list_upgrade"
    });


</script>

</body>
</html>
