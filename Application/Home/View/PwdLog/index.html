<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__highlight.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .form-inline .row div {
            margin:0 5px 0 0
        }
        .line_second {
            margin-top: 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">
                <div class="line_first">
                    <div class="form-group">
                        <label  class="" for="time_begin">开始时间</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= !isset($request_params['begin']) ? date('Y-m-d') : $request_params['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label  class="" for="time_end">结束时间</label>
                        <input type="date" name="end" id="time_end" class="form-control"  value="<?= !isset($request_params['end']) ? date('Y-m-d') : $request_params['end']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label for="telecom">运营商</label>
                        <select name="telecom" id="telecom" class="form-control">
                            <option value="">全部</option>
                            <option value="移动" <?= ($request_params['telecom'] == '移动') ? 'selected' : ''; ?>>移动</option>
                            <option value="联通" <?= ($request_params['telecom'] == '联通') ? 'selected' : ''; ?>>联通</option>
                            <option value="电信" <?= ($request_params['telecom'] == '电信') ? 'selected' : ''; ?>>电信</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="province">爬虫渠道</label>
                        <select name="province" id="province" class="form-control">
                            <option value="">全部</option>
                            <?php foreach ($crawler_areas as $area) { ?>
                            <option value="<?= $area; ?>" <?= ($request_params['province'] == $area) ? 'selected' : '';?>><?= $area;?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="line_second">
                    <div class="form-group">
                        <label for="status_list">错误等级</label>
                        <select name="status_list" id="status_list" class="form-control">
                            <option value="">全部</option>
                            <option value="success" <?= ($request_params['status_list'] == 'success') ? 'selected' : '' ;?>>无错误</option>
                            <option value="crawler_error" <?= ($request_params['status_list'] == 'crawler_error') ? 'selected' : '' ;?>>爬虫错误</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="sr-only" for="tid">Tid</label>
                        <input type="text" name="tid" id="tid" class="form-control" placeholder="Tid" value="<?= isset($request_params['tid']) ? $request_params['tid'] : ''; ?>">
                    </div>
                    <div class="form-group">
                        <label class="sr-only" for="status">Status</label>
                        <input type="text" name="status" id="status" placeholder="Status" class="form-control" value="<?= isset($request_params['status']) ? $request_params['status'] : ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="tel" class="sr-only">Tel</label>
                        <input type="text" name="tel" id="tel" placeholder="Tel" class="form-control" value="<?= isset($request_params['tel']) ? $request_params['tel'] : ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="cid" class="sr-only">Cid</label>
                        <input type="text" name="cid" id="cid" placeholder="cid" value="<?= isset($request_params['cid']) ? $request_params['cid'] : ''; ?>" class="form-control">
                    </div>
                    <input type="submit" onclick="return checkParams()" class="btn btn-primary  btn-sm pull-right" value="查询">
                </div>
            </form>
    </div>
</div>

</div>
<div class="container">
    <div class="bs-example" data-example-id="hoverable-table table-responsive">
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>Tid</th>
                <th>Cid</th>
                <th>Status</th>
                <th>手机号</th>
                <th>运营商</th>
                <th>Province</th>
                <th>City</th>
                <th>Message</th>
                <th>Time</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list as $log) {?>
            <tr>
                <td scope="row">
                    <a href="/Home/PwdLog/msg?tid=<?= $log['tid']; ?>" target="_blank"><?= $log['tid']; ?></a>
                </td>
                <td><?= $log['cid']; ?></td>
                <td><?= $log['status']; ?></td>
                <td><?= $log['tel']; ?> </td>
                <td><?= $log['telecom']; ?> </td>
                <td><?= $log['province']; ?> </td>
                <td><?= $log['city']; ?> </td>
                <td><?= $log['message']; ?> </td>\
                <td>
                    Start: <?= date('Y-m-d H:i:s', $log['start_time']) ?><br>
                    &nbsp;End: <?= date('Y-m-d H:i:s', $log['end_time']) ?>
                </td>
            </tr>
            <?php }?>
            </tbody>
        </table>
        <div>
            <ul class="pagination">
                <?= $page; ?>
            </ul>
        </div>
    </div>

</div>
</body>
<script type="application/javascript">
    $(function(){
        $("#province").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '爬虫渠道'
        });
    });

    function checkParams() {

        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();

        // change time format for firefox
        time_end = time_end.replace(/\-/g, '\/');
        time_begin = time_begin.replace(/\-/g, '\/');

        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }

        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }

        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }

        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }

        if (!time_end) {
            time_end = today_str;
        }

        // check time
        var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }

        // calculate the days between begin and end
        var day_diff =  Math.floor(time_diff/8.64e7);

        //  the time should less than 31
        if (day_diff <= 30) {
            return true;
        } else {
            alert('单次查询时间范围不能超过31天');
            return false;
        }
    }
</script>
</html>
