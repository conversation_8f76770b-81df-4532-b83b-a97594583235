<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="__JS__jquery.tablesorter.min.js"></script>
    <link rel="stylesheet" href="__CSS__tablesorter-theme/style.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered tablesorter focus-highlight" id="table_show">
            <thead>
            <tr>
                <th>爬虫名称</th>
                <th>总计</th>
                <th>授权差值</th>
                <th>授权成功量</th>
                <th>爬取成功量</th>
                <th>爬取差值</th>
                <th>报告生成量</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach($show_list as $crawler => $show) {?>
            <tr>
                <td><?= $crawler; ?></td>
                <td><?= $show['total']; ?></td>
                <td><?= $show['diff_login_status']; ?></td>
                <td><?= $show['login_status']; ?></td>
                <td><?= $show['status']; ?></td>
                <td><?= $show['diff_status']; ?></td>
                <td><?= $show['status_report']; ?></td>
            </tr>
            <?php }?>
            </tbody>
        </table>
    </div>
</div>
<script type="text/javascript">
    $(function () {

        var setting = {
            headers : {0:{ sorter: false}},
            1 : {sorter:"integer"},
            2 : {sorter:"integer"},
            3 : {sorter:"integer"},
            4 : {sorter:"integer"},
            sortList : [[5, 1], [2, 1]]
        };
        $("#table_show").tablesorter(setting);
    })
</script>
</body>
</html>
