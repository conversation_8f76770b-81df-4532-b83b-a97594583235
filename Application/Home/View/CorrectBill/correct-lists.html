<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<!-- 矫正账单调用量的列表组件 -->
<include file="Common@components/correct_lists"/>
<!-- 矫正账单调用量的create组件 -->
<include file="Common@components/correct_create"/>
<!--  校正账单调用量的的查看模式 -->
<include file="Common@components/correct_check"/>
<!--  校正账单调用量的的编辑模式 -->
<include file="Common@components/correct_edit"/>
<!--  增加备注的组件 -->
<include file="Common@components/correct_comment"/>


<div class="container" id="app">
    <correct_list_template  month_t_begin="<?= $month_begin ?>" month_t_end="<?= $month_begin ?>"
                            api_download="<?= $api_download ?>" back_api_correct_add_comment="<?= $back_api_correct_add_comment ?>"
                            back_api_correct_operators="<?= $back_api_correct_operators ?>"
                            back_api_correct_comments="<?=  $back_api_correct_comments ?>"
                            backend_api_sorts="<?= $backend_api_sorts ?>"
                            back_api_correct_numbers_create="<?= $back_api_correct_numbers_create ?>"  back_api_correct_numbers="<?= $back_api_correct_numbers ?>"
                            list_t_customers='<?= $list_customers ?>' list_t_products='<?=  $list_products ?>' login_user="<?= $login_user ?>"
    ></correct_list_template>
</div>
</body>
<script>
    new Vue({
        el: '#app'
    });

</script>
</html>
