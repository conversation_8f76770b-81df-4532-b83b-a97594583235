<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">

                <div class="form-group">
                    <label class="control-label" for="search_address">邮箱名称：</label>
                    <input type="text" name="search_address" id="search_address" class="form-control" maxlength="50" size="30" value="{$input.search_address}" placeholder="邮箱地址" autocomplete="off" />
                </div>
                <div class="form-group">
                    <label class="control-label" for="search_address">使用场景：</label>
                    <input type="text" name="search_scene" id="search_scene" class="form-control" maxlength="50" size="30" value="{$input.search_scene}" placeholder="使用场景" autocomplete="off" />
                </div>

                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <button type="button" id="add" class="btn btn-success btn-sm">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">
    <div class="table_title">

    </div>
    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">ID</th>
                <th style="text-align:center;">名称</th>
                <th style="text-align:center;">邮箱</th>
                <th style="text-align:center;">类型</th>
                <th style="text-align:center;">是否发送</th>
                <th style="text-align:center;">使用场景</th>
                <th style="text-align:center;">备注</th>
            </tr>
            </thead>
            <tbody>
            <volist name="list_data" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{$vo.address}</td>
                    <td align="center">
                        <switch name="vo.type">
                            <case value="0"><nobr>收件人</nobr></case>
                            <case value="1"><nobr>抄送人</nobr></case>
                        </switch>
                    </td>
                    <td align="center">
                        <switch name="vo.status">
                            <case value="0"><nobr>否</nobr></case>
                            <case value="1"><nobr>是</nobr></case>
                        </switch>
                    </td>
                    <td align="center"><nobr>{$vo.scene}</nobr></td>
                    <td align="center"><nobr>{$vo.remark}</nobr></td>
                    <td align="center">

                      <a onclick="add_remit('{$vo.id}')" style="cursor:pointer;"><nobr>编辑</nobr></a>

                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<div class="modal fade" id="add_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="exampleModalLabel">添加</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}"  method="post" class="form-horizontal" id="add_form">
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">名称：</label>
                            <input type="text" id="name" name="name" value="" size="25"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">邮箱：</label>
                            <input type="text" id="address" name="address" value="" size="25"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">场景：</label>
                            <input type="text" id="scene" name="scene" value="" size="30"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">类型：</label>
                            <input type="radio" id="type1" name="type" value="0" checked/>
                            收件人
                            &nbsp;&nbsp;
                            <input type="radio" id="type2" name="type" value="1"/>
                            抄送人
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">是否发送：</label>
                            <input type="radio" id="status1" name="status" value="1" checked/>
                            是
                            &nbsp;&nbsp;
                            <input type="radio" id="status2" name="status" value="1"/>
                            否
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">备注：</label>
                            <input type="text" id="remark" name="remark" value="" size="30"/>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="add_submit">增加</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="edit_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="exampleModalLabel2">编辑</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}"  method="post" class="form-horizontal" id="edit_form">
                    <input type="hidden" id="id2" name="id2" value=""/>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">名称：</label>
                            <input type="text" id="name2" name="name2" value="" size="25"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">邮箱：</label>
                            <input type="text" id="address2" name="address2" value="" size="25"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">场景：</label>
                            <input type="text" id="scene2" name="scene2" value="" size="30"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">类型：</label>
                            <input type="radio" id="type12" name="type2" value="0" checked/>
                            收件人
                            &nbsp;&nbsp;
                            <input type="radio" id="type22" name="type2" value="1"/>
                            抄送人
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">是否发送：</label>
                            <input type="radio" id="status12" name="status2" value="1" checked/>
                            是
                            &nbsp;&nbsp;
                            <input type="radio" id="status22" name="status2" value="0"/>
                            否
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">备注：</label>
                            <input type="text" id="remark2" name="remark2" value="" size="30"/>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="edit_submit">编辑</button>
            </div>
        </div>
    </div>
</div>



<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    //编辑
    function add_remit(id){
        if(id == ''){
            return false;
        }
        $.post("/Home/EmailConfig/getEdit", { id: id }, function(data){
            $('#id2').val(id);
            $('#name2').val(data.name);
            $('#address2').val(data.address);
            $('#scene2').val(data.scene);
            if (data.type == 1){
                $('#type22').prop('checked', 'checked');
            }else{
                $('#type12').prop('checked', 'checked');
            }
            if (data.status == 1){
                $('#status12').prop('checked', 'checked');
            }else{
                $('#status22').prop('checked', 'checked');
            }
            $('#remark2').val(data.remark);
            $('#edit_modal').modal('show');
        });

    }
    //客户的数据
    $(document).ready(function () {

        //$("#loading_modal").modal();
        $("#add").click(function () {
            $("#add_modal").modal({});
        });
        //条件查询
        $("#list_submit").click(function () {
            //验证金额是否符合标准
            $("#list_form").submit();
        });

        $("#add_submit").on('click', function(){
            var name = $('#name').val();
            var address = $('#address').val();
            var scene = $('#scene').val();
            if (name == ''){
                alert('名称不能为空');
                return false;
            }
            if(address == ''){
                alert('邮箱不能为空');
                return false;
            }
            if(scene == ''){
                alert('使用场景不能为空');
                return false;
            }
            var add_form = new FormData($("#add_form")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/EmailConfig/add",
                data:add_form,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#add_modal').modal('hide');
                       // $("#loading").hide();
                        window.location.reload();
                    }else{
                       // $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });
        });

        $("#edit_submit").on('click', function(){
            var name = $('#name2').val();
            var address = $('#address2').val();
            var scene = $('#scene2').val();
            if (name == ''){
                alert('名称不能为空');
                return false;
            }
            if(address == ''){
                alert('邮箱不能为空');
                return false;
            }
            if(scene == ''){
                alert('使用场景不能为空');
                return false;
            }
            var edit_form = new FormData($("#edit_form")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/EmailConfig/edit",
                data:edit_form,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#edit_modal').modal('hide');
                        // $("#loading").hide();
                        window.location.reload();
                    }else{
                        // $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });
        });

    });
</script>
</body>
</html>
