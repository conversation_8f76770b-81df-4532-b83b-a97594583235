<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>

    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">

    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
                <div class="form-group">
                    <label class="control-label">选择产品：</label>
<!--                    <input type="checkbox" name="product_search" value="280" />&nbsp;近30天催收号码数&nbsp;-->
<!--                    <input type="checkbox" name="product_search" value="281" />&nbsp;近30天被叫催收次数&nbsp;-->
<!--                    <input type="checkbox" name="product_search" value="297" /> 邦信分_评分A12-->
<!--                    <input type="checkbox" name="product_search" value="711" /> 邦信分_评分Y01-->
                    <input type="radio" name="product_id" value="280"/> 近30天催收号码数(280) &nbsp;
                    <input type="radio" name="product_id" value="281"/> 近30天被叫催收次数(281) &nbsp;
                    <input type="radio" name="product_id" value="297"/> 邦信分_评分A12(297) &nbsp;
                    <input type="radio" name="product_id" value="711"/> 邦信分_评分Y01(711) &nbsp;
                </div>

                <div class="form-group">
                    <label class="control-label" >选择渠道：</label>
                    <span id="channel"></span>
                </div>

                <div class="form-group">
                    <label class="control-label">测试手机号码：</label>
                    <input  type="text" name="phonenumber" id="phonenumber" value="" /> &nbsp;&nbsp;<span style="font-size: 2px">(手动输入手机号后，以该手机号为准，渠道选择将不起作用)</span>
                </div>

                <div class="form-group">
                    <label class="control-label">账  &nbsp;  期：</label> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="text" style="width: 14%;display: inline;height: 25px" name="payment_days" placeholder="请选择开始时间" autocomplete="off" class="layui-input" id="payment_days"  >
                </div>

                <div class="form-group">
                    <label class="control-label">测 试 条 数：</label>&nbsp;&nbsp;&nbsp;&nbsp;
                    <input type="text" name="num_search" id="num_search" value="" />
                </div>

                <div class="form-group">
                    <button type="button" id="run_case" class="btn btn-primary btn-sm">开始测试</button>
                </div>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">请求次数</th>
                <th style="text-align:center;">手机号</th>
                <th style="text-align:center;">状态说明</th>
                <th style="text-align:center;">请求结果</th>
                <th style="text-align:center;">渠道测试结果</th>
            </tr>
            </thead>
            <tbody id="tbody_append">

            </tbody>
        </table>
    </div>
</div>


<div id="openProductBox" style="display: none; padding: 10px;">
    <table id="openProductTable" lay-filter="openProductTable">
        <!-- <h3 style="text-align: center">渠道测试结果</h3> -->
    </table>
</div>

</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="text/javascript">

    layui.laydate.render({
        elem  : '#payment_days',
        //type  : 'datetime',
    });


    $(document).ready(function () {
        //获取渠道
        $.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/monitor/lastRecord/getChannelUseing", function(data){
            var count = data.data.length;
            var html = joinData(data.data, count);
            $("#channel").html(html);
        });

        $('#run_case').on('click', function(){

            var product_id = $('input[name="product_id"]:checked').val();
            var channel_id = $('input[name="channel_id"]:checked').val();
            var phone_number = $("#phonenumber").val();
            var payment_days = $("#payment_days").val();

            if (payment_days != undefined && payment_days != ''){
                payment_days = payment_days.replace(/-/g,'');
            }
            var phonestatus = isPoneAvailable(phone_number);
            if (phonestatus == false &&phone_number != '' && phone_number != undefined){
                alert('请输入11位手机号码');
                return false;
            }

           if (product_id == undefined){
               alert('请选择相应的产品');
               return false;
           }
           if(phone_number ==undefined || phone_number == ''){
               if (channel_id == undefined){
                   alert('请选择相应的渠道');
                   return false;
               }
           }
           if(phone_number ==undefined){
               phone_number = '';
           }
            var num_search = $('#num_search').val();
            if(num_search == ''){
                alert('请输入对应的条数');
                return false;
            }
            if(isNaN(num_search)){
                alert('请输入整型数字');
                return false;
            }


            var arr_num = new Array('1','2','3','4','5','6','7','8','9','10');
            if(arr_num.indexOf(num_search) === -1){
                alert('请输入1-10之间的数字');
                return false;
            }
            var j = 0
            sendTestChannel(product_id,channel_id,phone_number,payment_days,num_search,j);

        });
    });

    function sendTestChannel(product_id,channel_id,phone_number,payment_days,num_search,j){

        if(j < num_search){
            $.ajax({
                contentType: "application/x-www-form-urlencoded",
                type: 'post',
                url: "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/test/testChannel",
                async:true,
                dataType:'json',
                data: {
                    i: j,
                    product:product_id,
                    channel: channel_id,
                    phone_number :phone_number,
                    payment_days :payment_days,
                },
                success: function(data) {
                    var ii = parseInt(j)+1;
                    if (ii == 1){
                        $("#tbody_append").empty();
                    }
                    //console.log(data.data);
                    if(data.data.status == 'ok'){
                        var str = '<tr> <td align="center" style="word-wrap:break-word;word-break:break-all;">'+ii+'</td><td align="center" style="word-wrap:break-word;word-break:break-all;">'+data.data.info.phone+'</td> <td align="center" style="word-wrap:break-word;word-break:break-all;">'+data.data.info.msg+'</td> <td align="center" style="word-wrap:break-word;word-break:break-all;">'+data.data.info.json_res+'</td><td align="center" style="word-wrap:break-word;word-break:break-all;"><button type="button" class="layui-btn layui-btn-xs" onclick=channelTestRes("'+data.data.info.sid+'")>渠道测试结果</button></td></tr>';
                        $('#tbody_append').append(str);
                    }else{
                        var str = '<tr> <td align="center" style="word-wrap:break-word;word-break:break-all;color: red;">'+ii+'</td> <td align="center" style="word-wrap:break-word;word-break:break-all;color: red;">'+data.data.info.phone+'</td> <td align="center" style="word-wrap:break-word;word-break:break-all;color: red;">'+data.data.info.msg+'</td> <td align="center" style="word-wrap:break-word;word-break:break-all;color: red;">'+data.data.info.json_res+'</td><td align="center" style="word-wrap:break-word;word-break:break-all;"></td></tr>';
                        $('#tbody_append').append(str);
                    }
                    j++;
                    sendTestChannel(product_id,channel_id,phone_number,payment_days,num_search,j);
                }
            });
        }
    }

    //数组拼接下拉选择框
    function joinData(data,count){
        var  html = '';
        for (var i = 0; i <count; i++) {
           // html  += '<option value="' + data[i].channel_id + '">' + data[i].label + '</option>';
            html += '<input type="radio" name="channel_id" value=' +data[i].channel_id + '>'  + data[i].label + '&nbsp';
        }
        return html;
    }

    function channelTestRes(sid){
        $.ajax({
            type : 'GET',
             url : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/test/channelTestRes",
            data : {sid:sid},
            success: function (data) {
                //console.log(sid);
                //console.log(data);
                if (data.code == 1){
                    layer.open({
                        type: 1,
                        title: '渠道测试结果',
                        area: ['60%', '40%'],
                        content: data.message
                    });
                }else{
                    layer.open({
                        type: 1,
                        content: $('#openProductBox'),
                        area: ['60%', '40%'], //宽高
                        title: '渠道测试结果',
                        success: function () {
                            layui.use(['table'],function () {
                                var table = layui.table;
                                table.render({
                                    elem: '#openProductTable'
                                    , height: 150,
                                    data:data['data'],
                                    page: false, //关闭分页
                                     cols: [[ //表头
                                         {field: 'in_param', title: '入参'},
                                         {field: 'status', title: '状态'},
                                         {field: 'info', title: '状态提示信息'},
                                         {field: 'value', title: '返回值'}
                                    ]]
                                });
                            });
                        }

                    })
                }
            }
        });
    }

    function isPoneAvailable(poneInput) {
        var myreg=/^[1][3,4,5,6,7,8,9][0-9]{9}$/;

        if (!myreg.test(poneInput)) {
            return false;
        } else {
            return true;
        }
    }

</script>
</body>
</html>
