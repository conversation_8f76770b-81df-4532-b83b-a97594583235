<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="name_search">批次名称：</label>
                    <input type="input" name="name_search" id="name_search" class="form-control" value="{$input.name_search}"/>
                </div>

                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加</button>
                </div>

            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">id</th>
                <th style="text-align:center;">批次名称</th>
                <th style="text-align:center;">开始时间</th>
                <th style="text-align:center;">截止时间</th>
                <th style="text-align:center;">预计调用量</th>
                <th style="text-align:center;">更新时间</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="data" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{$vo.start_time|date="Y-m-d H:i:s", ###}</td>
                    <td align="center">{$vo.end_time|date="Y-m-d H:i:s", ###}</td>
                    <td align="center">{$vo.number}</td>
                    <td align="center">{$vo.last_update_time|date="Y-m-d H:i:s", ###}</td>
                    <td align="center">
                        <a href="javascript:void(0);" class="btn btn-info btn-sm" onclick="edit('<?php echo $vo['id'] ?>')">编辑</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>

<div class="modal fade" id="file_in_modal2">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">编辑</h4>
            </div>
            <div class="modal-body" style="font-size: 12px;">
                <form class="form-horizontal" id="formCar2" method="post" >
                    <input type="hidden" id="id2" name="id2" value="" />
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">批次名称<span style="color:red">*</span>：</label>
                            <input type="input" id="name2" name="name2" maxlength="20" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">开始时间<span style="color:red">*</span>：</label>
                            <input type="text" class="datetimepicker" id="start_time2" name="start_time2" value="">
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">结束时间<span style="color:red">*</span>：</label>
                            <input type="text" class="datetimepicker" id="end_time2" name="end_time2" value="">
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">预计调用量<span style="color:red">*</span>：</label>
                            <input type="input" id="number2" name="number2" maxlength="11" value="" size="20">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button"  class="btn btn-primary btn-sm btn-submit" id="btn-dis2">提交</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div class="modal fade" id="file_in_modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">添加</h4>
            </div>
            <div class="modal-body" style="font-size: 12px;">
                <form class="form-horizontal" id="formCar" method="post" >

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">批次名称<span style="color:red">*</span>：</label>
                            <input type="input" id="name" name="name" maxlength="20" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">开始时间<span style="color:red">*</span>：</label>
                            <input type="text" class="datetimepicker" id="start_time" name="start_time" value="">
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">结束时间<span style="color:red">*</span>：</label>
                            <input type="text" class="datetimepicker" id="end_time" name="end_time" value="">
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">预计调用量<span style="color:red">*</span>：</label>
                            <input type="input" id="number" name="number" maxlength="11" value="" size="20">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button"  class="btn btn-primary btn-sm btn-submit" id="btn-dis">提交</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>

<script src="__JS__bootstrap-datetimepicker.min.js"></script>
<script src="__JS__bootstrap-datetimepicker.zh-CN.js"></script>
<link rel="stylesheet" href="__CSS__bootstrap-datetimepicker.min.css">

<script type="text/javascript">

    $('.datetimepicker').datetimepicker({
        language: 'zh-CN',//显示中文
        format: 'yyyy-mm-dd hh:ii:ss',//显示格式
        minView: "hour",//设置只显示到月份
        initialDate: new Date(),//初始化当前日期
        autoclose: true,//选中自动关闭
        todayBtn: true//显示今日按钮
    });

    function edit(id){
        if(id == ''){
            return false;
        }
        $('#id2').val(id);
        $.ajax({
            type: 'post',
            url: "{:U('/Home/AntConfig/getEdit')}",
            data: {
                id2: id
            },
            success: function(data) {
                if(data.status == 'ok'){
                    $('#id2').val(data.data.id);
                    $('#name2').val(data.data.name);
                    $('#start_time2').val(data.data.start_time);
                    $('#end_time2').val(data.data.end_time);
                    $('#number2').val(data.data.number);
                    $("#file_in_modal2").modal('show');
                }else{
                    alert(data.msg);
                }

            }
        });

    }

    $(document).ready(function () {

        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        $('#file_in').on('click', function(){
            $("#file_in_modal").modal('show');
        });
        $('#btn-dis').on('click', function(){
            var name = $('#name').val();
            if (name == ''){
                alert('批次名称不能为空');
                return false;
            }

            var start_time = $('#start_time').val();
            if (start_time == ''){
                alert('开始时间不能为空');
                return false;
            }
            var end_time = $('#end_time').val();
            if (end_time == ''){
                alert('结束时间不能为空');
                return false;
            }
            if(start_time >= end_time){
                alert('开始时间不能大于或者等于结束时间');
                return false;
            }

            var number = $('#number').val();
            if (number == ''){
                alert('预计调用量不能为空');
                return false;
            }

            if(isNaN(number)){
                alert('预计调用量必须为数字类型');
                return false;
            }

            var formCar = new FormData($("#formCar")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/AntConfig/add",
                data:formCar,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });

        $('#btn-dis2').on('click', function(){
            var id = $('#id2').val();
            if(id == ''){
                return false;
            }

            var name = $('#name2').val();
            if (name == ''){
                alert('批次名称不能为空');
                return false;
            }

            var start_time = $('#start_time2').val();
            if (start_time == ''){
                alert('开始时间不能为空');
                return false;
            }
            var end_time = $('#end_time2').val();
            if (end_time == ''){
                alert('结束时间不能为空');
                return false;
            }
            if(start_time >= end_time){
                alert('开始时间不能大于或者等于结束时间');
                return false;
            }

            var number = $('#number2').val();
            if (number == ''){
                alert('预计调用量不能为空');
                return false;
            }

            if(isNaN(number)){
                alert('预计调用量必须为数字类型');
                return false;
            }


            var formCar = new FormData($("#formCar2")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/AntConfig/edit",
                data:formCar,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal2').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });

    });

</script>
</body>
</html>
