<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css" />
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">

                <div class="form-group">
                    <label  class="control-label">开始时间</label>&nbsp;
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group">
                    <label  class="control-label">结束时间</label>&nbsp;
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                </div>
                &nbsp;
                <!-- <div class="form-group"  >
                    <label for="data_source">查询数据源</label>
                    <select class="form-control" id="data_source" name="data_source">
                        <option value="" >全部</option>
                        <option value="" >大有</option>
                        <option value="" >聚合</option>
                        <option value="" >缓存数据</option>
                    </select>
                </div>
                &nbsp;
                <div class="form-group"  >
                    <label for="flow">运营商</label>
                    <select class="form-control" id="flow" name="flow">
                        <option value="" >全部</option>
                        <option value="" >移动</option>
                        <option value="" >联通</option>
                        <option value="" >电信</option>
                    </select>
                </div>
                &nbsp; -->
                <div class="form-group">
                        <input type="submit" onclick="return checkTime();" class="btn btn-primary  btn-block" value="查询">
                </div>
                &nbsp;
                <!-- <div class="form-group">
                    <button type="button" id="file_export" class="btn btn-success  btn-block">导出 </button>
                </div>-->
            </form>

        </div>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th width="7%">时间</th>
                <?php
                    if ($product_list && is_array($product_list)) {
                        foreach ($product_list as $key => $value) {
                            echo '<th>'.$value['name'].'</th>';
                        }
                    }
                ?>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>总计</td>
                <?php
                    if ($product_list && is_array($product_list)) {
                        foreach ($product_list as $key => $value) {
                            echo '<td>'.(isset($total[$value['action']]) ? $total[$value['action']] : 0).'</td>';
                        }
                    }
                ?>
            </tr>

            <?php foreach ($list as $key => $value) {?>
            <tr>
                <td><?= $key; ?></td>
                <?php
                    if ($product_list && is_array($product_list)) {
                        foreach ($product_list as $kk => $vv) {
                            echo '<td>'.(isset($value[$vv['action']]) ? $value[$vv['action']] : 0).'</td>';
                        }
                    }
                ?>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">

    $(document).ready(function() {
        $("#data_source").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '全部'
        });

        $("#flow").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '全部'
        });
    });

    function checkTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();
        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }
        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }
        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }
        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }
        if (!time_end) {
            time_end = today_str;
        }
        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }
        // calculate the days between begin and end
        var day_diff =  Math.floor(time_diff/8.64e7);
        //  the time should less than 31
        if (day_diff > 365) {
            alert('单次查询时间范围不能超过365天');
            return false;
        }

        return true;
    }
</script>
</body>
</html>
