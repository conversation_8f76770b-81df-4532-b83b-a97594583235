<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">

    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" id="form_init">
                <input type="hidden" name="model" value="{$input['model']}">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control" value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d',strtotime('-1 day')) : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control" value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d',strtotime('-1 day')) : $input['end']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label">客户名称：</label>
                        <input type="text" id="name" name="name" value="<?= $input['name'] ?>">
                        </select>
                    </div>

                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" class="btn btn-primary btn-sm"
                                       value="查询">
                            </li>
                            <li>
                                <a class="btn btn-info btn-sm" href="{:U('/FeeProductStat/index',['begin'=>$input['begin'],'end'=>$input['end']])}" role="button">返回</a>
                            </li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th>客户ID</th>
                <th>客户名称</th>
                <th>公司名称</th>
                <th>计费用量</th>
                <th>费用（元）</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td></td>
                <td></td>
                <td>总计</td>
                <td><?= $total_data['fee_amount'] ? $total_data['fee_amount'] : 0 ?></td>
                <td><?= $total_data['fee_price'] ? sprintf('%.2f', $total_data['fee_price']) : '0.00' ?></td>
            </tr>
            <?php
            if ($list && is_array($list)) {
                foreach ($list as $stat) {
            ?>
                <tr>
                    <td>{$stat['account_id']}</td>
                    <td>
                        <a href="{:U('detail',['id'=>$stat['account_id'],'model'=> $input['model'],'begin'=>$input['begin'],'end'=>$input['end']])}"><?= $stat['account_name'] ? $stat['account_name'] : '无绑定'?></a>
                    </td>
                    <td>{$stat['company']}</td>
                    <td><?= $stat['fee_amount'] ? $stat['fee_amount'] : 0 ?></td>
                    <td><?= $stat['fee_price'] ? sprintf('%.2f', $stat['fee_price']) : '0.00' ?></td>
                </tr>
            <?php }} ?>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">
    $(function () {
        // export file
        $("#file_export").click(function () {
            var params = genParamsForFile();
            var url_export = '/Home/FeeProductStatFile/account' + params;
            $.fileDownload(url_export);
            return false;
        });
    });

    // 为导出文件生成参数
    function genParamsForFile() {
        var params = '';
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var model = $('input[name="model"]').val();
        var name = $('#name').val();
        if (model) {
            params += '&model=' + model;
        }
        if (time_begin) {
            params += '&begin=' + time_begin;
        }
        if (time_end) {
            params += '&end=' + time_end;
        }
        if (name) {
            params += '&name=' + name;
        }
        // tidy url
        if (params) {
            params = params.replace('&', '?');
        }
        console.log(params);
        return params;
    }
</script>
</body>
</html>
