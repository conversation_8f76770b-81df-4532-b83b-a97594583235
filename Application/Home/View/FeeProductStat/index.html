<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">

    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" id="form_init">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"
                               value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d',strtotime('-1 day')) : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control"
                               value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d',strtotime('-1 day')) : $input['end']; ?>"/>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" class="btn btn-primary btn-sm"
                                       value="查询"></li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <tr align="center">
                <th>产品名称</th>
                <th>计费用量</th>
                <th>费用（元）</th>
            </tr>
            <tr>
                <td>总计</td>
                <td>{$total_data['fee_amount']}</td>
                <td>{$total_data['fee_price']}</td>
            </tr>

            <?php
            if ($list && is_array($list)) {
                foreach ($list as $key => $stat) {
            ?>
                <tr>
                    <td>
                        <a href="{:U('account',['model'=>$stat['model'],'begin'=>$input['begin'],'end'=>$input['end']])}">{$stat['name']}</a>
                    </td>
                    <td>{$stat['fee_amount']}</td>
                    <td>{$stat['fee_price']}</td>
                </tr>
            <?php }} ?>
        </table>
    </div>
</div>
</div>
<script type="text/javascript">
    $(function () {
        // export file
        $("#file_export").click(function () {
            var params = genParamsForFile();
            var url_export = '/Home/FeeProductStatFile/index' + params;
            $.fileDownload(url_export);
            return false;
        });
    });

    // 为导出文件生成参数
    function genParamsForFile() {
        var params = '';
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();

        if (time_begin) {
            params += '&begin=' + time_begin;
        }
        if (time_end) {
            params += '&end=' + time_end;
        }
        // tidy url
        if (params) {
            params = params.replace('&', '?');
        }
        return params;
    }
</script>
</body>
</html>
