<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <!-- Include Required Prerequisites -->
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">

    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" id="form_coverage">
                <div class="row-first">
                    <input type="hidden" name="id" value="{$input['id']}">
                    <input type="hidden" name="model" value="<?= $input['model'] ?>">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control" value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d', strtotime('-1 days')) : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control" value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d', strtotime('-1 days')) : $input['end']; ?>"/>
                    </div>

                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" class="btn btn-primary btn-sm" value="查询"></li>
                            <li>
                                <a class="btn btn-info btn-sm" href="{:U('/FeeProductStat/account',['model'=>$input['model'],'begin'=>$input['begin'],'end'=>$input['end']])}" role="button">返回</a>
                            </li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th>日期</th>
                <th>本人查得量</th>
                <th>本人查得费用</th>
                <th>联系人查得量</th>
                <th>联系人查得费用</th>
            </tr>
            </thead>
            <tbody id="tbody">
                <tr>
                    <td>总计</td>
                    <td><?= isset($total_data['fee_own_num']) ? $total_data['fee_own_num'] : 0;?></td>
                    <td><?= isset($total_data['fee_own_price']) ? sprintf("%.2f", $total_data['fee_own_price']) : '0.00'?></td>
                    <td><?= isset($total_data['fee_input_num']) ? $total_data['fee_input_num'] : 0;?></td>
                    <td><?= isset($total_data['fee_input_price']) ? sprintf("%.2f", $total_data['fee_input_price']) : '0.00'?></td>
                </tr>
                <?php if ($list && is_array($list)) {
                    foreach($list as $k => $v){
                ?>
                <tr>
                    <td><?php echo $k;?></td>
                    <td><?= isset($v['fee_own_num']) ? $v['fee_own_num'] : 0;?></td>
                    <td><?= isset($v['fee_own_price']) ? sprintf("%.2f", $v['fee_own_price']) : '0.00'?></td>
                    <td><?= isset($v['fee_input_num']) ? $v['fee_input_num'] : 0;?></td>
                    <td><?= isset($v['fee_input_price']) ? sprintf("%.2f", $v['fee_input_price']) : '0.00'?></td>
                </tr>
                <?php }}?>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">

    $(document).ready(function () {
        // 文件导出事件
        fileDownload();
    });

    // 文件导出功能
    function fileDownload() {
        // export file
        $("#file_export").click(function () {
            // init params
            var params = '';
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var id = $('input[name="id"]').val();
            var model = $('input[name="model"]').val();

            if (time_begin) {
                params = params + '&begin=' + time_begin;
            }
            if (time_end) {
                params = params + '&end=' + time_end;
            }
            if (id) {
                params = params + '&id=' + id;
            }
            if (model) {
                params = params + '&model=' + model;
            }

            // tidy url
            if (params) {
                params = params.replace('&', '?');
            }
            var url_export = '/Home/FeeProductStatFile/detail' + params;
            $.fileDownload(url_export);
            return false;
        });
    }
</script>
</body>
</html>
