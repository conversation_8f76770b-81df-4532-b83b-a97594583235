<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('/Home/Upstream/haomaNewConfig')}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="start_time">计费日期：</label>
                    <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
                    -
                    <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
                </div>
                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group">
                    <button type="button" id="file_in" class="btn btn-success btn-sm">添加配置</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">ID</th>
                <th style="text-align:center;">产品名称</th>
                <th style="text-align:center;">渠道名称</th>
                <th style="text-align:center;" class="order_field" data-info="all">
                    查询量
                    <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i
                            class="v-icon-down-dir"></i></span>
                </th>
                <th style="text-align:center;" class="order_field" data-info="succ">
                    查得量
                     <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i
                             class="v-icon-down-dir"></i></span>
                </th>
                <th style="text-align:center;" class="order_field" data-info="yd">
                    移动查得量
                     <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i
                             class="v-icon-down-dir"></i></span>
                </th>
                <th style="text-align:center;" class="order_field" data-info="liantong">
                    联通查得量
                     <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i
                             class="v-icon-down-dir"></i></span>
                </th>
                <th style="text-align:center;" class="order_field" data-info="dx">
                    电信查得量
                     <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i
                             class="v-icon-down-dir"></i></span>
                </th>
                <th style="text-align:center;">是否计费</th>
                <th style="text-align:center;">计费日期</th>
                <th style="text-align:center;">操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="channel_price_list" id="vo">
                <tr>
                    <td align="center">{$vo.id}</td>
                    <td align="center">{$vo.product_name}</td>
                    <td align="center">{$vo.channel_name}</td>
                    <td align="center">{$vo.peizhi.all}</td>
                    <td align="center">{$vo.peizhi.succ}</td>
                    <td align="center">{$vo.peizhi.yd}</td>
                    <td align="center">{$vo.peizhi.lt}</td>
                    <td align="center">{$vo.peizhi.dx}</td>
                    <td align="center">
                        <switch name="vo.is_billing">
                            <case value="1"><nobr>是</nobr></case>
                            <case value="0"><nobr>否</nobr></case>
                        </switch>
                    </td>
                    <td align="center">{$vo.start_date}</td>
                    <td align="center">
                        <if condition="($vo.is_billing eq 1)">
                            <span data-id="{$vo.id}" role="button" class="btn btn-info btn-sm">编辑</span>
                            <else />

                        </if>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="examplePositionCenter1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">添加配置</h4>
            </div>
            <!--ZJ1-->
            <div class="modal-body" id="Dialogue_form_add">
                <form class="form-horizontal" action="#" method="post" id="DialogueFormAdd">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">产品名称</label>
                        <div class="col-sm-4">
                            <span>号码状态查询</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">渠道名称</label>
                        <div class="col-sm-4">
                            <select name="channel_name" id="channel_name">
                                <option value="">请选择</option>
                                <?php
                                    foreach($channel_name_arr as $key=>$value){
                                ?>
                                <option value="<?php echo $value['id']?>"><?php echo $value['name'];?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">计费日期</label>
                        <div class="col-sm-4">
                            <input type="date" name="start_date" id="start_date" class="form-control" value=""/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">是否计费</label>
                        <div class="col-sm-4">
                            <input type="radio" name="is_billing" id="is_billing_y" value="1" checked/>
                            是
                            &nbsp;&nbsp;
                            <input type="radio" name="is_billing" id="is_billing_n" value="0"/>
                            否
                            &nbsp;&nbsp;
                        </div>
                    </div>
                    <div class="form-group" style="margin-left: 30px;">
                        <a type="button" class="btn btn-primary" id="add_dialogue_add" >+添加</a>
                        <a type="button" class="btn btn-danger" id="del_dialogue_add" >-删除</a>
                    </div>
                    <div id="dialogue_form_html_add">
                        <div class="dialogue_form_child_html_add" style="background-color: #FCFCFC;">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">选项</label>
                                <div>
                                    <div class="col-sm-2 indent-html">
                                        <select class="intents_name" name="peizhi[]" style="width: 120px;">
                                            <option value="">请选择</option>
                                            <option value="all">查询量</option>
                                            <option value="succ">查得量</option>
                                            <option value="yd">移动查得量</option>
                                            <option value="lt">联通查得量</option>
                                            <option value="dx">电信查得量</option>
                                        </select>
                                    </div>
                                </div>
                                <label class="col-sm-2 control-label">价格</label>
                                <div>
                                    <div class="col-sm-2 indent-html">
                                        <input type="text" name="price[]" value="" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" name="bot_id" id="bot_id_add" value="">
                    <input type="hidden" name="goal_type" id="goal_type_add" value="2">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button" class="btn btn-primary btn-sm btn-submit add_submit_dialogue">添加</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<!--编辑功能-----------start----------->
<div class="modal fade" id="examplePositionCenter0">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">编辑配置</h4>
            </div>
            <!--ZJ1-->
            <div class="modal-body" id="Dialogue_form_edit">
                <form class="form-horizontal" action="#" method="post" id="DialogueFormEdit">
                    <input type="hidden" id="id_edit" name="id_edit" value="" />
                    <div class="form-group">
                        <label class="col-sm-2 control-label">产品名称</label>
                        <div class="col-sm-4">
                            <span>号码状态查询</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">渠道名称</label>
                        <div class="col-sm-4">
                            <select name="channel_name_edit" id="channel_name_edit">

                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">计费日期</label>
                        <div class="col-sm-4">
                            <input type="date" name="start_date_edit" id="start_date_edit" class="form-control" value=""/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">是否计费</label>
                        <div class="col-sm-4">
                            <input type="radio" id="is_billing_edit_y" name="is_billing_edit" value="1" checked/>
                            是
                            &nbsp;&nbsp;
                            <input type="radio" id="is_billing_edit_n" name="is_billing_edit" value="0"/>
                            否
                            &nbsp;&nbsp;
                        </div>
                    </div>
                    <div class="form-group" style="margin-left: 30px;">
                        <a type="button" class="btn btn-primary" id="add_dialogue_edit" >+添加</a>
                        <a type="button" class="btn btn-danger" id="del_dialogue_edit" >-删除</a>
                    </div>
                    <div id="dialogue_form_html_edit">

                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button" class="btn btn-primary btn-sm btn-submit edit_submit_dialogue">编辑</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!--编辑功能----------------------end-->


<div class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

            <div class="modal-body">
                <form class="form-horizontal" action="#" method="post">
                    <div id="dialogue_form_html_add_bak">
                        <div class="dialogue_form_child_html_add" style="background-color: #FCFCFC;">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">选项</label>
                                <div>
                                    <div class="col-sm-2 indent-html">
                                        <select class="intents_name" name="peizhi[]" style="width: 120px;">
                                            <option value="">请选择</option>
                                            <option value="all">查询量</option>
                                            <option value="succ">查得量</option>
                                            <option value="yd">移动查得量</option>
                                            <option value="lt">联通查得量</option>
                                            <option value="dx">电信查得量</option>
                                        </select>
                                    </div>
                                </div>
                                <label class="col-sm-2 control-label">价格</label>
                                <div>
                                    <div class="col-sm-2 indent-html">
                                        <input type="text" name="price[]" value="" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<div class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

            <div class="modal-body">
                <form class="form-horizontal" action="#" method="post">
                    <div id="dialogue_form_html_edit_bak">
                        <div class="dialogue_form_child_html_edit" style="background-color: #FCFCFC;">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">选项</label>
                                <div>
                                    <div class="col-sm-2 indent-html">
                                        <select class="intents_name" name="peizhi_edit[]" style="width: 120px;">
                                            <option value="">请选择</option>
                                            <option value="all">查询量</option>
                                            <option value="succ">查得量</option>
                                            <option value="yd">移动查得量</option>
                                            <option value="lt">联通查得量</option>
                                            <option value="dx">电信查得量</option>
                                        </select>
                                    </div>
                                </div>
                                <label class="col-sm-2 control-label">价格</label>
                                <div>
                                    <div class="col-sm-2 indent-html">
                                        <input type="text" name="price_edit[]" value="" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>


<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $("#channel_name").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '请选择',
        width: '200px'
    });
    $("#channel_name_edit").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '请选择',
        width: '200px'
    });

    $(document).ready(function () {
        iniOrder();
        $(".order_field").click(function () {
            order($(this).attr('data-info'))
        });



        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        //批量导入收款单（模态框）
        $("#file_in").click(function () {
            $("#examplePositionCenter1").modal('show');
        });
        $("#add_dialogue_add").click(function(){
            var html = $("#dialogue_form_html_add_bak").html();
            var dialogue_form_length = $("#dialogue_form_html_add").find('.dialogue_form_child_html_add').length;
            if(dialogue_form_length < 5){
                $("#dialogue_form_html_add").append(html);
            }
        });

        $("#del_dialogue_add").click(function(){
            var dialogue_form_length = $("#dialogue_form_html_add").find('.dialogue_form_child_html_add').length;
            if (dialogue_form_length > 1) {
                $("#dialogue_form_html_add").find('.dialogue_form_child_html_add').eq(dialogue_form_length-1).remove();
            }
        });

        $("#add_dialogue_edit").click(function(){
            var html = $("#dialogue_form_html_edit_bak").html();
            var dialogue_form_length = $("#dialogue_form_html_edit").find('.dialogue_form_child_html_edit').length;
            if(dialogue_form_length < 5){
                $("#dialogue_form_html_edit").append(html);
            }
        });
        $("#del_dialogue_edit").click(function(){
            var dialogue_form_length = $("#dialogue_form_html_edit").find('.dialogue_form_child_html_edit').length;
            if (dialogue_form_length > 1) {
                $("#dialogue_form_html_edit").find('.dialogue_form_child_html_edit').eq(dialogue_form_length-1).remove();
            }
        });


        //批量导入收款单
        $(".add_submit_dialogue").click(function () {

            var channel_name = $('#channel_name').val();
            if(channel_name == ''){
                alert('渠道名称不能为空');
            }

            var start_date = $('#start_date').val();
            if(start_date == ''){
                alert('生效日期不能为空');
                return false;
            }
            var is_billing = $("input[name='is_billing']:checked").val();
            if(is_billing == ''){
                alert('是否计费不能为空');
                return false;
            }

            var dialogue_form_select_length = $("#Dialogue_form_add").find("select[name='peizhi[]']").length;
            for (var i = 0; i < dialogue_form_select_length; i++) {
                var select_val = $("#Dialogue_form_add").find("select[name='peizhi[]']").eq(i).val();
                if (select_val=="") {
                    alert('选择框不能为空');
                    return false;
                }
            }
            var dialogue_form_price_length = $("#Dialogue_form_add").find("input[name='price[]']").length;
            for (var i = 0; i < dialogue_form_price_length; i++) {
                var price_val = $("#Dialogue_form_add").find("input[name='price[]']").eq(i).val();
                if (price_val=="") {
                    alert('价格不能为空');
                    return false;
                }else{
                    if(isNaN(price_val)){
                        alert('价格字段必须为数值类型');
                        return false;
                    }
                    if(price_val < 0){
                        alert('价格字段不能小于零');
                        return false;
                    }
                }
            }
            var dialogue_length = $("#dialogue_form_html_add").find(".dialogue_form_child_html_add").length;
            var arr = new Array();
            for (var i=0; i<dialogue_length; i++) {
                var select_val = $("#Dialogue_form_add").find("select[name='peizhi[]']").eq(i).val();
                var price_val = $("#Dialogue_form_add").find("input[name='price[]']").eq(i).val();
                arr.push(select_val + '#'  + price_val);
            }
            $.ajax({
                type: 'post',
                url: "{:U('/Home/Upstream/setBxfShortConfig')}",
                data: {
                    product_id: 801,
                    id: channel_name,
                    start_date: start_date,
                    is_billing: is_billing,
                    arr: arr,
                    type: 'haoma'
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#examplePositionCenter1').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });

        });

        $('tbody').on('click','.btn-info',function(){
            // 隐藏所有表单
            var id = $(this).data('id');
            var url = "/Home/Upstream/getBxfShortConfig";
            $.ajax({
                type: 'post',
                url: url,
                data: {id: id, type: 'haoma'},
                success: function(data) {
                    $('#channel_name_edit').html(data.option);
                    $('#id_edit').val(data.info.id);
                    $("#dialogue_form_html_edit").html(data.text);
                    $('#start_date_edit').val(data.info.start_date);
                    if (data.info.is_billing == 1){
                        $('#is_billing_edit_y').prop('checked', 'checked');
                    }else{
                        $('#is_billing_edit_n').prop('checked', 'checked');
                    }
                    $('#examplePositionCenter0').modal('show');
                }
            });
        });

        /**
         * 处理编辑功能
         */
        $('.edit_submit_dialogue').on('click', function(){
            var  channel_name = $('#channel_name_edit').val();
            if(channel_name == ''){
                alert('渠道名称不能为空');
                return false;
            }

            var start_date = $('#start_date_edit').val();
            if(start_date == ''){
                alert('生效日期不能为空');
                return false;
            }
            var is_billing = $("input[name='is_billing_edit']:checked").val();
            if(is_billing == ''){
                alert('是否计费不能为空');
                return false;
            }

            var dialogue_form_select_length = $("#Dialogue_form_edit").find("select[name='peizhi_edit[]']").length;
            for (var i = 0; i < dialogue_form_select_length; i++) {
                var select_val = $("#Dialogue_form_edit").find("select[name='peizhi_edit[]']").eq(i).val();
                if (select_val=="") {
                    alert('选择框不能为空');
                    return false;
                }
            }
            var dialogue_form_price_length = $("#Dialogue_form_edit").find("input[name='price_edit[]']").length;
            for (var i = 0; i < dialogue_form_price_length; i++) {
                var price_val = $("#Dialogue_form_edit").find("input[name='price_edit[]']").eq(i).val();
                if (price_val=="") {
                    alert('价格不能为空');
                    return false;
                }else{
                    if(isNaN(price_val)){
                        alert('价格字段必须为数值类型');
                        return false;
                    }
                    if(price_val < 0){
                        alert('价格字段不能小于零');
                        return false;
                    }
                }
            }
            var dialogue_length = $("#dialogue_form_html_edit").find(".dialogue_form_child_html_edit").length;
            var arr = new Array();
            for (var i=0; i<dialogue_length; i++) {
                var select_val = $("#Dialogue_form_edit").find("select[name='peizhi_edit[]']").eq(i).val();
                var price_val = $("#Dialogue_form_edit").find("input[name='price_edit[]']").eq(i).val();
                arr.push(select_val + '#'  + price_val);
            }
            var id = $('#id_edit').val();
            $.ajax({
                type: 'post',
                url: "{:U('/Home/Upstream/editBxfShortConfig')}",
                data: {
                    product_id: 801,
                    id: id,
                    channel_id: channel_name,
                    start_date: start_date,
                    is_billing: is_billing,
                    arr: arr,
                    type: 'haoma'
                },
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#examplePositionCenter0').modal('hide');
                        window.location.reload();
                    }else{
                        alert(data.msg);
                    }
                }
            });
        });
    });
    function iniOrder() {
        let params = GetRequest();
        let field  = params.order;
        if (field) {
            let asc = params.asc;
            if (asc == 1) {
                $('.order_field[data-info="' + field + '"]').find('.v-icon-up-dir').addClass('checked');
            } else {
                $('.order_field[data-info="' + field + '"]').find('.v-icon-down-dir').addClass('checked');
            }
        } else {
            // $('.order_field[data-info="all"]').find('.v-icon-down-dir').addClass('checked');
        }
    }
    function order(field) {
        let params = GetRequest();
        if (params.asc == 1) {
            params.asc = 0;
        } else {
            params.asc = 1;
        }
        params.order = field;
        let href     = "{:U('/Home/Upstream/haomaNewConfig')}?";
        $.each(params, function (i, n) {
            href += (i + '=' + n + '&');
        });
        location.href = href.substr(0, href.length - 1);
    }
    function GetRequest() {
        var url        = location.search;
        var theRequest = new Object();
        if (url.indexOf("?") != -1) {
            var str = url.substr(1);
            strs    = str.split("&");
            for (var i = 0; i < strs.length; i++) {
                theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
            }
        }
        return theRequest;
    }

</script>
</body>
</html>
