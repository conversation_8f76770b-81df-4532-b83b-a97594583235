<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<div class="container">
    <div id="breadcrumb_box">
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('/Home/Upstream/upstream/product_id/'.$input['product_id'])}" class="form-inline" method="get" id="list_form">
                <div class="form-group">
                    <label class="control-label" for="search_name">产品名称：</label>
                    <select name="search_product_name" id="search_product_name" class="form-control">
                        <option value="">选择产品</option>
                        <?php foreach($product_name_arr as $key=>$value) {?>
                        <option value="<?php echo $key ?>" <?php echo $input['search_product_name'] == $key ? 'selected' : ''; ?>><?php echo $value;?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="search_name">渠道名称：</label>
                    <select name="search_name" id="search_name" class="form-control">
                        <option value="">选择渠道</option>
                        <?php foreach($channel_list as $key=>$value) {?>
                        <option value="<?php echo $value['name'] ?>" <?php echo $input['search_name'] == $value['name'] ? 'selected' : ''; ?>><?php echo $value['name'];?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group">
                    <label class="control-label" for="search_name">状态：</label>
                    <select name="search_status" id="search_status" class="form-control">
                        <option value="">请选择</option>
                        <option value="1" <?php echo $input['search_status'] == 1 ? 'selected' : ''; ?>>正常</option>
                        <option value="2" <?php echo $input['search_status'] == 2 ? 'selected' : ''; ?>>失效</option>
                    </select>
                </div>

                <div class="form-group">
                    <input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">
                </div>
            </form>
        </div>
    </div>
</div>
<div class="container">

    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">产品id</th>
                <th style="text-align:center;">产品名称</th>
                <th style="text-align:center;">渠道名称</th>
                <?php if($input['product_id'] == 200){?>
                <th style="text-align:center;">移动状态</th>
                <th style="text-align:center;">联通状态</th>
                <th style="text-align:center;">电线状态</th>
                <?php }else{ ?>
                <th style="text-align:center;">总状态</th>
                <?php if($input['product_id'] == 210){ ?>
                <th style="text-align:center;">渠道类型</th>
                <?php } ?>
                <?php } ?>
                <th style="text-align:center;">操作</th>
                <?php if($input['product_id'] != 801){ ?>
                <th style="text-align:center;">计费配置</th>
                <?php } ?>
            </tr>
            </thead>
            <tbody>
            <volist name="list_data" id="vo">
                <tr>
                    <td align="center">{$vo.product_id}</td>
                    <td align="center">{$vo.product_name}</td>
                    <td align="center">{$vo.name}</td>
                    <?php if($input['product_id'] == 200){?>
                    <td align="center">
                        <switch name="vo.yd_status">
                            <case value="1"><nobr>正常</nobr></case>
                            <case value="0"><nobr>失效</nobr></case>
                        </switch>
                    </td>
                    <td align="center">
                        <switch name="vo.lt_status">
                            <case value="1"><nobr>正常</nobr></case>
                            <case value="0"><nobr>失效</nobr></case>
                        </switch>
                    </td>
                    <td align="center">
                        <switch name="vo.dx_status">
                            <case value="1"><nobr>正常</nobr></case>
                            <case value="0"><nobr>失效</nobr></case>
                        </switch>
                    </td>
                    <?php }else {?>
                    <td align="center">
                        <switch name="vo.status">
                            <case value="1"><nobr>正常</nobr></case>
                            <case value="0"><nobr>失效</nobr></case>
                        </switch>
                    </td>
                    <?php if($input['product_id'] == 210){ ?>
                    <td align="center">
                        <switch name="vo.type">
                            <case value="1"><nobr>评分字段</nobr></case>
                            <case value="2"><nobr>统计字段</nobr></case>
                        </switch>
                    </td>
                    <?php } ?>
                    <?php } ?>
                    <td align="center">
                        <a href="javascript:void(0)"  onclick="editGroup({$vo['id']})"  class="btn btn-primary">编辑</a>
                    </td>
                    <?php if($input['product_id'] != 801){ ?>
                    <td align="center">
                        <a href="{:U('/Home/Upstream/priceConfig/id/'.$vo['id'].'/product_name/'.$vo['product_name'])}" target="_blank" class="btn btn-success">配置</a>
                    </td>
                    <?php } ?>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$pagenation">
        <ul class="pagination">
            {$pagenation}
        </ul>
    </if>
</div>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >编辑</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('edit')}" id="file_in_form" method="post" class="form-horizontal">
                    <input type="hidden" id="id" name="id" value="" />
                    <?php if($input['product_id'] == 200){ ?>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">移动状态：</label>
                            <input type="radio" id="yd_status1" name="yd_status" value="1" checked/>
                            正常
                            &nbsp;&nbsp;
                            <input type="radio" id="yd_status2" name="yd_status" value="0"/>
                            失效
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">联通状态：</label>
                            <input type="radio" id="lt_status1" name="lt_status" value="1" checked/>
                            正常
                            &nbsp;&nbsp;
                            <input type="radio" id="lt_status2" name="lt_status" value="0"/>
                            失效
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">电信状态：</label>
                            <input type="radio" id="dx_status1" name="dx_status" value="1" checked/>
                            正常
                            &nbsp;&nbsp;
                            <input type="radio" id="dx_status2" name="dx_status" value="0"/>
                            失效
                        </div>
                    </div>
                    <?php }else{?>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">&nbsp;&nbsp;&nbsp;总状态：</label>
                            <input type="radio" id="status1" name="status" value="1" checked/>
                            正常
                            &nbsp;&nbsp;
                            <input type="radio" id="status2" name="status" value="0"/>
                            失效
                        </div>
                    </div>
                    <?php if($input['product_id'] == 210){ ?>
                    <div class="form-group">
                        <div class="col-sm-11">
                            <label class="col-sm-3 control-label" style="text-align:left">渠道类型：</label>
                            <input type="radio" id="type1" name="type" value="1" checked/>
                            评分字段
                            &nbsp;&nbsp;
                            <input type="radio" id="type2" name="type" value="2"/>
                            统计字段
                        </div>
                    </div>
                    <?php } ?>
                    <?php } ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_submit">确定</button>
            </div>
        </div>
    </div>
</div>



<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function editGroup(id){
        if(id == ''){
            return false;
        }
        $.post("/Home/Upstream/getEdit", { id: id }, function(data){
            var obj = eval('('+data+')');
            $('#id').val(id);
            if (obj.yd_status == 1){
                $('#yd_status1').prop('checked', 'checked');
            }else{
                $('#yd_status2').prop('checked', 'checked');
            }
            if (obj.lt_status == 1){
                $('#lt_status1').prop('checked', 'checked');
            }else{
                $('#lt_status2').prop('checked', 'checked');
            }
            if (obj.dx_status == 1){
                $('#dx_status1').prop('checked', 'checked');
            }else{
                $('#dx_status2').prop('checked', 'checked');
            }
            if (obj.status == 1){
                $('#status1').prop('checked', 'checked');
            }else{
                $('#status2').prop('checked', 'checked');
            }
            if (obj.type == 1){
                $('#type1').prop('checked', 'checked');
            }else{
                $('#type2').prop('checked', 'checked');
            }
        });
       $('#file_in_modal').modal('show');
    }

    $(document).ready(function () {

        $("#search_product_name").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择产品',
            width: '200px'
        });

        $("#search_name").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择渠道',
            width: '200px'
        });

        $("#list_submit").click(function () {
            $("#list_form").submit();
        });

        //批量导入收款单
        $("#file_in_submit").click(function () {
            $('#file_in_modal').modal('hide');
            $("#loading").show();
            var uploadFileForm = new FormData($("#file_in_form")[0]);
            $.ajax({
                cache: true,
                type: "POST",
                url:"/Home/Upstream/setEdit",
                data:uploadFileForm,
                async: false,
                timeout: 0,
                error: function(request) {
                    alert("Connection error");
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(data) {
                    if(data.status == 'ok'){
                        alert(data.msg);
                        $('#file_in_modal').modal('hide');
                        $("#loading").hide();
                        window.location.reload();
                    }else{
                        $("#loading").hide();
                        alert(data.msg);
                    }
                }
            });

        });
    });

</script>
</body>
</html>
