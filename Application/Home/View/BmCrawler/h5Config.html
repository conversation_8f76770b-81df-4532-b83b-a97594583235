<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
</head>
<body>
<div class="container">
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <include file="Common@Public/nav"/>
    <div class="row">
        <div class="col-md-8  col-md-offset-2">
            <form class="form-horizontal" id="form_h5">
                <div class="panel panel-default">
                    <input type="hidden" value="<?= $account_info['id'];?>" name="id" id="id">
                    <input type="hidden" value="<?= $account_info['contactor_page'];?>" name="contactor_page">
                    <?php if ($account_info['contactor_page'] == 'Y') {
?>
                    <!-- 申请人信息配置 -->
                    <div class="panel-heading">
                        申请人信息配置
                        <a href="/Home/BmCrawler/index" class="pull-right btn btn-primary" style="margin-left: 15px">返回列表</a>
                        <label onclick="previewBaseInfo()" class="btn btn-info pull-right">预览基础信息页</label>
                        <h5></h5>
                    </div>
                    <div class="panel-body">
                        <div class="form-group">
                            <label for="ui_proposer_show_fields" class="col-md-2">显示的字段</label>
                            <div class="col-md-9" id="ui_proposer_show_fields">
                                <?php foreach($config_info_shenqing as $shenqing_config) {?>
                                <label class="checkbox-inline">
                                    <input type="checkbox" class="show_fields"
                                           name="ui_proposer_show_fields[]"  <?= (strpos($account_info['ui_proposer_show_fields'], $shenqing_config)!==false) ? 'checked' : '' ?>
                                    value="<?= $shenqing_config; ?>"><?= $shenqing_config; ?>
                                </label>
                                <?php } ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="ui_proposer_required_fields" class="col-md-2">必填的字段</label>
                            <div class="col-md-9" id="ui_proposer_required_fields">
                                <?php foreach($config_info_shenqing as $shenqing_config) {?>
                                <label class="checkbox-inline">
                                    <input type="checkbox" class="required_fields"
                                           name="ui_proposer_required_fields[]" <?= (strpos($account_info['ui_proposer_required_fields'], $shenqing_config) !==false ) ? 'checked' : '' ?>
                                    value="<?= $shenqing_config; ?>"><?= $shenqing_config; ?>
                                </label>
                                <?php } ?>
                            </div>
                        </div>

                    </div>

                    <!-- 紧急联系人信息配置-->
                    <div class="panel-heading">紧急联系人信息配置</div>
                    <div class="panel-body">
                        <?php if ($account_info['emergency_contact_detail_limits']) { ?>
                        <div style="display:block" id="actual_ralationship">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">最多紧急联系人数量</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" name="emergency_contact_max_number"
                                           value="<?= $account_info['emergency_contact_max_number'] ?>">
                                </div>
                            </div>
                            <div class="row" id="relationship_div">
                                <label class="col-sm-2 control-label">必填紧急联系人限制</label>
                                <?php
                             $keys = array_keys($account_info['emergency_contact_detail_limits']);
                             $first_key = $keys ? $keys[0] : '';
                             foreach ($account_info['emergency_contact_detail_limits'] as $key=>$item_relation) {
                                ?>
                                <div class="form-inline" style="margin-bottom: 15px">
                                    <?php if ($first_key != $key) {?>
                                    <label class="col-sm-2 control-label"></label>
                                    <?php }?>
                                    <select multiple="multiple" name="relationship<?= $key ?>[]">
                                        <?php foreach($relation_ships as $relationship) {?>
                                        <option value="<?= $relationship ;?>"
                                        <?= in_array($relationship, $item_relation) ? selected : ''; ?>
                                        ><?= $relationship ;?></option>
                                        <?php }?>
                                    </select>
                                    <?php if ($first_key == $key) {?>
                                    <button type="button" onclick="addEmergency()" class="btn btn-default">+</button>
                                    <?php } else { ?>
                                    <button type="button" class="btn btn-default disappear_element"
                                            onclick="disappearElement($(this))" style="width: 34px">-
                                    </button>
                                    <?php }?>
                                </div>
                                <?php }?>
                            </div>
                        </div>

                        <!-- 没有配置 或者不需要的情况 -->
                        <?php } else {
                $show =  'ui' == $account_info['source'] && 'Y' == $account_info['contactor_page'];
    ?>
                        <div style="display:<?= $show ? 'block' : 'none'?>">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">最多紧急联系人数量</label>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control" name="emergency_contact_max_number"
                                           value="<?= $account_info['emergency_contact_max_number'] ?>">
                                </div>
                            </div>
                            <div class="row" id="relationship_div">
                                <label class="col-sm-2 control-label">必填紧急联系人限制</label>
                                <div class="form-inline" style="margin-bottom: 15px">
                                    <select class="form-control relationship_request" multiple="multiple"
                                            name="relationship0[]">
                                        <?php foreach($relation_ships as $relationship) {?>
                                        <option value="<?= $relationship ;?>"><?= $relationship ;?></option>
                                        <?php }?>
                                    </select>
                                    <button type="button" onclick="addEmergency()" class="btn btn-default">+</button>
                                </div>
                            </div>
                        </div>
                        <?php }?>
                    </div>
                    <?php } ?>
                    <!-- 其他配置 -->
                    <div class="panel-heading">
                        其他配置
                        <?php if ($account_info['contactor_page'] != 'Y'){ ?>
                        <a href="/Home/BmCrawler/index" class="pull-right btn btn-primary" style="margin-left: 15px">返回列表</a>
                        <h5></h5>
                        <?php } ?>
                    </div>
                    <div class="panel-body">
                        <!-- 授权失效时间 -->
                        <div class="form-group" id="h5_authorization_time">
                            <label class="col-sm-3" for="effective_authorization_time">H5授权链接失效限制时间</label>
                            <div class="col-sm-2">
                                <input type="text" name="effective_authorization_time" id="effective_authorization_time"
                                       value="<?=  $account_info['effective_authorization_time'] ? $account_info['effective_authorization_time'] : ''; ?>"
                                       class="form-control">
                            </div>
                            <label style="font-weight: normal">*说明：填写后，用户在该时间内只能授权一次</label>
                        </div>
                        <!-- 协议 -->

                        <div class="form-group" style="display: block" id="protocol">
                            <label class="col-md-2">授权协议</label>
                            <div class="col-md-7">
                                <label class="radio-inline">
                                    <input type="radio" name="protocol_default"
                                           value="1" <?= $account_info['protocol_default'] == 1 ? 'checked' : '';  ?>>
                                    默认协议
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="protocol_default"
                                           value="2" <?= $account_info['protocol_default'] != 1 ? 'checked' : ''; ?>>
                                    自定义协议
                                </label>
                                <label class="radio-inline" id="label_preview"
                                       style="display:<?= $account_info['protocol_default'] == 1 ? 'none' : 'inline';  ?>;">
                                    <a href="javascript:void(0);" target="_blank" onclick="preview($(this))"
                                       class="btn btn-primary btn-xs" preview_url='<?= $preview_url; ?>'>预览</a>
                                </label>
                            </div>
                        </div>

                        <?php  $show = ($account_info['protocol_default'] == 1) ? 'none' : 'block' ; ?>
                        <div class="form-group" id="protocol_content_show"
                             style="width:80%; margin: auto; display: <?= $show; ?>">
                            <div id="editor">

                            </div>
                            <textarea id="protocol_content" name="protocol_content"
                                      style="width:100%; height:200px; display: none"></textarea>
                        </div>
                    </div>
                </div>
                <input class="btn btn-primary btn-sm pull-right" id="but-submit" value="更新配置">
            </form>
        </div>
    </div>
</div>

<!--预览协议 模拟post提交使用 -->
<div style="display: none" id="preview_protocol"></div>
<div style="display: none" id="preview_base_info"></div>
<script type="text/javascript">

    $(function () {
        // 初始化select2
        iniSelect2();

        // 表单提交事件
        formSubmit();

        // 默认协议和自定义协议切换事件
        protocolClick();

        // 初始化富文本框
        iniRichEdit();
    });

    // 初始化富文本框
    function iniRichEdit() {
        // 要编辑的产品
        let id = $('#id').val();
        axios.get('/Api/BackendCrawler/protocol', {
            params: {
                id: id
            }
        }).then(function (response) {
            console.log('初始化页面', response);
            if (response.data.status === 0) {
                iniProtocolDo(response.data.protocol);
            } else {
                console.log('获取授权协议失败', response);
                alert('获取授权协议失败,请稍后再试');
            }
        }).catch(function (response) {
            console.log('获取授权协议失败', response);
        });

    }

    // 初始化授权协议
    function iniProtocolDo(protocol) {
        $('#editor').append(protocol);

        // 授权协议(富文本框)
        let editor = new window.wangEditor('#editor');

        let protocol_content = $('#protocol_content');
        editor.customConfig.onchange = function (html) {
            // 监控变化，同步更新到 textarea
            protocol_content.val(html);
        };
        editor.customConfig.pasteFilterStyle = false; // 不对粘贴的样式过滤
        editor.customConfig.uploadImgShowBase64 = true;  // 使用 base64 保存图片
        editor.customConfig.showLinkImg = true; // 使用网络照片
        editor.create();

        // 初始化 textarea 的值
        protocol_content.val(editor.txt.html());
    }

    // 默认协议和自定义协议切换事件
    function protocolClick() {
        $('[name=protocol_default]').click(function () {
            let protocol_val = parseInt($(this).val());

            if (protocol_val === 1) {
                $('#protocol_content_show').css('display', 'none');
                $('#label_preview').css('display', 'none');
            } else {
                $('#protocol_content_show').css('display', 'block');
                $('#label_preview').css('display', 'inline');
            }
        });

    }

    // 表单提交事件
    function formSubmit() {
        $('#but-submit').click(function () {
            let formData = new FormData($('#form_h5')[0]);
            axios.post('/Api/BackendCrawler/h5Config', formData, {
                responseType: 'json',
            }).then(function (response) {
                if (response.data.status === 0) {
                    alert('更新成功,正在跳转到列表');
                    window.setTimeout(function () {
                        window.location.href = '/Home/BmCrawler/index';
                    }, 2000);
                } else {
                    console.log(response);
                    alert('更新失败' + !!response.data.errors.msg ? response.data.errors.msg : '');
                }
            }).catch(function (response) {
                alert('更新失败' + !!response.data.errors.msg ? response.data.errors.msg : '');
                console.log(response);
            });
        });
    }

    // 初始化select2
    function iniSelect2() {
        $('select').select2({
            width: 500,
            allowClear: false,
            theme: "bootstrap",
        });
    }

    // 添加紧急联系人选项的事件
    function addEmergency() {
        // 增加select2
        addEmergencySelect();
        // 初始化新增的select
        iniSelect2();
    }

    // 增加select2
    function addEmergencySelect() {
        let selector_length = $('select').length;
        let item_relationship = '<div class="form-inline" style="margin-bottom: 15px">\n' +
            '<label class="col-sm-2 control-label"></label>\n' +
            '<select  class="form-control relationship_request" multiple="multiple" name="relationship' + selector_length + '[]">\n' +
            '<?php foreach($relation_ships as $relationship) {?>\n' +
            '      <option value="<?= $relationship ;?>"><?= $relationship ;?></option>\n' +
            '<?php }?>\n' +
            '</select>\n' +
            '<button type="button" class="btn btn-default disappear_element" onclick="disappearElement($(this))" style="width: 34px">-</button></div>';
        $('#relationship_div').append(item_relationship);
    }


    // 紧急联系人关系删减事件
    function disappearElement(that) {
        $(that).parent().remove();
    }

    function preview(that) {
        let protocol = $('#protocol_content').val();
        let preview_url = $(that).attr('preview_url');
        $('#preview_protocol').html('<form id="preview_form" target="NewWindow" action="' + preview_url + '" method="POST" style="display:none;"><textarea name="protocol_content">' + protocol + '</textarea></form>');
        window.open("", 'NewWindow');
        $('#preview_form').submit();
    }

    // 预览基础信息
    function previewBaseInfo() {
        // 获取紧急联系人配置
        let form_data = new FormData($('#form_h5')[0]);
        axios.post('/Api/BackendCrawler/previewData', form_data, {responseType: 'json'}).then(function (response) {
            console.log(response);
            if (response.data.status === 0) {
                previewBaseInfoDo(response.data.preview_data);
            } else {
                console.log('预览出错', response);
            }
        }).catch(function (response) {
            console.log('预览出错2', response);
        });
    }

    // 预览DO
    function previewBaseInfoDo(preview_data) {
        // 紧急联系人配置
        let preview_url = preview_data.preview_url;
        let ele_show_fields = preview_data.ui_proposer_show_fields;
        let ele_required_fields = preview_data.ui_proposer_required_fields;
        let relationship_str = preview_data.emergency_contact_detail_relation_limits;
        let emergency_contact_max_number = preview_data.emergency_contact_max_number;

        let html_form = "<form id='from_base_info' target='NewWindow' action='" + preview_url + "' method='POST' style='display:none;'>" +
            "<input name='ui_proposer_show_fields' value='" + ele_show_fields + "'>" +
            "<input name='ui_proposer_required_fields' value='" + ele_required_fields + "'>" +
            "<input name='emergency_contact_detail_relation_limits' value='" + relationship_str + "'>" +
            "<input name='emergency_contact_max_number' value='" + emergency_contact_max_number + "'>" +
            "</form>";
        $('#preview_base_info').html(html_form);
        window.open("", 'NewWindow');
        $('#from_base_info').submit();
    }

</script>
</body>
</html>