<div id="myTabContent" class="tab-content">
    <div class="tab-pane fade in active" id="bmcrawling">
        <form class="form-horizontal" method="post" role="form" id="form_set_id" protocol_data="<?= $account_info['protocol_content'];?>">

            <div class="panel-body">

                <div class="form-group">
                    <label class="col-sm-2 control-label">账号名称</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" name="developer" value="{$account_info['developer']}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" for="set_choose_belongs_to_user">所属客户：</label>
                    <div class="col-sm-4" id="set_div_choose_belongs_to_user">
                        <select name="name" id="set_choose_belongs_to_user" class="form-control">
                            <option value="">暂不绑定</option>
                            <?php foreach($list_account_for_select2 as $account) {?>
                            <option value="<?= $account['name']; ?>" <?= ($account['id']==$account_product_info['account_id'])? 'selected="selected"' : ''?>><?= $account['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">账号状态</label>
                    <div class="col-sm-4">
                        <select class="form-control" name="status">
                            <option value="1" selected>可用</option>
                            <option value="2" <?=  (1 != $account_info['status']) ? 'selected="selected"' : ''?>>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">签约状态</label>
                    <div class="col-sm-4">
                        <select name="contract_status" id="contract_status" class="form-control">
                            <option value="1" <?= ($account_info['contract_status'] == 1) ? 'selected' : '' ?>>已签约已付款</option>
                            <option value="2" <?= ($account_info['contract_status'] == 2) ? 'selected' : '' ?>>已签约未付费</option>
                            <option value="3" <?= ($account_info['contract_status'] == 3) ? 'selected' : '' ?>>未签约</option>
                            <option value="5" <?= ($account_info['contract_status'] == 5) ? 'selected' : '' ?>>特殊客户</option>
                            <option value="4" <?= ($account_info['contract_status'] == 4) ? 'selected' : '' ?>>其他</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">是否调用实名认证接口</label>
                    <div class="col-sm-10">
                        <label class="radio-inline">
                            <input type="radio" name="need_id" value="2" <?= (2 == $account_info['need_id']) ? 'checked' : '';?>>
                            不调用
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="need_id" value="1" <?= (1 == $account_info['need_id']) ? 'checked' : '';?>>
                            调用
                        </label>
                        <span style="color: red;display:inline-block;padding-left: 16px;padding-top:7px;vertical-align:middle;">提示：是否调用实名认证接口影响产品价格，更改后请及时变更计费配置</span>
                    </div>
                </div>
                <div class="form-group access_sel">
                    <label class="col-sm-2 control-label">接入方式</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="source" class="source-radio" value="ui" checked>
                            h5接入
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="source" class="source-radio" value="api" <?= ('api' == $account_info['source']) ? 'checked' : '';?>>
                            api接入
                        </label>
                    </div>
                </div>

                <if condition="'ui' == $account_info['source']">
                    <div class="form-group ui-dist">
                        <label class="col-sm-2 control-label">授权期限</label>
                            <div class="col-sm-4">
                                <input type="date" class="form-control" id="token_due_date" name="token_due_date" value="<?= date('Y-m-d',$account_info['token_due_date']) ?>">
                            </div>
                        <label class="col-sm-2 control-label">联系人页面</label>
                        <div class="col-sm-4">
                            <label class="radio-inline">
                                <input type="radio" name="contactor_page" value="N" checked>
                                不接入
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="contactor_page" value="Y" <?= ('Y' == $account_info['contactor_page']) ? 'checked' : '';?>>接入
                            </label>
                        </div>
                    </div>
                </if>

                <div class="form-group">
                    <label class="col-sm-2 control-label">是否生成报告</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="need_report" class="need_report_radio" value="1" checked>
                            是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="need_report" class="need_report_radio" value="0" <?= (1 != $account_info['need_report']) ? 'checked' : '' ?>>
                            否
                        </label>
                    </div>
                    <label class="col-sm-2 control-label">是否加密推送数据</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="security_push_service" value="1" checked>
                            是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="security_push_service" value="0" <?= (1 != $account_info['security_push_service']) ? 'checked' : '' ?>>
                            否
                        </label>
                    </div>
                </div>

                <div class="form-group" id="output_report" style="display:<?= (isset($account_info['need_report']) && (1 == $account_info['need_report'])) ? 'block' : 'none' ?>">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">运营商报告输出内容</label>
                    <div class="col-sm-4">
                    <span>
                        <label class="checkbox-inline">
                            <input type="checkbox" name="output_report[]" value="1" <?=  (strpos($account_info['output_report'], '1') !== false) ? 'checked' : ''; ?>>催收风险检测
                        </label>
                    </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">运营商报告来源</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="report_source" value="1" <?= (1 == $account_info['report_source']) ? 'checked' : '';?>>
                            羽乐科技
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="report_source" value="2" <?= (2 == $account_info['report_source']) ? 'checked' : '';?>>
                            魔蝎
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">详单来源</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="record_source" value="1" <?= (1 == $account_info['record_source']) ? 'checked' : '';?>>
                            爬虫
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="record_source" value="2" <?= (2 == $account_info['record_source']) ? 'checked' : '';?>>
                            客户既有详单
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否输出邦信分详单版V1参数</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="need_dunning" value="1" class="cuishou_radio" checked>
                            是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="need_dunning" value="2" class="cuishou_radio" <?= (1 != $account_info['need_dunning']) ? 'checked' : '' ?>>
                            否
                        </label>
                    </div>
                </div>

                <div class="form-group cuishou" style="display:<?= (isset($account_info['need_dunning']) && (1 == $account_info['need_dunning'])) ? 'block' : 'none' ?>">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">邦信分详单版V1APIKEY</label>
                    <div class="col-sm-4">
                        <input type="text" class="form-control" name="service_key" value="{$service_info['service_key']}">
                    </div>
                </div>

                <div class="form-group cuishou" style="display:<?= (isset($account_info['need_dunning']) && (1 == $account_info['need_dunning'])) ? 'block' : 'none' ?>">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">邦信分详单版V1PASSWORD</label>
                    <div class="col-sm-4">
                        <input type="text" name="service_secret" class="form-control" value="{$service_info['service_secret']}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">爬虫是否输出邦秒配</label>
                    <div class="col-sm-4">
                        <label class="radio-inline">
                            <input type="radio" name="need_itag" value="1" class="" <?= ($account_info['need_itag'] == 1) ? 'checked' : '';?>>
                            是
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="need_itag" value="2" class="" <?= ($account_info['need_itag'] != 1) ? 'checked' : '';?>>
                            否
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">截止日期</label>
                        <div class="col-sm-4">
                            <input type="date" class="form-control" id="expiration_date" name="expiration_date" value="<?= date('Y-m-d',$account_info['expiration_date']) ?>">
                        </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">APPID</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="appid" id="input_appid" value="{$account_info['appid']}" readonly>
                    </div>
                    <if condition="empty($account_info['appid'])">
                        <div class="col-sm-2">
                            <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appid',32)">生 成</button>
                        </div>
                    </if>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">APPSECRET</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" name="appsecret" id="input_appsecret" value="{$account_info['appsecret']}" readonly>
                    </div>

                    <if condition="!$account_info['appsecret']">
                        <div class="col-sm-2">
                            <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appsecret',64)">生 成</button>
                        </div>
                    </if>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">CID</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" value="{$account_info['id']}" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label">TOKEN</label>
                    <div class="col-sm-8">
                        <input type="text" class="form-control" value="{$account_info['token']}" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">详单推送地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="notify_url" value="{$account_info['notify_url']}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">授权状态回调地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="authorize_notify_url" value="{$account_info['authorize_notify_url']}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">报告状态回调地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="report_notify_url" value="{$account_info['report_notify_url']}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">重定向地址</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="close_redirect_url" value="{$account_info['close_redirect_url']}">
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-sm-2 control-label">重定向地址白名单</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" name="redirect_url_domain" value="{$account_info['redirect_url_domain']}">
                    </div>
                </div>
            </div>

            <div class="alert alert-danger" role="alert">
                <p>注意：</p>
                <!--<p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>-->
                <!--<p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>-->
                <p>1.推送地址由客户提供,信息(eg:通话详单)推送到该地址,无需求可置空</p>
                <p>2.重定向地址由客户提供,接入h5时,授权成功后的跳转地址,无需求可置空</p>
                <p>3.输出催收参数时，应填写催收参数的apikey和password</p>
            </div>

        </form>
    </div>
</div>

<!--预览协议 模拟post提交使用 -->
<div style="display: none" id="preview"></div>

<script type="text/javascript">
    $(function () {
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};

        //避免搜索框不聚焦方法
        $("#set_choose_belongs_to_user").select2({
            dropdownParent: $("#set_div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#set_choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });

        // 将protocol_content字符串转成html
        var protocol_data = $('#form_set_id').attr('protocol_data');
        $('#editor').append($(protocol_data));

        // 授权协议(富文本框)
        var E = window.wangEditor;
        var editor = new E('#editor');

        var protocol_content = $('#protocol_content');
        editor.customConfig.onchange = function (html) {
            // 监控变化，同步更新到 textarea
            protocol_content.val(html);
        };
        editor.customConfig.pasteFilterStyle = false; // 不对粘贴的样式过滤
        editor.customConfig.uploadImgShowBase64 = true;  // 使用 base64 保存图片
        editor.customConfig.showLinkImg = true; // 使用网络照片
        editor.create();

        // 初始化 textarea 的值
        protocol_content.val(editor.txt.html());

        // 默认协议和自定义协议切换事件
        $('[name=protocol_default]').click(function(){
            var protocol_val = parseInt($(this).val());

            if (protocol_val === 1) {
                $('#protocol_content_show').css('display', 'none');
                $('#label_preview').css('display', 'none');
            } else {
                $('#protocol_content_show').css('display', 'block');
                $('#label_preview').css('display', 'inline');
            }
        });

        // 紧急联系人添加事件
        $("#add_element").click(function() {
            var item_relationship = '<div class="form-inline">\n' +
                '<label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;"></label>\n' +
                '                    &nbsp;&nbsp;关系&nbsp;\n' +
                '                    <select  class="form-control" name="relationship[]">\n' +
                '                        <?php foreach($relation_ships as $relationship) {?>\n' +
                '                        <option value="<?= $relationship ;?>"><?= $relationship ;?></option>\n' +
                '                        <?php }?>\n' +
                '                    </select>&nbsp;\n' +
                '                    数量&nbsp;\n' +
                '                    <input type="text" class="form-control" name="emergency_contact_limit_number[]" value="1">&nbsp;\n' +
                '                    <button type="button" class="btn btn-default disappear_element" onclick="disappearElement($(this))" style="width: 34px">-</button>\n' +
                '                </div>';
            $('#relationship_div').append(item_relationship);
        });

        var today_str = (new Date()).toDateString();

        // check token_due_date
        $("#token_due_date").change(
            function () {
                var time_choose = $(this).val();
                if ((Date.parse(time_choose + ' GMT +8') - Date.parse(today_str + ' GMT +8') < 0)) {
                    confirm('您选定的授权日期是无效的, 请问您确定要这样吗?');
                }
            }
        );

        // check expiration_date
        $("#expiration_date").change(
            function () {
                var time_choose = $(this).val();
                if ((Date.parse(time_choose + ' GMT +8') - Date.parse(today_str + ' GMT +8') < 0)) {
                    confirm('您选定的截止日期是无效的, 请问您确定要这样吗?');
                }
            }
        );

        // check need_report change
        $("input:radio.need_report_radio").on('change', function(event) {
            if ('1' == $(this).val()) {
                $("#output_report").css({
                    display: 'block'
                });
            } else {
                $("#output_report").css({
                    display: 'none'
                });
            }
        });

        // check dunning
        $("input:radio.cuishou_radio").on('change', function() {
            if ('1' == $(this).val()) {
                $("div.cuishou").css({
                    display: 'block'
                });
            } else {
                $("div.cuishou").css({
                    display: 'none'
                });
            }
        });

        // 协议预览
        $('#preview').click(function(){
            console.log('hello world');
        });

    });

    // 紧急联系人关系删减事件
    function disappearElement(that) {
        $(that).parent().remove();
    }

    // 联系人页面的触发紧急联系人是否显示
    $(":radio[name=contactor_page]").click(function(){

        // radio的选中与否都是初始化的时候定下的
        if ($(this).val() === 'Y') {
            $("#actual_ralationship").css('display', 'block');
        } else {
            $("#actual_ralationship").css('display', 'none');
        }
    });

    function radioControlRelation(that) {

        if ($(that).val() === 'Y') {
            $("#actual_ralationship").css('display', 'block');
        } else {
            $("#actual_ralationship").css('display', 'none');
        }
    }

    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length)
    {
        if(confirm('确定生成appid,appsecret吗,如果已授权会使当前账号的授权失效?')) {
            creat_hash(id, length);
        }
    }

    $("input:radio.source-radio").change(function(event) {
        event.preventDefault();
        if ('ui' != $(this).val()) {
            $("#h5_authorization_time").css('display', 'none');
            $("#actual_ralationship").css('display', 'none');
            $("#protocol").css('display', 'none');
            $("#protocol_content_show").css('display', 'none');

            $("div.ui-dist").remove();
        } else {
            $("#h5_authorization_time").css('display', 'block');
            $("div.ui-dist").remove();
            createInput();

            // 是否展示最多联系人
            $('[name=contactor_page]:checked').click();

            // 授权协议
            $("#protocol").css('display', 'block');
            $('[name=protocol_default]:checked').click();
        }
    });

    function createInput()
    {
        var htm = '\
            <div class="form-group ui-dist"> \
                <label class="col-sm-2 control-label">授权期限</label> \
                <div class="col-sm-4"> \
                    <input type="date" class="form-control" id="token_due_date" name="token_due_date" value=""> \
                </div> \
                <label class="col-sm-2 control-label">联系人页面</label> \
                <div class="col-sm-4"> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" onclick="radioControlRelation($(this))"  value="N" <?php if("N" == $account_info["contactor_page"]): ?>checked<?php endif;?>> \
                        不接入 \
                   </label> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" onclick="radioControlRelation($(this))" value="Y" <?php if("Y" == $account_info["contactor_page"]): ?>checked<?php endif;?>>接入 \
                   </label> \
               </div> \
            </div> \
'
        $("div.access_sel").after(htm);
    }

    function preview(that) {
        var protocol = $('#protocol_content').val();
        var preview_url = $(that).attr('preview_url');
        $('#preview').html('<form id="preview_form" target="NewWindow" action="' + preview_url +'" method="POST" style="display:none;"><textarea name="protocol_content">'+ protocol +'</textarea></form>');
        window.open("", 'NewWindow');
       $('#preview_form').submit();
    }
</script>


