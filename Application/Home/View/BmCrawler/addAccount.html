<form class="form-horizontal" role="form" id="form_set_id" data-info="<?= isset($info)?$info:'' ?>">
    <div class="form-group">
        <label class="col-sm-2 control-label">账号名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="developer" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" for="choose_belongs_to_user">所属客户：</label>
        <div class="col-sm-4" id="div_choose_belongs_to_user">
            <select name="name" id="choose_belongs_to_user" class="form-control">
                <option value="">暂不绑定</option>
                <?php foreach($list_account_for_select2 as $account) {?>
                <option value="<?= $account['name']; ?>"><?= $account['name']; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="status">
                <option value="1">可用</option>
                <option value="2">禁用</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签约状态</label>
        <div class="col-sm-4">
            <select name="contract_status" id="contract_status" class="form-control">
                <option value="1">已签约已付款</option>
                <option value="2">已签约未付费</option>
                <option value="3" selected>未签约</option>
                <option value="5">特殊客户</option>
                <option value="4">其他</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">是否调用实名认证接口</label>
        <div class="col-sm-10">
            <label class="radio-inline">
                <input type="radio" name="need_id" value="2" checked>
                不调用
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_id" value="1">
                调用
            </label>
            <span style="color: red;display:inline-block;padding-left: 16px;padding-top:7px;vertical-align:middle;">提示：是否调用实名认证接口影响产品价格，更改后请及时变更计费配置</span>
        </div>
    </div>
    <div class="form-group access_sel">

        <label class="col-sm-2 control-label">接入方式</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="source" class="source-radio" value="ui" checked>
                h5接入
            </label>
            <label class="radio-inline">
                <input type="radio" name="source" class="source-radio" value="api">
                api接入
            </label>
        </div>
    </div>

    <div class="form-group ui-dist">
        <label class="col-sm-2 control-label">授权期限</label>
        <if condition="empty($info['token_due_date'])">
            <div class="col-sm-4">
                <input type="date" class="form-control" name="token_due_date" value="">
            </div>
            <else/>
            <div class="col-sm-4">
                <input type="date" class="form-control" name="token_due_date"
                       value="{:date('Y-m-d',$info['token_due_date'])}">
            </div>
        </if>
        <label class="col-sm-2 control-label">联系人页面</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="contactor_page" value="N" checked>不接入
            </label>
            <label class="radio-inline">
                <input type="radio" name="contactor_page" value="Y">接入
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">是否生成报告</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_report" value="0" class="need_report_radio" checked>
                否
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_report" value="1" class="need_report_radio">
                是
            </label>
        </div>

        <label class="col-sm-2 control-label">是否加密推送数据</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="security_push_service" value="0" checked>
                否
            </label>
            <label class="radio-inline">
                <input type="radio" name="security_push_service" value="1">
                是
            </label>
        </div>
    </div>

    <div class="form-group output_report" style="display: none" id="output_report">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">运营商报告输出内容</label>
        <div class="col-sm-4">
            <span>
                <label class="checkbox-inline">
                    <input type="checkbox" name="output_report[]" value="1">催收风险检测
                </label>
            </span>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">运营商报告来源</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="report_source" value="1" checked>
                羽乐科技
            </label>
            <label class="radio-inline">
                <input type="radio" name="report_source" value="2" >
                魔蝎
            </label>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">详单来源</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="record_source" value="1" checked>
                爬虫
            </label>
            <label class="radio-inline">
                <input type="radio" name="record_source" value="2" >
                客户既有详单
            </label>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否输出邦信分详单版V1参数</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_dunning" value="2" class="cuishou_radio" checked>
                否
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_dunning" value="1" class="cuishou_radio">
                是
            </label>
        </div>
    </div>

    <div class="form-group cuishou" style="display: none;">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">邦信分详单版V1APIKEY</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="service_key" value="">
        </div>
    </div>

    <div class="form-group cuishou" style="display: none;">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">邦信分详单版V1PASSWORD</label>
        <div class="col-sm-4">
            <input type="text" name="service_secret" class="form-control" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">爬虫是否输出邦秒配</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_itag" value="2" class="" checked>
                否
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_itag" value="1" class="">
                是
            </label>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">截止日期</label>
        <if condition="empty($info['expiration_date'])">
            <div class="col-sm-4">
                <input type="date" class="form-control" name="expiration_date" value="">
            </div>
            <else/>
            <div class="col-sm-4">
                <input type="date" class="form-control" name="expiration_date"
                       value="{:date('Y-m-d',$info['expiration_date'])}">
            </div>
        </if>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">APPID</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="appid" id="input_appid" value="" readonly>
        </div>

        <IF condition="empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appid',32)">生 成
                </button>
            </div>
        </IF>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">APPSECRET</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="appsecret" id="input_appsecret" value="" readonly>
        </div>
        <IF condition="empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appsecret',64)">生 成
                </button>
            </div>
        </IF>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">详单推送地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="notify_url" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">授权状态回调地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="authorize_notify_url" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">报告状态回调地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="report_notify_url" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">重定向地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="close_redirect_url" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">重定向地址白名单</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="redirect_url_domain" value="">
        </div>
    </div>
</form>
<div class="alert alert-danger" role="alert">
    <p>注意：</p>
    <!--<p>1.创建新账号时,密码默认为:123456，由客户登录金融企业服务平台自行修改密码</p>-->
    <!--<p>2.创建新账号时,如客户未提供邮箱,建议使用客户名的伪邮箱如:<EMAIL>,由客户登录金融企业服务平台自行修改账号</p>-->
    <p>1.推送地址由客户提供,信息(eg:通话详单)推送到该地址,无需求可置空</p>
    <p>2.重定向地址由客户提供,接入h5时,授权成功后的跳转地址,无需求可置空</p>
    <p>3.输出催收参数时，应填写催收参数的apikey和password</p>
</div>
<!--预览协议 模拟post提交使用 -->
<div style="display: none" id="preview"></div>
<script type="text/javascript">
    $(function () {
        // 初始化select2
        initSelect2();

        // 初始化api_key && api
        genApiKeyAndSecret();

        // 来源(h5或者api)切换事件
        sourceChange();

        // 催收按钮控制
        radioCuishou();

        // 是否生报告按钮控制
        showReport();
    });

    // 初始化select2
    function initSelect2() {
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};
        //避免搜索框不聚焦方法
        $("#choose_belongs_to_user").select2({
            dropdownParent: $("#div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });
    }


    // 是否生报告按钮控制
    function showReport() {
        $("input:radio.need_report_radio").on('change', function (event) {
            if ('1' == $(this).val()) {
                $("#output_report").css({
                    display: 'block'
                });
            } else {
                $("#output_report").css({
                    display: 'none'
                });
            }
        });
    }


    // 催收单选按钮变化
    function radioCuishou() {
        $("input:radio.cuishou_radio").on('change', function (event) {
            if ('1' == $(this).val()) {
                $("div.cuishou").css({
                    display: 'block'
                });
            } else {
                $("div.cuishou").css({
                    display: 'none'
                });
            }
        });
    }

    // 来源(h5或者api)切换事件
    function sourceChange() {
        $("input:radio.source-radio").on('change', function (event) {
            event.preventDefault();
            $("div.ui-dist").remove();
            if ('ui' != $(this).val()) {
                $("div.ui-dist").remove();
                $("#h5_authorization_time").css('display', 'none');
                $("#protocol").css('display', 'none');
                $("#protocol_content_show").css('display', 'none');
            } else {
                createInput();
                $("#h5_authorization_time").css('display', 'block');
                $("#protocol").css('display', 'block');

                // 是否展示协议
                $('[name=protocol_default]:checked').click();
            }
        });
    }

    // 初始化api_key && api
    function genApiKeyAndSecret() {
        creat_hash('input_appsecret', 64);
        creat_hash('input_appid', 32);
    }

    // 紧急联系人关系删减事件
    function disappearElement(that) {
        $(that).parent().remove();
    }

    // api ui 接入对紧急联系人的影响
    $(":radio[name=source]").change(function () {
        if ($(this).val() === 'api') {
            $("#actual_ralationship").css('display', 'none');
        } else {
            $("#actual_ralationship").css('display', 'none');
        }
    });

    // 联系人页面的触发紧急联系人是否显示
    $(":radio[name=contactor_page]").change(function () {

        // radio的选中与否都是初始化的时候定下的
        if ($(this).val() === 'Y') {
            $("#actual_ralationship").css('display', 'block');
        } else {
            $("#actual_ralationship").css('display', 'none');
        }
    });

    function radioControlRelation(that) {

        if ($(that).val() === 'Y') {
            $("#actual_ralationship").css('display', 'block');
        } else {
            $("#actual_ralationship").css('display', 'none');
        }
    }

    function creat_hash(id, length) {
        DHB.ajax({
            url: "{:U('Home/Tool/hashid')}",
            type: 'get',
            data: {"length": length},
            success: function (r) {
                $("#" + id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length) {
        if (confirm('修改appid,appsecret会使当前账号的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        }
    }

    function createInput() {
        var htm = '\
            <div class="form-group ui-dist"> \
                <label class="col-sm-2 control-label">授权期限</label> \
                <div class="col-sm-4"> \
                    <input type="date" class="form-control" name="token_due_date" value=""> \
                </div> \
                <label class="col-sm-2 control-label">联系人页面</label> \
                <div class="col-sm-4"> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" onclick="radioControlRelation($(this))" value="N" checked> \
                        不接入 \
                   </label> \
                   <label class="radio-inline"> \
                        <input type="radio" name="contactor_page" onclick="radioControlRelation($(this))" value="Y">接入 \
                   </label> \
               </div> \
            </div> \
';
        $("div.access_sel").after(htm);
    }

    // protocol preview
    function preview(that) {
        var protocol = $('#protocol_content').val();
        var preview_url = $(that).attr('preview_url');
        $('#preview').html('<form id="preview_form" target="NewWindow" action="' + preview_url + '" method="POST" style="display:none;"><textarea name="protocol_content">' + protocol + '</textarea></form>');
        window.open("", 'NewWindow');
        $('#preview_form').submit();
    }
</script>
