<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <!-- Include Required Prerequisites -->
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css" />

    <!-- Include Date Range Picker -->
    <script type="text/javascript" src="//cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.js"></script>
    <script src="__JS__echarts.common.min.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.css" />
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />

<div class="panel panel-default">
    <div class="panel-body">
        <form class="form-horizontal" id="channelForm" action="{:U('')}" method="get">
            <!--时间插件开始-->
            <div class="form-group">
                <label  class="col-sm-2 control-label">开始时间</label>
                <div class="col-sm-5">
                    <if condition=" isset($_GET['begin']) and !empty($_GET['begin'])">
                        <input type="date" class="form-control" name="begin" value="{:date('Y-m-d',$_GET['begin'])}"/>
                        <else/>
                        <input type="date" name="begin"  class="form-control"  value=""/>
                    </if>

                </div>
            </div>
            <div class="form-group">
                <label  class="col-sm-2 control-label">结束时间</label>
                <div class="col-sm-5">
                    <if condition=" isset($_GET['begin']) and !empty($_GET['begin'])">
                        <input type="date" name="end" class="form-control" value="{:date('Y-m-d',$_GET['end'])}"/>
                        <else/>
                        <input type="date" name="end" class="form-control" value=""/>
                    </if>
                </div>
            </div>
            <div class="form-group"  >
                <label class="col-sm-2 control-label">选择客户</label>
                <!--<label class="col-sm-1 control-label">选择客户</label>-->
                <div class="col-sm-5" >
                    <select class="form-control" name="cid" id="suser" >
                        <option value="" >选择客户</option>
                        <foreach name="list.user_info"  item="vo">
                            <option value="<?php  echo $vo['id']?>" ><?php  echo $vo['developer']?></option>

                        </foreach>

                    </select>
                </div>
            </div>
            <div class="form-group" data-toggle="buttons" >
                <label class="col-sm-2 control-label">选择项目</label>
                <label class="btn btn-primary active">
                    <input type="radio" name="search_type" id="option1" value="0" checked  > 总览
                </label>
                <label class="btn btn-primary ">
                    <input type="radio" name="search_type" id="option2" value="1"> 邦秒爬
                </label>
                <label class="btn btn-primary">
                    <input type="radio" name="search_type" id="option3"  value="2" > 邦秒配
                </label>
            </div>
            <if condition=" isset($_GET['search_type']) and  in_array($_GET['search_type'],['3'])">
                <div class="form-group" >
                    <label class="col-sm-2 control-label">返回列表</label>
                    <label class="btn ">
                        <a href="{:U('Home/CrawlerStat/index',['search_type'=>1])}">返回客户列表</a>
                    </label>
                </div>
                <elseif condition="isset($_GET['search_type']) and  in_array($_GET['search_type'],['4'])"/>
                <div class="form-group" >
                    <label class="col-sm-2 control-label">返回列表</label>
                    <label class="btn ">
                        <a href="{:U('Home/CrawlerStat/index',['search_type'=>2])}">返回客户列表</a>
                    </label>
                </div>
            </if>
            <input type="hidden" name="search_first" value="{$_GET['search_type']}">
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-10">
                    <button type="submit" style="width: 80%" class="btn btn-primary  btn-block">查询</button>

                </div>
            </div>
        </form>

    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <?php foreach($list['header'] as $value): ?>
                <th >{$value}</th>
                <?php endforeach ?>
            </tr>
            </thead>
            <tbody>
            <tr>

                <if condition=" isset($_GET['search_type']) and  in_array($_GET['search_type'],['2','1'])">
                    <?php foreach($list['total'] as $vo):?>
                    <td class="touchC">{$vo} </td>
                    <?php endforeach ?>
                    <else />

                </if>


            </tr>
            <?php foreach ($list['info'] as $value): ?>


            <tr>
                <?php if(is_array($value)){?>
                <?php
foreach($list['index'] as $key=>$val)
                {
                ?>
                <?php if($key == 0){
                $index1 = $list['detail'][0];
                $value2 = $list['detail'][1];
                $value1 = $value[$index1];
                $userDef = isset($list['detail'][2]) && !empty($list['detail'][2])?"&{$list['detail'][2]}=".$value[$list['detail'][2]]:'';
                $url = "?$index1=$value1&search_type=$value2".$userDef;
?>
                <td class="touchC">
                    <if condition=" isset($_GET['search_type']) and  in_array($_GET['search_type'],['2','1'])">
                            <a href="{:U('index'.$url)}">
                            <?php echo  $value[$val];?></a>
                        <else />
                        <?php echo  $value[$val];?>

                    </if>
                </td>
                <?php }else{ ?><td>  <?php echo  $value[$val];?></td> <?php }?>
                <?php
}
}
?>

            </tr>
            <?php endforeach ?>

            </tbody>

        </table>
    </div>

    <if condition="!empty($page) ">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
    <input type="hidden" id="search" value="<?php echo isset($_GET['search_type'])?$_GET['search_type']:0 ?>"/>
    <input type="hidden" id="login_status" value="{$list['lChart']['info']['login_status']}"/>
    <input type="hidden" id="total" value="{$list['lChart']['info']['total']}"/>
    <input type="hidden" id="status_report" value="{$list['lChart']['info']['status_report']}"/>
    <input type="hidden" id="status" value="{$list['lChart']['info']['status']}"/>
    <input type="hidden" id="date" value="{$list['lChart']['date']}"/>

    <!---------                 ---------------折线图部分 开始-----------------     -->

    <if condition=" !isset($_GET['search_type']) ||  in_array($_GET['search_type'],['0'])">
        <div  class="row">
            <div class="col-md-6" >
                <div id="main" style="width: 600px;height:400px;"></div>
            </div>
            <div  class="col-md-6">
                <div id="main2" style="width: 600px;height:400px;"></div>
            </div>
        </div>
        <else />

    </if>
</div>
</div>





<!--  js开始-->
<script type="text/javascript">
    $(function() {
        function getQueryString(name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
            var r = window.location.search.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            }
            return null;
        }


        //选择用户部分
        var cid = getQueryString('cid');
        console.log(cid);
        if(cid){
            $("#suser ").val(cid);

        }

        //页面加载则执行的函数  加载客户的信息
        $(document).ready(function() {
            var search = $("#search").val();

            if(search == 4  || search==2) {
                $("#option1").attr('checked', false);
                $("#option2").attr('checked', false);
                $("#option3").attr('checked', true);
                $("#option3").parent().attr('class', 'btn btn-primary active');
                $("#option1").parent().attr('class', 'btn btn-primary');
                $("#option2").parent().attr('class', 'btn btn-primary');
            }

            if(search == 3 || search==1){
                $("#option1").attr('checked',false);
                $("#option3").attr('checked',false);
                $("#option2").attr('checked',true);
                $("#option2").parent().attr('class','btn btn-primary active');
                $("#option1").parent().attr('class','btn btn-primary');
                $("#option3").parent().attr('class','btn btn-primary');
            }


            /*折线图部分*/
            //获取需要的信息去展开折线图
            var dateV = $("#date").val();
            var statusV = $("#status").val();
            var loginV = $("#login_status").val();
            var totalV = $("#total").val();
            var reportV = $("#status_report").val();

            var myChart = echarts.init(document.getElementById('main'));

            var option = {
                title: {
                    text: '邦秒爬',
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data:['授权成功量','爬取成功量','报告生成量']
                },
                toolbox: {
                    show: false,
                    feature: {
                        saveAsImage: {show: true},
                        magicType: {show: false, type: ['stack', 'tiled']},
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dateV.split(',')
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    name: '授权成功量',
                    type: 'line',
                    smooth: true,
                    data: loginV.split(',')
                },
                    {
                        name: '爬取成功量',
                        type: 'line',
                        smooth: true,
                        data: statusV.split(',')
                    },
                    {
                        name: '报告生成量',
                        type: 'line',
                        smooth: true,
                        data: reportV.split(',')
                    }]
            };
            myChart.setOption(option);


            /*邦秒配*/
            var myChart2 = echarts.init(document.getElementById('main2'));

            var option = {
                title: {
                    text: '邦秒配',
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data:['查询量']
                },
                toolbox: {
                    show: false,
                    feature: {
                        magicType: {show: true, type: ['stack', 'tiled']},
                        saveAsImage: {show: true}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dateV.split(',')
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    name: '查询量',
                    type: 'line',
                    smooth: true,
                    data: totalV.split(',')
                }]
            };
            myChart2.setOption(option);
        });

    });

</script>
</body>
</html>