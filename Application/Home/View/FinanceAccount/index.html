<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        div.form-group {
            margin-left: 10px;
        }
        td span{
            margin: 0 1px ;
        }
        .time_span span {
            display: block;
        }
        td {
            word-wrap: break-word;
            text-align: center;
        }

        .textarea {
            width: 100%;
            min-height: 120px;
            max-height: 300px;
            _height: 120px;
            margin-left: auto;
            margin-right: auto;
            padding: 3px;
            outline: 0;
            /*border: 1px solid #a0b3d6;*/
            font-size: 12px;
            line-height: 24px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            /*border-color: rgba(82, 168, 236, 0.8);*/
            /*box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 8px rgba(82, 168, 236, 0.6);*/
        }
        .row-first {
            margin-bottom: 10px;
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
        <div id="breadcrumb_search_box">
            <a href="/Home/FinanceAccount/add" class="btn btn-primary btn-sm">添加客户</a>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-body">
            <form action="/Home/FinanceAccount/index" method="get" class="form-inline">
                <div class="row-first">
                <div class="form-group">
                    <label for="id">ID：</label>
                    <input type="text" name="id" id="id" class="form-control"
                           value="<?= (isset($request['id']) && $request['id']) ? $request['id'] : '';?>">
                </div>
                <div class="form-group">
                    <label for="status">状态：</label>
                    <select name="status" id="status" class="form-control">
                        <option value="">状态</option>
                        <option value="1"
                        <?= (isset($request['status']) && $request['status']==1) ? 'selected' : '' ?>>可用</option>
                        <option value="2"
                        <?= (isset($request['status']) && $request['status']==2) ? 'selected' : '' ?>>禁用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="name">客户名称：</label>
                    <select name="name" id="name" class="form-control">
                        <?php if (isset($request['name']) && $request['name']) {?>
                        <option value="<?= $request['name']; ?>" selected><?= $request['name']; ?></option>
                        <?php } ?>
                        <option value="">客户名称</option>
                        <?php foreach($list_account_for_select2 as $account) {?>
                        <option value="<?= $account['name']; ?>"><?= $account['name']; ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="id">公司名称：</label>
                    <input type="text" name="company" id="company" class="form-control"
                           value="<?= (isset($request['company']) && $request['company']) ? $request['company'] : '';?>">
                </div>
                </div>

                <div class="row-second">
                <div class="form-group">
                    <label for="operator">运营跟进人：</label>
                    <input type="text" name="operator" id="operator" class="form-control"
                           value="<?= (isset($request['operator']) && $request['operator']) ? $request['operator'] : '';?>">
                </div>
                <div class="form-group">
                    <label for="salesman">商务跟进人：</label>
                    <input type="text" name="salesman" id="salesman" class="form-control"
                           value="<?= (isset($request['salesman']) && $request['salesman']) ? $request['salesman'] : '';?>">
                </div>
                <div class="form-group">
                    <label for="submit_btn"></label>
                    <input type="submit" id="submit_btn" class="btn btn-primary btn-sm" value="查询">
                </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">客户列表</h3></div>
        <table class="table table-hover table-bordered" style="table-layout:fixed">
            <thead>
            <tr>
                <th>ID</th>
                <th>客户名称</th>
                <th>公司名称</th>
                <th>邮箱</th>
                <th>状态</th>
                <th>邦秒爬产品</th>
                <th>邦秒配产品</th>
                <th>催收分产品</th>
                <th>TIME</th>
                <th>操作人</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list_account as $account){ ?>
            <tr>
                <td><?= $account['id']; ?></td>
                <td><?= $account['name']; ?></td>
                <td><?= $account['company']; ?></td>
                <td><?= $account['email']; ?></td>
                <td><?= ($account['status'] == 1) ? '可用' : '禁用' ; ?></td>
                <td>
                    <label for="crawler"></label>
                    <div class="textarea" id="crawler">
                        <ul class="list-group">
                            <?php foreach ($account['crawler'] as $crawler) {?>
                            <li class="list-item">
                                <span>ID: <?= $crawler['id'] ?></span> <span><?= $crawler['developer'] ?></span>
                            </li>
                            <?php } ?>
                        </ul>
                    </div>
                </td>
                <td>
                    <label for="matching"></label>
                    <div class="textarea" id="matching" contenteditable="true">
                        <ul class="list-group">
                            <?php foreach ($account['matching'] as $matching) {?>
                            <li class="list-item">
                                <span>ID: <?= $matching['id'] ?></span> <span><?= $matching['owner'] ?></span>
                            </li>
                            <?php } ?>
                        </ul>
                    </div>
                </td>
                <td>
                    <label for="cuishou"></label>
                    <div class="textarea" id="cuishou" contenteditable="true">
                        <ul class="list-group">
                            <?php foreach ($account['cuishou'] as $cuishou) {?>
                            <li class="list-item">
                                <span>ID: <?= $cuishou['id'] ?></span> <span><?= $cuishou['developer'] ?></span>
                            </li>
                            <?php } ?>
                        </ul>
                    </div>
                </td>
                <td class="time_span">
                    <span>创建: <?= date('Y-m-d H:i:s', $account['created_at']) ?></span>
                    <span>更新: <?= date('Y-m-d H:i:s', $account['updated_at']) ?></span>
                </td>
                <td><?= $account['admin']; ?></td>
                <td>
                    <a href="/Home/FinanceAccount/edit?id=<?= $account['id'];?>" class="btn btn-info btn-sm">编辑</a>
                </td>
            </tr>

            <?php } ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            {$obj_page->show()}
        </ul>
    </nav>
</div>
<script type="text/javascript">
    $(function () {
        $("#name").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width: '200px'
        });

    });
</script>
</body>
</html>
