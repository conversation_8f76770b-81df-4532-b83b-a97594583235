<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            height: 100px;
            width: 100%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container" id="account_app">
    <dialog_template></dialog_template>
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
        <div id="breadcrumb_search_box">
            <a href="/Home/FinanceAccount/index" class="btn btn-primary btn-sm">返回客户列表</a>
        </div>
    </div>
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel panel-default">
                <div class="panel-body">
                    <form action="/Home/FinanceAccount/add" method="post" class="form-horizontal" id="form_account">
                        <div class="form-group">
                            <label for="name" class="control-label col-md-3">客户名称：</label>
                            <div class="col-md-4">
                                <input type="text" name="name" id="name" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="company" class="control-label col-md-3">公司名称：</label>
                            <div class="col-md-4">
                                <input type="text" name="company" id="company" class="form-control" value="">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email" class="control-label col-md-3">邮箱：</label>
                            <div class="col-md-4">
                                <input type="text" name="email" class="form-control" id="email">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="status_radio1" class="control-label col-md-3">状态：</label>
                            <label class="radio-inline">
                                <input type="radio" name="status" id="status_radio1" value="1" checked>可用
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="status" id="status_radio2" value="2">禁用
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="operator" class="control-label col-md-3">运营跟进人：</label>
                            <div class="col-md-4">
                                <input type="text" name="operator" id="operator" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="salesman" class="control-label col-md-3">商务跟进人：</label>
                            <div class="col-md-4">
                                <input type="text" name="salesman" id="salesman" class="form-control" value="">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="type" class="control-label col-md-3">客户类型：</label>
                            <div class="col-md-4">
                                <select name="type" id="type" class="form-control">
                                    <option value="">暂不选择</option>
                                    <option value="1">数据公司</option>
                                    <option value="2">风控系统</option>
                                    <option value="3">综合类</option>
                                    <option value="4">p2p</option>
                                    <option value="5">现金分期</option>
                                    <option value="6">消费金融（3C)</option>
                                    <option value="7">农商行</option>
                                    <option value="8">汽车金融</option>
                                    <option value="9">消费金融</option>
                                    <option value="10">其他</option>
                                </select>
                            </div>
                        </div>

                        <div class="pull-right">
                            <input type="submit" class="btn btn-primary btn-sm" value="添加">
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    new Vue({
        el : '#account_app'
    });

    $(function () {
        // 表单提交事件
        formSubmit();

    });

    // 表单提交事件
    function formSubmit() {

        $('#form_account').submit(function(){
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request = $(that).attr('action');
        var url_redirect = '/Home/FinanceAccount/index';

        $.post(url_request, data_request).success(function(response){
            if (response.status !== 'success') {
                modalExport(response.info);
                return '';
            }
            alert( '创建客户成功');
            window.location.href = url_redirect;
        }).error(function(response){
            modalExport('创建用户出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var name = $('#name').val().trim();
        var email = $('#email').val().trim();
        if (!name) {
            modalExport('请填写客户名');
            return false;
        }
        if (name.length < 2) {
            modalExport('客户的名字的长度需要不可以小于2');
            return false;
        }
        if (!email) {
            modalExport('请填写邮箱');
            return false;
        }
        return true;
    }
</script>
</body>
</html>
