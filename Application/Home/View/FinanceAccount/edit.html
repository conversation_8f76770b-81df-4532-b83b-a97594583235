<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .textarea {
            width: 100%;
            min-height: 20px;
            max-height: 150px;
            _height: 120px;
            margin-left: auto;
            margin-right: auto;
            padding: 3px;
            outline: 0;
            border: 1px solid #a0b3d6;
            font-size: 12px;
            line-height: 24px;
            word-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            border-color: rgba(82, 168, 236, 0.8);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 8px rgba(82, 168, 236, 0.6);
        }

        .checkbox span {
            margin-right: 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container" id="update_account_app">
    <dialog_template></dialog_template>
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
        <div id="breadcrumb_search_box">
            <a href="/Home/FinanceAccount/index" class="btn btn-primary btn-sm">返回客户列表</a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="panel panel-default">
                <div class="panel-body">
                    <form action="/Home/FinanceAccount/edit" method="post" class="form-horizontal" id="form_account">

                        <input type="hidden" name="id" id="id" value="<?= $account['id'] ?>">
                        <div class="form-group">
                            <label for="name" class="control-label col-md-2">客户名称：</label>
                            <div class="col-md-4">
                                <input type="text" name="name" id="name" class="form-control"
                                       value="<?= $account['name'] ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="company" class="control-label col-md-2">公司名称：</label>
                            <div class="col-md-4">
                                <input type="text" name="company" id="company" class="form-control"
                                       value="<?= $account['company'] ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email" class="control-label col-md-2">邮箱：</label>
                            <div class="col-md-4">
                                <input type="text" name="email" class="form-control" id="email"
                                       value="<?= $account['email'] ?>">
                            </div>
                            <div class="col-md-4">
                                <input type="button" onclick="DHB.INFO.set('{:U('resetPwd',array('id'=>$account['id']))}','提示')" class="btn btn-primary btn-sm" value="重置密码">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="status_radio1" class="control-label col-md-2">状态：</label>
                            <div class="col-md-4">
                                <label class="radio-inline">
                                    <input type="radio" name="status" id="status_radio1"
                                           value="1" <?= $account['status'] == 1 ? 'checked' : ''; ?>>可用
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="status" id="status_radio2"
                                           value="2" <?= $account['status'] != 1 ? 'checked' : ''; ?>>禁用
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="operator" class="control-label col-md-2">运营跟进人：</label>
                            <div class="col-md-4">
                                <input type="text" name="operator" id="operator" class="form-control" value="<?= $account['operator'] ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="salesman" class="control-label col-md-2">商务跟进人：</label>
                            <div class="col-md-4">
                                <input type="text" name="salesman" id="salesman" class="form-control" value="<?= $account['salesman'] ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="type" class="control-label col-md-2">客户类型：</label>
                            <div class="col-md-4">
                                <select name="type" id="type" class="form-control">
                                    <option value="">暂不选择</option>
                                    <option value="1" <?=  ($account['type'] === '1') ? 'selected' : '';?>>数据公司</option>
                                    <option value="2" <?=  ($account['type'] === '2') ? 'selected' : '';?>>风控系统</option>
                                    <option value="3" <?=  ($account['type'] === '3') ? 'selected' : '';?>>综合类</option>
                                    <option value="4" <?=  ($account['type'] === '4') ? 'selected' : '';?>>p2p</option>
                                    <option value="5" <?=  ($account['type'] === '5') ? 'selected' : '';?>>现金分期</option>
                                    <option value="6" <?=  ($account['type'] === '6') ? 'selected' : '';?>>消费金融（3C)</option>
                                    <option value="7" <?=  ($account['type'] === '7') ? 'selected' : '';?>>农商行</option>
                                    <option value="8" <?=  ($account['type'] === '8') ? 'selected' : '';?>>汽车金融</option>
                                    <option value="9" <?=  ($account['type'] === '9') ? 'selected' : '';?>>消费金融</option>
                                    <option value="10" <?=  ($account['type'] === '10') ? 'selected' : '';?>>其他</option>
                                </select>
                            </div>
                        </div>


                        <div class="pull-right">
                            <ul class="list-inline">
                                <li><input type="submit" class="btn btn-primary btn-sm" value="更新"></li>
                                <li><a href="http://php.net/manual/zh/function.strpos.php" onclick="return goBack()" class="btn btn-info btn-sm">返回</a></li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    new Vue({
        el : '#update_account_app'
    });
    $(function () {
        // 表单提交事件
        formSubmit();

    });

    // 返回上级菜单
    function goBack() {
        event.preventDefault();
        window.history.back();
    }

    // 表单提交事件
    function formSubmit() {

        $('#form_account').submit(function () {
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request = $(that).attr('action');
        var url_redirect = '/Home/FinanceAccount/index';

        $.post(url_request, data_request).success(function (response) {
            alert(response.info);
            if (response.status === 'success') {
                window.location.href = url_redirect;
            }
            console.log(response.info);
        }).error(function (response) {
            modalExport('更新用户出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var name = $('#name').val().trim();
        var email = $('#email').val().trim();
        var id = $('#id').val().trim();

        if (!id) {
            modalExport('网络故障，请刷新后再试');
            return false;
        }
        if (!name) {
            modalExport('请填写客户名');
            return false;
        }
        if (name.length < 2) {
            modalExport('客户的名字的长度需要不可以小于2');
            return false;
        }
        if (!email) {
            modalExport('请填写邮箱');
            return false;
        }
        return true;
    }

</script>
</body>
</html>
