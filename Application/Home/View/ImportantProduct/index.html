<html>
<include file="Common@Public/head"/>
<script src="__JS__/vue/vue.js"> </script>
<script src="__JS__/vue/index.js"> </script>
<script src="__JS__/vue/axios.min.js"> </script>
<link rel="stylesheet" href="__JS__/vue/index.css">
<style>
    ::-webkit-scrollbar {
        width: 1px;
        height: 1px;
    }
    /* // 滚动条的滑块 */
    ::-webkit-scrollbar-thumb {
        background-color: #a1a3a9;
        border-radius: 0px;
    }

    element.style {
    }
    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
        font-size: 10px;
    }
 .mini{
     margin-top: 5;
 }
</style>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="app">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <span class="demonstration">选择时间：</span>
        <el-date-picker
                v-model="formInline.time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00','23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="changeTime()"
        >
        </el-date-picker>

        <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
        <el-form-item>
            <el-button class="mini" size="mini" type="primary" @click="setTime(-10)" round>近十分钟</el-button>
        </el-form-item>
        <el-form-item>
            <el-button  class="mini" size="mini" type="primary" @click="setTime(-20)" round>近二十分钟</el-button>
        </el-form-item>
        <el-form-item>
            <el-button class="mini" size="mini" type="primary" @click="setTime(-30)" round>近半小时</el-button>
        </el-form-item>
        <el-form-item>
            <el-button class="mini" size="mini" type="warning" @click="setTime(-1)" round>前一天</el-button>
        </el-form-item>
        <el-form-item>
            <el-button class="mini" size="mini" type="warning" @click="setTime(1)" round>后一天</el-button>
        </el-form-item>
        <el-form-item>
            <el-button class="mini" size="mini" type="warning" @click="setTime(0)" round>今天</el-button>
        </el-form-item>
        {{testtext}}
        <el-switch
                v-model="testData"
                active-value="true"
                inactive-value="false"
                @change="switchChange()"
         >
        </el-switch>
    </el-form>

    <el-table
            v-loading="loading"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            border
            :data="tableData"
            :cell-style="setBackground"
            style="width: 100%">
        <el-table-column
                prop="name"
                label="产品"
                width="180">
        </el-table-column>
        <el-table-column
                prop="total_amount"
                label="调用量(昨日)"
                >
        </el-table-column>
        <el-table-column
                prop="check_rate"
                label="查的率(昨日)">
        </el-table-column>
        <el-table-column
                prop="avg_time"
                label="平均响应时间(毫秒)/(昨日)"
               >
        </el-table-column>
        <el-table-column
                prop="beijing_amount"
                label="北京机房数(昨日)"
                >
        </el-table-column>
        <el-table-column
                prop="tp90"
                label="tp90(毫秒)/(昨日)"
                >
        </el-table-column>
        <el-table-column
                prop="tp95"
                label="tp95(毫秒)/(昨日)"
                >
        </el-table-column>
        <el-table-column
                prop="tp99"
                label="tp99(毫秒)/(昨日)"
                >
        </el-table-column>
<!--        <el-table-column-->
<!--                prop="more_one_rate"-->
<!--                label="超1s占比/数量"-->
<!--                width="180">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--                prop="between_middle_rate"-->
<!--                label="0.8-0.5占比/数量"-->
<!--                width="more_one_rate">-->
<!--        </el-table-column>-->
<!--        <el-table-column-->
<!--                prop="between_low_rate"-->
<!--                label="0.5-0.3占比/数量"-->
<!--                width="more_one_rate">-->
<!--        </el-table-column>-->
        <el-table-column
                prop="abnorma_num_rate"
                label="异常占比/数量"
                >
        </el-table-column>
    </el-table>
</div>
</body>
</html>
<script>
    var Main = {
        data() {
            return {
                optiondataurl:'{$Think.config.FINANCE_MANAGE_API_DOMAIN}/importantproductstatistic/getImportantProductInfo',
                formInline: {
                    time:'',
                },
                tableData:[],
                loading:false,
                backgroud:[
                ],
                update:true,
                testData:false,
                testtext:'包含测试',
                show:true,
                timesteamp:'allday',
                times:'allday'
            }
        },
        created(){
            this.setInitTime();
            this.onSubmit();
        },
        methods: {
            onSubmit:function () {
                var time1 = this.formInline.time[0];
                var time2 = this.formInline.time[1];
                var test_data = this.testData;
                this.loading = true;
                _this = this;
                this.gettabledataurl = this.optiondataurl + '?start_time=' +time1 + '&end_time=' + time2 +"&test_data=" + test_data;
                axios.get(this.gettabledataurl).then(function (response) {
                       _this.tableData = response.data.data.data;
                       _this.backgroud = response.data.data.backgroud;
                       _this.loading = false;
                })

            },
            setInitTime:function () {
                var todayYear = (new Date()).getFullYear()
                var todayMonth = (new Date()).getMonth() + 1;
                var todayDay = (new Date()).getDate();
                var h = (new Date()).getHours() < 10 ? ('0' + (new Date()).getHours()) : (new Date()).getHours();
                var f = (new Date()).getMinutes() < 10 ? ('0' + (new Date()).getMinutes()) : (new Date()).getMinutes();
                var s = (new Date()).getSeconds() < 10 ? ('0' + (new Date()).getSeconds()) : (new Date()).getSeconds();
                var time1 = todayYear+'-'+todayMonth+'-'+todayDay + " 00:00:00";
                var time2 = todayYear+'-'+todayMonth+'-'+todayDay + " " + h + ":" + f + ":" + s;
                this.formInline.time = [time1,time2];
            },
            setBackground({row, column, rowIndex, columnIndex}) {
                for ($i = 0;$i<this.backgroud.length;$i++){
                    var kkk = this.backgroud[$i];
                    if (columnIndex === kkk[0] && rowIndex  === kkk[1]) {
                        cellStyle = "background:red;color:#FFFAF0";
                        return cellStyle;
                    }
                }
            },
            setTime:function (time) {
                 switch (time) {
                       case -10:
                           this.get_time(-10);
                           break;
                       case -20:
                            this.get_time(-20);
                            break;
                       case -30:
                            this.get_time(-30);
                            break;
                       case -1:
                            this.turn_day(-1);
                            break;
                       case  1:
                            this.turn_day(1);
                            break;
                       case 0:
                           this.setInitTime();
                           break;
                 }
                 if (this.update == true){
                     this.onSubmit();
                 }
            },
             get_time (Minutes) {
                var date=new Date();     //1. js获取当前时间
                var y = date.getFullYear();
                var m = (date.getMonth() + 1) < 10 ? ("0" + (date.getMonth() + 1)) : (date.getMonth() + 1);
                var d = date.getDate() < 10 ? ("0" + date.getDate()) : date.getDate();
                var h = date.getHours() < 10 ? ('0' + date.getHours()) : date.getHours();
                var f = date.getMinutes() < 10 ? ('0' + date.getMinutes()) : date.getMinutes();
                var s = date.getSeconds() < 10 ? ('0' + date.getSeconds()) : date.getSeconds();
                var formatdate1 = y+'-'+m+'-'+d + " " + h + ":" + f + ":" + s;
                var min=date.getMinutes();  //2. 获取当前分钟
                date.setMinutes(min+Minutes);  //3. 设置当前时间
                var y = date.getFullYear();
                var m = (date.getMonth() + 1) < 10 ? ("0" + (date.getMonth() + 1)) : (date.getMonth() + 1);
                var d = date.getDate() < 10 ? ("0" + date.getDate()) : date.getDate();
                var h = date.getHours() < 10 ? ('0' + date.getHours()) : date.getHours();
                var f = date.getMinutes() < 10 ? ('0' + date.getMinutes()) : date.getMinutes();
                var s = date.getSeconds() < 10 ? ('0' + date.getSeconds()) : date.getSeconds();
                var formatdate2 = y+'-'+m+'-'+d + " " + h + ":" + f + ":" + s;
                 this.formInline.time = [formatdate2,formatdate1];
            },
            turn_day(days){
                 now_day = this.formInline.time[0];
                 now_day = now_day.replace(/-/g,'/')
                 //new Date(now_day.getTime() - 3600 *24 *1000);
                 var time_stamp = (new Date(now_day)).getTime() + 24*60*60*1000*days;
                 var now_time_stamp = Date.parse(new Date());
                 if (time_stamp > now_time_stamp){
                     this.$message('不可大于当前时间');
                     this.update = false;
                     return ;
                 }
                this.update = true;
                var todayYear = (new Date(time_stamp)).getFullYear()
                var todayMonth = (new Date(time_stamp)).getMonth() + 1;
                var todayDay = (new Date(time_stamp)).getDate();
                var time1 = todayYear+'-'+todayMonth+'-'+todayDay + " 00:00:00";
                var time2 = todayYear+'-'+todayMonth+'-'+todayDay + " 23:59:59";
                this.formInline.time = [time1,time2];
            },
            switchChange(){
                if (this.testData == 'true'){
                    this.testtext = '不含测试';
                }else{
                    this.testtext = '包含测试';
                }
                this.onSubmit();
            },
            switchChangeTime(){
                console.log(this.timesteamp)
                this.times = this.timesteamp;
            },
        },

    }
    var Ctor = Vue.extend(Main)
    new Ctor().$mount('#app')
</script>