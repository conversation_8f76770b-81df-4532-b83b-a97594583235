<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <style>
        .close_open{
            margin:0 10px 0;
            transform: rotate(180deg);
            display: inline-block;
            cursor:pointer;
            color:#337ab7;
        }
        .table tbody{
            border:none !important;
        }
    </style>
    <link rel="stylesheet" href="//at.alicdn.com/t/font_497737_h0nybddy43t.css">
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container index_search">
    <form class="form-inline" action="{:U('index', ['product_id' => $product_id])}">
        <div class="form-group">
            <label for="bargain_id">合同编号</label>
            <input type="text" value="{$input.bargain_id}" class="form-control" id="bargain_id" name="bargain_id" placeholder="请填写合同编号" maxlength="15" />
        </div>
        <div class="form-group">
            <label for="customer_id">选择客户</label>
            <select class="form-control" id="customer_id" name="customer_id">
                <option value="">选择客户</option>
                {$input.customer_option}
            </select>
        </div>
        <div class="form-group">
            <label for="company">公司名称</label>
            <input type="text" value="{$input.company}" class="form-control" id="company" name="company" placeholder="请填写公司名称" maxlength="50" />
        </div>
        <div class="form-group">
            <label for="product_id">签约产品</label>
            <select class="form-control" id="product_id" name="product_id">
                <option value="">签约产品</option>
                {$input.product_option}
            </select>
        </div>
        <div class="form-group">
            <label for="start_start_date">合同开始日期</label>
            <input class="form-control" value="{$input.start_start_date}" type="date" id="start_start_date" name="start_start_date" />
            -
            <input class="form-control" value="{$input.end_start_date}" type="date" id="end_start_date" name="end_start_date" />
        </div>
        <div class="form-group">
            <label for="start_end_date">合同结束日期</label>
            <input class="form-control" value="{$input.start_end_date}" type="date" id="start_end_date" name="start_end_date" />
            -
            <input class="form-control" value="{$input.end_end_date}" type="date" id="end_end_date" name="end_end_date" />
        </div>
        <button type="submit" class="btn btn-primary">确定</button>
        <a href="{:U('add')}" class="btn btn-success">新增合同</a>
    </form>

</div>
<div class="container index_table">
    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th class="order_field" data-info="bargain_id">
                        合同编号
                        <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                    </th>
                    <th>客户名称</th>
                    <th>公司名称</th>
                    <th class="order_field" data-info="start_date">
                        合同开始时间
                        <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                    </th>
                    <th class="order_field" data-info="end_date">
                        合同结束时间
                        <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                    </th>
                    <th>签约产品</th>
                    <th class="order_field" data-info="money">
                        合同总金额(元)
                        <span class="v-table-sort-icon"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>
                    </th>
                    <th>到款总金额(元)</th>
                    <th>已消耗金额(元)</th>
                    <th>剩余金额(元)</th>
                    <th>创建更新时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <volist name="result.data" id="vo">
                    <tr>
                        <td>{$vo.bargain_id}</td>
                        <td>{$vo.customer_name}</td>
                        <td>{$vo.company}</td>
                        <td>{$vo.start_date}</td>
                        <td>{$vo.end_date}</td>
                        <td>{$vo.product|implode="<br/>",###}</td>
                        <td>{$vo.money}</td>
                        <td>{$vo.remit_money|round=###,2}</td>
                        <td>{$vo.consume_meony|round=###,2}</td>
                        <td>{$vo.balance_money|round=###,2}</td>
                        <td>
                            创建时间：{$vo.create_time|date='Y-m-d H:i',###}
                            <br/>
                            更新时间：{$vo.update_time|date='Y-m-d H:i',###}
                        </td>
                        <td>
                            <nobr><a style="margin:0 5px;" class="btn-link" href="{:U('detail', ['bargain_id' => $vo['bargain_id']])}">查看详细</a></nobr>
                            <nobr><a style="margin:0 5px;" class="btn-link" href="{:U('edit', ['bargain_id' => $vo['bargain_id']])}">编辑</a></nobr>
                            <nobr><a style="margin:0 5px;" class="btn-link" onclick="return confirm('删除后，数据将不可找回，是否确认删除？')" href="{:U('del', ['bargain_id' => $vo['bargain_id']])}">删除</a></nobr>
                        </td>
                    </tr>
                </volist>
            </tbody>
        </table>
    </div>
</div>
<div class="container" style="padding:20px 10px;"><ul class="pagination">
    {$result.render}
</ul></div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript">
$(document).ready(function () {
    $("#customer_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择客户',
        width:'200px'
    });
    $("#product_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择产品',
        width:'200px'
    });
    iniOrder();
    $(".order_field").click(function () {
        order($(this).attr('data-info'));
    })
});
function iniOrder()
{
    let params = GetRequest();
    let field = params.order;
    console.log(field);
    if (field) {
        let asc = params.asc;
        if (asc==1) {
            $('.order_field[data-info="' + field + '"]').find('.v-icon-up-dir').addClass('checked');
        } else {
            $('.order_field[data-info="' + field + '"]').find('.v-icon-down-dir').addClass('checked');
        }
    } else {
        $('.order_field[data-info="total_nums"]').find('.v-icon-down-dir').addClass('checked');
    }
}
function order(field)
{
    let params = GetRequest();
    if (params.asc==1) {
        params.asc = 0;
    } else {
        params.asc = 1;
    }
    params.order = field;
    let href = "{:U('index')}?";
    $.each(params, function(i, n) {
        href += (i + '=' + n + '&');
    });
    location.href = href.substr(0, href.length-1);
}
function GetRequest() {
    var url = location.search;
    var theRequest = new Object();
    if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        strs = str.split("&");
        for(var i = 0; i < strs.length; i ++) {
            theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
        }
    }
    return theRequest;
}
</script>
