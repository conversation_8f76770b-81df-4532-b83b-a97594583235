<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>

    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <button class="btn btn-primary" id="back">返回列表</button>
</div>
<div class="container add_form">
    <form class="form-horizontal" id="form" method="post">
        <div class="form-group">
            <label class="col-sm-2 control-label">
                合同编号：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$bargain_id}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                客户名称：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$customer_name}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                公司名称：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$company}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                签约产品：
            </label>
            <div class="col-sm-10">
                <div class="form-control" readonly disabled style="display:block;height:auto;min-height:60px;">
                    <volist name="product" id="vo">
                        {$vo}（{$key}）<br/>
                    </volist>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                合同开始时间：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$start_date}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                合同截止时间：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$end_date}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                合同金额：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$money}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                到款金额：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$remit_money|round=###,2}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                消耗金额：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$consume_money|round=###,2}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                剩余金额：
            </label>
            <div class="col-sm-10">
                <input type="text" class="form-control" readonly disabled value="{$balance_money|round=###,2}" />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                备注：
            </label>
            <div class="col-sm-10">
                <div class="form-control" readonly disabled style="display:block;height:auto;min-height:60px;">
                    {$remark}
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                计费详细：
            </label>
            <div class="col-sm-10">
                <span style="color:#c9302c;line-height:36px;font-size:14px;">详见表格内容</span>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-12 table-responsive">
                <table class="table table-hover table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>计费配置ID</th>
                            <th>签约产品</th>
                            <th>计费类型</th>
                            <th>计费开始时间</th>
                            <th>单价</th>
                            <th>总消耗量</th>
                            <th>总消耗金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <volist name="fee_config_money" id="vo">
                            <tr>
                                <td style="overflow: hidden;">{$vo.section_id}</td>
                                <td>{$vo.product_name}</td>
                                <td>{$vo.fee_type}</td>
                                <td>{$vo.start_date}</td>
                                <td>{$vo.price}</td>
                                <td>{$vo.total}</td>
                                <td>{$vo.money|round=###,2}</td>
                            </tr>
                        </volist>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__ajaxform.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript">
    $(function () {
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width:'100%'
        });
        $("#customer_id").change(function () {
            $.ajax({
                url : "{:U('add')}",
                type : 'POST',
                data : {
                    type : 'cut_customer',
                    customer_id : $(this).val()
                },
                success : function (res) {
                    console.log(res);
                    if (res.status==0) {
                        $("#company").val(res.data.company);
                        setProductOption(res.data.product);
                    }
                }
            })
        });
        $("#submit").click(function () {
            $.ajax({
                url : "{:U('add')}",
                type : 'post',
                data : $("#form").serialize(),
                success : function (res) {
                    if (res.status==0) {
                        alert('保存成功');
                        location.href = "{:U('index')}";
                    } else {
                        alert(res.data.message);
                    }
                }
            });
        });
    });
    function setProductOption(product) {
        var option = '';
        console.log(product);
        for (var key in product) {
            option += "<option value='"+ key +"'>" + product[key] + "</option>";
        }
        console.log(option);
        $("#product_id").html(option);
        $("#product_id").selectpicker('refresh');
    }
</script>