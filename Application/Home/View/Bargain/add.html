<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style></style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container add_form">
    <form class="form-horizontal" id="form" method="post">
        <div class="form-group">
            <label for="bargain_id" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                合同编号：
            </label>
            <div class="col-sm-10">
                <input type="text" name="bargain_id" class="form-control" id="bargain_id" placeholder="请填写合同编号" maxlength="15" value="">
            </div>
        </div>
        <div class="form-group">
            <label for="customer_id" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                客户名称：
            </label>
            <div class="col-sm-10">
                <select name="customer_id" id="customer_id" class="form-control">
                    <option value="">选择客户</option>
                    {$customer_option}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="company" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                公司名称：
            </label>
            <div class="col-sm-10">
                <input type="text" name="company" class="form-control" id="company" placeholder="请填写公司名称" maxlength="50">
            </div>
        </div>
        <div class="form-group">
            <label for="product_id" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                签约产品：
            </label>
            <div class="col-sm-10">
                <select name="product_id[]" id="product_id" class="form-control selectpicker" data-live-search="true" data-actions-box="true" multiple>
                    {$product_option}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="money" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                合同金额：
            </label>
            <div class="col-sm-10">
                <input type="text" name="money" class="form-control" id="money" placeholder="请填写合同金额" maxlength="13">
            </div>
        </div>
        <div class="form-group">
            <label for="start_date" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                合同开始时间：
            </label>
            <div class="col-sm-10">
                <input type="date" name="start_date" class="form-control" id="start_date" value="{:date('Y-m-d')}" placeholder="请填写合同开始时间">
            </div>
        </div>
        <div class="form-group">
            <label for="end_date" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                合同截止时间：
            </label>
            <div class="col-sm-10">
                <input type="date" name="end_date" class="form-control" value="{:date('Y-m-d', strtotime('+1 years'))}" id="end_date" placeholder="请填写合同开始时间">
            </div>
        </div>
        <div class="form-group">
            <label for="remark" class="col-sm-2 control-label">
                备注：
            </label>
            <div class="col-sm-10">
                <textarea name="remark" id="remark" class="form-control" cols="30" rows="10"></textarea>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="button" id="submit" class="btn btn-warning">保存</button>
                <a href="{:U('index')}"><button type="button" class="btn btn-primary">返回</button></a>
            </div>
        </div>
    </form>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__ajaxform.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript">
    $(function () {
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width:'100%'
        });
        $("#customer_id").change(function () {
            $.ajax({
                url : "{:U('add')}",
                type : 'POST',
                data : {
                    type : 'cut_customer',
                    customer_id : $(this).val()
                },
                success : function (res) {
                    console.log(res);
                    if (res.status==0) {
                        $("#company").val(res.data.company);
                    }
                }
            })
        });
        $("#submit").click(function () {
            $.ajax({
                url : "{:U('add')}",
                type : 'post',
                data : $("#form").serialize(),
                success : function (res) {
                    if (res.status==0) {
                        alert('保存成功');
                        location.href = "{:U('index')}";
                    } else {
                        alert(res.data.message);
                    }
                }
            });
        });
    });
    setProductOption();
    function setProductOption() {
        $("#product_id").selectpicker('refresh');
    }
</script>