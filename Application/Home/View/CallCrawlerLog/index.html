<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <script src="__JS__jquery.fileDownload.js"></script>
    <style>
        .row_first {
            margin-bottom: 10px;
        }

        .row_first label {
            margin-left: 10px;
        }

        .row-second {
            margin-bottom: 10px;
        }

        .row-third {
            margin-bottom: 0;
            margin-left: 10px;
        }

        .row-second label {
            margin-left: 10px;
        }

        div.pull-right button {
            margin-right: 10px;
        }


    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="panel-heading">
    <div class="row">
        <div class="container">
            <div class="panel-body">
                <form class="form-inline" action="{:U('')}" method="get" id="form_log">
                    <div class="row_first">
                        <div class="form-group">
                            <label class="control-label">开始时间：</label>
                            <input type="date" name="begin" id="time_begin" class="form-control"
                                   value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                        </div>
                        <div class="form-group">
                            <label class="control-label">结束时间：</label>
                            <input type="date" name="end" id="time_end" class="form-control"
                                   value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                        </div>

                        <div class="form-group pull-right">
                            <?php if($can_access) {?>
                            <button type="button" id="clear_cache_time" class="btn btn-sm btn-danger">清除详单缓存</button>
                            <?php }?>
                        </div>
                        <div class="form-group pull-right">
                            <button type="button" id="file_export" class="btn btn-sm btn-success">导出</button>
                        </div>
                        <div class="form-group pull-right">
                            <button type="submit" class="btn btn-sm btn-primary">查询</button>
                        </div>
                    </div>
                    <div class="row-second">
                        <div class="form-group">
                            <label class="control-label">错误等级：</label>
                            <select class="form-control" name="error_level" id="error_level">
                                <option value="">错误等级</option>
                                <option value="success"
                                <?= ($input['error_level'] === 'success') ? 'selected' : '' ;?>>无错误</option>
                                <option value="user_error"
                                <?= ($input['error_level'] === 'user_error') ? 'selected' : '' ;?>>用户错误</option>
                                <option value="crawler_error"
                                <?= ($input['error_level'] === 'crawler_error') ? 'selected' : '' ;?>>爬虫错误</option>
                                <option value="third_party_error"
                                <?= ($input['error_level'] === 'third_party_error') ? 'selected' : '' ;?>>第三方错误</option>
                                <option value="telecom_error"
                                <?= ($input['error_level'] === 'telecom_error') ? 'selected' : '' ;?>>运营商错误</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="control-label" for="crawler_channel">爬虫渠道：</label>
                            <select class="form-control" id="crawler_channel" name="crawler_channel">
                                <option value="">全部渠道</option>
                                <foreach name="channel" item="vo">
                                    <option value="{$key}" <?= ($input['crawler_channel'] == $key) ? 'selected' : '';?>>{$vo}</option>
                                </foreach>
                                <option value="other" <?= ($input['crawler_channel'] == 'other') ? 'selected' : '';?>>客户自有渠道</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">运营商：</label>
                            <select class="form-control" name="flow_type" id="flow_type">
                                <option value="" selected>运营商</option>
                                <option value="移动"
                                <?= ($input['flow_type'] === '移动') ? 'selected' : '' ;?>>移动</option>
                                <option value="联通"
                                <?= ($input['flow_type'] === '联通') ? 'selected' : '' ;?>>联通</option>
                                <option value="电信"
                                <?= ($input['flow_type'] === '电信') ? 'selected' : '' ;?>>电信</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">爬虫地区：</label>
                            <select class="form-control" id="province_sel" name="province">
                                <?php if ($input['province']) { ?>
                                <option value="<?= $input['province']; ?>"><?= $input['province']; ?></option>
                                <?php } ?>
                                <option value="">爬虫渠道</option>
                                <foreach name="crawler_areas" item="province">
                                    <option value="{$province}">{$province}</option>
                                </foreach>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="control-label">Status：</label>
                            <input type="text" class="form-control input-sm" name="status"
                                   value="{$input['status']}"
                                   placeholder="搜索Status">
                        </div>

                    </div>

                    <div class="row-third">
                        <div class="form-group">
                            <label class="control-label">Sid：</label>
                            <input type="text" class="form-control input-sm" name="sid" value="{$input['sid']}"
                                   placeholder="搜索Sid">
                        </div>

                        <div class="form-group">
                            <label class="control-label">Tel：</label>
                            <input type="text" class="form-control input-sm" name="tel" value="{$input['tel']}"
                                   placeholder="搜索Tel">
                        </div>
                        <div class="form-group">
                            <label class="control-label">Cid：</label>
                            <input type="text" class="form-control input-sm" name="cid" value="{$input['cid']}"
                                   placeholder="搜索Cid">
                        </div>
                    </div>

                </form>
            </div>

        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive" style="overflow-y: hidden;">
        <div class="panel-heading"><h3 class="panel-title">日志列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th style="width: 280px;">SID</th>
                <th style="width: 100px;">Tel</th>
                <th style="width: 80px;">地区</th>
                <th style="width: 80px;">Telecom</th>

                <th style="width: 70px;">Channel</th>
                <th style="width: 310px;">Status</th>

                <th style="width: 30px;">CID</th>
                <th style="width: 80px;">缺失月份</th>
                <th style="width: 200px;">Time</th>
                <th style="width: 50px;">操作</th>
            </tr>
            </thead>
            <tbody>
            <?php
                if($list && is_array($list)) {
                    foreach ($list as $key => $value) {
            ?>
            <tr>
                <td>
                    <if condition="!isset($value['crawler_channel']) || empty($value['crawler_channel']) || $value['crawler_channel'] == 'yulore'">
                        <a href="{:U('msg',array('sid'=>$value['sid']))}"
                           style="text-decoration:none;">{$value['sid']}</a>
                        <else/>
                        {$value['sid']}
                    </if>
                </td>
                <td>{$value['tel']}</td>

                <td>
                    <div>{$value['tel_info']['province']}</div>
                    <div>{$value['tel_info']['city']}</div>
                </td>

                <td>{$value['tel_info']['telecom']}</td>

                <td>{$value['crawler_channel']|default="yulore"}</td>

                <td>
                    <if condition="!in_array($value[status],[0,1,2,3100])">
                        <div class="btn-danger btn-xs" style="width: 50px;">{$value['status']}</div>
                        <div>{$value['message']}</div>
                    <else/>
                        <div>{$value['status']}</div>
                        <div>{$value['message']}</div>
                    </if>
                </td>

                <td>{$value['cid']}</td>

                <td style="word-wrap : break-word; overflow:hidden;" width="150px">
                    <?= $value['lock_month'];?>
                </td>

                <td>
                    <div>start:&nbsp;{:date('Y-m-d H:i:s',$value['start_time'])}</div>
                    <if condition="!empty($value['end_time'])">
                        <div>&nbsp;end:&nbsp;{:date('Y-m-d H:i:s',$value['end_time'])}</div>
                    </if>
                </td>

                <?php if(!in_array($value['status'],[0,1,2,3100])): ?>
                <?php if(!empty($value['repairid'])):?>
                <td>
                    <a onclick="DHB.INFO.view('{:U(\'repair\',array(\'repairid\'=>$value[\'repairid\']))}','已修复')"
                       class="btn label-default btn-xs" style="background-color: #9d9d9d;color: #f5f5f5;">已修复</a>

                    <a href="{:U('locus')}?sid={$value.sid}" class="btn btn-primary btn-xs">任务轨迹</a>
                </td>
                <?php else: ?>
                <td>
                    <a onclick="DHB.INFO.set('{:U(\'repair\',array(\'sid\'=>$value[\'sid\'],\'error_time\'=>$value[\'end_time\']))}','修复')"
                       class="btn btn-primary btn-xs">修复</a>
                    <a href="{:U('locus')}?sid={$value.sid}" class="btn btn-primary btn-xs">任务轨迹</a>
                </td>
                <?php endif ?>
                <?php else: ?>
                <td><a href="{:U('locus')}?sid={$value.sid}" class="btn btn-primary btn-xs">任务轨迹</a></td>
                <?php endif ?>
            </tr>
            <?php }} ?>
            </tbody>
        </table>
    </div>

    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>
<script type="text/javascript">
    $(function () {
        $("#province_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '爬虫地区'
        });

        // check end time
        $("#form_log").submit(function () {
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var today_str = (new Date()).toDateString();

            if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
                alert('请选择有效的结束时间');
                return false;
            }

            // check time
            var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
            if (time_end && time_diff < 0) {
                alert('开始时间必须小于结束时间');
                return false;
            }

            return true;
        });

        // export file
        $("#file_export").click(function () {
            var form_data = $("#form_log").serializeArray();
            var url_export = '/Home/CallCrawlerLog/export';
            $.fileDownload(url_export, {
                httpMethod: 'GET',
                data: form_data
            });
            return false;
        });

        // click cache_time
        $('#clear_cache_time').click(function () {
            var url_clear_cache = '/Home/CallCrawlerLog/clearCache';
            $.get(url_clear_cache).success(function (response) {
                response = JSON.parse(response);
                alert(response.msg);
            });
        });
    });
</script>
</body>
</html>