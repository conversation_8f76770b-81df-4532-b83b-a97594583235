<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
        <div id="breadcrumb_search_box">
            <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'\create\')}','添加字段')" class="btn btn-success">添加字段</a>
        </div>
    </div>

    <form class="form-inline" action="{:U('index')}" style="padding-bottom: 10px">
        <div class="form-group">
            <label class="control-label">状态</label>&nbsp;
            <select class="form-control" name="status">
                <option value="" selected>全部</option>
                <option value="1" <?= ($check_query['status'] === '1') ? 'selected' : '' ;?>>可用</option>
                <option value="2" <?= ($check_query['status'] === '2') ? 'selected' : '' ;?>>禁用</option>
            </select>
        </div>
        &nbsp;
        <div class="form-group">
            <input type="submit" class="btn btn-primary  btn-block" value="查询">
        </div>
    </form>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">字段列表</h3></div>
        <table class="table table-hover table-bordered" style="table-layout:fixed;" align="center">
            <thead>
            <tr>
                <th>ID</th>
                <th>状态</th>
                <th>字段</th>
                <th>字段名称</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list_show as $show): ?>
            <tr>
                <td>{$show['id']}</td>
                <td><?= ($show['status'] == 1) ? '可用' : '禁用' ;?></td>
                <td>{$show['name']}</td>
                <td>{$show['remark']}</td>
                <td>
                    <a href="javascript:;" onclick="DHB.INFO.set('{:U(\'edit\',array(\'id\'=>$show[\'id\']))}','编辑字段')" class="btn btn-primary btn-xs">编辑</a>
                </td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>
    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>
</body>
</html>
