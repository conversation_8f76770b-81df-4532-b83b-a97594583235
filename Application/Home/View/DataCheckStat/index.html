<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .container label {
            margin-left: 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="/Home/DataCheckStat/index" method="get" id="form_id">
                <div class="form-group">
                    <label  class="control-label">开始时间</label>
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                </div>
                <div class="form-group">
                    <label  class="control-label">结束时间</label>
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                </div>
                <div class="form-group">
                    <label for="account_id">选择客户</label>
                    <select name="customer_id" id="customer_id" style="min-width: 100px">
                        <option value="">选择客户</option>
                        <?php
                            if ($customer_list && is_array($customer_list)) {
                                foreach ($customer_list as $key => $value) {
                        ?>
                        <option value="{$key}" <?= (isset($input['customer_id']) && $input['customer_id'] == $key) ? 'selected' : '' ?>>{$value['name']}</option>
                        <?php
                                }
                            }
                        ?>
                    </select>
                </div>
                <div class="form-group">
                    <select class="form-control" name="contract_status" id="contract_status" onChange="return requestUser()">
                        <option value="0" selected>账号签约状态</option>
                        <?php foreach ($contract_status as $key => $value) { ?>
                        <option value="<?= $key ;?>" <?= (isset($input['contract_status']) && $input['contract_status'] == $key) ? 'selected' : '' ?>><?= $value;?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group pull-right">
                    <input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询">
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <tr align="center">
                <th>客户ID</th>
                <th>客户名称</th>
                <th>账号名称</th>
                <th>总查询量</th>
                <th>有效查询量</th>
                <th>有效查询率</th>
            </tr>
            <tr>
                <?php if ($total) { ?>
                <td colspan="2"></td>
                <td style="text-align: center">总计</td>
                <td><?= isset($total['sum_all']) ? $total['sum_all'] : 0; ?></td>
                <td><?= isset($total['sum_success']) ? $total['sum_success'] : 0; ?></td>
                <td><?= round(($total['sum_success']/$total['sum_all']*100), 2).'%'?></td>
                <?php } ?>
            </tr>
            <?php
            foreach ($list as $key => $value) { ?>
            <tr>
                <td>{$key}</td>
                <td>
                    <a href="{:U('detail',['customer_id'=>$key])}">{$value['name']}</a>
                </td>
                <td width="50%">
                    <?php
                        $num = count($value['account_list']);
                        if (isset($value['account_list']) && is_array($value['account_list'])) {
                            foreach ($value['account_list'] as $kk => $val) {
                    ?>
                    <a href="{:U('detail',['customer_id' => $key,'account_id'=>$val['account_id']])}">{$val['account_name']}</a>
                    <?php
                                echo ($kk == $num-1) ? '' : ' | ';
                            }
                        }
                    ?>
                </td>
                <td><?= isset($value['sum_all']) ? $value['sum_all'] : 0; ?></td>
                <td><?= isset($value['sum_success']) ? $value['sum_success'] : 0; ?></td>
                <td><?= isset($value['sum_rate']) ? $value['sum_rate'] : '0.00%'; ?></td>
            </tr>
            <?php } ?>

        </table>
    </div>

    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>

</div>
</div>
<script type="text/javascript">
    $(function() {
        // 客户select2
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户'
        });
    });

    function checkTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();
        // change time format for firefox
        time_end = time_end.replace(/\-/g, '\/');
        time_begin = time_begin.replace(/\-/g, '\/');
        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }
        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }
        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }
        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }

        if (!time_end) {
            time_end = today_str;
        }
        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }
        // calculate the days between begin and end
        var day_diff =  Math.floor(time_diff/8.64e7);
        //  the time should less than 31
        if (day_diff <= 365) {
            return true;
        } else {
            alert('单次查询时间范围不能超过365天');
            return false;
        }
    }

</script>
</body>
</html>
