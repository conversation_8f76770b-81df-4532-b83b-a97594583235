<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery-ui.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css" />
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">

                <div class="form-group">
                    <label  class="control-label">开始时间</label>&nbsp;
                    <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d', strtotime('-1 month')) : $input['begin']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group">
                    <label  class="control-label">结束时间</label>&nbsp;
                    <input type="date" name="end" id="time_end" class="form-control"  value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                </div>
                &nbsp;
                <div class="form-group select2-style-adjust">
                    <label for="account_id">选择客户</label>
                    <select class="form-control" id="customer_id" name="customer_id" style="min-width: 100px">
                        <option value="" >选择客户</option>
                        <foreach name="customer_list"  item="value" key="key">
                            <option value="{$key}" <?= (isset($input['customer_id']) && $input['customer_id'] == $key) ? 'selected' : '' ?> >{$value['name']}</option>
                        </foreach>
                    </select>
                </div>
                &nbsp;
                <div class="form-group select2-style-adjust" >
                    <label for="account_id">选择账号</label>
                    <select class="form-control" id="account_id" name="account_id" style="min-width: 100px">
                        <option value="" >选择账号</option>
                        <foreach name="customer_list[$input['customer_id']]['account_list']"  item="value">
                            <option value="{$value['account_id']}" <?= (isset($input['account_id']) && $input['account_id'] == $value['account_id']) ? 'selected' : '' ?> >{$value['account_name']}</option>
                        </foreach>
                    </select>
                </div>
                &nbsp;
                <div class="form-group">
                        <input type="submit" onclick="return checkTime();" class="btn btn-primary  btn-block" value="查询">
                </div>
                &nbsp;
                <div class="form-group">
                    <a class="btn btn-info" href="{:U('index')}" role="button">返回列表</a>
                </div>
            </form>

        </div>
    </div>
</div>
<?php
if (isset($input['customer_id']) && (!isset($input['account_id']) || !$input['account_id'])) {
    echo '<div style="margin-bottom: 10px;margin-left: 14px;"><strong>'.$customer_list[$input['customer_id']]['name'].'客户统计详情</strong></div>';
}
if (isset($input['customer_id']) && isset($input['account_id']) && $input['account_id']) {
    echo '<div style="margin-bottom: 10px;margin-left: 14px;"><strong>'.$customer_list[$input['customer_id']]['account_list'][0]['account_name'].'账号统计详情</strong></div>';
}
?>
<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th width="7%">时间</th>
                <th>总查询量</th>
                <th>有效查询量</th>
                <th>有效查询率</th>
                <?php
                    if ($product_list && is_array($product_list)) {
                        foreach ($product_list as $key => $value) {
                            echo '<th>'.$value['name'].'</th>';
                        }
                    }
                ?>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>总计</td>
                <td><?= isset($total['c_all']) ? $total['c_all'] : 0; ?></td>
                <td><?= isset($total['c_success']) ? $total['c_success'] : 0; ?></td>
                <td><?= round(($total['c_success']/$total['c_all']*100), 2).'%'?></td>
                <?php
                    if ($product_list && is_array($product_list)) {
                        foreach ($product_list as $key => $value) {
                            echo '<td>'.(isset($total[$value['action']]) ? $total[$value['action']] : 0).'</td>';
                        }
                    }
                ?>
            </tr>

            <?php foreach ($list as $key => $value) {?>
            <tr>
                <td><?= $key; ?></td>
                <td><?= isset($value['c_all']) ? $value['c_all'] : 0; ?></td>
                <td><?= isset($value['c_success']) ? $value['c_success'] : 0; ?></td>
                <td><?= isset($value['c_rate']) ? $value['c_rate'] : '0.00%'; ?></td>
                <?php
                    if ($product_list && is_array($product_list)) {
                        foreach ($product_list as $kk => $vv) {
                            echo '<td>'.(isset($value[$vv['action']]) ? $value[$vv['action']] : 0).'</td>';
                        }
                    }
                ?>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">

    $(document).ready(function() {
        $("#customer_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户'
        });

        $("#account_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号'
        });
    });

    function checkTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();
        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }
        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }
        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }
        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }
        if (!time_end) {
            time_end = today_str;
        }
        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }
        // calculate the days between begin and end
        var day_diff =  Math.floor(time_diff/8.64e7);
        //  the time should less than 31
        if (day_diff > 365) {
            alert('单次查询时间范围不能超过365天');
            return false;
        }
        // check choose user
        var customer_id = $('#customer_id').val();
        if (!customer_id) {
            alert('请选择客户');
            return false;
        }
        return true;
    }
</script>
</body>
</html>
