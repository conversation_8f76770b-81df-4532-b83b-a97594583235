<form class="form-horizontal" role="form" id="form_set_id" data-info='<?= isset($list) ? json_encode($list) : "" ?>'>
    <div class="form-group">
        <label class="col-sm-2 control-label">账号名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="developer" value="{$list['developer']}">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" for="set_choose_belongs_to_user">所属客户</label>
        <div class="col-sm-4" id="set_div_choose_belongs_to_user">
            <select name="name" id="set_choose_belongs_to_user" class="form-control">
                <option value="">暂不绑定</option>
                <?php foreach($list_account_for_select2 as $account) {?>
                <option value="<?= $account['name']; ?>" <?= ($account['id']==$account_product_info['account_id'])? 'selected="selected"' : ''?>><?= $account['name']; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="status">
                <option value="1" <?= ($list['status'] == 1) ? 'selected="selected"' : '' ?> >可用</option>
                <option value="2" <?= ($list['status'] == 2) ? 'selected="selected"' : '' ?> >禁用</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签约状态</label>
        <div class="col-sm-4">
            <select name="contract_status" id="contract_status" class="form-control">
                <option value="1" <?= ($list['contract_status'] == 1) ? 'selected' : '' ?>>已签约已付款</option>
                <option value="2" <?= ($list['contract_status'] == 2) ? 'selected' : '' ?>>已签约未付费</option>
                <option value="3" <?= ($list['contract_status'] == 3) ? 'selected' : '' ?>>未签约</option>
                <option value="5" <?= ($list['contract_status'] == 5) ? 'selected' : '' ?>>特殊客户</option>
                <option value="4" <?= ($list['contract_status'] == 4) ? 'selected' : '' ?>>其他</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">输出类型</label>
        <div class="col-sm-8">
            <label class="radio-inline">
                <input type="radio" name="is_level" value="0" <?=  (!isset($list['is_level'])  || $list['is_level'] == 0) ? 'checked' : '' ;?>>
                158个字段
            </label>
            <label class="radio-inline">
                <input type="radio" name="is_level"   value="1" <?=  $list['is_level'] == 1 ? 'checked' : '' ;?>>
                催收综合分
            </label>
            <label class="radio-inline">
                <input type="radio" name="is_level"  value="2" <?=  $list['is_level'] == 2 ? 'checked' : '' ;?>>
                158个字段&催收综合分
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签名类型</label>
        <div class="col-sm-8">
            <label class="radio-inline">
                <input type="radio" name="flag" value="0" <?=  (!isset($list['flag']) || $list['flag'] == 0) ? 'checked' : '' ;?>>
                旧签名
            </label>
            <label class="radio-inline">
                <input type="radio" name="flag"   value="1" <?=  $list['flag'] == 1 ? 'checked' : '' ;?>>
                新签名
            </label>
        </div>
    </div>

    <div class="form-group access_sel">
        <label class="col-sm-2 control-label">是否需要金融标签</label>
        <div class="col-sm-10">
            <label class="radio-inline">
                <input type="radio" name="need_itag" class="need_itag-radio" value="1" <?= ($list['need_itag'] == 1) ? 'checked' : '' ?>>
                是
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_itag" class="need_itag-radio" value="2" <?= ($list['need_itag'] != 1) ? 'checked' : '' ?>>
                否
            </label>
        </div>
        <label class="col-sm-2 control-label">是否异步推送</label>
        <div class="col-sm-10">
            <label class="radio-inline">
                <input type="radio" name="need_async_notify" value="1" class="async_notify_radio" <?= ((isset($list['notify_url']) && $list['notify_url']) || $list['need_async_notify']) ? 'checked' : '' ?>>
                是
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_async_notify" value="2" class="async_notify_radio" <?= ((isset($list['notify_url']) && $list['notify_url']) || $list['need_async_notify']) ? '' : 'checked' ?>>
                否
            </label>
        </div>
    </div>

    <?php if ($list['need_itag'] == 1) { ?>
    <div class="form-group ui-dist">
        <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否推送催收分结果</label>
        <div class="col-sm-10">
            <label class="radio-inline"><input type="radio" name="only_itag" value="0" <?= empty($list['only_itag']) ? 'checked' : '' ?>>是</label>
            <label class="radio-inline"> <input type="radio" name="only_itag" value="1" <?= ($list['only_itag'] == 1) ? 'checked' : '' ?>>否</label>
        </div>
        <label class="col-sm-2 control-label">金融推送地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="itag_notify_url" value="{$list['itag_notify_url']}">
        </div>
        <label class="col-sm-2 control-label">金融APIKEY</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="itag_apikey" value="{$list['itag_apikey']}">
        </div>
        <label class="col-sm-2 control-label">金融APPSECRET</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="itag_password" value="{$list['itag_password']}">
        </div>
        <label class="col-sm-2 control-label">金融标签vip通道</label>
        <div class="col-sm-4">
            <label class="radio-inline"><input type="radio" name="itag_vip_queue" value="1" <?= (isset($list['itag_vip_queue']) && $list['itag_vip_queue']) ? 'checked' : '' ?>>是</label>
            <label class="radio-inline"> <input type="radio" name="itag_vip_queue" value="0" <?= (!isset($list['itag_vip_queue']) || !$list['itag_vip_queue']) ? 'checked' : '' ?>>否</label>
        </div>
    </div>
    <?php } ?>

    <div class="form-group">
        <label class="col-sm-2 control-label">截止日期</label>
        <if condition="empty($list['validuntil'])">
            <div class="col-sm-4">
                <input type="date" class="form-control" id="validuntil" name="validuntil" value="">
            </div>
            <else/>
            <div class="col-sm-4">
                <input type="date" class="form-control" id="validuntil" name="validuntil" value="{:date('Y-m-d',$list['validuntil'])}">
            </div>
        </if>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">日限额</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="daily_limit" value="<?= (isset($list['daily_limit']) && ($list['daily_limit'] != -1)) ? $list['daily_limit'] : '';?>" placeholder="无限制"/>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">月限额</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="monthly_limit" value="<?= (isset($list['monthly_limit']) && ($list['monthly_limit'] != -1)) ? $list['monthly_limit'] : '';?>" placeholder="无限制"/>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">年限额</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="yearly_limit" value="<?= (isset($list['yearly_limit']) && ($list['yearly_limit'] != -1)) ? $list['yearly_limit'] : '';?>" placeholder="无限制"/>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">APIKEY</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="apikey" id="input_appid" value="{$list['apikey']}" readonly>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">APPSECRET</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="password" id="input_password" value="{$list['password']}" readonly>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">IP白名单</label>
        <div class="col-sm-10">
            <textarea type="text" class="form-control" name="access_ip"><?= implode(PHP_EOL,$list['access_ip']);?></textarea>
        </div>
    </div>

    <div class="form-group async_notify" style="display:<?= ((isset($list['notify_url']) && $list['notify_url']) || $list['need_async_notify']) ? 'block' : 'none' ?>">
        <label class="col-sm-2 control-label">异步推送地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="notify_url" value="<?= $list['notify_url'] ?>">
        </div>
    </div>

    <input type="hidden" name="id" value="{$list['id']}">

</form>
<div class="alert alert-danger" role="alert">
    <p>注意：</p>
    <p>1. 编辑账号时如果选择了需要金融标签,那么请输入金融推送地址,金融APPSECRET,金融APIKEY</p>
    <p>2. IP白名单由客户提供,无需求可置空</p>
    <p>3. 需要异步推送时，需填写异步推送地址</p>
    <p>4. 限额无限制时置空</p>
</div>

<script type="text/javascript">
    $(function(){
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};

        //避免搜索框不聚焦方法
        $("#set_choose_belongs_to_user").select2({
            dropdownParent: $("#set_div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#set_choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });

        var list = JSON.parse($('#form_set_id').attr('data-info'));
        if(!list){
            creat_hash('input_password',64);
            creat_hash('input_appid',32);
        }

        $("input:radio.need_itag-radio").on('change', function(event) {
            event.preventDefault();
            $("div.ui-dist").remove();
            if ('1' != $(this).val()) {
                $("div.ui-dist").remove();
            } else {
                createInput(list);
            }
        });

        $("input:radio.async_notify_radio").on('change', function() {
            if ('1' == $(this).val()) {
                $("div.async_notify").css({
                    display: 'block'
                });
            } else {
                $("div.async_notify").css({
                    display: 'none'
                });
            }
        });

        // check validuntil
        $("#validuntil").change(
            function () {
                var time_choose = $(this).val();
                var today_str = (new Date()).toDateString();
                if ((Date.parse(time_choose + ' GMT +8') - Date.parse(today_str + ' GMT +8') < 0)) {
                    confirm('您选定的截至日期是无效的, 请问您确定要这样吗?');
                }
            }
        );

    });
    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function createInput(info)
    {
        var htm = '\
         <div class="form-group ui-dist">\
            <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否推送催收分结果</label>\
            <div class="col-sm-10">\
                <label class="radio-inline"><input type="radio" name="only_itag" value="0" <?= empty($list['only_itag']) ? 'checked' : '' ?> >是</label>\
                <label class="radio-inline"><input type="radio" name="only_itag" value="1" <?= ($list['only_itag'] == 1) ? 'checked' : '' ?> >否</label>\
            </div>\
            <label class="col-sm-2 control-label">金融推送地址</label>\
            <div class="col-sm-10">\
                <input type="text" class="form-control" name="itag_notify_url" value="'+((info.itag_notify_url) ? info.itag_notify_url : '')+'" >\
            </div>\
            <label class="col-sm-2 control-label">金融APIKEY</label>\
            <div class="col-sm-10">\
                 <input type="text" class="form-control" name="itag_apikey" value="'+((info.itag_apikey) ? info.itag_apikey : '')+'">\
             </div>\
            <label class="col-sm-2 control-label">金融APPSECRET</label>\
            <div class="col-sm-10">\
                <input type="text" class="form-control" name="itag_password" value="'+((info.itag_password) ? info.itag_password : '')+'">\
            </div>\
            <label class="col-sm-2 control-label">金融标签vip通道</label>\
            <div class="col-sm-4">\
                <label class="radio-inline"><input type="radio" name="itag_vip_queue" value="1" <?= (isset($list['itag_vip_queue']) && $list['itag_vip_queue']) ? 'checked' : '' ?>>是</label>\
                <label class="radio-inline"> <input type="radio" name="itag_vip_queue" value="0" <?= (!isset($list['itag_vip_queue']) || !$list['itag_vip_queue']) ? 'checked' : '' ?>>否</label>\
            </div>\
        </div>\
'
        $("div.access_sel").after(htm);
    }

</script>
