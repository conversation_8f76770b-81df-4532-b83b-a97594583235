<form class="form-horizontal" role="form" id="form_set_id" data-info="<?= isset($info)?$info:'' ?>">
    <div class="form-group">
        <label class="col-sm-2 control-label">账号名称</label>
        <div class="col-sm-4">
            <input type="text" class="form-control" name="developer" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label" for="choose_belongs_to_user">所属客户</label>
        <div class="col-sm-4" id="div_choose_belongs_to_user">
            <select name="name" id="choose_belongs_to_user" class="form-control">
                <option value="">暂不绑定</option>
                <?php foreach($list_account_for_select2 as $account) {?>
                <option value="<?= $account['name']; ?>"><?= $account['name']; ?></option>
                <?php } ?>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">账号状态</label>
        <div class="col-sm-4">
            <select class="form-control" name="status">
                <option value="1">可用</option>
                <option value="2">禁用</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签约状态</label>
        <div class="col-sm-4">
            <select name="contract_status" id="contract_status" class="form-control">
                <option value="1">已签约已付款</option>
                <option value="2">已签约未付费</option>
                <option value="3" selected>未签约</option>
                <option value="5">特殊客户</option>
                <option value="4">其他</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">输出类型</label>
        <div class="col-sm-8">
            <label class="radio-inline">
                <input type="radio" name="is_level" value="0" checked>
                158个字段
            </label>
            <label class="radio-inline">
                <input type="radio" name="is_level"   value="1">
                催收综合分
            </label>
            <label class="radio-inline">
                <input type="radio" name="is_level"  value="2">
                158个字段&催收综合分
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">签名类型</label>
        <div class="col-sm-8">
            <label class="radio-inline">
                <input type="radio" name="flag" value="0" >
                旧签名
            </label>
            <label class="radio-inline">
                <input type="radio" name="flag"   value="1" checked>
                新签名
            </label>
        </div>
    </div>
    <div class="form-group access_sel">
        <label class="col-sm-2 control-label">是否需要金融标签</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_itag" class="need_itag-radio"  value="1">
                是
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_itag" class="need_itag-radio deny"   value="2" checked >
                否
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">是否异步推送</label>
        <div class="col-sm-4">
            <label class="radio-inline">
                <input type="radio" name="need_async_notify" value="1" class="async_notify_radio">
                是
            </label>
            <label class="radio-inline">
                <input type="radio" name="need_async_notify" value="2" class="async_notify_radio" checked>
                否
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">截止日期</label>
        <if condition="empty($info['validuntil'])">
            <div class="col-sm-4">
                <input type="date" class="form-control" name="validuntil" value="">
            </div>
            <else/>
            <div class="col-sm-4">
                <input type="date" class="form-control" name="validuntil" value="{:date('Y-m-d',$info['validuntil'])}">
            </div>
        </if>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">日限额</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="daily_limit" value="" placeholder="无限制"/>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">月限额</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="monthly_limit" value="" placeholder="无限制"/>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">年限额</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="yearly_limit" value="" placeholder="无限制"/>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label">AKIKEY</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="apikey" id="input_appid" value="" readonly>
        </div>

        <IF condition="empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_appid',32)">生 成</button>
            </div>
        </IF>

    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">APPSECRET</label>
        <div class="col-sm-8">
            <input type="text" class="form-control" name="password" id="input_password" value="" readonly>
        </div>
        <IF condition="empty($info['id'])">
            <div class="col-sm-2">
                <button type="button" class="btn btn-default btn-block" onclick="edit_hash('input_password',64)">生 成</button>
            </div>
        </IF>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">IP白名单</label>
        <div class="col-sm-10">
            <textarea type="text" class="form-control" name="access_ip"></textarea>
        </div>
    </div>
    <div class="form-group async_notify" style="display: none;">
        <label class="col-sm-2 control-label">异步推送地址</label>
        <div class="col-sm-10">
            <input type="text" class="form-control" name="notify_url" value="">
        </div>
    </div>
</form>
<div class="alert alert-danger" role="alert">
    <p>注意：</p>
    <p>1. 编辑账号时如果选择了需要金融标签,那么请输入金融推送地址,金融APPSECRET,金融APIKEY</p>
    <p>2. IP白名单由客户提供,无需求可置空</p>
    <p>3. 需要异步推送时，需填写异步推送地址</p>
    <p>4. 限额无限制时置空</p>
</div>

<script type="text/javascript">
    $(function(){
        // Do this before you initialize any of your modals
        $.fn.modal.Constructor.prototype.enforceFocus = function() {};
        //避免搜索框不聚焦方法
        $("#choose_belongs_to_user").select2({
            dropdownParent: $("#div_choose_belongs_to_user")
        });

        //选择所属客户
        $("#choose_belongs_to_user").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '暂不绑定',
            width : '100%'
        });

        var info = $('#form_set_id').attr('data-info');

        if(!info){
            creat_hash('input_password',64);
            creat_hash('input_appid',32);
        }

        $("input:radio.need_itag-radio").on('change', function(event) {
            event.preventDefault();
            if ('1' != $(this).val()) {
                $("div.ui-dist").remove();
            } else {
                createInput();
            }
        });

        $("input:radio.async_notify_radio").on('change', function() {
            if ('1' == $(this).val()) {
                $("div.async_notify").css({
                    display: 'block'
                });
            } else {
                $("div.async_notify").css({
                    display: 'none'
                });
            }
        });

    });
    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length)
    {
        if(confirm('修改appid,appsecret会使当前账号的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        }
    }

    function createInput()
    {
        var htm ='\
         <div class="form-group ui-dist">\
            <label class="col-sm-2 control-label" style="padding-right: 8px;padding-left: 8px;">是否推送催收分结果</label>\
            <div class="col-sm-10">\
                <label class="radio-inline"><input type="radio" name="only_itag" value="0" checked>是</label>\
                <label class="radio-inline"><input type="radio" name="only_itag" value="1">否</label>\
            </div>\
            <label class="col-sm-2 control-label">金融推送地址</label>\
            <div class="col-sm-10">\
                <input type="text" class="form-control" name="itag_notify_url" value="">\
            </div>\
            <label class="col-sm-2 control-label">金融APIKEY</label>\
            <div class="col-sm-10">\
                 <input type="text" class="form-control" name="itag_apikey" value="">\
             </div>\
            <label class="col-sm-2 control-label">金融APPSECRET</label>\
            <div class="col-sm-10">\
                <input type="text" class="form-control" name="itag_password" value="">\
            </div>\
            <label class="col-sm-2 control-label">金融标签vip通道</label>\
            <div class="col-sm-4">\
                <label class="radio-inline"><input type="radio" name="itag_vip_queue" value="1">是</label>\
                <label class="radio-inline"> <input type="radio" name="itag_vip_queue" value="0" checked>否</label>\
             </div>\
        </div>\
';
        $("div.access_sel").after(htm);
    }

</script>
