<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
        span {
            font-weight: bold;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<div class="container" id="list_upgrade">
    <api_config_template api_config_info='<?= $api_config_info ?>' :id='<?= $id ?>'></api_config_template>
    <dialog_template></dialog_template>
</div>
<script type="text/x-template" id="api_config_update">
    <div>
        <div class="row">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span>催收分API配置</span>
                </div>
                <div class="panel-body">
                    <form class="form-horizontal" @submit.prevent="updateConfig">
                        <div class="form-group">
                            <label class="col-md-2 col-md-offset-2">必填字段</label>
                            <div class="checkbox checkbox-inline" :class="[(index == 1) ? '' : '']" v-for="(field_name, index) in form_body.list_require_field">
                                <label>
                                    <input type="checkbox" v-model="form_body.required_field" :value="index"> {{ field_name }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-offset-2 col-md-4">
                                <input type="submit" style="margin-right: 40px" class="btn btn-primary btn-sm"
                                       value="更新">
                                <a href="/Home/Cuishou/index" class="btn btn-info btn-sm">返回列表</a>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</script>

<script>
    Vue.component('api_config_template', {
        template: '#api_config_update',
        props: ['api_config_info', 'id'],
        data: function () {
            return {
                form_body: {
                    required_field: [],
                    id: '',
                    list_require_field: {
                        1: '手机号',
                        2: '身份证号',
                        3: '姓名'
                    }
                }
            }
        },
        created: function () {
            // 初始化配置
            this.initParams();
        },
        methods: {
            // 初始化配置
            initParams: function () {
                this.form_body.id = this.id;

                // 已经进行了配置 则渲染页面
                var api_required_field = JSON.parse(this.api_config_info);
                this.form_body.required_field = api_required_field.required_field ? api_required_field.required_field : [1];
            },

            // 更新配置
            updateConfig: function () {
                // 检查参数
                var result_check = this.checkParam();
                if (result_check === false) {
                    return false;
                }

                // 发送请求
                var url = '/Home/Cuishou/apiConfig';
                var vm = this;

                this.$http.post(url, vm.form_body, {responseType: 'json'}).then(function (response) {
                    if (response.body.status !== 0) {
                        modalExport(response.body.msg);
                        return false;
                    } else {
                        modalExport(response.body.msg);
                        // window.location.href = '/Home/BmCrawler/index';
                    }
                }, function (reason) {
                    console.log(reason)
                });
            },
            checkParam: function () {
                return true;
            }
        }
    });

    new Vue({
        el: "#list_upgrade"
    });


</script>

</body>
</html>
