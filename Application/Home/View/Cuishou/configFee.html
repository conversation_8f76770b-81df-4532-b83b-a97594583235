<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        .row-first { margin-bottom: 10px; }
        .row-first label, .row-second label { margin-left: 10px;}
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<form class="form-horizontal" action="{:U('Home/Cuishou/configFee', ['id'=>$list['id']])}" method="post" id="fee_cuishou_config">
    <div class="form-group">
        <label class="col-sm-2 control-label">催收分账号：</label>
        <div class="col-sm-3">
            <input type="hidden" id="config_id" class="form-control" name="id" value="{$info['id']}">
            <input type="hidden" id="product_id" class="form-control" name="product_id" value="{$list['id']}">
            <input type="text" class="form-control" name="developer" readonly value="{$list['developer']}">
        </div>
    </div>

    <div class="form-group">
        <label class="col-md-2 control-label">计费依据：</label>
        <div class="col-sm-8">
            <?php foreach($feeBasis as $key=>$value) : ?>
            <label class="radio-inline">
                <input type="radio" <?php if($info){echo 'disabled';}?> name="fee_basis" value="{$key}" <?php if($key==$info['fee_basis']){echo 'checked';} ?>>
                {$value}
            </label>
            <?php endforeach; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-2 control-label">计费方式：</label>
        <div class="col-sm-8">
            <?php foreach($feeMethod as $key=>$value) : ?>
            <label class="radio-inline">
                <input type="radio" <?php if($info){echo 'disabled';}?> class="fee_method" name="fee_method" value="{$key}" <?php if($key==$info['fee_method']){echo 'checked';} ?>>
                {$value}
            </label>
            <?php endforeach; ?>
        </div>
    </div>
    <div class="form-group access_sel" id="fee_time_rule" <?php if($info&&$info['fee_method']==1){echo 'style="display: block"';}else{echo 'style="display: none"';}?>>
        <label class="col-sm-2 control-label">时间计费规则：</label>
        <div class="col-sm-4">
            <?php foreach($timeRule as $key=>$value) : ?>
            <label class="radio-inline">
                <input type="radio" <?php if($info){echo 'disabled';}?> name="fee_time_rule" class="need_itag-radio"  value="{$key}" <?php if($key==$info['fee_time_rule']){echo 'checked';} ?>>
                {$value}
            </label>
            <?php endforeach; ?>
        </div>
    </div>
    <div class="form-group" id="fee_time_price" <?php if($info&&$info['fee_method']==1){echo 'style="display: block"';}else{echo 'style="display: none"';}?>>
        <label class="col-sm-2 control-label">价格（元）：</label>
        <div class="col-sm-2">
            <input type="text" class="form-control" id="fee_time_price_value" name="fee_time_price" value="<?php if(1==$info['fee_method']){echo $info['fee_price'];} ?>">
        </div>
    </div>


    <div class="form-group" id="fee_amount_rule" <?php if($info&&$info['fee_method']==2){echo 'style="display: block"';}else{echo 'style="display: none"';}?>>
        <label class="col-sm-2 control-label">用量计费规则：</label>
        <div class="col-sm-4">
            <?php foreach($amountRule as $key=>$value) : ?>
            <label class="radio-inline">
                <input type="radio" <?php if($info){echo 'disabled';}?> name="fee_amount_rule" value="{$key}" class="async_notify_radio fee_amount_rule" <?php if($key==$info['fee_amount_rule']){echo 'checked';} ?>>
                {$value}
            </label>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="form-group" id="fee_amount_price" <?php if($info&&$info['fee_amount_rule']==1){echo 'style="display: block"';}else{echo 'style="display: none"';}?>>
        <label class="col-sm-2 control-label">计费价格（元）：</label>
        <div class="col-sm-2">
            <input type="text" class="form-control" id="fee_amount_price_value" name="fee_amount_price" value="<?php if($info['fee_amount_rule']==1){echo $info['fee_price'];} ?>">
            <!--<input type="hidden" class="form-control" name="fee_step_price" value="">-->
        </div>
    </div>

    <div class="form-group"  id="fee_step_rule" <?php if($info&&$info['fee_amount_rule']==2){echo 'style="display: block"';}else{echo 'style="display: none"';}?>>
        <label class="col-sm-2 control-label">阶梯周期：</label>
        <div class="col-sm-4">
            <?php foreach($stepRule as $key=>$value) : ?>
            <label class="radio-inline">
                <input type="radio" <?php if($info){echo 'disabled';}?> name="fee_step_rule" value="{$key}" class="async_notify_radio fee_step_rule" <?php if($key==$info['fee_step_rule']){echo 'checked';} ?>>
                {$value}
            </label>
            <?php endforeach; ?>
        </div>
    </div>

    <div id="fee_step_price" <?php if($info&&$info['fee_amount_rule']==2){echo 'style="display: block"';}else{echo 'style="display: none"';}?>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格区间（元）：</label>
            <div>
                <a href="javascript:;" id="add_price" class="btn btn-primary btn-xs">添加</a>
            </div>
        </div>
        <div class="col-sm-5" style="margin-left: 200px;">
            <table class="table table-hover table-bordered" style="table-layout: fixed">
                <thead>
                <tr>
                    <th>左边界</th>
                    <th>右边界</th>
                    <th>单价（元）</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody id="step_price">
                <?php if($info){?>
                <?php foreach($info['fee_price'] as $key=>$value) : ?>
                <tr>
                    <?php foreach($value as $v) : ?>
                    <td><input type="text" value="<?php echo $v;?>"></td>
                    <?php endforeach; ?>
                    <?php if($key!=0){ ?>
                    <td><a href="javascript:;" class="btn btn-primary btn-xs delete_price">删除</a></td>
                    <?php }else{ ?>
                    <td></td>
                    <?php } ?>
                </tr>
                <?php endforeach; ?>
                <?php }else{?>
                    <td><input type="text" value="1"></td>
                    <td><input type="text" value="-1"></td>
                    <td><input type="text" value=""></td>
                    <td></td>
                <?php }?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="form-group">
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">计费开始时间：</label>
        <if condition="empty($info['start_date'])">
            <div class="col-sm-2">
                <input type="date" id="start_date" class="form-control" name="start_date" value="">
            </div>
            <else/>
            <div class="col-sm-2">
                <input type="date" id="start_date" class="form-control" name="start_date" value="{:$info['start_date']}">
            </div>
        </if>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">备注：</label>
        <div class="col-sm-4">
            <textarea type="text" id="remarks" class="form-control" name="remarks">{$info['remarks']}</textarea>
        </div>
    </div><br><br><br>
    <div style="margin-left: 400px">
        <button type="button" id="submit" class="btn btn-primary submit_button">保存</button>
        <button type="button" id="cancel" class="btn btn-default">返回</button>
    </div>
</form>

<script>
    $('#cancel').click(function () {
        window.location.href='{:U("index")}';
    });

    $(function(){
        var fee_method_this = 0
        $(".fee_method").click(function(){
            fee_method_this =  $(this).val();
            if(fee_method_this == 1){
                showTimeMethod();
            }else{
                showAmountMethod();
            }

        });
    });

    $(function(){
        var fee_amount_rule_this = 0
        $(".fee_amount_rule").click(function(){
            fee_amount_rule_this =  $(this).val();
            if(fee_amount_rule_this == 1){
                showAmountPrice();
            }else{
                showStepPrice();
            }
        });
    });

    function showTimeMethod() {
        $('#fee_amount_rule').hide();
        $('#fee_amount_price').hide();
        $('#fee_step_price').hide();
        $('#fee_time_rule').show();
        $('#fee_time_price').show();
        $('#fee_step_rule').hide();
    }
    function showAmountMethod() {
        $('#fee_time_rule').hide();
        $('#fee_time_price').hide();
        $('#fee_amount_rule').show();
        $('#fee_step_rule').hide();
    }
    function showAmountPrice() {
        $('#fee_step_price').hide();
        $('#fee_step_rule').hide();
        $('#fee_amount_price').show();
    }
    function showStepPrice() {
        $('#fee_amount_price').hide();
        $('#fee_step_rule').show();
        $('#fee_step_price').show();
    }

    $(function () {
       $('#add_price').on('click',function(){
           var s = $('#step_price').find('input').eq(-2).val();
           if(s == '-1'){
               $('#step_price').find('input').eq(-2).val('');
           }else{
               var r = parseInt(s)+1;
           }
           var html = '';
           html += '<tr>'
           if(r){
               html += '<td><input type="text" value="'+r+'"></td>';
           }else {
               html += '<td><input type="text" value=""></td>';
           }
           html += '<td><input class="line_two" type="text" value="-1"></td>';
           html += '<td><input type="text" value=""></td>';
           html += '<td>';
           html += '<a href="javascript:;" class="btn btn-primary btn-xs delete_price">删除</a>';
           html += '</td>';
           html += '</tr>';
           $('#step_price').append(html);
       }); 
    });

    $(function () {
        $(document).on("click",".delete_price",function(){
            $(this).parent().parent().remove();
        });
    });

    function nowDate() {
        var myDate = new Date();
        var year=myDate.getFullYear();
        var month=myDate.getMonth()+1;
        var date=myDate.getDate();
        return year+'-'+month+'-'+date;
    }
    
    $(function () {
        $('#submit').click(function () {
            var config_id = $('#config_id').val();
            var product_id = $('#product_id').val();
            var fee_basis = $("input[name='fee_basis']:checked").val();
            var fee_method = $("input[name='fee_method']:checked").val();
            var fee_time_rule = $("input[name='fee_time_rule']:checked").val();
            var fee_amount_rule = $("input[name='fee_amount_rule']:checked").val();
            var fee_step_rule = $("input[name='fee_step_rule']:checked").val();
            var fee_time_price = $("#fee_time_price_value").val();
            var fee_amount_price = $("#fee_amount_price_value").val();
            var start_date = $("#start_date").val();
            var remarks = $("#remarks").val();
            var today = nowDate();
            if(!fee_basis){
                alert('请选择计费依据！');return;
            }
            if(!fee_method){
                alert('请选择计费方式！');return;
            }
            if(fee_method=='1' && !fee_time_rule){
                alert('请选择时间计费规则！');return;
            }
            if(fee_method=='1' && !fee_time_price){
                alert('请填写时间计费价格！');return;
            }
            if(fee_method=='2' && !fee_amount_rule){
                alert('请填写用量计费规则！');return;
            }
            if(fee_method=='2' && fee_amount_rule=='1' && !fee_amount_price){
                alert('请填写用量计费价格！');return;
            }
            if(!start_date){
                alert('请选择计费开始时间！');return;
            }
            var s_today = new Date(today.replace(/\-/g, "\/"));
            var s_start_date = new Date(start_date.replace(/\-/g, "\/"));
//            if(s_start_date <= s_today){
//                alert('计费开始时间最早选择明日！');return;
//            }
            if(fee_method=='2' && fee_amount_rule=='2'){
                if(!fee_step_rule){
                    alert('请选择阶梯计费周期！');return;
                }
                var line_count = $('#step_price').find('input').length;
                var jsonT = '';
                var jsonLength = 0;
                var pro_value = 0;
                $('#step_price').find('input').each(function (i) {
                    line_value = $(this).val();
                    if(!line_value){
                        alert('阶梯价格配置不能为空！');return false;
                    }
                    if(i==0 && line_value != '1'){
                        alert('左边界第一位必须为1');return false;
                    }
                    if(i+2==line_count && line_value != '-1'){
                        alert('右边界最后一位必须为-1');return false;
                    }
                    if (i%3!=2 && !(/(^[1-9]\d*$)/.test(line_value)) && i+2!=line_count) {
                        alert('阶梯价格边界只能为正整数！');
                        return false;
                    }
                    if (i%3==2 && !(/^\d+(\.\d+)?$/.test(line_value))) {
                        alert('阶梯价格只能为浮点数！');
                        return false;
                    }
                    if(i%3==0 && i!=0){
                        pro_value = $('#step_price').find('input').eq(i-2).val();
                        if(parseInt(pro_value)+1 != parseInt(line_value)){
                            alert('阶梯价格边界设置有误！');
                            return false;
                        }
                    }
                    if (i%3==0){
                        if(i==0){
                            jsonT +=''+line_value;
                        }else{
                            jsonT +='|'+line_value;
                        }

                    }else if (i%3==1){
                        jsonT +=':'+line_value;
                    }else if (i%3==2){
                        jsonT +=':'+line_value;
                    }
                    jsonLength++;
                });
                if(jsonLength==line_count){
                    var fee_step_price = jsonT;
                }else{
                    return false;
                }
            }
            DHB.ajax({
                url:"{:U('Home/Cuishou/configFee', ['id'=>$list['id']])}",
                type:'post',
                data:{
                    "id":config_id,
                    "product_id":product_id,
                    "fee_basis":fee_basis,
                    "fee_method":fee_method,
                    "fee_time_rule":fee_time_rule,
                    "fee_amount_rule":fee_amount_rule,
                    "fee_step_rule":fee_step_rule,
                    "fee_time_price":fee_time_price,
                    "fee_amount_price":fee_amount_price,
                    "fee_step_price":fee_step_price,
                    "start_date":start_date,
                    "remarks":remarks
                },
                success:function(r){
                    if(r.status=='tip_success'){
                        window.location.reload();
                    }
                }
            });
        });
    });
</script>
</body>
</html>

