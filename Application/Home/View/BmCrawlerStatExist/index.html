<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row-first { margin-bottom: 10px; }
        .row-first label, .row-second label { margin-left: 10px;}
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" style="padding-bottom: 10px">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label">开始日期：</label>
                        <input type="date" name="begin" id="begin" class="form-control" value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label">结束日期：</label>
                        <input type="date" name="end" id="end" class="form-control" value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li>
                                <input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询">
                            </li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="row-second">
                    <div class="form-group">
                        <label class="control-label">签约状态</label>&nbsp;
                        <select class="form-control" name="contract_status" id="contract_status">
                            <option value="" selected>全部</option>
                            <foreach name="contract_status" item="val" key="k">
                                <option value="{$k}" <?= ($input['contract_status'] == $k) ? 'selected' : '' ;?>>{$val}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label">选择客户</label>&nbsp;
                        <select class="form-control" id="account_id" name="account_id">
                            <option value="">全部</option>
                            <?php foreach($list_account as $account) {?>
                            <option value="<?= $account['id']; ?>" <?= (isset($input['account_id']) && $input['account_id'] == $account['id']) ? 'selected' : ''?> ><?= $account['name']; ?></option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label">选择账号</label>&nbsp;
                        <select class="form-control" id="choose_user_sel" name="id">
                            <option value="">全部</option>
                            <foreach name="list_product" item="vo">
                                <option value="{$vo['id']}" <?= (isset($input['id']) && ($input['id'] == $vo['id'])) ? 'selected' : ''?>>{$vo['developer']}</option>
                            </foreach>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <div class="panel-heading"><h3 class="panel-title">账号列表</h3></div>
        <table class="table table-hover table-bordered" style="table-layout: fixed">
            <thead>
            <tr>
                <th>账号ID</th>
                <th>账号名称</th>
                <th>客户名称</th>
                <th>总查询量</th>
                <th>有效查询量</th>
                <th>有效查询率</th>
                <th>报告生成量</th>
                <th>报告生成率</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td colspan="2"></td>
                <td style="text-align: center">总计</td>
                <td><?= isset($total_data['total_nums']) ? $total_data['total_nums'] : 0; ?></td>
                <td><?= isset($total_data['authen_nums']) ? $total_data['authen_nums'] : 0; ?></td>
                <td><?= isset($total_data['authen_pct']) ? $total_data['authen_pct'] : '0.00%';?></td>
                <td><?= isset($total_data['report_nums']) ? $total_data['report_nums'] : 0; ?></td>
                <td><?= isset($total_data['report_pct']) ? $total_data['report_pct'] : '0.00%'; ?></td>
            </tr>
            <?php foreach ($list as $key => $value): ?>
            <tr>
                <td>{$value['id']}</td>
                <td><a href="{:U('detail',['id'=>$value['id']])}">{$value['developer']}</a></td>
                <td><?= isset($value['name_account']) ? $value['name_account'] : ''; ?></td>
                <td><?= isset($value['total_nums']) ? $value['total_nums'] : 0; ?></td>
                <td><?= isset($value['authen_nums']) ? $value['authen_nums'] : 0; ?></td>
                <td><?= isset($value['authen_pct']) ? $value['authen_pct'] : '0.00%';?></td>
                <td><?= isset($value['report_nums']) ? $value['report_nums'] : 0; ?></td>
                <td><?= isset($value['report_pct']) ? $value['report_pct'] : '0.00%'; ?></td>
            </tr>
            <?php endforeach ?>
            </tbody>
        </table>
    </div>

    <nav>
        <ul class="pagination">
            {$page}
        </ul>
    </nav>

</div>

<script type="text/javascript">
$(function () {
    $("#choose_user_sel").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择账号',
        width: '200px'
    });

    $("#account_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择客户',
        width: '200px'
    });

    // export file
    $("#file_export").click(function () {
        if (!checkTime()) {
            return false;
        }
        var params = genParamsForFile();
        var url_export = '/Home/BmCrawlerStatExist/downloadList' + params;
        $.fileDownload(url_export);
        return false;
    });
});
// 为导出文件生成参数
function genParamsForFile() {
    var params = '';
    var begin = $('#begin').val();
    var end = $('#end').val();
    var status = $('#status').val();
    var contract_status = $('#contract_status').val();
    var choose_user_sel = $('#choose_user_sel').val();
    var account_id = $('#account_id').val();

    if (begin) {
        params += '&begin=' + begin;
    }
    if (end) {
        params += '&end=' + end;
    }
    if (choose_user_sel) {
        params += '&id=' + choose_user_sel;
    }
    if (contract_status) {
        params += '&contract_status=' + contract_status;
    }
    if (status) {
        params += '&status=' + status;
    }
    if (account_id) {
        params += '&account_id='+account_id;
    }

    // tidy url
    if (params) {
        params = params.replace('&', '?');
    }
    return params;
}

function checkTime() {
    // get begin time and end time
    var time_begin = $('#begin').val();
    var time_end = $('#end').val();
    var today_str = (new Date()).toDateString();

    // change time format for firefox
    time_end = time_end.replace(/\-/g, '\/');
    time_begin = time_begin.replace(/\-/g, '\/');

    // set default time
    if (!time_begin) {
        time_begin = today_str;
    }

    if (!time_end) {
        time_end = today_str;
    }

    // check time
    // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
    var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
    if (time_diff < 0) {
        alert('开始时间必须小于结束时间');
        return false;
    }

    // calculate the days between begin and end
    var day_diff = Math.floor(time_diff / 8.64e7);

    //  the time should less than 365
    if (day_diff <= 365) {
        return true;
    } else {
        alert('单次查询时间范围不能超过365天');
        return false;
    }
}
</script>
</body>
</html>
