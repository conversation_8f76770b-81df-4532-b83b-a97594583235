<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__highcharts.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>

    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }

        .tooltip-show {
            width: 100%;
            height: 800px;
        }

    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div id="coverage_app">
    <coverage_list_template user_list_coming='<?= $list_account; ?>'
                            params_init='<?= $params_init ?>'></coverage_list_template>
    <dialog_template></dialog_template>
</div>

<script id="coverage_list" type="text/x-template">
    <div>
        <div class="container">
            <div class="panel panel-default">
                <div class="panel-body">
                    <form class="form-inline" @submit.prevent="formSubmit">
                        <div class="row-first">
                            <div class="form-group">
                                <label class="control-label" for="time_begin">开始时间：</label>
                                <input type="date" id="time_begin" class="form-control" v-model="begin"/>
                            </div>
                            <div class="form-group">
                                <label class="control-label" for="time_end">结束时间：</label>
                                <input type="date" id="time_end" class="form-control" v-model="end"/>
                            </div>
                            <div class="form-group pull-right">
                                <ul class="list-inline">
                                    <li><input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询"></li>
                                    <li>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                客户覆盖率 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a @click.prevent="productCharts" class="btn btn-sm">账号覆盖率</a></li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li><a href="/Home/CuishouStat/index" class="btn btn-info btn-sm">催收分统计列表</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="row-second">
                            <div class="form-group">
                                <label for="type" class="control-label">客户类型：</label>
                                    <select  v-model="type" id="type" class="form-control">
                                        <option value="">全部类型</option>
                                        <option value="1">数据公司</option>
                                        <option value="2">风控系统</option>
                                        <option value="3">综合类</option>
                                        <option value="4">p2p</option>
                                        <option value="5">现金分期</option>
                                        <option value="6">消费金融（3C)</option>
                                        <option value="7">农商行</option>
                                        <option value="8">汽车金融</option>
                                        <option value="9">消费金融</option>
                                        <option value="10">其他</option>
                                    </select>
                            </div>

                            <div class="form-group form-inline">
                                <label class="control-label">选择客户：</label>
                                <select_template :options="list_user_options" :selected.sync="id" description="全部客户"></select_template>
                            </div>
                            <div class="form-group">
                                <label class="control-label" for="data_range">数据范围：</label>
                                <select v-model="data_range" class="form-control" id="data_range"
                                        @change.prevent="dataRangeChange">
                                    <option value="4">Top3</option>
                                    <option value="6">Top5</option>
                                    <option value="11">Top10</option>
                                    <option value="16">Top15</option>
                                    <option value="21">Top20</option>
                                    <option value="26">Top25</option>
                                    <option value="31">Top30</option>
                                    <option value="all">全部</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="panel-title" style="text-align: center">
                        催收覆盖率
                    </div>
                </div>
                <div class="panel-body">
                    <div id="coverage_dunning" class="tooltip-show"></div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="panel-title" style="text-align: center">
                        疑似催收覆盖率
                    </div>
                </div>
                <div class="panel-body">
                    <div id="coverage_not_sure_dunning" class="tooltip-show" contenteditable="true"></div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="panel-title" style="text-align: center">
                        整体催收覆盖率
                    </div>
                </div>
                <div class="panel-body">
                    <div id="coverage_all" class="tooltip-show"></div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="panel-title" style="text-align: center">
                        同时催收覆盖率
                    </div>
                </div>
                <div class="panel-body">
                    <div id="coverage_both" class="tooltip-show"></div>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    Vue.component('coverage_list_template', {
        template: '#coverage_list',
        data: function () {
            return {
                begin: '',
                end: '',
                type: '',
                id: '',
                data_range: 11,
                chart_info: {},
                user_list: {}
            }
        },
        props: ['user_list_coming', 'params_init'],
        created: function () {
            // 初始化参数
            this.initParams();

            // 初始化折线图
            this.initLineChart();
        },
        computed: {
            // 将用户转成select2组件需要的样子
            list_user_options: function () {
                return this.user_list.map(function (item_user) {
                    item_user.text = item_user.name;
                    return item_user;
                });
            }
        },
        methods: {
            // 初始化参数
            initParams: function () {
                this.user_list = JSON.parse(this.user_list_coming);
                var params_coming = JSON.parse(this.params_init);
                this.begin = params_coming.begin;
                this.end = params_coming.end;
            },
            //
            productCharts: function () {
                var url_list = '/Home/CuishouStatLineChart/coverageList?begin=' + this.begin + '&end='+this.end;
                window.location.href =  url_list;
            },
            // 表单提交事件
            formSubmit: function () {
                var params = {
                    begin: this.begin,
                    end: this.end,
                    type: this.type,
                    id: this.id
                };

                var url = '/Home/CuishouStatLineChart/lineChartClient';
                var vm = this;
                this.$http.post(url, params, {responseType: 'json'}).then(function (response) {
                    console.log(response.body.info);

                    if (response.body.status === 'success') {
                        // 初始化折线图
                        vm.chart_info = response.body.info;
                        vm.genLineChart();
                    } else {
                        modalExport('意外出错,请稍后刷新后重试');
                    }


                }, function (response) {
                    console.log('获取折线图的信息出错');
                    console.log(response);
                });
            },

            // 初始化折线图
            initLineChart: function () {
                this.formSubmit();
            },

            // 生成折线图
            genLineChart: function () {
                var xAxis_data = this.chart_info.list_coverage.date_range;
                var series_dunning = this.chart_info.list_coverage.coverage_dunning;
                var series_not_user_dunning = this.chart_info.list_coverage.coverage_not_sure;
                var series_all = this.chart_info.list_coverage.coverage_all;
                var series_both = this.chart_info.list_coverage.coverage_both;

                // 如果没有查到数据 则给出提示
                if (!series_dunning || !series_not_user_dunning || !series_all || !series_both) {
                    modalExport('当前条件没有查到数据；请日更换查询条件');
                    return true;
                }

                // 限定数据范围 (如果没有查到数据 自然没有办法进行切割)
                if (this.data_range !== 'all') {
                    series_dunning = series_dunning.slice(0, this.data_range);
                    series_not_user_dunning = series_not_user_dunning.slice(0, this.data_range);
                    series_all = series_all.slice(0, this.data_range);
                    series_both = series_both.slice(0, this.data_range);
                }

                // 生成折线图 highCharts
                genLineChart(xAxis_data, series_dunning, 'coverage_dunning');
                genLineChart(xAxis_data, series_not_user_dunning, 'coverage_not_sure_dunning');
                genLineChart(xAxis_data, series_all, 'coverage_all');
                genLineChart(xAxis_data, series_both, 'coverage_both');
            },
            // 数据范围发生了变化
            dataRangeChange: function () {
                this.genLineChart();
            }
        }
    });

    new Vue({
        el: '#coverage_app'
    });

    /*
    *  生成折线图(highCharts)
    *  xAxis_categories X坐标
    *  series 数据列(name,data)
    *  html_id （生成折线图的id）
    *
    * */
    function genLineChart(xAxis_categories, series, html_id) {
        // 图表配置
        var options = {
            credits: {
                enabled: false
            },
            chart: {
                type: 'line'                          //指定图表的类型，默认是折线图（line）
            },
            title: {
                text: null                 // 标题
            },
            tooltip: {
                backgroundColor: '#FCFFC5',   // 背景颜色
                borderColor: 'black',         // 边框颜色
                borderRadius: 10,             // 边框圆角
                borderWidth: 1,               // 边框宽度
                shadow: true,                 // 是否显示阴影
                animation: true,             // 是否启用动画效果
                shared: true,                   // 共享提示框
                useHTML: true,              // 使用html解析提示框
                style: {                      // 文字内容相关样式
                    color: "#ff0000",
                    fontSize: "12px",
                    fontWeight: "blod",
                    fontFamily: "Courir new"
                },
                formatter: formatTool
            },
            xAxis: {
                categories: xAxis_categories   // x 轴分类
            },
            yAxis: {
                title: {
                    text: '覆盖率(100%)'                // y 轴标题
                },
                min: 0,
                max: 120
            },
            series: series
        };
        // 图表初始化函数
        var chart = Highcharts.chart(html_id, options);
    }

    // 格式化输出数据提示框
    function formatTool() {
        // 拼接两个td
        var mid = '';
        // 存储tr
        var tr_str = '';
        $.each(this.points, function (index, value) {
            var td_str = formatTooltip(index, this);

            mid += td_str;
            if (index % 2 === 0) {
                if (index === 0) {
                    mid = '<td>' + this.x + '<td>' + mid;
                }

                tr_str += '<tr>' + mid + '</tr>';
                mid = '';
            }
        });

        // 最后一个产品如果是单数的话需要清除
        if (mid !== '') {
            tr_str += '<tr>' + mid + '</tr>';
        }

        var table_prefix = '<div class="table-responsive">' +
            '<table class="">' +
            '<tbody>';
        var table_suffix = '</tbody>' +
            '</table>' +
            '</div>';

        return table_prefix + tr_str + table_suffix;
    }

    // 将item加工成td
    function formatTooltip(index, that) {
        var color = that.color;
        return '<td style="color:' + color + '">' + that.series.name + ': ' + that.y + '%</td>';
    }

    function checkTime() {
        // 检查开始时间和结束时间, 如果没有达到要求，则弹窗警告；如果达到要求，则返回包含两个时间的对象
        var time_obj = filterBeginAndEndTime();
        if (time_obj === false) {
            return false;
        }

        // 开始和结束时间不可以超过31天
        var result_check = timeLimitForOneTime(time_obj);
        if (result_check === false) {
            return false;
        }
    }

    /*
    * 开始和结束时间不可以超过31天
    * obj time_obj 则返回包含两个时间的对象
    *  return boolean
    * */
    function timeLimitForOneTime(time_obj) {
        var time_begin = time_obj.time_begin;
        var time_end = time_obj.time_end;

        // 单词查询不可以超过31天
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));

        if (time_diff < 0) {
            modalExport('开始时间必须小于结束时间');
            return false;
        }

        // calculate the days between begin and end
        var day_diff = Math.floor(time_diff / 8.64e7);

        //  the time should less than 31
        if (day_diff <= 91) {
            return true;
        } else {
            modalExport('单次查询时间范围不能超过92天');
            return false;
        }
    }

    /*
    * 检查开始时间和结束时间, 如果没有达到要求，则弹窗警告；如果达到要求，则返回包含两个时间的对象
    *
    * */
    function filterBeginAndEndTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();

        // change time format for firefox
        time_end = time_end.replace(/\-/g, '\/');
        time_begin = time_begin.replace(/\-/g, '\/');

        // check begin time
        if (!time_begin && time_end) {
            modalExport('请选择开始时间');
            return false;
        }

        // check end time
        if (time_begin && !time_end) {
            modalExport('请选择结束时间');
            return false;
        }
        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            modalExport('请选择有效的结束时间');
            return false;
        }

        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }
        if (!time_end) {
            time_end = today_str;
        }

        return {
            time_end: time_end,
            time_begin: time_begin
        };
    }

</script>
</body>
</html>
