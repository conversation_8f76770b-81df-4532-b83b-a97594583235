<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__dataTables.css">
    <script src="__JS__jquery.dataTables.js"></script>
    <script src="__JS__jquery.dataTables.bootstrap.js"></script>
    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="cuishou_list_app">
    <dialog_template></dialog_template>
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" method="get" id="form_init">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"
                               value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d') : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control"
                               value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                    </div>

                    <div class="form-group">
                        <label for="account_id">选择客户</label>
                        <select name="account_id" id="account_id">
                            <option value="">选择客户</option>
                            <?php foreach ($list_account as $account) { ?>
                            <option value="<?= $account['id']; ?>"
                            <?= ($account['id'] == $input['account_id']) ? 'selected' : '' ?>
                            > <?= $account['name'] ?></option>
                            <?php  } ?>
                        </select>
                    </div>

                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input id="searchBtn" type="button" onclick="return selectList();" class="btn btn-primary btn-sm"
                                       value="查询"></li>
                            <li><a href="javascript:void(0);" target="_self" onclick="return coverageRequest()"
                                   class="btn btn-info btn-sm">覆盖率走势</a></li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出（列表）</button>
                            </li>
                            <li>
                                <button type="button" id="file_export_day" class="btn btn-success btn-sm">导出（按天）</button>
                            </li>
                        </ul>
                    </div>

                </div>
                <div class="row-second">
                    <div class="form-group">
                        <label class="control-label" for="contract_status">签约状态：</label>
                        <select class="form-control" name="contract_status" id="contract_status"
                                onChange="return requestUser()">
                            <option value="" selected>签约状态</option>
                            <option value="1"
                            <?= ($input['contract_status'] == '1') ? 'selected' : '' ;?>>已签约已付款</option>
                            <option value="2"
                            <?= ($input['contract_status'] == '2') ? 'selected' : '' ;?>>已签约未付款</option>
                            <option value="3"
                            <?= ($input['contract_status'] == '3') ? 'selected' : '' ;?>>未签约</option>
                            <option value="4"
                            <?= ($input['contract_status'] == '4') ? 'selected' : '' ;?>>其他</option>
                            <option value="5"
                            <?= ($input['contract_status'] == '5') ? 'selected' : '' ;?>>特殊客户</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="status">账号状态：</label>
                        <select class="form-control" name="status" id="status" onChange="return requestUser()">
                            <option value="" selected>账号状态</option>
                            <option value="1"
                            <?= ($input['status'] == '1') ? 'selected' : '' ;?>>正常</option>
                            <option value="2"
                            <?= ($input['status'] == '2') ? 'selected' : '' ;?>>禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="choose_user_sel">选择账号：</label>
                        <select class="form-control" id="choose_user_sel" name="id">
                            <option value="">选择账号</option>
                                <?php foreach($user_list as $product){ ?>
                                <option value="<?= $product['id']; ?>"
                                <?= (isset($input['id']) && $input['id'] == $product['id']) ? 'selected' : '' ?>><?= $product['developer'] ?></option>
                                <?php } ?>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr>
                <th>账号ID</th>
                <th>账号名称</th>
                <th>客户名称</th>
                <th>总调用量</th>
                <th>有效调用量</th>
                <th>催收覆盖率</th>
                <th>疑似催收覆盖率</th>
                <th>同时覆盖率</th>
                <th>整体覆盖率</th>
            </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<script type="text/javascript">

    new Vue({
        el : '#cuishou_list_app'
    });
    $(function () {
        // 初始化select2
        initSelect();

        // 导出（列表）
        exportList();

        // 导出（按天）
        exportDay();

        //初始化排序
        initDatatable();
    });

    function initDatatable() {
        var tablePrefix = "#table_server_";
        $("#table_dataTable").dataTable({
            searching: false, //是否显示搜索
            bLengthChange: false, //是否允许终端用户从一个选择列表中选择分页的页数，页数为10，25，50和100，需要分页组件bPaginate的支持
            paging: true, //是否显示分页
            bInfo: false, //页脚信息
            bAutoWidth: true, //自动宽度
            bFilter: false, //过滤功能
            aoColumns: [
                {mDataProp: 'id', defaultContent: '', asSorting: ['desc', 'asc']},
                {
                    mDataProp: 'developer', bSortable: false, defaultContent: '',
                    render: function (data, type, setting) {
                        return '<a href="/Home/CuishouStat/cuishouDetail/id/'+setting.id+'/developer/'+data+'.html">'+data+'</a>';
                    }
                },//不支持排序
                {
                    mDataProp: 'name_account', bSortable: false, defaultContent: '',
                    render: function (data, type) {
                        if (data == '总计') {
                            return '<a href="javascript:void(0);" onclick="statTotal()">总计</a>';
                        } else {
                            return data;
                        }
                    }
                },
                {mDataProp: 'access_counts', defaultContent: 0, asSorting: ['desc', 'asc']},
                {mDataProp: 'success_counts', defaultContent: 0, asSorting: ['desc', 'asc']},
                {mDataProp: 'converage_dunning', defaultContent: '0.00%', asSorting: ['desc', 'asc']},
                {mDataProp: 'converage_not_sure_dunning', defaultContent: '0.00%', asSorting: ['desc', 'asc']},
                {mDataProp: 'coverage_both', defaultContent: '0.00%', asSorting: ['desc', 'asc']},
                {mDataProp: 'converage_all', defaultContent: '0.00%', asSorting: ['desc', 'asc']}
            ],
            aaSorting: [0,'desc'],
            bServerSide: true, //分页，取数据等等的都放到服务端去
            processing: true, //载入数据的时候是否显示"载入中"
            pageLength: 30, //首次加载的数据条数
            ordering: true, //排序操作在服务端进行，所以可以关了。
            ajax: { //类似jquery的ajax参数，基本都可以用。
                type: "get", //后台指定了方式，默认get，外加datatable默认构造的参数很长，有可能超过get的最大长度。
                url: "/Home/CuishouStat/ajaxList",
                data: function (d, setting) { //d 是原始的发送给服务器的数据，默认很长。
                    var param = {}; //因为服务端排序，可以新建一个参数对象
                    param.length = d.length;
                    param.start = d.start;
                    param.order = setting.aoColumns[d.order[0]['column']]['mDataProp']+' '+d.order[0]['dir'];
                    param.page = Math.round(d.start/d.length) + 1; //页数
                    param.draw = d.draw;
                    var formData = $("#form_init").serializeArray();//把form里面的数据序列化成数组
                    formData.forEach(function (e) {
                        param[e.name] = e.value;
                    });
                    return param;//自定义需要传递的参数。
                },
                dataType: 'json',
                dataSrc: function(json) {
                    return dataTableInfo(json);//这几个参数都是datatable需要的，必须要
                },
                error: function (json) {
                    alert(json.info);
                    return false;
                }
            },
            initComplete:  function (setting, json) {
                $('#table_dataTable').dataTable().fnClearTable(); //将数据清除
                $('#table_dataTable').dataTable().fnAddData(dataTableInfo(json), true); //数据必须是json对象或json对象数组
            },
            language: {
                paginate: { //分页的样式文本内容。
                    previous: "«",
                    next: "»",
                },
                processing: '载入中',
                zeroRecords: '没有数据',
            },
        });
    }

    function dataTableInfo(msgObj)
    {
        var a=[];
        var tableName=['id','developer','name_account','access_counts','success_counts','converage_dunning','converage_not_sure_dunning','coverage_both','converage_all'];
        var total_data = msgObj['total_data'];
        var user_show = msgObj['user_show'];
        a.push(JSON.parse(JSON.stringify(total(total_data), tableName)));
        $.each(user_show, function(index, content) {
            var tempObj = {};
            tempObj.id = content.id;
            tempObj.developer = content.developer;
            tempObj.name_account = content.name_account;
            tempObj.access_counts = content.access_counts;
            tempObj.success_counts = content.success_counts;
            tempObj.converage_dunning = content.converage_dunning != 'NA' ? content.converage_dunning.toFixed(2)+'%' : 'NA';;
            tempObj.converage_not_sure_dunning = content.converage_not_sure_dunning != 'NA' ? content.converage_not_sure_dunning.toFixed(2)+'%' : 'NA';
            tempObj.coverage_both = content.coverage_both != 'NA' ? content.coverage_both.toFixed(2)+'%' : 'NA';
            tempObj.converage_all = content.converage_all != 'NA' ? content.converage_all.toFixed(2)+'%' : 'NA';
            a.push(JSON.parse(JSON.stringify(tempObj, tableName)));
        });
        return a;
    }

    function total(data)
    {
        var tempObj = {};
        tempObj.id = '';
        tempObj.developer = '';
        tempObj.name_account = '总计';
        tempObj.access_counts = data.access_counts;
        tempObj.success_counts = data.success_counts;
        tempObj.converage_dunning = data.converage_dunning != 'NA' ? data.converage_dunning.toFixed(2)+'%' : 'NA';
        tempObj.converage_not_sure_dunning = data.converage_not_sure_dunning != 'NA' ? data.converage_not_sure_dunning.toFixed(2)+'%' : 'NA';
        tempObj.coverage_both = data.coverage_both != 'NA' ? data.coverage_both.toFixed(2)+'%' : 'NA';
        tempObj.converage_all = data.converage_all != 'NA' ? data.converage_all.toFixed(2)+'%' : 'NA';
        return tempObj;
    }

    function selectList()
    {
        checkTime();
        $("#table_dataTable").DataTable().draw();
    }

    // 总计详情
    function statTotal() {
        // 检查参数
        var result_check_time = checkTime();
        if (result_check_time === false) {
            return false;
        }

        // 生成参数
        var params= genParamsForFile();
        var url = '/Home/CuishouStat/totalShow';
        window.open(url + params);
    }

    // 导出（列表）
    function exportList() {
        $("#file_export").click(function () {
            if (!checkTime()) {
                return false;
            }

            var params = genParamsForFile();
            var url_export = '/Home/CuishouStatFile/index' + params;
            $.fileDownload(url_export);
            return false;
        });
    }


    // 导出（按天）
    function exportDay() {
        $("#file_export_day").click(function () {
            if (!checkTime()) {
                return false;
            }

            var params = genParamsForFile();
            var url_export = '/Home/CuishouStat/exportByDay' + params;
            $.fileDownload(url_export);
            return false;
        });
    }

    // 初始化select2
    function initSelect() {
        // choose cuishou user
        $("#choose_user_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号'
        });

        // 客户select2
        $("#account_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户'
        });
    }

    // 为导出文件生成参数
    function genParamsForFile() {
        var params = '';
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var choose_user_sel = $('#choose_user_sel').val();
        var contract_status = $('#contract_status').val();
        var account_id = $('#account_id').val();
        var status = $('#status').val();

        if (time_begin) {
            params += '&begin=' + time_begin;
        }
        if (time_end) {
            params += '&end=' + time_end;
        }
        if (choose_user_sel) {
            params += '&id=' + choose_user_sel;
        }
        if (contract_status) {
            params += '&contract_status=' + contract_status;
        }
        if (account_id) {
            params += '&account_id=' + account_id;
        }
        if (status) {
            params += '&status=' + status;
        }

        // tidy url
        if (params) {
            params = params.replace('&', '?');
        }
        return params;
    }

    // 动态获取用户
    function requestUser() {
        var contract_status = $('#contract_status').val();
        var status = $('#status').val();
        var url = '/Home/CuishouStat/clientList';

        $.post(
            url,
            {contract_status: contract_status, status: status}
        ).success(function (response) {
            var client_info = response.info;
            var choose_user_sel = $('#choose_user_sel');
            // var first = '<option value="">选择账号</option>';
            // 保留上次select的第一个的option
            var ele_first = $('#choose_user_sel option').first();
            choose_user_sel.empty();
            choose_user_sel.append(ele_first);

            // 填充新的option
            client_info.forEach(function (element) {
                var ele_client = "<option value='" + element.id + "'>" + element.developer + "</option>";
                $('#choose_user_sel').append(ele_client);
            });

            // 重新渲染样式
            choose_user_sel.select2({
                allowClear: true,
                theme: "bootstrap",
                placeholder: '选择账号'
            });
        });
    }

    function coverageRequest() {
        // 检查时间
        var result_check_time = checkTime();
        if (result_check_time === false) {
            return false;
        }

        // 跳转覆盖率页面
        var url_params = $('#form_init').serialize();
        var url_coverage = '/Home/CuishouStatLineChart/lineChartClient';
        var url_request = url_coverage + '?' + url_params;
        window.location.href = url_request;
    }


    function checkTime() {

        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();

        // change time format for firefox
        time_end = time_end.replace(/\-/g, '\/');
        time_begin = time_begin.replace(/\-/g, '\/');

        // check begin time
        if (!time_begin && time_end) {
            modalExport('请选择开始时间');
            return false;
        }

        // check end time
        if (time_begin && !time_end) {
            modalExport('请选择结束时间');
            return false;
        }

        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            modalExport('请选择有效的结束时间');
            return false;
        }

        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }

        if (!time_end) {
            time_end = today_str;
        }

        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));

        if (time_diff < 0) {
            modalExport('开始时间必须小于结束时间');
            return false;
        }

        // calculate the days between begin and end
        var day_diff = Math.floor(time_diff / 8.64e7);

        //  the time should less than 31
        if (day_diff <= 364) {
            return true;
        } else {
            modalExport('单次查询时间范围不能超过365天');
            return false;
        }
    }

</script>
</body>
</html>
