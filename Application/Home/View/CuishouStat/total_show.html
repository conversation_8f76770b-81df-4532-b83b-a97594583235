<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }

        .form-group label {
            margin-left: 10px;
        }

        .row_first {
            margin-bottom: 20px;
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<div class="container" id="list_matching">
    <list_upgrade_template :init_params='<?= $init_params ?>'></list_upgrade_template>
    <dialog_template></dialog_template>
</div>

<script type="text/x-template" id="show_gray_upgrade">
    <div>
        <div class="row">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <span style="font-weight: bold">邦信分详单版V1总计详情</span>
                    <a class="btn btn-sm btn-primary pull-right" href="/Home/CuishouStat/index">返回列表页<i class=" icon-reply"></i></a>
                </div>
                <div class="panel-body">
                    <form class="form-inline">
                        <div class="row_first">
                            <div class="form-group">
                                <label for="begin">开始时间：</label>
                                <input type="date" id="begin" v-model="begin" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="end">结束时间：</label>
                                <input type="date" v-model="end" id="end" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="contract_status">签约状态：</label>
                                <select id="contract_status" v-model="contract_status" class="form-control">
                                    <option :value="item.value" v-for="item in list_contract_status">{{ item.name }}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status">账号状态：</label>
                                <select id="status" v-model="status" class="form-control">
                                    <option value="">账号状态</option>
                                    <option value="1">正常</option>
                                    <option value="2">禁用</option>
                                </select>
                            </div>
                            <div class="form-group pull-right">
                                <button type="submit" class="btn btn-sm btn-primary" @click.prevent="requestList"><i class="icon-search"></i> 查询</button>
                                <button class="btn btn-info btn-sm" @click.prevent="downloadFile"> <i class="icon-download-alt"></i>导出</button>
                            </div>
                        </div>
                        <div class="row-second">
                            <div class="form-group">
                                <ul class="list-inline">
                                    <li><label class="control-label">选择客户：</label></li>
                                    <li><v-select :options="list_account_options" v-model="account"></v-select></li>
                                </ul>
                            </div>
                            <div class="form-group">
                                <ul class="list-inline">
                                    <li><label class="control-label">选择账号：</label></li>
                                    <li><v-select :options="list_product_options" v-model="product"></v-select></li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <v-table
                    is-horizontal-resize
                    column-width-drag
                    :is-loading="isLoading"
                    style="width:100%"
                    :columns="tableConfig.columns"
                    :table-data="tableConfig.tableData"
                    :show-vertical-border="true"
                    row-hover-color="#eee"
                    row-click-color="#edf7ff"
                    :paging-index="(pageIndex-1)*pageSize"

            ></v-table>
            <div style="margin-top: 10px;">
                <v-pagination @page-change="pageChange" @page-size-change="pageSizeChange" :total="total" :page-size="pageSize" :layout="['total', 'prev', 'pager', 'next', 'sizer', 'jumper']"></v-pagination>
            </div>
        </div>
    </div>
    </div>
</script>

<script>
    Vue.component('list_upgrade_template', {
        template: '#show_gray_upgrade',
        props : ['init_params'],
        data: function () {
            return {
                list_contract_status :[
                    {name: '签约状态', value : ''},
                    {name: '已签约已付款', value:1},
                    {name: '已签约未付款', value:2},
                    {name: '未签约', value:3},
                    {name: '特殊客户', value:5},
                    {name: '其他', value:4}
                ],
                contract_status : '',
                status : '',
                account : {label : '全部客户', id : ''},
                id : '',
                begin:'',
                end : '',
                product : {id : '', label:'全部账号'},
                isLoading: false,
                pageIndex:1,
                pageSize:20,
                total : 0,
                tableRange : [
                ],
                tableConfig: {
                    multipleSort: false,
                    tableData: [
                    ],
                    columns: [
                        {field : 'date_key', title : '日期', width :100, columnAlign: 'center', isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'access_counts', title : '总调用量', width: 100, columnAlign: 'center', isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'success_counts', title : '有效调用量', width: 100, columnAlign: 'center',isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'converage_dunning', title : '催收覆盖率', width: 100, columnAlign: 'center',isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'converage_not_sure_dunning', title : '疑似催收覆盖率', width: 100, columnAlign: 'center',isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'coverage_both', title : '同时覆盖率', width: 100, columnAlign: 'center',isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                        {field: 'converage_all', title : '整体覆盖率', width: 100, columnAlign: 'center',isFrozen: true,isResize:true, titleCellClassName: 'title_column'},
                    ]
                }
            }
        },
        mounted : function () {
            // 初始化页面
            this.initParams();

            // 请求数据
            this.requestList();
        },
        computed : {
            list_account_options : function () {
                var list_account = Object.values(this.init_params.list_account).map(function(item){
                    item.label = item.name;
                    return item;
                });

                list_account.unshift({label:'全部客户', id:''});
                return list_account;
            },
            list_product_options : function () {
                var list_product = Object.values(this.init_params.list_product).map(function(item){
                    item.label = item.developer;
                    return item;
                });
                list_product.unshift({id : '', label:'全部账号'});
                return list_product;
            }
        },
        methods: {
            downloadFile : function(){
                if (this.checkTime() === false) {
                    return false;
                }

                var params = {
                    begin: this.begin,
                    end: this.end,
                    product_id : (this.product === null || this.product.id === undefined ) ? '' : this.product.id,
                    account_id : (this.account === null || this.account.id === undefined) ? '' : this.account.id,
                    status:this.status,
                    contract_status : this.contract_status
                };
                var url_export = '/Home/CuishouStat/downloadTotalShow';
                $.fileDownload(url_export, {
                    data : params,
                    httpMethod : 'POST'
                });
                return false;
            },
            checkTime: function() {
                // get begin time and end time
                var time_begin = this.begin;
                var time_end = this.end;
                var today_str = (new Date()).toDateString();

                // change time format for firefox
                time_end = time_end.replace(/\-/g, '\/');
                time_begin = time_begin.replace(/\-/g, '\/');

                // check begin time
                if (!time_begin && time_end) {
                    modalExport('请选择开始时间');
                    return false;
                }

                // check end time
                if (time_begin && !time_end) {
                    modalExport('请选择结束时间');
                    return false;
                }

                if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
                    modalExport('请选择有效的结束时间');
                    return false;
                }

                // check time
                // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
                var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));

                if (time_diff < 0) {
                    modalExport('开始时间必须小于结束时间');
                    return false;
                }

                // calculate the days between begin and end
                var day_diff = Math.floor(time_diff / 8.64e7);

                //  the time should less than 31
                if (day_diff <= 364) {
                    return true;
                } else {
                    modalExport('单次查询时间范围不能超过365天');
                    return false;
                }
            },
            // 初始化参数
            initParams: function(){
                var request_params = this.init_params.request_params;

                // 初始化 时间 && 协议 && 状态
                this.end = request_params.end;
                this.begin = request_params.begin;

                if (request_params.contract_status !== undefined) {
                    this.contract_status = request_params.contract_status;
                }
                if (request_params.status !== undefined) {
                    this.status = request_params.status;
                }

                // 初始化客户 && 产品
                if (request_params.account_id !== undefined) {
                    var account_id = request_params.account_id;
                    this.account = this.init_params.list_account[account_id];

                }
                if (request_params.id !== undefined) {
                    var product_id = request_params.id;
                    this.product = this.init_params.list_product[product_id];
                }
            },

            // 请求数据
            requestList: function () {
                if (this.checkTime() === false) {
                    return false;
                }

                this.isLoading = true;
                var url = '/Home/CuishouStat/totalShow';
                var params = {
                    begin: this.begin,
                    end: this.end,
                    product_id : (this.product === null || this.product.id === undefined ) ? '' : this.product.id,
                    account_id : (this.account === null || this.account.id === undefined) ? '' : this.account.id,
                    status:this.status,
                    contract_status : this.contract_status
                };
                var vm = this;
                this.$http.post(url, params, {responseType: 'json'}).then(function (response) {
                    console.log(response);
                    if (response.body.status === 0) {
                        vm.isLoading = false;
                        var list_range = Object.values(response.body.list_info);
                        vm.tableRange = list_range;
                        vm.total = list_range.length;
                        vm.getTableData();
                    }
                });
            },
            // 重置当前页展示的数据
            getTableData : function(){
                this.tableConfig.tableData = this.tableRange.slice((this.pageIndex-1)*this.pageSize,(this.pageIndex)*this.pageSize)
            },
            // 换页重置数据
            pageChange: function(pageIndex){

                this.pageIndex = pageIndex;
                this.getTableData();
            },
            // 修改每页展示的条数
            pageSizeChange : function(pageSize){

                this.pageIndex = 1;
                this.pageSize = pageSize;
                this.getTableData();
            }
        },
        events: {
            'vuetable:action': function (action, data) {
                console.log('vuetable:action', action, data);
                if (action === 'view-item') {
                    this.viewProfile(data.id)
                }
            },
            'vuetable:load-error': function (response) {
                console.log('Load Error: ', response)
            }
        }
    });

    new Vue({
        el: "#list_matching"
    });


</script>

</body>
</html>
