<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <!-- Include Required Prerequisites -->
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <script src="__JS__highcharts.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">

    <style>
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info"/>
<include file="Common@Public/header"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get" id="form_coverage">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间：</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"
                               value="<?= (!isset($input['begin']) || !$input['begin']) ? date('Y-m-d', strtotime('-30 days')) : $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间：</label>
                        <input type="date" name="end" id="time_end" class="form-control"
                               value="<?= (!isset($input['end']) || !$input['end']) ? date('Y-m-d') : $input['end']; ?>"/>
                    </div>

                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm"
                                       value="查询"></li>
                            <li>
                                    <a class="btn btn-info btn-sm" id="you_know" href="{:U('index')}" role="button">返回账号列表</a>
                            </li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="row-second">
                    <div class="form-group">
                        <label class="control-label" for="choose_user_sel">选择账号：</label>
                        <select class="form-control" id="choose_user_sel" name="id">
                            <?php if (isset($input['developer'])) { ?>
                            <option value="{$input['id']}">{$input['developer']}</option>
                            <?php } ?>
                            <option value="">选择账号</option>
                            <?php var_dump($user_list); ?>
                            <?php foreach ($user_list as $user) {?>
                            <option value="<?= $user['id'] ?>"><?= $user['developer'] ?></option>
                            <?php } ?>
                        </select>
                    </div>

                </div>
            </form>
        </div>
    </div>
</div>
<!-- 引入highCharts 开始-->
<div class="container" style="text-align: center;width: 100%;margin-bottom: 10px">
    <div id="coverage" style="min-width:80%;height:400px; "></div>
    <div class="message"></div>
</div>
<!-- 折线图结束 -->

<div class="container">
    <div class="panel panel-default">
        <table class="table table-hover table-bordered">
            <thead>
            <tr align="center">
                <th>时间</th>
                <th>总调用量</th>
                <th>有效调用量</th>
                <th>催收覆盖率</th>
                <th>疑似催收覆盖率</th>
                <th>同时覆盖率</th>
                <th>整体覆盖率</th>
            </tr>
            </thead>
            <tbody id="tbody">
            </tbody>
        </table>
    </div>
</div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        // 用户select2初始化功能
        userSelect2Init();

        // 文件导出事件
        fileDownload();

        // 表单提交事件
        formSubmit();

        // 初始化表单
        formInit();
    });

    // 展示表格数据
    function showDataForTable(info) {
        var total_data = info.total_data;
        var date_stat_show = info.date_stat_show;

        // 生成总计数据
        genTotalDataForTable(total_data);

        // 生成每天的数据
        genDayDataForTable(date_stat_show);
    }

    // 为表格生成天数据
    function genDayDataForTable(date_stat_show) {
        $.map(date_stat_show, genDayItem);
    }

    // 为表格生成每天的数据
    function genDayItem(item, date) {
        var tr_html = '<tr>\n' +
            '                <td>' + date + '</td>\n' +
            '                <td>' + item.access_counts + '</td>\n' +
            '                <td>' + item.success_counts + '</td>\n' +
            '                <td>' + item.converage_dunning + '%</td>\n' +
            '                <td>' + item.converage_not_sure_dunning + '%</td>\n' +
            '                <td>' + item.coverage_both + '%</td>\n' +
            '                <td>' + item.converage_all + '%</td>\n' +
            '            </tr>';

        $('#tbody').append(tr_html);
    }


    // 生成总计数据
    function genTotalDataForTable(total_data) {
        $('#tbody').empty();
        var tr_html = '<tr>\n' +
            '                <td>总计</td>\n' +
            '                <td>' + total_data.access_counts + '</td>\n' +
            '                <td>' + total_data.success_counts + '</td>\n' +
            '                <td>' + total_data.converage_dunning + '%</td>\n' +
            '                <td>' + total_data.converage_not_sure_dunning + '%</td>\n' +
            '                <td>' + total_data.coverage_both + '%</td>\n' +
            '                <td>' + total_data.converage_all + '%</td>\n' +
            '            </tr>';
        $('#tbody').append(tr_html);
    }

    // 初始化表单
    function formInit() {
        var url = '/Home/CuishouStat/ajaxDetail';
        var params = $('#form_coverage').serialize();

        $.get({
            url: url,
            data: params,
            dataType: 'json',
            cache: true

        }).success(function (response) {
            if (response.status !== 'success') {
                alert('页面刷新失败,请刷新页面后重试');
            }
            console.log(response);
            // 生成表格数据
            showDataForTable(response.info);

            // 生成折线图数据
            genLineChart(response.info);

        }).error(function (response) {
            alert('页面刷新失败,请刷新页面后重试');
        });
    }

    // 生成折线图
    function genLineChart(response) {
        var xAxis_categories = response.date_line_chart;
        var series = response.series;

        // 图表配置
        var options = {
            credits: {
                enabled: false
            },
            chart: {
                type: 'line'                          //指定图表的类型，默认是折线图（line）
            },
            title: {
                text: null                 // 标题
            },
            tooltip: {
                backgroundColor: '#FCFFC5',   // 背景颜色
                borderColor: 'black',         // 边框颜色
                borderRadius: 10,             // 边框圆角
                borderWidth: 1,               // 边框宽度
                shadow: true,                 // 是否显示阴影
                animation: true,             // 是否启用动画效果
                shared:true,                   // 共享提示框
                valueSuffix:'%',                   // 后缀
                style: {                      // 文字内容相关样式
                    color: "#ff0000",
                    fontSize: "12px",
                    fontWeight: "blod",
                    fontFamily: "Courir new"
                }
            },
            xAxis: {
                categories: xAxis_categories   // x 轴分类
            },
            yAxis: {
                title: {
                    text: '覆盖率(100%)'                // y 轴标题
                },
                min: 0,
                max: 100
            },
            series: series
        };
        // 图表初始化函数
        var chart = Highcharts.chart('coverage', options);
    }

    // 表单提交事件
    function formSubmit() {

        $('#form_coverage').submit(function () {
            // 屏蔽表单的默认提交事件
            event.preventDefault();

            // 检查时间是否规范
            var result_time = checkTime();
            if (result_time === false) {
                return false;
            }

            // 提交表单
            var url = '/Home/CuishouStat/ajaxDetail';
            var params = $(this).serialize();
            $.get({
                url: url,
                data: params,
                dataType: 'json',
                cache: true

            }).success(function (response) {
                if (response.status !== 'success') {
                    alert('页面刷新失败,请刷新页面后重试');
                }
                console.log(response);

                // 生成表格数据
                showDataForTable(response.info);

                // 生成折线图数据
                genLineChart(response.info);

            }).error(function (response) {
                alert('页面刷新失败,请刷新页面后重试');
            });

        });
    }

    // 用户select2初始化功能
    function userSelect2Init() {
        $("#choose_user_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号'
        });
    }

    // 文件导出功能
    function fileDownload() {
        // export file
        $("#file_export").click(function () {
            if (!checkTime()) {
                return false;
            }

            // init params
            var params = '';
            var time_begin = $('#time_begin').val();
            var time_end = $('#time_end').val();
            var choose_user_sel = $('#choose_user_sel').val();

            if (time_begin) {
                params = params + '&begin=' + time_begin;
            }
            if (time_end) {
                params = params + '&end=' + time_end;
            }
            if (choose_user_sel) {
                params = params + '&id=' + choose_user_sel;
            }

            // tidy url
            if (params) {
                params = params.replace('&', '?');
            }
            var url_export = '/Home/CuishouStatFile/cuishouDetail' + params;
            $.fileDownload(url_export);
            return false;
        });
    }

    // 检查时间
    function checkTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var today_str = (new Date()).toDateString();
        var user_choose = $('#choose_user_sel').val();

        // check begin time
        if (!time_begin && time_end) {
            alert('请选择开始时间');
            return false;
        }

        // check end time
        if (time_begin && !time_end) {
            alert('请选择结束时间');
            return false;
        }

        if (time_end && (Date.parse(time_end + ' GMT +8') - Date.parse(today_str + ' GMT +8') > 0)) {
            alert('请选择有效的结束时间');
            return false;
        }

        // set default time
        if (!time_begin) {
            time_begin = today_str;
        }

        if (!time_end) {
            time_end = today_str;
        }

        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }

        // calculate the days between begin and end
        var day_diff = Math.floor(time_diff / 8.64e7);

        //  the time should less than 31
        if (day_diff > 364) {
            alert('单次查询时间范围不能超过365天');
            return false;
        }

        if (!user_choose) {
            alert('请选择账号');
            return false;
        }

        return true;
    }
</script>
</body>
</html>
