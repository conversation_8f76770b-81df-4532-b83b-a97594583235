<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div class="row">
        <div class="panel panel-default">
            <div class="panel-heading">
                <span>邦企查账号列表</span> <a class="btn btn-sm btn-primary  pull-right" href="/Home/BangProduct/add">添加邦企查账号</a>
            </div>
            <div class="panel-body">
                <form class="form-inline" action="/Home/BangProduct/index" method="get">
                    <div class="form-group">
                        <label for="status">账号状态：</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">选择状态</option>
                            <option value="1"
                            <?= (isset($request_params['status']) && $request_params['status'] == 1) ? 'selected' : ''  ?>
                            >可用</option>
                            <option value="2"
                            <?= (isset($request_params['status']) && $request_params['status'] == 2) ? 'selected' : ''  ?>
                            >禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="id">账号ID：</label>
                        <input class="form-control" type="text" name="id" id="id"
                               value="<?= isset($request_params['id']) ? $request_params['id'] : ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="choose_product_sel">账号：</label>
                        <select name="choose_id" id="choose_product_sel" class="form-control">
                            <option value="">选择账号</option>
                            <?php foreach ($list_all_product as $product) {?>
                            <option value="<?= $product['id'] ?>"
                            <?= (isset($request_params['choose_id']) && $request_params['choose_id'] == $product['id']) ? 'selected' : ''  ?>
                            > <?= $product['name'];?>
                            <?php }?>
                            </option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="apikey">APIKEY：</label>
                        <input class="form-control" type="text" id="apikey" name="apikey"
                               value="<?= isset($request_params['apikey']) ? $request_params['apikey'] : ''; ?>">
                    </div>
                    <div class="form-group pull-right">
                        <input class="btn btn-sm btn-primary" type="submit" value="查询">
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="table-responsive">
            <table class="table table-hover table-bordered" style="table-layout:fixed;word-break:break-all; word-wrap:break-all;">
                <thead>
                <tr>
                    <th>账号ID</th>
                    <th>客户名称</th>
                    <th>账号名称</th>
                    <th>账号状态</th>
                    <th>APIKEY</th>
                    <th>备注</th>
                    <th>时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($list_product as $product) { ?>
                <tr>
                    <td><?= $product['id'] ?></td>
                    <td><?= $product['name_account'] ?></td>
                    <td><?= $product['name'] ?></td>
                    <td><?= ($product['status'] == 1) ? '可用' : '禁用' ?></td>
                    <td><?= $product['apikey'] ?></td>
                    <td><?= $product['remark'] ?></td>
                    <td>
                        <div>start:&nbsp;<?= date('Y-m-d H:i:s',$product['created_at']) ?></div>
                        <div>&nbsp;end:&nbsp;<?= date('Y-m-d H:i:s',$product['expiration_date']) ?></div>

                    </td>
                    <td>
                        <a href="/Home/BangProduct/edit?id=<?= $product['id'];?>" class="btn btn-info btn-xs">编辑</a>
                        <a href="/Home/BangProduct/feeConfig?id=<?= $product['id'];?>" class="btn btn-primary btn-xs">计费配置</a>
                    </td>
                </tr>
                <?php }?>
                </tbody>
            </table>
            <nav>
                <ul class="pagination">
                    <?= $page_show ?>
                </ul>
            </nav>
        </div>
    </div>
</div>
<script>
    $("#choose_product_sel").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择账号'
    });
</script>
</body>
</html>
