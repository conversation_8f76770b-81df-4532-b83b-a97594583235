<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }

        .label_top {
            margin-top:5px
        }

    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container" id="bang_product_add">
    <div class="row">
        <div class="panel panel-default">
            <div class="panel-heading">
                <span>邦企查账号列表</span> <a class="btn btn-sm btn-primary pull-right" href="/Home/BangProduct/index">账号列表</a>
            </div>
            <div class="panel-body">
                <dialog_template></dialog_template>
                <bang_add_template init_params='<?= $init_params ?>'></bang_add_template>
            </div>
        </div>
    </div>
</div>

<template type="text/x-template" id="form_add_bang">

    <form action="" class="form-horizontal" @submit.prevent="addProduct">
        <div class="form-group">
            <label for="name" class="col-sm-2 control-label">账号名称：</label>
            <div class="col-sm-4">
                <input class="form-control" type="text" v-model="form_params.name" id="name">
            </div>
        </div>

        <div class="form-group">
            <label for="status" class="col-sm-2 control-label">状态：</label>
            <div class="col-sm-4">
                <select id="status" class="form-control" v-model="form_params.status">
                    <option value="1">正常</option>
                    <option value="2">禁用</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-sm-2">所属客户</label>
            <div class="col-sm-4">
                <v-select :options="list_account" v-model="form_params.account"></v-select>
            </div>
        </div>

        <div class="form-group">
            <label for="contract_status" class="control-label col-sm-2">签约状态：</label>
            <div class="col-sm-4">
                <select id="contract_status" v-model="form_params.contract_status" class="form-control">
                    <option :value="item.value" v-for="item in form_params.contract_status_list">{{ item.name}}</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">限额类型：</label>
            <div class="col-sm-4">
                <label class="radio-inline" v-for="(limit_show, index) in form_params.limit_type_list">
                    <input type="radio" v-model="form_params.limit_type" :value="limit_show.value"> {{ limit_show.name }}
                </label>
            </div>
        </div>

        <div class="form-group" v-if="form_params.limit_type">
            <label for="limit_num" class="col-sm-2 control-label">限额数量：</label>
            <div class="col-sm-4">
                <input type="text" v-model="form_params.limit_num" id="limit_num" class="form-control">
            </div>
        </div>

        <div class="form-group">
            <label for="limit_total" class="col-sm-2 control-label">总量：</label>
            <div class="col-md-4">
                <input type="text" v-model="form_params.limit_total" id="limit_total" class="form-control">
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-sm-2">号码加密方式：</label>
            <div class="col-md-4">
                <label class="radio-inline">
                    <input type="radio" v-model="form_params.encrypt_way"  value="0"> 明文
                </label>
                <label class="radio-inline">
                    <input type="radio" v-model="form_params.encrypt_way"  value="1"> MD5
                </label>
                <label class="radio-inline">
                    <input type="radio" v-model="form_params.encrypt_way"  value="2"> SM3
                </label>
            </div>
        </div>

        <div class="form-group">
            <label for="expiration_date" class="col-sm-2 control-label">截至时间：</label>
            <div class="col-sm-4">
                <input type="date" v-model="form_params.expiration_date" id="expiration_date" class="form-control">
            </div>
        </div>

        <div class="form-group">
            <label for="limit_second" class="col-sm-2 control-label">秒并发：</label>
            <div class="col-sm-4">
                <input type="text" v-model="form_params.limit_second" id="limit_second" class="form-control">
            </div>
        </div>

        <div class="form-group">
            <label for="limit_ip" class="col-sm-2 control-label">IP白名单：</label>
            <div class="col-sm-4">
                <textarea id="limit_ip" cols="73" rows="5" class="" v-model="form_params.limit_ip"></textarea>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label label_top">算法模块：</label>
            <div class="col-sm-6">
                <div class="checkbox">
                    <label class="checkbox-inline" v-for="switch_field in form_params.list_algorithm_swith">
                        <input type="checkbox"  v-model="form_params.algorithm_switch" :value="switch_field.value">{{ switch_field.name }}
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label label_top">接口输出的字段：</label>
            <div class="col-sm-6">
                <div class="checkbox">
                    <label class="checkbox-inline" v-for="show_field in form_params.export_fields">
                        <input type="checkbox" :disabled="isDefault(show_field.value)" v-model="form_params.field" :value="show_field.value">{{ show_field.name }}
                    </label>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="remark" class="col-sm-2 control-label">备注</label>
            <div class="col-sm-6">
                <textarea v-model="form_params.remark" id="remark" cols="73" rows="5">
                </textarea>
            </div>
        </div>

        <div class="from-group pull-right">
            <input type="submit" class="btn btn-primary btn-sm" value="添加账号">
        </div>
    </form>
</template>

<script>

    Vue.component('bang_add_template', {
        template: '#form_add_bang',
        props: ['init_params'],
        data: function () {
            return {
                form_params: {
                    name: '',
                    remark : '',
                    limit_second: '',
                    limit_total: '',
                    expiration_date: '',
                    limit_num: '',
                    encrypt_way : 0,
                    account : {label : '暂不选择', id : ''},
                    limit_type: 1,
                    algorithm_switch : [],
                    list_algorithm_swith : [
                        {name: '搜索引擎模块', value: 1},
                        {name: '有数金服模糊搜索接口', value: 2},
                        {name: '行业一致性', value: 3},
                        {name: '第三方数据源模块', value: 4},
                        {name: '名查号模块', value: 5},
                    ],
                    limit_type_list: [
                        {name: '日限额', value: 1},
                        {name: '月限额', value: 2},
                        {name: '年限额', value: 3},
                        {name: '无限制', value: 0}
                    ],
                    limit_ip: '',
                    status: 1,
                    field: ['x', 'y', 'z'],
                    contract_status: 3,
                    contract_status_list: [
                        {name: '已签约已付费', value: 1},
                        {name: '已签约未付费', value: 2},
                        {name: '未签约', value: 3},
                        {name: '特殊客户', value: 5},
                        {name: '其他', value: 4}
                    ],
                    export_fields: [
                        {name: '号码（客户传入）', value: 'x'},
                        {name: '商户名称（客户传入）', value: 'y'},
                        {name: '商户地址（客户传入）', value: 'z'},
                        {name: '查得名称', value: 1},
                        {name: '查得地址', value: 2},
                        {name: '名称比对结果', value: 8},
                        {name: '地址比对结果', value: 10},
                        {name: '名称查得号码', value: 3},
                        {name: '名称查得地址', value: 4},
                    ]
                }
            };
        },
        computed: {
            // 初始化客户列表
            list_account : function () {
                var init_params = JSON.parse(this.init_params);
                var list_account = Object.values(init_params.list_account).map(function (item) {
                    item.label = item.name;
                    return item;
                });
                list_account.unshift({label : '暂不选择', id : ''});
                return list_account;
            }  
        },
        methods: {
            // 是否是默认展示的字段
            isDefault : function(field){
                return jQuery.inArray(field, ['x', 'y', 'z']) !== -1;
            },
            addProduct: function () {
                // 检查参数
                var result = checkParams(this.form_params);
                if (!result) {
                    return false;
                }

                // 追加客户id参数
                this.form_params.account_id =this.form_params.account.id;
                var params = this.form_params;

                // 添加账号
                console.log(params);
                
                var url = '/Home/BangProduct/add';
                this.$http.post(url, params).then(function (response) {
                    if (response.body.success === true) {
                        modalExport(response.body.msg, '/Home/BangProduct/index');
                    } else {
                        modalExport(response.body.msg);
                    }
                });

            }
        }
    });

    function checkParams(vm) {
        // 检查基础信息
        var result_base = checkBaseInfo(vm);
        if (result_base === false) {
            return false;
        }

        // 检查限额
        var result_limit = checkLimit(vm);
        if (result_limit === false) {
            return false;
        }

        // 检查时间
        return checkTime(vm);
    }

    function checkLimit(vm) {
        // 如果限额类型不是无限制 那需要限额数量必须是正整数
        if (vm.limit_type && vm.limit_num.length < 1) {
            modalExport('请填写限额');
            return false;
        }

        if (vm.limit_total === '') {
            modalExport('请填写限额总量');
            return false;
        }

        if (vm.limit_second === '') {
            modalExport('请填写秒并发');
            return false;
        }

        if (vm.limit_type > 0 && isNaN(vm.limit_num)) {
            modalExport('限额必须是正整数');
            return false;
        }
        if (isNaN(vm.limit_total)) {
            modalExport('总量必须是正整数');
            return false;
        }
        if (isNaN(vm.limit_second)) {
            modalExport('秒并发必须是正整数');
            return false;
        }
        vm.limit_total = parseInt(vm.limit_total);
        vm.limit_num = parseInt(vm.limit_num);
        vm.limit_second = parseInt(vm.limit_second);
        vm.limit_type = parseInt(vm.limit_type);

        if (vm.limit_type > 0 && vm.limit_num && vm.limit_num > vm.limit_total) {
            modalExport('限额必须小于总量');
            return false;
        }

        if (vm.limit_second > vm.limit_total) {
            modalExport('秒并发必须小于总量');
            return false;
        }

        if(vm.limit_type >0 && vm.limit_num && vm.limit_num < vm.limit_second) {
            modalExport('秒并发必须小于限额');
            return false;
        }

        if (vm.limit_num < 0) {
            modalExport('限额必须大于0');
            return false;
        }
        if (vm.limit_total < 0) {
            modalExport('总量必须大于0');
            return false;
        }
    }

    // 检查基础信息(name, limit_type, limit_num, limit_total,limit_second,field)
    function checkBaseInfo(vm) {
        if (vm.name.trim() === '') {
            modalExport('请填写账号名称');
            return false;
        }

        if (vm.name.trim().length > 100) {
            modalExport('账号名称的长度必须小于100');
            return false;
        }

        // 检查接口输出字段

        if (vm.field.length < 4) {
            modalExport('请选择至少四个输出字段');
            return false;
        }

        if (vm.limit_ip.trim().length > 800) {
            modalExport('IP白名单必须要小于800字符');
            return false;
        }

        if (vm.remark.trim().length > 500) {
            modalExport('备注长度必须要小于500字符');
            return false;
        }

        return true;
    }

    function checkApiField(vm) {
        if (vm.field.length < 4) {
            modalExport('请选择至少四个输出字段');
            return false;
        }


    }

    // 检查时间
    function checkTime(vm) {
        if (vm.expiration_date === '') {
            modalExport('请选择截至时间');
            return false;
        } else {
            // 选定的时间必须是大于今天的
            var today_str = (new Date()).toDateString();
            var time_diff = new Date(Date.parse(vm.expiration_date)) - new Date(Date.parse(today_str));
            if (time_diff < 0) {
                modalExport('请选定合法的截止时间');
                return false;
            }
        }
        return true;
    }

    new Vue({
        el: '#bang_product_add'
    });

</script>
</body>
</html>
