<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <style>
        div.row {
            width: 90%;
            margin-left: 5%;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<!-- 统计概览  -->
<include file="Common@components/overview_stat"/>
<!-- 统计概览number -->
<include file="Common@components/overview_stat_number"/>
<!-- 统计概览折线图 -->
<include file="Common@components/overview_stat_chart"/>
<!-- 统计概览饼图 -->
<include file="Common@components/overview_stat_pie"/>
<!-- 统计概览催收分析v2饼图 -->
<include file="Common@components/overview_stat_pie_cuishou_v2"/>
<!-- 统计概览邦企查饼图 -->
<include file="Common@components/overview_stat_pie_bang"/>
<!-- 数据核验饼图 -->
<include file="Common@components/overview_stat_pie_data_validate"/>
<!-- 催收分析详单版饼图 -->
<include file="Common@components/overview_stat_pie_cuishou_xiangdan"/>
<!-- 催收分析私有云饼图 -->
<include file="Common@components/overview_stat_pie_cuishou_private"/>
<!-- 邦秒爬饼图 -->
<include file="Common@components/overview_stat_pie_crawler"/>
<!-- 邦秒配饼图 -->
<include file="Common@components/overview_stat_pie_miao_pei"/>
<!-- 邦秒配单号版 -->
<include file="Common@components/overview_stat_pie_miao_pei_single"/>
<!-- 邦秒配详单版 -->
<include file="Common@components/overview_stat_pie_miao_pei_xiangdan"/>

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>

<div id="app">
    <stat-overview-template></stat-overview-template>
</div>
</body>
<script>
    new Vue({
        el: '#app'
    });

</script>
</html>
