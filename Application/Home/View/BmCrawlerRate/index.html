<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="row">
            <div class="panel-body">
                <form class="form-inline" style="margin-left:10px" action="/Home/BmCrawlerRate/index" method="get">
                    <div class="form-group">
                        <select name="flow" class="form-control">
                            <option value="">运营商</option>
                            <foreach name="flow" item="vo">
                            <option <if condition="isset($input['flow']) && $key == $input['flow']"> selected </if> value="{$key}">{$vo}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <select name="channel" class="form-control">
                            <option value="">供应商选择</option>
                            <foreach name="channel" item="vo">
                            <option <if condition="isset($input['channel']) && $key == $input['channel']"> selected </if> value="{$key}">{$vo}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="submit" class="btn btn-info" value="搜索">
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                一键切换供应商
                            <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                            <?php foreach ($channel as $key => $value) { ?>
                                <a href="javascript:void(0);" onclick="channel_crawler($(this))" channel="<?= $key ?>" intro="一键切<?= $value ?>">一键切到<?= $value ?> </a>
                            <?php } ?>
                            </li>
                        </ul>
                    </div>
                    <div class="pull-right" style="margin-right: 10px">
                        <label class="">服务器切换</label>
                        <foreach name="server_list" item="vo">
                            <button type="button" onclick='server_info(<?= json_encode($vo)?>, "{$vo[\"type\"]}")' <if condition="$vo['active'] == true"> class="btn btn-success active"<else/> class="btn" </if> >{$vo['name']}</button>&nbsp;&nbsp;
                        </foreach>
                    </div>
                </form>
            </div>
        </div>
        <div class="panel-heading"><h3 class="panel-title">爬虫列表</h3></div>
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>
                    <input id="channel_list" type="checkbox" name="channel_all" value="all" onclick="checkbox_all();">
                </th>
                <th>省市&运营商</th>
                <th>供应商分配</th>
                <th>羽乐科技主备服务器</th>
                <th>羽乐科技支持</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list as $key => $value) {
                    if ($value['channel_cn'] && $value['rate_cn'] && $value['support_cn']) {
            ?>
                <tr>
                    <th width="20px">
                        <input type="checkbox" name="channel_list" value="<?= $value['channel']['location'].','.$value['channel']['carrier']?>" >
                    </th>
                    <td>{$value['channel_cn']}</td>
                    <td>{$value['rate_cn']}</td>
                    <td><?php
                        if ($value['dist']['yulore'] == 'primary') {
                            echo '<span style="color: green">'.$value['server_cn'].'</span>';
                        } else {
                            echo $value['server_cn'];
                        }
                        ?>
                    </td>
                    <td>{$value['support_cn']}
                        <a href="#support" onclick='support_info(<?= json_encode($value["channel"]) ?>, {$value['support']['yulore']}, 1)' data-toggle="modal" class="btn btn-danger btn-xs">切换</a></td>
                    <td>
                        <div>
                            <a href="#rate" onclick='rate_info(<?= json_encode($value["channel"]) ?>, <?= json_encode($value["rate"]) ?>, 1)' data-toggle="modal" class="btn btn-info btn-xs">流量分配</a>
                        </div>
                    </td>
                </tr>
            <?php }} ?>
            </tbody>
        </table>
    </div>
</div>

<div id="rate" class="modal fade" tabindex="-1" style="margin-top: 16%">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">流量分配</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="control-label col-sm-2">供应商：</label>
                    <div class="col-sm-10" id="rate_list">
                        <foreach name="channel" item="vo">
                        <div class="form-group form-inline">
                            <label class="control-label col-sm-2">{$vo}</label>
                            <input class="form-control" style="width: 20%" type="text" name="{$key}" value=""><span> %</span>
                        </div>
                        </foreach>
                    </div>
                </div>
                <div class="form-group">
                    <label for="channel_url" class="col-sm-2 control-label">配置理由：</label>
                    <input class="form-control" style="width: 60%" type="text" name="reason">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-danger" data-dismiss="modal">关闭</button>
                <button id="rate_submit" class="btn btn-sm btn-primary">提交</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>

<div id="support" class="modal fade" tabindex="-1" style="margin-top: 16%">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">切换支持状态</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="form-group">
                        <label for="channel_url" class="col-sm-2 control-label"><span style="color:red;"> * </span> 支持状态</label>
                        <div class="col-sm-10">
                            <label class="radio-inline">
                                <input type="radio" name="support" class="channel-radio" value="1"> 已支持
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="support" class="channel-radio" value="0"> 未支持
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-sm btn-danger" data-dismiss="modal">关闭</button>
                    <button id="support_submit" class="btn btn-sm btn-primary">提交</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal -->
    </div>
</div>
<script type="text/javascript">
    // 全部选择
    function checkbox_all()
    {
        if ($('#channel_list').is(":checked") == true) {
           $('input[name="channel_list"]').prop("checked",true);
        } else {
           $('input[name="channel_list"]').removeAttr("checked","checked");
        }
    }
    // 流量分配
    function rate_info(channel, rate, type)
    {
        if (type == 1) {
            $.each(rate, function(index, content) {
                $('input[name="'+index+'"]').val(content);
            });
            var channel = JSON.stringify(channel);
            $('#rate_submit').attr('onclick', 'rate_info('+channel+', "", 2)');
        }

        if (type == 2) {
            var total = 0;
            var rate = {};
            $('#rate_list').find('input[type="text"]').each(function () {
                var val = parseInt($(this).val());
                var name = $(this).attr('name');
                rate[name] = val;
                total += val;
            });
            if (total != 100) {
                alert('请正确输入所占比例，总和为100%');
                return false;
            }
            var reason = $('input[name="reason"]').val();
            $.post('/Home/BmCrawlerRate/rate', {'channel': channel, 'rate': rate, 'reason': reason}, function(data) {
                alert(data.info);
                if (data.status == 'success') {
                    location.reload();
                }
                return true;
            });
        }
    }

    // 支持状态
    function support_info(channel, support, type)
    {
        if (type == 1) {
            var channel = JSON.stringify(channel);
            $('input[name="support"][value="'+support+'"]').attr('checked', true);
            $('#support_submit').attr('onclick', 'support_info('+channel+', "", 2)');
        }

        if (type == 2) {
            var support_status = $('input[name="support"]:checked').val();
            if (support_status == 0) {
                if (!confirm('状态切换至未支持，服务器将自动切换至信德服务器，是否确认操作？')) {
                    return false;
                }
            }
            $.post('/Home/BmCrawlerRate/support', {
                channel: channel,
                support: support_status
            }).success(function(data) {
                alert(data.info);
                location.reload();
            }).error(function(data) {
                alert(data.info);
            });
        }
    }

    function channel_info()
    {
        var channel_list = [];
        $('input[name="channel_list"]:checked').each(function(){
            if($(this).prop("checked")){
                 channel_list.push($(this).val());
            }
        });

        if (channel_list.length == 0) {
            alert('请至少选择一个省份&运营商');
            return false;
        }
        return channel_list;
    }

    //切换主备
    function server_info(info, active)
    {
        if (!confirm('确定切换为：'+info.name+'：'+info.host+'吗？')) {
            return false;
        }

        var channel = channel_info();
        if (!channel) {
            return false;
        }

        var reason = prompt('请填写切换服务器的理由');
        var reason = reason.trim();
        if (!reason || reason.length  === 0) {
            alert('您未填写切换理由');
            return false;
        }

        var flow = $('select[name="flow"]').val();
        var nchannel = $('select[name="channel"]').val();

        var info = {'active': active, 'channel': channel, 'reason': reason, 'flow': flow, 'nchannel': nchannel};

        $.post('/Home/BmCrawlerRate/server', info, function (data) {
            alert(data.info);
            location.reload();
        })
    }

    function channel_crawler(obj)
    {
        var info = $(obj).attr('intro');
        var switchs = $(obj).attr('channel');

        if (!confirm('确定要'+info)) {
            return false;
        }

        var channel = channel_info();
        if (!channel) {
            return false;
        }

        var reason = prompt('请填写'+info+'的理由');
        var reason = reason.trim();
        if (!reason || reason.length  === 0) {
            alert('您未填写切换理由');
            return false;
        }

        var flow = $('select[name="flow"]').val();
        var nchannel = $('select[name="channel"]').val();
        var info = {'switch': switchs, 'channel': channel, 'reason' : reason, 'flow': flow, 'nchannel': nchannel};
        $.post('/Home/BmCrawlerRate/channelCrawler', info, function (data) {
            alert(data.info);
            location.reload();
        })
    }

</script>
</body>
</html>