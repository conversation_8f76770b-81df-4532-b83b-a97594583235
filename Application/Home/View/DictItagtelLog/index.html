<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery/1/jquery.min.js"></script>
    <script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__jquery.fileDownload.js"></script>
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .form-inline .row div {
            margin:0 5px 0 0
        }
        .line_second {
            margin-top: 5px;
        }
        .row-first {
            margin-bottom: 10px;
        }

        label {
            margin-left: 10px;
        }
    </style>
</head>
<body>
<include file="Common@Public/dhb_info" />
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form class="form-inline" action="{:U('')}" method="get">
                <div class="row-first">
                    <div class="form-group">
                        <label class="control-label" for="time_begin">开始时间</label>
                        <input type="date" name="begin" id="time_begin" class="form-control"  value="<?= $input['begin']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="time_end">结束时间</label>
                        <input type="date" name="end" id="time_end" class="form-control"  value="<?= $input['end']; ?>"/>
                    </div>
                    <div class="form-group">
                        <label class="control-label">号码类型</label>
                        <select class="form-control" name="itag_id" id="itag_sel">
                            <option value="" >全部</option>
                            <foreach name="tag_list"  item="tag">
                                <option value="{$tag['id']}" <?= $input['itag_id']==$tag['id'] ? 'selected=""':''; ?> >{$tag['itag']}</option>
                            </foreach>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="control-label">操作类型</label>
                        <select class="form-control" name="up_status" id="up_status">
                            <option value="" <?= $input['up_status']=='' ? 'selected=""':''; ?> >全部</option>
                            <option value="1" <?= $input['up_status']==='1' ? 'selected=""':''; ?> >上线</option>
                            <option value="0" <?= $input['up_status']==='0' ? 'selected=""':''; ?> >下线</option>
                        </select>
                    </div>
                    <div class="form-group pull-right">
                        <ul class="list-inline">
                            <li><input type="submit" onclick="return checkTime();" class="btn btn-primary btn-sm" value="查询"></li>
                            <li>
                                <button type="button" id="file_export" class="btn btn-success btn-sm">导出</button>
                            </li>
                        </ul>
                    </div>
                </div>
            </form>
    </div>
</div>

</div>
<div class="container">
    <div class="bs-example" data-example-id="hoverable-table table-responsive">
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>ID</th>
                <th>号码类型</th>
                <th>操作类型</th>
                <th>上传数量</th>
                <th>净增数量</th>
                <th>操作时间</th>
                <th>操作人</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($log_list as $log) {?>
                <tr>
                    <td><?= $log['id']; ?> </td>
                    <td><?= $log['itag_name']; ?> </td>
                    <td><?= $log['up_status_type']; ?> </td>
                    <td><?= $log['tel_num_f']; ?> </td>
                    <td><?= $log['inc_num']; ?> </td>
                    <td><?= $log['utime']; ?> </td>
                    <td><?= $log['handle_person']; ?> </td>
                </tr>
            <?php }?>
            </tbody>
        </table>
        <div>
            <ul class="pagination">
                <?= $page; ?>
            </ul>
        </div>
    </div>

</div>
</body>
<script type="application/javascript">
    $(function(){
        $("#itag_sel").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '全部'
        });

        // 导出
        $("#file_export").click(function () {
            if (!checkTime()) {
                return false;
            }

            var params = genParamsForFile();
            var url_export = '/Home/DictItagtelLog/export' + params;
            $.fileDownload(url_export);
            return false;
        });
    });

    function checkTime() {
        // get begin time and end time
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();

        // check time
        // var time_diff = Date.parse(time_end + ' GMT +8') - Date.parse(time_begin + ' GMT +8');
        var time_diff = new Date(Date.parse(time_end)) - new Date(Date.parse(time_begin));
        if (time_diff < 0) {
            alert('开始时间必须小于结束时间');
            return false;
        }
        return true;
    }

    // 为导出文件生成参数
    function genParamsForFile() {
        var params = '';
        var time_begin = $('#time_begin').val();
        var time_end = $('#time_end').val();
        var itag_sel = $('#itag_sel').val();
        var up_status = $('#up_status').val();

        if (time_begin) {
            params += '&begin=' + time_begin;
        }
        if (time_end) {
            params += '&end=' + time_end;
        }
        if (itag_sel) {
            params += '&itag_id=' + itag_sel;
        }
        if (up_status) {
            params += '&up_status=' + up_status;
        }

        // tidy url
        if (params) {
            params = params.replace('&', '?');
        }
        return params;
    }
</script>
</html>
