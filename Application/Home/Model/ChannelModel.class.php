<?php

namespace Home\Model;

use Think\Model;

class ChannelModel extends Model
{
    protected $connection  = 'DB_FINANCE';
    protected $tableName   = 'channel';
    protected $tablePrefix = '';

    public static function getChannelsByFid($fatherId)
    {
        $model = new static();
        $whereStr = "where father_id in ({$fatherId})";

        $result = $model->query("select * from channel ".$whereStr);

        return $result;
    }
}