<?php

namespace Home\Model;

use Think\Model;

class ConfigPriceInterfaceModel extends Model
{
    protected $connection  = 'DB_FINANCE';
    protected $tableName   = 'config_price_interface';
    protected $tablePrefix = '';

    public static function addConfig($data)
    {
        $model = new static();
        $model->add($data);
    }

    public static function updateConfig($id, $data)
    {
        $model = new static();
        $model -> where(['id'=>$id])-> save($data);
    }

    public static function getPriceConfigByIdAndDate($iid, $date)
    {
        $model = new static();
        $result = $model->query("select * from config_price_interface where iid={$iid} and date={$date}");

        return $result;
    }

    public static function getPriceConfigListByFid($fid, $iid=0)
    {
        $model = new static();
        $whereStr = " where cp.father_id={$fid} ";
        if( $iid ){
            $whereStr .= " and cp.iid={$iid}";
        }
        $sql = "select cp.*, ci.label ilabel, ci.cid, c.label clabel from config_price_interface cp 
                left join channel_interface ci on cp.iid=ci.id
                left join channel c on ci.cid=c.id".$whereStr;
        $result = $model->query($sql);

        return $result;
    }

    public static function getPriceConfigListByIids($iids=[])
    {
        $model = new static();

        $iids = '('.implode(',', $iids).')';
        $whereStr = " where cp.iid in {$iids} and delete_time=0";

        $sql = "select cp.*, ci.label ilabel, ci.cid, c.label clabel from config_price_interface cp 
                left join channel_interface ci on cp.iid=ci.id
                left join channel c on ci.cid=c.id".$whereStr;
        $result = $model->query($sql);

        return $result;
    }

    public static function getPriceConfigById($id)
    {
        $model = new static();
        $sql = "select cp.*, ci.cid from config_price_interface cp 
                left join channel_interface ci on cp.iid=ci.id
                where cp.id={$id} ";
        $result = $model->query($sql);

        return isset($result[0]) ? $result[0] : [];
    }
}