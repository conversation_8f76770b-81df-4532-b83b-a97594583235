<?php

namespace Home\Model;

use Think\Model;

class ChannelProductModel extends Model
{
    protected $connection  = 'DB_FINANCE';
    protected $tableName   = 'channel_product';
    protected $tablePrefix = '';

    public static function getChannelByPid($pid)
    {
        if( !$pid ){
            return [];
        }

        $model = new static();
        $result = $model->query("select cp.*, c.label from channel_product cp left join channel c on cp.cid=c.id where pid ={$pid}");

        return $result;
    }
}