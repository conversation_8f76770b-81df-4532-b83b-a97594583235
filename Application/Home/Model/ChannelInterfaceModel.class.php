<?php

namespace Home\Model;

use Think\Model;

class ChannelInterfaceModel extends Model
{
    protected $connection  = 'DB_FINANCE';
    protected $tableName   = 'channel_interface';
    protected $tablePrefix = '';

    public static function getInterfaceByCids($cids)
    {
        if( !$cids ){
            return [];
        }

        $cids = '('.implode(',', $cids).')';

        $model = new static();
        $result = $model->query("select * from channel_interface where cid in {$cids}");

        return $result;
    }


    public static function getInterfaceByIds($ids)
    {
        if( !$ids ){
            return [];
        }

        $ids = '('.implode(',', $ids).')';

        $model = new static();
        $result = $model->query("select id,channel_id,label from channel_interface where id in {$ids}");

        return $result;
    }

}