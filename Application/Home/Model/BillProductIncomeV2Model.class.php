<?php
namespace Home\Model;

use Think\Model;

/**
 * @method order(string $string)
 * @method group(string $string)
 */
class BillProductIncomeV2Model extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'bill_product_income_v2';
    protected $tablePrefix = '';

    /**
     * 查看一年前是否有收入 计算新老收入
     *
     * @param $start_date
     * @param $apikeys
     * @param $product_ids
     *
     * @static
     * @return mixed
     * <AUTHOR> 2024-11-04 18:42:52
     */
    public static function isHaveIncome($start_date, $apikeys = [], $product_ids = []){
        $model = new static();
        if(empty($start_date)){
            $start_date = date('Ymd');
        }

        $start_date = $start_date - 10000;//一年以前

        $where = [];
        $where['date'] = [
            ['lt', $start_date],
            ['egt', 20210701]
        ];
        $where['money'] = ['gt', 0];

        if(!empty($apikeys)){
            $where['apikey'] = ['in',$apikeys];
        }
        if(!empty($product_ids)){
            $where['product_id'] = ['in',$product_ids];
        }

        return $model->where($where)->group('apikey, product_id')->field('apikey,product_id,min(date)')->select();
    }
}
