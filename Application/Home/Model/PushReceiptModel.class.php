<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/14 0014
 * Time: 15:06
 */

namespace Home\Model;

use Think\Model;

/**
 * 财务系统推送收款单记录表
 */
class PushReceiptModel extends Model
{
    protected $connection  = 'DB_FINANCE';
    protected $tableName   = 'push_receipt';
    protected $tablePrefix = '';


    /**
     * 通过流水号获取推送详情
     *
     * @param $trade_id
     *
     * @static
     * @return mixed
     * <AUTHOR> 2024-06-24 17:59:54
     */
    public static function getInfoByTradeId($trade_id){
        $model = new static();

        $where = [
            'trade_id' => $trade_id,
        ];

        return $model->where($where)->order('id desc')->find();
    }


    /**
     * 通过流水号获取推送详情
     *
     * @param $task_id
     *
     * @return bool|float|int|string
     * @static
     * <AUTHOR> 2024-06-24 17:59:54
     */
    public static function updateAcceptNumberByTaskId($task_id){
        $model = new static();

        $where = [
            'task_id' => $task_id,
        ];

        return $model->where($where)->save([
            'updated_at'    => date("Y-m-d H:i:s")
        ]);
    }

}