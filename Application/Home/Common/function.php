<?php

/**
 * 模块函数
 * @param $json
 * @return string
 */
function indent ($json) {

    $result = '';
    $pos = 0;
    $strLen = strlen($json);
    $indentStr = '';
    $newLine = "<br/>";
    $prevChar = '';
    $outOfQuotes = true;

    for ($i=0; $i<=$strLen; $i++) {

        // Grab the next character in the string.
        $char = substr($json, $i, 1);
        // Are we inside a quoted string?
        if ($char == '"' && $prevChar != '\\\\') {
            $outOfQuotes = !$outOfQuotes;
            // If this character is the end of an element,
            // output a new line and indent the next line.
        } else if(($char == '}' || $char == ']') && $outOfQuotes) {
            $result .= $newLine;
            $pos --;
            for ($j=0; $j<$pos; $j++) {
                $result .= $indentStr;
            }
        }
        // Add the character to the result string.
        $result .= $char;
        // If the last character was the beginning of an element,
        // output a new line and indent the next line.
        if (($char == ',' || $char == '{' || $char == '[') && $outOfQuotes) {
            $result .= $newLine;
            if ($char == '{' || $char == '[') {
                $pos ++;
            }
            for ($j = 0; $j < $pos; $j++) {
                $result .= $indentStr;
            }
        }
        $prevChar = $char;
    }

    return $result;
}

/**
 * 获取当天开始结束的时间戳
 * @return array
 */
if(!function_exists('getTodayStamp')) {
    function getTodayStamp()
    {
        $t = time();
        $start = mktime(0, 0, 0, date("m", $t), date("d", $t), date("Y", $t));
        $end = mktime(23, 59, 59, date("m", $t), date("d", $t), date("Y", $t));
        return [
            'begin'=>$start,
            'end'=>$end
        ];
    }
}

/**
 * 获取昨天的时间戳
 * @return array
 */
if (!function_exists('getYesStamp')) {

    function getYesStamp()
    {
        $begin = strtotime(date('Y-m-d', strtotime('-1 day')));
        return [
            'begin' => $begin,
            'end' => $begin+24*60*60-1
        ];
    }
}


/**
 * 获取最近30天的时间戳
 * @return array
 */
if (!function_exists('getMonStamp')) {

    function getMonStamp()
    {
        $end = time();
        $begin = strtotime(date('Y-m-d', strtotime('-30 days')));
        return [
            'begin' => $begin,
            'end' => $end
        ];
    }
}



/**
 * 获取最近一周的时间戳
 * @return array
 */
if (!function_exists('getWeekStamp')) {

    function getWeekStamp()
    {
        $end = time();
        $begin = strtotime(date('Y-m-d', strtotime('-7 days')));
        return [
            'begin' => $begin,
            'end' => $end
        ];
    }
}



/*
 * 切分时间段函数
 * beginTime->开始时间时间戳
 * endTime->结束时间时间戳
 * type->[1]按年分;[2]按月分;[3]按天分
 * 返回二维数组
 * 		arr[]['beginTime']->开始时间
 * 		arr[]['endTime']->结束时间
 * 		arr[]['date']->相应date
 * */
if (!function_exists('sliceTime')) {
    function sliceTime($beginTime, $endTime, $type)
    {

        $arr = array();
        $sub = 0;
        $beginY = date('Y', $beginTime);
        $beginM = date('m', $beginTime);
        $beginD = date('d', $beginTime);
        $endY = date('Y', $endTime);
        $endM = date('m', $endTime);
        $endD = date('d', $endTime);
        $duringY = $endY - $beginY;
        $duringM = 12 - $beginM + $endM + ($duringY - 1) * 12;
        $arr[$sub]['beginTime'] = $beginTime;

        switch ($type) {
            case '1': {
                for ($n = 0; $n < $duringY; $n++) {
                    $arr[$sub]['endTime'] = mktime(23, 59, 59, 12, 31, $beginY++);
                    $arr[$sub]['date'] = date('Y', $arr[$sub]['endTime']);
                    $sub++;
                    $arr[$sub]['beginTime'] = mktime(0, 0, 0, 1, 1, $beginY);
                }
                $arr[$sub]['endTime'] = $endTime;
                $arr[$sub]['date'] = date('Y', $arr[$sub]['endTime']);
                break;
            }
            case '2': {
                $beginYY = $beginY;
                for ($a = 0; $a < $duringM; $a++) {
                    $m = $a + $beginM;
                    $numM = date('t', strtotime($beginY . '-' . (($m % 12) ? ($m % 12) : 12) . '-1'));
                    $arr[$sub]['endTime'] = mktime(23, 59, 59, $m, $numM, $beginYY);
                    $arr[$sub]['date'] = date('Y-m', $arr[$sub]['endTime']);
                    $m += 1;
                    $sub++;
                    $arr[$sub]['beginTime'] = strtotime($beginY . '-' . (($m % 12) ? ($m % 12) : 12));
                    if ((($m % 12) == 0) && ($m / 12) >= 1) {
                        $beginY++;
                    }
                }
                $arr[$sub]['endTime'] = $endTime;
                $arr[$sub]['date'] = date('Y-m', $arr[$sub]['endTime']);
                break;
            }
            case '3': {
                for ($bY = $beginY; $bY <= $endY; $bY++) {
                    $bM = 1;
                    $eM = 12;
                    if ($bY == $beginY) {
                        $bM = $beginM;
                    }
                    if ($bY == $endY) {
                        $eM = $endM;
                    }
                    for ($mm = $bM; $mm <= $eM; $mm++) {
                        $bD = 1;
                        $eD = date('t', strtotime($bY . '-' . (($mm % 12) ? ($mm % 12) : 12) . '-1'));
                        if ($bY == $beginY && $mm == $beginM) {
                            $bD = $beginD;
                        }
                        if ($bY == $endY && $mm == $endM) {
                            $eD = $endD;
                        }
                        for ($dd = $bD; $dd <= $eD;) {
                            if ($bY == $endY && $mm == $endM && $dd == $eD) {
                                break;
                            }
                            $arr[$sub]['endTime'] = mktime(23, 59, 59, (($mm % 12) ? ($mm % 12) : 12), $dd, $bY);
                            $arr[$sub]['date'] = date('Y-m-d', $arr[$sub]['endTime']);
                            $sub++;
                            $dd++;
                            $arr[$sub]['beginTime'] = mktime(0, 0, 0, (($mm % 12) ? ($mm % 12) : 12), $dd, $bY);
                        }

                    }
                }
                $arr[$sub]['endTime'] = $endTime;
                $arr[$sub]['date'] = date('Y-m-d', $arr[$sub]['endTime']);
                break;
            }
        }
        return $arr;
    }
}


/**
 *获取当月的开始时间戳以及结束的时间戳
 *
 */
if (!function_exists('getMonthTime')) {

    function getMonthTime()
    {
        $begin = mktime(0, 0, 0, date('m'), 1, date('Y'));
        $end = mktime(23, 59, 59, date('m'), date('t'), date('Y'));

        return [
            'begin' => $begin,
            'end' => $end
        ];
    }
}


/**
 * 将当前的数据写入一个文件之中,方便调试
 * @param $data  需要调试的数据
 * @param string $fileName 生成的文件名
 * @param string $flag 当前调试数据的标记
 * @return bool|int    int|bool The function returns the number of bytes that were written to the file, or
 * false on failure.
 */
if (!function_exists('debug')) {
    function debug($data, $flag = '', $fileName = 'debug.txt')
    {

        $file = RUNTIME_PATH . 'bug' . DIRECTORY_SEPARATOR . $fileName;
        return file_put_contents($file, PHP_EOL . $flag . ':' . var_export($data, true), FILE_APPEND);
    }
}


/**
 * 返回非空数据
 * @return array
 */
if(!function_exists('getNotNull')) {
    function getNotNull($value,$default)
    {
        if(isset($value) && !empty($value) && $value!=' ' && !is_null($value)){
            return $value;
        }
        return $default;
    }
}



function gen_pwd($len)
{
    $str = '';
    $n = ceil($len/60);
    for ($i=0; $i < $n; $i++) {
        $str .= password_hash(microtime(true).uniqid(), PASSWORD_DEFAULT);
    }

    if (strlen($str) > $len) {
        $str = str_replace('.', '', $str);
        return substr($str, 4, $len);
    }
    return null;
}






/**
 * @param $url 访问的 url
 * @param $type POST|GET|get|post
 * @param bool $data 传输的参数
 * @param null $err_msg 错误的 code
 * @param int $timeout 超时时间
 * @param array $cert_info 证书信息
 *
 * @return mixed
 */
if(!function_exists('GoCurl')) {
    function GoCurl($url, $type, $data = false, &$err_msg = null, $timeout = 20, $cert_info = array())
    {
        $type = strtoupper($type);
        if ($type == 'GET' && is_array($data)) {
            $data = http_build_query($data);
        }
        $option = array();
        if ($type == 'POST') {
            $option[CURLOPT_POST] = 1;
        }
        if ($data) {
            if ($type == 'POST') {
                $option[CURLOPT_POSTFIELDS] = $data;
            } elseif ($type == 'GET') {
                $url = strpos($url, '?') !== false ? $url . '&' . $data : $url . '?' . $data;
            }
        }
        $option[CURLOPT_URL] = $url;
        $option[CURLOPT_MAXREDIRS] = 4;
        $option[CURLOPT_RETURNTRANSFER] = true;
        $option[CURLOPT_TIMEOUT] = $timeout;
        //设置证书信息
        if (!empty($cert_info) && !empty($cert_info['cert_file'])) {
            $option[CURLOPT_SSLCERT] = $cert_info['cert_file'];
            $option[CURLOPT_SSLCERTPASSWD] = $cert_info['cert_pass'];
            $option[CURLOPT_SSLCERTTYPE] = $cert_info['cert_type'];
        }
        //设置 CA
        if (!empty($cert_info['ca_file'])) {
            // 对认证证书来源的检查，0 表示阻止对证书的合法性的检查。 1 需要设置 CURLOPT_CAINFO
            $option[CURLOPT_SSL_VERIFYPEER] = 1;
            $option[CURLOPT_CAINFO] = $cert_info['ca_file'];
        } else {
            // 对认证证书来源的检查，0 表示阻止对证书的合法性的检查。 1 需要设置CURLOPT_CAINFO
            $option[CURLOPT_SSL_VERIFYPEER] = 0;
        }
        $ch = curl_init();
        curl_setopt_array($ch, $option);
        $response = curl_exec($ch);
        $curl_no = curl_errno($ch);
        $curl_err = curl_error($ch);
        curl_close($ch);
        // error_log
        if ($curl_no > 0) {
            if ($err_msg !== null) {
                $err_msg = '(' . $curl_no . ')' . $curl_err;
            }
        }
        return $response;
    }
}
