<?php
namespace Home\Repositories;

class PwdLogRepository extends BaseRepository
{
    protected $list = '/admin/pwd/log';

    protected $detail = '/admin/pwd/detail';

    protected $statusList = [
        'success' => [0],
        'crawler_error' => [4016, 5000, 5001, 5002, 9999]
    ];

    public function getPwdLogList($limit = 20)
    {
        $param = $this->getPwdLogListParam();
        $param['size'] = $limit;

        $list = $this->getPwdLogListApi($param);

        return $list;
    }

    public function getPwdLogDetail()
    {
        $tid = I('get.tid', '', 'trim');

        $list = $this->getPwdLogDetailApi(compact('tid'));

        array_walk($list, function (&$v, $k) {
            $v = json_encode($v, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        });

        return $list;
    }

    public function getPwdLogListParam()
    {
        $start_time = I('get.begin', strtotime(date('Y-m-d')), 'strtotime');
        $end_time = I('get.end', strtotime(date('Y-m-d')), 'strtotime');
        $end_time += 60*60*24-1;

        $telecom = I('get.telecom', '', 'trim');
        $province = I('get.province', '', 'trim');
        $tid = I('get.tid', '', 'trim');
        $tel = I('get.tel', '', 'trim');
        $cid = I('get.cid', '', 'trim');
        $status = I('get.status', '', 'trim');

        $status_list = I('get.status_list', '', 'trim');

        if ($status_list) {
            if ($status !== '' && !in_array($status, $this->statusList[$status_list])) {
                $status = 'no result';
            } else if ($status === '') {
                $status = $this->statusList[$status_list];
            }
        }

        $page = I('get.page', 1, 'intval');

        $param = array_filter(compact('start_time', 'end_time', 'telecom', 'province', 'tid', 'cid', 'page', 'tel'));
        if ($status !== '') {
            $param['status'] = $status;
        }

        return $param;
    }

    public function getPwdLogListApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->list;
        $url = $param ? $url.'?'.http_build_query($param, '', '&') : $url;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    public function getPwdLogDetailApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->detail;
        $url = $param ? $url.'?'.http_build_query($param, '', '&') : $url;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }
}