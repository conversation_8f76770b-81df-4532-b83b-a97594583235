<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/14 0014
 * Time: 15:05
 */

namespace Home\Repositories;


use Home\Model\ConfigModel;

class ConfigRepository extends BaseRepository
{
    protected $id;

    public function __construct($id)
    {
        $this->id = $id;
    }

    public function get()
    {
        $id = $this->id;
        return (new ConfigModel)->where(compact('id'))
            ->find();
    }

    public function update()
    {
        $config = I('post.config', '', 'trim');

        if (!json_validate($config)) {
            return '配置不是标准的JSON格式';
        }
        //$config = json_encode(json_decode($config, true), JSON_UNESCAPED_UNICODE);

        $update_admin = isset($_SESSION[C('LOGIN_SESSION_NAME')]) ? $_SESSION[C('LOGIN_SESSION_NAME')] : '';
        $updated_at   = time();
        $id           = $this->id;

        $res = (new ConfigModel)->save(compact('config', 'updated_at', 'update_admin', 'id'));
        if ($res) {
            return true;
        }
        return false;
    }
}