<?php

namespace Home\Repositories;

use Api\Model\CuishouUserModel;
use Home\Controller\ToolController;
use Common\Model\CuishouUserModel as CommonCuishouUserModel;
use Think\Cache;

class BmMatchingRepository
{
    /**
     * 列表时间上的制约
     * @return mixed
     */
    public function timeLimitForList()
    {
        $active = I('get.active', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $id = I('get.id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        // check active user
        $where = $active ? ['active' => $active] : [];
        $where = $apikey ? (['apikey' => $apikey] + $where) : $where;
        $where = $contract_status ? (['contract_status' => $contract_status] + $where) : $where;
        if ($begin && $begin_e){
            $where['created_at'] = ['between', [strtotime($begin), strtotime($begin_e.' 23:59:59')]];
        }elseif($begin){
            $where['created_at'] = ['egt',strtotime($begin)];
        }elseif($begin_e){
            $where['created_at'] = ['elt', strtotime($begin_e. ' 23:59:59')];
        }
        if ($end && $end_e){
            $where['validuntil'] = ['between', [$end.' 00:00:00', $end_e.' 23:59:59']];
        }elseif($end){
            $where['validuntil'] = ['egt', $end.' 00:00:00'];
        }elseif ($end_e){
            $where['validuntil'] = ['elt', $end_e.' 23:59:59'];
        }
        // 如果输入的用户id不同那么获取不到数据值
        if ($choose_id && $id && $choose_id != $id) {
            $where['id'] = 'what happened';
        } elseif ($choose_id) {
            $where['id'] = $choose_id;
        } elseif ($id) {
            $where['id'] = $id;
        }
        return $where;
    }


    /**
     * 催收分导出csv准备数据
     * @param array $where
     * @return array
     */
    public function geBmMatchingForList($where)
    {
        $CuishouUser = new CuishouUserModel();
        $CommonCuishouUser = new CommonCuishouUserModel();
        $contract_status = $CommonCuishouUser->getContractStatus();

        //查下数据
        $account_list = D('AdminApikey')->where($where)->order('id desc')->select();
        $account_product_infos = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($account_list, 'id'));
        foreach ($account_list as $k=>$item){
            if ($account_product_infos[$item['id']]){
                $account_list[$k]['account_product'] = $account_product_infos[$item['id']];
            }
        }
        // gen api example and sig_str
        $account_list = array_map(
            function ($account) {
                // generate sig_str
                $account['sig_str'] = (new ToolController())->gen_sig_str($account['password']);
                $sig = (new ToolController())->gen_sig($account['sig_str'], '10086', 'NMKJ', 'myApp',
                    '1.0', $account['apikey'], '86', '1');
                // gen api example
                $account['api_example'] = "https://itag.dianhua.cn/itag/?apikey=" . $account['apikey'] .
                    "&app=myApp&app_ver=1.0&country=86&version=1&tel=10086&uid=NMKJ&sig=" . $sig;
                return $account;
            }, $account_list
        );

        $res = [];
        foreach ($account_list as $k=>$item){
            $temp = [];
            $temp['id'] = $item['id'];
            $temp['owner'] = $item['owner'];
            $temp['active'] = ($item['active'] == 1) ? '可用' : '禁用';
            $temp['contract_status'] = !empty($contract_status[$item['contract_status']]) ? $contract_status[$item['contract_status']] : ' ';
            $temp['apikey'] = $item['apikey'];
            $temp['sig_str'] = $item['sig_str'];
            $temp['api_example'] = $item['api_example'];
            $timeStr = 'start :'. ($item['created_at']) ? date('Y-m-d H:i:s',$item['created_at']) : ' ';
            $timeStr .= ' end :'. ($item['validuntil']) ?$item['validuntil'] : ' ';
            $temp['timeStr'] = $timeStr;
            $temp['account_name'] = $item['account_product']['account_name'];
            $temp['account_name'] = $temp['account_name'] ? $temp['account_name'] : '';
            $res[] = $temp;
        }
        return $res;
    }

    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListByDayForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '日期,账号ID,账号名称,客户ID,客户名称,总调用量';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $stat_data) {
            // 数据补全
            $specialChar = is_numeric($stat_data['owner']) ? "\"\t" . $stat_data['owner'] . "\"," : '"'.$stat_data['owner'].'",';
            $file_str = '"'.$stat_data['date'] . '","' . $stat_data['id'] . '",'.$specialChar.'"'
                . $stat_data['account_id'] . '","' .$stat_data['name_account'] .'","'. $stat_data['itag'] . '"';

            $file_str = mb_convert_encoding($file_str,'gb2312','utf-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
