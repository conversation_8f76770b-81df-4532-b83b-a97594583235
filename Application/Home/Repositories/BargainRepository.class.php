<?php

namespace Home\Repositories;


use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Controller\DataAuthController;
use Common\Model\FeeConfigModel;
use Common\Model\RemitModel;
use Common\ORG\Page;
use Home\Model\BargainModel;
use Home\Model\BargainProductModel;

class BargainRepository
{
    protected $customer_model;
    protected $account_model;
    protected $fee_config_model;
    protected $account_product_model;
    protected $product_model;
    protected $bargain_model;
    protected $bargain_product_model;
    public function __construct()
    {
        $this->customer_model = new CustomerModel();
        $this->account_model = new AccountModel();
        $this->fee_config_model = new FeeConfigModel();
        $this->account_product_model = new AccountProductModel();
        $this->product_model = new ProductModel();
        $this->bargain_model = new BargainModel();
        $this->bargain_product_model = new BargainProductModel();
    }
    /**
     * 获取客户数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function customer_data($where = [], $field = '*', $order = null)
    {
        $is_delete = 0;
        $model = $this->customer_model->field($field)->where(array_merge(compact('is_delete', $where)));
        if (!is_null($order)) {
            $model = $model->order($order);
        }
        return $model->select();
    }
    /**
     * 获取账号数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function account_data($where = [], $field = '*', $order = null)
    {
        $is_delete = 0;
        $model = $this->account_model->field($field)->where(array_merge(compact('is_delete'), $where));
        if (!is_null($order)) {
            $model = $model->order($order);
        }
        return $model->select();
    }
    /**
     * 获取账号产品数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function account_product_data($where = [], $field = '*', $order = null)
    {
        $model = $this->account_product_model->field($field)->where($where);
        if (!is_null($order)) {
            $model = $model->order($order);
        }
        return $model->select();
    }
    /**
     * 获取产品数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function product_data($where = [], $field = '*', $order = null)
    {
        $model = $this->product_model->field($field)->where($where);
        if (!is_null($order)) {
            $model = $model->order($order);
        }
        return $model->select();
    }
    /**
     * 获取客户的select选择项
     *
     * @access protected
     * @param $default string 默认选择的客户ID
     * @param $with_delete boolean 是否携带删除的客户 默认false
     *
     * @return string
     **/
    protected function getCustomerOption($default = '', $with_delete = false)
    {
        $where = [];
        if (!$with_delete) {
            $where['is_delete'] = 0;
        }
        $data = $this
            ->customer_model
            ->field('customer_id, name')
            ->where(DataAuthController::instance()->getCustomerWhere())
            ->where($where)
            ->select();
        return makeOption(array_column($data, 'name', 'customer_id'), $default);
    }
    /**
     * 监听切换选择的客户
     *
     * @access public
     *
     * @return void
     **/
    public function listenCutCustomer()
    {
        $customer_id = I('post.type', '', 'trim');
        if ($customer_id!='cut_customer') {
            return;
        }
        $customer_id = I('post.customer_id', '', 'trim');
        if (empty($customer_id)) {
            $this->returnJson([
                'company'   => '',
                'product'   => []
            ]);
        }
        $data = $this->customer_model->where(compact('customer_id', 'company'))->find();
        if (empty($data)) {
            $this->returnJson([
                'company'   => '',
                'product'   => []
            ]);
        }
        $company = $data['company'];
        $this->returnJson(compact('company'));
        //$product = $this->getStoreProductByCustomerId($customer_id);
        //$company = $data['company'];
        //$this->returnJson(compact('product', 'company'));
    }
    /**
     * 获取客户的所有开通的产品
     *
     * @access protected
     * @param $customer_id string 客户ID
     *
     * @return array
     **/
    protected function getStoreProductByCustomerId($customer_id)
    {
        $account_id = $this->account_data(compact('customer_id'), 'account_id');
        $account_id = ['in', array_column($account_id, 'account_id')];
        $store_product_data = $this->account_product_data(compact('account_id'), 'product_id');
        $product_id = ['in', array_column($store_product_data, 'product_id')];
        $back_status = 1;
        $data = $this->product_data(compact('product_id', 'back_status'), 'product_id, product_name');
        return array_column($data, 'product_name', 'product_id');
    }
    /**
     * 生成一个JSON响应
     *
     * @access protected
     * @param $data array 响应内容
     * @param $status integer 状态值
     *
     * @return void
     **/
    protected function returnJson($data, $status = 0)
    {
        ob_end_clean();
        header('Content-Type:application/json;charset=utf8');
        echo json_encode(compact('status', 'data'), JSON_UNESCAPED_UNICODE);
        die;
    }
    /**
     * 获取增加合同展示数据
     *
     * @access public
     *
     * @return array
     **/
    public function add()
    {
        //获取所有的未被删除的客户的Option
        $customer_option = $this->getCustomerOption();

        //获取所有可签约产品
        $product_option = $this->getAllowSignProduct();
        return compact('customer_option', 'product_option');
    }
    /**
     * 增加合同
     *
     * @access public
     *
     * @return void
     **/
    public function run_add()
    {
        try {
            //获取数据
            $data = $this->getAddData();
            //生成需要增加的bargain数据
            $bargain_data = $this->getAddBargainData($data);
            //生成需要增加的bargainProduct数据
            $bargain_product_data = $this->getAddBargainProductData($data);
            //先增加bargainProduct数据（允许存在不正常数据，不影响使用）
            $res = $this->bargain_product_model->addAll($bargain_product_data);
            if ($res) {
                $res1 = $this->bargain_model->add($bargain_data);
                if ($res1) {
                    $this->returnJson([], 0);
                } else {
                    throw new \Exception('合同产品数据增加成功，合同数据增加失败');
                }
            }
            throw new \Exception('合同产品数据增加失败');
        } catch (\Exception $exception) {
            $message = $exception->getMessage();
            $this->returnJson(compact('message'), 1);
        }
    }
    /**
     * 获取合同增加的数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getAddData()
    {
        //获取合同编号
        $bargain_id = I('post.bargain_id', '', 'trim');
        if (empty($bargain_id)) {
            throw new \Exception('合同编号格式不正确');
        }
        $isExists = $this->bargain_model->where(compact('bargain_id'))->count();
        if ($isExists) {
            throw new \Exception("【{$bargain_id}】合同编号已存在，请重新输入");
        }
        //客户名称
        $customer_id = I('post.customer_id', '', 'trim');
        if (empty($customer_id)) {
            throw new \Exception('请选择关联客户');
        }
        $isExists = $this->customer_model->where(compact('customer_id'))->count();
        if (!$isExists) {
            throw new \Exception('客户ID不正确');
        }
        //公司名称
        $company = I('post.company', '', 'trim');
        if (empty($company)) {
            throw new \Exception('请填写公司名称');
        }
        //签约产品
        $product_id = I('post.product_id', []);
        if (empty($product_id)) {
            throw new \Exception('至少选择一个签约产品');
        }
        //金额
        $money = I('post.money', '', 'trim');
        if (empty($money)) {
            throw new \Exception('请填写合同金额');
        }
        $money_preg = '/^\d{1,11}(\.\d{1,2})?$/';
        if (!preg_match($money_preg, $money)) {
            throw new \Exception('合同金额格式不正确');
        }
        //合同开始时间
        $start_date = I('post.start_date', '', 'trim');
        if (empty($start_date)) {
            throw new \Exception('请选择合同开始时间');
        }
        //合同截止时间
        $end_date = I('post.end_date', '', 'trim');
        if (empty($end_date)) {
            throw new \Exception('请选择合同截止时间');
        }
        if (strtotime($start_date)>=strtotime($end_date)) {
            throw new \Exception('合同开始时间需小于合同截止时间');
        }
        //备注
        $remark = I('post.remark', '', 'trim');
        return compact('bargain_id', 'customer_id', 'company', 'product_id', 'money', 'start_date', 'end_date', 'remark');
    }
    /**
     * 获取增加到bargain表中的数据
     *
     * @access protected
     * @param $data array 获取到的数据
     *
     * @return array
     **/
    protected function getAddBargainData($data)
    {
        return [
            'bargain_id'        => $data['bargain_id'],
            'customer_id'       => $data['customer_id'],
            'company'           => $data['company'],
            'start_date'        => $data['start_date'],
            'end_date'          => $data['end_date'],
            'money'             => $data['money'],
            'remark'            => $data['remark'],
            'create_time'       => time(),
            'create_admin'      => session('site_login_name'),
            'update_time'       => time(),
            'update_admin'      => session('site_login_name'),
            'delete_time'       => 0
        ];
    }
    /**
     * 获取增加到bargain_product表中的数据
     *
     * @access protected
     * @param $data array 获取到的数据
     *
     * @return array
     **/
    protected function getAddBargainProductData($data)
    {
        $product = $data['product_id'];
        $bargain_id = $data['bargain_id'];
        $result =[];
        array_walk($product, function ($product_id) use ($bargain_id, &$result) {
            $result[] = compact('product_id', 'bargain_id');
        });
        return $result;
    }
    /**
     * 获取合同首页需要展示的数据
     *
     * @access public
     *
     * @return array
     **/
    public function index()
    {
        //获取查询参数
        $params = $this->getParamsForIndex();
        //获取查询条件
        $where = $this->getWhereForIndex($params);
        //获取合同数据
        $result = $this->getDataForIndex($where);
        //获取页面展示的input
        $input = $this->getInputForIndex($params);
        return compact('input', 'result');
    }
    /**
     * 获取查询参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getParamsForIndex()
    {
        $params = [];
        $params['bargain_id'] = I('get.bargain_id', '', 'trim');
        $params['customer_id'] = I('get.customer_id', '', 'trim');
        $params['company'] = I('get.company', '', 'trim');
        $params['product_id'] = I('get.product_id', '', 'trim');
        //合同开始日期
        $start_start_date = I('get.start_start_date', '', 'trim');
        $end_start_date = I('get.end_start_date', '', 'trim');
        if (!empty($start_start_date) && !empty($end_start_date)) {
            $params['start_start_date'] = date('Y-m-d', min(strtotime($start_start_date), strtotime($end_start_date)));
            $params['end_start_date'] = date('Y-m-d', max(strtotime($start_start_date), strtotime($end_start_date)));
        } elseif (!empty($end_start_date)) {
            $params['start_start_date'] = '';
            $params['end_start_date'] = $end_start_date;
        } elseif (!empty($start_start_date)) {
            $params['start_start_date'] = $start_start_date;
            $params['end_start_date'] = '';
        } else {
            $params['start_start_date'] = '';
            $params['end_start_date'] = '';
        }
        //合同截止日期
        $start_end_date = I('get.start_end_date', '', 'trim');
        $end_end_date = I('get.end_end_date', '', 'trim');
        if (!empty($start_end_date) && !empty($end_end_date)) {
            $params['start_end_date'] = date('Y-m-d', min(strtotime($start_end_date), strtotime($end_end_date)));
            $params['end_end_date'] = date('Y-m-d', max(strtotime($start_end_date), strtotime($end_end_date)));
        } elseif (!empty($end_end_date)) {
            $params['start_end_date'] = '';
            $params['end_end_date'] = $end_end_date;
        } elseif (!empty($start_end_date)) {
            $params['start_end_date'] = $start_end_date;
            $params['end_end_date'] = '';
        } else {
            $params['start_end_date'] = '';
            $params['end_end_date'] = '';
        }
        return $params;
    }
    /**
     * 获取查询条件
     *
     * @access protected
     * @param $params array 查询参数
     *
     * @return array
     **/
    protected function getWhereForIndex($params)
    {
        $where = [];
        //客户名称
        if (!empty($params['customer_id'])) {
            $where['bargain.customer_id'] = $params['customer_id'];
        }
        //公司名称
        if (!empty($params['company'])) {
            $where['bargain.company'] = $params['company'];
        }
        //选择签约产品&&合同编号的交互条件
        if (!empty($params['product_id']) && !empty($params['bargain_id'])) {
            $product_id = $params['product_id'];
            $bargain_id = $this->bargain_product_model->where(compact('product_id'))->field('bargain_id')->select();
            if (!empty($bargain_id)) {
                $where['_string'] = 'bargain.bargain_id in ("' . implode('","', array_column($bargain_id, 'bargain_id')) . '") AND bargain.bargain_id = "' . $params['bargain_id'] . '"';
            } else {
                //设置一个查不到的条件
                $where['bargain.bargain_id'] = 0;
            }
        } elseif (!empty($params['product_id'])) {
            $product_id = $params['product_id'];
            $bargain_id = $this->bargain_product_model->where(compact('product_id'))->field('bargain_id')->select();
            if (!empty($bargain_id)) {
                $bargain_id = ['in', array_column($bargain_id, 'bargain_id')];
                $where['bargain.bargain_id'] = $bargain_id;
            } else {
                //设置一个查不到的条件
                $where['bargain.bargain_id'] = 0;
            }
        } elseif (!empty($params['bargain_id'])) {
            $where['bargain.bargain_id'] = $params['bargain_id'];
        }
        //合同开始时间
        if (!empty($params['start_start_date']) && !empty($params['end_start_date'])) {
            $where['bargain.start_date'] = ['between', $params['start_start_date'] . ',' . $params['end_start_date']];
        } elseif (!empty($params['start_start_date'])) {
            $where['bargain.start_date'] = ['egt', $params['start_start_date']];
        } elseif (!empty($params['end_start_date'])) {
            $where['bargain.start_date'] = ['elt', $params['end_start_date']];
        }
        //合同截止时间
        if (!empty($params['start_end_date']) && !empty($params['end_end_date'])) {
            $where['bargain.end_date'] = ['between', $params['start_end_date'] . ',' . $params['end_end_date']];
        } elseif (!empty($params['start_start_date'])) {
            $where['bargain.end_date'] = ['egt', $params['start_end_date']];
        } elseif (!empty($params['end_start_date'])) {
            $where['bargain.end_date'] = ['elt', $params['end_end_date']];
        }
        //排除删除的数据
        $where['bargain.delete_time'] = 0;
        return $where;
    }
    /**
     * 获取查询的数据
     *
     * @access protected
     * @param $where array 查询条件
     *
     * @return array
     **/
    protected function getDataForIndex($where = [])
    {
        $field = [
            'customer.name AS customer_name',
            'customer.customer_id',
            'bargain.bargain_id',
            'bargain.money',
            'bargain.company',
            'bargain.start_date',
            'bargain.end_date',
            'bargain.create_time',
            'bargain.update_time'
        ];
        //获取排序条件
        $orderBy = $this->getOrderForIndex();
        //获取分页内容
        $page = $this->getPageForIndex($where);
        $limit = $page['limit'];
        //获取本页的查询数据
        $data = $this
            ->bargain_model
            ->field($field)
            ->join('customer ON customer.customer_id = bargain.customer_id')
            ->where($where)
            ->where(DataAuthController::instance()->getBargainWhere())
            ->order($orderBy)
            ->limit($limit)
            ->select();
        //糅合其他数据
        $data = $this->mergeDataForIndex($data);
        $render = $page['render'];
        return compact('data', 'render');
    }
    /**
     * 获取排序条件
     *
     * @access protected
     *
     * @return string
     **/
    protected function getOrderForIndex()
    {
        $order = I('get.order', 'create_time', 'trim');
        $asc = I('get.asc', 0, 'intval');
        $orderString = 'bargain.' . $order . ($asc ? ' ASC' : ' DESC');
        return $orderString;
    }
    /**
     * 获取分页内容
     *
     * @access protected
     * @param $where array 查询条件
     *
     * @return array
     **/
    protected function getPageForIndex($where)
    {
        $total = $this->bargain_model->where($where)->count();
        $page = new Page($total, 15);
        return [
            'render'    => $page->show(),
            'limit'     => $page->firstRow . ',' . $page->listRows
        ];
    }
    /**
     * 获取页面展示的表单查询功能的参数
     *
     * @access protected
     * @param $params array 传递的参数
     *
     * @return array
     **/
    protected function getInputForIndex($params)
    {
        $input = [];
        $input['bargain_id'] = $params['bargain_id'];
        //客户选择option
        $input['customer_option'] = $this->getCustomerOption($params['customer_id']);
        //公司名称
        $input['company'] = $params['company'];
        //签约产品
        $product_data = $this->getAllStoreProduct();
        $input['product_option'] = makeOption(array_column($product_data, 'product_name', 'product_id'), $params['product_id']);
        //合同开始日期
        $input['start_start_date'] = $params['start_start_date'];
        $input['end_start_date'] = $params['end_start_date'];
        //合同截止日期
        $input['start_end_date'] = $params['start_end_date'];
        $input['end_end_date'] = $params['end_end_date'];
        return $input;
    }
    /**
     * 糅合其他的数据
     *
     * @access protected
     * @param $data array 查询的合同数据
     *
     * @return array
     **/
    protected function mergeDataForIndex($data)
    {
        //糅合签约产品数据
        $data = $this->mergeProductDataForIndex($data);
        //糅合打款单数据
        $data = $this->mergeRemitDataForIndex($data);
        //糅合剩余金额数据
        $data = $this->mergeBalanceDataForIndex($data);
        return $data;
    }
    /**
     * 糅合签约产品数据
     *
     * @access protected
     * @param $data array 查询的合同数据
     *
     * @return array
     **/
    protected function mergeProductDataForIndex($data)
    {
        //整理合同数据
        $data = array_column($data, null, 'bargain_id');
        $bargain_id = array_column($data, 'bargain_id');
        if (!empty($bargain_id)) {
            //获取本页的合同ID
            $bargain_id = ['in', $bargain_id];
            //查询所有签约的产品数据
            $bargain_product_data = $this->bargain_product_model->where(compact('bargain_id'), 'bargain_id, product_id')->select();
        } else {
            $bargain_product_data = [];
        }
        //获取所有的产品数据
        $product_data = $this->getAllStoreProduct();
        $product_data = array_column($product_data, 'product_name', 'product_id');
        //糅合
        array_walk($bargain_product_data, function ($item) use (&$data, $product_data) {
            $product_id = $item['product_id'];
            $bargain_id = $item['bargain_id'];
            $product_name = $product_data[$product_id];
            $data[$bargain_id]['product'][] = $product_name;
        });
        return $data;
    }
    /**
     * 糅合打款单数据
     *
     * @access protected 
     * @param $data array 合同数据
     *
     * @return array
     **/
    protected function mergeRemitDataForIndex($data)
    {
        //获取本页的客户ID
        $customer_id = array_column($data, 'customer_id');
        if (!empty($customer_id)) {
            $customer_id = ['in', $customer_id];
            //统计查询本页的所有客户的认款金额
            $status = 3;
            $remit_data = (new RemitModel())->field('SUM(money) as money, customer_id')->group('customer_id')->where(compact('customer_id', 'status'))->select();
            //整理认款金额数据
            $remit_data = array_column($remit_data, 'money', 'customer_id');
        } else {
            $remit_data = [];
        }
        //糅合数据
        array_walk($data, function (&$item) use ($remit_data) {
            $customer_id = $item['customer_id'];
            $item['remit_money'] = isset($remit_data[$customer_id])?$remit_data[$customer_id]:0;
        });
        return $data;
    }
    /**
     * 糅合客户余额数据
     *
     * @access protected
     * @param $data array 合同数据
     *
     * @return array
     **/
    protected function mergeBalanceDataForIndex($data)
    {
        //获取客户消费数据
        $customer_id = array_map(function ($item) {
           return 'customer_id[]=' . $item;
        }, array_column($data, 'customer_id'));
        $url = C('LIST_API_URL.customer_consume') . '?key=fb53fddb7157dd76fd1bb656df4980a3&' . implode('&', $customer_id);
        $consume_data = @json_decode(file_get_contents($url), true)['lists'];
        $consume_data = array_column($consume_data, null, 'customer_id');
        //糅合数据
        array_walk($data, function (&$item) use ($consume_data) {
            $customer_id = $item['customer_id'];
            $consume_money = isset( $consume_data[$customer_id]['money_sum']) ?  $consume_data[$customer_id]['money_sum'] : 0;
            $item['balance_money'] = round(bcsub($item['remit_money'], $consume_money,4), 2);
            $item['consume_meony'] = $consume_money;
        });
        return $data;
    }
    /**
     * 获取全部的可开通的产品数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getAllStoreProduct()
    {
        if (isset($this->product_all_data) && !empty($this->product_all_data)) {
            return $this->product_all_data;
        }
        $back_status = 1;
        $this->product_all_data = $this->product_data(compact('back_status'), 'product_id,product_name');
        return $this->product_all_data;
    }
    /**
     * 合同详细页面
     *
     * @access public
     *
     * @return array
     **/
    public function detail()
    {
        $bargain_id = I('get.bargain_id', '', 'trim');
        if (empty($bargain_id)) {
            throw new \Exception('不存在bargain_id');
        }
        //获取合同数据
        $delete_time = 0;
        $data = $this->bargain_model->where(compact('bargain_id', 'delete_time'))->find();
        if (empty($data)) {
            throw new \Exception('该数据不存在或已被删除');
        }
        //数据权限校验
        DataAuthController::instance()->validAllowDoCustomer($data['customer_id']);
        //获取客户名称
        $customer_id = $data['customer_id'];
        $data['customer_name'] = $this->customer_model->where(compact('customer_id'))->getField('name');
        //获取签约产品
        $product_id = $this->bargain_product_model->field('product_id')->where(compact('bargain_id'))->select();
        $product_id = array_column($product_id, null, 'product_id');
        $product_data = array_column($this->getAllStoreProduct(), 'product_name', 'product_id');
        $product_data = array_intersect_key($product_data, $product_id);
        $data['product'] = $product_data;
        //获取到款金额
        $status = 3;
        $remit_money = (new RemitModel())->where(compact('customer_id', 'status'))->sum('money');
        $data['remit_money'] = $remit_money?$remit_money:0;
        //获取剩余金额
        $url = C('LIST_API_URL.customer_consume') . '?key=fb53fddb7157dd76fd1bb656df4980a3&customer_id[]='.$customer_id;
        $consume_data = @json_decode(file_get_contents($url), true)['lists'];
        $data['consume_money'] = isset($consume_data[0]['money_sum'])?$consume_data[0]['money_sum']:0;
        $data['balance_money'] = round(bcsub($data['remit_money'], $data['consume_money'], 4), 2);
        //获取计费配置对应的消费金额与消费数量
        $data['fee_config_money'] = $this->getFeeConfigDataForDetail($customer_id, array_keys($product_data));
        return $data;
    }
    /**
     * 获取某客户的计费配置及消耗金额、数量
     *
     * @access protected
     * @param $customer_id string 客户ID
     * @param $sign_product_id array 签约产品ID
     *
     * @return array
     **/
    protected function getFeeConfigDataForDetail($customer_id, $sign_product_id)
    {
        //查询210子产品ID
        $father_id = 210;
        $children_210_product_id = $this->product_data(compact('father_id'), 'product_id');
        $children_210_product_id = array_column($children_210_product_id, 'product_id');
        //查询所有计费配置
        $is_delete = 0;
        if (in_array(210, $sign_product_id)) {
            unset($sign_product_id[210]);
            $sign_product_id = array_merge($sign_product_id, $children_210_product_id);
        }
        $product_id = ['in', $sign_product_id];
        $fee_config_data = (new FeeConfigModel())->field('id as section_id,account_id,product_id,start_date,fee_method,fee_time_rule,fee_amount_rule,fee_price')->where(compact('customer_id', 'is_delete', 'product_id'))->select();
        //通过接口获取每个计费配置的消耗金额、数量的数据
        $url = C('LIST_API_URL.fee_config_consume') . '?key=fb53fddb7157dd76fd1bb656df4980a3&';
        $section_id = array_map(function ($item) {
            return 'section_id[]=' . $item['section_id'];
        }, $fee_config_data);
        $data = @json_decode(file_get_contents($url . implode('&', $section_id)), true)['lists'];
        $data = array_column($data, null, 'section_id');
        //糅合相关的数据
        $result = [];
        $result210 = [];
        $product_data = array_column($this->getAllStoreProduct(), 'product_name', 'product_id');
        array_walk($fee_config_data, function ($item) use (&$result, &$result210, $data, $product_data, $children_210_product_id) {
            //计费配置ID
            $section_id = $item['section_id'];
            //签约产品
            $product_id = $item['product_id'];
            //计费开始时间
            $start_date = $item['start_date'];
            $product_name = $product_data[$product_id];
            //计费类型
            $fee_type = '';
            if ($item['fee_method']==1) {
                $fee_type .= '按时间--';
                switch ($item['fee_time_rule']) {
                    case 1:
                        $fee_type .= '包日';
                        break;
                    case 2:
                        $fee_type .= '包月';
                        break;
                    case 3:
                        $fee_type .= '包年';
                        break;
                }
            } else {
                switch($item['fee_amount_rule']) {
                    case 1:
                        $fee_type .= '固定价格';
                        break;
                    case 2:
                        $fee_type .= '累进阶梯';
                        break;
                    case 3:
                        $fee_type .= '到达阶梯';
                        break;
                }
            }
            //单价
            $price = $item['fee_price'];
            //总消耗量
            $total = $data[$section_id]['section_number_sum'];
            //总消耗金额
            $money = $data[$section_id]['money_sum'];
            //如果为210产品的子产品，则保存跳出此次糅合
            if (in_array($product_id, $children_210_product_id)) {
                //账号ID
                $account_id = $item['account_id'];
                if (!isset($result210[$account_id . '_' . $start_date])) {
                    $result210[$account_id . '_' . $start_date] = [
                        'section_id'    => [],
                        'product_name'  => '邦信分快捷版',
                        'fee_type'      => $fee_type,
                        'start_date'    => $start_date,
                        'price'         => $price,
                        'total'         => [],
                        'money'         => []
                    ];
                }
                $result210[$account_id . '_' . $start_date]['total'][] = $total;
                $result210[$account_id . '_' . $start_date]['money'][] = $money;
                $result210[$account_id . '_' . $start_date]['section_id'][] = $section_id;
            } else {
                $result[] = compact('section_id', 'product_name', 'fee_type', 'start_date', 'price', 'total', 'money');
            }
        });
        //糅合210产品
        array_walk($result210, function ($item) use (&$result) {
            $item['section_id'] = implode(',', $item['section_id']);
            $item['total'] = array_sum($item['total']);
            $item['money'] = array_sum($item['money']);
            $result[] = $item;
        });
        array_multisort(array_map('strtotime', array_column($result, 'start_date')), SORT_DESC, $result);
        return $result;
    }
    /**
     * 编辑合同
     *
     * @access public
     *
     * @return array
     **/
    public function edit()
    {
        //获取编辑的数据
        $bargain_id = I('get.bargain_id', '', 'trim');
        if (empty($bargain_id)) {
            throw new \Exception('不存在bargain_id');
        }
        $delete_time = 0;
        $data = $this->bargain_model->where(compact('bargain_id', 'delete_time'))->find();
        if (empty($data)) {
            throw new \Exception('该数据不存在或已被删除');
        }
        //数据权限校验
        DataAuthController::instance()->validAllowDoCustomer($data['customer_id']);
        $bargain_product = $this->bargain_product_model->field('product_id')->where(compact('bargain_id'))->select();
        $data['bargain_product_id'] = array_column($bargain_product, 'product_id');

        //获取所有的未被删除的客户的Option
        $customer_option = $this->getCustomerOption($data['customer_id']);

        //获取所有可签约产品
        $product_option = $this->getAllowSignProduct($data['bargain_product_id']);
        return compact('customer_option', 'data', 'product_option');
    }
    /**
     * 编辑合同执行
     *
     * @access public
     *
     * @return void
     **/
    public function run_edit()
    {
        //获取编辑的数据内容
        try {
            //获取数据
            $data = $this->getEditData();
            //生成需要增加的bargain数据
            $bargain_data = $this->getEditBargainData($data);
            //删除bargainProduct的所有数据
            $bargain_id = $data['bargain_id'];
            $this->bargain_product_model->where(compact('bargain_id'))->delete();
            //生成需要增加的bargainProduct数据
            $bargain_product_data = $this->getAddBargainProductData($data);
            //先增加bargainProduct数据（允许存在不正常数据，不影响使用）
            $res = $this->bargain_product_model->addAll($bargain_product_data);
            if ($res) {
                $res1 = $this->bargain_model->where(compact('bargain_id'))->save($bargain_data);
                if ($res1) {
                    $this->returnJson([], 0);
                } else {
                    throw new \Exception('合同产品保存成功，合同数据保存失败');
                }
            }
            throw new \Exception('合同产品数据保存失败');
        } catch (\Exception $exception) {
            $message = $exception->getMessage();
            $this->returnJson(compact('message'), 1);
        }
    }
    /**
     * 获取合同编辑的数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getEditData()
    {
        //获取合同编号
        $bargain_id = I('post.bargain_id', '', 'trim');
        if (empty($bargain_id)) {
            throw new \Exception('合同编号格式不正确');
        }
        $isExists = $this->bargain_model->where(compact('bargain_id'))->count();
        if (!$isExists) {
            throw new \Exception("【{$bargain_id}】合同编号不存在，请联系管理员");
        }
        //客户名称
        $customer_id = I('post.customer_id', '', 'trim');
        if (empty($customer_id)) {
            throw new \Exception('请选择关联客户');
        }
        $isExists = $this->customer_model->where(compact('customer_id'))->count();
        if (!$isExists) {
            throw new \Exception('客户ID不正确');
        }
        //公司名称
        $company = I('post.company', '', 'trim');
        if (empty($company)) {
            throw new \Exception('请填写公司名称');
        }
        //签约产品
        $product_id = I('post.product_id', []);
        if (empty($product_id)) {
            throw new \Exception('至少选择一个签约产品');
        }
        //金额
        $money = I('post.money', '', 'trim');
        if (empty($money)) {
            throw new \Exception('请填写合同金额');
        }
        $money_preg = '/^\d{1,11}(\.\d{1,2})?$/';
        if (!preg_match($money_preg, $money)) {
            throw new \Exception('合同金额格式不正确');
        }
        //合同开始时间
        $start_date = I('post.start_date', '', 'trim');
        if (empty($start_date)) {
            throw new \Exception('请选择合同开始时间');
        }
        //合同截止时间
        $end_date = I('post.end_date', '', 'trim');
        if (empty($end_date)) {
            throw new \Exception('请选择合同截止时间');
        }
        if (strtotime($start_date)>=strtotime($end_date)) {
            throw new \Exception('合同开始时间需小于合同截止时间');
        }
        //备注
        $remark = I('post.remark', '', 'trim');
        return compact('bargain_id', 'customer_id', 'company', 'product_id', 'money', 'start_date', 'end_date', 'remark');
    }
    /**
     * 获取编辑到bargain表中的数据
     *
     * @access protected
     * @param $data array 获取到的数据
     *
     * @return array
     **/
    protected function getEditBargainData($data)
    {
        return [
            'customer_id'       => $data['customer_id'],
            'company'           => $data['company'],
            'start_date'        => $data['start_date'],
            'end_date'          => $data['end_date'],
            'money'             => $data['money'],
            'remark'            => $data['remark'],
            'update_time'       => time(),
            'update_admin'      => session('site_login_name')
        ];
    }
    /**
     * 删除合同
     *
     * @access public
     *
     * @return void
     **/
    public function del()
    {
        $bargain_id = I('get.bargain_id', '', 'trim');
        if (empty($bargain_id)) {
            throw new \Exception('不存在bargain_id');
        }
        $data = $this->bargain_model->where(compact('bargain_id'))->find();
        //数据权限校验
        DataAuthController::instance()->validAllowDoCustomer($data['customer_id']);
        $res = $this->bargain_model->where(compact('bargain_id'))->save([
            'delete_time'   => time()
        ]);
        if (!$res) {
            throw new \Exception('删除失败');
        }
    }

    /**
     * 获取所有可签约产品Option
     *
     * @access protected
     * @param $default array 默认选中的产品
     *
     * @return string
     **/
    protected function getAllowSignProduct($default = [])
    {
        $back_status = 1;
        $data = $this->product_model->field('product_id, product_name')->where(compact('back_status'))->select();
        $option = '';
        foreach ($data as $item) {
            if (in_array($item['product_id'], $default)) {
                $option .= "<option value='{$item['product_id']}' selected>{$item['product_name']}</option>";
            } else {
                $option .= "<option value='{$item['product_id']}'>{$item['product_name']}</option>";
            }
        }
        return $option;
    }
}