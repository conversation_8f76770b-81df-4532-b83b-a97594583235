<?php

namespace Home\Repositories;

class BaseRepository
{
    /**
     * 计算上一个月的今天，如果上个月没有今天，则返回上一个月的最后一天
     * @param integer $time  今天的时间戳
     * @return string
     */
    public function lastMonthToday($time){
        $last_month_time = mktime(date("G", $time), date("i", $time),
            date("s", $time), date("n", $time), 0, date("Y", $time));
        $last_month_t =  date("t", $last_month_time);

        if ($last_month_t < date("j", $time)) {
            return date("Y-m-t", $last_month_time);
        }

        return date(date("Y-m", $last_month_time) . "-d", $time);
    }


    /**
     * 追加客户到产品列表
     * @param array $list_product 二维的产品数组
     * @param integer $type_id 产品类型
     * @return array
     */
    public function appendAccountInfo($list_product, $type_id)
    {
        // 客户列表
        $list_account = $this->getAccountList();

        // 客户产品的relationship
        $list_relationship = $this->getRelationshipOfAccountAndProduct($type_id);
        $list_relationship = array_column($list_relationship, null, 'product_id');

        // 整合
        return $this->appendAccountToProduct($list_product, $list_account, $list_relationship);
    }

    /**
     * 获取客户列表
     * @return array
     */
    public function getAccountList()
    {
        return D('FinanceAccounts')->index('id')
            ->select();
    }

    /**
     * 追加客户名称到产品信息中
     * @param $list_product
     * @param $list_account
     * @param $list_relationship
     * @return array
     */
    protected function appendAccountToProduct($list_product, $list_account, $list_relationship)
    {
        return array_map(function ($item_product) use ($list_account, $list_relationship) {
            $product_id = $item_product['id'];

            // 没有绑定客户的情况
            if (!array_key_exists($product_id, $list_relationship)) {
                $item_product['name_account'] = '';
                $item_product['id_account'] = '';
                return $item_product;
            }

            // 绑定客户的名字
            $account_id = $list_relationship[$product_id]['account_id'];
            $name_account = isset($list_account[$account_id]['name']) ? $list_account[$account_id]['name'] : '';
            $item_product['name_account'] = $name_account;
            $item_product['id_account'] = $account_id;

            return $item_product;
        }, $list_product);
    }

    /**
     * 客户产品的relationship
     * @param $type_id
     * @return mixed
     */
    protected function getRelationshipOfAccountAndProduct($type_id)
    {
        return D('FinanceAccountProduct')
            ->where(compact('type_id'))
            ->select();
    }


    /**
     * 解决range无法生成z之后的字母的问题
     * @param $lower
     * @param $upper
     * @return \Generator
     */
    public function excelColumnRange($lower, $upper)
    {
        $mid = $lower;
        while ($mid !== $upper) {
            // 返回第一个数据
            if ($mid === $lower) {
                yield $mid;
            }

            $mid ++;
            yield $mid;
        }
    }

    /**
     * 日志
     * @param $response
     * @return string
     */
    public function log($response)
    {
        // 組裝要寫日志
        $info_log = json_encode($response, JSON_UNESCAPED_UNICODE);

        // 檢查日志文件是否存在
        $dir = RUNTIME_PATH . date('Ymd') . '/';
        if (!file_exists($dir) || !is_dir($dir)) {
            @mkdir($dir, 0755, true);
        }

        // 日志文件名
        $log_name = $this->genLogName();
        $destination = $dir . $log_name . '.log';

        // 寫入
        file_put_contents(
            $destination,
            '[' . date('Y-m-d H:i:s') . ']  ' . $info_log . PHP_EOL,
            FILE_APPEND
        );
    }

    /**
     * 生成日志名字
     * @return mixed
     */
    protected function genLogName()
    {
        // 回溯调用者的文件的名字
        $file_name_invoking = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 2)[1]['file'];
        $file_name_invoking = trim($file_name_invoking, '.class.php');
        $list_name = explode('/', $file_name_invoking);

        return end($list_name);
    }

    /**
     * 发送post请求
     * @param $url
     * @param $post_data
     * @return array|mixed
     */
    protected function curlRequestForPost($url, $post_data)
    {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $list_stat = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'msg' => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($list_stat)) {
            return json_decode($list_stat, true);
        }

        return $list_stat;
    }

    /**
     * 百分比格式化
     * @param double $item 已经*100的值
     * @return string
     */
    public function formatWithPercentage($item)
    {
        if ($item == 'NA') {
            return $item;
        }
        return round($item, 2) . '%';
    }

    /**
     * (原来没有*100)百分比的换算
     * @param $item
     * @return string
     */
    public function formatSourcePercentage($item)
    {
        if ($item == 'NA') {
            return $item;
        }
        return round(100* $item, 2) . '%';
    }

    /**
     * 格式化数字
     * @param  integer $number
     * @return string
     */
    public function formatNumber($number)
    {
        if (!is_numeric($number)) {
            return $number;
        }
        return number_format($number, 0, '.', ',');
    }

    /**
     * 获取payload && formData数据
     * @return array
     */
    public function genParamsForPost()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        return array_merge(I('post.'), (array)$request_body);
    }

    /**
     * 为fileDownload插件生成文件
     * @param string $file_name 全部路径的文件名
     * @throws \Exception
     */
    public function fileDownload($file_name)
    {
        // basename要捕捉中文, 则需要进行本地化的设置
        setlocale(LC_ALL, 'zh_CN.GBK');
        if (!file_exists($file_name) || !is_file($file_name)) {
            throw new \Exception($file_name . ' 文件不存在,请核对后再试试');
        }
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        // del this file
       @unlink($file_name);
    }

    /**
     * 获取从一个数组中获取一个特定索引的数值， 支持默认值
     * @param $list_source
     * @param $key_search
     * @param string $default_value
     * @throws \Exception
     * @return mixed
     */
    public function getValueWithDefault($list_source, $key_search, $default_value = '')
    {
        if (!is_array($list_source) || !is_string($key_search)) {
            throw new \Exception(__METHOD__ . ' 函数需要第一个参数必须是array, 第二参数必选是string 第三个参数可选参数');
        }

        return isset($list_source[$key_search]) ? $list_source[$key_search] : $default_value;
    }

    /**
     * curl请求
     * @param  string $method 请求方式
     * @param  string $url 请求地址
     * @param  array $vars 传入参数
     * @return string 返回结果
     */
    public function getCurl($method, $url, $vars = array(), $build = 'form')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        if ($vars && 'form' == strtolower($build)) {
            $vars = http_build_query($vars, '', '&');
        }
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Expect:']);
        if ($vars && 'form' != strtolower($build)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type' => 'application/json', 'Content-Length' => strlen($vars)]);
        }
        if ($vars) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);
        }
        curl_setopt($curl, CURLOPT_TIMEOUT, 180);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 180);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        switch (strtoupper($method)) {
            case 'HEAD':
                curl_setopt($curl, CURLOPT_NOBODY, true);
                break;
            case 'GET':
                curl_setopt($curl, CURLOPT_HTTPGET, true);
                break;
            case 'POST':
                curl_setopt($curl, CURLOPT_POST, true);
                break;
            default:
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
                break;
        }

        $response = curl_exec($curl);
        //释放curl
        curl_close($curl);
        return $response;
    }

    /**
     * 获取所有的签约状态标识
     * @return array
     */
    public function getContractStatus()
    {
        return [1 => '已签约已付款', 2 => '已签约未付费', 3 => '未签约', 5 =>'特殊客户', 4 => '其他'];
    }
}
