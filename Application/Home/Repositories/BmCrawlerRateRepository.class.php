<?php
namespace Home\Repositories;

class BmCrawlerRateRepository extends BaseRepository
{
    // 设置流量分配
    protected $rate = '/channel/rate';
    // 支持状态
    protected $support = '/channel/support';
    // 列表
    protected $list = '/channel/list';
    // 主备切换
    protected $dist = '/yulore/dist';
    // 服务器列表
    protected $server = '/yulore/server/list';

    protected $channel = '/crawler/list';

    protected $switch = '/channel/switch';

    /**
     * 设置流量分配
     */
    public function setChannelRate()
    {
        $param = $this->getChannelRateParam();
        return $this->getChannelRateApi($param);
    }

    /**
     * 获取流量分配参数
     * @return json
     */
    public function getChannelRateParam()
    {
        $channel = I('post.channel', '', 'trim');
        $rate = I('post.rate', '', 'trim');
        if (!$channel || !$rate) {
            throw new \Exception('参数错误');
        }
        array_walk($rate, function(&$v, $k) {
            $v = $k.','.$v;
        });
        $channel = implode(',', $channel);
        $rate = implode(',', $rate);
        $note = '';
        return json_encode([compact('channel', 'rate', 'note')]);
    }

    /**
     * 设置支持状态
     */
    public function setChannelSupport()
    {
        $param = $this->getChannelSupportParam();
        return $this->getChannelSupportApi($param);
    }

    /**
     * 获取状态参数
     * @return json
     */
    public function getChannelSupportParam()
    {
        $channel = I('post.channel', '', 'trim');
        $support = I('post.support', 0, 'intval');
        if (!$channel) {
            throw new \Exception('参数错误');
        }
        $channel = implode(',', $channel);
        $support = 'yulore,'.$support;
        return json_encode([compact('channel', 'support')]);
    }

    /**
     * 设置主备服务器
     */
    public function setYuloreServer()
    {
        $param = $this->getYuloreSwitchParam();
        return $this->getYuloreSwitchApi($param);
    }

    /**
     * 获取主备服务器参数
     * @return json
     */
    public function getYuloreSwitchParam()
    {
        $active = I('post.active', '', 'trim');
        $channel = I('post.channel', '', 'trim');
        $server = $this->getServerList();
        $server = array_column($server, 'type');
        if (!$active || !in_array($active, $server) || !$channel) {
            throw new \Exception('参数错误');
        }
        array_walk($channel, function(&$v, $k, $p) {
            $m['channel'] = $v;
            $m['active'] = $p;
            $m['note'] = '';
            $v = $m;
        }, $active);
        return json_encode($channel);
    }

    /**
     * 一键切换供应商
     * @return array
     */
    public function setChannelSwitch()
    {
        $param = $this->getChannelSwitchParam();
        return $this->getChannelSwitchApi($param);
    }

    /**
     * 一键切换供应商参数
     * @return array
     */
    public function getChannelSwitchParam()
    {
        $switch = I('post.switch', '', 'trim');
        $channel = I('post.channel', '', 'trim');
        $list = $this->getChannelList();
        $list = array_keys($list);
        if (!$switch || !in_array($switch, $list) || !$channel) {
            throw new \Exception('参数错误');
        }
        array_walk($channel, function(&$v, $k, $p) {
            $m['channel'] = $v;
            $m['switch'] = $p;
            $m['note'] = '';
            $v = $m;
        }, $switch);
        print_r($channel);
        return json_encode($channel);
    }

    /**
     * 设置流量分配接口
     * @param  array $param 参数
     * @return true/false
     */
    public function getChannelRateApi($param)
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->rate;

        $res = $this->getCurl('POST', $url, $param, 'json');
        if (!$res) {
            throw new \Exception('接口返回为空');
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            throw new \Exception($res['msg']);
        }

        $channel = I('post.channel', '', 'trim');
        $rate = I('post.rate', '', 'trim');

        $data['nchannel'] = json_encode($rate);
        $data['flow_type'] = implode(',', $channel);
        $this->addLog($data);
        return true;
    }

    /**
     * 支持状态接口
     * @param  array $param 参数
     * @return true/false
     */
    public function getChannelSupportApi($param)
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->support;
        $res = $this->getCurl('POST', $url, $param, 'json');
        if (!$res) {
            throw new \Exception('接口返回为空');
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            throw new \Exception($res['msg']);
        }

        $channel = I('post.channel', '', 'trim');
        $support = I('post.support', 0, 'intval');
        $data['nchannel'] = '切换为'.$support.'状态';
        $data['ochannel'] = '';
        $data['flow_type'] = $channel;
        $data['area'] = '';
        $this->addLog($data);
        return true;
    }

    /**
     * 流量分配列表
     * @return array
     */
    public function getChannelRateList()
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->list;

        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 切换服务器接口
     * @param  array $param 参数
     * @return array
     */
    public function getYuloreSwitchApi($param)
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->dist;
        $res = $this->getCurl('POST', $url, $param, 'json');
        if (!$res) {
            throw new \Exception('接口返回为空');
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            throw new \Exception($res['msg']);
        }

        $active = I('post.active', '', 'trim');
        $channel = I('post.nchannel', '', 'trim');
        $data['nchannel'] = $channel.'切换为'.$active.'服务器';
        $data['flow_type'] = I('post.flow', '', 'trim');
        $this->addLog($data);
        return true;
    }

    /**
     * 一键切换
     * @param  array $param 参数
     * @return array
     */
    public function getChannelSwitchApi($param)
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->switch;
        $res = $this->getCurl('POST', $url, $param, 'json');
        if (!$res) {
            throw new \Exception('接口返回为空');
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            throw new \Exception($res['msg']);
        }

        $switch = I('post.switch', '', 'trim');
        $channel = I('post.nchannel', '', 'trim');

        $data['nchannel'] = '将'.$channel.'一键切换到'.$switch;
        $data['flow_type'] = I('post.flow', '', 'trim');

        $this->addLog($data);
        return true;
    }

    /**
     * 服务器列表
     * @return array
     */
    public function getServerList()
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->server;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 供应商列表
     * @return array
     */
    public function getChannelList()
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->channel;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        array_map(function($v) use (&$list) {
            $list[$v['channel']] = $v['name'];
        }, $res['data']);
        return $list;
    }

    public function addLog($data)
    {
        $data['admin'] = $_SESSION['site_login_name'];
        $data['created_at'] = time();
        $data['reason'] = I('post.reason', '', 'trim');
        return D('ChannelSwitchLog')->add($data);
    }
}