<?php

namespace Home\Repositories;

class CuishouPrivateStatRepository extends BaseRepository
{
    /**
     * 为统计列表生成初始化参数
     * @return array
     */
    public function genIniParamsForList()
    {
        $time_begin = $time_end = date('Y-m-d');
        return compact('time_begin', 'time_end');
    }

    /**
     * 为统计列表生成初始化参数
     * @return array
     */
    public function genIniParamsForDetail()
    {
        // 时间参数
        $params_time = $this->genTimeParamsForDetail();

        // 其他参数
        $params_other = $this->genOtherParamsForDetail();
        return array_merge($params_other, $params_time);
    }

    /**
     * 其他参数
     * @return array
     */
    protected function genOtherParamsForDetail()
    {
        return I('get.');
    }

    /**
     * 生成时间参数
     */
    protected function genTimeParamsForDetail()
    {
        $time_begin = $this->lastMonthToday(time());
        $time_end = date('Y-m-d');
        return compact('time_begin', 'time_end');
    }
}
