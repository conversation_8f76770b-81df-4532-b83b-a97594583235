<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-10 14:55:23
 */

namespace Home\Repositories;

class RiskListFeeRepository extends BaseRepository
{
    /**
     * 根据ID获取计费信息
     * @param  number $pro_id 账号ID
     * @return
     */
    public function getFeeConfig($pro_id)
    {
        if (!$pro_id) {
            return false;
        }
        $info = D('RiskListFeeConfig')->where(['product_id' => $pro_id, 'is_delete' => 0])->find();
        if (!$info) {
            return false;
        }
        $fee_price = json_decode($info['fee_price'], true);
        $fee_price = $this->getExplode($fee_price);
        $info['fee_price'] = $fee_price;
        return $info;
    }

    public function getExplode($fee_price)
    {
        $list = [];
        if ($fee_price && is_array($fee_price)) {
            foreach ($fee_price as $key => &$value) {
                if (is_array($value)) {
                    //$list[$key] = $this->getExplode($value);
                    $list[$key] = $value;
                } else {
                    if (strpos($value, '/') !== false) {
                        $price = explode('/', $value);
                        $list[$key] = $price;
                    } else {
                        $list[$key] = $value;
                    }
                }
            }
        }
        return $list;
    }

    /**
     * 获取今天的数据
     * @return info
     */
    public function getFeeConfigByTime($pro_id)
    {
        $start_time = strtotime(date('Y-m-d'));
        $end_time = strtotime(date('Y-m-d 23:59:59'));
        $complex['create_time'] = ['between', [$start_time, $end_time]];
        $complex['start_date'] = ['egt' => date('Y-m-d')];
        $complex['_logic'] = 'or';
        $where = ['product_id' => $pro_id, 'is_delete' => 0, '_complex' => $complex];
        return D('RiskListFeeConfig')->where($where)->field('id')->find();
    }

    /**
     * 添加、编辑
     * @param  number $product_id 账号ID
     * @return true/false
     */
    public function saveFeeConfig($pro_id)
    {
        $info = $this->getFeeConfigByTime($pro_id);
        $data = $this->getFeeConfigParam();
        $data['create_time'] = time();
        if (!$info) { //添加
            D('RiskListFeeConfig')->where(['product_id' => $pro_id, 'is_delete' => 0])->save(['is_delete' => 1]);
            $data['product_id'] = $pro_id;
            return D('RiskListFeeConfig')->add($data);
        }
        return D('RiskListFeeConfig')->where(['id' => $info['id']])->save($data);
    }

    /**
     * 获取计费配置参数
     * @return list
     */
    public function getFeeConfigParam()
    {
        $fee_basis = I('post.fee_basis', 0, 'intval');
        $fee_method = I('post.fee_method', 0, 'intval');
        $start_date = I('post.start_date', '', 'trim');
        $remarks = I('post.remarks', '', 'trim,strip_tags,stripslashes');
        if (!$fee_basis) {
            throw new \Exception('请选择计费依据');
        }
        if (!$fee_method) {
            throw new \Exception('请选择计费方式');
        }
        if (!$start_date) {
            throw new \Exception('请选择正式计费开始时间');
        }
        if ($remarks && strlen($remarks) > 1000) {
            throw new \Exception('备注不可超过1000个字符');
        }
        if ($fee_method == 1) { //按时间
            $fee_rule = $this->getFeeTimeParam();
        } elseif ($fee_method == 2) { //按用量
            $fee_rule = $this->getFeeAmountParam();
        }
        $base = compact('fee_basis', 'fee_method', 'start_date', 'remarks');
        return array_merge($base, $fee_rule);
    }

    /**
     * 按时间计费
     * @return list
     */
    public function getFeeTimeParam()
    {
        $fee_time_rule = I('post.fee_time_rule', 0, 'intval');
        $fee_time_own = sprintf("%.2f", I('post.fee_time_own', 0.00, 'floatval'));
        $fee_time_input = sprintf("%.2f", I('post.fee_time_input', 0.00, 'floatval'));
        if (!$fee_time_rule) {
            throw new \Exception('请选择时间计费规则');
        }
        if ($fee_time_own < 0) {
            throw new \Exception('请输入本人查得价格');
        }
        if ($fee_time_input < 0) {
            throw new \Exception('请输入本人查得价格');
        }
        $fee_amount_rule = 0;
        $fee_step_rule = 0;
        //计费价格
        $fee_price = json_encode([$fee_time_own, $fee_time_input]);
        $fee_rule = compact('fee_time_rule', 'fee_price', 'fee_amount_rule', 'fee_step_rule');
        return $fee_rule;
    }

    /**
     * 按用量计费
     * @return
     */
    public function getFeeAmountParam()
    {
        $fee_amount_rule = I('post.fee_amount_rule', 0, 'intval');
        if (!$fee_amount_rule) {
            throw new \Exception('请选择用量计费规则');
        }
        if ($fee_amount_rule == 1) { //用量固定单价
            $fee_rule = $this->getFeeFixedParam();
        } elseif ($fee_amount_rule == 2) { //阶梯计价
            $fee_rule = $this->getFeeStepParam();
        }
        $fee_time_rule = 0;
        return array_merge(compact('fee_amount_rule', 'fee_time_rule'), $fee_rule);
    }

    /**
     * 用量固定单价
     * @return
     */
    public function getFeeFixedParam()
    {
        $amount_own_price = I('post.amount_own_price', 0, 'intval');
        $amount_own_num = I('post.amount_own_num', 0, 'intval');
        $amount_input_price = I('post.amount_input_price', 0, 'intval');
        $amount_input_num = I('post.amount_input_num', 0, 'intval');
        if ($amount_own_num <= 0 || $amount_own_price < 0) {
            throw new \Exception('请输入正确的本人查得单价');
        }
        if ($amount_input_num <= 0 || $amount_input_price < 0) {
            throw new \Exception('请输入正确的联系人查得单价');
        }
        $fee_price = json_encode([$amount_own_price.'/'.$amount_own_num, $amount_input_price.'/'.$amount_input_num], JSON_UNESCAPED_SLASHES);
        $fee_step_rule = 0;
        return compact('fee_price', 'fee_step_rule');
    }

    /**
     * 用量阶梯计价
     * @return
     */
    public function getFeeStepParam()
    {
        $fee_step_rule = I('post.fee_step_rule', 0, 'intval');
        if (!$fee_step_rule) {
            throw new \Exception('请选择阶梯周期');
        }
        $fee_price = I('post.fee_price', '');
        if (!$fee_price) {
            throw new \Exception('请选择区间价格');
        }
        $fee_price = json_encode($fee_price, JSON_UNESCAPED_SLASHES);
        return compact('fee_step_rule', 'fee_price');
    }
}
