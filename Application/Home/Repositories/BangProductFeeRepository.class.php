<?php

/**
 * @Author: Administrator
 * @Date:   2018-07-26 10:21:58
 * @Last Modified by:   Administrator
 * @Last Modified time: 2018-07-26 14:49:06
 */

namespace Home\Repositories;

class BangProductFeeRepository extends BaseRepository
{
    /**
     * 获取计费配置信息
     * @param  number $product_id 账号ID
     * @return info
     */
    public function getFeeConfigInfo($product_id)
    {
        if (!$product_id) {
            return false;
        }
        $info = D('BangProductFeeConfig')->where(['product_id' => $product_id, 'is_delete' => 0])->find();
        if (!$info) {
            return false;
        }
        $fee_price = json_decode($info['fee_price'], true);
        $fee_price = $fee_price ? $fee_price : $info['fee_price'];
        if (is_string($fee_price) && strpos($fee_price, '/') !== false) {
            $fee_price = explode('/', $fee_price);
        }
        $info['fee_price'] = $fee_price;
        return $info;
    }

    /**
     * 保存计费配置
     * @param  number $product_id 账号ID
     * @return true/false
     */
    public function saveFeeConfig($product_id)
    {
        $info = $this->getFeeConfigByTime($product_id);
        $data = $this->getFeeConfigParam();
        $data['create_time'] = time();
        if (!$info) { //添加
            D('BangProductFeeConfig')->where(['product_id' => $product_id, 'is_delete' => 0])->save(['is_delete' => 1]);
            $data['product_id'] = $product_id;
            return D('BangProductFeeConfig')->add($data);
        }
        return D('BangProductFeeConfig')->where(['id' => $info['id']])->save($data);
    }

    /**
     * 获取今天或未正式计费开始的数据
     * @return info
     */
    public function getFeeConfigByTime($product_id)
    {
        $start_time = strtotime(date('Y-m-d'));
        $end_time = strtotime(date('Y-m-d 23:59:59'));
        $complex['create_time'] = ['between', [$start_time, $end_time]];
        $complex['start_date'] = ['egt' => date('Y-m-d')];
        $complex['_logic'] = 'or';
        $where = ['product_id' => $product_id, 'is_delete' => 0, '_complex' => $complex];
        return D('BangProductFeeConfig')->where($where)->field('id')->find();
    }

    /**
     * 获取计费配置参数
     * @return array
     */
    public function getFeeConfigParam()
    {
        $fee_basis = I('post.fee_basis', 0, 'intval');
        $fee_method = I('post.fee_method', 0, 'intval');
        $start_date = I('post.start_date', '', 'trim');
        $remarks = I('post.remarks', '', 'trim,strip_tags,stripslashe');
        if (!$fee_basis) {
            throw new \Exception('请选择计费依据');
        }
        if (!$fee_method) {
            throw new \Exception('请选择计费方式');
        }
        if (!$start_date) {
            throw new \Exception('请选择正式计费开始时间');
        }
        if ($remarks && strlen($remarks) > 1000) {
            throw new \Exception('备注不可超过1000个字符');
        }
        if ($fee_method == 1) { //按时间
            $fee_rule = $this->getFeeTimeParam();
        } elseif ($fee_method == 2) { //按用量
            $fee_rule = $this->getFeeAmountParam();
        }
        $base = compact('fee_basis', 'fee_method', 'start_date', 'remarks');
        return array_merge($base, $fee_rule);
    }

    /**
     * 获取时间计费规则参数
     * @return array
     */
    public function getFeeTimeParam()
    {
        $fee_time_rule = I('post.fee_time_rule', 0, 'intval');
        $fee_price = sprintf("%.2f", I('post.fee_time_price', 0.00, 'floatval'));
        if (!$fee_time_rule) {
            throw new \Exception('请选择时间计费规则');
        }
        if ($fee_price < 0) {
            throw new \Exception('请输入时间计费价格');
        }
        $fee_amount_rule = 0;
        $fee_step_rule = 0;
        return compact('fee_time_rule', 'fee_price', 'fee_amount_rule', 'fee_step_rule');
    }

    /**
     * 获取用量计费规则参数
     * @return array
     */
    public function getFeeAmountParam()
    {
        $fee_amount_rule = I('post.fee_amount_rule', 0, 'intval');
        if (!$fee_amount_rule) {
            throw new \Exception('请选择用量计费规则');
        }
        if ($fee_amount_rule == 1) { //用量固定单价
            $fee_rule = $this->getFeeFixedParam();
        } elseif ($fee_amount_rule == 2) { //阶梯计价
            $fee_rule = $this->getFeeStepParam();
        }
        $fee_time_rule = 0;
        return array_merge(compact('fee_amount_rule', 'fee_time_rule'), $fee_rule);
    }

    /**
     * 获取固定单价计费参数
     * @return array
     */
    public function getFeeFixedParam()
    {
        $amount_num = I('post.amount_num', 0, 'intval');
        $amount_price = I('post.amount_price', 0, 'intval');
        if ($amount_num <= 0 || $amount_price < 0) {
            throw new \Exception('请输入正确的计费单价');
        }
        $fee_price = $amount_price.'/'.$amount_num;
        $fee_step_rule = 0;
        return compact('fee_price', 'fee_step_rule');
    }

    /**
     * 获取阶梯计价计费参数
     * @return array
     */
    public function getFeeStepParam()
    {
        $fee_step_rule = I('post.fee_step_rule', 0, 'intval');
        if (!$fee_step_rule) {
            throw new \Exception('请选择阶梯周期');
        }
        $fee_price = I('post.fee_price', '');
        if (!$fee_price) {
            throw new \Exception('请选择区间价格');
        }
        $fee_price = json_encode($fee_price, JSON_UNESCAPED_SLASHES);
        return compact('fee_step_rule', 'fee_price');
    }
}