<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-30 14:13:20
 */
namespace Home\Repositories;

class BangProductFeeStatRepository extends BaseRepository
{
    /**
     * 获取邦企查日对账单列表
     * @return list
     */
    public function getBangProductFeeStatDay()
    {
        $date_list = $this->getFeeStatDateList();
        $where = $this->getFeeStatTimeParam();
        $where['product_id'] = I('get.product_id', 0, 'intval');
        $stat_list = D('BangProductFeeStat')->where($where)->index('fee_date')->select();
        $list = array_merge($date_list, $stat_list);
        $total_data['fee_amount'] = D('BangProductFeeStat')->where($where)->sum('fee_amount');
        $fee_price = D('BangProductFeeStat')->where($where)->sum('fee_price');
        $total_data['fee_price'] = sprintf('%.2f', $fee_price);
        return ['list' => $list, 'total_data' => $total_data];
    }

    /**
     * 日期列表
     * @return list
     */
    public function getFeeStatDateList()
    {
        $begin_date = I('get.begin_date', '', 'trim');
        $end_date = I('get.end_date', '', 'trim');

        $begin = $begin_date ? strtotime($begin_date) : strtotime("-1 day");
        $end = $end_date ? strtotime($end_date.' 23:59:59') : (strtotime(date('Y-m-d')) - 1);

        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }

    /**
     * 时间参数
     * @return param
     */
    public function getFeeStatTimeParam()
    {
        $begin_date = I('get.begin_date', '', 'trim');
        $end_date = I('get.end_date', '', 'trim');

        $begin_date = $begin_date ? $begin_date : date('Y-m-d', strtotime("-1 day"));
        $end_date = $end_date ? $end_date : date('Y-m-d', strtotime("-1 day"));
        $fee_date = ['between', [$begin_date, $end_date]];
        return compact('fee_date');
    }

    /**
     * 导出数据
     * @param  array $list 导出数据
     * @return true/false
     */
    public function getBangProductFeeStatDownload($list)
    {
        $product_id = I('get.product_id', 0, 'intval');
        $info = D('BangProducts')->where(['id' => $product_id])->field('name')->find();
        $product_name = isset($info['name']) ? $info['name'] : '';
        $begin = I('get.begin_date', '', 'strtotime');
        $end = I('get.end_date', '', 'strtotime');
        $begin_date = date('Ymd', $begin);
        $end_date = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/name_'.$product_name.'_邦企查日对账单_'.$begin_date.'至'.$end_date.'.csv';

        $title_list = '日期,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        $fee_amount = isset($list['total_data']['fee_amount']) ? $list['total_data']['fee_amount'] : 0;
        $fee_price = isset($list['total_data']['fee_price']) ? $list['total_data']['fee_price'] : '0.00';
        $total_str = '"总计","'.$fee_amount.'","'.$fee_price.'"';
        $total_str = mb_convert_encoding($total_str,'GBK','UTF-8');
        file_put_contents($file_name, PHP_EOL . $total_str, FILE_APPEND);

        foreach ($list['list'] as $key => $value) {

            $fee_amount = isset($value['fee_amount']) ? $value['fee_amount'] : 0;
            $fee_price = isset($value['fee_price']) ? $value['fee_price'] : '0.00';

            $file_str = '"'.$key.'","'.$fee_amount.'","'.$fee_price.'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }
}