<?php

namespace Home\Repositories;

use Common\Model\AdminApikeyModel;
use Think\Exception;

class BmMatchingFileRepository extends BaseRepository
{
    /*
     * 按天导出文件的title
     * */
    protected $list_title_day = [
        '日期', '账号名称', '客户名称', '单次查询量', '批量查询量'
    ];

    /*
     * 按天导出字段之间的关系
     * */
    protected $list_map_day = [
        '日期' => 'day',
        '账号名称' => 'owner',
        '客户名称' => 'name_account',
        '单次查询量' => 'itag',
        '批量查询量' => 'batch'
    ];

    /*
     * 列表导出文件title
     * */
    protected $list_title = [
        '账号ID', '账号名称', '客户名称', '单次查询量', '批量查询量'
    ];

    /*
     * 详情的title
     * */
    protected $list_title_detail = [
        '日期', '单次查询量', '批量查询量'
    ];

    /*
     * 详情导出文件的映射关系
     * */
    protected $list_map_detail = [
        '日期' => 'day',
        '单次查询量' => 'itag',
        '批量查询量' => 'batch',
    ];

    /*
     * 列表导出文件的映射关系
     * */
    protected $list_map = [
        '账号ID' => 'id',
        '账号名称' => 'owner',
        '客户名称' => 'name_account',
        '单次查询量' => 'itag',
        '批量查询量' => 'batch'
    ];

    // 统计repository
    protected $repository_stat;

    public function __construct()
    {
        $this->repository_stat = new BmMatchingStatRepository();
    }

    /**
     * 详情的导出文件
     * @throws \Exception
     */
    public function genFileForDetail()
    {
        // 获取数据
        $list_stat_data = $this->repository_stat->getStatDataForDetail();

        // 反格式化
        $list_stat_data = $this->formatItem($list_stat_data);

        // 生成文件
        $this->genDetailFile($list_stat_data);

        // 导出文件
        $file_name = $this->genFileNameForDetail();
        $this->fileDownload($file_name);
    }

    /**
     * 生成列表文件
     * @param array $list_data
     * @return  void
     */
    protected function genDetailFile($list_data)
    {
        // 为详情导出生成title
        $this->writeDetailTitleForCsv();

        // 填充内容
        $this->writeDetailContentForCsv($list_data);
    }

    /**
     * 为详情填充内容
     * @param $list_data
     */
    protected function writeDetailContentForCsv($list_data)
    {
        $file_name = $this->genFileNameForDetail();
        array_walk($list_data, function ($item) use ($file_name) {
            // 每行的内容
            $content_item = $this->genDetailContentForCsv($item);
            file_put_contents($file_name, PHP_EOL . $content_item, FILE_APPEND);
        });

    }

    /**
     * 为详情生成每行的内容
     * @param array $item
     * @return string
     */
    protected function genDetailContentForCsv($item)
    {
        $content_item = '';
        array_walk($this->list_title_detail, function ($title) use (&$content_item, $item) {
            $column = $this->list_map_detail[$title];
            $delimiter = $content_item !== '' ? ',' : '';
            $content_item .= $delimiter . (isset($item[$column]) ? $item[$column] : ' ');
        });
        return $content_item;
    }

    /**
     * 为详情导出生成title
     */
    protected function writeDetailTitleForCsv()
    {
        $file_name_day = $this->genFileNameForDetail();
        $content_title = implode(',', $this->list_title_detail);
        file_put_contents($file_name_day, $content_title);
    }

    /**
     * 生成详情的文件名
     * @return string
     */
    protected function genFileNameForDetail()
    {
        // 路径
        $path_name = RUNTIME_PATH;
        $file_prefix = '邦秒配详情文档-';

        // 文件名
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = $file_prefix . $begin . '-' . $end . '.csv';;
        return $path_name . $file_name;
    }

    /**
     * 格式化产品信息
     * @param array $list_stat_data 统计的原始数据
     * @return array
     */
    protected function formatItem($list_stat_data)
    {
        // 移除总计
        array_shift($list_stat_data);
        return array_map(function ($item) {
            if (strpos($item['itag'], ',') !== false) {
                $item['itag'] = str_replace(',', '', $item['itag']);
            }

            if (strpos($item['batch'], ',') !== false) {
                $item['batch'] = str_replace(',', '', $item['batch']);
            }
            return $item;
        }, $list_stat_data);
    }


    /**
     * 为统计列表生成文件
     * @throws \Exception
     */
    public function genFileForList()
    {
        // 获取列表数据
        $list_data = $this->repository_stat->getStatInfoForList();

        // 格式化信息
        $list_data = $this->formatProduct($list_data);

        // 生成文件
        $this->genListFile($list_data);

        $file_name = $this->genFileNameForList();
        $this->fileDownload($file_name);
    }

    /**
     * 生成列表文件
     * @param $list_data
     */
    protected function genListFile($list_data)
    {
        // 为按天导出生成title
        $this->writeListTitleForCsv();

        // 填充内容
        $this->writeListContentForCsv($list_data);
    }

    /**
     * 为列表填充内容
     * @param $list_data
     */
    protected function writeListContentForCsv($list_data)
    {
        $file_name = $this->genFileNameForList();
        array_walk($list_data, function ($item) use ($file_name) {
            // 每行的内容
            $content_item = $this->genListContentForCsv($item);
            file_put_contents($file_name, PHP_EOL . $content_item, FILE_APPEND);
        });

    }

    /**
     * 生成每行的内容
     * @param array $item
     * @return string
     */
    protected function genListContentForCsv($item)
    {
        $content_item = '';
        array_walk($this->list_title, function ($title) use (&$content_item, $item) {
            $column = $this->list_map[$title];
            $delimiter = $content_item !== '' ? ',' : '';
            $content_item .= $delimiter . (isset($item[$column]) ? $item[$column] : ' ');
        });
        return $content_item;
    }

    /**
     * 为按天导出生成Title
     */
    protected function writeListTitleForCsv()
    {
        $file_name_day = $this->genFileNameForList();
        $content_title = implode(',', $this->list_title);
        file_put_contents($file_name_day, $content_title);
    }

    /**
     * 生成列表的文件名
     * @return string
     */
    protected function genFileNameForList()
    {
        // 路径
        $path_name = RUNTIME_PATH;
        $file_prefix = '邦秒配列表文档-';

        // 文件名
        $request_body = I('get.');
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = $file_prefix . $begin . '-' . $end . '.csv';;
        return $path_name . $file_name;
    }

    /**
     * 生成按天统计的文件
     * @throws \Exception
     */
    public function genFile()
    {
        // 为按天导出生成数据
        $list_day_data = $this->genDataForTotal();

        // 生成文件
        $this->genFileForDay($list_day_data);

        // file_download return
        $file_name = $this->genFileNameForDay();
        $this->fileDownload($file_name);
    }

    /**
     * @param array $list_day_data 统计数据
     * @return string
     */
    protected function genFileForDay($list_day_data)
    {
        // 为按天导出生成title
        $this->writeTitleForCsv();

        // 填充内容
        $this->writeContentForCsv($list_day_data);
    }

    /**
     * 为按天导出生成实体内容
     * @param array $list_data
     */
    protected function writeContentForCsv($list_data)
    {
        $file_name = $this->genFileNameForDay();
        array_walk($list_data, function ($item) use ($file_name) {
            // 每行的内容
            $content_item = $this->genContentForCsv($item);
            file_put_contents($file_name, PHP_EOL . $content_item, FILE_APPEND);
        });
    }

    /**
     * 生成每行的内容
     * @param array $item
     * @return string
     */
    protected function genContentForCsv($item)
    {
        $content_item = '';
        array_walk($this->list_title_day, function ($title) use (&$content_item, $item) {
            $column = $this->list_map_day[$title];
            $delimiter = $content_item !== '' ? ',' : '';
            $content_item .= $delimiter . $item[$column];
        });
        return $content_item;
    }

    /**
     * 为按天导出生成Title
     */
    protected function writeTitleForCsv()
    {
        $file_name_day = $this->genFileNameForDay();
        $content_title = implode(',', $this->list_title_day);
        file_put_contents($file_name_day, $content_title);
    }

    /**
     * 生成详情的文件名
     * @return string
     */
    protected function genFileNameForDay()
    {
        // 路径
        $path_name = RUNTIME_PATH;
        $file_prefix = '邦秒配按天导出列表文档-';

        // 文件名
        $request_body = I('get.');
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = $file_prefix . $begin . '-' . $end . '.csv';;
        return $path_name . $file_name;
    }

    /**
     * 为按天导出生成数据
     * @return array
     */
    protected function genDataForTotal()
    {
        // 条件
        $where = $this->genConditionForDay();

        // 限定条件下各个产品的每天用量
        $list_day_info = $this->getGroupInfoByDay($where);

        // 限定的产品
        $list_product = $this->getLimitProduct();

        // 去掉账号名称中逗号
        $list_product = $this->formatProduct($list_product);

        // 格式化 && 补全没有数据的那些天
        return $this->tidyDataForTotal($list_day_info, $list_product);
    }

    /**
     * 获取限定的产品列表
     * @return array
     */
    protected function getLimitProduct()
    {
        // 条件
        $where = $this->genConditionForProduct();

        // 列表
        $list_product = $this->getProductList($where);

        // 追加客户信息
        return $this->appendAccountInfo($list_product, 2);
    }

    /**
     * 格式化产品信息
     * @param $list_product
     * @return array
     */
    protected function formatProduct($list_product)
    {
        return array_map(function ($item) {
            if (strpos($item['owner'], ',') !== false) {
                $item['owner'] = str_replace(',', ' ', $item['owner']);
            }

            if (strpos($item['itag'], ',') !== false) {
                $item['itag'] = str_replace(',', '', $item['itag']);
            }

            if (strpos($item['batch'], ',') !== false) {
                $item['batch'] = str_replace(',', '', $item['batch']);
            }

            return $item;
        }, $list_product);
    }

    /**
     * 获取产品
     * @param array $where
     * @return array
     */
    protected function getProductList($where)
    {
        $list_field = ['id', 'owner', 'active'];
        return D('AdminApikey')->where($where)
            ->field($list_field)
            ->select();
    }

    /**
     * 为限定产品生成条件
     * @return array
     */
    protected function genConditionForProduct()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && active
        $limit_base = $this->limitBaseInfo();

        // 条件整合
        return $this->tidyConditionForProduct($limit_product, $limit_base);
    }


    /**
     * 为限定的产品列表生成条件
     * @param array $limit_product 对于product_id的限定
     * @param array $limit_base 对于id的限定
     * @return array
     */
    protected function tidyConditionForProduct($limit_product, $limit_base)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['id' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $id = [
                'in', $limit_base
            ];
            return compact('id');
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $id = '哈哈,永远不可能数据的';
        } else {
            $id = [
                'in', $limit_intersect
            ];
        }

        return compact('id');
    }


    /**
     * 将原始数据整合成依天为单位包含单次和批量调用量的数据
     * @param array $list_day_info 分组得到的原始数据
     * @param array $list_product 限定的产品
     * @return array
     */
    protected function tidyDataForTotal($list_day_info, $list_product)
    {
        // 以$day_$id 为key的二位数组
        $list_day_data = $this->tidyDataToDay($list_day_info);

        // 统计数据绑定到产品
        return $this->appendStatToProduct($list_day_data, $list_product);
    }

    /**
     * 统计数据绑定到产品
     * @param array $list_day_data 每天的统计信息
     * @param array $list_product 限定的产品列表
     * @return array
     */
    protected function appendStatToProduct($list_day_data, $list_product)
    {
        // 容器
        $list_stat_data = [];

        // 需要天的范围
        $list_day_show = $this->dateRange();

        array_walk($list_product, function ($item_product) use (&$list_stat_data, $list_day_data, $list_day_show) {
            array_walk($list_day_show, function ($day) use (&$list_stat_data, $list_day_data, $item_product) {
                $key_unique = $day . '_' . $item_product['id'];

                // 如果这个用户这天没有统计数据的话
                if (!array_key_exists($key_unique, $list_day_data)) {
                    $batch = 0;
                    $itag = 0;
                } else {
                    $batch = $this->getValueWithDefault($list_day_data[$key_unique], 'batch', 0);
                    $itag = $this->getValueWithDefault($list_day_data[$key_unique], 'itag', 0);
                }

                $list_stat_data[$key_unique] = array_merge($item_product, compact('batch', 'itag', 'day'));
            });

        });

        return $list_stat_data;
    }

    /**
     * 需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = I('get.');
        $day_begin = date('Ymd', strtotime($request_body['begin']));
        $day_end = date('Ymd', strtotime($request_body['end']));

        // 容器 ['2018-10-01', '2018-10-02']
        $list_date = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            $date_format = date('Y-m-d', strtotime($day_end));
            array_push($list_date, $date_format);

            $day_end = date('Ymd', strtotime($day_end . '-1 day'));
        }

        return $list_date;
    }

    /**
     * 以天为key的二位数组
     * @param array $list_day_info
     * @return array
     */
    protected function tidyDataToDay($list_day_info)
    {
        // 容器 [2018-07-11-$id => [itag => 300]]
        $list_data = [];
        array_walk($list_day_info, function ($item) use (&$list_data) {
            $day = date('Y-m-d', strtotime($item['daily_time']));
            $id = $item['apikey'];
            $project = $item['project'];
            $day_used = $item['daily_used'];
            $list_data[$day . '_' . $id][$project] = $day_used;
        });

        return $list_data;
    }

    /**
     * 按天分组的信息
     * @param array $where
     * @return array
     */
    protected function getGroupInfoByDay($where)
    {
        return D('AdminApistat')->where($where)
            ->select();
    }

    /**
     * 为按天导出文件生成条件
     * @return array
     */
    protected function genConditionForDay()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && active
        $limit_base = $this->limitBaseInfo();

        // 时间限制
        $limit_time = $this->limitTime();

        // 条件整合
        return $this->tidyConditionForDay($limit_product, $limit_base, $limit_time);
    }

    /**
     * 条件整合
     * @param array $limit_product
     * @param array $limit_base
     * @param array $limit_time
     * @return array
     */
    protected function tidyConditionForDay($limit_product, $limit_base, $limit_time)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['apikey' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $apikey = [
                'in', $limit_base
            ];
            $limit_apikey = compact('apikey');
            return array_merge($limit_apikey, $limit_time);
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $apikey = '哈哈,永远不可能数据的';
        } else {
            $apikey = [
                'in', $limit_intersect
            ];
        }

        return array_merge(compact('apikey'), $limit_time);
    }

    /**
     * 时间限制
     * @return array
     */
    protected function limitTime()
    {
        $request_body = I('get.');

        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));

        $daily_time = [
            'between', [$begin, $end]
        ];
        return compact('daily_time');
    }

    /**
     * 限制基本的信息
     * @return array
     */
    protected function limitBaseInfo()
    {
        // 限制状态
        $limit_active = $this->limitActive();

        // 限制签约类型
        $limit_contract_status = $this->limitContractStatus();

        // 符合条件的产品
        $where = array_merge($limit_active, $limit_contract_status);
        $list_product = $this->getProductByCondition($where);

        // 转成product_id的限制
        return array_map(function ($item) {
            return $item['id'];
        }, $list_product);
    }

    /**
     * 根据条件获取邦秒配产品
     * @param array $where
     * @return array
     */
    protected function getProductByCondition($where)
    {
        return D('AdminApikey')->where($where)
            ->select();
    }

    /**
     * 限制签约类型
     * @return array
     */
    protected function limitContractStatus()
    {
        $request_body = I('get.');
        if (!array_key_exists('contract_status', $request_body) || !$request_body['contract_status']) {
            return [];
        }
        $contract_status = $request_body['contract_status'];
        return compact('contract_status');
    }

    /**
     * 限制状态
     * @return array
     */
    protected function limitActive()
    {
        $request_body = I('get.');

        if (!array_key_exists('active', $request_body) || !$request_body['active']) {
            return [];
        }
        $active = $request_body['active'];
        return compact('active');
    }

    /**
     * 限制 product_id
     */
    protected function limitProduct()
    {
        $request_body = I('get.');

        // 如果没有限制product_id，也没有限制account_id
        $limit_both_not = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_both_not) {
            return [];
        }

        // 如果只是限制了product_id
        $limit_product_only = (array_key_exists('product_id', $request_body) && $request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_product_only) {
            $product_id = [$request_body['product_id']];
            return compact('product_id');
        }

        // 如果只是限制了account_id
        $limit_account_only = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (array_key_exists('account_id', $request_body) && $request_body['account_id']);
        if ($limit_account_only) {
            $account_id = $request_body['account_id'];
            $product_id = $this->getRelationshipOfAccount($account_id);
            return compact('product_id');
        }

        // 同时限制了product_id && account_id
        return $this->limitProdutAndAccount($request_body);
    }


    /**
     * 同时限制了product_id && account_id
     * @param $request_body
     * @return array
     */
    protected function limitProdutAndAccount($request_body)
    {
        $product_id = $request_body['product_id'];
        $account_id = $request_body['account_id'];
        $list_product = $this->getRelationshipOfAccount($account_id);
        if (in_array($product_id, $list_product)) {
            return ['product_id' => [$product_id]];
        } else {
            return ['product_id' => ['永远不可能有值的哈哈']];
        }
    }

    /**
     * 获取特定客户下辖的邦秒配产品ID
     * @param integer $account_id
     * @return array
     */
    protected function getRelationshipOfAccount($account_id)
    {
        $type_id = 2;
        $where = compact('type_id', 'account_id');
        $list_relationship = D('FinanceAccountProduct')->where($where)
            ->select();

        return array_map(function ($item) {
            return $item['product_id'];
        }, $list_relationship);
    }


    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '账号ID,账号名称,所属用户,状态,签约状态,APIKEY,签名字符串,接口调用示例,Time';
        $title_list = mb_convert_encoding($title_list, 'GBK', 'UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $stat_data) {
            // 数据补全
            $file_str = '"' . $stat_data['id'] . '","' . $stat_data['owner'] . '","' . $stat_data['account_name'] . '","' . $stat_data['active'] . '","'
                . $stat_data['contract_status'] . '","' . $stat_data['apikey'] . '","' .
                $stat_data['sig_str'] . '","' . $stat_data['api_example'] . '","' . $stat_data['timeStr'] . '"';

            $file_str = mb_convert_encoding($file_str, 'GBK', 'UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
