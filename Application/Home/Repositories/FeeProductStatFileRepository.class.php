<?php
/**
 * @Author: lidandan
 * @Date:   2018-08-15 16:44:29
 */
namespace Home\Repositories;

class FeeProductStatFileRepository extends BaseRepository
{
    /**
     * 导出产品对账单列表
     * @param  array $list 对账单数据
     * @return true
     */
    public function getFeeProductStatDownload($list)
    {
        $file_name = RUNTIME_PATH . 'Cache/fee_product_stat.csv';
        $title_list = '产品名称,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        if ($list) {
            foreach ($list as $key => $value) {
                $file_str = '"'.$value['name'].'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';
                $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
                file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
            }
        }
        $this->fileDownload($file_name);
    }

    /**
     * 导出产品客户对账单
     * @param  array $list 对账单数据
     * @return true
     */
    public function getFeeAccountStatDownload($list)
    {
        $file_name = RUNTIME_PATH . 'Cache/fee_product_account_stat.csv';
        $title_list = '客户ID,客户名称,公司名称,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        if ($list && is_array($list)) {
            foreach ($list as $key => $value) {
                $file_str = '"'.$value['account_id'].'","'.str_replace(',', '，', $value['account_name']).'","'.str_replace(',', '，', $value['company']).'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';
                $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
                file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
            }
        }
        $this->fileDownload($file_name);
    }

    /**
     * 导出产品账户日对账单（邦秒爬、邦秒配、催收分、邦企查日对账单）
     * @param  array $list 对账单数据
     * @return true
     */
    public function getFeeDayStatDownload($list)
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $begin_date = date('Ymd', $begin);
        $end_date = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/fee_day_stat_'.$begin_date.'至'.$end_date.'.csv';
        $title_list = '日期,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        if ($list && is_array($list)) {
            foreach ($list as $key => $value) {
                $fee_amount = isset($value['fee_amount']) ? $value['fee_amount'] : 0;
                $fee_price = isset($value['fee_price']) ? $value['fee_price'] : '0.00';

                $file_str = '"'.$key.'","'.$fee_amount.'","'.$fee_price.'"';
                $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
                file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
            }
        }
        $this->fileDownload($file_name);
    }

    /**
     * 导出风险名单日对账单
     * @param  array $list 对账单数据
     * @return true
     */
    public function getRistStatFeeDayStatDownload($list)
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $begin_date = date('Ymd', $begin);
        $end_date = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/fee_day_stat_'.$begin_date.'至'.$end_date.'.csv';
        $title_list = '日期,本人查得量,本人查得费用,联系人查得量,联系人查得费用';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        if ($list && is_array($list)) {
            foreach ($list as $key => $value) {
                $fee_own_num = isset($value['fee_own_num']) ? $value['fee_own_num'] : 0;
                $fee_own_price = isset($value['fee_own_price']) ? $value['fee_own_price'] : '0.00';
                $fee_input_num = isset($value['fee_input_num']) ? $value['fee_input_num'] : 0;
                $fee_input_price = isset($value['fee_input_price']) ? $value['fee_input_price'] : '0.00';

                $file_str = '"'.$key.'","'.$fee_own_num.'","'.$fee_own_price.'","'.$fee_input_num.'","'.$fee_input_price.'"';

                $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
                file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
            }
        }

        $this->fileDownload($file_name);
    }

}
