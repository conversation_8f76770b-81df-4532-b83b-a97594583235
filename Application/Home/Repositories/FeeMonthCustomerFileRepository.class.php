<?php
namespace Home\Repositories;

class FeeMonthCustomerFileRepository extends BaseRepository
{
    public function getFeeMonthCustomerDownload($list)
    {
        $date = $this->getFeeMonthTime();
        $file_name = RUNTIME_PATH . 'Cache/客户对账单'.$date['start'].'~'.$date['end'].'.csv';
        $file_name = mb_convert_encoding($file_name,'GBK','UTF-8');
        $title_list = '客户ID,客户名称,公司名称,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $file_str = '"'.$value['customer_id'].'","'.$value['customer_name'].'","'.$value['customer_company'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getFeeMonthCustomerProductDownload($list)
    {
        $customer = $this->getCustomerInfo();
        $date = $this->getFeeMonthTime();
        $name = isset($customer['name']) ? $customer['name'] : '';
        $file_name = RUNTIME_PATH . 'Cache/'.$name.'客户产品对账单'.$date['start'].'~'.$date['end'].'.csv';
        $file_name = mb_convert_encoding($file_name, 'GBK', 'UTF-8');
        $title_list = '产品名称,账号名称,计费依据,计费方式,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $file_str = '"'.$value['product_name'].'","'.$value['account_name'].'","'.$value['fee_basis'].'","'.$value['fee_method'].'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getFeeMonthProductDownload($list, $product_info)
    {
        $date = $this->getFeeMonthTime();
        $file_name = RUNTIME_PATH . 'Cache/'.$product_info['product_name'].'产品月对账单'.$date['start'].'~'.$date['end'].'.csv';
        $file_name = mb_convert_encoding($file_name,'GBK','UTF-8');
        $title_list = '日期,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $month = date('Y/m/01', strtotime($value['month']));
            $file_str = '"'.$month.'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getFeeDayProductDownload($list, $product_info)
    {
        $date = $this->getFeeMonthTime();
        $file_name = RUNTIME_PATH . 'Cache/'.$product_info['product_name'].'产品日对账单'.$date['start'].'~'.$date['end'].'.csv';
        $file_name = mb_convert_encoding($file_name,'GBK','UTF-8');
        $title_list = '日期,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $file_str = '"'.$value['date'].'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getCustomerInfo()
    {
        $customer_id = I('get.customer_id', '', 'trim');
        $customer_name = I('get.customer_name', '', 'trim');
        $where = [];
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }
        if ($customer_name) {
            if ($customer_id && $customer_name != $customer_id) {
                $where['customer_id'] = 'no result';
            }
            $where['customer_id'] = $customer_name;
        }
        return D('Account/Customer')->field('name')->where($where)->find();
    }

    public function getFeeMonthTime()
    {
        $start = I('get.start', date('Y-m', strtotime('-1 month')), 'trim');
        $end = I('get.end', date('Y-m', strtotime('-1 month')), 'trim');
        return compact('start', 'end');
    }
}