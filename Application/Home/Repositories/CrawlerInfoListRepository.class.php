<?php
namespace Home\Repositories;

class CrawlerInfoListRepository extends BaseRepository
{
    protected $url = '/admin/crawler/search';

    /**
     * 爬虫信息查询接口
     * @param  array $param 参数
     * @return array
     */
    public function getCrawlerInfoApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->url;
        $url = $param ? $url.'?'.http_build_query($param, '', '&') : $url;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 下载
     * @param  array  $msg  下载内容
     * @param  string $type 表名
     * @param  string $sid  sid
     * @return array
     */
    public function getCrawlerInfoDownload($msg, $type, $sid)
    {
        $file_name = RUNTIME_PATH.'Cache/'.$type.'_'.$sid.'_result.txt';
        foreach ($msg as $key => $value) {
            unset($value['_id']);
            $msg[$key] = json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            file_put_contents($file_name, $msg[$key] . PHP_EOL, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }
}