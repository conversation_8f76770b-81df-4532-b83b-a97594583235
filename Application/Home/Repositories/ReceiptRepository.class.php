<?php

namespace Home\Repositories;


use Account\Model\AccountModel;
use Account\Model\CustomerModel;
use Account\Repositories\CustomerSalesmanHistoryRepository;
use Common\Common\Feishu;
use Common\Controller\DataAuthController;
use Common\Model\CommonEnumModel;
use Common\Model\CustomerConsumeModel;
use Common\Model\CustomerInvoiceModel;
use Common\Model\ReceiptModel;
use Common\Model\RelInvoiceConsumeModel;
use Common\Model\RelInvoiceRemitModel;
use Common\Model\RelRemitConsumeModel;
use Common\Model\RemitBalanceModel;
use Common\Model\RemitModel;
use Common\Model\RemitSplitPriceModel;
use Common\Model\SystemUserModel;
use Common\ORG\Page;
use Common\Repositories\DeptSalesmanRepository;
use Exception;
use Home\Model\BillProductIncomeV2Model;
use Home\Model\ProductConfigModel;
use Home\Model\PushReceiptModel;
use Think\Upload;

//收款单管理
class ReceiptRepository
{
    private $model;
    /**
     * @var \PHPExcel
     **/
    private $objPHPExcel;

    public function __construct()
    {
        $this->model = new ReceiptModel();
    }

    /**
     * 获取收款单的数据
     *
     * @access public
     *
     * @param $where array 查询的条件
     * @param $field string|array 需要查询的字段
     * @param $order string 排序号
     * @param $page  Page 如果存在这个参数，
     *
     * @return array
     **/
    public function data($where = [], $field = '*', $order = null, $page = null)
    {
        $where = array_merge([
            'delete_time' => 0
        ], $where);
        foreach ($where as $key => $value) {
            if ($key == 'customer_id') {
                $where['r2.' . $key] = $value;
            } else {
                $where['r.' . $key] = $value;
            }
            unset($where[$key]);
        }
        $model = $this->model->alias('r')->field($field)
            ->join('remit r2 on r.receipt_serial = r2.receipt_serial', 'LEFT')
            ->where(DataAuthController::instance()->getRemiteWhere())
            ->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        if (!is_null($page)) {
            $model = $model->limit($page->firstRow, $page->listRows);
        }
        $list = $model->select();
        return $list;
    }

    public function dataAuth($where = [], $field = '*', $order = null, $page = null){
        $where = array_merge([
            'delete_time' => 0
        ], $where);
        foreach ($where as $key => $value) {
            if ($key == 'customer_id') {
                $where['r2.' . $key] = $value;
            } else {
                $where['r.' . $key] = $value;
            }
            unset($where[$key]);
        }

        //获取历史商务
        // $auth_customer_ids = DataAuthController::instance()->getRemiteWhere(true);
        // $auth_customer_ids = array_merge($auth_customer_ids,$this->getOldCustomer());
        //            ->where('customer_id in ("'.implode('","', $auth_customer_ids) . '")')


        $model = $this->model->alias('r')->field($field)
            ->join('remit r2 on r.receipt_serial = r2.receipt_serial', 'LEFT')
            ->where(DataAuthController::instance()->getRemiteWhere())
            ->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }

        $list = $model->select();

        foreach ($list as $key => $item) {
            // 过滤已认款 未认款无法确定客户和来源
            if($item['customer_id']!==null && $item['source']!==null){
                if(DataAuthController::instance()->filterSourceAuth($item['customer_id'],$item['source'])){
                    unset($list[$key]);
                }
            }
        }

        if (!is_null($page)) {
            return array_slice($list,$page->firstRow,$page->listRows);
            // $model = $model->limit($page->firstRow, $page->listRows);
        }
        return $list;
    }

    // private function getOldCustomer(){
    //     //获取历史商务
    //     $user_info = (new SystemUserModel())->getCurrentUser();
    //     $old_customer_ids = (new CustomerSalesmanHistoryModel())->getOldCustomerIds($user_info['username']);
    //     return array_column($old_customer_ids,'customer_id');
    // }

    /**
     * 获取收款单数据的数量
     *
     * @access public
     *
     * @param $where array 查询的条件
     *
     * @return integer
     **/
    public function count($where = [])
    {
        $where = array_merge([
            'delete_time' => 0
        ], $where);
        $field = 'r2.id,r2.customer_id,r2.source';
        $count = $this->dataAuth($where, $field);
        return count($count);
    }

    public function getMoneySum($where = [])
    {
        $field = 'r.id,r.money,r2.customer_id,r2.source';
        $money = $this->dataAuth($where, $field);
        return array_sum(array_column($money,'money'));
    }

    /**
     * 获取list数据
     *
     * @access public
     *
     * @return array
     **/
    public function getListData()
    {
        //获取get参数
        $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);

        //获取符合当前条件的数据数量
        $count = $this->count($where);

        //所有客户数据
        // //获取历史商务
        // $auth_customer_ids = DataAuthController::instance()->getRemiteWhere(true);
        // $auth_customer_ids = array_merge($auth_customer_ids,$this->getOldCustomer());
        //

        $customer = (new CustomerModel)->field('company, name, customer_id, payment_type, salesman, is_delete')
            ->where(DataAuthController::instance()->getRemiteWhere())->select();
        //分页
        $listRow = 15;
        $page = new Page($count, $listRow);


        //用户数据
        // $user_data = (new SystemUserModel())->alias('u')->field('u.realname,u.username,d.dept_name')->join('crs_system_dept as d on u.dept_id = d.dept_id', 'left')->select();
        // $user_data = array_column($user_data, null, 'username');
        $_user_data = DeptSalesmanRepository::getSalesmanDeptMap();
        $user_data = [];
        foreach($_user_data as $user_name => $user_info){
            $user_data[$user_name] = [
                'realname'  => $user_info['realname'],
                'username'  => $user_name,
                'dept_name' => $user_info['area'],
            ];
        }

        //查询数据
        $list_data = $this->dataAuth($where, 'r.*, r2.customer_id , r2.remit_serial,r2.source', 'r.remit_date desc', $page);
        $list_data = $this->mergeCustomerRemit($customer, $list_data, $user_data);


        $page = $page->show();
        //获取input内容
        $input = $this->getInputByParam($params, $customer);
        //统计合计金额
        $where['delete_time'] = 0;
        $total_money = $this->getMoneySum($where);
        //已认款金额
        $where['status'] = ['egt', 2];
        $affirm_money = $this->getMoneySum($where);

        //余额数据
        $balance_list = (new RemitBalanceModel())->field('receipt_serial,remit_balance')->where(['remit_balance' => ['gt',0]])->select();
        $balance_list = array_column($balance_list, null,'receipt_serial');

        $customer = json_encode(array_column($customer, null, 'customer_id'), JSON_UNESCAPED_UNICODE);
        $user_json = json_encode($user_data, JSON_UNESCAPED_UNICODE);
        return compact('list_data', 'page', 'input', 'count', 'total_money', 'affirm_money', 'user_data', 'customer', 'user_json','balance_list');
    }

    /**
     * 将客户数据与打款单数据糅合到一起
     *
     * @access private
     * @param $customer array 客户数据
     * @param $data array 打款单数据
     *
     * @return array
     **/
    private function mergeCustomerRemit($customer, $data, $user_data)
    {
        $customer = array_column($customer, null, 'customer_id');
        $receipt_serial = array_column($data, 'receipt_serial');

        $rsp_list = [];
        if(!empty($receipt_serial)){
            $_rsp_list = (new RemitSplitPriceModel())->gettListByWhere(['receipt_serial' => ['in',$receipt_serial],'delete_time' => 0]);
            foreach($_rsp_list as $rsp_info){
                $rsp_list[$rsp_info['receipt_serial']][] = $rsp_info;
            }
        }

        //每月客户的商务
        $csh_repository = new CustomerSalesmanHistoryRepository();
        $csh_res = $csh_repository->getListMonthly(array_keys($customer), date("Ym",strtotime('20190101')),'','Ym');


        $result = [];
        $sourcePairs =(new CommonEnumModel())->getEnumPairs(1);
        array_walk($data, function ($item) use (&$result, $customer,$sourcePairs,$user_data,$csh_res,$rsp_list) {
            $item['customer_name'] = $customer[$item['customer_id']]['name'] . (($customer[$item['customer_id']]['is_delete']) ? '<span style="color:red;">（已删除）</span>' : '');
            $item['customer_is_delete'] = $customer[$item['customer_id']]['is_delete'];
            $item['company'] = $customer[$item['customer_id']]['company'];
            $item['payment_type'] = $customer[$item['customer_id']]['payment_type'];
            $item['payment_type_name'] = $customer[$item['customer_id']]['payment_type'] == 1 ? '预付费' : '后付费';
            $item['customer_type'] = $customer[$item['customer_id']]['customer_type'];
            $item['source_label'] = $sourcePairs[$item['source']];

            if($item['status'] == 3){//已拆单
                $salesman = [];
                $dept_name = [];
                foreach($rsp_list[$item['receipt_serial']] as $rsp_info){
                    if(isset($csh_res[$item['customer_id']][$rsp_info['month']])) {
                        $_salesman = $user_data[$csh_res[$item['customer_id']][$rsp_info['month']]];
                        $salesman[$_salesman['username']]  = $_salesman['realname'];
                        $dept_name[$_salesman['username']] = $_salesman['dept_name'];
                    }
                }

                $item['salesman']  = implode(",",array_unique($salesman));
                $item['dept_name'] = implode(",",array_unique($dept_name));


            }else if($item['status'] == 2){//已认款
                $item['salesman']  = $user_data[$customer[$item['customer_id']]['salesman']]['realname'];
                $item['dept_name'] = $user_data[$customer[$item['customer_id']]['salesman']]['dept_name'];
            }else{
                $item['salesman']  = '';
                $item['dept_name'] = '';
            }


            $result[] = $item;
        });
        return $result;
    }

    /**
     * 通过GET参数获取查询条件
     *
     * @access protected
     *
     * @param $params array 通过Get获取的参数
     *
     * @return array
     **/
    protected function getWhereByParam($params)
    {
        $where = [];
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $start_time = min($time);
            $end_time = max($time) + 86399;
            $where['remit_date'] = ['between', [$start_time, $end_time]];
        } elseif (!empty($params['start_time'])) {
            $where['remit_date'] = ['egt', strtotime($params['start_time'])];
        } elseif (!empty($params['end_time'])) {
            $where['remit_date'] = ['elt', strtotime($params['end_time'])];
        }

        //金额区间
        if (!empty($params['max_money']) && !empty($params['min_money'])) {
            if ($params['min_money'] < $params['max_money']) {
                $where['money'] = ['between', [$params['min_money'], $params['max_money']]];
            } else {
                $where['money'] = ['between', [$params['max_money'], $params['min_money']]];
            }
        } elseif (!empty($params['max_money'])) {
            $where['money'] = ['elt', $params['max_money']];
        } elseif (!empty($params['min_money'])) {
            $where['money'] = ['egt', $params['min_money']];
        }
        //付款方名称
        if (!empty($params['name'])) {
            $where['name'] = ['eq', $params['name']];
        }

        //流水号
        if (!empty($params['receipt_serial'])) {
            $where['receipt_serial'] = ['eq', $params['receipt_serial']];
        }
        //状态
        if ($params['status'] != '-1') {
            if ($params['status'] == 1) {
                $where['status'] = ['elt', $params['status']];
            } else {
                $where['status'] = ['eq', $params['status']];
            }
        }
        //来源
        if ($params['source'] != '-1') {
           $where['source'] = ['eq', $params['source']];
        }
        if (isset($params['customer_id']) && $params['customer_id']) {
            $where['customer_id'] = $params['customer_id'];
        }

        //客户付费类型
        if (isset($params['payment_type']) && in_array($params['payment_type'],[1,2])) {
            $customer_ids = (new CustomerModel)->field('customer_id')->where(['payment_type' => $params['payment_type']])->select();
            $customer_ids = array_column($customer_ids, 'customer_id');
            $where['customer_id'] = ['in',$customer_ids];
        }

        //存在余额的收款单
        if (isset($params['show_balance']) && $params['show_balance'] == 1) {
            $remit_balance = (new RemitBalanceModel())->field('receipt_serial')->where(['remit_balance' => ['gt',0]])->select();
            $receipt_serial_arr = array_column($remit_balance, 'receipt_serial');
            if(key_exists('receipt_serial', $where)){
                if(!in_array($params['receipt_serial'],$receipt_serial_arr)){//即选择的收款单不是有余额的收款单
                    $where['receipt_serial'] = ['eq', '-'];//破坏查询条件 查询返回空
                }//else $where['receipt_serial'] = ['eq', $params['receipt_serial']]; 不变
            }else{
                $where['receipt_serial'] = ['in',$receipt_serial_arr];
            }
        }

        return $where;
    }

    /**
     * 通过GET参数获取页面中显示的表单内容Input
     *
     * @access protected
     *
     * @param $params array 通过Get获取的参数
     * @param $customer array 客户
     * @return array
     **/
    protected function getInputByParam($params, $customer = [])
    {
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $params['start_time'] = date('Y-m-d', min($time));
            $params['end_time'] = date('Y-m-d', max($time));
        }
        //付款方名称Option
        $name = $this->data(['delete_time' => 0], 'r.name');
        $params['name_option'] = makeOption(array_column($name, 'name', 'name'), $params['name']);
        
        //状态
        $params['source_select'] = makeOption(array(-1=>'全部')+ (new CommonEnumModel())->getEnumPairs(1), $params['source']);
         
        $customer = array_column($customer, null, 'customer_id');
        //客户选择列表
        $customer_select = '';
        if (!empty($params['customer_id'])) {
            $customer_select .= '<option value="' . $params['customer_id'] . '">' . $customer[$params['customer_id']]['name'] . '</option>';
        } else {
            $customer_select .= '<option></option>';
        }
        array_walk($customer, function ($item) use (&$customer_select) {
            if ($item['is_delete'] == 0) {
                $customer_select .= '<option value="' . $item['customer_id'] . '">' . $item['name'] . '</option>';
            }
        });
        $params['customer_select'] = $customer_select;
        return $params;
    }

    /**
     * 获取GET的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getParamByGet()
    {
        $params = [];
        //交易日期
        $params['start_time'] = I('get.start_time', '', 'trim');
        $params['end_time'] = I('get.end_time', '', 'trim');
        //最小最大的金额
        $params['max_money'] = I('get.max_money', '', 'trim');
        $params['min_money'] = I('get.min_money', '', 'trim');
        //付款方名称
        $params['name'] = I('get.name', '', 'trim');
        //流水号(收款单)
        $params['receipt_serial'] = I('get.receipt_serial', '', 'trim');
        //状态
        $params['status'] = I('get.status', '-1', 'trim');
        //来源
        $params['source'] = I('get.source', '-1', 'trim');
        $params['customer_id'] = I('get.customer_id', '', 'trim');
        $params['payment_type'] = I('get.payment_type', '', 'trim');
        $params['show_balance'] = I('get.show_balance', '', 'trim');
        return $params;
    }

    /**
     * AJAX验证某个流水号是否存在
     *
     * @access public
     *
     * @param $serial string 流水号
     *
     * @return boolean
     **/
    public function valid_serial($serial)
    {
        $serial = $this->count([
            'receipt_serial' => $serial
        ]);
        return $serial != 0;
    }

    /**
     * 收款单数据增加
     *
     * @access public
     *
     * @param $admin string 操作人
     *
     * @return boolean|string
     **/
    public function add($admin = '')
    {
        //获取POST的数据
        $data = $this->getDataByPost();
        //补充操作人数据
        $data['admin'] = $admin;
        $data['create_time'] = time();
        //验证某流水号是否存在，存在则返回,不存在则增加
        if (!$this->valid_serial($data['receipt_serial'])) {
            $this->model->add($data);
            return true;
        }
        return 'this receipt_serial is exists';
    }

    /**
     * 获取POST传输的数据
     *
     * @access public
     *
     * @return array
     **/
    private function getDataByPost()
    {
        //流水号
        $receipt_serial = I('post.receipt_serial', '', 'trim');
        //付款方名称
        $name = I('post.name', '', 'trim');
        $name = str_replace(['(', ')'], ['（', '）'], $name);
        //付款方账号
        $account = I('post.account', '', 'trim');
        //交易金额（元）
        $money = I('post.money', '', 'trim');
        //付款开户行名
        $bank = I('post.bank', '', 'trim');
        //付款日期
        $remit_date = I('post.remit_date', '', 'strtotime');

        $source  = I('post.add_remit_source', '', 'trim');
        return compact('receipt_serial', 'name', 'account', 'money', 'bank', 'remit_date','source');
    }

    /**
     * excel导入收款单
     *
     * @access public
     *
     * @param $admin string 操作人
     *
     * @return mixed
     **/
    public function file_in($admin = '')
    {
        //将文件上传至服务器
        $filePath = $this->upload();
        //读取Excel内容
        $res = $this->getDataByFile($filePath);
        $data = $res['data'];
        //excel中流水号重复的数据
        $copy_row = $res['copy_row'];
        //对获取到的数据进行验证
        $res = $this->validData($data, $admin);
        $data = $res['data'];
        //数据库中已存在的流水单号
        $exists_row = $res['exists_list'];
        $count = count($data);
        if (!empty($data)) {
            //批量增加数据
            $this->model->addAll(array_values($data));
        }
        //删除文件
        @unlink($filePath);
        return compact('count', 'exists_row', 'copy_row');
    }

    /**
     * 网商银行导入回款数据
     *
     * @param $admin
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-03 18:47:12
     */
    public function file_in_wangshang($admin = '') {

        //将文件上传至服务器
        $filePath = $this->upload();
        //读取Excel内容
        $res = $this->getDataByFile($filePath,'wangshang');
        $data = $res['data'];
        //excel中流水号重复的数据
        $copy_row = $res['copy_row'];
        //对获取到的数据进行验证
        $res = $this->validData($data, $admin);
        $data = $res['data'];
        //数据库中已存在的流水单号
        $exists_row = $res['exists_list'];
        $count = count($data);
        if (!empty($data)) {
            //批量增加数据
            $this->model->addAll(array_values($data));
        }
        //删除文件
        @unlink($filePath);
        return compact('count', 'exists_row', 'copy_row');
    }


    /**
     * 上传的文件至服务器
     *
     * @access public
     *
     * @return string|boolean 文件的路径
     **/
    private function upload()
    {
        $config = [
            'savePath' => '',
            'exts' => ['xls', 'xlsx'],
            'autoSub' => false,
            'rootPath' => CACHE_PATH,
            'maxSize' => 1024 * 3 * 1024
        ];
        $upload = new Upload($config);// 实例化上传类
        $res = $upload->upload();
        if ($res !== false) {
            return CACHE_PATH . $res['file']['savename'];
        } else {
            throw new Exception('upload excel failed, error message is ' . $upload->getError());
        }
    }

    /**
     * 读取Excel内的数据内容
     *
     * @access private
     *
     * @param string $filePath  文件地址
     * @param string $template_type 模板类型,用于区分是否是网商模板
     *
     * @return array
     * @throws Exception
     */
    private function getDataByFile($filePath,$template_type = 'original')
    {
        if (!file_exists($filePath)) {
            throw new Exception('excel not defined');
        }
        //引入PHPExcel
        $this->include_phpExcel();
        //实例化excel读取对象
        $this->ini_read_excel($filePath);
        //读取Excel的数据
        $res = $this->getDataForExcel($template_type);
        $data = $res['data'];
        //Excel中重复的流水单号
        $copy_row = $res['copy_row'];
        return compact('data', 'copy_row');
    }

    /**
     * 引入Excel
     *
     * @access private
     *
     * @return void
     **/
    private function include_phpExcel()
    {
        include LIB_PATH . '/Org/PHPExcel/PHPExcel.php';
        include LIB_PATH . '/Org/PHPExcel/PHPExcel/Writer/Excel2007.php';
        //$this->objPHPExcel = new \PHPExcel();
    }

    /**
     * excel文件读取预先实例化
     *
     * @access private
     *
     * @return void
     **/
    private function ini_read_excel($filePath)
    {
        try {
            $inputFileType = \PHPExcel_IOFactory::identify($filePath);
            $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
            $this->objPHPExcel = $objReader->load($filePath);
        } catch (Exception $e) {
            die('read excel failed, message : ' . $e->getMessage());
        }
    }

    /**
     * 获取Excel文件中的数据
     *
     * @access private
     *
     * @param string $template_type
     *
     * @return array
     * @throws \PHPExcel_Exception
     */
    private function getDataForExcel($template_type = 'original')
    {
        $sheet = $this->objPHPExcel->getSheet(0);
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        $data = [];
        //用于记录流水号的变量，防止excel中存在相同的流水号
        $serial = [];
        //用于记录存在重复流水号的行
        $copy_row = [];
        //遍历获取每条数据

        $limit_row = 10;
        if($template_type == 'wangshang'){
            $limit_row = 5;
        }

        for ($row = $limit_row; $row <= $highestRow; $row++) {
            $temp = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, NULL, TRUE, FALSE);
            //对每行数据进行格式校验，并返回处理后的数据
            if($template_type == 'original'){
                $temp = $this->valid_format_v2($temp[0], $row);//使用最新版本验证
            }else if($template_type == 'wangshang'){
                $temp = $this->valid_wangshang_format_v2($temp[0], $row);//使用最新版本验证
            }

            if (empty($temp)) {
                continue;
            }

            //验证之前的遍历是否存在相同的流水单号
            $temp_serial = $temp['receipt_serial'];
            if (in_array($temp_serial, $serial)) {
                $copy_row[array_search($temp_serial, $serial)][] = $row;
            } else {
                $serial[$row] = $temp_serial;
                $data[$row] = $temp;
            }
        }
        return compact('data', 'copy_row');
    }

    /**
     * 验证Excel中的数据
     *
     * @access private
     *
     * @param $data  array 数据
     * @param $admin string 当前操作人
     *
     * @return boolean|array
     **/
    private function validData($data, $admin)
    {
        //验证唯一性
        $receipt_serial = ['in', array_column($data, 'receipt_serial')];
        $exists_data = $this->data(compact('receipt_serial'), 'r.receipt_serial');
        $exists_list = [];
        $new_data = [];
        $exists_data = array_column($exists_data, 'receipt_serial');
        array_walk($data, function ($value, $row) use (&$exists_list, &$new_data, $exists_data, $admin) {
            $serial = $value['receipt_serial'];
            if (in_array($serial, $exists_data)) {
                $exists_list[] = $row;
                return false;
            }
            $value['admin'] = $admin;
            $value['create_time'] = time();
            $new_data[] = $value;
            return true;
        });
        $data = $new_data;
        //将数据库中已经存在的数据删除
        array_walk($exists_list, function ($value) use (&$data) {
            unset($data[$value]);
        });
        return compact('data', 'exists_list');
    }

    /**
     * 对每行的数据的D/I/L/S/T/V的数据进行校验
     *
     * @access protected
     *
     * @param $data array 数据
     * @param $row  integer 行号
     *
     * @return array 处理过的数据
     **/
    protected function valid_format_v2($data, $row)
    {
        //时间格式的校验
        $a = trim($data[3]);//D
        //金额格式
        $f = $data[8];//I
        //流水单号
        $i = $data[11];//L
        //付款方名称
        $q = $data[19];//T
        //付款方账号
        $r = $data[20];//U
        //付款开户行名
        $t = $data[22];//W
        if (empty($a) && empty($f) && empty($i) && empty($r) && empty($q) && empty($t)) {
            return null;
        }
        if (!preg_match('/^(20[0-5][0-9])-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/', $a)) {
            throw new Exception('第' . $row . '行D列值【' . $a . '】格式不符合规则');
        }
        $remit_date = strtotime($a);
        if ($remit_date > time()) {
            throw new Exception('第' . $row . '行D列值【' . $a . '】超过当前时间，不可录入');
        }
        if (!preg_match('/^(\-?)\d{1,9}(\.\d{1,6})?$/', $f)) {
            throw new Exception('第' . $row . '行I列值【' . $f . '】格式不符合规则');
        }
        $money = $f;
        if (!preg_match('/^[0-9a-zA-Z]{10,20}$/', $i)) {
            throw new Exception('第' . $row . '行L列值【' . $i . '】格式不符合规则');
        }
        $receipt_serial = $i;
        if (mb_strlen($q, 'UTF-8') > 50 || empty($q)) {
            throw new Exception('第' . $row . '行T列值【' . $q . '】格式不符合规则');
        }
        $name = str_replace(['(', ')'], ['（', '）'], $q);
        if (mb_strlen($r, 'UTF-8') > 50 || empty($r)) {
            throw new Exception('第' . $row . '行U列值【' . $r . '】格式不符合规则');
        }
        $account = $r;
        if (mb_strlen($t, 'UTF-8') > 50 || empty($t)) {
            throw new Exception('第' . $row . '行W列值【' . $t . '】格式不符合规则');
        }
        $bank = $t;
        return compact('remit_date', 'money', 'receipt_serial', 'name', 'account', 'bank');
    }


    /**
     * 对每行的数据的D/I/L/S/T/V的数据进行校验
     *
     * @access protected
     *
     * @param $data array 数据
     * @param $row  integer 行号
     *
     * @return array 处理过的数据
     * @throws Exception
     */
    protected function valid_wangshang_format_v2($data, $row) {
        //流水单号
        $col_a = $data[0];//A

        //时间格式的校验
        $col_c = trim($data[2]);//C

        //金额格式
        $col_e = $data[4];//E

        //付款方名称
        $col_h = $data[7];//H

        //付款方账号
        $col_i = $data[8];//I

        //付款开户行名
        $col_j = $data[9];//J

        if (empty($col_c) && empty($col_e) && empty($col_a) && empty($col_i) && empty($col_h) && empty($col_j)) {
            return null;
        }
        if (!preg_match('/^(20[0-5]\d)-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]) ([0-1]\d|2[0-4]):[0-5]\d:[0-5]\d$/', $col_c)) {
            throw new Exception('第' . $row . '行C列值【' . $col_c . '】格式不符合规则');
        }
        $remit_date = strtotime($col_c);
        if ($remit_date > time()) {
            throw new Exception('第' . $row . '行C列值【' . $col_c . '】超过当前时间，不可录入');
        }
        if (!preg_match('/^(\-?)\d{1,9}(\.\d{1,6})?$/', $col_e)) {
            throw new Exception('第' . $row . '行IE列值【' . $col_e . '】格式不符合规则');
        }
        $money = $col_e;
        if (!preg_match('/^[0-9a-zA-Z]{10,50}$/', $col_a)) {
            throw new Exception('第' . $row . '行A列值【' . $col_a . '】格式不符合规则');
        }
        $receipt_serial = $col_a;
        if (mb_strlen($col_h, 'UTF-8') > 50 || empty($col_h)) {
            throw new Exception('第' . $row . '行H列值【' . $col_h . '】格式不符合规则');
        }
        $name = str_replace(['(', ')'], ['（', '）'], $col_h);
        if (mb_strlen($col_i, 'UTF-8') > 50 || empty($col_i)) {
            throw new Exception('第' . $row . '行I列值【' . $col_i . '】格式不符合规则');
        }
        $account = $col_i;
        if (mb_strlen($col_j, 'UTF-8') > 50 || empty($col_j)) {
            throw new Exception('第' . $row . '行J列值【' . $col_j . '】格式不符合规则');
        }
        $bank = $col_j;
        return compact('remit_date', 'money', 'receipt_serial', 'name', 'account', 'bank');
    }


    /**
     * 对每行的数据的A/F/I/Q/R/T的数据进行校验
     *
     * @access protected
     *
     * @param $data array 数据
     * @param $row  integer 行号
     *
     * @return array 处理过的数据
     **/
    protected function valid_format($data, $row)
    {
        //时间格式的校验
        $a = trim($data[0]);
        //金额格式
        $f = $data[5];
        //流水单号
        $i = $data[8];
        //付款方名称
        $q = $data[16];
        //付款方账号
        $r = $data[17];
        //付款开户行名
        $t = $data[19];
        if (empty($a) && empty($f) && empty($i) && empty($r) && empty($q) && empty($t)) {
            return null;
        }
        if (!preg_match('/^(20[0-5][0-9])(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])$/', $a)) {
            throw new Exception('第' . $row . '行A列值【' . $a . '】格式不符合规则');
        }
        $remit_date = strtotime($a);
        if ($remit_date > time()) {
            throw new Exception('第' . $row . '行A列值【' . $a . '】超过当前时间，不可录入');
        }
        if (!preg_match('/^(\-?)\d{1,9}(\.\d{1,6})?$/', $f)) {
            throw new Exception('第' . $row . '行F列值【' . $f . '】格式不符合规则');
        }
        $money = $f;
        if (!preg_match('/^[0-9a-zA-Z]{10,20}$/', $i)) {
            throw new Exception('第' . $row . '行I列值【' . $i . '】格式不符合规则');
        }
        $receipt_serial = $i;
        if (mb_strlen($q, 'UTF-8') > 50 || empty($q)) {
            throw new Exception('第' . $row . '行Q列值【' . $q . '】格式不符合规则');
        }
        $name = str_replace(['(', ')'], ['（', '）'], $q);
        if (mb_strlen($r, 'UTF-8') > 50 || empty($r)) {
            throw new Exception('第' . $row . '行R列值【' . $r . '】格式不符合规则');
        }
        $account = $r;
        if (mb_strlen($t, 'UTF-8') > 50 || empty($t)) {
            throw new Exception('第' . $row . '行T列值【' . $t . '】格式不符合规则');
        }
        $bank = $t;
        return compact('remit_date', 'money', 'receipt_serial', 'name', 'account', 'bank');
    }

    /**
     * 根据流水号删除数据
     *
     * @access public
     *
     * @return array
     **/
    public function del()
    {
        $serial = I('get.serial');

        // if($serial){
        //     // 存在自动拆分的订单
        //     $existAutoChildReceipt = $this->data([
        //         'parent_serial'=>$serial,   
        //         'delete_time'=>0,         
        //     ]);
        //     if($existAutoChildReceipt){
        //         //删除自动认款
        //         (new RemitModel)
        //         ->where([
        //             'parent_serial'=>$serial
        //         ])
        //         ->delete();
        //         //删除自动拆单
        //         (new ReceiptModel())
        //         ->where([
        //             'parent_serial'=>$serial
        //         ])
        //         ->delete();
        //     }
        // }
        
        $this->model->where([
            'receipt_serial' => $serial,
        ])->save([
            'delete_time' => time()
        ]);
    }

    /**
     * 撤销认款
     *
     * @return true
     * <AUTHOR> 2024-02-02 17:27:17
     *
     */
    public function cancel_remit(){
        $receipt_serial = I('post.receipt_serial');

        // delete from remit where receipt_serial = 'C0646SC000ES8IZ';
        // UPDATE receipt SET status = 0 WHERE receipt_serial = 'C0646SC000ES8IZ';

        $remit_model = new RemitModel();
        $push_receipt_repository = new PushReceiptRepository();

        $remit_info = $remit_model->where(['receipt_serial' => $receipt_serial,])->find();
        $remit_model->where(['receipt_serial' => $receipt_serial,])->delete();
        $this->model->where(['receipt_serial' => $receipt_serial,])->save(['status' => 0]);

        $customer_id = $remit_info['customer_id'];
        $push_receipt_repository->cancle($receipt_serial, $customer_id);

        return true;
    }

    /**
     * 查询数据的批量导出
     *
     * @access public
     *
     * @return void
     *
     * @throws \PHPExcel_Exception
     */
    public function file_out()
    {
        //获取get参数
        $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);
        //所有客户数据
        $customer = (new CustomerModel)->field('company, name, customer_id, salesman, is_delete, customer_type, payment_type')
            ->where(DataAuthController::instance()->getRemiteWhere())->select();


        //用户数据
        $user_data = (new SystemUserModel())->alias('u')->field('u.realname,u.username,d.dept_name')->join('crs_system_dept as d on u.dept_id = d.dept_id', 'left')->select();
        $user_data = array_column($user_data, null, 'username');


        //查询数据
        $list_data = $this->dataAuth($where, 'r.*, r2.customer_id , r2.remit_serial', 'r.remit_date desc');
        $list_data = $this->mergeCustomerRemit($customer, $list_data, $user_data);
        //企服用户特殊处理
        $excelOutType = I('get.excelOutType', '', 'trim');
        $file_suff = '';
        if ($excelOutType) {
            $file_suff = '(企服)';
            foreach ($list_data as $k => $v) {
                if ($v['customer_type'] == CustomerModel::$customerType['企服用户']) {
                    $list_data[$k]['money'] = (string) round($v['money']/2, 2);
                }
                // 金融用户 和未认款 金额不变
            }
        }

        $this->ini_write_excel();
        //将数据写入Excel中
        $max_row = $this->write_data($list_data);
        //统一设置Excel样式
        $this->setStyle($max_row);
        //下载Excel
        $this->download($params,$file_suff);
        dump($list_data);
        die;

    }

    /**
     * Excel导出初始化对象
     *
     * @access private
     *
     * @return void
     **/
    private function ini_write_excel()
    {
        //引入phpExcel
        $this->include_phpExcel();
        //初始化phpExcelObject
        $this->objPHPExcel = new \PHPExcel();
        //设置Sheet标题
        $this->objPHPExcel->setActiveSheetIndex(0);
        $this->objPHPExcel->getActiveSheet()->setTitle('收款单');
        //设置标题信息
        $title = [
            'A' => [
                'name' => '流水号',
                'width' => 25
            ], 'B' => [
                'name' => '客户名称',
                'width' => 25
            ], 'C' => [
                'name' => '付费类型',
                'width' => 12
            ], 'D' => [
                'name' => '客户类型',
                'width' => 12
            ], 'E' => [
                'name' => '公司名称',
                'width' => 40
            ], 'F' => [
                'name' => '商务跟进人',
                'width' => 14
            ], 'G' => [
                'name' => '区域',
                'width' => 18
            ], 'H' => [
                'name' => '付款方名称',
                'width' => 35
            ], 'I' => [
                'name' => '付款方账号',
                'width' => 30
            ], 'J' => [
                'name' => '金额（元）',
                'width' => 18
            ], 'K' => [
                'name' => '付方开户行名',
                'width' => 25,
            ], 'L' => [
                'name' => '交易日期',
                'width' => 18,
            ], 'M' => [
                'name' => '状态',
                'width' => 10
            ], 'N' => [
                'name' => '来源',
                'width' => 10
            ]
        ];
        foreach ($title as $col => $value) {
            $this->objPHPExcel->getActiveSheet()->setCellValue($col . '1', $value['name']);
            //设置宽度
            $this->objPHPExcel->getActiveSheet()->getColumnDimension($col)->setWidth($value['width']);
        }
        //设置文字加粗
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:K1')->getFont()->setBold(true);
    }

    /**
     * 将数据写到Excel中
     *
     * @access private
     *
     * @param $data array 数据
     *
     * @return int 行数
     * @throws \PHPExcel_Exception
     */
    private function write_data($data)
    {
        $this->objPHPExcel;
        $row = 2;
        foreach ($data as $item) {
            $customer_type = array_flip(CustomerModel::$customerType)[$item['customer_type']];
            $this->objPHPExcel->getActiveSheet()->setCellValueExplicit('A' . $row, $item['receipt_serial'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $item['customer_name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $item['payment_type_name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('D' . $row, $customer_type, \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('E' . $row, $item['company'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('F' . $row, $item['salesman'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('G' . $row, $item['dept_name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('H' . $row, $item['name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('I' . $row, $item['account'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('J' . $row, $item['money']);
            $this->objPHPExcel->getActiveSheet()->setCellValue('K' . $row, $item['bank'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('L' . $row, date('Y-m-d', $item['remit_date']));
            $this->objPHPExcel->getActiveSheet()->setCellValue('M' . $row, ($item['status'] < 2 ? '未认款' : ($item['status'] == 2 ? '已认款' : '已拆单')));
            $this->objPHPExcel->getActiveSheet()->setCellValue('N' . $row, $item['source_label']);

            $row++;
        }
        return $row--;
    }

    /**
     * 设置Excel样式
     *
     * @access private
     *
     * @param $max_row integer 行数
     *
     * @return void
     **/
    private function setStyle($max_row)
    {
        $cell = 'A1:K' . $max_row;
        //水平居中
        $this->objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //垂直居中
        $this->objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        //设置表格高度
        $this->objPHPExcel->getActiveSheet()->getRowDimension('1:' . $max_row)->setRowHeight(18);
    }

    /**
     * 下载Excel
     *
     * @access private
     *
     * @param $params array 当前get的参数
     *
     * @return void
     **/
    private function download($params,$file_suff)
    {
        $filename = '收款单数据' .$file_suff. $params['start_time'] . '_' . $params['end_time'];
        ob_end_clean();//清除缓冲区,避免乱码
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');
        $objWriter = new \PHPExcel_Writer_Excel5($this->objPHPExcel);
        $objWriter->save('php://output');
    }

    /**
     * 获取某条收款单数据
     *
     * @access public
     *
     * @return array
     **/
    public function getReceiptDataForAddRemite($receipt_serial)
    {
        $data = $this->model
            ->where([
                'status' => ['elt', 1],
                'delete_time' => 0,
                'receipt_serial' => $receipt_serial
            ])->find();
        //所有客户数据
        $customer = (new CustomerModel)->field('company, name, customer_id, salesman')
            ->where([
                'is_delete' => 0
            ])->select();
        $user_data = (new SystemUserModel())->alias('u')->field('u.realname,u.username,d.dept_name')->join('crs_system_dept as d on u.dept_id = d.dept_id', 'left')
            ->where(['u.username' => $customer[0]['salesman']])
            ->find();
        $data['company'] = $customer[0]['company'];
        $data['salesman'] = $user_data['realname'];
        $data['dept_name'] = $user_data['dept_name'];
        $data['customer_option'] = makeOption(array_column($customer, 'name', 'customer_id'));
        $data['source_options']  =makeOption((new CommonEnumModel)->getEnumPairs(1),$data['source']);
        return $data;
    }

    /**
     * 提交打款单
     *
     * @access public
     *
     * @param $admin array
     *
     * @return boolean
     **/
    public function add_remit($admin)
    {
        $repository = new RemitRepository($admin);
        $repository->run_add();
        $this->model->where([
            'status' => 0,
            'delete_time' => 0,
            'receipt_serial' => I('post.receipt_serial')
        ])->save([
            'status' => 1
        ]);
        return true;
    }

    /**
     * 认款
     * @param $admin
     * @return bool
     * @throws Exception
     */
    public function run_admit($admin)
    {
        $repository = new RemitRepository($admin);
        $prl_repository = new PushReceiptRepository();

        $receipt_serial = I('post.receipt_serial','','trim');
        $customer_id = I('post.customer_id', '', 'trim');

        $id = $repository->run_add();
        $repository->run_admit($id);
        $this->model->where([
            'status' => ['elt', 1],
            'delete_time' => 0,
            'receipt_serial' => I('post.receipt_serial')
        ])->save([
            'status' => 2
        ]);

        $res = $prl_repository->remit($receipt_serial, $customer_id);
        if($res !== true){
            $feishu_obj = new Feishu();
            $feishu_obj->send_card_message_to_chat_group("财务同步认款失败! 流水号:".$receipt_serial,$res);
        }

        return true;
    }

    public function getOptionCustomer($customer_id)
    {
        //所有客户数据
        $customer = (new CustomerModel)->field('company, name, customer_id, salesman, is_delete')
            ->where(['customer_id' => $customer_id])->find();

        $user_data = (new SystemUserModel())->alias('u')->field('u.realname,u.username,d.dept_name')->join('crs_system_dept as d on u.dept_id = d.dept_id', 'left')
            ->where(['u.username' => $customer['salesman']])
            ->find();

        return array_merge($customer, $user_data);
    }

    public function getSplitPrice()
    {
        $receipt_serial = I('get.receipt_serial', '', 'trim');
        $payment_type = I('get.payment_type', '', 'trim');
        $customer_id = I('get.customer_id', '', 'trim');
        $source = I('get.source', '-1', 'trim');
        $data = [];

        $product_info = (new ProductConfigModel())->where('search_show=1 and father_id = 0 or father_id = 401')->order('sort desc')->getField('product_id,product_name');
        // 已拆分的数据列表
        $list = (new RemitSplitPriceModel())->field('id, product_id, product_name, month, money')->where(
            ['delete_time' => 0, 'receipt_serial' => $receipt_serial]
        )->select();


        if($list){
            array_walk($list, function (&$value) {
                $value['month'] = date('Y-m', strtotime($value['month'] . '01'));
                $value['checked'] = $value['product_id'] . '_' . $value['month'] . '_' . round($value['money'], 2);
            });
        }


        // 2 后付费模式 自动匹配
        // if ($payment_type == 2) {
            $is_checked = $list ? array_column($list, 'checked') : [];
            $apikey = (new AccountModel())->field('apikey')->where(['customer_id' => $customer_id])->select();
            $apikey = array_column($apikey, 'apikey');
            $apikey = implode('","', $apikey);
            $money_data = [];


            // 已认款数据
            $start = '202201';
            $end = date('Ym');
            $sql = 'SELECT rsp.product_id,rsp.`month`,SUM(rsp.money) as money FROM remit r join remit_split_price rsp on r.receipt_serial = rsp.receipt_serial where r.customer_id = "' . $customer_id . '" and rsp.month>=' . $start . ' and rsp.month <= ' . $end . ' and delete_time=0 and r.source='.$source;
            // 如果是重新拆分需要把拆分的id去掉
            if ($list) {
                $ids = implode(',', array_column($list, 'id'));
                $sql .= ' and rsp.id not in (' . $ids . ')';
            }
            $sql .= ' GROUP BY rsp.product_id,rsp.`month` ';
            $remit = (new RemitSplitPriceModel())->query($sql);
            $remit_data = [];
            foreach ($remit as $value) {
                $remit_data[$value['month']][$value['product_id']] = $value['money'];
            }

            // 消耗日期
            $start = '20220101';
            //$start = $date_start ? $date_start[0]['month'].'01' : '20220101';
            $end = date('Ymd');
            // 收入每个产品每个月份的数据
            
            //来源条件
            $whereSource =$joinWhereSource ='';
            if($source!='-1'){
                $whereSource = " AND source=".$source."";
                $joinWhereSource = " AND ce.source=".$source."";
            }
            //征信机构的拆单 使用money_agent字段计算 || 20221101之前是money加特殊消耗调整，20221101开始 money 就是扣点后金额 所以都用money
            $sql = 'select date_format(date, "%Y%m") as month,father_id,sum(money) as money from '.(new BillProductIncomeV2Model())->getTableName().' where apikey in("' . $apikey . '") and date>=' . $start . ' and date < ' . $end .$whereSource. ' group by father_id, month';
         
            $income = (new BillProductIncomeV2Model())->query($sql);

            foreach ($income as $value) {
                $month = $value['month'];
                $money = $value['money'];
                $father_id = $value['father_id'];
                $money_data[$month]['money'] = isset($money_data[$month]['money']) ? round(($money_data[$month]['money'] + $money), 2) : round($money, 2);
                $money_data[$month]['month'] = date('Y-m', strtotime($month . '01'));
                $money_data[$month]['product'][$father_id]['money'] = isset($money_data[$month]['product'][$father_id]['money']) ? round(($money_data[$month]['product'][$father_id]['money'] + $money), 2) : round($money, 2);
                $money_data[$month]['product'][$father_id]['father_id'] = $father_id;
            }

            // 特殊消耗每个产品每个月份的数据
            $sql = 'SELECT profile_show_date, (IF (p.father_id, p.father_id, ce.product_id)) as father_id, money FROM customer_expend ce left join product p on ce.product_id = p.product_id 
                WHERE ce.customer_id = "' . $customer_id . '" AND ce.profile_show_date >= "' . $start . '" AND ce.profile_show_date < "' . $end . '" '.$joinWhereSource;

            $sql_sub = 'select date_format(profile_show_date , "%Y%m") as month, father_id, SUM(money) as money from (' . $sql . ' AND ce.type=1) as a group by father_id,month';
            $expend_sub = (new BillProductIncomeV2Model())->query($sql_sub);
            

            foreach ($expend_sub as $value) {
                $month = $value['month'];
                $money = $value['money'];
                $father_id = $value['father_id'];
                $money_data[$month]['money'] = isset($money_data[$month]['money']) ? round(($money_data[$month]['money'] - $money), 2) : (0 - round($money, 2));
                $money_data[$month]['month'] = date('Y-m', strtotime($month . '01'));
                $money_data[$month]['product'][$father_id]['money'] = isset($money_data[$month]['product'][$father_id]['money']) ? round(($money_data[$month]['product'][$father_id]['money'] - $money), 2) : (0 - round($money, 2));
                $money_data[$month]['product'][$father_id]['father_id'] = $father_id;
            }

            $sql_add = 'select date_format(profile_show_date , "%Y%m") as month, father_id, SUM(money) as money from (' . $sql . ' AND ce.type=2) as a group by father_id,month';
            $expend_add = (new BillProductIncomeV2Model())->query($sql_add);
            foreach ($expend_add as $value) {
                $month = $value['month'];
                $money = $value['money'];
                $father_id = $value['father_id'];
                $money_data[$month]['money'] = isset($money_data[$month]['money']) ? round(($money_data[$month]['money'] + $money), 2) : round($money, 2);
                $money_data[$month]['month'] = date('Y-m', strtotime($month . '01'));
                $money_data[$month]['product'][$father_id]['money'] = isset($money_data[$month]['product'][$father_id]['money']) ? round(($money_data[$month]['product'][$father_id]['money'] + $money), 2) : round($money, 2);
                $money_data[$month]['product'][$father_id]['father_id'] = $father_id;
            }

            // 总数据处理
            foreach ($money_data as $key => &$value) {
                $value['unremit_money'] = $value['money'];
                $check = false;
                foreach ($value['product'] as $kk => &$vv) {
                    $vv['unremit_money'] = $vv['money'];
                    if ($remit_data) { // 已认款的数据
                        $month = str_replace('-', '', $value['month']);
                        $remit_money = isset($remit_data[$month][$vv['father_id']]) ? $remit_data[$month][$vv['father_id']] : 0;
                        // 产品未认款的
                        $vv['unremit_money'] = round(($vv['money'] - $remit_money), 2);
                        // 月份未认款
                        $value['unremit_money'] = round(($value['unremit_money'] - $remit_money), 2);
                    }
                    if ($vv['unremit_money'] == 0) {
                        unset($value['product'][$kk]);
                    }
                    $checked = $vv['father_id'] . '_' . $value['month'] . '_' . round($vv['unremit_money'], 2);
                    $vv['checked'] = $is_checked ? in_array($checked, $is_checked) : false;
                    $is_checked && in_array($checked, $is_checked) && $check = true;
                    if ($value['unremit_money'] == 0) {
                        unset($money_data[$key]);
                    }
                }

                $value['checked'] = $check;
            }

            $data['money_data'] = $money_data;

        // }
        $data['receipt_serial'] = $receipt_serial;
        $data['money'] = $list ? array_sum(array_column($list, 'money')) : 0;
        $data['list_data'] = $list;
        $data['payment_type'] = $payment_type;
        $data['product_option'] = makeOption($product_info);
        $data['product_data'] = $product_info;
        return $data;
    }

    /**
     * @param $admin
     *
     * @return bool
     * @throws Exception
     */
    public function add_split_price($admin = '')
    {
        $list = I('post.data', []);

        if (!$list) {
            return false;
        }
        $one = $list[0];

        $rsp_obj = new RemitSplitPriceModel();
        $where = [
            'receipt_serial' => $one['receipt_serial'],
            'delete_time' => 0
        ];
        $_remit_split_list = $rsp_obj->where($where)->select();
        $remit_split_list = [];
        $remit_split_ids = [];
        $used_ids = [];
        foreach ($_remit_split_list as $rs_info){
            $_tmpk = $rs_info['product_id'].'-'.$rs_info['month'];
            $remit_split_list[$_tmpk] = $rs_info;
            $remit_split_ids[] = $rs_info['id'];
        }

        $Model = new ReceiptModel();
        $Model->startTrans();
	    try {
            $totoal_split   = 0;
            $max_month      = '';
            $receipt_serial = '';
            foreach ($list as $value) {
                $value['month']       = str_replace('-', '', $value['month']);
                $value['admin']       = $admin;
                $value['update_time'] = time();
                $where                = [
                    'receipt_serial' => $value['receipt_serial'],
                    'product_id'     => $value['product_id'],
                    'month'          => $value['month'],
                    'delete_time'    => 0,
                ];

                $max_month      = max($max_month, $value['month']);
                $receipt_serial = $value['receipt_serial'];
                $totoal_split   = bcadd($totoal_split, $value['money'], 6);
                $_k             = $value['product_id'] . '-' . $value['month'];
                if (key_exists($_k, $remit_split_list)) {
                    $used_ids[] = $remit_split_list[$_k]['id'];
                    $rsp_obj->where($where)->save($value);
                } else {
                    $value['create_time'] = time();
                    $rsp_obj->add($value);
                }
            }

            //删除其余拆分
            $need_del_ids = array_diff($remit_split_ids, $used_ids);
            if (!empty($need_del_ids)) {
                $rsp_obj->where(['id' => ['in', $need_del_ids],])->save(['delete_time' => time()]);
            }


            $remite_info = (new RemitModel())->where(['receipt_serial' => $receipt_serial])->find();

            //更新收款单余额信息
            $where             = ['receipt_serial' => $receipt_serial,];
            $now               = date("Y-m-d H:i:s");
            $remit_balance_obj = new RemitBalanceModel();
            $res               = $remit_balance_obj->field('id,money')->where($where)->find();
            $balance_money     = 0;
            if ($res) {
                $balance_money = bcsub($res['money'], $totoal_split, 6);
                $value         = [
                    'remit_balance' => $balance_money,
                    'month_date'    => $max_month,
                    'updated_at'    => $now,
                ];
                $remit_balance_obj->where($where)->save($value);
            } else {
                $balance_money = bcsub($remite_info['money'], $totoal_split, 6);
                $value         = [
                    'customer_id'    => $remite_info['customer_id'],
                    'receipt_serial' => $receipt_serial,
                    'month_date'     => $max_month,
                    'money'          => $remite_info['money'],
                    'remit_balance'  => $balance_money,
                    'created_at'     => $now,
                    'updated_at'     => $now,
                ];
                $remit_balance_obj->add($value);
            }


            //更新收款单状态
            $this->model->where([
                'status'         => 2,
                'delete_time'    => 0,
                'receipt_serial' => $receipt_serial,
            ])->save([
                'status' => 3, // 已拆单
            ]);

            //更新消耗信息
            //更新消耗回款关系
            //更新消耗发票关系
            //更新收款单余额 remit
            //更新发票状态

            $rel_invoice_remit_obj = new RelInvoiceRemitModel();
            $customer_invoice_obj  = new CustomerInvoiceModel();
            $rir_res               = $rel_invoice_remit_obj->where(['receipt_serial' => $receipt_serial])->find();

            //检查是否是企服产品拆单
            if (empty($rir_res)) {
                $all_split_is_3100 = true;
                foreach($list as $split_info){
                    if($split_info['product_id'] != 3100){//企服产品
                        $all_split_is_3100 = false;
                        break;
                    }
                }
                // if(!$all_split_is_3100){
			    //     throw new \Exception('非企服产品回款单,无关联发票,请联系技术处理!');
                // }
            }

            //如果不存在关联的发票,不处理相关关系
            if (!empty($rir_res)) {
                $invoice_id              = $rir_res['invoice_id'];
                $invoice_info            = $customer_invoice_obj->where(['invoice_id' => $invoice_id])->find();
                $customer_consume_obj    = new CustomerConsumeModel();
                $customer_invoice_obj    = new CustomerInvoiceModel();
                $rel_remit_consume_obj   = new RelRemitConsumeModel();
                $rel_invoice_consume_obj = new RelInvoiceConsumeModel();


                $now         = date("Y-m-d H:i:s");
                $source      = $remite_info['source'];
                $customer_id = $remite_info['customer_id'];
                foreach ($list as $value) {
                    $month = str_replace('-', '', $value['month']);
                    $where = [
                        'customer_id' => $customer_id,
                        'source'      => $source,
                        'month'       => $month,
                        'product_id'  => $value['product_id'],
                    ];

                    //获取发票-回款关系表 获取发票id

                    //消耗信息 更新消耗关系表
                    $consume_info = $customer_consume_obj->where($where)->find();
                    $consume_id   = $consume_info['id'];

                    $where['id']   = $consume_id;
                    $consume_money = mockMysqlDecrypt($consume_info['consume_money']);
                    $balance_money = bcsub($consume_money, $value['money'], 6);
                    $customer_consume_obj->where($where)->save([
                        'consume_balance' => mockMysqlEncrypt($balance_money),//未开票消耗金额 加密后
                        'consume_unpaid'  => mockMysqlEncrypt($balance_money),//未到款消耗金额 加密后
                        'invoice_status'  => $balance_money > 0 ? 25 : 30,//状态 未开票金额为0 -> 30 开票,大于0 25部分开票
                        'updated_at'      => $now,
                    ]);

                    //消耗 - 回款 关系表
                    $rel_remit_consume_info = $rel_remit_consume_obj->where(['consume_id' => $consume_id])->find();
                    if (!empty($rel_remit_consume_info)) {//更新
                        $rel_remit_consume_obj->where(['consume_id' => $consume_id])->save([
                            'rel_money'  => mockMysqlEncrypt($value['money']),
                            'updated_at' => $now,
                        ]);
                    } else {//插入
                        $rel_remit_consume_obj->add([
                            'customer_id'    => $customer_id,
                            'source'         => $source,
                            'remit_serial'   => $receipt_serial,
                            'receipt_serial' => $receipt_serial,
                            'remit_money'    => mockMysqlEncrypt($remite_info['money']),
                            'consume_id'     => $consume_id,
                            'consume_month'  => $month,
                            'product_id'     => $value['product_id'],
                            'father_id'      => 0,
                            'consume_money'  => $consume_info['consume_money'],
                            'rel_money'      => mockMysqlEncrypt($value['money']),
                            'admin'          => $admin,
                            'created_at'     => $now,
                            'updated_at'     => $now,
                        ]);
                    }

                    //发票 - 回款 关系表
                    $rel_invoice_consume_info = $rel_invoice_consume_obj->where(['consume_id' => $consume_id])->find();
                    if (!empty($rel_invoice_consume_info)) {//更新
                        $rel_invoice_consume_obj->where(['consume_id' => $consume_id])->save([
                            'rel_money'  => mockMysqlEncrypt($value['money']),
                            'updated_at' => $now,
                        ]);
                    } else {//插入
                        $rel_invoice_consume_obj->add([
                            'customer_id'   => $customer_id,
                            'source'        => $source,
                            'invoice_id'    => $invoice_id,
                            'invoice_money' => mockMysqlEncrypt($invoice_info['money']),
                            'consume_id'    => $consume_id,
                            'consume_month' => $month,
                            'product_id'    => $value['product_id'],
                            'father_id'     => 0,
                            'consume_money' => $consume_info['consume_money'],
                            'rel_money'     => mockMysqlEncrypt($value['money']),
                            'invoice_model' => $invoice_info['invoice_model'],
                            'admin'         => $admin,
                            'created_at'    => $now,
                            'updated_at'    => $now,
                        ]);
                    }
                }

                $invoice_balance = bcsub($invoice_info['money'], $totoal_split, 6);
                //发票
                $customer_invoice_obj->where(['invoice_id' => $invoice_id])->save([
                    'invoice_balance' => mockMysqlEncrypt($invoice_balance),
                    'updated_at'      => $now,
                ]);
                // $invoice_info = $customer_invoice_obj->where(['invoice_id' => $invoice_id])->find();
            }
			$Model->commit();
		} catch (\Exception $exception) {
			$Model->rollback();
			throw new \Exception('store father product failed，exception is ' . $exception->getMessage());
		}
        return true;
    }

    public function del_split_price($admin = '')
    {
        $id = I('post.id', 0);
        $data['delete_time'] = time();
        $data['admin'] = $admin;
        return (new RemitSplitPriceModel())->where(['id' => $id])->save($data);
    }

    // 征信机构打款 统计拆分的子认款单
    public function checkChildReceipt(){
        $receipt_serial = I('get.receipt_serial', '', 'trim');
        $check_child = I('get.check_child', '', 'trim');
        if($receipt_serial&&$check_child ){
            $exist=  (new ReceiptModel())
            ->where(
                ['delete_time' => 0, 'parent_serial' => $receipt_serial]
            )
            ->count();
            if($exist){
                echo 'hasChild';exit;
            }
        }
    }


    /**
     * 重新推送认款
     *
     * @throws Exception
     */
    public function re_push() {
        $receipt_serial = I('get.receipt_serial','','trim');

        $remit_model = new RemitModel();
        $push_receipt_repository = new PushReceiptRepository();

        $remit_info = $remit_model->where(['receipt_serial' => $receipt_serial,])->find();

        $customer_id = $remit_info['customer_id'];

        $res = $push_receipt_repository->remit($receipt_serial, $customer_id);
        if($res !== true){
            echo "<pre>";
            var_dump($res);
            echo "</pre>";
            $feishu_obj = new Feishu();
            $feishu_obj->send_card_message_to_chat_group("财务同步认款失败! 流水号:".$receipt_serial,$res);
        // }else{
        //     var_dump("重新推送成功!");
        }
    }

    /**
     * 重新推送 撤销认款
     *
     * @throws Exception
     */
    public function re_push_cancle() {
        $receipt_serial = I('get.receipt_serial');

        $remit_model = new RemitModel();
        $push_receipt_repository = new PushReceiptRepository();
        $push_receipt_model = new PushReceiptModel();

        $push_receipt_info = $push_receipt_model->getInfoByTradeId($receipt_serial);

        $remit_info = $remit_model->where(['receipt_serial' => $receipt_serial,])->find();

        $customer_id = $remit_info['customer_id'];
        $push_receipt_repository->cancle($receipt_serial, $customer_id);
    }
}
