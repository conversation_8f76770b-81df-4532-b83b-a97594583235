<?php

namespace Home\Repositories;

use Common\Model\CuishouUserModel;
use Think\Cache;

class CuishouRepository
{
    /*
     *  字段转integer
     * */
    protected $list_mapping_config = [
        'require_tel' => 1,
        'require_idnum' => 2,
        'require_name' => 3
    ];

    /*
     * integer转字段
     * */
    protected $list_mapping_field = [
        1 => 'require_tel',
        2 => 'require_idnum',
        3 => 'require_name'
    ];


    /**
     * 过滤参数信息
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    public function filterInfo($data)
    {
        // 检测基本信息
        $data = $this->filterBaseInfoForFilter($data);

        // 检测金融标签
        $data = $this->filterItag($data);

        // filter催收异步推送 && 过期时间
        $data = $this->filterSyncUrl($data);

        // 检查IP白名单
        $data = $this->filterLimitIp($data);

        // 为插入整理数据类型
        return $this->tidyFormatForInsert($data);
    }

    /**
     * 检测白名单
     * @param  array $data
     * @return array
     */
    protected function filterLimitIp($data)
    {
        // check limit_access_ip
        if (isset($data['access_ip']) && $data['access_ip']) {

            $ip_list = explode(PHP_EOL, $data['access_ip']);

            // check ip one by one
            $ip_list = array_map(function ($ip_str) {
                $ip_str = trim($ip_str, " \t\n\r\0\x0B ");

                // normal ip
                $ip_validate_result = filter_var($ip_str, FILTER_VALIDATE_IP);
                if ($ip_validate_result) {
                    return $ip_str;
                }

                // match ip eg: 172.18.19.x
                $regex_ip = '/\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){3}x$/';
                $ip_reg_result = preg_match($regex_ip, $ip_str, $matches);
                if ($ip_reg_result) {
                    return $ip_str;
                }

                throw new \Exception('IP白名单输入不合法');
            }, $ip_list);

            $data['access_ip'] = $ip_list;
        } else {
            $data['access_ip'] = '';
        }
        return $data;
    }

    /**
     * 检测异步推送的Url && 过期时间
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    protected function filterSyncUrl($data)
    {
        $data['notify_url'] = trim($data['notify_url']);
        if ($data['need_async_notify'] == 1 && $data['notify_url']) {
            if (!(filter_var($data['notify_url'], FILTER_VALIDATE_URL))) {
                throw new \Exception('请输入有效的异步推送地址');
            }
        } else {
            $data['notify_url'] = '';
        }

        $data['need_async_notify'] = ($data['need_async_notify'] == 1);

        if (!$data['validuntil'] || ($data['validuntil'] <= time() && !isset($data['id']))) {
            throw new \Exception('请选择有效的账号截止时间');
        }
        return $data;
    }

    /**
     * 检测金融标签
     * @param $data
     * @return mixed
     * @throws \Exception
     */
    protected function filterItag($data)
    {
        if ($data['need_itag'] == 1) {
            $data['itag_notify_url'] = trim($data['itag_notify_url']);

            if ($data['itag_notify_url'] && !filter_var($data['itag_notify_url'], FILTER_VALIDATE_URL)) {
                throw new \Exception('请输入有效的金融推送地址');
            }
            if (!$data['itag_apikey']) {
                throw new \Exception('请输入金融APIKEY');
            }
            if (!$data['itag_password']) {
                throw new \Exception('请输入金融PASSWORD');
            }
        } else {
            $data['itag_notify_url'] = '';
            $data['itag_apikey'] = '';
            $data['itag_password'] = '';
        }

        return $data;
    }

    /**
     * 检测基本信息
     * @param $data
     * @throws \Exception
     * @return array
     */
    protected function filterBaseInfoForFilter($data)
    {
        $data['validuntil'] = $data['validuntil'] ? (strtotime($data['validuntil'])) : 0;
        $data['developer'] = trim($data['developer']);

        if (isset($data['id'])) {
            $data['id'] = intval($data['id']);
        }

        // filter limit
        if (($data['daily_limit'] = trim($data['daily_limit'])) == '') {
            $data['daily_limit'] = -1;
        } else {
            if (!is_numeric($data['daily_limit'])) {
                throw new \Exception('日限额填充有误');
            }
        }

        if (($data['monthly_limit'] = trim($data['monthly_limit'])) == '') {
            $data['monthly_limit'] = -1;
        } else {
            if (!is_numeric($data['monthly_limit'])) {
                throw new \Exception('月限额填充有误');
            }
        }

        if (($data['yearly_limit'] = trim($data['yearly_limit'])) == '') {
            $data['yearly_limit'] = -1;
        } else {
            if (!is_numeric($data['yearly_limit'])) {
                throw new \Exception('年限额填充有误');
            }
        }

        if (($data['monthly_limit'] != -1) && ($data['monthly_limit'] < $data['daily_limit'])) {
            throw new \Exception('月限额不可以小于日限额');
        }

        if (($data['yearly_limit'] != -1) && (($data['yearly_limit'] < $data['monthly_limit']) || ($data['yearly_limit'] < $data['daily_limit']))) {
            throw new \Exception('年限额不可以小于月和日限额');
        }

        // filter developer
        if (!$data['developer']) {
            throw new \Exception('请输入用户名');
        }

        return $data;
    }

    /**
     * 设置默认的API配置
     * @param array $data
     * @return array
     */
    public function setDefaultConfigForInsert($data)
    {
        array_walk($this->list_mapping_field, function ($field) use (&$data) {
            $data[$field] = true;
        });
        return $data;
    }


    /**
     *  为插入整理数据类型
     * @param array $data
     * @return array
     */
    protected function tidyFormatForInsert($data)
    {
        $data['daily_limit'] = new \MongoInt32($data['daily_limit']);
        $data['monthly_limit'] = new \MongoInt32($data['monthly_limit']);
        $data['yearly_limit'] = new \MongoInt32($data['yearly_limit']);
        $data['status'] = new \MongoInt32($data['status']);
        $data['need_itag'] = new \MongoInt32($data['need_itag']);
        $data['validuntil'] = new \MongoInt32($data['validuntil']);
        $data['contract_status'] = new \MongoInt32($data['contract_status']);
        $data['flag'] = new \MongoInt32($data['flag']);
        $data['is_level'] = new \MongoInt32($data['is_level']);

        return $data;
    }

    /**
     * 更新APi配置
     * @throws \Exception
     */
    public function updateApiConfig()
    {
        // 条件
        $where = $this->genConditionsForApiConfig();

        // 待更新参数
        $params = $this->genParamsForApiConfig();

        // 更新配置
        $this->updateConfig($where, $params);
    }

    /**
     * 更新配置
     * @param array $where
     * @param array $params
     */
    protected function updateConfig($where, $params)
    {
        (new CuishouUserModel())->updateInfo(['$set' => $params], $where);
    }

    /**
     * 为API配置生成参数
     * @return array
     */
    protected function genParamsForApiConfig()
    {
        // 必选的参数
        return $this->genParamsForRequiredFiledByUpdate();
    }

    /**
     * 为API更新生成必选参数
     */
    protected function genParamsForRequiredFiledByUpdate()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 必填的字段
        $required_field = $request_body['required_field'];

        // 必选参数转化
        return $this->numberToRequiredField($required_field);
    }

    /**
     * 必选参数有number转换成字段
     * @param array $required_field 选定字段
     * @return array
     */
    protected function numberToRequiredField($required_field)
    {
        $required_format = [];

        array_walk($this->list_mapping_field, function ($field, $index) use (&$required_format, $required_field) {
            // 如果选定 则true
            $required = in_array($index, $required_field);
            $required_format[$field] = $required;
        });
        return $required_format;
    }

    /**
     * 生成API配置的条件
     * @return array
     * @throws \Exception
     */
    protected function genConditionsForApiConfig()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        if (!isset($request_body['id']) || !$request_body['id']) {
            throw new \Exception('缺少必须的参数ID');
        }
        $id = new \MongoInt32($request_body['id']);
        return compact('id');
    }

    /**
     * 获取选定产品的API配置
     * @return string
     * @throws \Exception
     */
    public function getApiConfig()
    {
        // 要配置的参数
        $product = $this->getProductForApiConfig();

        // 整合成需要的样式
        return $this->tidyDataForApiConfig($product);
    }

    /**
     * 为API配置整合数据
     * @param array $product 要更新的产品
     * @return string
     */
    protected function tidyDataForApiConfig($product)
    {
        // 整理必选字段
        $required_field = $this->tidyRequiredFiledForConfig($product);

        $api_config_info = compact('required_field');

        return json_encode($api_config_info);
    }

    /**
     * 整理必选字段(转换)
     * @param $product
     * @return array
     */
    protected function tidyRequiredFiledForConfig($product)
    {
        $require_idnum = isset($product['require_idnum']) ? $product['require_idnum'] : '';
        $require_name = isset($product['require_name']) ? $product['require_name'] : '';
        $require_tel = isset($product['require_tel']) ? $product['require_tel'] : '';

        $list_required = compact('require_idnum', 'require_name', 'require_tel');

        $list_required = array_filter($list_required, function ($item) {
            return $item === true;
        });

        $required_field = [];

        array_walk($list_required, function ($item_value, $field) use (&$required_field) {
            if ($item_value) {
                $mapping_value = $this->list_mapping_config[$field];
                array_push($required_field, $mapping_value);
            }
        });

        return $required_field;
    }


    /**
     * 为API配置获取产品信息
     * @return array
     * @throws \Exception
     */
    private function getProductForApiConfig()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            throw new \Exception('缺少必选参数ID');
        }

        $id = new \MongoInt32($id);

        return (new CuishouUserModel())->where(compact('id'))
            ->field(['_id' => 0])
            ->find();
    }


    /**
     * 列表时间上的制约
     * @return mixed
     */
    public function timeLimitForList()
    {
        $status = I('get.status', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $user_id = I('get.id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        // check status, user
        $where = $status ? ['status' => new \MongoInt32($status)] : [];
        $where = $apikey ? (compact('apikey') + $where) : $where;

        //  输入的id冲突，拿不到数据
        if ($user_id && $choose_id && $choose_id != $user_id) {
            $where['id'] = 'what happened';
        } elseif ($user_id) {
            $where['id'] = new \MongoInt32($user_id);
        } elseif ($choose_id) {
            $where['id'] = new \MongoInt32($choose_id);
        }
        //检索签约状态、开通时间、到期时间
        if ($contract_status) {
            $where['contract_status'] = new \MongoInt32($contract_status);
        }
        if ($begin && $begin_e){
            $where['created_at'] = ['between', [new \MongoInt32(strtotime($begin)), new \MongoInt32(strtotime($begin_e.' 23:59:59'))]];
        }elseif($begin){
            $where['created_at'] = ['egt', new \MongoInt32(strtotime($begin))];
        }elseif($begin_e){
            $where['created_at'] = ['elt', new \MongoInt32(strtotime($begin_e.' 23:59:59'))];
        }
        if ($end && $end_e){
            $where['validuntil'] = ['between', [new \MongoInt32(strtotime($end)), new \MongoInt32(strtotime($end_e.' 23:59:59'))]];
        }elseif($end){
            $where['validuntil'] = ['egt', new \MongoInt32(strtotime($end))];
        }elseif ($end_e){
            $where['validuntil'] = ['elt', new \MongoInt32(strtotime($end_e.' 23:59:59'))];
        }
        return $where;
    }


    /**
     * 催收分导出csv准备数据
     * @param array $where
     * @return array
     */
    public function getCuishouForList($where)
    {
        $CuishouUser = new CuishouUserModel();
        $contract_status = $CuishouUser->getContractStatus();
        $lists = $CuishouUser->field(['_id' => 0])->where($where)->order('_id desc')->select();

        $account_product_infos = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($lists, 'id'), 3);
        foreach ($lists as $k=>$item){
            if ($account_product_infos[$item['id']]){
                $lists[$k]['account_product'] = $account_product_infos[$item['id']];
            }
        }

        $res = [];
        foreach ($lists as $k => $item) {
            $temp = [];
            $temp['id'] = $item['id'];
            $temp['developer'] = $item['developer'];
            $temp['status'] = ($item['status'] == 1) ? '可用' : '禁用';
            $temp['contract_status'] = !empty($contract_status[$item['contract_status']]) ? $contract_status[$item['contract_status']] : ' ';
            $temp['need_itag'] = ($item['need_itag'] == 1) ? '是' : '否';
            $temp['apikey'] = $item['apikey'];
            $temp['itag_notify_url'] = ($item['need_itag'] == 1 && $item['itag_notify_url']) ? $item['itag_notify_url'] : ' ';
            $temp['itag_apikey'] = ($item['need_itag'] == 1 && $item['itag_apikey']) ? $item['itag_apikey'] : ' ';
            $temp['notify_url'] = isset($item['notify_url']) ? $item['notify_url'] : ' ';
            $timeStr = 'start :' . ($item['created_at']) ? date('Y-m-d H:i:s', $item['created_at']) : ' ';
            $timeStr .= ' end :' . ($item['validuntil']) ? date('Y-m-d H:i:s', $item['validuntil']) : ' ';
            $temp['timeStr'] = $timeStr;
            $temp['account_name'] = $item['account_product']['account_name'];
            $temp['account_name'] = $temp['account_name'] ? $temp['account_name'] : '';
            $res[] = $temp;
        }
        return $res;
    }
}
