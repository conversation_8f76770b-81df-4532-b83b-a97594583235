<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-19 10:16:52
 */

namespace Home\Repositories;

class RiskListFeeStatRepository extends BaseRepository
{
    //计费方式
    protected $feeMethod = ['--', '按时间', '按用量'];
    /**
     * 根据条件获取客户的风险名单费用
     * @return 费用
     */
    public function getRiskListFeeStatAccount()
    {
        $where = $this->getRiskListFeeStatParam();
        $stat_list = D('RiskListFeeStat')->where($where)->select();
        $list = [];
        if ($stat_list && is_array($stat_list)) {
            foreach ($stat_list as $key => $value) {
                $list[$value['account_id']] += $value['fee_price'];
            }
        }
        return $list;
    }

    /**
     * 获取某产品信息
     * @return list
     */
    public function getRiskListFeeStatProduct()
    {
        $where = $this->getRiskListFeeStatTimeParam();
        $product_id = I('get.product_id', 0, 'intval');
        if (!$product_id) {
            return false;
        }
        $where['product_id'] = $product_id;
        $list = D('RiskListFeeStat')->where($where)->select();
        $info = [];
        if ($list) {
            $info['fee_basis'] = $list[0]['fee_basis'] == 1 ? '查得量' : '--';
            $info['fee_method'] = $this->feeMethod($list[0]['fee_method']);
            $info['fee_amount'] = array_sum(array_map(function($val) {return $val['fee_amount'];}, $list));
            $info['fee_price'] = array_sum(array_map(function($val) {return $val['fee_price'];}, $list));
        }
        return $info;
    }

    /**
     * 风险名单日对账列表
     * @return list
     */
    public function getRiskListFeeStatDay()
    {
        $date_list = $this->getRiskListFeeStatDateList();
        $where = $this->getRiskListFeeStatTimeParam();
        $where['product_id'] = I('get.product_id', 0, 'intval');
        $stat_list = D('RiskListFeeStat')->where($where)->index('fee_date')->select();
        $list = array_merge($date_list, $stat_list);
        $total_data = [];
        if ($stat_list) {
            $total_data['fee_own_num'] = array_sum(array_map(function($val) {return $val['fee_own_num'];}, $stat_list));
            $total_data['fee_own_price'] = sprintf("%.2f", array_sum(array_map(function($val) {return $val['fee_own_price'];}, $stat_list)));
            $total_data['fee_input_num'] = array_sum(array_map(function($val) {return $val['fee_input_num'];}, $stat_list));
            $total_data['fee_input_price'] = sprintf("%.2f", array_sum(array_map(function($val) {return $val['fee_input_price'];}, $stat_list)));
        }

        return ['list' => $list, 'total_data' => $total_data];
    }

    /**
     * 获取日期列表
     * @return list
     */
    public function getRiskListFeeStatDateList()
    {
        $begin_date = I('get.begin_date', '', 'trim');
        $end_date = I('get.end_date', '', 'trim');

        $begin = $begin_date ? strtotime($begin_date) : strtotime("-1 day");
        $end = $end_date ? strtotime($end_date.' 23:59:59') : (strtotime(date('Y-m-d')) - 1);

        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }

    /**
     * 获取参数
     * @return array
     */
    public function getRiskListFeeStatParam()
    {
        $where = $this->getRiskListFeeStatTimeParam();
        $account_id = I('get.account_id', 0, 'intval');
        $account_name = I('get.account_name', '', 'trim');
        if ($account_id) {
            $where['account_id'] = $account_id;
        }
        if ($account_name) {
            $where['account_name'] = $account_name;
        }
        return $where;
    }

    /**
     * 获取时间参数
     * @return array
     */
    public function getRiskListFeeStatTimeParam()
    {
        $begin_date = I('get.begin_date', '', 'trim');
        $end_date = I('get.end_date', '', 'trim');

        $begin_date = $begin_date ? $begin_date : date('Y-m-d', strtotime("-1 day"));
        $end_date = $end_date ? $end_date : date('Y-m-d', strtotime("-1 day"));
        $fee_date = ['between', [$begin_date, $end_date]];
        return compact('fee_date');
    }


    /**
     * 导出文件
     * @return true
     */
    public function getRistStatFeeStatDownload($list)
    {
        $product_id = I('get.product_id', 0, 'intval');
        $info = D('RiskListUser')->where(['id' => $product_id])->field(['_id' => 0, 'developer'])->find();
        $product_name = isset($info['developer']) ? $info['developer'] : '';
        $begin = I('get.begin_date', '', 'strtotime');
        $end = I('get.end_date', '', 'strtotime');
        $begin_date = date('Ymd', $begin);
        $end_date = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/name_'.$product_name.'_风险名单日对账单_'.$begin_date.'至'.$end_date.'.csv';

        $title_list = '日期,本人查得量,本人查得费用,联系人查得量,联系人查得费用';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        $fee_own_num = isset($list['total_data']['fee_own_num']) ? $list['total_data']['fee_own_num'] : 0;
        $fee_own_price = isset($list['total_data']['fee_own_price']) ? $list['total_data']['fee_own_price'] : "0.00";
        $fee_input_num = isset($list['total_data']['fee_input_num']) ? $list['total_data']['fee_input_num'] : 0;
        $fee_input_price = isset($list['total_data']['fee_input_price']) ? $list['total_data']['fee_input_price'] : '0.00';

        $total_str = '"总计","'.$fee_own_num.'","'.$fee_own_price.'","'.$fee_input_num.'","'.$fee_input_price.'"';
        $total_str = mb_convert_encoding($total_str,'GBK','UTF-8');
        file_put_contents($file_name, PHP_EOL . $total_str, FILE_APPEND);

        foreach ($list['list'] as $key => $value) {

            $fee_own_num = isset($value['fee_own_num']) ? $value['fee_own_num'] : 0;
            $fee_own_price = isset($value['fee_own_price']) ? $value['fee_own_price'] : '0.00';
            $fee_input_num = isset($value['fee_input_num']) ? $value['fee_input_num'] : 0;
            $fee_input_price = isset($value['fee_input_price']) ? $value['fee_input_price'] : '0.00';

            $file_str = '"'.$key.'","'.$fee_own_num.'","'.$fee_own_price.'","'.$fee_input_num.'","'.$fee_input_price.'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }
}
