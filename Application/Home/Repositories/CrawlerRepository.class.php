<?php

namespace Home\Repositories;

use Common\Model\SystemUserRoleModel;

class CrawlerRepository
{
    /*
     * 展示一键切换选项的角色ID
     * */
    private $list_role_channel = [1,12,13];

    /**
     * 是否拥有一键切换通道的权限
     * @return boolean
     */
    public function showOneKeyChannel()
    {
        $login_name = trim($_SESSION['site_login_name']);

        if (!$login_name) {
            return false;
        }

        // 当前登陆用户的角色ID
        $role_info = (new SystemUserRoleModel())->where(['username' => $login_name])->field('roleid')->index('roleid')->select();
        $list_roles = array_keys($role_info);

        // 如果角色不是 1，12，13 则拒绝展示
        return !!array_intersect($list_roles, $this->list_role_channel);
    }
}
