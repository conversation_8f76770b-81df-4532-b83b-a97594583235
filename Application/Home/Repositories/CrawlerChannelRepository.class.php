<?php
/**
 * @Author: lidandan
 * @Date:   2018-08-13 10:05:57
 */

namespace Home\Repositories;

use Think\Cache\Driver\Redis;

class CrawlerChannelRepository extends BaseRepository
{
    //yulore服务器切换
    protected $switch_tag = 'calls_switch_channel_tag';
    //爬虫渠道地址
    protected $bmp_crawl_channel = 'bmp_crawl_channel';
    //重置密码渠道地址
    protected $bmp_pwd_channel = 'bmp_pwd_channel';
    //不支持的省份
    protected $crawler_unsurport = 'crawler_unsurport';

    protected $channel = '/crawler/list';

    protected $redis;

    public function __construct()
    {
        $this->redis = new Redis(C('REDIS_CHANNEL_CONFIG'));
    }

    /**
     * 获取服务器切换列表
     * @return $list
     */
    public function getSwitchList()
    {
        $this->redis->select(0);
        $list = $this->redis->hgetall($this->switch_tag);
        if ($list && is_array($list)) {
            array_walk($list, function(&$v, $k) {
                $v = json_decode($v, true);
            });
        }
        return $list;
    }

    /**
     * 设置切换服务器
     * @param array $param 参数
     */
    public function setCrawlerSwitch($param)
    {
        $this->redis->select(0);
        $key = $param['area'].$param['flow_type'];
        if (!$key) {
            throw new \Exception('缺少省份和运营商参数');
        }
        $kw = md5($key);
        $res = true;
        $channel = $this->getCrawlerlList();
        unset($channel['loc']);
        if (in_array($param['channel'], array_keys($channel))) {
            $value = ['dist' => $param['dist'], 'des' => '第三方通道：'.$channel[$param['channel']], 'type' => $param['channel']];
            $res = $this->redis->hset($this->switch_tag, $kw, json_encode($value, JSON_UNESCAPED_UNICODE));
        } else {
            if ($this->redis->hexists($this->switch_tag, $kw)) {
                $res = $this->redis->hdel($this->switch_tag, $kw);
            }
        }

        if ($res === false) {
            throw new \Exception('切换服务器失败');
        }
        return true;
    }

    /**
     * 获取不支持省份列表
     * @return list
     */
    public function getUnsurportList()
    {
        $this->redis->select(1);
        $list = $this->redis->hgetall($this->crawler_unsurport);
        return $list;
    }

    /**
     * 获取单个不支持省份信息
     * @param  array $param 参数名
     * @return true/false
     */
    public function getUnsurport($param)
    {
        $this->redis->select(1);
        $flow_type = isset($param['flow_type']) ? $param['flow_type'] : '';
        $area = isset($param['area']) ? $param['area'] : '';
        if (!$flow_type || !$area) {
            return false;
        }
        $key = $flow_type.'-'.$area;
        return $this->redis->hget($this->crawler_unsurport, $key);
    }

    /**
     * 设置支持状态
     * @param array $param 参数
     */
    public function setUnsurports($param, $unsur)
    {
        $flow_type = isset($param['flow_type']) ? $param['flow_type'] : '';
        $area = isset($param['area']) ? $param['area'] : '';
        if (!$flow_type || !$area) {
            throw new \Exception('缺少参数');
        }
        $key = $flow_type.'-'.$area;
        if ($unsur == '未支持') {
            $kw = md5($area.$flow_type);
            $this->redis->select(0);
            $this->redis->hdel($this->switch_tag, $kw);
            $flow_type = ($flow_type == '189') ? '10000' : $flow_type;
            $craw_key = $flow_type.'-'.$area;
            $this->redis->select(1);
            $this->redis->hdel($this->bmp_crawl_channel, $craw_key);
            $cache = $this->redis->hset($this->crawler_unsurport, $key, 1);
        } else {
            $this->redis->select(1);
            $cache = $this->redis->hdel($this->crawler_unsurport, $key);
        }
        return true;
    }

    /**
     * 获取爬虫渠道地址列表
     * @param  array $flow_map 运营商信息
     * @return list
     */
    public function getChannelList($flow_map)
    {
        $list = [];
        $this->redis->select(1);
        $channel_list = $this->redis->hgetall($this->bmp_crawl_channel);
        $channel_path = $channel_list ? array_filter(array_values($channel_list)) : [];
        if ($channel_path) {
            $where['channel_path'] = ['in', $channel_path];
            $where['channel_type'] = 1;
            $channel_config = D('ChannelConfig')->where($where)
                                                ->field('crawler_name, channel_name, channel_path')
                                                ->index('crawler_name')
                                                ->select();
            foreach ($channel_list as $key => $value) {
                $key_list = explode('-', $key);
                if ($value && isset($key_list[0]) && isset($key_list[1])) {
                    $flow_type = $key_list[0] == '10000' ? '189' : $key_list[0];
                    $crawler_name = $key_list[1].$flow_map[$flow_type];
                    $list[$crawler_name] = (isset($channel_config[$crawler_name]) && ($channel_config[$crawler_name]['channel_path'] == $value)) ? $channel_config[$crawler_name]['channel_name'] : '';
                }
            }
        }
        return $list;
    }

    /**
     * 设置爬虫渠道地址
     * @param array  $param 参数
     * @param string $url   渠道地址
     */
    public function setBmpCrawlChannel($param, $url)
    {
        $flow_type = isset($param['flow_type']) ? trim($param['flow_type']) : '';
        $flow_type = ($flow_type == '189') ? '10000' : $flow_type;
        $area = isset($param['area']) ? trim($param['area']) : '';
        if (!$flow_type || !$area || !$url) {
            throw new \Exception('缺少参数');
        }
        $this->redis->select(1);
        $key = $flow_type.'-'.$area;
        $res = $this->redis->hset($this->bmp_crawl_channel, $key, $url);
        if ($res === false) {
            throw new \Exception('爬虫渠道切换失败');
        }
        return true;
    }

    /**
     * 获取重置密码渠道地址列表
     * @return list
     */
    public function getPwdChannelList()
    {
        $list = [];
        $this->redis->select(5);
        $pwd_list = $this->redis->hgetall($this->bmp_pwd_channel);
        $pwd_path = $pwd_list ? array_filter(array_values($pwd_list)) : [];
        if ($pwd_path) {
            $where['channel_path'] = ['in', $pwd_path];
            $where['channel_type'] = 2;
            $pwd_config = D('ChannelConfig')->where($where)
                                            ->field('crawler_name, channel_name, channel_path')
                                            ->index('crawler_name')
                                            ->select();

            foreach ($pwd_list as $key => $value) {
                $key_list = explode('-', $key);
                if ($value && isset($key_list[0]) && isset($key_list[1])) {
                    $crawler_name = $key_list[1].$key_list[0];
                    $list[$crawler_name] = (isset($pwd_config[$crawler_name]) && ($pwd_config[$crawler_name]['channel_path'] == $value)) ? $pwd_config[$crawler_name]['channel_name'] : '';
                }
            }
        }
        return $list;
    }

    /**
     * 设置重置密码渠道地址
     * @param array  $param 参数
     * @param string $url   渠道地址
     */
    public function setPwdChannel($param, $url)
    {
        $flow_type = isset($param['flow_type']) ? trim($param['flow_type']) : '';
        $area = isset($param['area']) ? trim($param['area']) : '';

        if (!$flow_type || !$area || !$url) {
            throw new \Exception('缺少参数');
        }

        $this->redis->select(5);
        $key = $flow_type.'-'.$area;
        $res = $this->redis->hset($this->bmp_pwd_channel, $key, $url);
        if ($res === false) {
            throw new \Exception('重置密码渠道切换失败');
        }
        return true;
    }

    /**
     * 检查切换渠道参数
     * @return
     */
    public function getCheckParam()
    {
        $area = I('get.province', '', 'trim');
        $flow_type = I('get.flow_type', '', 'trim');
        $channel = I('post.channel', '', 'trim');
        $reason = I('post.channel_reason', '', 'trim');

        // filter channel dist
        if (!$reason) {
            throw new \Exception("请填充切换理由");
        }
        if (mb_strlen($reason, 'utf8') > 200) {
            throw new \Exception('请减少理由的长度, 限制200字内');
        }
        if (!$channel) {
            throw new \Exception('服务器必须选择！');
        }
        $dist = I('post.dist', '', 'trim');
        if ('ext' == $channel && (!$dist || !filter_var($dist, FILTER_VALIDATE_URL))) {
            throw new \Exception('服务地址必须是一个合法的URL地址！');
        }
        if ($this->getUnsurport(compact('flow_type', 'area')) && in_array($channel, ['loc', 'ext'])) {
            throw new \Exception('此羽乐科技服务器不支持！');
        }
        return compact('area', 'flow_type', 'channel', 'reason', 'dist');
    }

    /**
     * 获取渠道名称
     * @param  array $param    参数
     * @param  array $flow_map 运营商参数
     * @return list
     */
    public function getChannelConfigList($param, $flow_map)
    {
        $channel_type = $param['channel_type'];
        $crawler_name = $param['area'].$flow_map[$param['flow_type']];
        $list = D('ChannelConfig')->where(compact('channel_type', 'crawler_name'))
                                  ->field('channel_name, channel_path')
                                  ->order('crawler_name, channel_name')
                                  ->select();
        if ($channel_type == 1) {
            $this->redis->select(1);
            $key = ($param['flow_type'] == '189') ? '10000-'.$param['area'] : $param['flow_type'].'-'.$param['area'];
            $info = $this->redis->hget($this->bmp_crawl_channel, $key);
        } else {
            $this->redis->select(5);
            $key = $flow_map[$param['flow_type']].'-'.$param['area'];
            $info = $this->redis->hget($this->bmp_pwd_channel, $key);
        }
        if ($list && is_array($list)) {
            foreach ($list as $key => $value) {
                $list[$key]['channel_flag'] = ($info == $value['channel_path']) ? 1 : 0;
            }
        }
        return $list;
    }

    public function getCrawlerlList()
    {
        $domain = C('CRS_API_CONFIG')['manage'];
        $url = $domain.$this->channel;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        array_map(function($v) use (&$list) {
            $list[$v['channel']] = $v['name'];
        }, $res['data']);

        $channel = ['loc' => '羽乐科技', 'ext' => '羽乐科技备用'];
        unset($list['yulore']);
        $list = array_merge($channel, $list);
        return $list;
    }
}