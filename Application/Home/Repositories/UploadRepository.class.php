<?php

namespace Home\Repositories;

use Think\Upload;
use Common\ORG\Page;
use Common\Model\UploadModel;

class UploadRepository
{
    private $model;
    /**
     * @var \PHPExcel
     **/
    private $objPHPExcel;

    public function __construct()
    {
        $this->model = new UploadModel();
    }

    public function data($where = [], $field = '*', $order = null, $page = null)
    {
        $model = $this->model->field($field)->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        if (!is_null($page)) {
            $model = $model->limit($page->firstRow, $page->listRows);
        }
        return $model->select();
    }

    public function count($where = [])
    {
        $field = 'count(*) as c';
        $count = $this->data($where, $field);
        return $count[0]['c'];
    }

    public function getListData()
    {
        //获取get参数
        $input = $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);
        //获取符合当前条件的数据数量
        $count = $this->count($where);
        //分页
        $listRow = 50;
        $page    = new Page($count, $listRow);
        //查询数据
        $list_data = $this->data($where, '*', 'id desc', $page);
        $page      = $page->show();

        $arr = compact('list_data', 'page', 'input');
        return $arr;


    }

    protected function getWhereByParam($params)
    {
        $where = [];
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time                = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $start_time          = min($time);
            $end_time            = max($time) + 86399;
            $where['created_at'] = ['between', [$start_time, $end_time]];
        } elseif (!empty($params['start_time'])) {
            $where['created_at'] = ['egt', strtotime($params['start_time'])];
        } elseif (!empty($params['end_time'])) {
            $where['created_at'] = ['elt', strtotime($params['end_time'])];
        }

        //状态
        if ($params['status'] != '-1') {
            $where['status'] = ['eq', $params['status']];
        }
        return $where;
    }

    protected function getParamByGet()
    {
        $params = [];
        //交易日期
        $params['start_time'] = I('get.start_time', '', 'trim');
        $params['end_time']   = I('get.end_time', '', 'trim');
        //状态
        $params['status'] = I('get.status', '-1', 'trim');
        return $params;
    }

    public function file_in($admin = '')
    {
        //将文件上传至服务器
        $file_arr = $this->upload();
        if(!$file_arr){
            return false;
        }
        $filePath = $file_arr['save_name'];
        $fileName = $file_arr['file_name'];
        //读取Excel内容
        $res  = $this->getDataByFile($filePath);
        $time = time();
        $arr = [];
        array_walk($res, function($value) use (&$arr, $time, $admin){
            $arr[] = [
                'tel' => $value['tel'],
                'callrisk' => $value['callrisk']
            ];
        });
        if (empty($arr)) {
            return false;
        }

        $json_data = json_encode($arr, JSON_UNESCAPED_UNICODE);
        $data = [
            'json_data' => $json_data,
            'created_at' => $time,
            'updated_at' => $time,
            'admin' => $admin,
            'file_name' => $fileName
        ];

        $res = $this->model->add($data);
        if($res){
            @unlink($filePath);
            return true;
        }
        return false;
    }

    /**
     * 上传的文件至服务器
     *
     * @access public
     *
     * @return string|boolean 文件的路径
     **/
    private function upload()
    {
        $config = [
            'savePath' => '',
            'exts'     => ['xls', 'xlsx', 'csv'],
            'autoSub'  => false,
            'rootPath' => CACHE_PATH,
            'maxSize'  => 1024 * 3 * 1024
        ];
        $upload = new Upload($config);// 实例化上传类
        $res    = $upload->upload();

        $res_arr = [];
        $res_arr['save_name'] = '';
        $res_arr['file_name'] = '';
        if ($res !== false) {
            $res_arr['save_name'] =  CACHE_PATH . $res['file']['savename'];
            $res_arr['file_name'] = $res['file']['name'];
            return $res_arr;
        }
        return false;
    }

    /**
     * 读取Excel内的数据内容
     *
     * @access private
     *
     * @param $filePath string 文件地址
     *
     * @return array|boolean
     **/
    private function getDataByFile($filePath)
    {
        if (!file_exists($filePath)) {
            return false;
        }
        //引入PHPExcel
        $this->include_phpExcel();
        //实例化excel读取对象
        $this->ini_read_excel($filePath);
        //读取Excel的数据
        $res  = $this->getDataForExcel();
        return $res;
    }

    /**
     * 引入Excel
     *
     * @access private
     *
     * @return void
     **/
    private function include_phpExcel()
    {
        include LIB_PATH . '/Org/PHPExcel/PHPExcel.php';
        include LIB_PATH . '/Org/PHPExcel/PHPExcel/Writer/Excel2007.php';
        //$this->objPHPExcel = new \PHPExcel();
    }

    /**
     * excel文件读取预先实例化
     *
     * @access private
     *
     * @return void
     **/
    private function ini_read_excel($filePath)
    {
        try {
            $inputFileType     = \PHPExcel_IOFactory::identify($filePath);
            $objReader         = \PHPExcel_IOFactory::createReader($inputFileType);
            $this->objPHPExcel = $objReader->load($filePath);
        } catch (\Exception $e) {
            die('read excel failed, message : ' . $e->getMessage());
        }
    }

    /**
     * 获取Excel文件中的数据
     *
     * @access private
     *
     * @return array
     **/
    private function getDataForExcel()
    {
        $sheet         = $this->objPHPExcel->getSheet(0);
        $highestRow    = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        $data          = [];
        for ($row = 2; $row <= $highestRow; $row ++) {
            $tel = $this->objPHPExcel->getActiveSheet()->getCell("A".$row)->getValue();
            $callrisk = $this->objPHPExcel->getActiveSheet()->getCell("B".$row)->getValue();
            $data[]   = [
                'tel' => $tel,
                'callrisk' => $callrisk
            ];
        }
        return $data;
    }

}