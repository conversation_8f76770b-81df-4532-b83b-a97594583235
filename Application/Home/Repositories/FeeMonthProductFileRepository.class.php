<?php
namespace Home\Repositories;

class FeeMonthProductFileRepository extends BaseRepository
{
    public function getFeeMonthProductDownload($list)
    {
        $date = $this->getFeeMonthTime();
        $file_name = RUNTIME_PATH . 'Cache/产品对账单'.$date['start'].'~'.$date['end'].'.csv';
        $file_name = mb_convert_encoding($file_name, 'GBK', 'UTF-8');
        $title_list = '产品ID,产品名称,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list, 'GBK', 'UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $file_str = '"'.$value['product_id'].'","'.$value['product_name'].'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str, 'GBK', 'UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getFeeMonthCustomerDownload($list)
    {
        $date = $this->getFeeMonthTime();
        $file_name = RUNTIME_PATH . 'Cache/产品客户对账单'.$date['start'].'~'.$date['end'].'.csv';
        $file_name = mb_convert_encoding($file_name, 'GBK', 'UTF-8');
        $title_list = '客户ID,客户名称,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list, 'GBK', 'UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $file_str = '"'.$value['customer_id'].'","'.$value['customer_name'].'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str, 'GBK', 'UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getFeeMonthDetailDownload($list)
    {
        $date = $this->getFeeMonthTime();
        $file_name = RUNTIME_PATH . 'Cache/产品月对账单'.$date['start'].'~'.$date['start'].'.csv';
        $file_name = mb_convert_encoding($file_name, 'GBK', 'UTF-8');
        $title_list = '日期,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list, 'GBK', 'UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $month = date('Y/m/01', strtotime($value['month']));
            $file_str = '"'.$month.'","'.$value['fee_amount'].'","'.$value['fee_price'].'"';

            $file_str = mb_convert_encoding($file_str, 'GBK', 'UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    public function getFeeMonthTime()
    {
        $start = I('get.start', date('Y-m', strtotime('-1 month')), 'trim');
        $end = I('get.end', date('Y-m', strtotime('-1 month')), 'trim');
        return compact('start', 'end');
    }
}