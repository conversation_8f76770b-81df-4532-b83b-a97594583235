<?php

namespace Home\Repositories;

use Common\Common\CurlTrait;
use Common\ORG\Page;

class BmpaPushLogRepository
{
    use CurlTrait;
    protected $listRows = 15;

    //首页逻辑展示
    public function index()
    {
        //获取查询参数
        $params = $this->getParams();
        //获取接口请求数据
        $data = $this->getIndexDataByCurl($params);
        //分页
        $page = new Page($data['total'], $this->listRows);
        $page = $page->show();
        //条件查询展示台
        $input = $this->getInput($params);
        $data = $data['list'];
        //列表中的各种状态集合
        $status = [0 => '失败', 1 => '成功', -1 => '未推送'];
        return compact('data', 'page', 'input', 'status');
    }
    //获取查询参数
    protected function getParams()
    {
        //时间区间
        $time[] = I('get.start_time', time() - 86400*31, 'strtotime');
        $time[] = I('get.end_time', time(), 'strtotime');
        $end_time = date('Y-m-d', max($time));
        $start_time = date('Y-m-d', min($time));
        //运营商
        $channel = I('get.channel', -5, 'trim');
        //详单推送状态
        $push_status = I('get.push_status', -5, 'intval');
        //授权成功状态
        $auth_status = I('get.auth_status', -5, 'intval');
        //报告生成状态
        $make_status = I('get.make_status', -5, 'intval');
        //详单状态推送状态
        $status_status = I('get.status_status', -5, 'intval');
        //sid
        $sid = I('get.sid', '', 'trim');
        //tel
        $tel = I('get.tel', '', 'trim');
        //cid
        $cid = I('get.cid', '', 'trim');
        return compact('start_time', 'end_time', 'channel', 'push_status', 'auth_status', 'make_status', 'status_status', 'sid', 'cid', 'tel');
    }
    //根据参数生成接口请求的参数
    protected function getCurlParams($params)
    {
        $where = [];
        //时间区间
        $where['start_time'] = $params['start_time'];
        $where['end_time'] = $params['end_time'];
        //sid
        if (!empty($params['sid'])) {
            $where['sid'] = $params['sid'];
        }
        //cid
        if (!empty($params['cid'])) {
            $where['cid'] = $params['cid'];
        }
        //tel
        if (!empty($params['tel'])) {
            $where['tel'] = $params['tel'];
        }
        //运营商
        if ($params['channel']!=-5) {
            $where['carrier'] = $params['channel'];
        }
        //详单状态
        if ($params['push_status']!=-5) {
            $where['push_call_log'] = $params['push_status'];
        }
        //授权状态
        if ($params['auth_status']!=-5) {
            $where['push_auth'] = $params['auth_status'];
        }
        //报告状态
        if ($params['make_status']!=-5) {
            $where['push_report_status'] = $params['make_status'];
        }
        //详单状态推送状态
        if ($params['status_status']!=-5) {
            $where['push_call_log_status'] = $params['status_status'];
        }
        //当前页数
        $where['page'] = I('get.page', 1, 'intval');
        //每页条数
        $where['page_size'] = $this->listRows;
        return $where;
    }
    //获取请求数据
    protected function getIndexDataByCurl($params)
    {
        $url = C('LIST_API_URL.bmpa_log_list');
        $params = $this->getCurlParams($params);
        $params['start_time'] .= ' 00:00:00';
        $params['end_time'] .= '23:59:59';
        $result = $this->post($url, json_encode($params, JSON_UNESCAPED_UNICODE));
        if ($result['status'] != 0) {
            throw new \Exception('get data failed, message is ' . json_encode($result));
        }
        return $result['data'];
    }
    //获取页面的展示台
    protected function getInput($params)
    {
        //运营商
        $channel = [-5 => '查询全部', '移动' => '移动', '电信' => '电信', '联通' => '联通'];
        $params['channel_option'] = $this->makeOption($channel, $params['channel']);
        //推送状态
        $push_status = [-5 => '全部', 0 => '失败', 1 => '成功', -1 => '未推送'];
        $params['push_status_option'] = $this->makeOption($push_status, $params['push_status']);
        //授权状态
        $params['auth_status_option'] = $this->makeOption($push_status, $params['auth_status']);
        //生成状态
        $params['make_status_option'] = $this->makeOption($push_status, $params['make_status']);
        //详单状态推送状态
        $params['status_status_option'] = $this->makeOption($push_status, $params['status_status']);
        return $params;
    }
    //生成下拉选框的html代码
    protected function makeOption($data, $default = null, $value = null, $show = null)
    {
        if (!empty($value) && !empty($show)) {
            $data = array_column($data, $show, $value);
        }
        $display = '';
        array_walk($data, function ($show, $value) use ($default, &$display) {
            $display .= $default==$value?"<option value='{$value}' selected>{$show}</option>":"<option value='{$value}'>{$show}</option>";
        });
        return $display;
    }
    public function send()
    {
        $url = C('LIST_API_URL.bmpa_log_send');
        $result = $this->post($url, json_encode($this->getParamsForSend(), JSON_UNESCAPED_UNICODE));
        if ($result['status']==0) {
            return true;
        }
        return $result['msg'];
    }
    protected function getParamsForSend()
    {
        $params['sid'] = I('get.sid', '', 'trim');
        $params['push_type'] = I('get.type', '', 'trim');
        return $params;
    }
    /**
     * 详细页所需数据
     *
     * @access public
     *
     * @return array|boolean
     **/
    public function detail()
    {
        $sid = I('get.sid', '', 'trim');
        if (empty($sid)) {
            return false;
        }
        //获取接口地址
        $url = C('LIST_API_URL.bmpa_log_detail');
        $url .= '?sid='.$sid;
        //请求接口，获取数据
        $result = $this->get($url);
        if ($result['status']!=0) {
            throw new \Exception('gat data is failed, message is ' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }
        return $result['data'];
    }
}