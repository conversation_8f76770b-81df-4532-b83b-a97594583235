<?php
namespace Home\Repositories;

class FeeMonthCustomerRepository extends BaseRepository
{
    public function getCustomerAll()
    {
        $list = D('Account/Customer')->field('customer_id, name, company')->select();
        return array_column($list, null, 'customer_id');
    }

    public function getProductAll()
    {
        $list = D('Account/Product')->field('product_id, product_name, fee_config')->select();
        array_walk($list, function(&$v) {
            $fee_config = $v['fee_config'] ? json_decode($v['fee_config'], true) : [];
            $v['fee_config'] = array_column($fee_config, null, 'val');
        });
        return array_column($list, null, 'product_id');
    }

    public function getAccountAll()
    {
        $customer = $this->getCustomerAll();
        $list = D('Account/Account')->field('account_id, account_name, customer_id, create_at')->select();
        array_walk($list, function(&$v, $k, $p) {
            $v['customer_name'] = $p[$v['customer_id']]['name'];
            $v['customer_company'] = $p[$v['customer_id']]['company'];
        }, $customer);
        return array_column($list, null, 'account_id');
    }

    /**
     * 客户对账单列表
     * @param  number  $start 开始
     * @param  number  $limit 每页数量
     * @param  string  $order 排序
     * @return array
     */
    public function getFeeMonthCustomerList($where, $start, $limit = 25)
    {
        $customer = $this->getCustomerAll();
        $order = $this->getFeeMonthOrder();
        $field = 'customer_id, sum(fee_price) as fee_price';
        $list = D('FeeStat')->field($field)
                            ->where($where)
                            ->group('customer_id')
                            ->order($order)
                            ->limit($start.','.$limit)
                            ->select();
        array_walk($list, function(&$v, $k, $p) {
            $v['customer_name'] = $p[$v['customer_id']]['name'];
            $v['customer_company'] = $p[$v['customer_id']]['company'];
        }, $customer);
        return $list;
    }

    public function getFeeMonthCustomerNum($where)
    {
        $sub = D('FeeStat')->where($where)->group('customer_id')->select(false);
        $res = D('FeeStat')->query('select count(*) as num from ('.$sub.') a limit 1');
        return isset($res[0]['num']) ? $res[0]['num'] : 0;
    }

    /**
     * 获取费用总计
     * @return array
     */
    public function getFeeMonthCustomerPriceSum($where)
    {
        return D('FeeStat')->where($where)->sum('fee_price');
    }

    /**
     * 客户对账单列表
     * @return array
     */
    public function getFeeMonthCustomerParam()
    {
        $start = I('get.start', '', 'trim');
        $end = I('get.end', '', 'trim');
        $start = $start ? date('Y-m-01', strtotime($start)) : date('Y-m-01', strtotime(
            '-1 month'));
        $end = $end ? date('Y-m-01', strtotime($end)) : date('Y-m-01', strtotime('-1 month'));

        $where['fee_date'] = ['between', [$start, $end]];

        $customer_id = I('get.customer_id', '', 'trim');
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }

        $customer_name = I('get.customer_name', '', 'trim');
        if ($customer_name) {
            if ($customer_id && $customer_id != $customer_name) {
                $where['customer_id'] = 'no result';
            }
            $where['customer_id'] = $customer_name;
        }

        $product_id = I('get.product_id', '', 'trim');
        if ($product_id) {
            $where['product_id'] = $product_id;
        }

        $account_id = I('get.account_id', '', 'trim');
        if ($account_id) {
            $where['account_id'] = $account_id;
        }

        return $where;
    }

    public function getFeeMonthProductList($where, $start, $limit = 25)
    {
        $order = $this->getFeeMonthOrder();
        $product = $this->getProductAll();
        $account = $this->getAccountAll();
        $fee_config = $this->getFeeConfigList();
        $fee_method_list = D('FeeStat')->getFeeMethod();
        $field = 'product_id, sum(fee_amount) as fee_amount, sum(fee_price) as fee_price, GROUP_CONCAT(account_id) as account_id, GROUP_CONCAT(fee_basis) as fee_basis, GROUP_CONCAT(fee_method) as fee_method';
        $list = D('FeeStat')->field($field)
                            ->where($where)
                            ->order($order)
                            ->limit($start.','.$limit)
                            ->group('product_id')
                            ->select();
        foreach ($list as $key => &$value) {
            $value['product_name'] = $product[$value['product_id']]['product_name'];
            $account_id = explode(',', $value['account_id']);
            $account_id = $info = array_unique($account_id);
            $fee_method = explode(',', $value['fee_method']);
            $fee_basis = explode(',', $value['fee_basis']);
            array_walk($account_id, function(&$v, $k, $p) {
                $m['account_id'] = $v;
                $m['create_at'] = $p[$v]['create_at'];
                $m['account_name'] = $p[$v]['account_name'];
                $v = $m;
            }, $account);
            array_multisort(array_column($account_id, 'create_at'), SORT_DESC, $account_id);
            $value['account_name'] = implode(' | ', array_column($account_id, 'account_name'));
            $kk = array_search($account_id[0]['account_id'], $info);
            $value['fee_basis'] = $product[$value['product_id']]['fee_config'][$fee_basis[$kk]]['cn_name'];
            $value['fee_method'] = $fee_method_list[$fee_method[$kk]];
            $value['fee_day'] = isset($fee_config[$value['product_id']]) ? 1 : 0;
        }
        return $list;
    }

    public function getFeeMonthOrder()
    {
        $order = I('get.order', 'fee_price desc', 'trim');

        if (!in_array($order, ['fee_price desc', 'fee_price asc', 'fee_amount desc', 'fee_amount asc'])) {
            $order = 'fee_price desc';
        }
        return $order;
    }

    public function getFeeMonthProductNum($where)
    {
        $sub = D('FeeStat')->where($where)->group('product_id')->select(false);
        $res = D('FeeStat')->query('select count(*) as num from ('.$sub.') a limit 1');
        return isset($res[0]['num']) ? $res[0]['num'] : 0;
    }

    public function getFeeMonthProductSum($where)
    {
        return D('FeeStat')->field('sum(fee_amount) as fee_amount, sum(fee_price) as fee_price')
                           ->where($where)
                           ->find();
    }

    public function getFeeConfigList()
    {
        $where = ['fee_method' => 2, 'fee_amount_rule' => 1];
        $field = 'account_id, product_id, customer_id, fee_basis';
        $list = D('FeeConfig')->field($field)->where($where)->group('product_id')->select();
        return array_column($list, null, 'product_id');
    }

    public function getCustomerAccountList()
    {
        $product_id = I('get.product_id', '', 'trim');
        $where = [];
        if ($product_id) {
            $where['product_id'] = $product_id;
        }
        $account_product = D('Account/AccountProduct')->field('account_id')->where($where)->select();
        if (!$account_product) {
            return [];
        }
        $account_id = ['in', array_column($account_product, 'account_id')];
        $account = D('Account/Account')->field('customer_id, account_id, account_name')->where(compact('account_id'))->select();
        array_walk($account, function($v) use (&$customer_list) {
            $customer_list[$v['customer_id']][] = $v;
        });
        $customer = $this->getCustomerAll();
        array_walk($customer, function(&$v, $k, $p) {
            $v['account_list'] = isset($p[$v['customer_id']]) ? $p[$v['customer_id']] : [];
        }, $customer_list);
        return $customer;
    }

    public function getProductInfo()
    {
        $product_id = I('get.product_id', '', 'trim');
        return D('Account/Product')->where(compact('product_id'))->find();
    }

    public function getFeeMonthProductDetail($where)
    {
        $field = 'sum(fee_amount) as fee_amount, sum(fee_price) as fee_price, fee_date';
        $stat = D('FeeStat')->field($field)->where($where)->group('fee_date')->select();
        $stat = array_column($stat, null, 'fee_date');
        $list = $this->getMonthList();
        array_walk($list, function(&$v, $k, $p) {
            $info = ['fee_amount' => 0, 'fee_price' => 0];
            $date = $v.'-01';
            $m = isset($p[$date]) ? $p[$date] : $info;
            $m['month'] = $v;
            $v = $m;
        }, $stat);
        if (I('get.order')) {
            $order = $this->getFeeMonthOrder();
            $name = explode(' ', $order);
            $order = ($name[1] == 'asc') ? SORT_ASC : SORT_DESC;
            array_multisort(array_column($list, $name[0]), $order, $list);
        }
        return $list;
    }

    public function getMonthList()
    {
        $start = I('get.start', '', 'trim');
        $end = I('get.end', '', 'trim');
        $start = $start ? $start : date('Y-m', strtotime('-1 month'));
        $end = $end ? $end : date('Y-m', strtotime('-1 month'));
        $list = [];
        while ($end >= $start) {
            array_push($list, $end);
            $end = date('Y-m', strtotime($end.' -1 month'));
        }
        return $list;
    }

    public function getFeeDayProductDetail($where)
    {
        $field = 'sum(fee_amount) as fee_amount, sum(fee_price) as fee_price, fee_date';
        $stat = D('FeeStatDay')->field($field)->where($where)->group('fee_date')->select();
        $stat = array_column($stat, null, 'fee_date');
        $list = $this->getDayList();
        array_walk($list, function(&$v, $k, $p) {
            $m = isset($p[$v]) ? $p[$v] : ['fee_amount' => 0, 'fee_price' => 0];
            $m['date'] = $v;
            $v = $m;
        }, $stat);
        if (I('get.order')) {
            $order = $this->getFeeMonthOrder();
            $name = explode(' ', $order);
            $order = ($name[1] == 'asc') ? SORT_ASC : SORT_DESC;
            array_multisort(array_column($list, $name[0]), $order, $list);
        }
        return $list;
    }

    public function getFeeDayProductSum($where)
    {
        $field = 'sum(fee_amount) as fee_amount, sum(fee_price) as fee_price';
        return D('FeeStatDay')->field($field)->where($where)->find();
    }

    public function getDayList()
    {
        $start = I('get.start', '', 'trim');
        $end = I('get.end', '', 'trim');
        $start = $start ? $start : date('Y-m-01');
        $end = $end ? $end : date('Y-m-d');
        $list = [];
        while ($end >= $start) {
            array_push($list, $end);
            $end = date('Y-m-d', strtotime($end.' -1 day'));
        }
        return $list;
    }

    public function getFeeDayCustomerParam()
    {
        $start = I('get.start', '', 'trim');
        $end = I('get.end', '', 'trim');

        $start = $start ? $start : date('Y-m-01');
        $end = $end ? $end : date('Y-m-d');

        $where['fee_date'] = ['between', [$start, $end]];
        $customer_id = I('get.customer_id', '', 'trim');
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }
        $product_id = I('get.product_id', '', 'trim');
        if ($product_id) {
            $where['product_id'] = $product_id;
        }
        $account_id = I('get.account_id', '', 'trim');
        if ($account_id) {
            $where['account_id'] = $account_id;
        }
        return $where;
    }
}
