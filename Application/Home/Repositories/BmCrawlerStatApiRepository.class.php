<?php
namespace Home\Repositories;

class BmCrawlerStatApiRepository extends BaseRepository
{
    //APIKEY
    protected $apiKey = '0aa2cb33d0eaedc4abe412348045dc8';
    //APISECRET
    protected $apiSecret = '0b178ed2d1d049e46472711d8f92bf4';
    //列表统计
    protected $listUrl = '/admin/crawler/list';
    //详情统计
    protected $detailUrl = '/admin/crawler/detail';

    /**
     * 获取邦秒爬列表统计数据
     * @param  array $user_list 账号信息
     * @param  array $param 查询条件
     * @return array
     */
    public function getReportList($user_list, $param)
    {
        $list = $this->getReportListApi($param);
        $total_data = [];
        foreach ($user_list as $key => &$value) {
            $value = isset($list[$value['id']]) ? array_merge($value, $list[$value['id']]) : $value;
            $value['authen_pct'] = isset($value['authen_pct']) ? sprintf("%.2f", $value['authen_pct']*100).'%' : '0.00%';//授权成功率
            $value['log_loss_pct'] = isset($value['log_loss_pct']) ? sprintf("%.2f", $value['log_loss_pct']*100).'%' : '0.00%';//详单缺失率
            $value['pwd_rt_pct'] = isset($value['pwd_rt_pct']) ? sprintf("%.2f", $value['pwd_rt_pct']*100).'%' : '0.00%';//重置密码成功率
            $value['crawl_pct'] = isset($value['crawl_pct']) ? sprintf("%.2f", $value['crawl_pct']*100).'%' : '0.00%';//爬取成功率
            $value['report_pct'] = isset($value['report_pct']) ? sprintf("%.2f", $value['report_pct']*100).'%' : '0.00%';//报告成功率

            $total_data['total_nums'] += (isset($value['total_nums']) ? $value['total_nums'] : 0);
            $total_data['authen_nums'] += (isset($value['authen_nums']) ? $value['authen_nums'] : 0);
            $total_data['crawl_nums'] += (isset($value['crawl_nums']) ? $value['crawl_nums'] : 0);
            $total_data['report_nums'] += (isset($value['report_nums']) ? $value['report_nums'] : 0);
            $total_data['tel_num'] += (isset($value['tel_num']) ? $value['tel_num'] : 0);
            $total_data['call_log_intact_nums'] += (isset($value['call_log_intact_nums']) ? $value['call_log_intact_nums'] : 0);
            $total_data['final_nums'] += (isset($value['final_nums']) ? $value['final_nums'] : 0);
            $total_data['pwd_rt_success'] += (isset($value['pwd_rt_success']) ? $value['pwd_rt_success'] : 0);
            $total_data['pwd_rt_total'] += (isset($value['pwd_rt_total']) ? $value['pwd_rt_total'] : 0);
        }
        if ($total_data) {
            $bill_loss_num = $total_data['final_nums'] - $total_data['call_log_intact_nums'];
            $total_data['log_loss_pct'] = empty($total_data['final_nums']) ? '0.00%' : sprintf("%.2f", $bill_loss_num/$total_data['final_nums']*100).'%';
            $total_data['pwd_rt_pct'] = empty($total_data['pwd_rt_total']) ? '0.00%' : sprintf("%.2f", $total_data['pwd_rt_success']/$total_data['pwd_rt_total']*100).'%';
            $total_data['authen_pct'] = empty($total_data['total_nums']) ? '0.00%' : sprintf("%.2f", $total_data['authen_nums']/$total_data['total_nums']*100).'%';
            $total_data['crawl_pct'] = empty($total_data['authen_nums']) ? '0.00%' : sprintf("%.2f", $total_data['crawl_nums']/$total_data['authen_nums']*100).'%';
            $total_data['report_pct'] = empty($total_data['crawl_nums']) ? '0.00%' : sprintf("%.2f", $total_data['report_nums']/$total_data['crawl_nums']*100).'%';
        }
        return ['user_list' => $user_list, 'total_data' => $total_data];
    }

    /**
     * 获取详情统计数据
     * @param  array $param 查询条件
     * @return array
     */
    public function getReportDetail($param)
    {
        $list = $total_data = [];
        $data = $this->getReportDetailApi($param);
        if (!empty($data) && is_array($data)) {
            array_walk($data, function(&$v, $k) {
                $v['authen_pct'] = sprintf("%.2f", $v['authen_pct']*100).'%';
                $v['log_loss_pct'] = sprintf("%.2f", $v['log_loss_pct']*100).'%';
                $v['pwd_rt_pct'] = sprintf("%.2f", $v['pwd_rt_pct']*100).'%';
                $v['crawl_pct'] = sprintf("%.2f", $v['crawl_pct']*100).'%';
                $v['report_pct'] = sprintf("%.2f", $v['report_pct']*100).'%';
            });
            foreach ($data as $key => &$value) {
                $total_data['total_nums'] += $value['total_nums'];
                $total_data['authen_nums'] += $value['authen_nums'];
                $total_data['crawl_nums'] += $value['crawl_nums'];
                $total_data['report_nums'] += $value['report_nums'];
                $total_data['tel_num'] += $value['tel_num'];
                $total_data['call_log_intact_nums'] += $value['call_log_intact_nums'];
                $total_data['final_nums'] += $value['final_nums'];
                $total_data['pwd_rt_success'] += $value['pwd_rt_success'];
                $total_data['pwd_rt_total'] += $value['pwd_rt_total'];
                $date = date('Y-m-d', strtotime($key));
                $list[$date] = $value;
            }
            $bill_loss_num = $total_data['final_nums'] - $total_data['call_log_intact_nums'];
            $total_data['log_loss_pct'] = empty($total_data['final_nums']) ? '0.00%' : sprintf("%.2f", $bill_loss_num/$total_data['final_nums']*100).'%';
            $total_data['pwd_rt_pct'] = empty($total_data['pwd_rt_total']) ? '0.00%' : sprintf("%.2f", $total_data['pwd_rt_success']/$total_data['pwd_rt_total']*100).'%';
            $total_data['authen_pct'] = empty($total_data['total_nums']) ? '0.00%' : sprintf("%.2f", $total_data['authen_nums']/$total_data['total_nums']*100).'%';
            $total_data['crawl_pct'] = empty($total_data['authen_nums']) ? '0.00%' : sprintf("%.2f", $total_data['crawl_nums']/$total_data['authen_nums']*100).'%';
            $total_data['report_pct'] = empty($total_data['crawl_nums']) ? '0.00%' : sprintf("%.2f", $total_data['report_nums']/$total_data['crawl_nums']*100).'%';
        }
        return ['date_stat_show' => $list, 'total_data' => $total_data];
    }

    /**
     * 获取列表统计数据
     * @param  array $param 查询数据
     * @return array
     */
    public function getReportListApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->listUrl;
        $param = array_merge($param, $this->getSign());
        
        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 获取详情统计数据
     * @param  array $param 参数
     * @return array
     */
    public function getReportDetailApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->detailUrl;
        $param = array_merge($param, $this->getSign());
        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 获取签名
     * @return array
     */
    public function getSign()
    {
        $api_key = $this->apiKey;
        $api_secret = $this->apiSecret;
        $dict_source = compact('api_key', 'api_secret');
        $param['t'] = time();
        $param['n'] = rand(1000, 9999);
        $data = array_merge($dict_source, $param);
        sort($data, SORT_STRING);
        $param['s'] = md5(md5(implode($data)));
        return $param;
    }
}