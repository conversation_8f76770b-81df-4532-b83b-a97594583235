<?php
/**
 * @Author: lidandan
 * @Date:   2018-06-29 14:04:57
 */
namespace Home\Repositories;

class RiskListRepository extends BaseRepository
{
    /**
     * 添加风险名单
     * @param array $data 添加的参数
     */
    public function addUser($data)
    {
        $data['created_at'] = new \MongoInt32(time());
        $data['updated_at'] = $data['created_at'];
        $id = D('RiskListUser')->getMongoNextId('id');
        $data['id'] = new \MongoInt32($id);
        $account_name = $data['name'];
        unset($data['name']);
        $data = $this->filterInfo($data);
        $res = D('RiskListUser')->add($data);
        if ($res && $account_name) {
            D('FinanceAccountProduct')->bindToAccounts($id, $account_name, 6);
        }
        return $res;
    }

    /**
     * 更新风险名单
     * @param  array $data  更新字段
     * @param  array $where 查询条件
     * @return true/false
     */
    public function updateUser($data, $id)
    {
        $account_name = $data['name'];
        unset($data['name']);
        $data['updated_at'] = new \MongoInt32(time());
        $data = $this->filterInfo($data, $id);
        D('FinanceAccountProduct')->updateBindToAccounts($id, $account_name, 6);
        return D('RiskListUser')->updateUser($data, ['id' => $id]);
    }

    /**
     * 根据ID获取账号信息
     * @param  number $id 账号ID
     * @return array
     */
    public function getUserInfo($id)
    {
        $info = D('RiskListUser')->field(['_id' => 0])->where(['id' => $id])->find();
        if (!$info) {
            return false;
        }
        if (isset($info['daily_limit']) && $info['daily_limit'] != -1) {
            $info['limit_type'] = 1;
            $info['limit_num'] = $info['daily_limit'];
            unset($info['daily_limit']);
        }
        if (isset($info['monthly_limit']) && $info['monthly_limit'] != -1) {
            $info['limit_type'] = 2;
            $info['limit_num'] = $info['monthly_limit'];
        }
        if (isset($info['yearly_limit']) && $info['yearly_limit'] != -1) {
            $info['limit_type'] = 3;
            $info['limit_num'] = $info['yearly_limit'];
        }
        if (isset($info['daily_limit']) && $info['daily_limit'] == -1) {
            $info['limit_type'] = -1;
            $info['limit_num'] = '';
        }
        $info['access_ip'] = (isset($info['access_ip']) && !empty($info['access_ip'])) ? implode("\n", $info['access_ip']) : '';
        $info['validuntil'] = isset($info['validuntil']) ? date('Y-m-d', $info['validuntil']) : '';
        return $info;
    }

    /**
     * 获取所有账号信息
     * @param  array  $field 查询字段
     * @return array list
     */
    public function getUserListAll($field = [])
    {
        $fields = array_merge(['_id' => 0, 'id', 'developer'], $field);
        return D('RiskListUser')->field($fields)->order('_id desc')->select();
    }

    /**
     * 根据条件获取账号列表信息
     * @param  array  $where 查询条件
     * @param  number $start 开始数
     * @param  number $limit 每页条数
     * @return array list
     */
    public function getUserListByParam($where, $start, $limit)
    {
        $list = D('RiskListUser')->field(['_id' => 0])
                                 ->where($where)
                                 ->limit($start, $limit)
                                 ->order('_id desc')
                                 ->select();
        $account_info = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($list, 'id'), 6);
        array_walk($list, function(&$v, $k, $p) {
            $v['account_id'] = isset($p[$v['id']]['account_id']) ? $p[$v['id']]['account_id'] : '';
            $v['account_name'] = isset($p[$v['id']]['account_name']) ? $p[$v['id']]['account_name'] : '';
        }, $account_info);
        return $list;
    }

    /**
     * 根据客户ID获取账号
     * @param  number $account_id 客户ID
     * @return array
     */
    public function getProductIdsByAccount($account_id)
    {
        $type_id = 6;
        $account_info = D('FinanceAccountProduct')->where(compact('type_id', 'account_id'))
                                                  ->field('account_id,product_id')
                                                  ->index('product_id')
                                                  ->select();
        $ids = array_keys($account_info);
        return $ids;
    }

    /**
     * 检查账号名称是否存在
     * @param  string $name 账号名称
     * @return true/false
     */
    public function checkUserName($name, $id = '')
    {
        $where = ['developer' => $name];
        if ($id) {
            $where = array_merge($where, ['id' => ['$ne' => $id]]);
        }
        $info = D('RiskListUser')->field(['_id' => 0, 'id'])
                                 ->where($where)
                                 ->find();
        if (!$info) {
            return false;
        }
        return true;
    }

    /**
     * 根据条件获取风险名单数量
     * @param  array  $where 条件查询
     * @return number
     */
    public function getRistListNum($where = [])
    {
        return D('RiskListUser')->where($where)->count();
    }

    /**
     * 列表查询条件
     * @return array
     */
    public function getRistListParam()
    {
        $status = I('get.status', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $user_id = I('get.id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $account_id = I('get.account_id', '', 'trim');

        $where = $this->getRistListTimeParam();
        if ($status) {
            $where['status'] = new \MongoInt32($status);
        }
        if ($apikey) {
            $where['apikey'] = $apikey;
        }

        //  输入的id冲突，拿不到数据
        if ($user_id && $choose_id && $choose_id != $user_id) {
            $where['id'] = 'what happened';
        } elseif ($user_id) {
            $where['id'] = new \MongoInt32($user_id);
        } elseif ($choose_id) {
            $where['id'] = new \MongoInt32($choose_id);
        }

        if ($account_id) {
            $ids = $this->getProductIdsByAccount($account_id);
            if (isset($where['id']) && !in_array($where['id'], $ids)) {
                $where['id'] = 'what happened';
            } elseif (!isset($where['id'])) {
                $where['id'] = ['$in' => $ids];
            }
        }
        //检索签约状态、开通时间、到期时间
        if ($contract_status){
            $where['contract_status'] = new \MongoInt32($contract_status);
        }
        return $where;
    }

    /**
     * 时间查新条件
     * @return array
     */
    protected function getRistListTimeParam()
    {
        $begin = I('get.begin', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        $where = [];
        if ($begin && $begin_e) {
            $where['created_at'] = ['between', [new \MongoInt32(strtotime($begin)), new \MongoInt32(strtotime($begin_e.' 23:59:59'))]];
        } elseif($begin) {
            $where['created_at'] = ['egt', new \MongoInt32(strtotime($begin))];
        } elseif($begin_e) {
            $where['created_at'] = ['elt', new \MongoInt32(strtotime($begin_e.' 23:59:59'))];
        }
        if ($end && $end_e) {
            $where['validuntil'] = ['between', [new \MongoInt32(strtotime($end)), new \MongoInt32(strtotime($end_e.' 23:59:59'))]];
        } elseif($end) {
            $where['validuntil'] = ['egt', new \MongoInt32(strtotime($end))];
        } elseif ($end_e) {
            $where['validuntil'] = ['elt', new \MongoInt32(strtotime($end_e.' 23:59:59'))];
        }

        return $where;
    }

    /**
     * 参数过滤
     * @param  array $param 参数
     * @return array
     */
    public function filterInfo($param, $id = '')
    {
        //验证用户名
        $param = $this->filterDeveloper($param, $id);
        //验证IP
        $param = $this->filterLimitIp($param);
        //验证限额
        $param = $this->filterLimitNum($param);

        return $param;

    }

    /**
     * 验证用户名
     * @param  array $param 参数
     * @return array
     */
    protected function filterDeveloper($param, $id)
    {
        $param['validuntil'] = isset($param['validuntil']) ? new \MongoInt32(strtotime($param['validuntil'])) : 0;
        $param['contract_status'] = isset($param['contract_status']) ? new \MongoInt32($param['contract_status']) : 3;
        $param['status'] = isset($param['status']) ? new \MongoInt32($param['status']) : 1;
        $param['developer'] = isset($param['developer']) ? trim($param['developer']) : '';
        if (!$param['developer']) {
            throw new \Exception('请输入风险名单账号名称');
        }
        $check = $this->checkUserName($param['developer'], $id);
        if ($check) {
            throw new \Exception('风险名单账号名称不可重复');
        }
        return $param;
    }

    /**
     * 限额验证
     * @param  array $param [description]
     * @return [type]        [description]
     */
    protected function filterLimitNum($param)
    {
        $limit_type = isset($param['limit_type']) ? (int)$param['limit_type'] : 1;
        unset($param['limit_type']);
        $limit_num = isset($param['limit_num']) ? (int)$param['limit_num'] : 0;
        unset($param['limit_num']);
        if (($limit_type != -1) && !$limit_num) {
            throw new \Exception('限额数量不能为空');
        }
        $limit_num = new \MongoInt32($limit_num);
        switch ($limit_type) {
            case 1://日限额
                $param['daily_limit'] = $limit_num;
                break;
            case 2://月限额
                $param['monthly_limit'] = $limit_num;
                break;
            case 3://年限额
                $param['yearly_limit'] = $limit_num;
                break;
            case -1://不限额
                $num = new \MongoInt32(-1);
                $param['daily_limit'] = $num;
                $param['monthly_limit'] = $num;
                $param['yearly_limit'] = $num;
                break;
            default://
                break;
        }
        return $param;
    }

    /**
     * 检测白名单
     * @param  array $data
     * @return array
     */
    protected function filterLimitIp($data)
    {
        // check limit_access_ip
        if (isset($data['access_ip']) && $data['access_ip']) {
            $ip_list = explode(PHP_EOL, $data['access_ip']);
            // check ip one by one
            $ip_list = array_map(function ($ip_str) {
                $ip_str = trim($ip_str, " \t\n\r\0\x0B ");
                // normal ip
                $ip_validate_result = filter_var($ip_str, FILTER_VALIDATE_IP);
                if ($ip_validate_result) {
                    return $ip_str;
                }
                // match ip eg: 172.18.19.x
                $regex_ip = '/\b((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|$)){3}x$/';
                $ip_reg_result = preg_match($regex_ip, $ip_str, $matches);
                if ($ip_reg_result) {
                    return $ip_str;
                }
                throw new \Exception('IP白名单输入不合法');
            }, $ip_list);
            $data['access_ip'] = $ip_list;
        } else {
            $data['access_ip'] = '';
        }
        return $data;
    }
}
