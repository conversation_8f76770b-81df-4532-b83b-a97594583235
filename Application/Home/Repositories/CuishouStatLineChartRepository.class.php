<?php

namespace Home\Repositories;

use Common\Model\CuishouUserModel;
use Think\Cache;
use Common\Model\CuishouDailyStatModel;

class CuishouStatLineChartRepository
{
    /*
    * 展示之前，一个客户必然包含的单元
    * */
    protected $item_base = [
        'converage_dunning' => 0.000,
        'converage_not_sure_dunning' => 0.000,
        'converage_all' => 0.000,
        'success_counts' => 0,
        'access_counts' => 0,
        'dunning_times' => 0,
        'not_sure_dunning_times' => 0,
        'not_times' => 0,
    ];

    /**
     * 为折线图列表数据按照这段时间的调用总量，进行倒序排列
     * @param array $list_stat
     * @return array
     */
    public function sortDescByTotalInvokingForList($list_stat)
    {
        // 这段时间各个产品的总调用量
        $list_invoking = $this->getTotalInvoking();

        // 产品列表
        $list_product = $this->getListForCuishouUser();

        // 合并调用量和产品
        $list_product = $this->mergeProductAndInvoking($list_invoking, $list_product);


        // 按照总量排序
        return $this->sortProduct($list_product, $list_stat);

    }

    /**
     * 产品排序
     * @param array $list_product
     * @param array $list_stat
     * @return array
     */
    protected function sortProduct($list_product, $list_stat)
    {
        // 只保留折线图展示的产品
        $list_product = $this->tidyProductBaseOnLineChart($list_product, $list_stat);

        // 排序
        return $this->sortByAccessCount($list_product, $list_stat);
    }

    /**
     * 根据总调用量排序
     * @param $list_product
     * @param $list_stat
     * @return array
     */
    protected function sortByAccessCount($list_product, $list_stat)
    {
        // 容器（存放覆盖率）
        $converage_all = $list_stat['info_series']['converage_all'];
        $converage_dunning = $list_stat['info_series']['converage_dunning'];
        $converage_not_sure_dunning = $list_stat['info_series']['converage_not_sure_dunning'];
        $coverage_both = $list_stat['info_series']['coverage_both'];

        $date_range = $list_stat['date_range'];
        $user_range = $list_stat['user_range'];

        // 排序依据的单元
        $list_sort_item = array_map(function ($item) {
            return $item['access_counts'];
        }, $list_product);

        array_multisort($list_sort_item, SORT_DESC, SORT_NUMERIC, $user_range,
            $list_product, $converage_all, $converage_dunning, $converage_not_sure_dunning, $coverage_both);
        $info_series = compact('converage_not_sure_dunning', 'converage_dunning', 'converage_all', 'coverage_both');

        // 返回的内容加入list_product && list_sort_item 去验证数据的正确性
        return compact('info_series', 'date_range', 'user_range', 'list_sort_item', 'list_product');
    }


    /**
     * 将产品安装折线图的需要整合(只保留折线图展示的产品)
     * @param array $list_product
     * @param array $list_stat
     * @return array
     */
    protected function tidyProductBaseOnLineChart($list_product, $list_stat)
    {
        $user_range = $list_stat['user_range'];
        $list_product = array_column($list_product, null, 'developer');

        // 折线图展示的产品 && 折线图产品的总计
        $list_product_temp = [];
        $total_number = 0;
        array_walk($user_range, function ($developer) use (&$list_product_temp, &$total_number, $list_product) {
            if (trim($developer) !== '总计') {
                $total_number += isset($list_product[$developer]['access_counts']) ? $list_product[$developer]['access_counts'] : 0;
                $list_product_temp[] = $list_product[$developer];
            }
        });

        // 加入总计
        array_unshift($list_product_temp, ['developer' => '总计', 'access_counts' => $total_number]);
        return $list_product_temp;
    }

    /**
     * 获取催收用户的列表
     */
    protected function getListForCuishouUser()
    {
        $field = [
            '_id' => 0,
            'id' => 1,
            'developer' => 1
        ];
        return (new CuishouUserModel())
            ->field($field)
            ->select();
    }

    /**
     * 合并产品和产品调用量
     * @param $list_invoking
     * @param $list_product
     * @return array
     */
    protected function mergeProductAndInvoking($list_invoking, $list_product)
    {
        $list_invoking = array_column($list_invoking, null, '_id');

        // 总调用量的总计
        return array_map(function ($item_user) use ($list_invoking) {
            $id = $item_user['id'];

            $item_user['access_counts'] = array_key_exists($id, $list_invoking) ? $list_invoking[$id]['access_counts'] : 0;
            return $item_user;
        }, $list_product);
    }

    /**
     * 获取各个产品的调用总量
     * @return array
     */
    protected function getTotalInvoking()
    {
        // 条件
        $where = $this->genConditionForListTotal();

        return (new CuishouDailyStatModel())->accessCountForElement($where);
    }


    /**
     * 条件
     * @return array
     */
    protected function genConditionForListTotal()
    {
        // 时间限制
        return $this->limitTime();
    }

    /**
     * 时间限制
     * @return array
     */
    protected function limitTime()
    {
        // 默认限制
        $begin = I('get.begin', '', 'trim');
        if ($begin == '') {
            return ['time' => date('Ymd')];
        }

        // 时间限制
        $end = I('get.end', '', 'trim');

        return [
            'time' => [
                '$gte' => date('Ymd', strtotime($begin)),
                '$lte' => date('Ymd', strtotime($end))
            ]
        ];
    }


    /**
     * 为详情页生成折线图的数据
     * @param $stat_data
     * @return array
     */
    public function lineInfoForDetail($stat_data)
    {
        // x轴依靠时间分类
        $date_line_chart = $this->showDateForDetailAsc();

        // 数据列(name && data)
        $date_stat_show = $stat_data['date_stat_show'];

        // 容器(存放覆盖率)
        $converage_dunning = [];
        $converage_not_sure_dunning = [];
        $converage_all = [];
        $coverage_both = [];
        array_walk($date_line_chart, function ($item_date) use (&$converage_dunning, &$converage_all, &$converage_not_sure_dunning, &$coverage_both, $date_stat_show) {
            $converage_dunning[] = isset($date_stat_show[$item_date]['converage_dunning']) ? $date_stat_show[$item_date]['converage_dunning'] : 0.000;
            $converage_not_sure_dunning[] = isset($date_stat_show[$item_date]['converage_not_sure_dunning']) ? $date_stat_show[$item_date]['converage_not_sure_dunning'] : 0.000;
            $converage_all[] = isset($date_stat_show[$item_date]['converage_all']) ? $date_stat_show[$item_date]['converage_all'] : 0.000;
            $coverage_both[] = isset($date_stat_show[$item_date]['coverage_both']) ? $date_stat_show[$item_date]['coverage_both'] : 0.000;
        });

        $series = [
            ['name' => '催收覆盖率', 'data' => $converage_dunning],
            ['name' => '疑似催收覆盖率', 'data' => $converage_not_sure_dunning],
            ['name' => '同时覆盖率', 'data' => $coverage_both],
            ['name' => '整体催收覆盖率', 'data' => $converage_all],
        ];

        return array_merge($stat_data, compact('series', 'date_line_chart'));
    }

    /**
     * 总量覆盖率和各个用户的覆盖率合并, 用来展示用的
     * @param array $info_total 总量
     * @param array $info_series 单个用户的合计
     * @return array
     */
    public function mergeTotalAndSingleCoverage($info_total, $info_series)
    {
        $converage_all = array_merge([$info_total['converage_all']], $info_series['converage_all']);
        $converage_not_sure_dunning = array_merge([$info_total['converage_not_sure_dunning']], $info_series['converage_not_sure_dunning']);
        $converage_dunning = array_merge([$info_total['converage_dunning']], $info_series['converage_dunning']);
        $coverage_both = array_merge([$info_total['coverage_both']], $info_series['coverage_both']);

        return compact('converage_dunning', 'converage_not_sure_dunning', 'converage_all', 'coverage_both');
    }


    /**
     * 列表的折线图的总计信息
     * @param $stat_total
     * @return mixed
     */
    public function totalInfoLineInfoForList($stat_total)
    {
        // 列表计算总计的覆盖率
        $coverage_total = $this->totalCoverageInfoByDateForList($stat_total);

        //将列表总计的覆盖率整理成折线图的格式
        return $this->totalCoverageFormatForList($coverage_total);
    }

    /**
     * 将列表总计的覆盖率整理成折线图的格式
     * @param $coverage_total
     * @return array
     */
    protected function totalCoverageFormatForList($coverage_total)
    {
        $converage_dunning_list = [];
        $converage_not_sure_dunning_list = [];
        $converage_all_list = [];
        $coverage_both_list = [];
        $name = '总计';

        array_walk($coverage_total, function($item_coverage) use(&$coverage_both_list, &$converage_dunning_list, &$converage_all_list, &$converage_not_sure_dunning, $name){
            array_push($converage_dunning_list, $item_coverage['converage_dunning']);
            array_push($converage_not_sure_dunning_list, $item_coverage['converage_not_sure_dunning']);
            array_push($converage_all_list, $item_coverage['converage_all']);
            array_push($coverage_both_list, $item_coverage['coverage_both']);
        });

        // 拼装成[ [name => $name, data=> $data]]格式
        $data = $converage_dunning_list;
        $converage_dunning = compact('name', 'data');
        $data = $converage_not_sure_dunning_list;
        $converage_not_sure_dunning = compact('name', 'data');
        $data = $converage_all_list;
        $converage_all = compact('name', 'data');
        $data = $coverage_both_list;
        $coverage_both = compact('name', 'data');

        return compact('converage_all', 'converage_not_sure_dunning', 'converage_dunning', 'coverage_both');
    }

    /**
     * 列表计算总计的覆盖率
     * @param $stat_total
     * @return mixed
     */
    protected function totalCoverageInfoByDateForList($stat_total)
    {
        foreach ($stat_total as $date => &$item_stat) {
            $item_stat = array_merge($this->item_base, $item_stat);
            $item_stat = $this->computeCoverage($item_stat);
        }
        return $stat_total;
    }

    /**
     * 计算覆盖率
     * @param array $item 计算覆盖率的单元
     * @return array
     */
    protected function computeCoverage($item)
    {
        // 有效调用量为0的时候,覆盖率为0.000
        if (isset($item['success_counts']) && $item['success_counts']) {
            $item['converage_dunning'] = 100 * round($item['dunning_times'] / $item['success_counts'], 3);
            $item['converage_not_sure_dunning'] = 100 * round($item['not_sure_dunning_times'] / $item['success_counts'], 3);
            $item['converage_all'] = 100 * (1.000 - round($item['not_times'] / $item['success_counts'], 3));
            $item['coverage_both'] = 100 * round($item['both_times'] / $item['success_counts'], 3);
        }
        return $item;
    }


    /**
     * 为折线图生成的user列表
     * @param $user_list
     * @return array
     */
    public function userRangeForLegend($user_list)
    {
        $user_range = array_column($user_list, 'developer', 'developer');
        return array_values($user_range);
    }

    /**
     * 为折线图整理数据
     * @param array $date_info 以天为单元的统计的覆盖率数据
     * @return array
     */
    public function tidyLineChartDataForList($date_info)
    {
        /*
         * 需要的格式
         * [{ // 数字索引
                    name:'邮件营销2', // 用户的名字
                    type:'line',
                    stack: '总量',
                    data:[120, 132, 101, 134, 90, 230, 210] // data换成'converage_dunning','converage_not_sure_dunning','converage_all',
                    // 里面的内容需要转化成，对应日期的数据
                },]
         * */

        $converage_dunning = [];
        $converage_not_sure_dunning = [];
        $converage_all = [];
        $coverage_both = [];

        array_walk($date_info, function ($info_item) use (&$converage_not_sure_dunning, &$converage_all, &$converage_dunning, &$coverage_both) {
            $converage_dunning[] = $info_item['converage_dunning'];
            $converage_not_sure_dunning[] = $info_item['converage_not_sure_dunning'];
            $converage_all[] = $info_item['converage_all'];
            $coverage_both[] = $info_item['coverage_both'];
        });

        return compact('converage_dunning', 'converage_all', 'converage_not_sure_dunning', 'coverage_both');
    }

    /**
     * 获取GET方法的Cache实列
     * @return mixed
     */
    public function getCacheInstanceForGet()
    {
        // cache instance
        $params = I('get.');
        $prefix = md5(__METHOD__ . serialize($params));
        return Cache::getInstance('File', ['expire' => '600', 'prefix' => $prefix]);
    }

    /**
     * 列表动态展示的时间范围
     * @return mixed
     */
    public function showDateForList()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        //默认一天, 前端代码确定了begin && end同时出现或者消失
        if (!$begin && !$end) {
            $begin = time() - 86400;
            $end = time();
        }
        return $this->dateRange($begin, $end);
    }

    /**
     * 正序日期范围（为催收详情的折线图定制）
     * @return array
     */
    public function showDateForDetailAsc()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        //默认一天, 前端代码确定了begin && end同时出现或者消失
        if (!$begin && !$end) {
            $begin = time() - 86400 * 30;
            $end = time();
        }
        return $this->dateRange($begin, $end);
    }


    /**
     * 详情页要展示的date list（正序）
     * @param integer $begin
     * @param integer $end
     * @return array
     */
    protected function dateRange($begin, $end)
    {
        $date_list = [];

        while ($end >= $begin) {
            $date_list[] = date('Y-m-d', $begin);
            $begin += 86400;
        }

        return $date_list;
    }

    /**
     * 需要展示的日期的列表(正序)
     * @return array
     */
    public function showDateListAsc()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        //默认三十天, 前端代码确定了begin && end同时出现或者消失
        if (!$begin && !$end) {
            $begin = time() - 86400 * 30;
            $end = time();
        }
        return $this->dateListAsc($begin, $end);
    }


    /**
     * 详情页要展示的date list,(正序)
     * @param integer $begin
     * @param integer $end
     * @return array
     */
    public function dateListAsc($begin, $end)
    {
        $date_list = [];

        while ($end >= $begin) {
            $date = date('Y-m-d', $begin);
            $date_list[$date] = 0;
            $begin += 86400;
        }

        return $date_list;
    }
}
