<?php

namespace Home\Repositories;

use Common\Model\CuishouDailyStatModel;
use Common\Model\CuishouUserModel;
use Common\Model\FinanceAccountProductModel;
use Common\Model\FinanceAccountsModel;

class AccountCuishouChartRepository extends BaseRepository
{

    /*
     * 统计元素的单元
     * */
    private $item_source = [
        'access_counts' => 0,
        'success_counts' => 0,
        'dunning_times' => 0,
        'not_sure_dunning_times' => 0,
        'not_times' => 0,
        'both_times' => 0,
    ];

    /*
     * 总计的统计的单元
     * */
    private $total_stat = [];

    /**
     * 获取覆盖率列表
     * @throws \Exception
     * @return array
     */
    public function getCoverageList()
    {
        // 选定的客户
        $list_account = $this->getCoverageAccount();

        // 客户追加产品限定
        $list_account = $this->appendProductLimit($list_account);

        // 客户追加统计信息
        $list_account = $this->appendStatInfo($list_account);

        // 计算总计的数据
        $this->computeTotalStat($list_account);

        // 追加总计到客户列表
        $list_account = $this->appendTotalToAccount($list_account);

        // 客户列表排序
        $list_account = $this->sortDescForAccount($list_account);

        // 客户按照highcharts需要的方式,组装数据
        $list_coverage = $this->tidyDataForHighCharts($list_account);

        return compact('list_account', 'list_coverage');
    }

    /**
     * 为折线图默认页生成默认的参数
     */
    public function tidyInitParamsForDefaultPage()
    {
        $params = I('get.');

        // 时间限制
        return $this->tidyParamsForTime($params);
    }

    /**
     * 时间初始化
     * @param array $params GET参数
     * @return array
     */
    protected function tidyParamsForTime($params)
    {
        if (!isset($params['begin']) || !trim($params['begin'])) {
            $params['begin'] = date('Y-m-d');
        }

        if (!isset($params['end']) || !trim($params['end'])) {
            $params['end'] = date('Y-m-d');
        }

        return $params;
    }


    /**
     * 为highCharts整理数据
     * @param array $list_account 客户列表
     * @return array
     */
    protected function tidyDataForHighCharts($list_account)
    {
        /*
         *  [{name: '安装，实施人员',data: [43934, 52503, 57177, 69658, 97031, 119931, 137133, 154175]}]
         * */

        // 趋势图需要展示的时间范围
        $date_range = $this->dateRange();

        // 放置覆盖率的容器
        $coverage_dunning = [];
        $coverage_both = [];
        $coverage_not_sure = [];
        $coverage_all = [];

        array_walk($list_account, function ($item_account) use (&$coverage_all, &$coverage_not_sure, &$coverage_both, &$coverage_dunning, $date_range) {
            $name = $item_account['name'];

            // 放置data的容器
            $data_coverage_all = [];
            $data_coverage_not_sure = [];
            $data_coverage_both = [];
            $data_coverage_dunning = [];
            array_walk($date_range, function ($date) use ($item_account, &$data_coverage_all, &$data_coverage_not_sure, &$data_coverage_both, &$data_coverage_dunning) {
                // 获取值
                $item_coverage_dunning = isset($item_account['list_coverage'][$date]['coverage_dunning']) ? $item_account['list_coverage'][$date]['coverage_dunning'] : 0;
                $item_coverage_not_sure = isset($item_account['list_coverage'][$date]['coverage_not_sure_dunning']) ? $item_account['list_coverage'][$date]['coverage_not_sure_dunning'] : 0;
                $item_coverage_both = isset($item_account['list_coverage'][$date]['coverage_both']) ? $item_account['list_coverage'][$date]['coverage_both'] : 0;
                $item_coverage_all = isset($item_account['list_coverage'][$date]['coverage_all']) ? $item_account['list_coverage'][$date]['coverage_all'] : 0;
                // 组装
                array_push($data_coverage_all, $item_coverage_all);
                array_push($data_coverage_both, $item_coverage_both);
                array_push($data_coverage_dunning, $item_coverage_dunning);
                array_push($data_coverage_not_sure, $item_coverage_not_sure);
            });

            // 赋值
            $data = $data_coverage_dunning;
            array_push($coverage_dunning, compact('data', 'name'));
            $data = $data_coverage_not_sure;
            array_push($coverage_not_sure, compact('data', 'name'));
            $data = $data_coverage_both;
            array_push($coverage_both, compact('data', 'name'));
            $data = $data_coverage_all;
            array_push($coverage_all, compact('data', 'name'));
        });

        return compact('coverage_all', 'coverage_both', 'coverage_not_sure', 'coverage_dunning', 'date_range');
    }

    /**
     * 按照成功调用量倒序排列
     * @param array $list_account
     * @return array
     */
    protected function sortDescForAccount($list_account)
    {
        $list_item = array_map(function ($item) {
            return $item['success_counts'];
        }, $list_account);

        array_multisort($list_item, SORT_DESC, SORT_NUMERIC, $list_account);
        return $list_account;
    }


    /**
     * 将总计作为一个客户追加到客户列表
     * @param array $list_account
     * @return array
     */
    protected function appendTotalToAccount($list_account)
    {
        array_unshift($list_account, $this->total_stat);
        return $list_account;
    }

    /**
     * 计算总计的数据
     * @param $list_account
     */
    private function computeTotalStat($list_account)
    {
        // 初始化总计的数据
        $this->initTotalStat($list_account);

        // 计算率
        $this->appendCoverageForTotal();
    }

    /**
     * 给总计增加各种率
     */
    protected function appendCoverageForTotal()
    {
        $this->total_stat['list_coverage'] = array_map(function ($item) {
            return $this->computeCoverage($item);
        }, $this->total_stat['list_coverage']);
    }


    /**
     * 初始化总计的数据
     * @param $list_account
     */
    protected function initTotalStat($list_account)
    {
        // 求和
        $this->total_stat['success_counts'] = 0;
        $this->total_stat['name'] = '总计';
        array_walk($list_account, function ($item_account) {
            array_walk($item_account['list_coverage'], function ($item, $date) {
                // 外层的累加 用于排序
                $this->total_stat['success_counts'] += $this->getValueWithDefault($item, 'success_counts', 0);

                // 如果这一天的数据,还没有插入 则重置为当前值
                if (!isset($this->total_stat['list_coverage'][$date])) {
                    $this->total_stat['list_coverage'][$date] = $item;
                    return true;
                }

                // 每天的数据累加
                $this->total_stat['list_coverage'][$date]['access_counts'] += $this->getValueWithDefault($item, 'access_counts', 0);
                $this->total_stat['list_coverage'][$date]['success_counts'] += $this->getValueWithDefault($item, 'success_counts', 0);
                $this->total_stat['list_coverage'][$date]['dunning_times'] += $this->getValueWithDefault($item, 'dunning_times', 0);
                $this->total_stat['list_coverage'][$date]['not_sure_dunning_times'] += $this->getValueWithDefault($item, 'not_sure_dunning_times', 0);
                $this->total_stat['list_coverage'][$date]['not_times'] += $this->getValueWithDefault($item, 'not_times', 0);
                $this->total_stat['list_coverage'][$date]['both_times'] += $this->getValueWithDefault($item, 'both_times', 0);
            });
        });
    }


    /**
     * 趋势图需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        $day_begin = date('Ymd', strtotime($request_body['begin']));
        $day_end = date('Ymd', strtotime($request_body['end']));

        $date_range = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            array_push($date_range, $day_begin);
            $day_begin = date('Ymd', strtotime('+1 day', strtotime($day_begin)));
        }

        return $date_range;
    }

    /**
     * 追加各种率
     * @param array $list_stat
     * @return array
     */
    private function appendCoverageToAccount($list_stat)
    {
        $list_coverage = [];
        array_walk($list_stat, function ($item, $date) use (&$list_coverage) {
            $item_coverage = $this->computeCoverage($item);
            $list_coverage[$date] = $item_coverage;
        });

        return $list_coverage;
    }

    /**
     * 计算覆盖率
     * @param array $item 计算覆盖率的单元
     * @return array
     */
    protected function computeCoverage($item)
    {
        // 有效调用量为0的时候,覆盖率为0.00
        if (isset($item['success_counts']) && $item['success_counts']) {
            $item['coverage_dunning'] = 100 * round($item['dunning_times'] / $item['success_counts'], 3);
            $item['coverage_not_sure_dunning'] = 100 * round($item['not_sure_dunning_times'] / $item['success_counts'], 3);
            $item['coverage_both'] = 100 * round($item['both_times'] / $item['success_counts'], 3);
            $item['coverage_all'] = 100 - 100 * round($item['not_times'] / $item['success_counts'], 3);
        } elseif (isset($item['success_counts']) && $item['success_counts'] == 0) {
            $item['coverage_dunning'] = 'NA';
            $item['coverage_not_sure_dunning'] = 'NA';
            $item['coverage_all'] = 'NA';
            $item['coverage_both'] = 'NA';
        } else {
            $item['coverage_dunning'] = 0;
            $item['coverage_not_sure_dunning'] = 0;
            $item['coverage_all'] = 0;
            $item['coverage_both'] = 0;
        }
        return $item;
    }

    /**
     * 追加统计信息
     * @param $list_account
     * @return array
     */
    protected function appendStatInfo($list_account)
    {
        // 时间限制
        $limit_time = $this->limitTime();
        return array_map(function ($item_account) use ($limit_time) {
            // 获取的统计信息
            $list_stat = $this->getStatInfoForAccount($item_account, $limit_time);

            // 追加各种率
            $item_account['list_coverage'] = $this->appendCoverageToAccount($list_stat);

            // 追加统计各个客户的有效调用量
            $item_account['success_counts'] = $this->sumSuccessCountForAccount($list_stat);

            return $item_account;
        }, $list_account);
    }

    /**
     *
     * @param array $list_stat (以天为单位的基本元素统计)
     * @return integer
     */
    protected function sumSuccessCountForAccount($list_stat)
    {
        $success_counts = 0;
        array_walk($list_stat, function ($item) use (&$success_counts) {
            $success_counts += $item['success_counts'];
        });
        return $success_counts;
    }

    /**
     * 获取统计信息
     * @param array $item_account
     * @param array $limit_time
     * @return array
     */
    protected function getStatInfoForAccount($item_account, $limit_time)
    {
        // 总计的容器（key=>Ymd）
        $list_stat = [];
        array_walk($item_account['product_ids'], function ($product_id) use ($limit_time, &$list_stat) {
            $daily_stat = $this->statInfo($limit_time, $product_id);
            // 累加(以天为单位)
            $list_stat = $this->sumStatByDaily($list_stat, $daily_stat);
        });
        return $list_stat;
    }

    /**
     * 将每天的统计数据累加到总计里面
     * @param  array $list_stat 总计的容器
     * @param array $daily_stat
     * @return array
     * @throws \Exception
     */
    protected function sumStatByDaily($list_stat, $daily_stat)
    {
        array_walk($daily_stat, function ($item) use (&$list_stat) {
            $day = $item['_id'];

            // 如果这一天不存在 则直接赋值
            if (!array_key_exists($day, $list_stat)) {
                $list_stat[$day] = $item;
                return true;
            }

            // 如果这一天已经存在 则累加
            $list_stat[$day]['access_counts'] += $this->getValueWithDefault($item, 'access_counts', 0);
            $list_stat[$day]['success_counts'] += $this->getValueWithDefault($item, 'success_counts', 0);
            $list_stat[$day]['dunning_times'] += $this->getValueWithDefault($item, 'dunning_times', 0);
            $list_stat[$day]['not_sure_dunning_times'] += $this->getValueWithDefault($item, 'not_sure_dunning_times', 0);
            $list_stat[$day]['not_times'] += $this->getValueWithDefault($item, 'not_times', 0);
            $list_stat[$day]['both_times'] += $this->getValueWithDefault($item, 'both_times', 0);
        });

        return $list_stat;
    }

    /**
     * 获取特定产品这些天的统计信息
     * @param array $limit_time
     * @param integer $uid
     * @return array
     */
    protected function statInfo($limit_time, $uid)
    {
        $limit_id = ['uid' => new \MongoInt32($uid)];
        $where = array_merge($limit_time, $limit_id);
        return (new CuishouDailyStatModel())->accessCount($where, '$time');
    }

    /**
     * 时间限制
     * @return array
     */
    protected function limitTime()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        $time = [
            '$gte' => date('Ymd', strtotime($request_body['begin'])),
            '$lte' => date('Ymd', strtotime($request_body['end']))
        ];
        return compact('time');
    }

    /**
     * 客户追加产品限定
     * @param $list_account
     * @return
     */
    protected function appendProductLimit($list_account)
    {
        // 催收分总的可用产品ID
        $list_ids_product = $this->getListNormalIdsOfProduct();

        // 各个客户限定催收分产品
        return array_map(function ($item_account) use ($list_ids_product) {
            $item_account['product_ids'] = $this->getProductIdsOfAccount($item_account, $list_ids_product);
            return $item_account;
        }, $list_account);
    }

    /**
     * 为了给客户追加产品ID生成条件现制
     * @return array
     */
    protected function genConditionForProduct()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        $where = [];
        if (array_key_exists('type', $request_body)) {
            $where = [ 'type' => $request_body['type']];
        }
        return $where;
    }

    /**
     * 获取特定的客户下辖可用的产品ID列表
     * @param array $account 选定的产品
     * @param array $list_ids_product 可用产品ID列表
     * @return array
     */
    protected function getProductIdsOfAccount($account, $list_ids_product)
    {
        // 当前客户下辖的催收账户id
        $account_id = $account['id'];
        $type_id = 3;
        $list_relation = $this->getRelationByCondition(compact('account_id', 'type_id'));
        $list_relation = array_column($list_relation, null, 'product_id');
        $list_ids = array_keys($list_relation);

        // 与所有可用产品ID求交集
        return array_intersect($list_ids, $list_ids_product);
    }

    /**
     * 根据条件获取客户和产品之间的关系
     * @param $where
     * @return array
     */
    protected function getRelationByCondition($where)
    {
        return (new FinanceAccountProductModel())->where($where)
            ->select();
    }

    /**
     * 客户列表
     * @return array
     * @throws \Exception
     */
    protected function getCoverageAccount()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 如果限定了特定客户
        if ($request_body['id']) {
            // 条件
            $where = $this->genConditionForSpecial();
            return $this->getAccountByCondition($where);
        }

        // 没有指定客户的时候
        $where = $this->genCondition();
        return $this->getAccountByCondition($where);
    }

    /**
     * 没有指定特定客户的时候条件
     * @throws \Exception
     * @return array
     */
    protected function genCondition()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 可用催收分产品的ID列表
        $list_ids_product = $this->getListNormalIdsOfProduct();

        // 至少有一个可用的催收分账户的客户ID列表
        $list_id = $this->getNormalIdsAccount($list_ids_product);
        $id = [
            'in', $list_id
        ];

        // 限制类型
        $type = $this->getValueWithDefault($request_body, 'type', '');
        if ($type) {
            return compact('id', 'type');
        } else {
            return compact('id');
        }
    }

    /**
     * 指定了特定客户时候的条件
     * @throws \Exception
     */
    protected function genConditionForSpecial()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        $id = $request_body['id'];
        $type = $this->getValueWithDefault($request_body, 'type', '');
        if ($type) {
            return compact('id', 'type');
        } else {
            return compact('id');
        }
    }


    /**
     * 获取默认的客户列表
     * @return array
     */
    public function getDefaultListAccount()
    {
        // 条件
        $where = $this->genConditionForDefault();

        // 获得客户列表
        return $this->getAccountByCondition($where);
    }

    /**
     * 根据特定条件获取客户列表
     * @param $where
     * @return array
     */
    protected function getAccountByCondition($where)
    {
        return (new FinanceAccountsModel())->where($where)
            ->select();
    }

    /**
     * 趋势图展示的默认条件
     * @return array
     */
    protected function genConditionForDefault()
    {
        // 限制(至少有一个可用的催收分账户的客户)
        $limit_id = $this->limitProductForAccount();

        // 限制状态码
        $limit_status = [
            'status' => 1
        ];

        return array_merge($limit_id, $limit_status);
    }

    /**
     * 限定（至少有一个可用的催收分账户的客户）
     * @return array
     */
    protected function limitProductForAccount()
    {
        // 可用催收分产品的ID列表
        $list_ids_product = $this->getListNormalIdsOfProduct();

        // 至少有一个可用的催收分账户的客户ID列表
        $list_account_ids = $this->getNormalIdsAccount($list_ids_product);

        $id = [
            'in', $list_account_ids
        ];

        return compact('id');
    }

    /**
     * 拥有一个可用催收分产品的客户ID列表
     * @param array $list_ids_product
     * @return array
     */
    protected function getNormalIdsAccount($list_ids_product)
    {
        // 条件
        $where = $this->genConditionForNormalAccount($list_ids_product);

        // 账户列表
        $list_account = (new FinanceAccountProductModel())->where($where)
            ->index('account_id')
            ->select();

        return array_keys($list_account);
    }


    /**
     * 获取用户一个可用催收分产品的客户的条件
     * @param array $list_ids_product id 列表
     * @return array
     */
    protected function genConditionForNormalAccount($list_ids_product)
    {
        if (!$list_ids_product) {
            return ['id' => '永远不会存在的数值'];
        }

        $type_id = 3;
        $product_id = [
            'in', $list_ids_product
        ];

        return compact('type_id', 'product_id');
    }


    /**
     * 可用催收分产品的ID
     * @return array
     */
    protected function getListNormalIdsOfProduct()
    {
        $status = new \MongoInt32(1);
        $field = [
            '_id' => 0,
            'id' => 1,
            'status' => 1
        ];

        // 可用的产品
        $list_product = $this->getProductByCondition(compact('status'), $field);

        // 返回
        return array_map(function ($item) {
            return $item['id'];
        }, $list_product);
    }

    /**
     * 根据条件获取产品
     * @param array $where
     * @param array $field
     * @return mixed
     */
    protected function getProductByCondition($where, $field = ['_id' => 0])
    {
        return (new CuishouUserModel())->where($where)
            ->field($field)
            ->select();
    }


}