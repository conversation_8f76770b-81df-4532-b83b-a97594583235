<?php

namespace Home\Repositories;

class BangProductStatRepository extends BaseRepository
{
    /*
     * 数值型字段
     * */
    protected $list_field_number = [
        'total_num', 'valid_name_or_address_num', 'valid_name_total',
        'valid_address_total', 'tel_right', 'name_right', 'tel_wrong', 'name_wrong',
        'valid_num', 'valid_address_num', 'name_dianhua_num', 'name_dianhua_no_phone_num',
        'name_tianyancha_num', 'name_yscredit_num', 'name_ysfuzzycredit_num', 'name_webengine_num'
    ];

    /*
     * 比率的字段
     * */
    protected $list_field_rate = [
        'name_cover_chance', 'address_cover_chance', 'name_address_cover_chance',
        'name_is_same_chance', 'name_blur_num_chance', 'name_not_same_chance',
        'name_not_right_chance', 'address_is_same_chance', 'address_blur_num_chance',
        'address_not_same_chance', 'address_not_right_chance', 'name_dianhua_num_chance',
        'name_dianhua_no_phone_num_chance', 'name_tianyancha_num_chance', 'name_yscredit_num_chance',
        'name_ysfuzzycredit_num_chance', 'name_webengine_num_chance'
    ];

    /*
     * 数据统计的基本单元
     * */
    protected $item_base = [
        "total_num" => 0,
        "valid_num" => 0,
        "valid_address_num" => 0,
        "valid_name_total" => 0,
        "valid_address_total" => 0,
        "valid_name_or_address_num" => 0,
        "tel_right" => 0,
        "tel_wrong" => 0,
        "name_right" => 0,
        "name_wrong" => 0,
        "name_not_right" => 0,
        "name_not_same" => 0,
        "address_not_right" => 0,
        "address_not_same" => 0,
        "name_blur_num" => 0,
        "address_blur_num" => 0,
        "name_is_same" => 0,
        "address_is_same" => 0,
        "name_address_not_right" => 0,
        "name_not_right_chance" => 0,
        "name_cover_chance" => 0,
        "address_not_right_chance" => 0,
        "address_cover_chance" => 0,
        "name_address_cover_chance" => 0,
        "name_is_same_chance" => 0,
        "name_not_same_chance" => 0,
        "name_blur_num_chance" => 0,
        "address_not_same_chance" => 0,
        "address_blur_num_chance" => 0,
        "address_is_same_chance" => 0,
        "name_dianhua_num" => 0,
        "name_dianhua_num_chance" => 0,
        "name_yscredit_num" => 0,
        "name_yscredit_num_chance" => 0,
        "name_tianyancha_num" => 0,
        "name_tianyancha_num_chance" => 0,
        "name_dianhua_no_phone_num" => 0,
        "name_dianhua_no_phone_num_chance" => 0,
        "name_amap_num" => 0,
        "name_amap_num_chance" => 0,
        "name_ysfuzzycredit_num" => 0,
        "name_ysfuzzycredit_num_chance" => 0,
        "name_webengine_num" => 0,
        "name_webengine_num_chance" => 0,
        "address_dianhua_num" => 0,
        "address_dianhua_num_chance" => 0,
        "address_yscredit_num" => 0,
        "address_yscredit_num_chance" => 0,
        "address_tianyancha_num" => 0,
        "address_tianyancha_num_chance" => 0,
        "address_dianhua_no_phone_num" => 0,
        "address_dianhua_no_phone_num_chance" => 0,
        "address_amap_num" => 0,
        "address_amap_num_chance" => 0,
        "address_ysfuzzycredit_num" => 0,
        "address_ysfuzzycredit_num_chance" => 0,
        "address_webengine_num" => 0,
        "address_webengine_num_chance" => 0,
    ];

    /**
     * 统计详情
     * @return array
     * @throws \Exception
     */
    public function getDayInfo()
    {
        // 条件
        $params = $this->genConditionForDetail();

        // 调用接口获取统计信息
        $list_stat = $this->getStatInfoForDetail($params);

        // 补全各天的数据
        return $this->appendMissingDayForDetail($list_stat);
    }

    /**
     * 为统计详情补全没有数据的那些天
     * @param array $list_stat
     * @return array
     */
    protected function appendMissingDayForDetail($list_stat)
    {
        // 补全每天的数据
        $list_day_data = $this->appendMissToDay($list_stat);

        // 总计值
        $list_total_data = $this->getTotalDataForDetail($list_stat);

        // 整理 && 合并
        return $this->mergeDayAndTotalForDetail($list_day_data, $list_total_data, $list_stat);
    }

    /**
     * 为统计详情合并每天数据和总计数据
     * @param array $list_day_data 每天数据
     * @param array $list_total_data 总计数据
     * @param array $list_source 统计接口返回数据
     * @return array
     */
    protected function mergeDayAndTotalForDetail($list_day_data, $list_total_data, $list_source)
    {
        // 合并
        array_unshift($list_day_data, $list_total_data);

        // 校验数据
        $verify_api = $this->verifyApiData($list_day_data);

        // 格式化数据
        $list_day_data = $this->formatData($list_day_data);
        return compact('list_day_data', 'list_source', 'verify_api');
    }

    /**
     * 获取详情的总计值
     * @param array $list_stat 统计值
     * @return array
     */
    protected function getTotalDataForDetail($list_stat)
    {
        // 如果没有查到数据
        if (!array_key_exists('result', $list_stat) || !array_key_exists('list_total', $list_stat['result'])) {
            return array_merge($this->item_base, ['day_time' => '总计']);
        }

        // 如果有查到的话
        return array_merge($list_stat['result']['list_total'], ['day_time' => '总计']);
    }

    /**
     * 为详情补全天数
     * @param array $list_stat 原始数据
     * @return array
     */
    protected function appendMissToDay($list_stat)
    {
        // 如果没有查到数据,则每天的返回初始化为空
        if (!array_key_exists('result', $list_stat) || !array_key_exists('list_item', $list_stat['result'])) {
            $list_day_data = [];
        } else {
            $list_day_data = array_column($list_stat['result']['list_item'], null, 'day_time');
        }

        // 需要展示的天
        $list_day_range = $this->dateRange();

        return array_map(function ($day_time) use ($list_day_data) {
            // 如果这天没有调用 则返回默认值
            if (!array_key_exists($day_time, $list_day_data)) {
                return array_merge($this->item_base, compact('day_time'));
            }

            // 如果有了调用值
            return $list_day_data[$day_time];
        }, $list_day_range);
    }

    /**
     * 需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = $this->genParamsForPost();
        $day_begin = date('Ymd', strtotime($request_body['begin']));
        $day_end = date('Ymd', strtotime($request_body['end']));

        // 容器 ['2018-10-01', '2018-10-02']
        $list_date = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            $date_format = date('Y-m-d', strtotime($day_end));
            array_push($list_date, $date_format);

            $day_end = date('Ymd', strtotime($day_end . '-1 day'));
        }

        return $list_date;
    }

    /**
     * 调用接口获取详情的统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getStatInfoForDetail($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['bang_stat_detail'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['status'] == 1000) {
                break;
            }
            $i++;
        }

        // 三次请求失败,做记录
        $this->logForFail($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 为详情统计生成条件
     * @return array
     * @throws \Exception
     */
    protected function genConditionForDetail()
    {
        // 限制 product_id
        $limit_product = $this->limitProductForDetail();

        // 时间限制
        $limit_time = $this->limitTime();

        // 整合
        $limit = array_merge($limit_product, $limit_time);

        // 附加签名信息
        return $this->appendSignToCondition($limit);
    }

    /**
     * 统计详情限制product_id
     * @return array
     * @throws \Exception
     */
    protected function limitProductForDetail()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) {
            throw new \Exception('详情统计缺少product_id');
        }
        $cid = $request_body['product_id'];
        return compact('cid');
    }

    /**
     * 统计排序
     * @return array
     * @throws \Exception
     */
    public function sortBy()
    {
        $response_body = $this->genParamsForPost();

        // 传递的数据
        $filter_field = $this->getValueWithDefault($response_body, 'filter_field', '');
        $filter_order = $this->getValueWithDefault($response_body, 'filter_order', '');
        $list_data = $this->getValueWithDefault($response_body, 'list_data', []);

        // 如果缺少必要元素 则不进行操作
        if (!$filter_field || !$filter_order) {
            return $list_data;
        }

        // 排序
        return $this->soreData($list_data, $filter_field, $filter_order);
    }

    /**
     * 数据排序
     * @param array $list_data 原始数据
     * @param string $filter_field 排序字段
     * @param string $filter_order 排序方式
     * @return mixed
     */
    protected function soreData($list_data, $filter_field, $filter_order)
    {
        // 总计不参与排序
        $item_total = array_shift($list_data);

        // 统计单元 && 统计单元格式化
        $list_item = array_column($list_data, $filter_field);
        $list_item = $this->formatDataForSort($list_item, $filter_field);

        // 排序
        $sort_order = strtolower($filter_order) === 'asc' ? SORT_ASC : SORT_DESC;
        array_multisort($list_item, $sort_order, SORT_NUMERIC, $list_data);

        // 总计不参与排序
        array_unshift($list_data, $item_total);
        return $list_data;
    }

    /**
     * 格式化统计元素(转成可以直接排序的数值型元素)
     * @param array $list_item
     * @param string $filter_field
     * @return array
     */
    protected function formatDataForSort($list_item, $filter_field)
    {
       $delimiter = in_array($filter_field, $this->list_field_number) ? ',' : '%';

       return array_map(function($item) use($delimiter){
           // 如果统计原始是需要反格式化的化
           if (strpos($item, $delimiter) !== false) {
               $item = str_replace($delimiter, '', $item);
           }
           return $item;
       }, $list_item);
    }

    /**
     * 获取列表统计信息
     * @return array
     * @throws \Exception
     */
    public function getStatData()
    {
        // 条件
        $params = $this->genConditionForList();

        $time_begin = microtime(true);
        // 调用接口获取统计信息
        $list_stat = $this->getStatInfo($params);

        // 选中的产品列表
        $list_product = $this->getProductForList();

        // 合并产品列表和统计信息
        $list_stat_tidy = $this->mergeStatAndProduct($list_product, $list_stat);

        // 校验数据
        $verify_api = $this->verifyApiData($list_stat_tidy);

        // 格式化统计数据
        $list_stat_tidy = $this->formatData($list_stat_tidy);

        $time_end = microtime(true);
        $time_diff = $time_end - $time_begin;

        return compact('time_diff', 'list_stat_tidy', 'list_stat', 'verify_api');
    }

    /**
     * 校验API数据是否ＯＫ
     * @param $list_stat_tidy
     * @return boolean
     */
    protected function verifyApiData($list_stat_tidy)
    {
        $verify_api = true;
        try {
            array_walk($list_stat_tidy, function ($item_stat) use (&$verify_api) {
                if ($verify_api === false) {
                    throw new \Exception('接口返回数据有明显问题, 查看...');
                }

                // 校验数据
                $verify_api = $this->verifyItem($item_stat);
            });
        } catch (\Exception $e) {
            return false;
        }

        return $verify_api;
    }

    /**
     * 校验API单元
     * @param array $item
     * @return boolean
     */
    protected function verifyItem($item)
    {
        try {
            array_walk($item, function ($value, $key) {
                // 数值型
                if (in_array($key, $this->list_field_number)) {
                    if ($value < 0) {
                        throw new \Exception('数值型数据为负');
                    }
                }
                if (in_array($key, $this->list_field_rate)) {
                    if ($value < 0 || $value > 1) {
                        throw new \Exception('占比数据不合法');
                    }
                }
            });
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 校验比率是否合法
     * @param float $rate 必须
     * @return bool
     */
    protected function checkRate($rate)
    {
        return $rate <= 1 && $rate >= 0;
    }

    /**
     * 格式化统计数据
     * @param array $list_stat_data
     * @return array
     */
    protected function formatData($list_stat_data)
    {
        return array_map(function ($item_data) {
            // 格式化操作
            return $this->formatItem($item_data);
        }, $list_stat_data);
    }

    /**
     * 格式化统计单元
     * @param array $item
     * @return array
     */
    protected function formatItem($item)
    {
        $item['total_num'] = $this->formatNumber($item['total_num']);
        $item['valid_name_or_address_num'] = $this->formatNumber($item['valid_name_or_address_num']);
        $item['valid_name_total'] = $this->formatNumber($item['valid_name_total']);
        $item['valid_address_total'] = $this->formatNumber($item['valid_address_total']);
        $item['tel_right'] = $this->formatNumber($item['tel_right']);
        $item['name_right'] = $this->formatNumber($item['name_right']);
        $item['tel_wrong'] = $this->formatNumber($item['tel_wrong']);
        $item['name_wrong'] = $this->formatNumber($item['name_wrong']);
        $item['valid_num'] = $this->formatNumber($item['valid_num']);
        $item['valid_address_num'] = $this->formatNumber($item['valid_address_num']);
        $item['name_cover_chance'] = $this->formatSourcePercentage($item['name_cover_chance']);
        $item['address_cover_chance'] = $this->formatSourcePercentage($item['address_cover_chance']);
        $item['name_address_cover_chance'] = $this->formatSourcePercentage($item['name_address_cover_chance']);
        $item['name_is_same_chance'] = $this->formatSourcePercentage($item['name_is_same_chance']);
        $item['name_blur_num_chance'] = $this->formatSourcePercentage($item['name_blur_num_chance']);
        $item['name_not_same_chance'] = $this->formatSourcePercentage($item['name_not_same_chance']);
        $item['name_not_right_chance'] = $this->formatSourcePercentage($item['name_not_right_chance']);
        $item['address_is_same_chance'] = $this->formatSourcePercentage($item['address_is_same_chance']);
        $item['address_blur_num_chance'] = $this->formatSourcePercentage($item['address_blur_num_chance']);
        $item['address_not_same_chance'] = $this->formatSourcePercentage($item['address_not_same_chance']);
        $item['address_not_right_chance'] = $this->formatSourcePercentage($item['address_not_right_chance']);
        $item['name_dianhua_num'] = $this->formatNumber($item['name_dianhua_num']);
        $item['name_dianhua_num_chance'] = $this->formatSourcePercentage($item['name_dianhua_num_chance']);
        $item['name_dianhua_no_phone_num'] = $this->formatNumber($item['name_dianhua_no_phone_num']);
        $item['name_dianhua_no_phone_num_chance'] = $this->formatSourcePercentage($item['name_dianhua_no_phone_num_chance']);
        $item['name_tianyancha_num'] = $this->formatNumber($item['name_tianyancha_num']);
        $item['name_yscredit_num'] = $this->formatNumber($item['name_yscredit_num']);
        $item['name_tianyancha_num_chance'] = $this->formatSourcePercentage($item['name_tianyancha_num_chance']);
        $item['name_yscredit_num_chance'] = $this->formatSourcePercentage($item['name_yscredit_num_chance']);
        $item['name_ysfuzzycredit_num'] = $this->formatNumber($item['name_ysfuzzycredit_num']);
        $item['name_ysfuzzycredit_num_chance'] = $this->formatSourcePercentage($item['name_ysfuzzycredit_num_chance']);
        $item['name_webengine_num'] = $this->formatNumber($item['name_webengine_num']);
        $item['name_webengine_num_chance'] = $this->formatSourcePercentage($item['name_webengine_num_chance']);

        return $item;
    }

    /**
     * 按天导出文件的数据
     * @throws \Exception
     */
    public function getListDay()
    {
        // 各天数据
        $list_data_day = $this->getDataGroupByDay();

        // 选中的产品列表
        $list_product = $this->getProductForList();

        // 为按天导出合并账号信息 && 统计信息
        return $this->mergeStatAndProductForDay($list_data_day, $list_product);
    }

    /**
     * 为按天导出合并账号信息 && 统计信息
     * @param array $list_data_day
     * @param array $list_product
     * @return array
     */
    protected function mergeStatAndProductForDay($list_data_day, $list_product)
    {
        // 容器
        $list_stat = [];
        $date_range = $this->dateRange();
        array_walk($list_product, function ($product) use ($list_data_day, &$list_stat, $date_range) {
            array_walk($date_range, function ($day_time) use ($product, $list_data_day, &$list_stat) {
                // 为按天导出文件整理数据
                $key_unique = $product['id'] . '-' . $day_time;
                $list_stat[$key_unique] = $this->tidyDataForDayFile($product, $day_time, $list_data_day);
            });
        });

        // 格式化统计数据
        return $this->formatData($list_stat);
    }

    /**
     * 为按天导出文件整理数据
     * @param $product
     * @param $day_time
     * @param $list_data_day
     * @return array
     */
    protected function tidyDataForDayFile($product, $day_time, $list_data_day)
    {
        $product_id = $product['id'];
        // 如果这一天，当前用户没有调用量
        if (!array_key_exists($day_time, $list_data_day) || !array_key_exists($product_id, $list_data_day[$day_time])) {
            return array_merge($this->item_base, $product, compact('day_time'));
        }

        // 如果有调用量
        return array_merge($list_data_day[$day_time][$product_id], $product, compact('day_time'));
    }

    /**
     * 获取按天分布的数据
     */
    protected function getDataGroupByDay()
    {
        // 需要展示的时间范围
        $list_range = $this->dateRange();

        // 基本的限制
        $where = $this->genParamsForDayShow();

        // 容器
        $list_data_day = [];
        array_walk($list_range, function ($date) use ($where, &$list_data_day) {
            // 获取某天的相应产品的统计信息
            $list_data_day[$date] = $this->appendStatByDay($where, $date);
        });

        return $list_data_day;
    }

    /**
     * 获取某天的相应产品的统计信息
     * @param array $where
     * @param string $date
     * @return array
     * @throws \Exception
     */
    protected function appendStatByDay($where, $date)
    {
        // 追加时间限制
        $params = $this->appendTimeLimit($where, $date);

        // 调用接口获取统计信息
        $list_stat = $this->getStatInfo($params);

        if (!array_key_exists('list_item', $list_stat['result'])) {
            return [];
        }

        return array_column($list_stat['result']['list_item'], null, 'cid');
    }


    /**
     * 追加时间限制
     * @param $where
     * @param $date
     * @return array
     */
    protected function appendTimeLimit($where, $date)
    {
        // 时间限制
        $limit_time = [
            'start_time' => $date,
            'end_time' => $date,
        ];
        return array_merge($where, $limit_time);
    }

    /**
     * 为列表按天生成条件
     * @return array
     */
    protected function genParamsForDayShow()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && 可用状态
        $limit_base = $this->limitBaseInfo();

        // 条件整合
        $limit = $this->tidyLimitProduct($limit_product, $limit_base);

        // 附加签名信息
        return $this->appendSignToCondition($limit);
    }

    /**
     * 整理参数
     * @param $limit_product
     * @param $limit_base
     * @return array
     */
    protected function tidyLimitProduct($limit_product, $limit_base)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['cid' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $cid = $limit_base;

            return compact('cid');
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $cid = '哈哈,永远不可能数据的';
        } else {
            $cid = $limit_intersect;
        }

        return compact('cid');

    }


    /**
     * 合并产品列表和统计信息
     * @param array $list_product 产品列表
     * @param array $list_stat 统计结果
     * @return array
     */
    protected function mergeStatAndProduct($list_product, $list_stat)
    {
        // 给产品追加统计信息
        $list_stat_product = $this->appendStatToProduct($list_product, $list_stat);

        // 总计信息
        $list_stat_total = $this->tidyTotalData($list_stat);

        // 合并(v-table的语法)
        array_unshift($list_stat_product, $list_stat_total);
        return $list_stat_product;
    }

    /**
     * 整理总计信息
     * @param $list_stat
     * @return array
     */
    protected function tidyTotalData($list_stat)
    {
        if (!array_key_exists('list_total', $list_stat['result'])) {
            return array_merge($this->item_base, ['name' => '总计']);
        }
        return array_merge($this->item_base, $list_stat['result']['list_total'], ['name' => '总计']);
    }

    /**
     * 给产品追加统计信息
     * @param array $list_product 产品列表
     * @param array $list_stat 统计信息
     * @return array
     */
    protected function appendStatToProduct($list_product, $list_stat)
    {
        $list_stat_product = array_column($list_stat['result']['list_item'], null, 'cid');

        // 以产品为单位集成统计信息
        return array_map(function ($item) use ($list_stat_product) {
            $product_id = $item['id'];

            // 如果产品没有统计数据
            if (!array_key_exists($product_id, $list_stat_product)) {
                return array_merge($item, $this->item_base);
            }

            // 如果有统计信息
            return array_merge($item, $list_stat_product[$product_id]);
        }, $list_product);
    }

    /**
     * 获取统计列表选中的产品
     * @return array
     */
    protected function getProductForList()
    {
        // 条件
        $where = $this->genConditionToProduct();

        // 产品列表
        $list_product = $this->getProductByCondition($where);

        // 追加客户名称
        return $this->mergeAccountToProduct($list_product);
    }

    /**
     * 追加客户到产品列表
     * @param array $list_product
     * @return array
     */
    protected function mergeAccountToProduct($list_product)
    {
        // 客户列表
        $list_account = $this->getAccountList();

        // 客户产品的relationship
        $list_relationship = $this->getRelationshipOfAccountAndProduct();
        $list_relationship = array_column($list_relationship, null, 'product_id');

        // 整合
        return $this->appendAccountToProduct($list_product, $list_account, $list_relationship);
    }

    /**
     * 追加客户名称到产品信息中
     * @param $list_product
     * @param $list_account
     * @param $list_relationship
     * @return array
     */
    protected function appendAccountToProduct($list_product, $list_account, $list_relationship)
    {
        return array_map(function ($item_product) use ($list_account, $list_relationship) {
            $product_id = $item_product['id'];

            // 没有绑定客户的情况
            if (!array_key_exists($product_id, $list_relationship)) {
                $item_product['name_account'] = '';
                $item_product['id_account'] = '';
                return $item_product;
            }

            // 绑定客户的名字
            $account_id = $list_relationship[$product_id]['account_id'];
            $name_account = isset($list_account[$account_id]['name']) ? $list_account[$account_id]['name'] : '';
            $item_product['name_account'] = $name_account;
            $item_product['id_account'] = $account_id;

            return $item_product;
        }, $list_product);
    }

    /**
     * 客户产品的relationship
     * @return mixed
     */
    protected function getRelationshipOfAccountAndProduct()
    {
        $type_id = 4;
        return D('FinanceAccountProduct')
            ->where(compact('type_id'))
            ->select();
    }

    /**
     * 产品列表展示产品的条件
     * @return array
     */
    protected function genConditionToProduct()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && 可用状态
        $limit_base = $this->limitBaseInfo();


        // 为产品列表生成条件
        return $this->tidyCondition($limit_product, $limit_base);
    }

    /**
     * 为产品列表生成条件
     * @param array $limit_product 限制 product_id
     * @param array $limit_base 限制签约状态 && 可用状态
     * @return array
     */
    protected function tidyCondition($limit_product, $limit_base)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['id' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $id = ['in', $limit_base];
            return compact('id');
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $id = '哈哈,永远不可能数据的';
        } else {
            $id = ['in', $limit_intersect];
        }

        return compact('id');
    }

    /**
     * 调用接口获取统计信息
     * @param array $params
     * @return array
     * @throws \Exception
     */
    protected function getStatInfo($params)
    {
        // 接口URL
        $url_api = C('LIST_API_URL')['bang_stat_list'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['status'] == 1000) {
                break;
            }
            $i++;
        }

        // 三次请求失败,做记录
        $this->logForFail($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 如果连续三次请求失败 则记录日志
     * @param array $result_request
     * @param string $url
     * @param array $params 请求的参数
     * @throws \Exception
     */
    protected function logForFail($result_request, $url, $params)
    {
        if ($result_request['status'] != 1000) {
            $msg = [
                'msg' => '邦企查统计列表API连续三次请求失败',
                'data' => $result_request,
                'url' => $url,
                'params' => $params
            ];
            $this->log($msg);
            throw new \Exception('邦企查统计列表API连续三次请求失败');
        }
    }

    /**
     * 为列表统计生成条件
     * @return array
     */
    protected function genConditionForList()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && 可用状态
        $limit_base = $this->limitBaseInfo();

        // 时间限制
        $limit_time = $this->limitTime();

        // 条件整合
        $limit = $this->tidyConditionForDay($limit_product, $limit_base, $limit_time);

        // 附加签名信息
        return $this->appendSignToCondition($limit);
    }

    /**
     * 给条件追加签名
     * @param array $limit 条件
     * @return array
     */
    protected function appendSignToCondition($limit)
    {
        // 签名信息
        $sign_info = $this->genApiSign();

        return array_merge($limit, $sign_info);
    }

    /**
     * 生成参数
     * @return array
     */
    protected function genApiSign()
    {
        // 参数
        $timestamp = time();
        $nonce = mt_rand(1000, 9999);

        // 签名
        $signature = $this->genSign($timestamp, $nonce);
        return compact('signature', 'timestamp', 'nonce');
    }

    /**
     * 生成签名
     * @param integer $timestamp 发起访问的时间戳
     * @param integer $nonce 四位随机数
     * @return string
     */
    protected function genSign($timestamp, $nonce)
    {
        $client_key = C('LIST_API_URL')['bang_api_key'];
        $client_secret = C('LIST_API_URL')['bang_api_secret'];

        $dict_source = compact('timestamp', 'nonce', 'client_key', 'client_secret');
        sort($dict_source, SORT_STRING);
        return sha1(implode('', $dict_source));
    }

    /**
     * 条件整合
     * @param array $limit_product
     * @param array $limit_base
     * @param array $limit_time
     * @return array
     */
    protected function tidyConditionForDay($limit_product, $limit_base, $limit_time)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['cid' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $cid = $limit_base;

            $limit_cid = compact('cid');
            return array_merge($limit_cid, $limit_time);
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $cid = '哈哈,永远不可能数据的';
        } else {
            $cid = $limit_intersect;
        }

        return array_merge(compact('cid'), $limit_time);
    }

    /**
     * 时间限制
     * @return array
     */
    protected function limitTime()
    {
        $request_body = $this->genParamsForPost();

        $start_time = date('Y-m-d', strtotime($request_body['begin']));
        $end_time = date('Y-m-d', strtotime($request_body['end']));
        return compact('start_time', 'end_time');
    }


    /**
     * 限制基本的信息
     *
     */
    protected function limitBaseInfo()
    {
        // 限制状态
        $limit_active = $this->limitStatus();

        // 限制签约类型
        $limit_contract_status = $this->limitContractStatus();

        // 符合条件的产品
        $where = array_merge($limit_active, $limit_contract_status);
        $list_product = $this->getProductByCondition($where);

        // 转成product_id的限制
        return array_map(function ($item) {
            return $item['id'];
        }, $list_product);
    }

    /**
     * 根据条件获取邦秒配产品
     * @param array $where
     * @return array
     */
    protected function getProductByCondition($where)
    {
        return D('BangProducts')->where($where)
            ->order('id desc')
            ->select();
    }

    /**
     * 限制签约类型
     * @return array
     */
    protected function limitContractStatus()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('contract_status', $request_body) || !$request_body['contract_status']) {
            return [];
        }
        $contract_status = $request_body['contract_status'];
        return compact('contract_status');
    }

    /**
     * 限制状态
     * @return array
     */
    protected function limitStatus()
    {
        $request_body = $this->genParamsForPost();

        if (!array_key_exists('status', $request_body) || !$request_body['status']) {
            return [];
        }
        $status = $request_body['status'];
        return compact('status');
    }

    /**
     * 限制product_id
     */
    protected function limitProduct()
    {
        $request_body = $this->genParamsForPost();

        // 如果没有限制product_id，也没有限制account_id
        $limit_both_not = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_both_not) {
            return [];
        }

        // 如果只是限制了product_id
        $limit_product_only = (array_key_exists('product_id', $request_body) && $request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_product_only) {
            $product_id = [$request_body['product_id']];
            return compact('product_id');
        }

        // 如果只是限制了account_id
        $limit_account_only = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (array_key_exists('account_id', $request_body) && $request_body['account_id']);
        if ($limit_account_only) {

            $account_id = $request_body['account_id'];
            $product_id = $this->getRelationshipOfAccount($account_id);
            return compact('product_id');
        }

        // 同时限制了product_id && account_id
        return $this->limitProdutAndAccount($request_body);
    }

    /**
     * 获取特定客户下辖的邦秒配产品ID
     * @param integer $account_id
     * @return array
     */
    protected function getRelationshipOfAccount($account_id)
    {
        $type_id = 4;
        $where = compact('type_id', 'account_id');
        $list_relationship = D('FinanceAccountProduct')->where($where)
            ->select();

        return array_map(function ($item) {
            return $item['product_id'];
        }, $list_relationship);
    }

    /**
     * 同时限制了product_id && account_id
     * @param $request_body
     * @return array
     */
    protected function limitProdutAndAccount($request_body)
    {
        $product_id = $request_body['product_id'];
        $account_id = $request_body['account_id'];
        $list_product = $this->getRelationshipOfAccount($account_id);
        if (in_array($product_id, $list_product)) {
            return compact('product_id');
        } else {
            return ['product_id' => ['永远不可能有值的哈哈']];
        }
    }

    /**
     * 客户列表
     * @return array
     */
    public function getAccountList()
    {
        return D('FinanceAccounts')->index('id')
            ->select();
    }

    /**
     * 产品列表
     * @return array
     */
    public function getProductList()
    {
        return D('BangProducts')->index('id')
            ->select();
    }

    /**
     * 进入统计列表页的初始化的参数
     * @return array
     */
    public function initParams()
    {
        $begin = date('Y-m-d');
        $end = date('Y-m-d');
        return compact('end', 'begin');
    }

    /**
     * 进入详情页的初始化参数
     */
    public function initParamsForDetail()
    {
        // 如果没有带着当前时间， 就初始化当前值, （哈哈 这一点是不可能的）
        $begin = I('get.begin', '');
        $end = I('get.end', '');
        if (!$begin || !$end) {
            $begin = $end = date('Y-m-d');
        }

        $id = I('get.id');
        return compact('id', 'end', 'begin');
    }
}
