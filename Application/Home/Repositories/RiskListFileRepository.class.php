<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-05 15:56:10
 */

namespace Home\Repositories;

class RiskListFileRepository extends BaseRepository
{
    /**
     * 导出风险名单账号数据
     * @param  array $list 导出的数据
     * @return
     */
    public function getUserListFile($list)
    {
        $file_name = RUNTIME_PATH . 'Cache/risk_user_list.csv';
        $title_list = '账号ID,账号名称,所属客户,账号状态,签约状态,API KEY,Time';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        $contract = $this->getContractStatus();
        foreach ($list as $key => $value) {
            $status = ($value['status'] == 1) ? '可用' : '禁用';
            $contract_status = $contract[$value['contract_status']];
            $time = ($value['created_at'] ? date('Y-m-d H:i:s', $value['created_at']) : '').'—';
            $time .= $value['validuntil'] ? date('Y-m-d H:i:s', $value['validuntil']) : '';
            $file_str = '"'.$value['id'].'","'.$value['developer'].'","'.$value['account_name'].'","'.$status.'","'.$contract_status.'","'.$value['apikey'].'","' . $time.'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    /**
     * 风险名单统计列表导出
     * @param  array $list     导出内容
     * @param  array $hit_type 命中种类
     * @return
     */
    public function getRistStatListFile($list, $hit_type)
    {
        $file_name = RUNTIME_PATH . 'Cache/risk_stat_list.csv';

        $title_list = '账号ID,账号名称,所属客户,总查询量,有效查询量,总查得量,本人查得量,联系人查得量';
        foreach ($hit_type as $key => $value) {
            $title_list .= ','.$value;
        }
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $all_counts = isset($value['all_counts']) ? (int)$value['all_counts'] : 0;
            $success_counts = isset($value['success_counts']) ? (int)$value['success_counts'] : 0;
            $get_counts = isset($value['get_counts']) ? (int)$value['get_counts'] : 0;
            $own_get_counts = isset($value['own_get_counts']) ? (int)$value['own_get_counts'] : 0;
            $input_get_counts = isset($value['input_get_counts']) ? (int)$value['input_get_counts'] : 0;
            $file_str = '"'.$value['id'].'","'.$value['developer'].'","'.$value['account_name'].'","'.$all_counts.'","'.$success_counts.'","'.$get_counts.'","'.$own_get_counts.'","'.$input_get_counts.'"';
            foreach ($hit_type as $kk => $vv) {
                $file_str .= ',"'.(isset($value[$kk]) ? (int)$value[$kk] : 0).'"';
            }
            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }

        $this->fileDownload($file_name);
    }

    /**
     * 风险名单统计详情导出
     * @param  array $list     导出内容
     * @param  array $hit_type 命中种类
     * @return
     */
    public function getRistStatDetail($list, $hit_type)
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $id = I('get.id', '', 'intval');

        $begin = date('Ymd', $begin);
        $end = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/risk_stat_'.$begin.'_'.$end.'_'.$id.'.csv';

        $title_list = '日期,总查询量,有效查询量,总查得量,本人查得量,联系人查得量';
        foreach ($hit_type as $key => $value) {
            $title_list .= ','.$value;
        }

        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $all_counts = isset($value['all_counts']) ? (int)$value['all_counts'] : 0;
            $success_counts = isset($value['success_counts']) ? (int)$value['success_counts'] : 0;
            $get_counts = isset($value['get_counts']) ? (int)$value['get_counts'] : 0;
            $own_get_counts = isset($value['own_get_counts']) ? (int)$value['own_get_counts'] : 0;
            $input_get_counts = isset($value['input_get_counts']) ? (int)$value['input_get_counts'] : 0;
            $file_str = '"'.$key.'","'.$all_counts.'","'.$success_counts.'","'.$get_counts.'","'.$own_get_counts.'","'.$input_get_counts.'"';
            foreach ($hit_type as $kk => $vv) {
                $file_str .= ',"'.(isset($value[$kk]) ? (int)$value[$kk] : 0).'"';
            }
            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }
}
