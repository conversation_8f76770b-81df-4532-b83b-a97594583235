<?php

namespace Home\Repositories;

class CrawlerStatListRepository extends BaseRepository
{
    protected $url = '/admin/crawler/sv';

    public function watchCrawlerList()
    {
        $list = $this->getWatchCrawlerApi();

        return $list;
    }

    public function getWatchCrawlerApi()
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->url;
        $url = $param ? $url.'?'.http_build_query($param, '', '&') : $url;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }
}
