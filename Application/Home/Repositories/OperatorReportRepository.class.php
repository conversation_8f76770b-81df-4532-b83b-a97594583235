<?php

namespace Home\Repositories;

use Account\Model\AccountModel;
use Common\ORG\Page;

/**
 * 运行商报告的资源整合类
 * <AUTHOR>
 * @datetime 10:23 2018/12/12
 **/
class OperatorReportRepository extends BaseRepository
{
    //运营商报告的列表接口中的每页数据条数（由接口服务端控制）
    private $listRows = 20;
    //详单状态
    public $recordStatus = [
        0   => '采集中',
        1   => '采集成功',
        2   => '采集失败'
    ];
    //报告状态
    public $pdfStatus = [
        0   => '生成中',
        1   => '生成成功',
        2   => '生成失败'
    ];
    /**
     * 获取当前的列表数据
     * <AUTHOR>
     * @datetime 10:24 2018/12/12
     *
     * @access public
     *
     * @return array
     **/
    public function getListData()
    {
        //获取查询条件
        $where = $this->getWhereByGet();
        //获取列表数据
        $data = $this->getListDataByWhere($where);
        //为数据进行分页
        return $this->getListDataByPage($data);
    }
    /**
     * 查询条件的整合
     * <AUTHOR>
     * @datetime 10:59 2018/12/12
     *
     * @access private
     *
     * @return array
     **/
    private function getWhereByGet()
    {
        $time = $this->getTimeAreaByGet();
        $where = [];
        $where['start_time'] = strtotime($time['startTime']);
        $where['end_time'] = strtotime($time['endTime']);

        if (!empty(I('get.sid'))) {
            $where['sid'] = I('get.sid');
        }
        if (!empty(I('get.tel'))) {
            $where['tel'] = I('get.tel');
        }
        if (!empty(I('get.account'))) {
            $where['apikey'] = I('get.account');
        }
        if (!empty(I('get.apikey'))) {
            $where['apikey'] = I('get.apikey');
        }
        if (isset($_GET['record_status']) && $_GET['record_status']!=-1) {
            $where['record_status'] = I('get.record_status');
        }
        if (isset($_GET['pdf_status']) && $_GET['pdf_status']!=-1) {
            $where['pdf_status'] = I('get.pdf_status');
        }

        $page = I('get.page');
        $where['curpage'] = empty($page)?1:$page;

        return $where;
    }
    /**
     * 获取查询时间区间
     * <AUTHOR>
     * @datetime 14:55 2018/12/12
     *
     * @access public
     *
     * @return array
     **/
    public function getTimeAreaByGet()
    {
        $oneTime = I('get.start_time') ? strtotime(I('get.start_time')) : time();
        $twoTime = I('get.end_time') ? strtotime(I('get.end_time')) : time();
        //大值为endTime，小值为startTime
        $endTime = ($startTime = min($oneTime, $twoTime))==$oneTime ? $twoTime : $oneTime;
        return [
            'startTime' => date('Y-m-d', $startTime) . ' 00:00:00',
            'endTime'   => date('Y-m-d', $endTime) . ' 23:59:59'
        ];
    }
    /**
     * 通过接口获取列表数据
     * <AUTHOR>
     * @datetime 11:00 2018/12/12
     *
     * @access private
     * @param $where array 查询条件
     *
     * @return array
     **/
    private function getListDataByWhere($where = [])
    {
        $url = $this->getDomain() . '/list/neibu';
        $where = http_build_query($where);
        $res = $this->getCurl('GET', $url . '?' . $where);
        //解码JSON格式
        $data = @json_decode($res, true);
        if (empty($data)) {
            return [];
        }
        //验证数据是否获取成功
        $status = isset($data['status'])?$data['status']:1;
        switch ($status) {
            case 0 :
                return [
                    'count' => $data['count'],
                    'list'  => $data['list']
                ];
                break;
            case 6000:
                return [
                    'count' => 0,
                    'list' => []
                ];
                break;
            default :
                exit('get data failed , status is ' . $status);
                break;
        }
    }
    /**
     * 对数据进行分页
     * <AUTHOR>
     * @datetime 11:40 2018/12/12
     *
     * @access private
     * @param $data array 获取到的数据
     *
     * @return array
     **/
    private function getListDataByPage($data)
    {
        //获取数据总数
        $count = $data['count'];
        //获取当前页数
        $page = new Page($count, $this->listRows);
        return [
            'page'  => $page->show(),
            'data'  => $data['list']
        ];
    }
    /**
     * 计算查看运营商报告详细的加密措施，然后返回URL
     * <AUTHOR>
     * @datetime 9:51 2018/12/13
     *
     * @access public
     *
     * @return array
     **/
    public function getViewUrl()
    {
        //获取用户数据
        $accountData = $this->getAccountData();
        if ($accountData['status']!=0) {
            return $accountData;
        }
        //生成URL地址
        $url = $this->getDomain() . '/site/view?' .$this->getParamForUrl($accountData);
        $status = 0;
        return compact('status', 'url');
    }
    /**
     * 计算查看运营商报告下载的URL地址
     * <AUTHOR>
     * @datetime 11:10 2018/12/13
     *
     * @access public
     *
     * @return array
     **/
    public function getDownloadUrl()
    {
        $type = I('post.type');
        if ($type=='single') {
            return $this->downloadSinglePdf();
        } else {
            return $this->downloadBatchPdf();
        }
    }
    /**
     * 单个PDF文件下载
     * <AUTHOR>
     * @datetime 15:11 2018/12/13
     *
     * @access private
     *
     * @return array
     **/
    private function downloadSinglePdf()
    {
        //获取用户数据
        $accountData = $this->getAccountData();
        if ($accountData['status']!=0) {
            return $accountData;
        }
        //生成URL地址
        $url = $this->getDomain() . '/make/download?' .$this->getParamForUrl($accountData);
        return $this->buildStatusArray(0, $url);
    }
    /**
     * 打包下载PDF文件
     * <AUTHOR>
     * @datetime 15:12 2018/12/13
     *
     * @access private
     *
     * @return array
     **/
    private function downloadBatchPdf()
    {
        set_time_limit(300);
        //获取用户数据
        $accountData = $this->getAccountData();
        if ($accountData['status']!=0) {
            return $accountData;
        }
        //生成URL地址
        $url = $this->getDomain() . '/make/export?' .$this->getParamForUrl($accountData, true);
        //批量生成由于需要传递JSON参数,所以不知道怎样才能通过JS完成，
        //这里使用PHP进行接口对接，保存在本地后返回给客户端

        //获取sid
        $sid = explode(',', $accountData['sid']);
        if (empty($sid)) {
            return $this->buildStatusArray(12, '当前SID丢失，请及时联系开发人员');
        }
        //验证并发锁
        $lockFilename = CACHE_PATH . 'operator_report_batch.lock';
        if (!$this->checkLockFile($lockFilename)) {
            return $this->buildStatusArray(11, '当前存在用户正在下载文件，系统只支持同时一位用户下载，请稍后重试');
        }
        //开始下载
        $filename = 'operator_report.zip';
        $filePath = CACHE_PATH . $filename;
        file_exists($filePath) && @unlink($filePath);
        $this->downloadBatchPdfFile($url, $filePath, $sid);
        @unlink($lockFilename);
        return $this->buildStatusArray(0, '/cache/Cache/' . $filename);
    }
    /**
     * 下载批量下载的PDF文件至本地
     * <AUTHOR>
     * @datetime 13:34 2018/12/13
     *
     * @access private
     * @param $url string 下载文件的地址
     * @param $filename string 文件保存路径
     * @param $sid array 需要下载的SID数据
     *
     * @return void
     **/
    private function downloadBatchPdfFile($url, $filename, $sid)
    {
        $sid = json_encode($sid);
        $res = $this->getCurl('POST', $url, $sid, 'json');
        file_put_contents($filename, $res);
    }
    /**
     * 文件的并发锁验证
     * <AUTHOR>
     * @datetime 13:35 2018/12/13
     *
     * @access private
     * @param $lock string 锁文件名称
     *
     * @return boolean
     **/
    private function checkLockFile($lock)
    {
        if (file_exists($lock)) {
            return false;
        }
        file_put_contents($lock, time());
        return true;
    }
    /**
     * 获取下载/查看接口的参数
     * <AUTHOR>
     * @datetime 11:08 2018/12/13
     *
     * @access private
     * @param $accountData array 账号的数据
     * @param $isBatch boolean 是否为批量下载（不加sid）
     *
     * @return string
     **/
    private function getParamForUrl($accountData, $isBatch = false)
    {
        $apikey = $accountData['apikey'];
        $appSecret = $accountData['appSecret'];
        //获取签名
        $sign = $this->getSign($apikey, $appSecret);
        //计算需要传递的参数
        $param = [
            'apikey'    => $apikey
        ];
        if (!$isBatch) {
            $sid = $accountData['sid'];
            $param['sid'] = $sid;
        }
        return http_build_query(array_merge($param, $sign));
    }
    /**
     * 用于在查看/下载的时候获取用户的apiKey/SID/apiSecret等数据
     * <AUTHOR>
     * @datetime 10:41 2018/12/13
     *
     * @access private
     *
     * @return array
     **/
    private function getAccountData()
    {
        //默认是失败状态
        $status = 10;
        //获取传递过来的SID
        $sid = I('post.sid');
        if (empty($sid)) {
            return $this->buildStatusArray($status, 'sid not exists');
        }
        //获取传递过来的apikey
        $apikey = I('post.apikey');
        if (empty($apikey)) {
            return $this->buildStatusArray($status, 'apikey not exists');
        }
        //获取appSecret
        $appSecret = (new AccountModel())->getAccountInfoByWhere(compact('apikey'), 'appsecret');
        if (empty($appSecret) || empty($appSecret = $appSecret['appsecret'])) {
            return $this->buildStatusArray($status, 'appsecret not exists');
        }
        $status = 0;
        return compact('status','apikey', 'appSecret', 'sid');
    }
    /**
     * 签名算法
     * <AUTHOR>
     * @datetime 10:43 2018/12/12
     *
     * @access private
     *
     * @return array
     **/
    private function getSign($apiKey, $appSecret)
    {
        $nonce = rand(1000, 9999);
        $timeStamp = time();
        $sign_arr = [
            $apiKey,
            $appSecret,
            $nonce,
            $timeStamp
        ];
        sort($sign_arr, SORT_STRING);
        $signature = sha1(implode('', $sign_arr));
        return [
            'signature' => $signature,
            'nonce'     => $nonce,
            'timestamp' => $timeStamp
        ];
    }
    /**
     * 获取接口地址
     * <AUTHOR>
     * @datetime 11:03 2018/12/12
     *
     * @access private
     *
     * @return string
     **/
    private function getDomain()
    {
        return C('LIST_API_URL')['operator_report_list'];
    }
    /**
     * 失败的情况下返回一个数组
     * <AUTHOR>
     * @datetime 13:50 2018/12/13
     *
     * @access private
     * @param $status int 标号（0为正常）
     * @param $msg string 提示信息
     *
     * @return array
     **/
    private function buildStatusArray($status, $msg)
    {
        return compact('status', 'msg');
    }
}