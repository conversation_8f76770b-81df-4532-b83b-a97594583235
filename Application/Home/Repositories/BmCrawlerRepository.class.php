<?php

namespace Home\Repositories;

use Common\Common\FlushOldCrawlerToken;
use Common\Model\AuthModel;
use Common\Model\CuishouUserModel as CommonCuishouUserModel;

class BmCrawlerRepository
{
    use  FlushOldCrawlerToken;
    private static $relation_ships = [
        '配偶',
        '父母',
        '兄弟姐妹',
        '子女',
        '亲戚',
        '同事',
        '朋友',
        '同学',
        '其他'
    ];

    protected $config_info_shenqing = [
        '姓名', '身份证号', '手机号码', '所在地区', '居住地址'
    ];

    protected $config_default_shenqing = [];

    /**
     * h5配置 view
     * @return array
     */
    public function h5Config()
    {
        // 当前编辑的产品信息
        $product = $this->getH5ProductForConfig();

        // 整理相应的参数
        return $this->tidyDataForH5Config($product);
    }

    /**
     * 整理相应的参数
     * @param array $account_info 要编辑的产品
     * @return array
     */
    protected function tidyDataForH5Config(array $account_info)
    {
        // 必填紧急联系人反序列化
        $account_info = $this->tidyEmergencyForProduct($account_info);

        // 基本配置信息
        $relation_ships = self::$relation_ships;
        $preview_url = C('PREVIEW_URL');
        $config_info_shenqing = $this->config_info_shenqing;
        $config_default_shenqing = $this->config_default_shenqing;

        return compact('emergency_contact_detail_limits', 'relation_ships',
            'account_info', 'preview_url', 'config_info_shenqing', 'config_default_shenqing');
    }

    /**
     * 格式化紧急联系人数据
     * @param array $account_info
     * @return array
     */
    protected function tidyEmergencyForProduct(array $account_info)
    {
        $account_info['emergency_contact_detail_limits'] = $this->getEmergencyForH5Config($account_info['emergency_contact_detail_limits']);
        return $account_info;
    }

    /**
     * 获取要编辑的产品的紧急联系人数据
     * @param string $emergency_contact_detail_limits
     * @return array
     */
    protected function getEmergencyForH5Config($emergency_contact_detail_limits)
    {
        if (!$emergency_contact_detail_limits) {
            return [];
        }

        $emergency_contact_detail_limits = unserialize($emergency_contact_detail_limits);
//        exit(json_encode(compact('emergency_contact_detail_limits')));

        // 判断版本
        $version_old = $this->isOldVersion($emergency_contact_detail_limits);

        // 新版版本
        if ($version_old === false) {
            return $emergency_contact_detail_limits;
        }

        return $this->convertOldToNewVersion($emergency_contact_detail_limits);
    }

    /**
     * 将老版本的紧急联系人转化成新版的格式
     * @param array $emergency_contact_detail_limits
     * @return array
     */
    protected function convertOldToNewVersion(array $emergency_contact_detail_limits)
    {
        $list_container = [];
        $start_index = 0;
        array_walk($emergency_contact_detail_limits, function ($number, $relation) use (&$list_container, &$start_index) {
            $item = array_fill($start_index, $number, [$relation]);
            $list_container = array_merge($list_container, $item);
            $start_index += $number;
        });
        return $list_container;
    }

    /**
     * 紧急联系人的配置是旧版本
     * @param array $emergency_contact_detail_limits
     * @return boolean
     */
    protected function isOldVersion(array $emergency_contact_detail_limits)
    {
        return count($emergency_contact_detail_limits) === count($emergency_contact_detail_limits, 1);
    }

    protected function appendBaseInfoToProduct()
    {

    }

    /**
     * 获取要编辑的h5的产品
     * @return array
     */
    protected function getH5ProductForConfig()
    {
        $id = I('get.id', '', 'trim');
        return $this->getOneProductByCondition(compact('id'));
    }

    /**
     * 获取一个邦秒爬产品
     * @param array $where
     * @return array
     */
    protected function getOneProductByCondition(array $where)
    {
        return (new AuthModel())->where($where)
            ->find();
    }


    /**
     * 为API客户初始化接口配置
     * @param integer $id
     * @param array $params
     * @return bool
     */
    public function initApiConfigForAccount($id, $params)
    {
        if ($params['source'] != 'api') {
            return true;
        }

        // 为初始化API配置,生成参数
        $params = $this->genParamsForInitApiConfig();

        // 更新
        $this->updateConfig(compact('id'), $params);
        return true;
    }


    /**
     * 为初始化API配置,生成参数
     * @return array
     */
    protected function genParamsForInitApiConfig()
    {
        // 必填字段
        $required_field = [1, 2, 3];

        // 整合配置
        $api_config_info = json_encode(compact('required_field'));
        return compact('api_config_info');
    }

    /**
     * 更新APi配置
     * @throws \Exception
     */
    public function updateApiConfig()
    {
        // 条件
        $where = $this->genConditionsForApiConfig();

        // 待更新参数
        $params = $this->genParamsForApiConfig();

        // 更新配置
        $this->updateConfig($where, $params);

        // 请求token 刷新信息
        $this->flushToken($where);
    }

    /**
     * 刷新token
     * @throws \Exception
     */
    protected function requestTokenForNewConfig()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        $id = $request_body['id'];
        // update token
        $user_info = (new AuthModel())->where(compact('id'))->field('id,appid,appsecret,source')->find();
        (new AuthModel())->updateToken($user_info);
    }

    /**
     * 更新配置
     * @param array $where
     * @param array $params
     */
    protected function updateConfig($where, $params)
    {
        (new AuthModel())->where($where)
            ->save($params);
    }

    /**
     * 为API配置生成参数
     * @return array
     */
    protected function genParamsForApiConfig()
    {
        // 必填的字段
        $required_field = $this->postKey('required_field');

        $api_config_info = json_encode(compact('required_field'));
        return compact('api_config_info');
    }

    /**
     * 生成API配置的条件
     * @return array
     * @throws \Exception
     */
    protected function genConditionsForApiConfig()
    {
        $id = $this->postKey('id', '', 'trim');
        if (!$id) {
            throw new \Exception('缺少必须的参数ID');
        }
        return compact('id');
    }

    /**
     * 获取选定产品的API配置
     * @return array
     * @throws \Exception
     */
    public function getApiConfig()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            throw new \Exception('缺少必选参数ID');
        }

        return (new AuthModel())->where(compact('id'))
            ->field('api_config_info')
            ->find();
    }

    /**
     * 列表时间上的制约
     * @return mixed
     */
    public function timeLimitForList()
    {
        // init params
        $status = I('get.status', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $id = I('get.id', '', 'trim');
        $appid = I('get.appid', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        // filter status, user, contract_status
        $where = $status ? ['status' => $status] : [];
        $where = $appid ? (['appid' => $appid] + $where) : $where;
        $where = $contract_status ? (['contract_status' => $contract_status] + $where) : $where;
        if ($begin && $begin_e) {
            $where['created_at'] = ['between', [strtotime($begin), strtotime($begin_e . ' 23:59:59')]];
        } elseif ($begin) {
            $where['created_at'] = ['egt', strtotime($begin)];
        } elseif ($begin_e) {
            $where['created_at'] = ['elt', strtotime($begin_e . ' 23:59:59')];
        }
        if ($end && $end_e) {
            $where['expiration_date'] = ['between', [strtotime($end), strtotime($end_e . ' 23:59:59')]];
        } elseif ($end) {
            $where['expiration_date'] = ['egt', strtotime($end)];
        } elseif ($end_e) {
            $where['expiration_date'] = ['elt', strtotime($end_e . ' 23:59:59')];
        }

        // 如果输入的用户id不同那么获取不到数据值
        if ($choose_id && $id && $choose_id != $id) {
            $where['id'] = 'what happened';
        } elseif ($choose_id) {
            $where['id'] = $choose_id;
        } elseif ($id) {
            $where['id'] = $id;
        }
        return $where;
    }


    /**
     * 催收分导出csv准备数据
     * @param array $where
     * @return array
     */
    public function geBmCrawlerForList($where)
    {
        $CommonCuishouUser = new CommonCuishouUserModel();
        $contract_status = $CommonCuishouUser->getContractStatus();

        //查下数据
        $account_list = D('Auth')->where($where)->order('id desc')->select();

        $account_product_infos = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($account_list, 'id'), 1);
        foreach ($account_list as $k => $item) {
            if ($account_product_infos[$item['id']]) {
                $account_list[$k]['account_product'] = $account_product_infos[$item['id']];
            }
        }

        $res = [];
        foreach ($account_list as $k => $item) {
            $temp = [];
            $temp['id'] = $item['id'];
            $temp['developer'] = $item['developer'];
            $temp['status'] = ($item['status'] == 1) ? '可用' : '禁用';
            $temp['contract_status'] = !empty($contract_status[$item['contract_status']]) ? $contract_status[$item['contract_status']] : ' ';
            $temp['email'] = $item['email'];
            $temp['source'] = $item['source'] == 'ui' ? 'h5' : 'api';
            $temp['appid'] = $item['appid'];
            $temp['need_report'] = ($item['need_report'] == 1) ? '是' : '否';
            $temp['need_dunning'] = ($item['need_dunning'] == 1) ? '是' : '否';
            $timeStr = 'start :' . ($item['created_at']) ? date('Y-m-d H:i:s', $item['created_at']) : ' ';
            $timeStr .= ' end :' . ($item['expiration_date']) ? date('Y-m-d H:i:s', $item['expiration_date']) : ' ';
            $temp['timeStr'] = $timeStr;
            $temp['account_name'] = $item['account_product']['account_name'];
            $temp['account_name'] = $temp['account_name'] ? $temp['account_name'] : '';
            $res[] = $temp;
        }
        return $res;
    }

    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListByDayForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '日期,账号ID,账号名称,客户ID,客户名称,尝试授权量,授权成功量,授权成功率,爬取成功量,爬取成功率,报告生成量,爬取成功号码量,尝试重置密码量,重置密码成功量,重置密码成功率,详单缺失率';
        $title_list = mb_convert_encoding($title_list, 'gb2312', 'utf-8');
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $key => $data) {
            foreach ($data as $kk => $stat_data) {
                // 数据补全
                $specialChar = is_numeric($stat_data['developer']) ? "\"\t" . $stat_data['developer'] . "\"," : '"' . $stat_data['developer'] . '",';
                $file_str = '"' . $kk . '","' . $stat_data['id'] . '",' . $specialChar . '"'
                    . $stat_data['account_id'] . '","' . $stat_data['name_account'] . '","' . $stat_data['total_nums'] . '","'
                    . $stat_data['authen_nums'] . '","' . $stat_data['authen_pct'] . '","' . $stat_data['crawl_nums'] . '","' . $stat_data['crawl_pct'] . '","' . $stat_data['report_nums'] . '","' . $stat_data['tel_num'] . '","' . $stat_data['pwd_rt_total'] . '","' . $stat_data['pwd_rt_success'] . '","' . $stat_data['pwd_rt_pct'] . '","' . $stat_data['log_loss_pct'] . '"';

                $file_str = mb_convert_encoding($file_str, 'gb2312', 'utf-8');
                file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
            }
        }
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
