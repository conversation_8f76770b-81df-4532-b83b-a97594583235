<?php
namespace Home\Repositories;

class DataCheckApiRepository extends BaseRepository
{
    //统计列表
    protected $listUrl = '/opdata_daily/lists';
    //统计详情
    protected $detailUrl = '/opdata_daily/detail';
    //接口统计
    protected $apiUrl = '/opdata_daily/statlist';
    //接口列表
    protected $productUrl = '/opdata_product/lists';

    /**
     * 邦秒验统计列表
     * @param  array $customer 客户列表及apikey
     * @return array
     */
    public function getOpdataDailStatList($customer)
    {
        $where = $this->getOpdataDailStatListParam();
        $where['apikey'] = $customer['apikey'];
        $stat = $this->getOpdataDailStatListApi($where);
        $list = isset($customer['list']) ? $customer['list'] : [];
        $total = ['sum_all' => 0, 'sum_success' => 0];
        if (!empty($list) && is_array($list)) {
            foreach ($list as $key => $value) {
                $account = $value['account_list'];
                $list[$key]['sum_all'] = 0;
                $list[$key]['sum_success'] = 0;
                if ($account && is_array($account)) {
                    foreach ($account as $kk => $vv) {
                        $list[$key]['sum_all'] += $stat[$vv['apikey']]['sum_all'];
                        $list[$key]['sum_success'] += $stat[$vv['apikey']]['sum_success'];
                    }
                }
                $list[$key]['sum_rate'] = round(($list[$key]['sum_success']/$list[$key]['sum_all']*100), 2).'%';

                $total['sum_all'] += $list[$key]['sum_all'];
                $total['sum_success'] += $list[$key]['sum_success'];
            }
        }
        return compact('list', 'total');
    }

    /**
     * 邦秒验统计详情
     * @param  array $customer 客户列表及apikey
     * @param  array $product_list 产品列表
     * @return array
     */
    public function getOpdataDailStatDetail($customer, $product_list)
    {
        $where = $this->getOpdataDailStatDetailParam();
        $where['apikey'] = $customer['apikey'];
        $stat = $this->getOpdataDailStatDetailApi($where);
        $total = ['c_all' => 0, 'c_success' => 0];
        if ($stat && is_array($stat)) {
            foreach ($stat as $key => $value) {
                $date = date('Y-m-d', strtotime($key));
                $list[$date] = $value;
                $list[$date]['c_rate'] = round(($value['c_success']/$value['c_all']*100), 2).'%';
                $total['c_all'] += $value['c_all'];
                $total['c_success'] += $value['c_success'];
                foreach ($product_list as $kk => $vv) {
                    if (isset($total[$vv['action']])) {
                        $total[$vv['action']] += isset($value[$vv['action']]) ? $value[$vv['action']] : 0;
                    } else {
                        $total[$vv['action']] = isset($value[$vv['action']]) ? $value[$vv['action']] : 0;
                    }
                }
            }
        }
        return compact('list', 'total');
    }

    /**
     * 邦秒验接口统计
     * @param  array $product_list 邦秒验产品列表
     * @return array
     */
    public function getOpdataDailApiStat($product_list)
    {
        $where = $this->getOpdataDailStatListParam();
        $stat = $this->getOpdataDailStatApi($where);
        $total = $list = [];
        if ($stat && is_array($stat)) {
            foreach ($stat as $key => $value) {
                $date = date('Y-m-d', strtotime($key));
                $list[$date] = $value;
                foreach ($product_list as $kk => $vv) {
                    if (isset($total[$vv['action']])) {
                        $total[$vv['action']] += isset($value[$vv['action']]) ? $value[$vv['action']] : 0;
                    } else {
                        $total[$vv['action']] = isset($value[$vv['action']]) ? $value[$vv['action']] : 0;
                    }
                }
            }
        }
        return compact('list', 'total');
    }

    /**
     * 邦秒验统计列表参数
     * @return array
     */
    public function getOpdataDailStatListParam()
    {
        $date_start = I('get.begin', '', 'trim');
        $date_end = I('get.end', '', 'trim');
        $date_start = $date_start ? date('Ymd', strtotime($date_start)) : date('Ymd');
        $date_end = $date_end ? date('Ymd', strtotime($date_end)) : date('Ymd');
        return compact('date_start', 'date_end');
    }

    /**
     * 邦秒验统计详情参数
     * @return array
     */
    public function getOpdataDailStatDetailParam()
    {
        $date_start = I('get.begin', '', 'trim');
        $date_end = I('get.end', '', 'trim');
        $date_start = $date_start ? date('Ymd', strtotime($date_start)) : date('Ymd', strtotime("-1 month"));
        $date_end = $date_end ? date('Ymd', strtotime($date_end)) : date('Ymd');
        return compact('date_start', 'date_end');
    }

    /**
     * 邦秒验统计列表
     * @param  array $param 参数条件
     * @return array
     */
    public function getOpdataDailStatListApi($param)
    {
        $domain = C('DATA_CHECK_CONFIG')['domain'];
        $url = $domain.$this->listUrl;

        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = is_array($res) ? $res : json_decode($res, true);
        if (!isset($res['code']) || $res['code'] != 0) {
            return [];
        }
        if (!isset($res['data']['stat']) || empty($res['data']['stat'])) {
            return [];
        }
        $list = [];
        foreach ($res['data']['stat'] as $key => $value) {
            $list[$value['apikey']] = $res['data']['stat'][$key];
        }
        return $list;
    }

    /**
     * 邦秒验详情统计
     * @param  array $param 参数条件
     * @return array
     */
    public function getOpdataDailStatDetailApi($param)
    {
        $domain = C('DATA_CHECK_CONFIG')['domain'];
        $url = $domain.$this->detailUrl;

        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = is_array($res) ? $res : json_decode($res, true);
        if (!isset($res['code']) || $res['code'] != 0) {
            return [];
        }
        if (!isset($res['data']['list'])) {
            return [];
        }
        return $res['data']['list'];
    }

    /**
     * 邦秒验接口统计列表
     * @param  array $param 参数列表
     * @return array
     */
    public function getOpdataDailStatApi($param)
    {
        $domain = C('DATA_CHECK_CONFIG')['domain'];
        $url = $domain.$this->apiUrl;

        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }

        $res = is_array($res) ? $res : json_decode($res, true);
        if (!isset($res['code']) || $res['code'] != 0) {
            return [];
        }
        if (!isset($res['data']['list'])) {
            return [];
        }
        return $res['data']['list'];
    }

    /**
     * 邦秒验接口列表
     * @return array
     */
    public function getOpdataDailProductApi()
    {
        $domain = C('DATA_CHECK_CONFIG')['domain'];
        $url = $domain.$this->productUrl;

        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }

        $res = is_array($res) ? $res : json_decode($res, true);
        if (!isset($res['code']) || $res['code'] != 0) {
            return [];
        }
        if (!isset($res['data']['list'])) {
            return [];
        }
        return $res['data']['list'];
    }
}