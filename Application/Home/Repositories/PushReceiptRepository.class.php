<?php

namespace Home\Repositories;

use Account\Model\CustomerModel;
use Common\Model\ReceiptModel;
use Common\Model\SystemUserModel;
use Exception;
use Home\Model\PushReceiptModel;

class PushReceiptRepository extends BaseRepository
{
    /*
     * @var RemitModel
     */
    private $model;

    const SIGN = '5db5147d21268bdc4f9b89d3249a4be4f7e0602ada6bda1a05100f979e2b6d07';

    public function __construct() {
        $this->model = new PushReceiptModel();
    }


    /**
     * 认款
     *
     * 推送数据中的 trade_id 就是 receipt_serial 流水单号
     *
     * @return bool|array
     * @throws Exception
     */
    public function remit($receipt_serial,$customer_id){
        //如果这个是征信渠道的收款单 需要使用这个子收款单流水后传入财务系统
        $sub_receipt_serial = $receipt_serial;

        $receipt_model = new ReceiptModel();
        //查询是否是拆分后的收款单
        $receipt_serial_info = $receipt_model->where(['receipt_serial' => $receipt_serial,])->find();

        if(!empty($receipt_serial_info['parent_serial'])){
            $receipt_serial = $receipt_serial_info['parent_serial'];
        }

        $push_receipt_info = $this->model->getInfoByTradeId($receipt_serial);

        if(empty($push_receipt_info)){
            // var_dump("无需推送!");die;
            return true; //无需推送
        }

        $customer_info = (new CustomerModel())->getCustomerByCustomerId($customer_id);
		$user_info = (new SystemUserModel())->getUserInfoByName($customer_info['salesman']);

        $data = [
            'record_id'     => $push_receipt_info['record_id'],// 上文推送接口数据
            'accept_number' => $sub_receipt_serial,// 认款编号 唯一id
            'charge_id'     => $sub_receipt_serial,// 认款编号 唯一id
            'money'         => $receipt_serial_info['money'],// 认款金额 (保证金额是字符串)
            'product_type'  => '金融数据',// 产品类型
            'accept_shop'   => $customer_info['company'],// 认款商户
            'sell_user'     => $user_info['realname'],// 销售
            'operate'       => 0,// int 0 认款时值为 0
        ];

        $jwt = $this->encode_jwt($data);

        $url = C('ACCOUNTANT_API_DOMAIN')."/v1/bill_pay/bill_push_finanical";
        $post_data = [
            'record_id' => $push_receipt_info['record_id'],
            'money'     => $receipt_serial_info['money'],
            'sign'      => $jwt,
        ];
        $post_res = $this->curlRequestForPost($url, json_encode($post_data));

        $this->log(['msg' => '同步财务认款 结果','ori_params' => $data,'params' => $post_data,'res' => $post_res]);

        if($post_res['code'] != 200) {
            return ['url' => $url, 'post_dat' => $post_data,'post_res' => $post_res];
        }

        $this->model->updateAcceptNumberByTaskId($push_receipt_info['task_id']);

        return true;
    }


    /**
     * 取消认款
     *
     * @return bool
     * <AUTHOR> 2024-06-24 15:47:50
     *
     */
    public function cancle($receipt_serial,$customer_id){
        //如果这个是征信渠道的收款单 需要使用这个子收款单流水后传入财务系统
        $sub_receipt_serial = $receipt_serial;

        $receipt_model = new ReceiptModel();
        //查询是否是拆分后的收款单
        $receipt_serial_info = $receipt_model->where(['receipt_serial' => $receipt_serial,])->find();

        if(!empty($receipt_serial_info['parent_serial'])){
            $receipt_serial = $receipt_serial_info['parent_serial'];
        }

        $push_receipt_info = $this->model->getInfoByTradeId($receipt_serial);

        if(empty($push_receipt_info)){
            return true; //无需推送
        }

        $customer_info = (new CustomerModel())->getCustomerByCustomerId($customer_id);
		$user_info = (new SystemUserModel())->getUserInfoByName($customer_info['salesman']);

        $data = [
            'record_id'     => $push_receipt_info['record_id'],// 上文推送接口数据
            'accept_number' => $sub_receipt_serial,// 认款编号 唯一id
            'money'         => $receipt_serial_info['money'],// 认款金额 (保证金额是字符串)
            'product_type'  => '金融数据',// 产品类型
            'accept_shop'   => $customer_info['company'],// 认款商户
            'sell_user'     => $user_info['realname'],// 销售
            'operate'       => 1,// 认款撤销时值为 1
        ];

        $jwt = $this->encode_jwt($data);

        $url = C('ACCOUNTANT_API_DOMAIN')."/v1/bill_pay/bill_finanical_del";
        $post_data = [
            'record_id' => $push_receipt_info['record_id'],
            'money'     => $receipt_serial_info['money'],
            'sign'      => $jwt,
        ];
        $post_res = $this->curlRequestForPost($url, json_encode($post_data));

        $this->log(['msg' => '同步财务认款撤销 结果','ori_params' => $data,'params' => $post_data,'res' => $post_res]);

        if($post_res['code'] != 200) {
            return false;
        }

        return true;
    }


    /**
     * jwt编码加密
     *
     * @return string
     * <AUTHOR> 2024-06-24 15:48:05
     *
     */
    private function encode_jwt($payload){
        $header = '{"alg":"HS256","typ":"JWT"}';

        $payload = json_encode($payload);

        $secretKey = self::SIGN;

        $headerBase64 = $this->base64url_encode($header);
        $payloadBase64 = $this->base64url_encode($payload);

        $expectedSignature = hash_hmac('sha256',$headerBase64.'.'.$payloadBase64, $secretKey,true);

        $signBase64 = $this->base64url_encode($expectedSignature);

        return $headerBase64.'.'.$payloadBase64.'.'.$signBase64;
    }





    /**
     * 解析数据
     *
     * @param $jwt
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-06-18 18:13:32
     */
    private function decode_jwt($jwt){

        // 用于签名的密钥
        $secretKey = self::SIGN;

        // 分割JWT
        list($headerBase64,$payloadBase64, $signatureBase64) = explode('.',$jwt);

        // 解码头部和有效载荷
        // $header = json_decode(Func::base64url_decode($headerBase64), true);
        $payload = json_decode($this->base64url_decode($payloadBase64), true);

        // 重新计算签名
        $signature = $this->base64url_decode($signatureBase64);
        $expectedSignature = hash_hmac('sha256',$headerBase64.'.'.$payloadBase64,$secretKey,true);

        // dd(Func::base64url_encode($signature),Func::base64url_encode($expectedSignature));

        // 比较签名
        if ($this->hash_equals($signature,$expectedSignature)) {
            // echo "JWT验证失败!";
            throw new Exception("JWT验证失败!");
        }

        return $payload;
    }



    // 辅助函数来安全地比较哈希值
    private function hash_equals($knownString,$userString) {
        if (strlen($knownString) !== strlen($userString)) {
            return false;
        }
        $result = 0;
        for ($i = 0;$i < strlen($knownString);$i++) {
            $result |= ord($knownString[$i]) ^ ord($userString[$i]);
        }
        return $result === 0;
    }

    /**
     * base64url_encode
     */
    private function base64url_encode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * base64url_decode
     */
    private function base64url_decode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }




    /**
     * 发送post请求
     * @param $url
     * @param $post_data
     * @return array|mixed
     */
    protected function curlRequestForPost($url, $post_data) {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $headers = [
            'Content-Type: application/json',
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $list_stat = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            //将返回结果和接口返回做相同接口处理
            return [
                'code' => -1,
                'msg'  => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($list_stat)) {
            return json_decode($list_stat, true);
        }

        return $list_stat;
    }
}