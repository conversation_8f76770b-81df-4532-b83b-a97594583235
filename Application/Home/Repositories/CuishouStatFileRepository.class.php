<?php

namespace Home\Repositories;

class CuishouStatFileRepository
{
    /*
    * 展示之前，一个客户必然包含的单元
    * */
    protected $item_base = [
        'converage_dunning' => 0.000,
        'converage_not_sure_dunning' => 0.000,
        'converage_all' => 0.000,
        'success_counts' => 0,
        'access_counts' => 0,
        'dunning_times' => 0,
        'not_sure_dunning_times' => 0,
        'not_times' => 0,
    ];

    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '客户ID,客户名称,总调用量,有效调用量,催收覆盖率,疑似催收覆盖率,整体覆盖率';
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $stat_data) {
            // 数据补全
            $stat_data = $this->tidyBaseItemForFile($stat_data);
            $file_str = $stat_data['id'] . ',' . $stat_data['developer'] . ',' . $stat_data['access_counts'] . ','
                . $stat_data['success_counts'] . ',' . $stat_data['converage_dunning'] . ',' .
                $stat_data['converage_not_sure_dunning'] . ',' . $stat_data['converage_all'];

            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * (详情)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileDetailForRequest($stat_list, $file_name)
    {
        $title_list = '时间,	总调用量,有效调用量,催收覆盖率,疑似催收覆盖率,整体覆盖率';
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $date => $stat_data) {

            // 补全基本的元素
            $stat_data = $this->tidyBaseItemForFile($stat_data);
            $show_str = $date . ',' . $stat_data['access_counts'] . ',' . $stat_data['success_counts'] . ',' . $stat_data['converage_dunning'] . ',' .
                $stat_data['converage_not_sure_dunning'] . ',' . $stat_data['converage_all'];

            file_put_contents($file_name, PHP_EOL . $show_str, FILE_APPEND);
        }
    }

    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListByDayForRequest($stat_list, $list_account, $file_name)
    {
        $list_account = array_column($list_account, 'name', 'id');
        $tempData = [];
        foreach ($stat_list as $key=>$item){
            $accountId = array_search($item['name_account'], $list_account);
            if ($accountId){
                $tempData[$accountId][] = $item;
            }else{
                $tempData[][] = $item;
            }
        }
        //组装导出格式
        $cvsArr = [];
        foreach ($tempData as $key=>$item){
            foreach ($item as $k=>$list){
                $tempArr = [];
                $tempArr['id'] = $list['id'];
                $tempArr['developer'] = $list['developer'];
                $tempArr['name_account'] = $list['name_account'];
                $tempArr['account_id'] = array_search($list['name_account'], $list_account);
                $tempArr['account_id'] = $tempArr['account_id'] ? $tempArr['account_id'] : '';
                foreach ($list['date_stat_show'] as $date=>$v){
                    $tempArr['date'] = $date;
                    $tempArr['access_counts']= $v['access_counts'];
                    $tempArr['success_counts']= $v['success_counts'];
                    $tempArr['converage_dunning']= $v['converage_dunning'];
                    $tempArr['converage_not_sure_dunning']= $v['converage_not_sure_dunning'];
                    $tempArr['coverage_both']= $v['coverage_both'];
                    $tempArr['converage_all']= $v['converage_all'];
                    $cvsArr[] = $tempArr;
                }
            }
        }

        // gen file
        $title_list = '日期,账号ID,账号名称,客户ID,客户名称,有效调用量,总调用量,催收覆盖率,疑似催收覆盖率,同时覆盖率,整体覆盖率';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);

        foreach ($cvsArr as $stat_data) {
            // 数据补全
            $specialChar = is_numeric($stat_data['developer']) ? "\"\t" . $stat_data['developer'] . "\"," : '"'.$stat_data['developer'].'",';
            $file_str = $stat_data['date'] . ',' . $stat_data['id'] . ',' . $specialChar
                . $stat_data['account_id'] . ',' . $stat_data['name_account'] . ',' .
                $stat_data['success_counts'] . ',' . $stat_data['access_counts']. ',"' . $stat_data['converage_dunning']
                . '%","' . $stat_data['converage_not_sure_dunning']. '%","' . $stat_data['coverage_both']. '%","' . $stat_data['converage_all'].'%"';
            $file_str = mb_convert_encoding($file_str,'gb2312','utf-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     *  补全需要的基本信息
     * @param $stat_data
     * @return array
     */
    protected function tidyBaseItemForFile($stat_data)
    {
        if (isset($stat_data['developer'])) {
            $stat_data['developer'] = str_replace(',', ' ', trim($stat_data['developer']));
        }
        return array_merge($this->item_base, $stat_data);
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
