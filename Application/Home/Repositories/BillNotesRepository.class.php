<?php

namespace Home\Repositories;

use Common\ORG\Page;
use Home\Model\BillNotesModel;
use Account\Model\CustomerModel;

class BillNotesRepository
{
    private $model;


    public function __construct()
    {
        $this->model = new BillNotesModel();
    }

    public function data($where = [], $field = '*', $order = null, $page = null)
    {
        $model = $this->model->field($field)->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        if (!is_null($page)) {
            $model = $model->limit($page->firstRow, $page->listRows);
        }
        return $model->select();
    }

    public function count($where = [])
    {
        $field = 'count(*) as c';
        $count = $this->data($where, $field);
        return $count[0]['c'];
    }

    public function getListData()
    {
        //获取get参数
        $input = $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);
        //获取符合当前条件的数据数量
        $count = $this->count($where);
        //分页
        $listRow = 50;
        $page    = new Page($count, $listRow);
        //查询数据
        $list_data = $this->data($where, '*', 'id desc', $page);
        $page      = $page->show();

        $arr = compact('list_data', 'page', 'input');
        return $arr;


    }

    protected function getWhereByParam($params)
    {
        $where = [];
        if(!empty($params['start_time'])){
            $where['cday'][] = ['egt', $params['start_time']];
        }
        if(!empty($params['end_time'])){
            $where['cday'][] = ['elt', $params['end_time']];
        }

        if (!empty($params['search_customer_id'])) {
            $arr = explode('#', $params['search_customer_id']);
            $where['customer_id'] = ['eq', $arr[0]];
        }
        return $where;
    }

    protected function getParamByGet()
    {
        $params = [];
        //交易日期
        $params['start_time'] = I('get.start_time', '', 'trim');
        $params['end_time']   = I('get.end_time', '', 'trim');
        //状态
        $params['search_customer_id'] = I('get.search_customer_id', '', 'trim');
        return $params;
    }

    /**
     * 获取客户信息
     */
    public function getCustomerInfo()
    {
        $customer_info = CustomerModel::getListCustomer(['is_delete'=>0], 'customer_id, name');
        return $customer_info;

    }
    /**
     * 添加备注时间
     */
    public function addBillNotes()
    {
        $data = I('post.');
        $customer_info = explode('#', $data['name']);
        $find_one = $this->model->where(['customer_id'=>$customer_info[0], 'cday'=>$data['cday']])->find();
        if(!empty($find_one)){
            return ['status' => 'error', 'msg' => '该客户配置日期已添加'];
        }
        $arr = [
            'cday' => $data['cday'],
            'customer_id' => $customer_info[0],
            'name' => $customer_info[1],
            'notes' => $data['notes'],
            'created_at' => time()
        ];
        $res = $this->model->add($arr);
        if($res){
            return ['status' => 'ok', 'msg' => '添加成功'];
        }
        return ['status' => 'ok', 'msg' => '添加失败'];
    }
    /**
     * 编辑备注时间
     */
    public function editBillNotes()
    {
        $data = I('post.');
        $customer_info = explode('#', $data['name2']);
        $find_one = $this->model->where(['id'=>$data['id2']])->find();

        if(empty($find_one)){
            return ['status' => 'error', 'msg' => '该客户配置不存在'];
        }
        //todo 时间处理
        if($data['cday2'] != $find_one['cday']){
            $info = $this->model->where(['customer_id'=>$customer_info[0], 'cday'=>$data['cday2']])->find();
            if(!empty($info)){
                return ['status' => 'error', 'msg' => '该客户配置日期已添加'];
            }
        }
        $arr = [
            'cday' => $data['cday2'],
            'customer_id' => $customer_info[0],
            'name' => $customer_info[1],
            'notes' => $data['notes2']
        ];
        $res = $this->model->where(['id'=>$data['id2']])->save($arr);
        if($res){
            return ['status' => 'ok', 'msg' => '编辑成功'];
        }
        return ['status' => 'ok', 'msg' => '编辑失败'];
    }
    /**
     * 获取编辑信息
     */
    public function getEditBillNotes()
    {
        $data = I('post.');
        $info = $this->model->where(['id'=>$data['id']])->find();
        if(empty($info)){
            return ['status'=>'error', 'msg'=>'数据不存在'];
        }
        $customer_info = CustomerModel::getListCustomer(['is_delete'=>0], 'customer_id, name');
        $select_option = '<option value="">请选择</option>';
        array_walk($customer_info, function($value) use (&$select_option, $info){
            $selected = '';
            if($value['customer_id'] == $info['customer_id']){
                $selected = 'selected';
            }
            $select_option .= '<option value="'.$value['customer_id'].'#'.$value['name'].'" '.$selected.'>'.$value['name'].'</option>';
        });

        $arr = [
            'name' => $select_option,
            'cday' => $info['cday'],
            'notes' => $info['notes']
        ];
        return ['status'=>'ok', 'msg'=>'成功', 'data'=>$arr];

    }
}