<?php

namespace Home\Repositories;

use Common\Common\CurlTrait;
use Common\Model\WechatWarningModel;

class WechatManageRepository extends BaseRepository
{
    use CurlTrait;

    /**
     * 编辑
     * @throws \Exception
     * @return string
     */
    public function edit()
    {
        // 限定访问的方式
        $this->limitMethodGet();

        // 校验条件
        $this->validateParamsForEdit();

        // 获取要编辑的对象
        return $this->getTheEditOne();
    }

    /**
     * 获取要编辑的对象
     * @return array
     */
    private function getTheEditOne()
    {
        $id = I('get.id', '', 'trim');
        $wechat = $this->getOneWechatByCondition(compact('id'));
        return json_encode($wechat, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @param array $where
     * @return array
     */
    private function getOneWechatByCondition(array $where)
    {
        return (new WechatWarningModel())->where($where)
            ->find();
    }

    /**
     * 校验条件
     * @throws \Exception
     */
    private function validateParamsForEdit()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            throw new \Exception('缺少必选参数ID');
        }
    }
}
