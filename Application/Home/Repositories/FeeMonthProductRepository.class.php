<?php
namespace Home\Repositories;

class FeeMonthProductRepository extends BaseRepository
{
    public function getCustomerAll()
    {
        $list = D('Account/Customer')->field('customer_id, name, company')->select();
        return array_column($list, null, 'customer_id');
    }

    public function getProductAll()
    {
        $list = D('Account/Product')->field('product_id, product_name')->select();
        return array_column($list, null, 'product_id');
    }

    public function getFeeProductList($where, $start, $limit = 25)
    {
        $order = $this->getFeeMonthOrder();
        $product = $this->getProductAll();
        $field = 'product_id, sum(fee_amount) as fee_amount, sum(fee_price) as fee_price';
        $list = D('FeeStat')->field($field)
                            ->where($where)
                            ->group('product_id')
                            ->order($order)
                            ->limit($start.','.$limit)
                            ->select();
        array_walk($list, function(&$v, $k, $p) {
            $v['product_name'] = $p[$v['product_id']]['product_name'];
        }, $product);

        return $list;
    }

    public function getFeeProductNum($where, $group = 'product_id')
    {
        $sub = D('FeeStat')->where($where)->group($group)->select(false);
        $res = D('FeeStat')->query('select count(*) as num from ('.$sub.') a limit 1');
        return isset($res[0]['num']) ? $res[0]['num'] : 0;
    }

    public function getFeeProductSum($where)
    {
        $field = 'sum(fee_amount) as fee_amount, sum(fee_price) as fee_price';
        return D('FeeStat')->field($field)->where($where)->find();
    }

    public function getFeeProductParam()
    {
        $start = I('get.start', '', 'trim');
        $end = I('get.end', '', 'trim');
        $start = $start ? date('Y-m-01', strtotime($start)) : date('Y-m-01', strtotime(
            '-1 month'));
        $end = $end ? date('Y-m-01', strtotime($end)) : date('Y-m-01', strtotime('-1 month'));

        $where['fee_date'] = ['between', [$start, $end]];

        $product_id = I('get.product_id', '', 'trim');
        if ($product_id) {
            $where['product_id'] = $product_id;
        }
        $product_name = I('get.product_name', '', 'trim');
        if ($product_name) {
            if ($product_id && $product_id != $product_name) {
                $where['product_id'] = 'no result';
            }
            $where['product_id'] = $product_name;
        }

        $customer_id = I('get.customer_id', '', 'trim');
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }

        return $where;
    }

    public function getFeeProductCustomer($where, $start, $limit = 25)
    {
        $order = $this->getFeeMonthOrder();
        $customer = $this->getCustomerAll();
        $field = 'customer_id, sum(fee_amount) as fee_amount, sum(fee_price) as fee_price';
        $list = D('FeeStat')->field($field)
                            ->where($where)
                            ->group('customer_id')
                            ->limit($start.','.$limit)
                            ->order($order)
                            ->select();
        array_walk($list, function(&$v, $k, $p) {
            $v['customer_name'] = $p[$v['customer_id']]['name'];
            $v['customer_company'] = $p[$v['customer_id']]['company'];
        }, $customer);

        return $list;
    }

    public function getFeeProductDetail($where)
    {
        $field = 'sum(fee_amount) as fee_amount, sum(fee_price) as fee_price, fee_date';
        $stat = D('FeeStat')->field($field)->where($where)->group('fee_date')->select();
        $stat = array_column($stat, null, 'fee_date');
        $list = $this->getMonthList();
        array_walk($list, function(&$v, $k, $p) {
            $date = $v.'-01';
            $m = isset($p[$date]) ? $p[$date] : ['fee_amount' => 0, 'fee_price' => 0];
            $m['month'] = $v;
            $v = $m;
        }, $stat);

        if (I('get.order')) {
            $order = $this->getFeeMonthOrder();
            $name = explode(' ', $order);
            $order = ($name[1] == 'asc') ? SORT_ASC : SORT_DESC;
            array_multisort(array_column($list, $name[0]), $order, $list);
        }

        return $list;
    }

    public function getMonthList()
    {
        $start = I('get.start', '', 'trim');
        $end = I('get.end', '', 'trim');
        $start = $start ? $start : date('Y-m', strtotime('-1 month'));
        $end = $end ? $end : date('Y-m', strtotime('-1 month'));
        $list = [];
        while ($end >= $start) {
            array_push($list, $end);
            $end = date('Y-m', strtotime($end.' -1 month'));
        }
        return $list;
    }

    public function getFeeMonthOrder()
    {
        $order = I('get.order', '', 'trim');

        if (!in_array($order, ['fee_price desc', 'fee_price asc', 'fee_amount desc', 'fee_amount asc'])) {
            $order = 'fee_price desc';
        }
        return $order;
    }
}