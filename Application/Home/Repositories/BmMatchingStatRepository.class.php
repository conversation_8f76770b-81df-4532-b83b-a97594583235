<?php

namespace Home\Repositories;

class BmMatchingStatRepository extends BaseRepository
{

    // 文件cell列表
    protected $list_cell = ['A', 'B', 'C'];

    protected $list_title = ["日期","单次查询量","批量查询量"];


    /**
     * 获取详情的统计信息
     * @return array
     * @throws \Exception
     */
    public function getStatDataForDetail()
    {
        // 限定条件
        $where = $this->genConditionForDetail();

        // 统计信息
        $list_stat_data = $this->getDetailStat($where);

        // 补全各天的数据
        $list_stat_data = $this->appendMissDayToDetail($list_stat_data);

        // 追加总计数据
        return $this->appendTotalForDetail($list_stat_data);
    }

    /**
     * 为详情添加总计
     * @param array $list_stat_data
     * @return array
     */
    protected function appendTotalForDetail($list_stat_data)
    {
        // 获取总计数据
        $list_total = $this->getTotalStatForDetail($list_stat_data);

        // 追加总计
        array_unshift($list_stat_data, $list_total);

        // 格式化数据
        return $this->formatStat($list_stat_data);
    }

    /**
     * 格式化统计数据
     * @param array $list_stat_data
     * @return array
     */
    protected function formatStat($list_stat_data)
    {
        return array_map(function($item_stat){
            $item_stat['itag'] = $this->formatNumber($item_stat['itag']);
            $item_stat['batch'] = $this->formatNumber($item_stat['batch']);
            return $item_stat;
        }, $list_stat_data);
    }

    /**
     * 为详情获取总计数据
     * @param array $list_stat_data
     * @return array
     */
    protected function getTotalStatForDetail($list_stat_data)
    {
        $list_total = [
            'day' => '总计',
            'itag' => 0,
            'detail' => 0
        ];

        // 计算总计
        array_walk($list_stat_data, function($item_stat) use (&$list_total){
            $list_total['itag'] += $item_stat['itag'];
            $list_total['batch'] += $item_stat['batch'];
        });
        return $list_total;
    }

    /**
     * 为详情补全各天的数据
     * @param array $list_stat_data 统计的原始数据
     * @return array
     * @throws \Exception
     */
    protected function appendMissDayToDetail($list_stat_data)
    {
        // 格式化
        $list_stat_data =  $this->tidyStatForDetail($list_stat_data);

        // 追加数据
        return $this->appendStatForDetail($list_stat_data);
    }

    /**
     * 为详情追加缺失的数据
     * @param array $list_stat_data 初步整理的原始数据
     * @return array
     */
    protected function appendStatForDetail($list_stat_data)
    {
        // 日期范围
        $list_day_show = $this->dateRange();

        // 容器 ['Y-m-d']
        $list_stat_info = [];

        array_walk($list_day_show, function($day) use (&$list_stat_info, $list_stat_data){
            $day_format = date('Ymd', strtotime($day));

            // 这一天是否有单次的调用， 如果没有的话 则是0
            $key_itag = $day_format . '_' . 'itag';
            $itag = array_key_exists($key_itag, $list_stat_data) ? $this->getValueWithDefault($list_stat_data[$key_itag], 'daily_used') : 0;

            // 这一天批量的调用量
            $key_batch = $day_format . '_' . 'batch';
            $batch = array_key_exists($key_batch, $list_stat_data) ? $this->getValueWithDefault($list_stat_data[$key_batch], 'daily_used') : 0;
            array_push($list_stat_info, compact('day', 'itag', 'batch'));
        });

        return  $list_stat_info;
    }

    /**
     * 为详情格式化数据
     * @param array $list_stat_data 原始的统计数据
     * @return array
     */
    protected function tidyStatForDetail($list_stat_data)
    {
        // 容器
        $list_tidy = [];
        array_walk($list_stat_data, function($item_stat) use (&$list_tidy){
            $project = $item_stat['project'];
            $day = $item_stat['daily_time'];
            $key_unique = $day . '_' . $project;

            $list_tidy[$key_unique] = $item_stat;
        });

        return $list_tidy;
    }



    /**
     * 统计详情的数据
     * @param array $where
     * @return array
     */
    protected function getDetailStat($where)
    {
        return D('AdminApistat')->where($where)
            ->select();
    }

    /**
     * 为统计详情生成条件
     * @throws \Exception
     */
    protected function genConditionForDetail()
    {
        // 限制id
        $limit_product = $this->limitProductForDetail();

        // 限制时间
        $limit_time = $this->limitTimeForDetail();

        return array_merge($limit_product, $limit_time);
    }

    /**
     * 统计详情限制时间
     * @return array
     * @throws \Exception
     */
    protected function limitTimeForDetail()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('begin', $request_body) || !array_key_exists('end', $request_body)) {
            throw new \Exception('需要同时限定开始时间 && 结束时间');
        }

        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $daily_time = [
           'between', [$begin, $end]
        ];

        return compact('daily_time');
    }

    /**
     * 统计详情限制product_id
     * @return array
     * @throws \Exception
     */
    protected function limitProductForDetail()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('product_id', $request_body)) {
            throw new \Exception('详情统计是必须限定产品ID的');
        }

        $apikey = $request_body['product_id'];
        return compact('apikey');
    }

    /**
     * 为列表获取GET请求的参数
     * @return array
     */
    public function paramsForDetail()
    {
        // 如果url里面包含了时间的信息 则直接返回GET参数
        $begin = I('get.begin', '');
        $end = I('get.end', '');
        if ($begin && $end) {
            return I('get.');
        }

        // 默认时间是当天
        $begin = $end = date('Y-m-d');
        return array_merge(I('get.'), compact('begin', 'end'));
    }

    /**
     * 排序
     * @return mixed
     * @throws \Exception
     */
    public function sortBy()
    {
        $response_body = $this->genParamsForPost();

        // 传递的数据
        $filter_field = $this->getValueWithDefault($response_body, 'filter_field', '');
        $filter_order = $this->getValueWithDefault($response_body, 'filter_order', '');
        $list_data = $this->getValueWithDefault($response_body, 'list_data', []);

        // 如果缺少必要元素 则不进行操作
        if (!$filter_field || !$filter_order) {
            return $list_data;
        }

        // 排序
        return $this->soreData($list_data, $filter_field, $filter_order);
    }

    /**
     * 数据排序
     * @param array $list_data 原始数据
     * @param string $filter_field 排序字段
     * @param string $filter_order 排序方式
     * @return mixed
     */
    protected function soreData($list_data, $filter_field, $filter_order)
    {
        // 总计不参与排序
        $item_total = array_shift($list_data);

        // 统计单元 && 统计单元格式化
        $list_item = array_column($list_data, $filter_field);
        $list_item = $this->formatDataForSort($list_item);

        // 排序
        $sort_order = strtolower($filter_order) === 'asc' ? SORT_ASC : SORT_DESC;
        array_multisort($list_item, $sort_order, SORT_NUMERIC, $list_data);

        // 总计不参与排序
        array_unshift($list_data, $item_total);
        return $list_data;
    }

    /**
     * 格式化统计元素(转成可以直接排序的数值型元素)
     * @param array $list_item
     * @return array
     */
    protected function formatDataForSort($list_item)
    {
        return array_map(function($item){
            // 如果统计原始是需要反格式化的化
            if (strpos($item, ',') !== false) {
                $item = str_replace(',', '', $item);
            }
            return $item;
        }, $list_item);
    }

    /**
     * 为列表获取GET请求的参数
     * @return array
     */
    public function paramsForList()
    {
        // 如果url里面包含了时间的信息 则直接返回GET参数
        $begin = I('get.begin', '');
        $end = I('get.end', '');
        if ($begin && $end) {
            return I('get.');
        }

        // 默认时间是当天
        $begin = $end = date('Y-m-d');
        return array_merge(I('get.'), compact('begin', 'end'));
    }

    /**
     * 总计详情导出文件
     * @throws \Exception
     */
    public function downloadTotalShow()
    {
        // 总计详情的数据
        $list_info = $this->getTotalInfoByDay();

        // 生成文件
        $this->genFileForTotal($list_info);

        // file_download return
        $file_name = $this->genFileNameForReport();

        $this->fileDownload($file_name);
    }

    /**
     * 生成文件
     * @param array $list_info 统计信息
     * @return string
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genFileForTotal($list_info)
    {

        // 加载PHPEXCEL
        require_once LIB_PATH . '/Org/PHPExcel/PHPExcel.php';

        $excel_php = new \PHPExcel();

        // 设置基本的属性
        $excel_php = $this->setPropertyForExcel($excel_php);

        // 将数据写 入excel
        $excel_php = $this->writeDataIntoExcel($excel_php, $list_info);

        // 设置sheet
        $excel_php = $this->setSheetForReport($excel_php);

        // 生成excel文件
        $this->genFileForReport($excel_php);
    }

    /**
     * 生成文件
     * @param \PHPExcel $excel_php
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genFileForReport($excel_php)
    {
        // 生成文件
        $objWriter = \PHPExcel_IOFactory::createWriter($excel_php, 'Excel2007');
        $name_report_file = $this->genFileNameForReport();
        ob_clean();
        $objWriter->save($name_report_file);
    }

    /**
     * 生成日报的文件名
     * @return string
     */
    protected function genFileNameForReport()
    {
        // 路径
        $path_name = RUNTIME_PATH;

        // 文件名
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = '邦秒配总计详情文档-' . $begin . '-' . $end . '.xlsx';;
        return $path_name . $file_name;
    }

    /**
     * 解决中文乱码的问题
     * @param $str
     * @return string
     */
    protected function convertUTF8($str)
    {
        if (empty($str)) return '';
        return iconv('utf-8', 'gb2312', $str);
    }

    /**
     * 为文件设置sheet
     * @param $excel_php
     * @return mixed
     */
    protected function setSheetForReport($excel_php)
    {
        // Rename worksheet
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $name_sheet = '邦秒配总计详情文档_' . $begin . '-' . $end;
        $excel_php->getActiveSheet()->setTitle($name_sheet);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $excel_php->setActiveSheetIndex(0);
        return $excel_php;
    }
    
    /**
     * 将数据写入excel
     * @param \PHPExcel $excel_php
     * @param array $list_info 统计信息
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     * @throws \Exception
     */
    protected function writeDataIntoExcel($excel_php, $list_info)
    {
        // 写入title
        $excel_php = $this->writeTitleForReport($excel_php);

        // 写入统计数据
        return $this->writeProductInfoForReport($list_info, $excel_php);
    }

    /**
     * 为总计详情写入产品的信息
     * @param array $list_info 统计信息
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \Exception
     */
    protected function writeProductInfoForReport($list_info, $excel_php)
    {
        // 开始写的行
        $row_begin = 2;
        array_walk($list_info, function ($item_info) use(&$row_begin, &$excel_php){
            $excel_php = $this->writeSingleProductIntoExcel($item_info, $row_begin, $excel_php);

            // 本轮单元结束，进行下一个产品
            $row_begin++;
        });

        // 全局居中
        $excel_php->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $excel_php->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        return $excel_php;
    }

    /**
     * 将单个产品的信息 写入excel
     * @param array $list_item 统计单元
     * @param integer $row 插入第几行
     * @param $excel_php
     * @return mixed
     */
    protected function writeSingleProductIntoExcel($list_item, $row, $excel_php)
    {
        $excel_php->setActiveSheetIndex(0)
            ->setCellValue('A' . $row, $list_item['date_key'])
            ->setCellValue('B' . $row, $list_item['itag'])
            ->setCellValue('C' . $row, $list_item['batch']);

        return $excel_php;
    }

    /**
     * 为日报写入标题
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     */
    protected function writeTitleForReport($excel_php)
    {
        array_walk($this->list_cell, function ($item_cell, $item_index) use (&$excel_php) {
            $value = $this->list_title[$item_index];
            $excel_php->setActiveSheetIndex(0)
                ->setCellValue($item_cell . 1, $value);

            // 加粗 居中
            $excel_php->setActiveSheetIndex(0)->getStyle($item_cell . 1)->applyFromArray(
                [
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER]
                ]
            );

            // 自动调整行宽
            $excel_php->setActiveSheetIndex(0)
                ->getColumnDimension($item_cell)
                ->setAutoSize(true);
        });

        // 这个是执行自动宽度的地方， 因为计算出来的一列的最大长度总是小了一些 所以代码中手动加上10
        $excel_php->setActiveSheetIndex(0)->calculateColumnWidths();
        return $excel_php;
    }

    /**
     * 为excel设置属性
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     */
    protected function setPropertyForExcel($excel_php)
    {
        $excel_php->getProperties()->setCreator("后台开发人员")
            ->setLastModifiedBy("后台开发人员")
            ->setTitle("邦秒配总计详情文档")
            ->setSubject("邦秒配总计详情文档")
            ->setDescription("调试的朋友你知道吗? 这个类是已经被废弃了的")
            ->setKeywords("office 2007 openxml php")
            ->setCategory("统计详情");
        return $excel_php;
    }
    
    /**
     * 获取总计详情(按天)
     * @return array
     */
    public function getTotalInfoByDay()
    {
        // 条件
        $where = $this->genConditionForDay();

        // 按天分组的信息
        $list_day_info = $this->getGroupInfoByDay($where);

        // 格式化 && 补全没有数据的那些天
        $list_day_data = $this->tidyDataForTotal($list_day_info);

        // 总计
        $total_stat = $this->computeTotalData($list_day_info);

        // 总计和各天数据合并
        return $this->mergeTotalAndDay($list_day_data, $total_stat);
    }

    /**
     * 合并总计和各天数据
     * @param array $list_day_data 各天数据
     * @param array $total_stat 总计数据
     * @return array
     */
    protected function mergeTotalAndDay($list_day_data, $total_stat)
    {
        array_unshift($list_day_data, $total_stat);

        // 格式化数据
        return array_map(function($item){
            $item['itag'] = $this->formatNumber($item['itag']);
            $item['batch'] = $this->formatNumber($item['batch']);
            return $item;
        }, $list_day_data);

    }

    /**
     * 为总计详情计算总计信息
     * @param array $list_day_info
     * @return array
     */
    protected function computeTotalData($list_day_info)
    {
        // 总计的容器
        $total_stat = [
            'batch' => 0,
            'itag' => 0,
            'date_key' => '总计'
        ];
        array_walk($list_day_info, function ($item) use (&$total_stat) {
            $project = $item['project'];
            $day_used = $item['day_used'];
            $total_stat[$project] += $day_used;
        });

        return $total_stat;
    }

    /**
     * 将原始数据整合成依天为单位包含单次和批量调用量的数据
     * @param array $list_day_info 分组得到的原始数据
     * @return array
     */
    protected function tidyDataForTotal($list_day_info)
    {
        // 以day为key的二位数组
        $list_day_data = $this->tidyDataToDay($list_day_info);

        // 补全没有数据的那些天
        return $this->appendMissDay($list_day_data);
    }

    /**
     * 向总计追加缺失的那些天
     * @param array $list_day_data
     * @return array
     */
    protected function appendMissDay($list_day_data)
    {
        // 需要的时间范围
        $list_date = $this->dateRange();

        // 格式化 [$date => [$project => $day_used]]
        $list_format = [];

        array_walk($list_date, function ($date) use ($list_day_data, &$list_format) {
            if (isset($list_day_data[$date]) && isset($list_day_data[$date]['itag'])) {
                $day_used = $list_day_data[$date]['itag'];
                $list_format[$date]['itag'] = $day_used;
            } else {
                $list_format[$date]['itag'] = 0;
            }

            if (isset($list_day_data[$date]) && isset($list_day_data[$date]['batch'])) {
                $day_used = $list_day_data[$date]['batch'];
                $list_format[$date]['batch'] = $day_used;
            } else {
                $list_format[$date]['batch'] = 0;
            }

            // vue-easytable 定制化的key
            $list_format[$date]['date_key'] = $date;
        });
        return $list_format;
    }

    /**
     * 需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = $this->genParamsForPost();
        $day_begin = date('Ymd', strtotime($request_body['begin']));
        $day_end = date('Ymd', strtotime($request_body['end']));

        // 容器 ['2018-10-01', '2018-10-02']
        $list_date = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            $date_format = date('Y-m-d', strtotime($day_end));
            array_push($list_date, $date_format);

            $day_end = date('Ymd', strtotime($day_end . '-1 day'));
        }

        return $list_date;
    }

    /**
     * 以天为key的二位数组
     * @param array $list_day_info
     * @return array
     */
    protected function tidyDataToDay($list_day_info)
    {
        // 容器 [2018-07-11 => [itag => 300]]
        $list_data = [];
        array_walk($list_day_info, function ($item) use (&$list_data) {
            $day = date('Y-m-d', strtotime($item['daily_time']));
            $project = $item['project'];
            $day_used = $item['day_used'];
            $list_data[$day][$project] = $day_used;
        });

        return $list_data;
    }

    /**
     * 按天分组的信息
     * @param array $where
     * @return array
     */
    protected function getGroupInfoByDay($where)
    {
        // user_id,username,max(score)
        $list_field = [
            'sum(daily_used) as day_used',
            'project',
            'daily_time'
        ];
        return D('AdminApistat')->where($where)
            ->group('daily_time,project')
            ->field($list_field)
            ->select();
    }

    /**
     * 为总计详情生成条件
     * @return array
     */
    protected function genConditionForDay()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && active
        $limit_base = $this->limitBaseInfo();

        // 时间限制
        $limit_time = $this->limitTime();

        // 条件整合
        return $this->tidyConditionForDay($limit_product, $limit_base, $limit_time);
    }

    /**
     * 条件整合
     * @param array $limit_product
     * @param array $limit_base
     * @param array $limit_time
     * @return array
     */
    protected function tidyConditionForDay($limit_product, $limit_base, $limit_time)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['apikey' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $apikey = [
                'in', $limit_base
            ];
            $limit_apikey = compact('apikey');
            return array_merge($limit_apikey, $limit_time);
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $apikey = '哈哈,永远不可能数据的';
        } else {
            $apikey = [
                'in', $limit_intersect
            ];
        }

        return array_merge(compact('apikey'), $limit_time);
    }

    /**
     * 时间限制
     * @return array
     */
    protected function limitTime()
    {
        $request_body = $this->genParamsForPost();

        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));

        $daily_time = [
            'between', [$begin, $end]
        ];
        return compact('daily_time');
    }

    /**
     * 限制基本的信息
     *
     */
    protected function limitBaseInfo()
    {
        // 限制状态
        $limit_active = $this->limitActive();

        // 限制签约类型
        $limit_contract_status = $this->limitContractStatus();

        // 符合条件的产品
        $where = array_merge($limit_active, $limit_contract_status);
        $list_product = $this->getProductByCondition($where);

        // 转成product_id的限制
        return array_map(function ($item) {
            return $item['id'];
        }, $list_product);
    }

    /**
     * 根据条件获取邦秒配产品
     * @param array $where
     * @return array
     */
    protected function getProductByCondition($where)
    {
        return D('AdminApikey')->where($where)
            ->select();
    }

    /**
     * 限制状态
     * @return array
     */
    protected function limitActive()
    {
        $request_body = $this->genParamsForPost();

        if (!array_key_exists('active', $request_body) || !$request_body['active']) {
            return [];
        }
        $active = $request_body['active'];
        return compact('active');
    }

    /**
     * 限制签约类型
     * @return array
     */
    protected function limitContractStatus()
    {
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('contract_status', $request_body) || !$request_body['contract_status']) {
            return [];
        }
        $contract_status = $request_body['contract_status'];
        return compact('contract_status');
    }

    /**
     * 限制 product_id
     */
    protected function limitProduct()
    {
        $request_body = $this->genParamsForPost();

        // 如果没有限制product_id，也没有限制account_id
        $limit_both_not = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_both_not) {
            return [];
        }

        // 如果只是限制了product_id
        $limit_product_only = (array_key_exists('product_id', $request_body) && $request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_product_only) {
            $product_id = [$request_body['product_id']];
            return compact('product_id');
        }

        // 如果只是限制了account_id
        $limit_account_only = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (array_key_exists('account_id', $request_body) && $request_body['account_id']);
        if ($limit_account_only) {
            $account_id = $request_body['account_id'];
            $product_id = $this->getRelationshipOfAccount($account_id);
            return compact('product_id');
        }

        // 同时限制了product_id && account_id
        return $this->limitProdutAndAccount($request_body);
    }

    /**
     * 同时限制了product_id && account_id
     * @param $request_body
     * @return array
     */
    protected function limitProdutAndAccount($request_body)
    {
        $product_id = $request_body['product_id'];
        $account_id = $request_body['account_id'];
        $list_product = $this->getRelationshipOfAccount($account_id);
        if (in_array($product_id, $list_product)) {
            return ['product_id' => [$product_id]];
        } else {
            return ['product_id' => ['永远不可能有值的哈哈']];
        }
    }

    /**
     * 获取特定客户下辖的邦秒配产品ID
     * @param integer $account_id
     * @return array
     */
    protected function getRelationshipOfAccount($account_id)
    {
        $type_id = 2;
        $where = compact('type_id', 'account_id');
        $list_relationship = D('FinanceAccountProduct')->where($where)
            ->select();

        return array_map(function ($item) {
            return $item['product_id'];
        }, $list_relationship);
    }

    /**
     * 获取产品列表
     * @return mixed
     */
    public function getProductList()
    {
        return D('AdminApikey')->index('id')->select();
    }

    /**
     * 获取列表的统计信息
     * @return array
     */
    public function getStatInfoForList()
    {
        // 获取数据
        $list_stat_data = $this->getStatDataForList();

        // 格式化数据
        return $this->formatData($list_stat_data);
    }

    /**
     * 格式化数据
     * @param $list_stat_data
     * @return array
     */
    protected function formatData($list_stat_data)
    {
        return array_map(function($item){
            $item['itag'] = $this->formatNumber($item['itag']);
            $item['batch'] = $this->formatNumber($item['batch']);
            return $item;
        }, $list_stat_data);
    }

    /**
     * 获取列表的统计数据
     * @return array
     */
    protected function getStatDataForList()
    {
        if (I('get.id', '')) {

            // 选定单一产品
            return $this->getStatInfoForOneProduct();
        } else {

            // 选中多条产品
            return $this->getStatInfoOfManyProductListForList();
        }
    }

    /**
     * 为列表生成统计信息(很多产品的时候)
     * @return array
     */
    protected function getStatInfoOfManyProductListForList()
    {
        // 满足条件的产品列表
        $list_product = $this->getProductListForListRequest();

        // 产品追加客户名称
        $list_product = $this->appendAccountInfo($list_product, 2);

        // 条件
        $where = $this->genConditionForList();

        // 以产品分组的所有产品的统计信息
        $list_product_stat_info = $this->getStatInfoGroupByProduct($where);

        // 整合产品和统计信息
        return $this->mergeProductAndStatInfo($list_product, $list_product_stat_info);
    }

    /**
     * 整合产品和统计信息
     * @param array $list_product  产品信息
     * @param array $list_product_stat_info 产品统计信息
     * @return array
     */
    protected function mergeProductAndStatInfo($list_product, $list_product_stat_info)
    {
        $personal_list_itag = $list_product_stat_info['personal_list_itag'];
        $personal_list_batch = $list_product_stat_info['personal_list_batch'];

        // 总计的元数据
        $total_stat = [
            'owner' => '总计',
            'itag' => 0,
            'batch' => 0
        ];
        foreach ($list_product as &$product) {

            // 个人信息的组合
            $stat['itag'] = isset($personal_list_itag[$product['id']]) ? $personal_list_itag[$product['id']] : 0;
            $stat['batch'] = isset($personal_list_batch[$product['id']]) ? $personal_list_batch[$product['id']] : 0;
            $product = array_merge($product, $stat);

            // 统计产生增量
            $total_stat['itag'] += $stat['itag'];
            $total_stat['batch'] += $stat['batch'];
        }

        array_unshift($list_product, $total_stat);
        return $list_product;
    }

    /**
     * 以产品为分组生成统计信息
     * @param string $where
     * @return array
     */
    protected function getStatInfoGroupByProduct($where)
    {
        $where_itag = $where . ' AND project = "itag"';
        $where_batch = $where . ' AND project = "batch"';

        // 单次统计
        $personal_list_itag = D('AdminApistat')
            ->where($where_itag)
            ->group('apikey')
            ->field('SUM(daily_used) AS used, apikey')
            ->select();


        // 批量统计
        $personal_list_batch = D('AdminApistat')
            ->where($where_batch)
            ->group('apikey')
            ->field('SUM(daily_used) AS used, apikey')
            ->select();

        // 产品主键作为索引
        $personal_list_itag = $personal_list_itag ? array_column($personal_list_itag, 'used', 'apikey') : [];
        $personal_list_batch = $personal_list_batch ? array_column($personal_list_batch, 'used', 'apikey') : [];

        return compact('personal_list_itag', 'personal_list_batch');
    }

    /**
     * 为列表(选定单一产品)生成统计信息
     */
    protected function getStatInfoForOneProduct()
    {
        // 满足条件的产品列表
        $list_product = $this->getProductListForListRequest();

        $list_product = $this->appendAccountInfo($list_product, 2);

        // 如果没有满足条件的产品被选中 则直接返回空
        if (!$list_product) {
            return [['owner' => '总计', 'itag' => 0, 'batch' => 0]];
        }

        // 条件
        $where = $this->genConditionForList();

        // 统计信息
        $list_product_stat = $total_data = $this->getStatListForOneProduct($where);

        // 总计信息
        $total_data['owner'] = '总计';

        // 整合选中产品的信息
        $list_product_stat['id'] = $product_id = I('get.id', '', 'trim');
        $list_product_stat['owner'] = $list_product[$product_id]['owner'];
        $list_product_stat['name_account'] = $list_product[$product_id]['name_account'];

        return [$total_data, $list_product_stat];
    }

    /**
     * 获取一个产品的统计信息
     * @param string $where
     * @return array
     */
    protected function getStatListForOneProduct($where)
    {
        $data_source = [
            'itag' => 0,
            'batch' => 0
        ];

        // total stat
        $stat_data = D('AdminApistat')
            ->where($where)
            ->group('project')
            ->field('SUM(daily_used) AS used,project')
            ->select();

        $stat_data = $stat_data ? array_column($stat_data, 'used', 'project') : $data_source;
        return array_merge($data_source, $stat_data);
    }

    /**
     * 为列表生成条件
     * @return string
     */
    public function genConditionForList()
    {
        // 时间限制
        $limit_time = $this->limitTimeForList();

        // 产品id限制
        $limit_id = $this->limitIdForList();

        return $limit_time . $limit_id;
    }

    /**
     * 为列表做产品ID的限制
     * @return string
     */
    protected function limitIdForList()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            return '';
        }
        return ' AND apikey=' . $id;
    }

    /**
     *  为列表做时间限制
     * @return string
     */
    protected function limitTimeForList()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        // 默认时间
        if (!$begin && !$end) {
            return 'daily_time=' . date('Ymd');
        }

        $where_date = $begin ? " daily_time>=" . date('Ymd', $begin) : '';
        $where_date .= $end ? " AND daily_time<=" . date('Ymd', $end) : '';
        return $where_date;
    }

    /**
     * 获取客户列表
     * @return array
     */
    public function getAccountList()
    {
        return D('FinanceAccounts')
            ->index('id')
            ->select();
    }

    /**
     *  为列表页获取产品列表
     * @return array
     */
    public function getProductListForListRequest()
    {
        // 条件
        $where = $this->genConditionsForList();
        $user_list = D('AdminApikey')
            ->where($where)
            ->field('owner,id')
            ->index('id')
            ->select();

        return $user_list;
    }

    /**
     * 为列表页面的请求生成条件
     */
    protected function genConditionsForList()
    {
        // 基础信息限制 (contract_status,active)
        $limit_base_info = $this->limitBaseInfoForList();

        // 限制登录的用户
        $limit_ids = $this->limitIdsForGet();

        return array_merge($limit_base_info, $limit_ids);
    }

    /**
     * 产品id的限制
     * @return array
     */
    protected function limitIdsForGet()
    {
        $id = I('get.id', '', 'trim');
        $account_id = I('get.account_id', '', 'trim');

        // id && account都没有限制
        if (!$account_id && !$id) {
            return [];
        }

        // 只限制了id
        if (!$account_id && $id) {
            return compact('id');
        }

        // 选定客户下的邦秒配产品ID列表
        $list_ids = $this->getListProductIdsOfAccountForGet();
        if (!$list_ids) {
            return ['id' => '选中的客户下面没有绑定产品'];
        }

        // 只是限制了客户
        if ($account_id && !$id) {
            $id = ['in', $list_ids];
            return compact('id');
        }

        // 同时限定了id和客户
        if (in_array($id, $list_ids)) {
            // 如果选中产品在选中的客户里面
            return compact('id');
        } else {
            // 如果选中的产品和客户冲突
            return ['id' => '永远不会有数据,哈哈'];
        }
    }

    /**
     * GET方式获取特定客户下辖的催收产品ID列表
     * @return array
     */
    protected function getListProductIdsOfAccountForGet()
    {
        $account_id = I('get.account_id', '', 'trim');

        if (!$account_id) {
            return [];
        }

        // 返回选中客户下辖的邦秒配产品ID
        $type_id = 2;
        $list_relationship = D('FinanceAccountProduct')
            ->where(compact('type_id', 'account_id'))
            ->select();

        $list_relationship = array_column($list_relationship, null, 'product_id');
        return array_keys($list_relationship);
    }

    /**
     * 限制基础信息
     * @return array
     */
    protected function limitBaseInfoForList()
    {
        $where = [];
        $contract_status = I('get.contract_status', '', 'trim');
        $active = I('get.active', '', 'trim');
        if ($contract_status) {
            $where['contract_status'] = $contract_status;
        }
        if ($active) {
            $where['active'] = $active;
        }
        return $where;
    }

}
