<?php
namespace Home\Repositories;

class CallCrawlerLogRepository extends BaseRepository
{
    protected $list = '/admin/crawler/log';

    protected $detail = '/admin/crawler/detail';

    public function getCrawlerLogList($limit = 20, $page = 0)
    {
        $param = $this->getCrawlerLogParam();
        $param['size'] = $limit;

        if ($page) {
            $param['page'] = $page;
        }

        $list = $this->getCrawlerLogListApi($param);

        $total = isset($list['page_info']['total']) ? $list['page_info']['total'] : 0;

        $list = isset($list['list']) ? $list['list'] : [];

        array_walk($list, function(&$v, $k) {
            $info = D('ErrorRepair')->field('id')->where(['sid' => $v['sid']])->find();
            $v['lock_month'] = $this->tidyLockMonth($v);
            $v['repairid'] = isset($info['id']) ? $info['id'] : '';
        });

        return compact('total', 'list');
    }

    /**
     * 获取缺失月份
     * @param $sid_info
     * @return string
     */
    protected function tidyLockMonth($sid_info)
    {
        $phone_bill_missing_month_list = isset($sid_info['phone_bill_missing_month_list']) ? $sid_info['phone_bill_missing_month_list'] : '';
        $call_log_possibly_missing_month_list = isset($sid_info['call_log_possibly_missing_month_list']) ? $sid_info['call_log_possibly_missing_month_list'] : '';
        $call_log_missing_month_list = isset($sid_info['call_log_missing_month_list']) ? $sid_info['call_log_missing_month_list'] : '';
        $call_log_cache_hit_month_list = isset($sid_info['call_log_cache_hit_month_list']) ? $sid_info['call_log_cache_hit_month_list'] : '';
        $call_log_part_missing_month_list = isset($sid_info['call_log_part_missing_month_list']) ? $sid_info['call_log_part_missing_month_list'] : '';

        $miss_month = '';
        if ($phone_bill_missing_month_list) {
            $miss_month .= '账单缺失: ' . implode(' ', $phone_bill_missing_month_list) . '<br/>';
        }
        if ($call_log_missing_month_list) {
            $miss_month .= '详单缺失: ' . implode(' ', $call_log_missing_month_list) . '<br/>';
        }
        if ($call_log_possibly_missing_month_list) {
            $miss_month .= '详单可能缺失: ' . implode(' ', $call_log_possibly_missing_month_list) . '<br/>';
        }
        if ($call_log_part_missing_month_list) {
            $miss_month .= '详单部分缺失: ' . implode(' ', $call_log_part_missing_month_list) . '<br/>';
        }
        if ($call_log_cache_hit_month_list) {
            $miss_month .= '缓存命中: ' . implode(' ', $call_log_cache_hit_month_list) . '<br/>';
        }
        return $miss_month;
    }

    public function getCrawlerLogDetail()
    {
        $sid = I('get.sid', '', 'trim');
        $list = $this->getCrawlerLogDetailApi(compact('sid'));

        array_walk($list, function (&$v, $k) {
            $v = json_encode($v, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        });
        return $list;
    }

    public function getCrawlerLogDownload($list, $limit = 1000)
    {
        $total = $list['total'];

        $list = $list['list'];

        $page = ceil($total/$limit);

        $file_name = RUNTIME_PATH . 'Cache/crawler_log.csv';
        $title = 'SID,Tel,地区,Telecom,Channel,Status,CID,缺失月份,StartTime';
        $title = mb_convert_encoding($title,'GBK','UTF-8');
        file_put_contents($file_name, $title);

        for($i = 1; $i <= $page; $i++) {
            if ($i > 1) {
                $list = $this->getCrawlerLogList($limit, $i);
                $list = $list['list'];
            }
            foreach ($list as $key => $value) {
                $area = $value['tel_info']['province'].$value['tel_info']['city'];
                $channel = (isset($value['crawler_channel']) && $value['crawler_channel']) ? $value['crawler_channel'] : 'yulore';
                $lock_month = str_replace('<br/>', ' ', $value['lock_month']);
                // 数据补全
                $file_str = '"'.$value['sid'].'","'.$value['tel'].'","'.$area.'","'.$value['tel_info']['telecom'].'","'.$channel.'","'.$value['status'].' '.$value['message'].'","'.$value['cid'].'","'.$lock_month.'","'.date('Y-m-d H:i:s', $value['start_time']).'"';

                $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
                file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
            }
        }
        $this->fileDownload($file_name);

    }

    public function getCrawlerLogParam()
    {
        $start_time = I('get.begin', strtotime(date('Y-m-d')), 'strtotime');
        $end_time = I('get.end', strtotime(date('Y-m-d')), 'strtotime');
        $end_time += 60*60*24-1;

        $channel = I('get.crawler_channel', '', 'trim');
        $telecom = I('get.flow_type', '', 'trim');
        $province = I('get.province', '', 'trim');
        $sid = I('get.sid', '', 'trim');
        $tel = I('get.tel', '', 'trim');
        $cid = I('get.cid', '', 'trim');
        $status = I('get.status', '', 'trim');

        $error_level = I('get.error_level', '', 'trim');

        $page = I('get.page', 1, 'intval');

        $param = array_filter(compact('start_time', 'end_time', 'channel', 'telecom', 'province', 'sid', 'tel', 'cid', 'error_level', 'page'));
        if ($status !== '') {
            $param['status'] = $status;
        }
        return $param;
    }

    public function getCrawlerLogListApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->list;
        $url = $param ? $url.'?'.http_build_query($param, '', '&') : $url;
        $res = $this->getCurl('GET', $url);
        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }

    public function getCrawlerLogDetailApi($param)
    {
        $domain = C('CRS_API_CONFIG')['domain'];
        $url = $domain.$this->detail;
        $url = $param ? $url.'?'.http_build_query($param, '', '&') : $url;
        $res = $this->getCurl('GET', $url);

        if (!$res) {
            return [];
        }
        $res = json_decode($res, true);
        if ($res['status'] != 0) {
            return [];
        }
        return $res['data'];
    }
}