<?php

namespace Home\Repositories;

class GrayUpgradeRepository
{

    /**
     * 获取执行版本信息
     * @throws \Exception
     */
    public function getSpecialMsg()
    {
        // 参数
        $where = $this->genConditionForSpecial();
        return D('FinanceGrayUpgrade')
            ->where($where)
            ->find();
    }

    /**
     * 为查看详情生成参数
     * @return array
     * @throws \Exception
     */
    protected function genConditionForSpecial()
    {
        $id = I('get.id', '', 'trim');
        if (!$id) {
            throw new \Exception('传参除了问题，请回到之前的页面重新打开');
        }

        return compact('id');
    }

    /**
     * 获取列表
     */
    public function getGrayUpgradeList()
    {
        // 生成参数
        $where = $this->genConditionsForList();

        // 查询结果
        $list_upgrade = $this->getListOfUpgrade($where);

        // 创建时间格式化
        return $this->timestampToDate($list_upgrade);
    }

    /**
     * 创建时间格式化
     * @param $list_upgrade
     * @return array
     */
    protected function timestampToDate($list_upgrade)
    {
        return array_map(function($item){
            $item['created_at'] = date('Y-m-d H:i:s', $item['created_at']);
            return $item;
        }, $list_upgrade);
    }

    /**
     * 获取灰度更新的列表
     * @param array $where
     * @return array
     */
    protected function getListOfUpgrade($where)
    {
        return D('FinanceGrayUpgrade')
            ->where($where)
            ->order('id desc')
            ->select();
    }

    /**
     * 为列表参数生成参数
     */
    protected function genConditionsForList()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        $upgrade_name = trim($request_body['upgrade_name']);
        $upgrade_key = trim($request_body['upgrade_key']);

        return array_filter(compact('upgrade_name', 'upgrade_key'), function ($item){
            return $item !== '';
        });
    }

    /**
     * 添加灰度发布的版本
     * @throws \Exception
     */
    public function add()
    {
        try {
            D('FinanceGrayUpgrade')->startTrans();

            // 检查参数
            $this->checkParamsForAdd();

            // 生成参数
            $params = $this->genConditionsForAdd();

            // 添加数据
            $result_add = D('FinanceGrayUpgrade')->add($params);

            if ($result_add !== false) {
                // 请求API写入数据
                $this->requestUpgradeWriteApi();

                // 提交
                D('FinanceGrayUpgrade')->commit();
            }
        } catch (\Exception $e) {
            D('FinanceGrayUpgrade')->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 请求灰度发布API
     * @throws \Exception
     */
    protected function requestUpgradeWriteApi()
    {
        try {
            // 生成参数
            $post_data = $this->genParamsForUpgradeWrite();

            // URL
            $url = $this->getUrlForUpgradeApi();

            // 发送请求
            $result_request = $this->curlRequestForPost($url, $post_data);

            // 如果请求失败 抛出错误
            if ($result_request['status'] != 0) {
                throw new \Exception($result_request['msg']);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取灰度发布写入Redis API的配置
     * @return mixed
     * @throws \Exception
     */
    protected function getUrlForUpgradeApi()
    {
        if (!isset(C('LIST_API_URL')['gray_upgrade_write'])) {
            throw new \Exception('缺少灰度发布写入Redis API的配置');
        }

        $url = C('LIST_API_URL')['gray_upgrade_write'];
        if (!$url) {
            throw new \Exception('灰度发布写入Redis API的配置不可以为空');
        }
        return $url;
    }

    /**
     * 发送post请求
     * @param $url
     * @param $post_data
     * @return mixed
     * @throws \Exception
     */
    protected function curlRequestForPost($url, $post_data)
    {
        if (is_array($post_data)) {
            $post_data  = http_build_query($post_data);
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 10);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $list_stat = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            throw new \Exception(curl_error($curl));
        }
        curl_close($curl);
        if (is_string($list_stat)) {
            return json_decode($list_stat, true);
        }
        return $list_stat;
    }

    /**
     * 为版本更新API生成参数
     */
    protected function genParamsForUpgradeWrite()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        $key = $request_body['upgrade_key'];
        $value = $request_body['upgrade_body'];
        $timestamp = time();

        // 生成签名
        $sign = $this->genSign($key, $value, $timestamp);
        return compact('key', 'value', 'timestamp', 'sign');
    }


    protected function genSign($key, $value, $timestamp)
    {
        $post = compact('key', 'value', 'timestamp');
        ksort($post);
        $str = '';
        foreach ($post as $k => $v) {
            if ($k !== 'sign') {
                $str .= $k . $v;
            }
        }
        $private_key = C('LIST_API_URL')['private_key'];
        $str .= md5($private_key . date('Ymd'));
        return md5($str);
    }


    /**
     * 为添加检查参数
     * @throws \Exception
     */
    protected function checkParamsForAdd()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 检查名称
        $upgrade_name = $request_body['upgrade_name'];
        $this->limitUpgradeNameForAdd($upgrade_name);

        // 检查body是否是json
        $this->limitUpgradeBodyForAdd($request_body['upgrade_body']);
    }

    /**
     * 限制策略
     * @param string $upgrade_body
     * @throws \Exception
     */
    protected function limitUpgradeBodyForAdd($upgrade_body)
    {
        if (!$upgrade_body) {
            throw new \Exception('请输入策略值');
        }
        if (!json_decode($upgrade_body, true)) {
            throw new \Exception('输入的策略值不是合法的JSON');
        }
    }

    /**
     * 版本名称限制
     * @param string $upgrade_name 版本名称
     * @throws \Exception
     */
    protected function limitUpgradeNameForAdd($upgrade_name)
    {

        if (!$upgrade_name) {
            throw new \Exception('请输入版本名称');
        }

        $name_unique_check = D('FinanceGrayUpgrade')
            ->where(compact('upgrade_name'))
            ->count();
        if ($name_unique_check) {
            throw new \Exception('版本名称已经被占用，请重新输入');
        }
    }

    /**
     * 为添加新版本生成条件
     * @return array
     */
    protected function genConditionsForAdd()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 从payload中获取参数, 校验在前端做
        $upgrade_name = trim($request_body['upgrade_name']);
        $upgrade_key = trim($request_body['upgrade_key']);
        $upgrade_body = trim($request_body['upgrade_body']);
        $remark = trim($request_body['remark']);
        $handle_person = trim($_SESSION['site_login_name']);
        $created_at = $updated_at = time();

        return compact('upgrade_body', 'upgrade_key', 'upgrade_name', 'remark', 'handle_person', 'created_at', 'updated_at');
    }
}
