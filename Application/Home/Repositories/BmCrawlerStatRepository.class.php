<?php

namespace Home\Repositories;

class BmCrawlerStatRepository extends BaseRepository
{
    protected $flow_map = ['10086' =>'移动', '10010' => '联通', '189' => '电信'];

    protected $record_source;

    public function __construct($record_source = 1)
    {
        $this->record_source = $record_source;
    }
    /**
     * 获取产品列表
     * @return mixed
     */
    public function getProductListAll()
    {
        return D('Auth')->where(['record_source' => $this->record_source])->field('id, developer')->select();
    }

    /**
     * 获取客户列表
     * @return array
     */
    public function getAccountListAll()
    {
        return D('FinanceAccounts')->field('id, name')->index('id')->select();
    }

    /**
     * 获取列表参数
     * @return param
     */
    public function getCrawlerListParam()
    {
        //开始时间
        $begin = I('get.begin', '', 'strtotime');
        //结束时间
        $end = I('get.end', '', 'strtotime');
        //渠道
        $choose_crawler_channel = I('get.crawler_channel', '', 'trim');
        //地区
        $province = I('get.province', '', 'trim');
        //运营商
        $flow_type = I('get.flow_type', '', 'trim');

        $where['start_date'] = $begin ? date('Ymd', $begin) : date('Ymd');
        $where['end_date'] = $end ? date('Ymd', $end) : date('Ymd');

        if ($province) {
            $where['province'] = $province;
        }
        if ($flow_type) {
            $where['telecom'] = $this->flow_map[$flow_type];
        }
        if ($choose_crawler_channel) {
            $where['crawler_channel'] = $choose_crawler_channel;
        }

        return $where;
    }

    /**
     * 获取详情参数
     * @return
     */
    public function getCrawlerDetailParam()
    {
        //开始时间
        $begin = I('get.begin', '', 'strtotime');
        //结束时间
        $end = I('get.end', '', 'strtotime');
        //渠道
        $choose_crawler_channel = I('get.crawler_channel', '', 'trim');
        //地区
        $province = I('get.province', '', 'trim');
        //运营商
        $flow_type = I('get.flow_type', '', 'trim');

        $where['start_date'] = $begin ? date('Ymd', $begin) : date('Ymd', strtotime("-1 month"));
        $where['end_date'] = $end ? date('Ymd', $end) : date('Ymd');

        if ($province) {
            $where['province'] = $province;
        }
        if ($flow_type) {
            $where['telecom'] = $this->flow_map[$flow_type];
        }
        if ($choose_crawler_channel) {
            $where['crawler_channel'] = $choose_crawler_channel;
        }

        return $where;
    }

    /**
     * 根据GET方法传递的信息 获取产品的信息
     * @return list
     */
    public function getProductList()
    {
        // 条件
        $where = $this->getProductListParam();

        $list = D('Auth')->field('id,developer')
                         ->where($where)
                         ->index('id')
                         ->select();

        if (!$list) {
            return [];
        }
        $account_list = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_keys($list), 1);
        array_walk($list, function(&$v, $k, $p) {
            $v['account_id'] = isset($p[$v['id']]['account_id']) ? $p[$v['id']]['account_id'] : '';
            $v['name_account'] = isset($p[$v['id']]['account_name']) ? $p[$v['id']]['account_name'] : '';
            //这是是为了在邦秒爬统计（爬虫）列表页中排序用到的数据 于2018/12/05由xiaogang.cui增加
            $v['created_at'] = isset($p[$v['id']]['created_at']) ? $p[$v['id']]['created_at'] : 0;
        }, $account_list);

        return $list;
    }

    /**
     * 账号条件
     * @return array
     */
    protected function getProductListParam()
    {
        $contract_status = I('get.contract_status', '', 'trim');
        $status = I('get.status', '', 'trim');
        $id = I('get.id', '', 'trim');
        $account_id = I('get.account_id', '', 'trim');
        $where = ['record_source' => $this->record_source];
        if ($contract_status) {
            $where['contract_status'] = $contract_status;
        }
        if ($status) {
            $where['status'] = $status;
        }
        if ($id) {
            $where['id'] = $id;
        }
        if ($account_id) {
            // 选定客户下的邦秒配产品ID列表
            $list_ids = $this->getUserIdByAccountId($account_id);
            if ($id && !in_array($id, $list_ids)) {
                $where['id'] = 'what happened';
            } elseif (!$id) {
                $where['id'] = $list_ids ? ['in', $list_ids] : 'what happened';
            }
        }
        return $where;
    }

    /**
     * 根据客户ID获取账号ID
     * @param  number $account_id 客户ID
     * @return array
     */
    public function getUserIdByAccountId($account_id)
    {
        $type_id = 1;
        $account_info = D('FinanceAccountProduct')->where(compact('type_id', 'account_id'))
                                                  ->field('account_id,product_id')
                                                  ->index('product_id')
                                                  ->select();
        $ids = array_keys($account_info);
        return $ids;
    }

    /**
     * 提供一个联动的用户筛选
     */
    public function clientList()
    {
        $contract_status = I('post.contract_status', '', 'trim');
        $status = I('post.status', '', 'trim');

        $where = ['record_source' => $this->record_source];
        if ($contract_status) {
            $where['contract_status'] = $contract_status;
        }
        if ($status) {
            $where['status'] = $status;
        }

        $user_list = D('Auth')->field('id,developer')
                              ->where($where)
                              ->index('id')
                              ->select();

        return $user_list;
    }

    /**
     * 详情页要展示的date list
     * @return array
     */
    public function dateList()
    {
        $date_list = [];
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end = empty($end) ? strtotime(date('Y-m-d 23:59:59')) : strtotime($end);
        $begin = empty($begin) ? ($end-86400*30) : strtotime($begin);
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }

        return $date_list;
    }
}
