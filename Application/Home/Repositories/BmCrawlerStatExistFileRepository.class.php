<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-23 16:14:14
 */
namespace Home\Repositories;

Class BmCrawlerStatExistFileRepository extends BaseRepository
{
    /**
     * 列表导出
     * @param  array $list 列表统计数据
     * @return
     */
    public function getCrawlerStatExistList($list)
    {
        $file_name = RUNTIME_PATH . 'Cache/crawler_stat_exist_list.csv';
        // gen file
        $title_list = '账号ID,账号名称,客户名称,总查询量,有效查询量,有效查询率,报告生成量,报告生成率';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);
        foreach ($list as $value) {
            $total_nums = isset($value['total_nums']) ? $value['total_nums'] : 0;
            $authen_nums = isset($value['authen_nums']) ? $value['authen_nums'] : 0;
            $authen_pct = isset($value['authen_pct']) ? $value['authen_pct'] : "0.00%";
            $report_nums = isset($value['report_nums']) ? $value['report_nums'] : 0;
            $report_pct = isset($value['report_pct']) ? $value['report_pct'] : "0.00%";
            // 数据补全
            $file_str = '"'.$value['id'].'","'.$value['developer'].'","'.$value['name_account'].'","'.$total_nums.'","'.$authen_nums.'","'.$authen_pct.'","'.$report_nums.'","'.$report_pct.'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }

    /**
     * 详情导出
     * @param  array $list 详情统计数据
     * @return
     */
    public function getCrawlerStatExistDetail($list)
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $id = I('get.id', '', 'intval');

        $begin = date('Ymd', $begin);
        $end = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/crawler_stat_exist_'.$begin.'_'.$end.'_'.$id.'.csv';
        // gen file
        $title_list = '日期,总查询量,有效查询量,有效查询率,报告生成量,报告生成率';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);

        foreach ($list as $key => $value) {
            $total_nums = isset($value['total_nums']) ? $value['total_nums'] : 0;
            $authen_nums = isset($value['authen_nums']) ? $value['authen_nums'] : 0;
            $authen_pct = isset($value['authen_pct']) ? $value['authen_pct'] : "0.00%";
            $report_nums = isset($value['report_nums']) ? $value['report_nums'] : 0;
            $report_pct = isset($value['report_pct']) ? $value['report_pct'] : "0.00%";
            // 数据补全
            $file_str = '"'.$key.'","'.$total_nums.'","'.$authen_nums.'","'.$authen_pct.'","'.$report_nums.'","'.$report_pct.'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->fileDownload($file_name);
    }
}