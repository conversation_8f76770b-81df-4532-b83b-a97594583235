<?php

namespace Home\Repositories;

use Common\ORG\Page;

class FinanceAccountRepository
{
    /*
     * 产品类型对应的标识
     * */
    protected $type_field_list = [
        1 => 'crawler',
        2 => 'matching',
        3 => 'cuishou'
    ];

    /**
     * post方式通过id修改客户 && relationship
     * @throws \Exception
     */
    public function updateAccountAndRelationshipByIdForPost()
    {
        // 检查参数
        $this->checkParamsByUpdateForPost();

        // 更新客户 && relationship
        $this->updateByIdForPost();
    }

    /**
     *  更新
     * @throws \Exception
     */
    protected function updateByIdForPost()
    {
        $model = M('', '', 'DB_FINANCE');
        $id = I('post.id', '', 'trim');
        try {
            $model->startTrans();

            // 更新客户
            $params_account = $this->getParamForUpdate();
            $result_update_account = $model->table('finance_accounts')->where(compact('id'))->save($params_account);

            if ($result_update_account === false){
                throw new \Exception('数据库更新失败，请刷新后再试');
            } else {
                $model->commit();
            }
        } catch (\Exception $e) {
            // 回滚错误
            $model->rollback();
            $msg = $e->getMessage();
            //错误再次抛出，Controller里面又接收的
            throw new \Exception($msg);
        }
    }

    /**
     * Post方式获取新的relationship的参数
     */
    protected function getNewRelationshipForPostById()
    {
        $account_id = I('post.id', '', 'trim');
        $list_crawler = I('post.crawler', '');
        $list_matching = I('post.matching', '');
        $list_cuishou = I('post.cuishou', '');

        // 循环获取需要格式的参数
        $data_loop_crawler = $this->loopDataForRelationship($list_crawler, $account_id, 1);
        $data_loop_matching = $this->loopDataForRelationship($list_matching, $account_id, 2);
        $data_loop_cuishou = $this->loopDataForRelationship($list_cuishou, $account_id, 3);
        return array_merge($data_loop_crawler, $data_loop_cuishou, $data_loop_matching);
    }

    /**
     * 将form表单转成可以直接插入数据库的数据
     * @param array $list_data 表单数据[1,2,3,4]
     * @param integer $account_id 客户ID
     * @param integer $type_id 1爬2配3催收分
     * @return array
     */
    protected function loopDataForRelationship($list_data, $account_id, $type_id)
    {
        if (!$list_data) {
            return [];
        }

        return array_map(function ($product_id) use ($account_id, $type_id) {
            $created_at = $updated_at = time();
            return compact('type_id', 'product_id', 'account_id', 'created_at', 'updated_at');
        }, $list_data);
    }


    /**
     * 通过ID GET的方式获取客户的信息 && 一对多的关系
     * @return array
     */
    public function getAccountAndRelationByIdForGet()
    {
        $id = I('get.id', '', 'trim');

        // 获取客户信息
        $account = D('FinanceAccounts')->where(compact('id'))->find();

        // 获取客户 && 产品的一对多的关系
//        $relation_ship = $this->getRelationshipByIdForGet();

        return compact('account', 'relation_ship');
    }

    /**
     * 通过ID　GET的请求方式获取制定客户的relationship关系(一对多)
     * 按照格式，整理出各个类型产品的主键id字符串(编辑界面strpos展示)
     * @return array
     */
    protected function getRelationshipByIdForGet()
    {
        $relationship = [
            'crawler' => [],
            'matching' => [],
            'cuishou' => []
        ];

        // 产品分类
        $account_id = I('get.id', '', 'trim');
        $relationship_list = D('FinanceAccountProduct')->where(compact('account_id'))
            ->field('group_concat(product_id) as product_ids, type_id')->group('type_id')->select();

        if (!$relationship_list) {
            return $relationship;
        }

        // 变换分类的下标
        array_walk($relationship_list, function ($relationship_item) use (&$relationship) {
            // 按照格式 整理出各个类型产品的主键id字符串
            $type_id = $relationship_item['type_id'];
            $type_field = $this->type_field_list[$type_id];
            $product_ids = $relationship_item['product_ids'];

            $relationship[$type_field] = explode(',', $product_ids);
        });
        return $relationship;
    }

    /**
     * 创建客户
     */
    public function createAccount()
    {
        // 检查参数
        $this->checkParamsByCreateForPost();

        // 获取参数
        $params = $this->getParamForCreate();
        return D('FinanceAccounts')->add($params);
    }

    /**
     * 修改客户操作人
     * @param $id
     * @param $admin
     * @return bool|null
     */
    public function updateAdminInfo($id, $admin){
        if (empty($id) || empty($admin)){
            return null;
        }
        $data['admin'] = $admin;
        return D('FinanceAccounts')->where('id='.$id)->save($data);
    }

    /**
     * 检查新建客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByCreateForPost()
    {
        $email = I('post.email', '', 'trim');
        $name = I('post.name', '', 'trim');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('请检查邮箱的格式');
        }
        if (!$name) {
            throw new \Exception('请输入客户名称');
        }
        // name email的唯一性
        $name_unique = D('FinanceAccounts')->where(compact('name'))->count();
        if ($name_unique) {
            throw new \Exception('客户名称已经被占用，请更换客户名称');
        }
        $email_unique = D('FinanceAccounts')->where(compact('email'))->count();
        if ($email_unique) {
            throw new \Exception('邮箱已经被占用,请更换邮箱');
        }
    }

    /**
     * 检查更新客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByUpdateForPost()
    {
        // 检查邮箱是否唯一
        $this->checkUniqueEmailForUpdate();

        // 检查Name是否唯一
        $this->checkUniqueNameForUpdate();
    }

    /**
     * 检查更新传递的邮件是否唯一
     * @throws \Exception
     */
    protected function checkUniqueEmailForUpdate()
    {
        $id = I('post.id', '', 'trim');
        $email = I('post.email', '', 'trim');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('请检查邮箱的格式');
        }
        $email_unique = D('FinanceAccounts')->where(compact('email'))->find();
        if ($email_unique && $email_unique['id'] != $id) {
            throw new \Exception('客户邮箱已经被占用,请更换邮箱');
        }
    }

    /**
     * 检查更新传递的name(id)是否是唯一的
     * @throws \Exception
     */
    protected function checkUniqueNameForUpdate()
    {
        $id = I('post.id', '', 'trim');
        // name email的唯一性
        $name = I('post.name', '', 'trim');
        if (!$name) {
            throw new \Exception('请输入客户名称');
        }
        $name_unique = D('FinanceAccounts')->where(compact('name'))->find();
        if ($name_unique && $name_unique['id'] !=$id) {
            throw new \Exception('客户名称已经被占用，请切更换客户名称');
        }
    }

    /**
     * 检测 只允许客户绑定一个邦秒爬产品
     * @throws \Exception
     */
    protected function onlyOneCrawlerForUpdate()
    {
        $list_crawler = I('post.crawler', '');
        if (count($list_crawler) > 1) {
            throw new \Exception('目前一个客户只允许绑定一个邦秒爬产品');
        }
    }

    /**
     * 为创建用户过滤参数
     */
    protected function getParamForCreate()
    {
        $name = I('post.name', '', 'trim');
        $company = I('post.company', '', 'trim');
        $email = I('post.email', '', 'trim');
        $status = I('post.status', '', 'trim');
        $type = I('post.type', '', 'trim');
        $operator = I('post.operator', '', 'trim');
        $salesman = I('post.salesman', '', 'trim');

        $created_at = $updated_at = time();

        // 生成password
        $password = $this->genPasswordForCreate();
        return compact('name', 'company', 'email', 'status', 'password', 'updated_at', 'created_at', 'type', 'operator', 'salesman');
    }

    /**
     * 为创建用户过滤参数
     */
    protected function getParamForUpdate()
    {
        $name = I('post.name', '', 'trim');
        $company = I('post.company', '', 'trim');
        $email = I('post.email', '', 'trim');
        $status = I('post.status', '', 'trim');
        $type = I('post.type', '', 'trim');
        $operator = I('post.operator', '', 'trim');
        $salesman = I('post.salesman', '', 'trim');
        $updated_at = time();

        // 生成password
        return compact('name', 'company', 'email', 'status', 'password', 'updated_at', 'type', 'operator', 'salesman');
    }

    /**
     * 为新建的客户生成密码
     * @return string
     */
    protected function genPasswordForCreate()
    {
        return md5(C('USER_PWD_SALT') . md5(C('USER_DEFAULT_PASSWORD')));
    }

    /**
     * 无条件获取邦秒爬的产品列表
     * @return array
     */
    public function getProductListCrawler()
    {
        return D('Auth')->field('id, developer')->select();
    }

    /**
     *  无条件获取邦秒配的产品列表
     */
    public function getProductListMatching()
    {
        return D('AdminApikey')->field('id,owner')->select();
    }

    /**
     *  无条件获取催收分的产品列表
     */
    public function getProductListCuishou()
    {
        $fields = [
            'id' => 1,
            'developer' => 1,
            '_id' => 0
        ];
        return D('CuishouUser')->field($fields)->select();
    }

    /**
     * GET方式获取用户列表客户
     */
    public function accountListForGet()
    {
        $where = $this->getAccountConditionByGetForSelect2();
        return D('FinanceAccounts')->where($where)->select();
    }

    /**
     * (列表）GET请求的方式获取客户信息 && 产品信息
     * @return array
     */
    public function getAccountWithProductForListByGet()
    {
        // 分页获取客户列表
        $list = $this->getAccountByGetForPage();
        $obj_page = $list['obj_page'];
        $list_account = $list['list_account'];

        // 客户列表加载产品信息
        $list_account = $this->fillAccountWithProduct($list_account);

        return compact('list_account', 'obj_page');
    }

    /**
     * 客户管理编辑页面重置密码功能
     */
    public function updatePwdById($id){
        // 生成password
        $data['password'] = $this->genPasswordForCreate();
        $data['updated_at'] = time();
       return D('FinanceAccounts')->where('id='.$id)->save($data);
    }

    /**
     *  产品加载到客户上面
     * @param array $list_account
     * @return array
     */
    protected function fillAccountWithProduct($list_account)
    {
        if (!$list_account) {
            return [];
        }

        $list_account = array_column($list_account, null, 'id');

        // 获取客户对应的产品
        $list_product = $this->getProductByAccount($list_account);

        // 组合产品和客户
        return $this->mergeProductAndAccount($list_product, $list_account);
    }

    /**
     * 合并产品信息和客户列表
     * @param $list_product
     * @param $list_account
     * @return mixed
     */
    protected function mergeProductAndAccount($list_product, $list_account)
    {
        foreach ($list_product as $item_product) {
            $account_id = $item_product['account_id'];
            $type_id = $item_product['type_id'];
            $type_field = $this->type_field_list[$type_id];
            $list_account[$account_id][$type_field][] = $item_product;
        }
        return $list_account;
    }

    /**
     * 获取客户对应的产品
     * @param array $list_account 客户列表
     * @return array
     */
    protected function getProductByAccount($list_account)
    {
        $list_account_id = array_keys($list_account);

        // 获取对应客户下面的产品
        $where['account_id'] = ['in', $list_account_id];
        $list_product = D('FinanceAccountProduct')->where($where)
            ->select();

        if (!$list_product) {
            return [];
        }

        // 产品加上具体的信息 eg:name,status,appid
        return array_map(function ($item) {
            $item_product = $this->getProductByTypeAndId($item);
            return array_merge($item, $item_product);
        }, $list_product);
    }

    /**
     * 根据产品ID和产品类型获取产品
     * @param array $info_product ['product_id', 'type_id']
     * @return array
     */
    protected function getProductByTypeAndId($info_product)
    {
        if (!$info_product) {
            return [];
        }

        // 条件
        $where = ['id' => (int)$info_product['product_id']];
        $type_id = $info_product['type_id'];
        switch ($type_id) {
            case 1:
                return D('Auth')->where($where)->field(['id', 'developer'])->find();
                break;
            case 2:
                return D('AdminApikey')->where($where)->find();
                break;
            case 3:
                return D('CuishouUser')->where($where)->find();
                break;
        }
    }


    /**
     * GET方式获取客户列表
     * @return array
     */
    protected function getAccountByGetForPage()
    {
        // 条件
        $where = $this->getAccountConditionByGet();

        // 分页
        $count_total = D('FinanceAccounts')->where($where)->count();
        $obj_page = new Page($count_total, C('LIST_ROWS'));

        // 账户列表
        $list_account = D('FinanceAccounts')
            ->where($where)
            ->limit($obj_page->firstRow, $obj_page->listRows)
            ->order('id DESC')
            ->select();

        return compact('obj_page', 'list_account');
    }

    /**
     * GET方法获取客户列表的条件
     * @return array
     */
    protected function getAccountConditionByGet()
    {
        $id = I('get.id', '', 'trim');
        $status = I('get.status', '', 'trim');
        $name = I('get.name', '', 'trim');
        $company = I('get.company', '', 'trim');
        $operator = I('get.operator', '', 'trim');
        $salesman = I('get.salesman', '', 'trim');
        if (!empty($company)){
            $company = ['like', '%'.$company.'%'];
        }
        if (!empty($operator)){
            $operator = ['like', '%'.$operator.'%'];
        }
        if (!empty($salesman)){
            $salesman = ['like', '%'.$salesman.'%'];
        }

        $where = compact('id', 'status', 'name', 'company', 'operator', 'salesman');
        return array_filter($where, function ($item) {
            return $item !== '';
        });
    }

    /**
     * GET方法获取客户列表的条件 select2使用(所以忽略)
     * @return array
     */
    protected function getAccountConditionByGetForSelect2()
    {
        return [];
    }

}
