<?php

namespace Home\Repositories;

use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Common\CurlTrait;

class CorrectBillRepository
{
    use CurlTrait;

    /**
     * 列表的前置条件
     * @throws \Exception
     */
    public function lists()
    {
        list($login_user, $month_begin, $month_end, $list_customers, $list_products,
            $back_api_correct_numbers_create, $back_api_correct_numbers,
            $back_api_correct_operators, $api_download, $back_api_correct_add_comment,
            $back_api_correct_comments, $backend_api_sorts) = [
            $_SESSION[C('LOGIN_SESSION_NAME')],
            date('Y-m', strtotime('first day of last month')),
            date('Y-m', strtotime('first day of last month')),
            json_encode(CustomerModel::getListCustomer([], ['customer_id', 'name', 'company']), JSON_UNESCAPED_UNICODE),
            json_encode(ProductModel::getListProduct([], ['product_id', 'product_name']), JSON_UNESCAPED_UNICODE),
            C('LIST_API_URL')['back_api_correct_numbers_create'],
            C('LIST_API_URL')['back_api_correct_numbers'],
            C('LIST_API_URL')['back_api_correct_operators'],
            C('LIST_API_URL')['back_api_correct_download'],
            C('LIST_API_URL')['back_api_correct_add_comment'],
            C('LIST_API_URL')['back_api_correct_comments'],
            C('LIST_API_URL')['backend_api_sorts'],

        ];

        return compact('back_api_correct_comments', 'backend_api_sorts', 'month_begin', 'month_end', 'list_products', 'back_api_correct_numbers_create', 'back_api_correct_numbers',
            'list_customers', 'list_operators', 'login_user', 'back_api_correct_operators', 'api_download', 'back_api_correct_add_comment');
    }
}