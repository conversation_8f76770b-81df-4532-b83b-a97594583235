<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-16 14:27:06
 */
namespace Home\Repositories;

class ChannelConfigRepository extends BaseRepository
{
    /**
     * 爬虫渠道配置列表
     * @param  array $where 查询条件
     * @param  number $start 开始页数
     * @param  number $limit 每页数量
     * @return list
     */
    public function getChannelConfigList($where, $start, $limit, $order = 'crawler_name, channel_name')
    {
        $list = D('ChannelConfig')->where($where)
                                  ->limit($start, $limit)
                                  ->order($order)
                                  ->select();
        return $list;
    }

    /**
     * 根据爬虫名称获取信息
     * @param  array $where 查询条件
     * @return list
     */
    public function getChannelListByCrawlerName($where)
    {
        $where['channel_flag'] = 1;
        $list = D('ChannelConfig')->where($where)->field('channel_type,channel_name')->index('channel_type')->select();
        return $list;
    }

    /**
     * 获取爬虫渠道数量
     * @param  array $where 查询条件
     * @return number
     */
    public function getChannelConfigNum($where)
    {
        return D('ChannelConfig')->where($where)->count();
    }
    /**
     * 爬虫渠道配置参数
     * @return array
     */
    public function getChannelConfigParam($type)
    {
        $area = I('get.area', '', 'trim');
        $flow_type = I('get.flow_type', '', 'trim');
        $channel = I('get.channel', '', 'trim');
        $where['channel_type'] = $type;
        if ($area && !$flow_type) {
            $where['crawler_name'] = ['like', $area.'%'];
        }
        if (!$area && $flow_type) {
            $where['crawler_name'] = ['like', '%'.$flow_type];
        }
        if ($area && $flow_type) {
            $where['crawler_name'] = $area.$flow_type;
        }
        if ($channel) {
            $where['channel_name'] = $channel;
        }
        return $where;
    }

    /**
     * 根据不同条件获取配置信息
     * @param  string $where 查询条件
     * @return info
     */
    public function getChannelConfigInfo($where)
    {
        if (!$where) {
            return false;
        }
        return D('ChannelConfig')->where($where)->field('id,channel_name, crawler_name, channel_path')->find();
    }

    /**
     * 添加渠道地址
     */
    public function addChannelConfig($channel_type)
    {
        $data = $this->checkChannelConfigParam($channel_type);
        $data['update_time'] = date('Y-m-d H:i:s');
        return D('ChannelConfig')->add($data);
    }

    /**
     * 检查爬虫渠道插入参数
     * @return param
     */
    public function checkChannelConfigParam($channel_type)
    {
        $crawler_name = I('post.crawler_name', '', 'trim');
        $channel_name = I('post.channel_name', '', 'trim');
        $channel_path = I('post.channel_path', '', 'trim');
        if (!$crawler_name) {
            throw new \Exception('爬虫名称不能为空');
        }
        if (!$channel_name) {
            throw new \Exception('渠道名称不能为空');
        }
        if (!$channel_path) {
            throw new \Exception('渠道地址不能为空');
        }
        // $info = $this->getChannelConfigInfo(compact('channel_path', 'channel_type'));
        // if ($info) {
        //     throw new \Exception('渠道地址不可重复');
        // }
        $info = $this->getChannelConfigInfo(compact('crawler_name', 'channel_name', 'channel_type'));
        if ($info) {
            throw new \Exception('该爬虫渠道已添加');
        }
        return compact('crawler_name', 'channel_name', 'channel_path', 'channel_type');
    }

    /**
     * 更新渠道切换地址
     * @return true/flase
     */
    public function updateChannelConfig($channel_type)
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            throw new \Exception('缺少爬虫渠道ID');
        }
        $channel_path = I('post.channel_path', '', 'trim');
        // $info = $this->getChannelConfigInfo(compact('channel_path', 'channel_type'));
        // if ($info && $info['id'] != $id) {
        //     throw new \Exception('渠道地址不可重复');
        // }
        $data['channel_path'] = $channel_path;
        return D('ChannelConfig')->where(['id' => $id])->save($data);
    }

    /**
     * 更新线上标志
     * @param  array $where 查询条件
     * @return true
     */
    public function updateChannelFlagByParam($where)
    {
        if (!$where) {
            throw new \Exception('缺少更新条件');
        }
        //将该爬虫该渠道设为1，
        D('ChannelConfig')->where($where)->save(['channel_flag' => 1]);
        //将该爬虫其他渠道设为0
        $where['channel_name'] = ['neq', $where['channel_name']];
        D('ChannelConfig')->where($where)->save(['channel_flag' => 0]);
        return true;
    }

    /**
     * 删除爬虫渠道
     * @return true/false
     */
    public function delChannelConfig()
    {
        $id = I('get.id', 0, 'intval');
        if (!$id) {
            throw new \Exception('缺少爬虫渠道ID');
        }
        return D('ChannelConfig')->where(['id' => $id])->delete();
    }
}