<?php

namespace Home\Repositories;

use Common\ORG\Page;
use Think\Exception;

class BangProductRepository extends BaseRepository
{
    // 被操作产品的apikey
    private $apikey = null;

    // 被操作产品的apisecret
    private $apisecret = null;

    // 被操作产品的ID
    private $product_id = null;

    /**
     * 编辑产品
     * @throws \Exception
     */
    public function editProduct()
    {
        // 编辑本地的产品
        $this->editSelfProduct();
    }

    /**
     * 获取绑定客户
     * @return mixed| bool
     */
    public function getRelationShipForGet()
    {
        $product_id = I('get.id', '', 'trim');
        $type_id = 4;
        $relation = D('FinanceAccountProduct')->where(compact('type_id', 'product_id'))
            ->find();

        // 如果没有绑定,则返回空
        if (!$relation) {
            return false;
        }

        $id = $relation['account_id'];
        return D('FinanceAccounts')->where(compact('id'))
            ->find();
    }

    /**
     * 获取客户列表
     * @return array
     */
    public function getAccountList()
    {
        return D('FinanceAccounts')->index('id')
            ->select();
    }

    /**
     * 编辑本地的产品
     * @throws \Exception
     *
     */
    protected function editSelfProduct()
    {
        $db = M('', '', 'DB_FINANCE');
        $db->startTrans();
        try {
            // 检查参数
            $this->checkParamsForEdit();

            // 检查限额是否有变化
            $limit_changed = $this->checkLimitChangedForEdit();

            // 更新本地库
            $this->updateProduct($db);

            // 绑定客户
            $this->updateRelationship($db);

            // 通知邦企查接口限额有变化
            $this->requestLimitApi($limit_changed);

            // 添加远程产品数据(如果遇到问题 会抛出错误，一起回滚)
            $this->editRemoteProduct();
            $db->commit();
        } catch (\Exception $e) {

            $db->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 更新自己数据库的产品
     * @param $db
     * @return bool
     * @throws \Exception
     */
    protected function updateProduct($db)
    {
        // 要编辑产品的ID
        $product_id = $id = $this->getEditProductId();

        // 获取产品参数
        $params_product = $this->genParamsForProductOfSelfByEdit();

        // 更新产品
        $result_save_product = $db->Table('bang_products')->where(compact('id'))->save($params_product);

        // 获取产品和字段relationship的参数
        $params_relationship = $this->genParamsForRelationOfSelfByEdit();

        // 删除旧有关系 && 添加关系
        $result_del_old_relationship = $db->Table('bang_product_fields')->where(compact('product_id'))->delete();
        $result_add_relationship = $db->Table('bang_product_fields')->addAll($params_relationship);

        // 如果更新成功 则返回true
        if ($result_save_product !== false && $result_del_old_relationship !== false && $result_add_relationship !== false) {
            return true;
        }

        // 出错则抛出错误
        $msg = ($result_save_product === false) ? '更新产品到自己的服务器失败' : '更新产品输出字段到自己的服务器失败';
        throw new \Exception($msg);
    }

    /**
     * 通知邦企查接口限额有变化
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    protected function requestLimitApi($params)
    {
        // 如果没有变化,则不需要请求限额接口
        if (!$params) {
            return true;
        }

        // 接口URL
        $url_api = C('LIST_API_URL')['bang_limit_num'];

        // 连续请求接口三次,确保不会失败
        $result_request = [];
        $i = 0;
        while (true) {
            if ($i > 2) {
                break;
            }
            $result_request = $this->curlRequestForPost($url_api, $params);
            if ($result_request['status'] == 10000) {
                break;
            }
            $i++;
        }

        // 三次请求失败,做记录
        $this->logForFail($result_request, $url_api, $params);
        return $result_request;
    }

    /**
     * 如果连续三次请求失败 则记录日志
     * @param array $result_request
     * @param string $url
     * @param array $params 请求的参数
     * @throws \Exception
     */
    protected function logForFail($result_request, $url, $params)
    {
        if ($result_request['status'] != 10000) {
            $msg = [
                'msg' => '邦企查限额API连续三次请求失败',
                'data' => $result_request,
                'url' => $url,
                'params' => $params
            ];
            $this->log($msg);
            throw new \Exception('邦企查限额API连续三次请求失败');
        }
    }

    /**
     * 编辑模式检查限额是否发生了变化
     * @return mixed
     */
    protected function checkLimitChangedForEdit()
    {
        // 获取新版的限额信息
        $limit_data_new = $this->getLimitInfoForNew();

        // 获取旧版本的限额信息
        $limit_data_old = $this->getLimitInfoForOld();

        // 如果没有差异 则返回false
        $diff = array_diff_assoc($limit_data_new, $limit_data_old);
        if (!$diff) {
            return false;
        }

        // 获取接口需要的参数
        $limit_data = $this->genParamsForUpdateLimit($limit_data_new, $limit_data_old);

        // 附加签名信息
        return $this->appendSignToCondition($limit_data);
    }

    /**
     * 给条件追加签名
     * @param array $limit 条件
     * @return array
     */
    protected function appendSignToCondition($limit)
    {
        // 签名信息
        $sign_info = $this->genApiSign();

        return array_merge($limit, $sign_info);
    }

    /**
     * 生成参数
     * @return array
     */
    protected function genApiSign()
    {
        // 参数
        $timestamp = time();
        $nonce = mt_rand(1000, 9999);

        // 签名
        $signature = $this->genSign($timestamp, $nonce);
        return compact('signature', 'timestamp', 'nonce');
    }


    /**
     * 生成签名
     * @param integer $timestamp 发起访问的时间戳
     * @param integer $nonce 四位随机数
     * @return string
     */
    protected function genSign($timestamp, $nonce)
    {
        $client_key = C('LIST_API_URL')['bang_api_key'];
        $client_secret = C('LIST_API_URL')['bang_api_secret'];

        $dict_source = compact('timestamp', 'nonce', 'client_key', 'client_secret');
        sort($dict_source, SORT_STRING);
        return sha1(implode('', $dict_source));
    }

    /**
     * 获取请求限额接口的参数
     * @param array $limit_data_new 新版限额
     * @param array $limit_data_old 旧版限额
     * @return array
     */
    protected function genParamsForUpdateLimit($limit_data_new, $limit_data_old)
    {
        $type = $type_new = $limit_data_new['limit_type'];
        $total_new = $limit_data_new['limit_total'];
        $limit_num_new = $limit_data_new['limit_num'];
        $type_old = $limit_data_old['limit_type'];
        $total_old = $limit_data_old['limit_total'];
        $limit_num_old = $limit_data_old['limit_num'];

        $request_body = $this->genParamsForPost();
        $cid = $request_body['id'];

        return compact('type_new', 'type_old', 'total_new', 'total_old', 'cid',
            'limit_num_new', 'limit_num_old', 'type');
    }

    /**
     * 获取旧版本的限额信息
     * @return array
     */
    protected function getLimitInfoForOld()
    {
        $request_body = $this->genParamsForPost();
        $id = $request_body['id'];

        return D('BangProducts')->where(compact('id'))
            ->field(['limit_type', 'limit_num', 'limit_total'])
            ->find();

    }

    /**
     * 获取新版的限额信息
     * @return array
     */
    protected function getLimitInfoForNew()
    {
        // 新版限额
        $request_body = $this->genParamsForPost();
        $limit_type = $request_body['limit_type'];
        $limit_num = $request_body['limit_num'];
        $limit_total = $request_body['limit_total'];
        return compact('limit_num', 'limit_total', 'limit_type');
    }

    /**
     * 更新绑定的客户
     * @param $db
     * @throws \Exception
     */
    protected function updateRelationship($db)
    {
        try {
            // 更新绑定的客户
            $relation_edit = $this->updateRelation($db);
            if ($relation_edit === false) {
                throw new \Exception('抱歉, 更新产品绑定的客户失败');
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 更新产品绑定的客户
     * @param $db
     * @return mixed
     */
    protected function updateRelation($db)
    {
        $request_body = $this->genParamsForPost();
        $account_id = $request_body['account_id'];
        $product_id = $request_body['id'];
        $type_id = 4;
        $updated_at = $created_at = time();

        // 如果不在需要绑定客户, 则清除旧的部分（为啥没有多对多关系可用呢?）
        if (!$account_id) {
            return $db->Table('finance_account_product')->where(compact('product_id', 'type_id'))
                ->delete();
        }

        // 如果之前已经绑定了客户，执行更新；否则执行add
        $relation_had = $this->getRelationForEdit();
        if ($relation_had) {
            return $db->Table('finance_account_product')->where(compact('product_id', 'type_id'))
                ->save(compact('account_id', 'updated_at'));
        }

        return $db->Table('finance_account_product')->add(compact('account_id', 'product_id', 'type_id', 'updated_at', 'created_at'));
    }

    /**
     * 编辑模式， 之前是否已经绑定了某个客户
     * @return boolean
     */
    protected function getRelationForEdit()
    {
        $request_body = $this->genParamsForPost();
        $product_id = $request_body['id'];
        $type_id = 4;
        return !!D('FinanceAccountProduct')->where(compact('product_id', 'type_id'))
            ->find();
    }

    /**
     * 获取编辑客户的时候, 输出字段的数据
     * @return array
     */
    protected function genParamsForRelationOfSelfByEdit()
    {
        // 要编辑产品的Id
        $product_id = $this->getEditProductId();
        $request_body = $this->genParamsForPost();
        $list_field = $request_body['field'];
        $created_at = $updated_at = time();

        // 组装relationship数据
        $list_relationship = [];
        array_walk($list_field, function ($field_id) use ($product_id, $created_at, $updated_at, &$list_relationship) {
            // 去掉默认的数据
            if (is_numeric($field_id)) {
                $item = compact('field_id', 'product_id', 'created_at', 'updated_at');
                array_push($list_relationship, $item);
            }
        });
        return $list_relationship;
    }

    /**
     * 获取编辑客户的时候, 自身的数据
     * @return array
     */
    protected function genParamsForProductOfSelfByEdit()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        $name = trim($request_body['name']);
        $contract_status = trim($request_body['contract_status']);
        $limit_type = trim($request_body['limit_type']);
        $limit_num = trim($request_body['limit_num']);
        $limit_total = trim($request_body['limit_total']);
        $expiration_date = strtotime($request_body['expiration_date']);
        $limit_second = trim($request_body['limit_second']);
        $status = trim($request_body['status']);
        $updated_at = time();
        $remark = trim($request_body['remark']);
        $encrypt_way = $request_body['encrypt_way'];

        // IP白名单
        $limit_ip = $this->genConditionOfLimitIp($request_body['limit_ip']);

        // 算法模块字段
        $algorithm_switch = $this->getAlgorithmFroEditLocal();

        return compact('name', 'contract_status', 'limit_type', 'limit_num', 'limit_total',
            'expiration_date', 'limit_second', 'status', 'limit_ip', 'apikey', 'apisecret', 'created_at',
            'updated_at', 'remark', 'encrypt_way', 'algorithm_switch');
    }

    /**
     * 编辑模式下获取的算法模块字段
     * @return string
     */
    protected function getAlgorithmFroEditLocal()
    {
        return $this->getAlgorithmForPayload();
    }

    /**
     * 编辑远程产品
     * @throws \Exception
     */
    protected function editRemoteProduct()
    {
        $db_remote = M('', '', 'DB_BANG');
        $db_remote->startTrans();
        try {
            // 要编辑产品的ID
            $client = $id = $this->getEditProductId();

            // 为远程产品数据生成参数
            $params_product = $this->genParamsForRemoteProductByEdit();
            $result_product = $db_remote->Table('bang_client')->where(compact('id'))->save($params_product);

            // 远程产品的字段参数
            $params_fields = $this->genParamsForRemoteFieldsByEdit();
            $result_fields = $db_remote->table('bang_client_columns')->where(compact('client'))->save($params_fields);

            if ($result_product !== false && $result_fields !== false) {
                $db_remote->commit();
            } else {

                // 遇到问题 则抛出并且回滚
                $msg = ($result_product === false) ? '更新产品到远程失败' : '更新产品输出字段到远程失败';
                throw new \Exception($msg);
            }
        } catch (\Exception $e) {
            $db_remote->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 为远程产品编辑生成参数
     * @return array
     */
    protected function genParamsForRemoteFieldsByEdit()
    {
        $request_body = $this->genParamsForPost();
        $fields_choose = $request_body['field'];
        $list_fields = D('BangFields')
            ->select();
        $params = [];

        // 将id转换成远程数据库需要的参数
        array_walk($list_fields, function ($filed) use (&$params, $fields_choose) {
            $slug = $filed['slug'];
            $id = $filed['id'];

            $item = [$slug => 0];
            if (in_array($id, $fields_choose)) {
                $item = [$slug => 1];
            }
            $params = array_merge($params, $item);
        });

        return $params;
    }

    /**
     * 为远程产品的编辑生成参数
     * @return array
     */
    protected function genParamsForRemoteProductByEdit()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        $name = trim($request_body['name']);
        $type = trim($request_body['limit_type']);
        $total = trim($request_body['limit_total']);
        $limit_num = trim($request_body['limit_num']) ? trim($request_body['limit_num']) : 0;
        $cut_time = strtotime($request_body['expiration_date']);
        $second_num = trim($request_body['limit_second']);
        $status = (trim($request_body['status']) == 1) ? 1 : 0;
        $encrypt_way = $request_body['encrypt_way'];

        // IP白名单
        $white_ips = $this->genConditionOfLimitIp($request_body['limit_ip']);

        // 算法模块
        $switch = $this->getAlgorithmForEditRemote();

        return compact('name', 'type', 'total',
            'limit_num', 'cut_time', 'second_num', 'status', 'white_ips', 'encrypt_way', 'switch');
    }

    /**
     * 编辑模式获取远程的算法模块字段
     * @return string
     */
    protected function getAlgorithmForEditRemote()
    {
        return $this->getAlgorithmForPayload();
    }


    /**
     * 获取要编辑产品的ID
     * @return mixed
     */
    protected function getEditProductId()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);
        return $request_body['id'];
    }

    /**
     * 为编辑客户检查参数
     * @throws \Exception
     */
    protected function checkParamsForEdit()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 检查产品名称是否唯一
        $this->checkNameUniqueForEdit();

        // 检查秒并发
        $this->checkLimitSecond($request_body['limit_second']);

        // 检查IP白名单
        $this->checkLimitIp($request_body['limit_ip']);
    }


    /**
     * 检查产品的唯一性
     * @throws \Exception
     */
    protected function checkNameUniqueForEdit()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // name相同且id不同则说明已经被占用
        $name = trim($request_body['name']);
        $id = trim($request_body['id']);
        $id = ['neq', $id];

        $name_count = D('BangProducts')
            ->where(compact('name', 'id'))
            ->find();

        if ($name_count) {
            throw new \Exception('请重新选择产品名字，您现在选定的名字已经被占用');
        }
    }


    /**
     * Get方式获取要编辑的产品
     * @return mixed
     */
    public function getEditProductForGet()
    {
        $id = I('get.id', '', 'trim');
        $product = D('BangProducts')
            ->where(compact('id'))
            ->find();

        // expiration_date 转成 Y-m-d
        $product['expiration_date'] = date('Y-m-d', $product['expiration_date']);

        // IP 转成PHP_EOL 分割
        $product['limit_ip'] = str_replace(',', PHP_EOL, $product['limit_ip']);


        // 算法模块转成数组
        $product['algorithm_switch'] = $product['algorithm_switch'] ? explode('_', $product['algorithm_switch']) : [];
        return $product;
    }

    /**
     * 获取要编辑产品的输出字段
     * @return array
     */
    public function getEditProductField()
    {
        $product_id = I('get.id', '', 'trim');
        $list_fields = D('BangProductFields')
            ->where(compact('product_id'))
            ->select();

        return array_map(function ($item) {
            return $item['field_id'];
        }, $list_fields);

    }

    /**
     * 获取接口输出字段列表
     * @return array
     */
    public function getExportFieldsList()
    {
        $status = 1;
        return D('BangFields')
            ->where(compact('status'))
            ->select();
    }

    /**
     * 添加产品
     * @throws \Exception
     */
    public function addAccount()
    {
        // 自己数据库添加完成之后嵌套添加远程数据库 这样可以完成一起回滚的问题
        $this->addSelfProduct();
    }

    /**
     * 添加远程数据
     * @throws \Exception
     */
    protected function addRemoteProduct()
    {
        $db_remote = M('', '', 'DB_BANG');
        $db_remote->startTrans();
        try {

            // 为远程产品数据生成参数
            $params_product = $this->genParamsForRemoteProduct();
            $result_product = $db_remote->Table('bang_client')->add($params_product);

            // 远程产品的字段参数
            $params_fields = $this->genParamsForRemoteFields();
            $result_fields = $db_remote->table('bang_client_columns')->add($params_fields);

            if ($result_product !== false && $result_fields !== false) {
                $db_remote->commit();
            } else {

                // 遇到问题 则抛出并且回滚
                $msg = ($result_product === false) ? '添加产品到远程失败' : '添加产品输出字段到远程失败';
                throw new \Exception($msg);
            }
        } catch (\Exception $e) {
            $db_remote->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 为远程参数添加生成参数
     * @return array
     */
    protected function genParamsForRemoteFields()
    {
        $request_body = $this->genParamsForPost();

        // 字段ID列表
        $params = [
            'client' => $this->product_id,
            'time' => time()
        ];

        // 将id转换成远程数据库需要的参数
        $id = ['in', $request_body['field']];

        $list_fields = D('BangFields')
            ->where(compact('id'))
            ->select();

        array_walk($list_fields, function ($filed) use (&$params) {
            $slug = $filed['slug'];
            $item = [$slug => 1];
            $params = array_merge($params, $item);
        });

        return $params;
    }

    /**
     * 为远程产品的添加生成参数
     * @return array
     */
    protected function genParamsForRemoteProduct()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        $id = $this->product_id;
        $name = trim($request_body['name']);
        $client_key = $this->apikey;
        $client_screct = $this->apisecret;
        $type = trim($request_body['limit_type']);
        $total = trim($request_body['limit_total']);
        $limit_num = trim($request_body['limit_num']);
        $limit_num = $limit_num ? $limit_num : 0;
        $cut_time = strtotime($request_body['expiration_date']);
        $second_num = trim($request_body['limit_second']);
        $status = trim($request_body['status']);
        $add_time = time();
        $encrypt_way = $request_body['encrypt_way'];

        // 无话可说系类(不需要维护的字段 还是硬要填充字段)
        $update_total_time = 0; //上次更新次数时间
        $total_num = $total; // 总量剩余
        $num = $limit_num; // 限额剩余

        // IP白名单
        $white_ips = $this->genConditionOfLimitIp($request_body['limit_ip']);

        $switch = $this->getAlgorithmForCreateRemote();

        return compact('id', 'name', 'client_key', 'client_screct', 'type', 'total',
            'limit_num', 'cut_time', 'second_num', 'status', 'add_time', 'white_ips',
            'update_total_time', 'total_num', 'num', 'encrypt_way', 'switch');
    }

    /**
     * 远程账户创建的时候，获取算法模块的值
     * @return string
     */
    protected function getAlgorithmForCreateRemote()
    {
        return $this->getAlgorithmForPayload();
    }

    protected function genUniqueString($length = 32)
    {
        $hash_string = '';
        for ($i = 0; $i < 10; $i++) {
            $hash_string .= md5(uniqid() . mt_rand(1, 9999999));
        }

        return substr($hash_string, 0, $length);
    }

    /**
     * 添加新产品到自己的账户
     * @throws  \Exception
     */
    protected function addSelfProduct()
    {
        $db = M('', '', 'DB_FINANCE');
        $db->startTrans();
        try {
            // 检查参数
            $this->checkParamsForAdd();

            // 获取产品参数
            $params_product = $this->genParamsForProductOfSelf();

            // 添加产品
            $result_add_product = $db->Table('bang_products')->add($params_product);
            $this->product_id = $result_add_product;

            // 获取产品和字段relationship的参数
            $params_relationship = $this->genParamsForRelationOfSelf($result_add_product);

            // 添加关系
            $result_add_relationship = $db->Table('bang_product_fields')->addAll($params_relationship);

            // 添加客户关系
            $this->addRelation($db);

            if ($result_add_product !== false && $result_add_relationship !== false) {
                // 添加远程产品数据(如果遇到问题 会抛出错误，一起回滚)
                $this->addRemoteProduct();
                $db->commit();
            } else {

                // 遇到问题 回滚
                $msg = ($result_add_product === false) ? '添加产品到自己的服务器失败' : '添加产品输出字段到自己的服务器失败';
                throw new \Exception($msg);
            }

        } catch (\Exception $e) {

            $db->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 添加客户，邦企查的关系
     * @param $db
     * @throws \Exception
     */
    protected function addRelation($db)
    {
        try {
            $relationship_result = $this->addRelationShip($db);
            if ($relationship_result === false) {
                throw new \Exception('抱歉,邦企查绑定客户失败');
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 添加客户，邦企查的关系
     * @param $db
     * @return mixed
     */
    protected function addRelationShip($db)
    {
        // 如果没有绑定客户，直接返回
        $request_body = $this->genParamsForPost();
        if (!$request_body['account_id']) {
            return true;
        }

        // 关系数据
        $account_id = $request_body['account_id'];
        $product_id = $this->product_id;
        $type_id = 4;
        $created_at = $updated_at = time();
        $relation_info = compact('account_id', 'product_id', 'type_id', 'created_at', 'updated_at');
        return $db->Table('finance_account_product')->add($relation_info);
    }

    /**
     * 获取添加客户的时候, 输出字段的数据
     * @param $product_id
     * @return array
     */
    protected function genParamsForRelationOfSelf($product_id)
    {
        $request_body = $this->genParamsForPost();
        $list_field = $request_body['field'];
        $created_at = $updated_at = time();

        // 组装relationship数据
        $list_relation = [];
        array_walk($list_field, function ($field_id) use ($product_id, $created_at, $updated_at, &$list_relation) {
            // 默认填充的三个是要抛掉的脏数据
            if (is_numeric($field_id)) {
                array_push($list_relation, compact('field_id', 'product_id', 'created_at', 'updated_at'));
            }
        });
        return $list_relation;
    }

    /**
     * 获取添加客户的时候, 自身的数据
     * @return array
     */
    protected function genParamsForProductOfSelf()
    {
        $request_body = $this->genParamsForPost();
        $name = trim($request_body['name']);
        $contract_status = trim($request_body['contract_status']);
        $limit_type = trim($request_body['limit_type']);
        $limit_num = trim($request_body['limit_num']);
        $limit_total = trim($request_body['limit_total']);
        $expiration_date = strtotime($request_body['expiration_date']);
        $limit_second = trim($request_body['limit_second']);
        $status = trim($request_body['status']);
        $created_at = $updated_at = time();
        $remark = trim($request_body['remark']);
        $encrypt_way = $request_body['encrypt_way'];

        // 算法模块
        $algorithm_switch = $this->getAlgorithmForCreateLocal();

        // IP白名单
        $limit_ip = $this->genConditionOfLimitIp($request_body['limit_ip']);

        // apikey apiserect
        $this->apikey = $apikey = $this->genUniqueString();
        $this->apisecret = $apisecret = $this->genUniqueString();


        return compact('name', 'contract_status', 'limit_type', 'limit_num', 'limit_total',
            'expiration_date', 'limit_second', 'status', 'limit_ip', 'apikey', 'apisecret', 'created_at',
            'updated_at', 'remark', 'encrypt_way', 'algorithm_switch');
    }

    /**
     * 创建账户的时候 获取算法模块的数据
     * @return string
     */
    protected function getAlgorithmForCreateLocal()
    {
        return $this->getAlgorithmForPayload();
    }

    /**
     * 从Payload参数中获取算法模块
     * @return string
     */
    protected function getAlgorithmForPayload()
    {
        $request_body = $this->genParamsForPost();
        $algorithm_switch = $request_body['algorithm_switch'];
        // 如果没有选择模块 则返回''
        if (!$algorithm_switch) {
            return '';
        }
//        exit(json_encode(compact('algorithm_switch')));
        return implode('_', $algorithm_switch);
    }

    /**
     * @param $limit_ip
     * @return mixed|string
     */
    protected function genConditionOfLimitIp($limit_ip)
    {
        if (!$limit_ip) {
            return '';
        }
        $limit_ip = trim($limit_ip);
        return str_replace(PHP_EOL, ',', $limit_ip);
    }

    /**
     * 检查添加产品的参数
     * @throws \Exception
     */
    protected function checkParamsForAdd()
    {
        $request_body = file_get_contents('php://input');
        $request_body = json_decode($request_body, true);

        // 检查产品名称是否唯一
        $this->checkNameUniqueForAdd($request_body['name']);

        // 检查秒并发
        $this->checkLimitSecond($request_body['limit_second']);

        // 检查IP白名单
        $this->checkLimitIp($request_body['limit_ip']);
    }

    /**
     * 检查IP白名单
     * @param $limit_ip
     * @return bool
     */
    protected function checkLimitIp($limit_ip)
    {
        // 整理传入的IP
        $limit_ip = trim($limit_ip);
        if ($limit_ip === '') {
            return true;
        }
        $limit_ip = explode(PHP_EOL, $limit_ip);

        // 检查IP是否合法
        array_walk($limit_ip, function ($item) {
            $item = trim($item);

            // 如果不是IP 则抛出错误
            if (!filter_var($item, FILTER_VALIDATE_IP)) {
                $msg = '请输入合法的IP, ' . $item . ' 不是合法的IP';
                throw new \Exception($msg);
            }
        });
    }

    /**
     * 检查秒并发
     * @param $limit_second
     * @throws \Exception
     */
    protected function checkLimitSecond($limit_second)
    {
        $limit_second = trim($limit_second);
        if (!is_numeric($limit_second) || $limit_second <= 0) {
            throw new \Exception('秒并发必须是正整数');
        }
        if ($limit_second > 100) {
            throw new \Exception('秒并发必须不大于100');
        }
    }

    /**
     * 检查产品的唯一性
     * @param $name
     * @throws \Exception
     */
    protected function checkNameUniqueForAdd($name)
    {
        $name = trim($name);
        $name_count = D('BangProducts')
            ->where(compact('name'))
            ->count();

        if ($name_count) {
            throw new \Exception('请重新选择产品名字，您现在选定的名字已经被占用');
        }
    }


    /**
     * get方式获取产品列表
     */
    public function getProductListForGet()
    {
        // 条件
        $where = $this->genConditionForList();

        // Page
        $count_total = D('BangProducts')
            ->where($where)
            ->count();

        $page_obj = new Page($count_total, 10);
        $page_show = $page_obj->show();

        //  获取产品
        $list_product = $this->getProductByCondition($where, $page_obj);

        // 追加客户信息
        $list_product = $this->appendAccountInfo($list_product, 4);

        return compact('page_show', 'list_product');
    }


    /**
     * 获取所有产品
     * @return array
     */
    public function getProductListOfAll()
    {
        return D('BangProducts')->select();
    }

    /**
     * 根据条件获取产品列表
     * @param $where
     * @param $page_obj
     * @return array
     */
    protected function getProductByCondition($where, $page_obj)
    {
        return D('BangProducts')
            ->where($where)
            ->limit($page_obj->firstRow, $page_obj->listRows)
            ->order('id desc')
            ->select();
    }

    /**
     * 为列表生成条件
     * @return array
     */
    protected function genConditionForList()
    {
        $status = I('get.status', '', 'trim');
        $id = I('get.id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');
        $choose_id = I('get.choose_id', '', 'trim');


        if ($choose_id && $id && $choose_id !== $id) {
            // 如果填写的id和选定的产品不同则 肯定不给数据
            $id = 'no none';
        } elseif (!$id && $choose_id) {
            // 如果id没有填充  但是又选择产品的情况
            $id = $choose_id;
        }

        // 组合 && 过滤
        $where = compact('status', 'id', 'apikey');
        return array_filter($where, function ($item) {
            return $item;
        });
    }
}