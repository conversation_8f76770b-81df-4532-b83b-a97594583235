<?php

namespace Home\Repositories;

use Common\Model\CuishouDailyStatModel;
use Think\Cache;

class CuishouStatRepository extends BaseRepository
{
    // 总计详情文件的文件名 && 文件路径
    protected $download_file_name = 'cuishou_total.csv';
    protected $download_file_path = RUNTIME_PATH;

    // 文件cell列表
    protected $list_cell = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];

    protected $list_title = ["日期", "总调用量", "有效调用量", "催收覆盖率", "疑似催收覆盖率", "同时覆盖率", "整体覆盖率"];

    /*
    * 展示之前，一个客户必然包含的单元
    * */
    protected $item_base = [
        'converage_dunning' => 0.000,
        'converage_not_sure_dunning' => 0.000,
        'converage_all' => 0.000,
        'coverage_both' => 0.000,
        'success_counts' => 0,
        'access_counts' => 0,
        'dunning_times' => 0,
        'not_sure_dunning_times' => 0,
        'not_times' => 0,
        'both_times' => 0,
    ];

    /**
     * 总计详情导出文件
     * @throws \Exception
     */
    public function downloadTotalShow()
    {
        // 总计详情的数据
        $list_info = $this->getTotalInfoByDay();

        // 生成文件
        $this->genFileForTotal($list_info);

        // file_download return
        $file_name = $this->genFileNameForReport();
        $this->fileDownload($file_name);
    }

    /**
     * 生成文件
     * @param array $list_info 统计信息
     * @return string
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genFileForTotal($list_info)
    {

        // 加载PHPEXCEL
        require_once LIB_PATH . '/Org/PHPExcel/PHPExcel.php';

        $excel_php = new \PHPExcel();

        // 设置基本的属性
        $excel_php = $this->setPropertyForExcel($excel_php);

        // 将数据写 入excel
        $excel_php = $this->writeDataIntoExcel($excel_php, $list_info);

        // 设置sheet
        $excel_php = $this->setSheetForReport($excel_php);

        // 生成excel文件
        $this->genFileForReport($excel_php);
    }

    /**
     * 将数据写入excel
     * @param \PHPExcel $excel_php
     * @param array $list_info 统计信息
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     * @throws \Exception
     */
    protected function writeDataIntoExcel($excel_php, $list_info)
    {
        // 写入title
        $excel_php = $this->writeTitleForReport($excel_php);

        // 写入产品数据
        return $this->writeProductInfoForReport($list_info, $excel_php);
    }

    /**
     * 为总计详情写入产品的信息
     * @param array $list_info 统计信息
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \Exception
     */
    protected function writeProductInfoForReport($list_info, $excel_php)
    {
        // 开始写的行
        $row_begin = 2;
        array_walk($list_info, function ($item_info) use (&$row_begin, &$excel_php) {
            $excel_php = $this->writeSingleProductIntoExcel($item_info, $row_begin, $excel_php);

            // 本轮单元结束，进行下一个产品
            $row_begin++;
        });

        // 全局居中
        $excel_php->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $excel_php->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        return $excel_php;
    }

    /**
     * 将单个产品的信息 写入excel
     * @param array $list_item 统计单元
     * @param integer $row 插入第几行
     * @param $excel_php
     * @return mixed
     */
    protected function writeSingleProductIntoExcel($list_item, $row, $excel_php)
    {
        $excel_php->setActiveSheetIndex(0)
            ->setCellValue('A' . $row, $list_item['date_key'])
            ->setCellValue('B' . $row, $list_item['access_counts'])
            ->setCellValue('C' . $row, $list_item['success_counts'])
            ->setCellValue('D' . $row, $list_item['converage_dunning'])
            ->setCellValue('E' . $row, $list_item['converage_not_sure_dunning'])
            ->setCellValue('F' . $row, $list_item['coverage_both'])
            ->setCellValue('G' . $row, $list_item['converage_all']);
        return $excel_php;
    }

    /**
     * 为日报写入标题
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     */
    protected function writeTitleForReport($excel_php)
    {
        array_walk($this->list_cell, function ($item_cell, $item_index) use (&$excel_php) {
            $value = $this->list_title[$item_index];
            $excel_php->setActiveSheetIndex(0)
                ->setCellValue($item_cell . 1, $value);

            // 加粗 居中
            $excel_php->setActiveSheetIndex(0)->getStyle($item_cell . 1)->applyFromArray(
                [
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER]
                ]
            );

            // 自动调整行宽
            $excel_php->setActiveSheetIndex(0)
                ->getColumnDimension($item_cell)
                ->setAutoSize(true);
        });

        // 这个是执行自动宽度的地方， 因为计算出来的一列的最大长度总是小了一些 所以代码中手动加上10
        $excel_php->setActiveSheetIndex(0)->calculateColumnWidths();
        return $excel_php;
    }

    /**
     * 生成文件
     * @param \PHPExcel $excel_php
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genFileForReport($excel_php)
    {
        // 生成文件
        $objWriter = \PHPExcel_IOFactory::createWriter($excel_php, 'Excel2007');
        $name_report_file = $this->genFileNameForReport();
        ob_clean();
        $objWriter->save($name_report_file);
    }

    /**
     * 生成日报的文件名
     * @return string
     */
    protected function genFileNameForReport()
    {
        // 路径
        $path_name = RUNTIME_PATH;

        // 文件名
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = '催收分总计详情文档-' . $begin . '-' . $end . '.xlsx';;
        return $path_name . $file_name;
    }

    /**
     * 为文件设置sheet
     * @param $excel_php
     * @return mixed
     */
    protected function setSheetForReport($excel_php)
    {
        // Rename worksheet
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $name_sheet = '催收分总计详情文档_' . $begin . '-' . $end;
        $excel_php->getActiveSheet()->setTitle($name_sheet);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $excel_php->setActiveSheetIndex(0);
        return $excel_php;
    }

    /**
     * 为excel设置属性
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     */
    protected function setPropertyForExcel($excel_php)
    {
        $excel_php->getProperties()->setCreator("后台开发人员")
            ->setLastModifiedBy("后台开发人员")
            ->setTitle("催收分总计详情文档")
            ->setSubject("催收分总计详情文档")
            ->setDescription("调试的朋友你知道吗? 这个类是已经被废弃了的")
            ->setKeywords("office 2007 openxml php")
            ->setCategory("统计详情");
        return $excel_php;
    }

    /**
     * 解决中文乱码的问题
     * @param string $str
     * @return string
     */
    protected function convertUTF8($str)
    {
        if (empty($str)) return '';
        return iconv('utf-8', 'gb2312', $str);
    }

    /**
     * 获取总计详情(按天)
     * @return array
     */
    public function getTotalInfoByDay()
    {
        // 条件
        $where = $this->genConditionForDay();

        // 按天分组的信息
        $list_day_info = $this->getGroupInfoByDay($where);

        // 格式化 && 补全没有数据的那些天
        $list_day_data = $this->tidyDataForTotal($list_day_info);

        // 总计
        $total_stat = $this->computeTotalData($list_day_info);

        // 总计和各天数据合并
        return $this->mergeTotalAndDay($list_day_data, $total_stat);
    }

    /**
     * 合并总计和各天数据
     * @param array $list_day_data 各天数据
     * @param array $total_stat 总计数据
     * @return array
     */
    protected function mergeTotalAndDay($list_day_data, $total_stat)
    {
        array_unshift($list_day_data, $total_stat);

        // 格式化处理
        return array_map(function($item){
            $item['access_counts'] = $this->formatNumber($item['access_counts']);
            $item['success_counts'] = $this->formatNumber($item['success_counts']);

            $item['converage_dunning'] = $this->formatWithPercentage($item['converage_dunning']);
            $item['converage_all'] = $this->formatWithPercentage($item['converage_all']);
            $item['converage_not_sure_dunning'] = $this->formatWithPercentage($item['converage_not_sure_dunning']);
            $item['coverage_both'] = $this->formatWithPercentage($item['coverage_both']);
            return $item;
        }, $list_day_data);
    }



    /**
     * 为总计详情计算总计信息
     * @param array $list_day_info
     * @return array
     */
    protected function computeTotalData($list_day_info)
    {
        // 总计的容器
        $total_stat = $this->item_base + ['date_key' => '总计'];

        // 累加相应的属性值
        array_walk($list_day_info, function ($item) use (&$total_stat) {
            $total_stat['success_counts'] += $item['success_counts'];
            $total_stat['access_counts'] += $item['access_counts'];
            $total_stat['dunning_times'] += $item['dunning_times'];
            $total_stat['not_sure_dunning_times'] += $item['not_sure_dunning_times'];
            $total_stat['not_times'] += $item['not_times'];
            $total_stat['both_times'] += $item['both_times'];
        });

        // 计算各种率
        return $this->computeCoverage($total_stat);
    }

    /**
     * 将原始数据整合成依天为key的数组
     * @param array $list_day_info 分组得到的原始数据
     * @return array
     */
    protected function tidyDataForTotal($list_day_info)
    {
        // 以day为key的二位数组
        $list_day_data = $this->tidyDataToDay($list_day_info);

        // 补全没有数据的那些天
        return $this->appendMissDay($list_day_data);
    }

    /**
     * 以天为key的二位数组
     * @param array $list_day_info
     * @return array
     */
    protected function tidyDataToDay($list_day_info)
    {
        // 容器 [2018-07-11 => [itag => 300]]
        $list_data = [];
        array_walk($list_day_info, function ($item) use (&$list_data) {
            $day = date('Y-m-d', strtotime($item['_id']));
            $list_data[$day] = $item;
        });

        return $list_data;
    }

    /**
     * 向总计追加缺失的那些天
     * @param array $list_day_data
     * @return array
     */
    protected function appendMissDay($list_day_data)
    {
        // 需要的时间范围
        $list_date = $this->dateRange();

        // 容器
        $list_format = [];

        array_walk($list_date, function ($date) use ($list_day_data, &$list_format) {
            // 如果这一天没有缺失 则覆盖默认值
            if (array_key_exists($date, $list_day_data)) {
                $list_format[$date] = array_merge($this->item_base, $list_day_data[$date]);
            } else {
                // 缺失默认值
                $list_format[$date] = $this->item_base;
            }

            // 计算各种率
            $list_format[$date] = $this->computeCoverage($list_format[$date]);

            // vue-easytable 定制化的key
            $list_format[$date]['date_key'] = $date;
        });
        return $list_format;
    }

    /**
     * 需要展示的时间范围
     * @return array
     */
    protected function dateRange()
    {
        $request_body = $this->genParamsForPost();
        $day_begin = date('Ymd', strtotime($request_body['begin']));
        $day_end = date('Ymd', strtotime($request_body['end']));

        // 容器 ['2018-10-01', '2018-10-02']
        $list_date = [];
        while (true) {
            if ($day_begin > $day_end) {
                break;
            }
            $date_format = date('Y-m-d', strtotime($day_end));
            array_push($list_date, $date_format);

            $day_end = date('Ymd', strtotime($day_end . '-1 day'));
        }
        return $list_date;
    }

    /**
     * 按天分组的信息
     * @param array $where
     * @return array
     */
    protected function getGroupInfoByDay($where)
    {
        // user_id,username,max(score)
        return $this->statGroupByItem($where, '$time');
    }

    /**
     * 为总计详情生成条件
     * @return array
     */
    protected function genConditionForDay()
    {
        // 限制 product_id
        $limit_product = $this->limitProduct();

        // 限制签约状态 && active
        $limit_base = $this->limitBaseInfo();

        // 时间限制
        $limit_time = $this->limitTime();

        // 条件整合
        return $this->tidyConditionForDay($limit_product, $limit_base, $limit_time);
    }

    /**
     * 条件整合
     * @param array $limit_product
     * @param array $limit_base
     * @param array $limit_time
     * @return array
     */
    protected function tidyConditionForDay($limit_product, $limit_base, $limit_time)
    {
        // 如果active and contract_status 没有找到对应的ID, 那么不会有数据的
        if (!$limit_base) {
            return ['uid' => '不会有数据的'];
        }

        // 如果没有对account_id && product_id进行限制则 && 但是对基本条件进行了限制
        if (!$limit_product) {
            $limit_base = $this->intToMongo($limit_base);
            $uid = [
                '$in' => $limit_base
            ];
            $limit_uids = compact('uid');
            return array_merge($limit_uids, $limit_time);
        }

        // 对account_id,product_id限制和产品状态和签约类型求交集
        $limit_intersect = array_intersect($limit_product['product_id'], $limit_base);

        // 如果关于product_id的条件冲突
        if (!$limit_intersect) {
            $uid = '哈哈,永远不可能数据的';
        } else {
            $limit_intersect = $this->intToMongo($limit_intersect);
            $uid = [
                '$in' => $limit_intersect
            ];
        }

        return array_merge(compact('uid'), $limit_time);
    }

    /**
     * int类型的数据转成mongoInt32的数据
     * @param array $limit_source
     * @return array
     */
    protected function intToMongo($limit_source)
    {
        $limit_source = array_values($limit_source);
        return  array_map(function ($item) {
            return new \MongoInt32($item);
        }, $limit_source);
    }

    /**
     * 时间限制
     * @return array
     */
    protected function limitTime()
    {
        $request_body = $this->genParamsForPost();

        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));

        $time = [
            '$gte' => $begin,
            '$lte' => $end
        ];
        return compact('time');
    }

    /**
     * 限制基本的信息
     *
     */
    protected function limitBaseInfo()
    {
        // 限制状态
        $limit_active = $this->limitStatus();

        // 限制签约类型
        $limit_contract_status = $this->limitContractStatus();

        // 符合条件的产品
        $where = array_merge($limit_active, $limit_contract_status);
        $list_product = $this->getProductByCondition($where);

        // 转成product_id的限制
        return array_map(function ($item) {
            return $item['id'];
        }, $list_product);
    }

    protected function getProductByCondition($where)
    {
        return D('CuishouUser')->where($where)
            ->field(['_id' => 0])
            ->select();
    }

    /**
     * 限制签约类型
     * @return array
     */
    protected function limitContractStatus()
    {
        // 获取post传入的参数
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('contract_status', $request_body) || !$request_body['contract_status']) {
            return [];
        }
        $contract_status = new \MongoInt32($request_body['contract_status']);
        return compact('contract_status');
    }

    /**
     * 限制状态
     * @return array
     */
    protected function limitStatus()
    {
        // 获取post传入的参数
        $request_body = $this->genParamsForPost();
        if (!array_key_exists('status', $request_body) || !$request_body['status']) {
            return [];
        }
        $status = new \MongoInt32($request_body['status']);
        return compact('status');
    }

    /**
     * 限制 product_id
     */
    protected function limitProduct()
    {
        // 请求的方式可能是 formData && payload
        $request_body = $this->genParamsForPost();

        // 如果没有限制product_id，也没有限制account_id
        $limit_both_not = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_both_not) {
            return [];
        }

        // 如果只是限制了product_id
        $limit_product_only = (array_key_exists('product_id', $request_body) && $request_body['product_id']) &&
            (!array_key_exists('account_id', $request_body) || !$request_body['account_id']);

        if ($limit_product_only) {
            $product_id = [$request_body['product_id']];
            return compact('product_id');
        }

        // 如果只是限制了account_id
        $limit_account_only = (!array_key_exists('product_id', $request_body) || !$request_body['product_id']) &&
            (array_key_exists('account_id', $request_body) && $request_body['account_id']);
        if ($limit_account_only) {
            $account_id = $request_body['account_id'];
            $product_id = $this->getRelationshipOfAccount($account_id);
            return compact('product_id');
        }

        // 同时限制了product_id && account_id
        return $this->limitProdutAndAccount($request_body);
    }

    /**
     * 同时限制了product_id && account_id
     * @param $request_body
     * @return array
     */
    protected function limitProdutAndAccount($request_body)
    {
        $product_id = $request_body['product_id'];
        $account_id = $request_body['account_id'];
        $list_product = $this->getRelationshipOfAccount($account_id);
        if (in_array($product_id, $list_product)) {
            return ['product_id' => [$product_id]];
        } else {
            return ['product_id' => ['永远不可能有值的哈哈']];
        }
    }

    /**
     * 获取特定客户下辖的邦秒配产品ID
     * @param integer $account_id
     * @return array
     */
    protected function getRelationshipOfAccount($account_id)
    {
        $type_id = 3;
        $where = compact('type_id', 'account_id');
        $list_relationship = D('FinanceAccountProduct')->where($where)
            ->select();

        return array_map(function ($item) {
            return $item['product_id'];
        }, $list_relationship);
    }


    /**
     * 产品列表（以id为key）
     * @return array
     */
    public function getProductList()
    {
        $list_product = D('CuishouUser')
            ->field(['_id' => 0])
            ->select();
        return array_column($list_product, null, 'id');
    }

    /**
     * 为折线图默认页生成默认的参数
     */
    public function tidyInitParamsForDefaultPage()
    {
        $params = I('get.');

        // 时间限制
        return $this->tidyParamsForTime($params);
    }

    /**
     * 时间初始化
     * @param array $params GET参数
     * @return array
     */
    protected function tidyParamsForTime($params)
    {
        if (!isset($params['begin']) || !trim($params['begin'])) {
            $params['begin'] = date('Y-m-d');
        }

        if (!isset($params['end']) || !trim($params['end'])) {
            $params['end'] = date('Y-m-d');
        }

        return $params;
    }

    /**
     * 在产品列表追加相应的客户信息
     * @param array $list_product 产品列表
     * @return  array
     */
    public function appendAccountToProductList($list_product)
    {
        if (!$list_product) {
            return [];
        }

        //  获取产品客户的多对多关系
        $list_relationship = $this->getRelationshipOfAccountProduct();

        // 合并客户和产品
        return $this->mergeAccountAndProduct($list_product, $list_relationship);
    }

    /**
     * 合并客户和产品
     * @param $list_product
     * @param $list_relationship
     * @return array
     */
    protected function mergeAccountAndProduct($list_product, $list_relationship)
    {
        // 客户列表
        $list_account = $this->getAccountList();
        return array_map(function ($item_product) use ($list_relationship, $list_account) {

            // 产品id
            $product_id = $item_product['id'];

            // 对应的客户ID
            $account_id = isset($list_relationship[$product_id]['account_id']) ? $list_relationship[$product_id]['account_id'] : '';

            // 没有绑定客户 则不再补全客户
            if (!$account_id) {
                return $item_product;
            }
            $name_account = $list_account[$account_id]['name'];

            return array_merge($item_product, compact('name_account'));

        }, $list_product);
    }

    /**
     * 获取客户和产品依附关系关系
     * @return array
     */
    protected function getRelationshipOfAccountProduct()
    {
        $type_id = 3;
        return D('FinanceAccountProduct')
            ->where(compact('type_id'))
            ->field('account_id,product_id')
            ->index('product_id')
            ->select();
    }

    /**
     * 获取客户列表
     * @return array
     */
    public function getAccountList()
    {
        return D('FinanceAccounts')
            ->index('id')
            ->select();
    }

    /**
     * 限制的用户列表（GET方法）
     */
    public function userListNoIdForGet()
    {
        $where = [];
        $contract_status = I('get.contract_status', '', 'trim');
        $status = I('get.status', '', 'trim');

        if ($contract_status) {
            $where['contract_status'] = new \MongoInt32($contract_status);
        }
        if ($status) {
            $where['status'] = new \MongoInt32($status);
        }

        $user_list = D('CuishouUser')
            ->where($where)
            ->field(['_id' => 0, 'id' => 1, 'developer' => 1])
            ->order('_id')
            ->select();

        return $user_list;
    }

    /**
     * 获取GET方法的Cache实列
     * @return mixed
     */
    public function getCacheInstanceForGet()
    {
        // cache instance
        $params = I('get.');
        $prefix = md5(__METHOD__ . serialize($params));
        return Cache::getInstance('File', ['expire' => '600', 'prefix' => $prefix]);
    }

    /**
     * 列表页分页
     * @param $user_list_stat
     * @param $Page
     * @return array
     */
    public function pageShowForList($user_list_stat)
    {
        $start = I('get.start', 0, 'intval');
        $limit = I('get.length', 30, 'intval');
        return array_slice($user_list_stat['user_list'], $start, $limit);
    }

    /**
     * 某个用户在某段时间的数据情况
     * @param array $where 条件
     * @param array $date_list 需要展示的列表
     * @return mixed
     */
    public function dailyStat($where, $date_list)
    {
        // 统计信息 
        $info_date_stat = $this->statGroupByItem($where, '$time');

        // 原始统计数据变成以‘2018-04-03’为索引的数组 && 计算覆盖率
        $info_date_stat = $this->tidyIndexFormatForDetailTongji($info_date_stat);

        // 将统计信息和需要展示的日期合并
        return $this->tidyDataForDetail($info_date_stat, $date_list);
    }

    /**
     * 统计信息合并到日期中(以天为单位)
     * @param $info_date_stat
     * @param $date_list
     * @return mixed
     */
    protected function tidyDataForDetail($info_date_stat, $date_list)
    {
        // 统计信息合并到日期中
        foreach ($date_list as $date => $date_stat_show) {
            $date_list[$date] = isset($info_date_stat[$date]) ? array_merge($this->item_base, $info_date_stat[$date]) : $this->item_base;
        }
        return $date_list;
    }

    /**
     * 原始的统计数据变成以‘2018-04-03’为索引的数组  && 计算覆盖率
     * @param array $info_date_stat
     * @return array
     */
    protected function tidyIndexFormatForDetailTongji($info_date_stat)
    {
        $info_date_format = [];
        if ($info_date_stat) {
            $info_date_stat = array_column($info_date_stat, null, '_id');
            foreach ($info_date_stat as $date => $date_stat) {
                // 计算覆盖率
                $date_stat = array_merge($this->item_base, $date_stat);
                $date_stat = $this->computeCoverage($date_stat);

                $date_format = date('Y-m-d', strtotime($date));
                $info_date_format[$date_format] = $date_stat;
            }
        }
        return $info_date_format;
    }


    /**
     * 列表时间上的制约
     * @return mixed
     */
    public function timeLimitForList()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        // check time
        $where_date = [];
        $where_date = $begin ? ['$gte' => date('Ymd', $begin)] : $where_date;
        $where_date = $end ? array_merge($where_date, ['$lte' => date('Ymd', $end)]) : $where_date;

        // default time
        if (!$where_date) {
            $where_date = date('Ymd');
        }
        return $where_date;
    }

    /**
     * 详情页的时间限制
     */
    public function timeLimitForDetail()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        // check time
        $where_date = [];
        $where_date = $begin ? ['$gte' => date('Ymd', $begin)] : $where_date;
        $where_date = $end ? array_merge($where_date, ['$lte' => date('Ymd', $end)]) : $where_date;

        // show date range
        if (!$where_date) {
            $begin_default = time() - 86400 * 30;
            $where_date = [
                '$gte' => date('Ymd', $begin_default)
            ];
        }
        return $where_date;
    }

    /**
     * 需要展示的日期的列表
     * @return array
     */
    public function showDateList()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $end = $end ? ($end + 86399) : '';

        //默认三十天, 前端代码确定了begin && end同时出现或者消失
        if (!$begin && !$end) {
            $begin = time() - 86400 * 30;
            $end = time();
        }
        return $this->dateList($begin, $end);
    }

    /**
     * 在某个条件下催收分各个指标的分组统计情况
     * @param array $where
     * @param string $group_type 分组的依据
     * @return array
     */
    public function statGroupByItem($where, $group_type = '$uid')
    {
        // total stat
        return (new CuishouDailyStatModel())->accessCount($where, $group_type);
    }

    /**
     * 获取单个用户在某段时间的指标的统计
     * @param $where
     * @param string $group_type
     * @return array|mixed
     */
    public function chooseOneStat($where, $group_type = '$uid')
    {
        // 统计信息
        $info_stat = $this->statGroupByItem($where, $group_type);
        $info_stat = isset($info_stat[0]) ? array_merge($this->item_base, $info_stat[0]) : $this->item_base;

        // 覆盖率计算
        return $this->computeCoverage($info_stat);
    }

    /**
     * 获取多个用户在某段时间的指标的统计
     * @param $user_list
     * @param array $where
     * @return array
     */
    public function personalStatForList($user_list, $where)
    {
        // 分组得到
        $personal_list = $this->statGroupByItem($where);
        $personal_list = $this->cuiShouCoverageForSingle($personal_list);

        // 将用户列表和分组得到的数据合并
        return $this->tidyDataForUserList($user_list, $personal_list);
    }

    /**
     * 遍历计算各个账户的催收覆盖率
     * @param $personal_list
     * @return array
     */
    protected function cuiShouCoverageForSingle($personal_list)
    {
        /*
         *  计算公式
         * （1）催收覆盖率=含催收号码的详单量/有效调用量
         * （2）疑似催收覆盖率=含疑似催收号码的详单量/有效调用量
         * （3）整体覆盖率=100%-不含催收也不含疑似催收的详单量/有效调用量
         * */

        return array_map(function ($item) {
            // 确保item包含基本的8个元素
            $item = array_merge($this->item_base, $item);
            return $this->computeCoverage($item);
        }, $personal_list);
    }

    /**
     * 计算覆盖率
     * @param array $item 计算覆盖率的单元
     * @return array
     */
    protected function computeCoverage($item)
    {
        // 有效调用量为0的时候,覆盖率为0.000
        if (isset($item['success_counts']) && $item['success_counts']) {
            $item['converage_dunning'] = 100 * round($item['dunning_times'] / $item['success_counts'], 3);
            $item['converage_not_sure_dunning'] = 100 * round($item['not_sure_dunning_times'] / $item['success_counts'], 3);
            $item['coverage_both'] = 100 * round($item['both_times'] / $item['success_counts'], 3);
            $item['converage_all'] = 100 * round((1 - $item['not_times'] / $item['success_counts']), 3);
        } else {
            $item['converage_dunning'] = 'NA';
            $item['converage_not_sure_dunning'] = 'NA';
            $item['coverage_both'] = 'NA';
            $item['converage_all'] = 'NA';
        }

        return $item;
    }

    /**
     * 将用户列表和分组得到的数据合并(以用户列表为基准,将数据补齐)
     * @param $user_list
     * @param $personal_list
     * @return array 新版的用户列表和数据统计
     */
    protected function tidyDataForUserList($user_list, $personal_list)
    {
        // merge stat and user info
        $personal_list = array_column($personal_list, null, '_id');

        $total_stat = $this->item_base;

        foreach ($user_list as &$user) {
            // 确保每个产品包含基本的8个元素
            $stat = isset($personal_list[$user['id']]) ? array_merge($this->item_base, $personal_list[$user['id']]) : $this->item_base;
            $user = array_merge($user, $stat);

            // 总计统计
            $total_stat['access_counts'] += $stat['access_counts'];
            $total_stat['success_counts'] += $stat['success_counts'];
            $total_stat['dunning_times'] += $stat['dunning_times'];
            $total_stat['not_sure_dunning_times'] += $stat['not_sure_dunning_times'];
            $total_stat['not_times'] += $stat['not_times'];
            $total_stat['both_times'] += $stat['both_times'];
        }

        $sort = $this->getStartSortParam();
        if (isset($sort[1]) && strtolower($sort[1]) == 'desc') {
            $user_list = $this->multiArraySort($user_list, $sort[0], SORT_DESC);
        }
        if (isset($sort[1]) && strtolower($sort[1]) == 'asc') {
            $user_list = $this->multiArraySort($user_list, $sort[0], SORT_ASC);
        }

        // 计算覆盖率
        $total_stat = $this->computeCoverage($total_stat);
        return compact('user_list', 'total_stat');
    }

    public function getStartSortParam()
    {
        $order = I('get.order', '', 'trim');
        if ($order && strpos($order, 'id') === false) {
            $order = explode(' ', $order);
        }
        return $order;
    }

    public function multiArraySort($multi_array, $sort_key, $sort = SORT_ASC)
    {
        if (is_array($multi_array)) {
            foreach ($multi_array as $row_array) {
                if (is_array($row_array)) {
                    $key_array[] = $row_array[$sort_key];
                } else {
                    return false;
                }
            }
        }else{
            return false;
        }
        array_multisort($key_array, $sort, $multi_array);
        return $multi_array;
    }

    // 供筛选的客户列表
    public function clientListForPost()
    {
        $contract_status = I('post.contract_status', '', 'trim');
        $status = I('post.status', '', 'trim');
        $where = [];
        if ($contract_status) {
            $where['contract_status'] = new \MongoInt32($contract_status);
        }
        if ($status) {
            $where['status'] = new \MongoInt32($status);
        }

        $user_list = D('CuishouUser')
            ->where($where)
            ->field(['_id' => 0, 'id' => 1, 'developer' => 1])
            ->order('_id')
            ->select();
        return $user_list;
    }

    /**
     * 限制的用户列表（GET方法）
     */
    public function userListDependGet()
    {
        // 条件
        $where = $this->genConditionForList();

        $order = I('get.order', '_id', 'trim');
        $user_list = D('CuishouUser')
            ->where($where)
            ->field(['_id' => 0, 'id' => 1, 'developer' => 1])
            ->order($order)
            ->select();

        return $user_list;
    }

    /**
     * 为统计列表生成条件
     * @return array
     */
    protected function genConditionForList()
    {
        // 基础信息限制
        $limit_base = $this->limitBaseInfoForGet();

        // id && account_id限制
        $limit_ids = $this->limitIdsForGet();
        return array_merge($limit_base, $limit_ids);
    }

    /**
     * 限制状态
     */
    protected function limitBaseInfoForGet()
    {
        $where = [];

        $contract_status = I('get.contract_status', '', 'trim');
        $status = I('get.status', '', 'trim');

        if ($contract_status) {
            $where['contract_status'] = new \MongoInt32($contract_status);
        }
        if ($status) {
            $where['status'] = new \MongoInt32($status);
        }
        return $where;
    }

    /**
     * 产品id的限制
     * @return array
     */
    protected function limitIdsForGet()
    {
        $id = I('get.id', '', 'trim');
        $account_id = I('get.account_id', '', 'trim');

        // id && account都没有限制
        if (!$account_id && !$id) {
            return [];
        }

        // 只限制了id
        if (!$account_id && $id) {
            $id = new \MongoInt32($id);
            return compact('id');
        }

        // 选定客户下的
        $list_ids = $this->getListProductIdsOfAccountForGet();

        // 只是限制了客户
        if ($account_id && !$id) {
            $id = ['in', $list_ids];
            return compact('id');
        }

        // 同时限定了id和客户
        $id = new \MongoInt32($id);
        if (in_array($id, $list_ids)) {
            // 如果选中产品在选中的客户里面
            return compact('id');
        } else {
            // 如果选中的产品和客户冲突
            return ['id' => '永远不会有数据,哈哈'];
        }
    }

    /**
     * GET方式获取特定客户下辖的催收产品ID列表
     * @return array
     */
    protected function getListProductIdsOfAccountForGet()
    {
        $account_id = I('get.account_id', '', 'trim');

        if (!$account_id) {
            return [];
        }

        // 返回选中客户下辖的催收分产品ID
        $type_id = 3;
        $list_relationship = D('FinanceAccountProduct')
            ->where(compact('type_id', 'account_id'))
            ->select();

        $list_relationship = array_column($list_relationship, null, 'product_id');
        $list_ids = array_keys($list_relationship);

        // id转成int类型
        return array_map(function ($item) {
            return new \MongoInt32($item);
        }, $list_ids);
    }

    /**
     * 获取特定客户下辖的产品列表
     * @return array
     */
    public function getProductListOfAccount()
    {
        $account_id = I('get.account_id', '', 'trim');
        $where = [];
        if ($account_id) {
            $where = compact('account_id');
        }
        return D('FinanceAccountProduct')->where($where)
            ->select();
    }


    /**
     * 限制的用户列表（GET方法  这个是只是适用于Detail方法,因为它需要在不同用户之间进行切换, 所以不需要id的限制）
     * status && contract_status的限制是为了方便扩展
     */
    public function userListDependGetForDetail()
    {
        $where = [];
        $contract_status = I('get.contract_status', '', 'trim');
        $status = I('get.status', '', 'trim');

        if ($contract_status) {
            $where['contract_status'] = new \MongoInt32($contract_status);
        }
        if ($status) {
            $where['status'] = new \MongoInt32($status);
        }

        $user_list = D('CuishouUser')
            ->where($where)
            ->field(['_id' => 0, 'id' => 1, 'developer' => 1])
            ->order('_id')
            ->select();

        return $user_list;
    }

    /**
     * 详情页要展示的date list, 这个时间是倒叙的
     * @param integer $begin
     * @param integer $end
     * @return array
     */
    public function dateList($begin, $end)
    {
        $date_list = [];

        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }

        return $date_list;
    }
}
