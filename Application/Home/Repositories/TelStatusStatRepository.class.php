<?php
namespace Home\Repositories;

class TelStatusStatRepository extends BaseRepository
{
    protected $listUrl = '/Statistics/getPhoneStatusInfoPeriod';

    protected $detailUrl = '/Statistics/getPhoneStatusInfoDetail';

    protected $productId = 801;

    /**
     * 获取开通该产品的客户信息
     * @param  array  $param 查询参数
     * @return array
     */
    public function getCustomerList($param = [])
    {
        // 账号产品关联信息
        $account_product = $this->getAccountProduct($param);
        // 账号信息
        $account = $this->getAccountByParam($account_product, $param);
        // 客户信息
        $customer = $this->getCustomerListByAccount($account, $param);
        if ($customer && $account) {
            array_walk($customer, function(&$v, $k, $p) {
                $v['account_list'] = $p[$k];
                $v['apikey'] = array_column($p[$k], 'apikey');
            }, $account);
        }
        return $customer;
    }

    /**
     * 获取账号产品关联信息
     * @param  array $param 查询参数
     * @return array
     */
    public function getAccountProduct($param)
    {
        $where['product_id'] = $this->productId;
        if (isset($param['contract_status']) && $param['contract_status']) {
            $where['contract_status'] = $param['contract_status'];
        }
        $account_product = D('Account/AccountProduct')->where($where)->field('account_id')->select();
        return $account_product;
    }

    /**
     * 获取账号信息
     * @param  array $account_product 产品账号关联信息
     * @param  array $param           查询参数
     * @return array
     */
    public function getAccountByParam($account_product, $param)
    {
        if (!$account_product) {
            return [];
        }
        $account_id = array_column($account_product, 'account_id');
        $account_where['account_id'] = ['in', $account_id];
        if ($param['account_id'] && !in_array($param['account_id'], $account_id)) {
            return [];
        }
        if ($param['account_id']) {
            $account_where['account_id'] = $param['account_id'];
        }
        $account = D('Account/Account')->where($account_where)->field('account_id, account_name, customer_id, apikey')->select();
        $list = $apikey = [];
        if ($account && is_array($account)) {
            foreach ($account as $key => $value) {
                $list[$value['customer_id']][] = $value;
            }
        }
        return $list;
    }

    /**
     * 根据账号ID获取客户列表
     * @param  array $account 账号信息
     * @param  array $param   查询参数
     * @return array
     */
    public function getCustomerListByAccount($account, $param)
    {
        if (!$account) {
            return [];
        }
        $customer_id = array_keys($account);
        $customer_where['customer_id'] = ['in', $customer_id];
        if ($param['customer_id'] && !in_array($param['customer_id'], $customer_id)) {
            return [];
        }
        if ($param['customer_id']) {
            $customer_where['customer_id'] = $param['customer_id'];
        }
        $customer = D('Account/Customer')->where($customer_where)->field('customer_id, name')->select();
        $customer = $customer ? array_column($customer, NULL, 'customer_id') : [];
        return $customer;
    }

    /**
     * 获取调用量
     * @return array
     */
    public function getTelStatusListStat()
    {
        // 客户信息
        $where = $this->getTelStatusStatParam();
        $customer = $this->getCustomerList($where);
        // 调用量
        $param = $this->getTimeListParam();
        $apikey = array_column($customer, 'apikey');
        $param['client_key'] = $this->arrayMultiSingle($apikey);
        $stat = $this->getTelStatusStatListApi($param);
        $list = [];
        if ($customer && is_array($customer)) {
            foreach ($customer as $key => $value) {
                $stat_num = $this->getAccountListStat($value['account_list'], $stat);
                $list[$key] = array_merge($value, $stat_num);
            }
        }
        // 总计
        $total = $this->getTelStatusStatTotal($stat);
        return compact('list', 'total');
    }

    /**
     * 多维数组转变为以为数组
     * @param  array $array 多维数组
     * @return array
     */
    public function arrayMultiSingle($array)
    {
        static $list = array();
        //对多维数组进行循环
        foreach ($array as $value) {
            if (is_array($value)) {
                $this->arrayMultiSingle($value);
            } else {
                $list[] = $value;
            }
        }
        return $list;
    }

    /**
     * 客户调用量
     * @param  array $account_list 账号信息
     * @param  array $stat         调用量
     * @return array
     */
    public function getAccountListStat($account_list, $stat)
    {
        $num = [];
        if ($account_list && is_array($account_list)) {
            foreach ($account_list as $key => $value) {
                $num['total'] += isset($stat[$value['apikey']]['total']) ? $stat[$value['apikey']]['total'] : 0;
                $num['errorPhoneNum'] += isset($stat[$value['apikey']]['errorPhoneNum']) ? $stat[$value['apikey']]['errorPhoneNum'] : 0;
                $num['correctPhoneNum'] += isset($stat[$value['apikey']]['correctPhoneNum']) ? $stat[$value['apikey']]['correctPhoneNum'] : 0;
                $num['getPhoneNum'] += isset($stat[$value['apikey']]['getPhoneNum']) ? $stat[$value['apikey']]['getPhoneNum'] : 0;
                $num['usePhoneNum'] += isset($stat[$value['apikey']]['usePhoneNum']) ? $stat[$value['apikey']]['usePhoneNum'] : 0;
                $num['stopPhoneNum'] += isset($stat[$value['apikey']]['stopPhoneNum']) ? $stat[$value['apikey']]['stopPhoneNum'] : 0;
                $num['noKnowPhoneNum'] += isset($stat[$value['apikey']]['noKnowPhoneNum']) ? $stat[$value['apikey']]['noKnowPhoneNum'] : 0;
            }
            $num['usePhoneRatio'] = empty($num['correctPhoneNum']) ? 'NA' : round($num['usePhoneNum']/$num['correctPhoneNum']*100, 2).'%';
            $num['stopPhoneRatio'] = empty($num['correctPhoneNum']) ? 'NA' : round($num['stopPhoneNum']/$num['correctPhoneNum']*100, 2).'%';
            $num['noKnowPhoneRatio'] = empty($num['correctPhoneNum']) ? 'NA' : round($num['noKnowPhoneNum']/$num['correctPhoneNum']*100, 2).'%';
        }
        return $num;
    }

    /**
     * 调用量总计
     * @param  array $stat 调用量
     * @return array
     */
    public function getTelStatusStatTotal($stat)
    {
        $total['total'] = array_sum(array_column($stat, 'total'));
        $total['errorPhoneNum'] = array_sum(array_column($stat, 'errorPhoneNum'));
        $total['correctPhoneNum'] = array_sum(array_column($stat, 'correctPhoneNum'));
        $total['getPhoneNum'] = array_sum(array_column($stat, 'getPhoneNum'));
        $total['usePhoneNum'] = array_sum(array_column($stat, 'usePhoneNum'));
        $total['stopPhoneNum'] = array_sum(array_column($stat, 'stopPhoneNum'));
        $total['noKnowPhoneNum'] = array_sum(array_column($stat, 'noKnowPhoneNum'));
        $total['usePhoneRatio'] = empty($total['correctPhoneNum']) ? 'NA' : round($total['usePhoneNum']/$total['correctPhoneNum']*100, 2).'%';
        $total['stopPhoneRatio'] = empty($total['correctPhoneNum']) ? 'NA' : round($total['stopPhoneNum']/$total['correctPhoneNum']*100, 2).'%';
        $total['noKnowPhoneRatio'] = empty($total['correctPhoneNum']) ? 'NA' : round($total['noKnowPhoneNum']/$total['correctPhoneNum']*100, 2).'%';
        return $total;
    }

    public function getTelStatusDetailStat()
    {
        $where = $this->getTelStatusStatParam();
        $customer = $this->getCustomerList($where);

        $param = $this->getTimeDetailParam();
        $apikey = array_column($customer, 'apikey');
        $param['client_key'] = $this->arrayMultiSingle($apikey);
        $stat = $this->getTelStatusStatDetailApi($param);
        $list = [];
        if ($stat && is_array($stat)) {
            foreach ($stat as $key => $value) {
                $date = date('Y-m-d', strtotime($key));
                $list[$date] = $value;
            }
        }
        $date = $this->getDateList();
        $list = array_merge($date, $list);
        $total = $this->getTelStatusStatTotal($stat);
        return compact('list', 'total');
    }

    /**
     * 获取时间列表
     * @param  number $begin 开始时间
     * @param  number $end   结束时间
     * @return list
     */
    public function getDateList()
    {
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end = empty($end) ? strtotime(date('Y-m-d 23:59:59')) : strtotime($end);
        $begin = empty($begin) ? strtotime(date('Ymd', strtotime('-1 days'))) : strtotime($begin);

        $date_list = [];
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }

    /**
     * 基本查询参数
     * @return array
     */
    public function getTelStatusStatParam()
    {
        $account_id = I('get.account_id', '', 'trim');
        $customer_id = I('get.customer_id', '', 'trim');
        $contract_status = I('get.contract_status', 0, 'trim');

        return compact('account_id', 'customer_id', 'contract_status');
    }

    /**
     * 获取列表时间限制条件
     * @return array
     */
    public function getTimeListParam()
    {
        $start_time = I('get.begin', '', 'trim');
        $end_time = I('get.end', '', 'trim');

        $start_time = $start_time ? date('Ymd', strtotime($start_time)) : date('Ymd');
        $end_time = $end_time ? date('Ymd', strtotime($end_time)) : date('Ymd');

        return compact('start_time', 'end_time');
    }

    /**
     * 获取详情时间限制条件
     * @return array
     */
    public function getTimeDetailParam()
    {
        $start_time = I('get.begin', '', 'trim');
        $end_time = I('get.end', '', 'trim');

        $start_time = $start_time ? date('Ymd', strtotime($start_time)) : date('Ymd', strtotime('-1 days'));
        $end_time = $end_time ? date('Ymd', strtotime($end_time)) : date('Ymd');

        return compact('start_time', 'end_time');
    }

    /**
     * 获取号码状态查询列表API
     * @param  array $param 查询条件
     * @return array
     */
    public function getTelStatusStatListApi($param)
    {
        $param['key'] = '31064ac828ebea24f969485e336262af';
        $param['product_id'] = $this->productId;
        $domain = C('TEL_STATUS_CONFIG')['domain'];
        $url = $domain.$this->listUrl;
        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = is_array($res) ? $res : json_decode($res, true);
        if (!isset($res['code']) || $res['code'] != 10000) {
            return [];
        }
        return $res['data'];
    }

    /**
     * 获取号码状态查询详情API
     * @param  array $param 查询条件
     * @return array
     */
    public function getTelStatusStatDetailApi($param)
    {
        $param['key'] = '31064ac828ebea24f969485e336262af';
        $param['product_id'] = $this->productId;
        $domain = C('TEL_STATUS_CONFIG')['domain'];
        $url = $domain.$this->detailUrl;
        $res = $this->getCurl('POST', $url, $param);
        if (!$res) {
            return [];
        }
        $res = is_array($res) ? $res : json_decode($res, true);
        if (!isset($res['code']) || $res['code'] != 10000) {
            return [];
        }
        return $res['data'];
    }
}