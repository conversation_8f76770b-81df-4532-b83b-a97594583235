<?php

namespace Home\Repositories;

class BmCrawlerFileRepository
{
    /**
     * (列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '账号ID,账号名称,所属用户,状态,签约状态,账号邮箱,接入方式,APPID,是否生成报告,是否输出催收分,Time';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);
        foreach ($stat_list as $stat_data) {
            // 数据补全
            $file_str = '"'.$stat_data['id'] . '","' . $stat_data['developer'] . '","' . $stat_data['account_name'] . '","'. $stat_data['status'] . '","'
                . $stat_data['contract_status'] . '","' . $stat_data['email'] . '","' .
                $stat_data['source'] . '","' . $stat_data['appid'] . '","' . $stat_data['need_report'] . '","' .
                $stat_data['need_dunning'].'","' . $stat_data['timeStr'].'"';

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
