<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/7 0007
 * Time: 14:13
 */

namespace Home\Repositories\Upstream;


use Account\Model\ProductModel;

class Upstream200 implements UpstreamDriver
{
    protected $product_id;

    public function __construct()
    {
        $this->product_id = ProductModel::where('father_id', 200)->column('product_id');

        halt($this->product_id);
    }

    public function getUpstreamList()
    {

    }
}