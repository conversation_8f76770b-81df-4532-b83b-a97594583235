<?php
namespace Home\Repositories;

class DataCheckStatRepository extends BaseRepository
{
    /**
     * 获取开通邦秒验的所有客户
     * @param  array $product_ids 产品IDS
     * @param  array $param       查询条件
     * @return array
     */
    public function getCustomerList($product_ids = [], $param = [])
    {
        if (empty($product_ids)) {
            return [];
        }
        $where['product_id'] = ['in', $product_ids];
        if (isset($param['contract_status']) && !empty($param['contract_status'])) {
            $where['contract_status'] = $param['contract_status'];
        }
        $account_product = D('Account/AccountProduct')->field('account_id')->where($where)->select();
        if (empty($account_product)) {
            return [];
        }
        $account_id = array_column($account_product, 'account_id');
        $account_id = array_unique($account_id);
        if (isset($param['account_id']) && !empty($param['account_id'])) {
            if (!in_array($param['account_id'], $account_id)) {
                return [];
            }
            $account_where['account_id'] = $param['account_id'];
        } else {
            $account_where['account_id'] = ['in', $account_id];
        }

        if (isset($param['customer_id']) && !empty($param['customer_id'])) {
            $account_where['customer_id'] = $param['customer_id'];
        }
        $account = D('Account/Account')->field('customer_id, account_id, account_name, father_id, apikey')
                                       ->where($account_where)
                                       ->order('customer_id desc')
                                       ->select();
        if (!$account) {
            return [];
        }

        $apikey = array_column($account, 'apikey');
        $apikey = implode(',', array_unique($apikey));
        $customer_id = array_column($account, 'customer_id');
        $customer_id = array_unique($customer_id);
        $customer_where['customer_id'] = ['in', $customer_id];
        $list = D('Account/Customer')->field('customer_id, name')
                                     ->where($customer_where)
                                     ->order('customer_id desc')
                                     ->index('customer_id')
                                     ->select();
        foreach ($account as $key => $value) {
            if (isset($list[$value['customer_id']])) {
                if ($value['father_id'] != '0') {
                    $list[$value['customer_id']]['account_list'][] = $value;
                }
            }
        }
        return compact('list', 'apikey');
    }

    /**
     * 根据条件获取客户列表
     * @param  string $product_ids 产品IDS
     * @return array
     */
    public function getCustomerListParam($product_ids = '')
    {
        //查询条件
        $where = $this->getDataCheckListParam();
        return $this->getCustomerList($product_ids, $where);
    }

    /**
     * 列表获取参数
     * @return array
     */
    public function getDataCheckListParam()
    {
        $where = '';
        $customer_id = I('get.customer_id', '', 'trim');
        $contract_status = I('get.contract_status', 0, 'intval');
        if ($contract_status) {
            $where['contract_status'] = $contract_status;
        }
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }

        return $where;
    }

    /**
     * 邦秒验统计详情
     * @param  array  $product_ids 产品ID列表
     * @return array
     */
    public function getDetailCustomerList($product_ids = [])
    {
        $where = $this->getDatacheckDetailParam();
        if (!$where) {
            return [];
        }
        return $this->getCustomerList($product_ids, $where);
    }

    /**
     * 邦秒验数据统计列表
     * @return array
     */
    public function getDatacheckDetailParam()
    {
        $where = '';
        $customer_id = I('get.customer_id', '', 'trim');
        $account_id = I('get.account_id', '', 'trim');
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }
        if ($account_id) {
            $where['account_id'] = $account_id;
        }
        return $where;
    }

    /**
     * 日期列表
     * @return array
     */
    public function getDateList()
    {
        $date_list = [];
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end = empty($end) ? strtotime(date('Y-m-d 23:59:59')) : strtotime($end);
        $begin = empty($begin) ? ($end-86400*30) : strtotime($begin);
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }


    /**
     * 日期列表
     * @return array
     */
    public function getApiDateList()
    {
        $date_list = [];
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end = empty($end) ? strtotime(date('Y-m-d 23:59:59')) : strtotime($end);
        $begin = empty($begin) ? $end : strtotime($begin);
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }
}