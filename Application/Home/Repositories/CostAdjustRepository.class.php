<?php

namespace Home\Repositories;

use Account\Model\CustomerModel;
use Common\Model\SystemUserModel;
use Home\Model\BillProductIncomeV2Model;
use Stat\Model\ChannelAccountAdjust;
use Common\Model\CommonEnumModel;
use Stat\Model\ProductModel;
use Home\Model\ChannelModel;

//渠道成本调整
class CostAdjustRepository
{
    private $model;
    /**
     * @var \PHPExcel
     **/
    private $objPHPExcel;

    // 导出文件名
    protected $export_title = '渠道成本调整数据';

    public function __construct()
    {
        $this->model = new ChannelAccountAdjust();
    }

    /**
     * 
     *
     * @return void
     */
	public function getParamByGet(){
		//获取查询条件
		$where = [];
		if (!empty(I('get.customer_id'))) {
			$where['channel_account_adjust.customer_id'] = I('get.customer_id');
		}
		if (!empty(I('get.account_id'))) {
			$where['channel_account_adjust.account_id'] = I('get.account_id');
		}
		if ('-1'!==I('get.source','-1')) {
			$where['channel_account_adjust.source'] = I('get.source');
		}
		if (!empty(I('get.channel'))) {
			$where['channel_account_adjust.channel_id'] = I('get.channel');
		}
		if (!empty(I('get.product_id'))) {
			$where['channel_account_adjust.product_id'] = I('get.product_id');
		}
		if (!empty(I('get.start_date')) && !empty(I('get.end_date'))) {
			$where['channel_account_adjust.date'] = ['between', [I('get.start_date'), I('get.end_date')]];
		} else if (!empty(I('get.start_date'))) {
			$where['channel_account_adjust.date'] = ['EGT', I('get.start_date')];
		} else if (!empty(I('get.end_date'))) {
			$where['channel_account_adjust.date'] = ['ELT', I('get.end_date')];
		}
        return $where;
	}

    /**
     * 引入Excel
     *
     * @access private
     *
     * @return void
     **/
    private function include_phpExcel()
    {
        include LIB_PATH . '/Org/PHPExcel/PHPExcel.php';
        include LIB_PATH . '/Org/PHPExcel/PHPExcel/Writer/Excel2007.php';
        //$this->objPHPExcel = new \PHPExcel();
    }

    /**
     * 查询数据的批量导出
     *
     * @access public
     *
     * @return void
     **/
    public function file_out()
    {
        //获取查询条件
        $where = $this->getParamByGet();
        $params = I('get.');
        $productModel = new ProductModel();
        $pids = $productModel->getGeneralProduct();
        $pids = array_column($pids, 'product_id');
        $productInfo    = $productModel->field('product_id,product_name')
            ->where([
                'product_id' => ['in', $pids],
                '_logic'     => 'OR',
                'father_id'  => ['gt', 0],
            ])
            ->select();
        $productInfo    = array_column($productInfo, 'product_name', 'product_id');
		
		$customerInfo    = (new CustomerModel())->field('name,customer_id')									->select();
		$customerInfo    = array_column($customerInfo, 'name', 'customer_id');

        $channelInfo    = (new ChannelModel())->field('label, channel_id')->select();
        $channelInfo    = array_column($channelInfo, 'label', 'channel_id');

        $list_data = $this->model->field([
			'channel_account_adjust.id',
			'channel_account_adjust.customer_id',
			'account.account_name',
            'account.apikey',
            'channel_interface.label as interface_name',
			'channel_account_adjust.product_id',
            'channel_account_adjust.channel_id',
            'channel_account_adjust.encrypt',
			'channel_account_adjust.date',
			'channel_account_adjust.title',
			'channel_account_adjust.money',
			'channel_account_adjust.fee_number',
			'channel_account_adjust.source',
            'channel_account_adjust.create_time',
			'channel_account_adjust.update_time',
		])
							->join('account ON account.account_id = channel_account_adjust.account_id')
                            ->join('left join channel_interface ON channel_interface.id = channel_account_adjust.interface_id')
							->where($where)
							->order('channel_account_adjust.date DESC')
							->select();
		$sourcePairs = (new CommonEnumModel())->getEnumPairs(1);

        $apikey_arr = $product_id_arr = $api_key_customer_id_map = $haveIncome = [];
        foreach($list_data as $info_data){
            $_apikey = $info_data['apikey'];
            $_product_id = $info_data['product_id'];
            $apikey_arr[$_apikey] = $_apikey;
            $product_id_arr[$_product_id] = $_product_id;
            $api_key_customer_id_map[$_apikey] = $info_data['customer_id'];
        }

        $start_date = I('get.start_date');
        $start_date = empty($start_date) ? '' : str_replace('-', '', $start_date);
        $_haveIncome = (new BillProductIncomeV2Model())->isHaveIncome($start_date, $apikey_arr, $product_id_arr);

        foreach($_haveIncome as $item){
            $_customer_id = $api_key_customer_id_map[$item['apikey']];
            $_product_id = $item['product_id'];
            $haveIncome[$_customer_id][$_product_id] = 1;
        }

		array_walk($list_data, function (&$item) use ($sourcePairs,$productInfo,$customerInfo,$channelInfo,$haveIncome) {
			$item['source_name'] = isset($sourcePairs[$item['source']])?$sourcePairs[$item['source']]:'--';
            $item['name'] = isset($customerInfo[$item['customer_id']])?$customerInfo[$item['customer_id']]:'--';
            $item['product_name'] = isset($productInfo[$item['product_id']])?$productInfo[$item['product_id']]:'--';
            $item['label'] = isset($channelInfo[$item['channel_id']])?$channelInfo[$item['channel_id']]:'--';
            // 计算新老收入
            $item['is_old_income'] = isset($haveIncome[$item['customer_id']][$item['product_id']]) ? '是' : '否';
            $item['create_time'] = date("Y-m-d H:i:s",$item['create_time']);
            $item['update_time'] = $item['update_time'] > 0 ? date("Y-m-d H:i:s",$item['update_time']) : $item['create_time'];
        });


        //用户数据
        $user_data = (new SystemUserModel())->alias('u')->field('u.realname,u.username,d.dept_name')->join('crs_system_dept as d on u.dept_id = d.dept_id', 'left')->select();
        $user_data = array_column($user_data, null, 'username');

        $this->ini_write_excel();
        //将数据写入Excel中
        $max_row = $this->write_data($list_data, $user_data);
        //统一设置Excel样式
        $this->setStyle($max_row);
        //下载Excel
        $this->download($params);
        dump($list_data);
        die;
    }

    /**
     * Excel导出初始化对象
     *
     * @access private
     *
     * @return void
     **/
    private function ini_write_excel()
    {
        //引入phpExcel
        $this->include_phpExcel();
        //初始化phpExcelObject
        $this->objPHPExcel = new \PHPExcel();
        //设置Sheet标题
        $this->objPHPExcel->setActiveSheetIndex(0);
        $this->objPHPExcel->getActiveSheet()->setTitle('渠道成本调整');
        //设置标题信息
        $title = [
            'A' => [
                'name' => '客户ID',
                'width' => 18
            ], 'B' => [
                'name' => '客户名称',
                'width' => 20
            ], 'C' => [
                'name' => '标题',
                'width' => 28
            ], 'D' => [
                'name' => '产品名称',
                'width' => 28
            ], 'E' => [
                'name' => '渠道名称',
                'width' => 18
            ], 'F' => [
                'name' => '接口名称',
                'width' => 25
            ], 'G' => [
                'name' => '加密方式',
                'width' => 12
            ], 'H' => [
                'name' => '调整日期',
                'width' => 15
            ], 'I' => [
                'name' => '计费用量',
                'width' => 12
            ], 'J' => [
                'name' => '调整费用',
                'width' => 12,
            ], 'K' => [
                'name' => '来源',
                'width' => 10,
            ], 'L' => [
                'name' => '是否为老收入',
                'width' => 10,
            ], 'M' => [
                'name' => '添加时间',
                'width' => 20,
            ], 'N' => [
                'name' => '更新时间',
                'width' => 20,
            ]
        ];
        foreach ($title as $col => $value) {
            $this->objPHPExcel->getActiveSheet()->setCellValue($col . '1', $value['name']);
            //设置宽度
            $this->objPHPExcel->getActiveSheet()->getColumnDimension($col)->setWidth($value['width']);
        }
        //设置文字加粗
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:K1')->getFont()->setBold(true);
    }

    /**
     * 将数据写到Excel中
     *
     * @access private
     *
     * @param $data array 数据
     * @param $user_data array 用户信息
     *
     * @return int 行数
     **/
    private function write_data($data, $user_data){
        $this->objPHPExcel;
        $row = 2;
        foreach ($data as $item) {
            $customer_type = array_flip(CustomerModel::$customerType)[$item['customer_type']];
            $this->objPHPExcel->getActiveSheet()->setCellValueExplicit('A' . $row, $item['customer_id'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $item['name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $item['title'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('D' . $row, $item['product_name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('E' . $row, $item['label'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('F' . $row, $item['interface_name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('G' . $row, $item['encrypt'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('H' . $row, $item['date'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('I' . $row, $item['fee_number'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('J' . $row, $item['money'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('K' . $row, $item['source_name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('L' . $row, $item['is_old_income'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('M' . $row, $item['create_time'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('N' . $row, $item['update_time'], \PHPExcel_Cell_DataType::TYPE_STRING);
            // $this->objPHPExcel->getActiveSheet()->setCellValue('L' . $row, ($item['status'] < 2 ? '未认款' : ($item['status'] == 2 ? '已认款' : '已拆单')));
            // $this->objPHPExcel->getActiveSheet()->setCellValue('M' . $row, $item['source_label']);

            $row++;
        }
        return $row--;
    }

    /**
     * 设置Excel样式
     *
     * @access private
     *
     * @param $max_row integer 行数
     *
     * @return void
     **/
    private function setStyle($max_row)
    {
        $cell = 'A1:K' . $max_row;
        //水平居中
        $this->objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //垂直居中
        $this->objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        //设置表格高度
        $this->objPHPExcel->getActiveSheet()->getRowDimension('1:' . $max_row)->setRowHeight(18);
    }

    /**
     * 下载Excel
     *
     * @access private
     *
     * @param $params array 当前get的参数
     *
     * @return void
     **/
    private function download($params)
    {
        $filename = $this->export_title . $params['start_date'] . '_' . $params['end_date'];
        ob_end_clean();//清除缓冲区,避免乱码
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');
        $objWriter = new \PHPExcel_Writer_Excel5($this->objPHPExcel);
        $objWriter->save('php://output');
    }

}
