<?php

namespace Home\Repositories;

class CuishouShortStatRepository extends BaseRepository
{
    public function index()
    {
        // 初始化的参数
        return $this->getIniParamsForList();
    }

    /**
     * 统计列表的初始化参数
     * @return string
     */
    protected function getIniParamsForList()
    {
        $begin = $end = date('Y-m-d');
        return json_encode(compact('begin', 'end'), JSON_UNESCAPED_UNICODE);
    }


    /**
     * 为统计列表生成初始化参数
     * @return array
     */
    public function genIniParamsForDetail()
    {
        // 时间参数
        $params_time = $this->genTimeParamsForDetail();

        // 其他参数
        $params_other = $this->genOtherParamsForDetail();
        return array_merge($params_other, $params_time);
    }

    /**
     * 生成时间参数
     */
    protected function genTimeParamsForDetail()
    {
        $time_begin = $this->lastMonthToday(time());
        $time_end = date('Y-m-d');
        return compact('time_begin', 'time_end');
    }

    /**
     * 其他参数
     * @return array
     */
    protected function genOtherParamsForDetail()
    {
        return I('get.');
    }

}