<?php

namespace Home\Repositories;

use Account\Model\ProductModel;
use Home\Model\UpstreamChannelPriceModel;
use Home\Repositories\Upstream\Upstream200;
use Home\Repositories\Upstream\UpstreamDriver;
use Home\Model\UpstreamChannelModel;
use Common\ORG\Page;

class UpstreamRepository
{
    /** @var int  邦信分快捷版产品ID */
    private $product_id_shortcuts = 210;

    /** @var int 邦秒验产品ID */
    private $product_id_verification = 200;


    /**
     * 上游邦妙验统计详情前置条件re
     * @return array
     */
    public function verificationDetails()
    {
        list($begin, $end, $product_id, $upstream_choose) = [
            I('get.begin') ? I('get.begin') : date('Y-m-d'),
            I('get.end') ? I('get.end') : date('Y-m-d'),
            trim(I('get.product_id')),
            trim(I('get.upstream')),
        ];
        list($upstream, $list_products) = $this->_getUpstreamInfoForVerification();

        $list_api          = C('LIST_API_URL')['back_api_upstream_verification_details'];
        $backend_api_sorts = C('LIST_API_URL')['backend_api_sorts'];
        $download_api      = C('LIST_API_URL')['back_api_upstream_verification_details_excel'];

        return compact('begin', 'end', 'backend_api_sorts', 'upstream', 'list_products', 'list_api', 'download_api',
            'product_id', 'upstream_choose');
    }

    /**
     * 上游邦妙验统计列表前置条件
     * @return array
     */
    public function verifications()
    {
        $begin = $end = date('Y-m-d');
        list($upstream, $list_products) = $this->_getUpstreamInfoForVerification();

        $list_api          = C('LIST_API_URL')['back_api_upstream_verification'];
        $backend_api_sorts = C('LIST_API_URL')['backend_api_sorts'];
        $download_api      = C('LIST_API_URL')['back_api_upstream_verification_excel'];
        $download_day_api  = C('LIST_API_URL')['back_api_upstream_verification_day_excel'];

        return compact('begin', 'end', 'backend_api_sorts', 'download_day_api', 'upstream', 'list_products', 'list_api',
            'download_api');
    }

    /**
     * 配置
     */
    private function _getUpstreamInfoForVerification()
    {
        // 配置
        $product_id   = $this->product_id_verification;
        $product      = ProductModel::getOneItem(compact('product_id'), 'channel_stat');
        $channel_stat = json_decode($product['channel_stat'], true);

        // 子产品列表
        $father_id     = $this->product_id_verification;
        $list_products = ProductModel::getListProduct(compact('father_id'), ['product_id', 'product_name']);
        return [
            $this->formatToLabel($channel_stat['upstream']),
            json_encode($list_products, JSON_UNESCAPED_UNICODE)
        ];
    }

    /**
     * 为统计详情生成前置的条件
     */
    public function shortcutDetails()
    {
        list($begin, $end, $field, $upstream_choose) = [
            I('get.begin') ? I('get.begin') : date('Y-m-d'),
            I('get.end') ? I('get.end') : date('Y-m-d'),
            trim(I('get.field')),
            trim(I('get.upstream')),
        ];

        list($interface_statistic_field, $upstream, $interface_item) = $this->_getUpstreamInfoForShortcuts();
        $backend_api_sorts = C('LIST_API_URL')['backend_api_sorts'];
        $list_api          = C('LIST_API_URL')['back_api_upstream_shortcuts_details'];
        $download_api      = C('LIST_API_URL')['back_api_upstream_shortcuts_details_excel'];

        return compact('begin', 'end', 'backend_api_sorts', 'interface_item', 'download_api', 'list_api', 'upstream',
            'field', 'upstream_choose');
    }

    /**
     * 为统计列表生成前置的条件
     * @return array
     */
    public function shortcuts()
    {
        $begin = $end = date('Y-m-d');
        list($interface_statistic_field, $upstream, $interface_item) = $this->_getUpstreamInfoForShortcuts();
        $back_api_upstream_shortcuts              = C('LIST_API_URL')['back_api_upstream_shortcuts'];
        $backend_api_sorts                        = C('LIST_API_URL')['backend_api_sorts'];
        $back_api_upstream_shortcuts_download     = C('LIST_API_URL')['back_api_upstream_shortcuts_download'];
        $back_api_upstream_shortcuts_day_download = C('LIST_API_URL')['back_api_upstream_shortcuts_day_download'];

        return compact('begin', 'end', 'interface_statistic_field', 'backend_api_sorts',
            'back_api_upstream_shortcuts_download', 'upstream', 'interface_item', 'back_api_upstream_shortcuts',
            'back_api_upstream_shortcuts_day_download');
    }

    /**
     * 获取配置信息
     * @return array
     */
    private function _getUpstreamInfoForShortcuts()
    {
        $product_id   = $this->product_id_shortcuts;
        $product      = ProductModel::getOneItem(compact('product_id'), 'channel_stat');
        $channel_stat = json_decode($product['channel_stat'], true);

        // 调整字段
        $interface_item = [];
        array_walk($channel_stat['interface_item'], function ($item, $key) use (&$interface_item) {
            $interface_item[$key] = $this->formatField($item);
        });

        return [
            $this->formatToLabel($channel_stat['interface_statistic_field']),
            $this->formatToLabel($channel_stat['upstream']),
            $this->formatToLabel($interface_item),
        ];
    }

    /**
     * 格式化字段
     *
     * @param array $data
     *
     * @return array
     */
    private function formatField(array $data)
    {
        $list_container = [];
        array_walk($data, function ($label, $value) use (&$list_container) {
            array_push($list_container, compact('label', 'value'));
        });
        return $list_container;
    }

    /**
     * 为了select2格式化元素
     *
     * @param array $items
     *
     * @return array
     */
    private function formatToLabel(array $items)
    {
        $list_container = [];
        array_walk($items, function ($item, $key) use (&$list_container) {
            list($label, $value) = [$item, $key];
            array_unshift($list_container, compact('label', 'value'));
        });

        return json_encode($list_container, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 渠道列表
     *
     * @access protected
     *
     * @return array
     **/
    public function get_upstream_list()
    {
        $product_id = I('get.product_id', '');

        if (empty($product_id)) {
            throw new \Exception('不存在此产品的数据源配置');
        }

        $data = $this->getUpstreamDriver($product_id)->getUpstreamList();
    }

    /**
     * 获取数据源驱动
     *
     * @access protected
     *
     * @return UpstreamDriver
     **/
    protected function getUpstreamDriver($product_id)
    {
        switch ($product_id) {
            case 200:
                return new Upstream200();
                break;
            default:


                break;
        }
    }

    /**
     * 获取渠道列表
     */
    public function getUpstreamList()
    {
        //获取get参数
        $input= [];
        $input['product_id']   = I('get.product_id', '', 'trim');
        $input['search_name'] = I('get.search_name', '', 'trim');
        $input['search_product_name'] = I('get.search_product_name', '', 'trim');
        $input['search_status'] = I('get.search_status', '', 'trim');

        //获取产品信息
        $product_where = [];
        if($input['product_id'] == 200){
            $product_where['father_id'] = $input['product_id'];
        }else{
            $product_where['product_id'] = $input['product_id'];
        }
        $product_model = new ProductModel();
        $product_info = $product_model->where($product_where)->field(['product_id', 'product_name'])->select();

        //获取查询条件
        $sub_id_arr = [];
        $product_name_arr = [];
        foreach($product_info as $key=>$value){
            $sub_id_arr[] = $value['product_id'];
            $product_name_arr[$value['product_id']] = $value['product_name'];
        }

        $channel_where = $where = [];
        if($input['product_id'] == 200){
            $channel_where['product_id'] = $where['product_id'] = ['IN',$sub_id_arr];
            if(!empty($input['search_status']) && $input['search_status'] == 2){
                $where['yd_status'] = 0;
                $where['lt_status'] = 0;
                $where['dx_status'] = 0;
            }else if(!empty($input['search_status']) && $input['search_status'] == 1){
                $where['_complex'] =[
                    ['yd_status'=>1],
                    ['lt_status'=>1],
                    ['dx_status'=>1],
                    '_logic' => 'or'
                ];
            }
        }else{
            $channel_where['product_id'] = $where['product_id'] = $input['product_id'];
            if(!empty($input['search_status'])){
                $where['status'] = $input['search_status'] == 1 ? 1 : 0;
            }
        }
        if (!empty($input['search_name'])) {
            $where['name'] = $input['search_name'];
        }
        if(!empty($input['search_product_name'])){
            $where['product_id'] = $input['search_product_name'];
        }

        //获取符合当前条件的数据数量
        $count = $this->count($where);
        //分页
        $listRow = 30;
        $page    = new Page($count, $listRow);

        $channelModel = new UpstreamChannelModel();
        $channelCloneModel = clone $channelModel;
        $list_data = $channelModel->where($where)
            ->order('id desc')
            ->limit($page->firstRow, $page->listRows)
            ->select();
        foreach($list_data as $key=>&$val){
            $val['product_name'] = isset($product_name_arr[$val['product_id']]) ? $product_name_arr[$val['product_id']] : '';
        }
        $pagenation      = $page->show();

        //获取渠道名称
        $channel_list = $channelCloneModel->where($channel_where)->field(['id', 'name'])->select();
        $arr = compact('list_data', 'pagenation', 'input', 'product_name_arr', 'channel_list');
        return $arr;

    }

    protected function getParamByGet()
    {
        $params = [];
        $params['product_id']   = I('get.product_id', '', 'trim');
        $params['search_name'] = I('get.search_name', '', 'trim');
        return $params;
    }

    protected function getWhereByParam($params)
    {
        if($params['product_id'] == 200){
            $where = ['father_id'=>$params['product_id']];
        }else{
            $where = ['product_id'=>$params['product_id']];
        }

        if (!empty($params['search_name'])) {
            $where['name'] = $params['search_name'];
        }

        return $where;
    }
    public function count($where = [])
    {
        $field = 'count(*) as c';
        $count = $this->data($where, $field);
        return $count[0]['c'];
    }
    public function data($where = [], $field = '*', $order = null, $page = null)
    {
        $model = new UpstreamChannelModel();
        $model = $model->field($field)->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        if (!is_null($page)) {
            $model = $model->limit($page->firstRow, $page->listRows);
        }

        return $model->select();
    }
    /**
     * 获取编辑信息
     */
    public function getUpstreamEdit()
    {
        $data = I('post.');
        $id = $data['id'];
        $model = new UpstreamChannelModel();
        $fields = ['id', 'product_id', 'yd_status', 'lt_status', 'dx_status', 'status', 'type'];
        $info = $model->field($fields)->where(compact('id'))->find();
        return json_encode($info, JSON_UNESCAPED_UNICODE);
    }
    /**
     * 设置编辑信息
     */
    public function setUpstreamEdit()
    {
        $data = I('post.');
        $arr = ['create_time' => time()];
        $data['yd_status'] !=  NULL ? $arr['yd_status'] = $data['yd_status'] : '';
        $data['lt_status'] !=  NULL ? $arr['lt_status'] = $data['lt_status'] : '';
        $data['dx_status'] !=   NULL ? $arr['dx_status'] = $data['dx_status'] : '';
        $data['status'] !=  NULL ? $arr['status'] = $data['status'] : '';
        $data['type'] !=  NULL ? $arr['type'] = $data['type'] : '';
        $model = new UpstreamChannelModel();
        $res = $model->where(array('id'=>$data['id']))->save($arr);
        return $res;

    }

    /**
     * 计费配置
     */
    public function priceConfig($data = [])
    {
        $where = ['id'=>$data['id']];
        $where_price = ['upstream_channel_id'=>$data['id']];
        if(!empty($data['start_time'])){
            $where_price['start_date'][] = ['egt', $data['start_time']];
        }
        if(!empty($data['end_time'])){
            $where_price['start_date'][] = ['elt', $data['end_time']];
        }

        $model = new UpstreamChannelPriceModel();
        $info = $model->where($where_price)->order('id desc')->select();

        //获取渠道名称
        $upstream_model = new UpstreamChannelModel();
        $upstream_info = $upstream_model->where($where)->find();
        $arr = [];
        array_walk($info, function(&$val) use (&$arr, $upstream_info, $data){
            $val['channel_name'] = isset($upstream_info['name']) ? $upstream_info['name'] : '';
            $json = json_decode($val['price'], JSON_UNESCAPED_UNICODE);
            $val['peizhi']['all'] = isset($json['all']) ? $json['all'] : '无';
            $val['peizhi']['succ'] = isset($json['succ']) ? $json['succ'] : '无';
            $val['peizhi']['yd'] = isset($json['yd']) ? $json['yd'] : '无';
            $val['peizhi']['lt'] = isset($json['lt']) ? $json['lt'] : '无';
            $val['peizhi']['dx'] = isset($json['dx']) ? $json['dx'] : '无';
            $val['product_name'] = $data['product_name'];
            $arr[] = $val;
        });
        return ['arr'=>$arr, 'upstream_info'=>$upstream_info];

    }

    /**
     * 添加渠道
     */
    public function setPriceConfig($admin = '')
    {
        $data = I('post.');
        $id = $data['id'];
        $start_date = $data['start_date'];
        $is_billing = $data['is_billing'];

        $now_time = date('Y-m-d');
        $if_config = C('ALLOW_HISTORY_FEE_CONFIG');
        if(!$if_config){
            if($start_date < $now_time){
                return ['status'=>'error', 'msg'=>'计费日期不能小于当前日期'];
            }
        }
        $where = ['upstream_channel_id'=>$id, 'start_date'=>$start_date];
        $model = new UpstreamChannelPriceModel();
        $find_one = $model->where($where)->find();
        if(!empty($find_one)){
            return ['status'=>'error', 'msg'=>'该渠道计费日期已配置'];
        }
        $price_res = [];
        $key_arr = [];
        array_walk($data['arr'], function($val) use (&$price_res, &$key_arr){
            $p_arr = explode('#', $val);
            $key_arr[] = $p_arr[0];
            $price_res[$p_arr[0]] = $p_arr[1];
        });
        if (count($key_arr) != count(array_unique($key_arr))){
            return ['status'=>'error', 'msg'=>'请检查配置项是否有重复字段'];
        }
        //获取该渠道信息
        $channel_model = new UpstreamChannelModel();
        $channel_info = $channel_model->where(compact('id'))->find();
        if(empty($channel_info)){
            return ['status'=>'error', 'msg'=>'渠道数据不存在'];
        }
        $res_data = [
            'upstream_channel_id' => $id,
            'product_id' => $channel_info['product_id'],
            'channel' => $channel_info['channel'],
            'price' => json_encode($price_res, JSON_UNESCAPED_UNICODE),
            'start_date' => $start_date,
            'admin' => $admin,
            'create_time' => time(),
            'is_billing' => $is_billing
        ];
        $model = new UpstreamChannelPriceModel();
        $r = $model -> add($res_data);
        if($r){
            return ['status'=>'ok', 'msg'=>'添加成功'];
        }
        return ['status'=>'error', 'msg'=>'添加失败'];
    }
    /**
     * 获取渠道价格配置信息
     */
    public function getPriceEdit()
    {
        $data = I('post.');
        $model = new UpstreamChannelPriceModel();
        $info = $model->where(['id'=>$data['id']])->find();
        $json_text = json_decode($info['price'], true);
        $out_count = count($json_text);
        $all_arr = ['all'=>'查询量', 'succ'=>'查得量', 'yd'=>'移动查得量', 'lt'=>'联通查得量', 'dx'=>'电信查得量'];
        $string = '';
        foreach($json_text as $k=>$v){
            $string .= '<div class="dialogue_form_child_html_edit" style="background-color: #FCFCFC;">';
            $str_intent = '<div class="form-group"><label for="tips" class="col-sm-2 control-label">选项</label><div>';
            $str_intent .= '<div class="col-sm-2 indent-html"><select class="intents_name" name="peizhi_edit[]" style="width: 120px;"><option value="">请选择</option>';
            $option_intent = '';
            foreach($all_arr as $kk=>$vv){
                $selected = '';
                if($k == $kk){
                $selected = 'selected';
                }
                $option_intent .= '<option '.$selected.' value="'.$kk.'">'.$vv.'</option>';
            }
            $str_intent .= $option_intent.'</select></div></div><label class="col-sm-2 control-label">价格</label><div><div class="col-sm-2 indent-html"><input type="text" name="price_edit[]" value="'.$v.'" /></div></div></div></div>';
            $string .= $str_intent;
        }
        return ['text'=>$string, 'info'=>$info];
    }
    /**
     * 编辑渠道价格配置
     */
    public function editPriceConfig($admin = '')
    {

        $data = I('post.');
        $id = $data['id'];
        $start_date = $data['start_date'];
        $is_billing = $data['is_billing'];
        $where = ['id'=>$id];
        $model = new UpstreamChannelPriceModel();
        $find_one = $model->where($where)->find();
        if(empty($find_one)){
            return ['status'=>'error', 'msg'=>'该配置项不存在'];
        }
        //如果日期不一样
        if($start_date != $find_one['start_date']){
            $if_config = C('ALLOW_HISTORY_FEE_CONFIG');
            if(!$if_config){
                if($start_date < $find_one['start_date']){
                    return ['status'=>'error', 'msg'=>'计费日期不能小于当前配置日期'];
                }
            }
            $model = new UpstreamChannelPriceModel();
            $find_one = $model->where(['upstream_channel_id'=>$find_one['upstream_channel_id'], 'start_date'=>$start_date])->find();
            if(!empty($find_one)){
                return ['status'=>'error', 'msg'=>'该渠道计费日期已配置'];
            }
        }

        $price_res = [];
        $key_arr = [];
        array_walk($data['arr'], function($val) use (&$price_res, &$key_arr){
            $p_arr = explode('#', $val);
            $key_arr[] = $p_arr[0];
            $price_res[$p_arr[0]] = $p_arr[1];
        });
        if (count($key_arr) != count(array_unique($key_arr))){
            return ['status'=>'error', 'msg'=>'请检查配置项是否有重复字段'];
        }

        $res_data = [
            'price' => json_encode($price_res, JSON_UNESCAPED_UNICODE),
            'start_date' => $start_date,
            'admin' => $admin,
            'update_time' => time(),
            'is_billing' => $is_billing
        ];
        $model = new UpstreamChannelPriceModel();
        $r = $model -> where(['id'=>$id])-> save($res_data);
        if($r){
            return ['status'=>'ok', 'msg'=>'编辑成功'];
        }
        return ['status'=>'error', 'msg'=>'编辑失败'];

    }
}
