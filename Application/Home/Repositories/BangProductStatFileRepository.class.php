<?php

namespace Home\Repositories;


class BangProductStatFileRepository extends BaseRepository
{
    protected $repository_stat;
    /*
    * 数值型字段
    * */
    protected $list_field_number = [
        'total_num', 'valid_name_or_address_num', 'valid_name_total',
        'valid_address_total', 'tel_right', 'name_right', 'tel_wrong', 'name_wrong',
        'valid_num', 'valid_address_num', 'name_dianhua_num', 'name_dianhua_no_phone_num',
        'name_tianyancha_num', 'name_yscredit_num', 'name_ysfuzzycredit_num', 'name_webengine_num'
    ];

    /*
     * 详情的title
     * */
    protected $list_title_detail = [
        '日期', '总查询量', '总查得量', '名称查得量', '地址查得量', '正确号码量', '正确名称量', '错误号码量', '错误名称量',
        '名称查询有效查询量', '地址查询有效查询量', '名称覆盖率', '地址覆盖率', '名称&地址覆盖率', '名称一致率', '名称模糊一致率',
        '名称不一致率', '名称无法匹配率', '地址一致率', '地址模糊一致率', '地址不一致率', '地址无法匹配率', '名称比对羽乐科技查得量',
        '名称比对羽乐科技查得占比', '名称比对企查查查得量', '名称比对企查查查得占比', '名称比对天眼查查得量', '名称比对天眼查查得占比',
        '名称比对有数金服工商接口查得量', '名称比对有数金服工商接口查得占比', '名称比对有数金服模糊查询查得量',
        '名称比对有数金服模糊查询查得占比', '名称比对搜索引擎查得量', '名称比对搜索引擎查得占比'
    ];

    /*
    * 列表的title
    * */
    protected $list_title = [
        '账号ID', '账号名称', '客户名称', '总查询量', '总查得量', '名称查得量', '地址查得量', '正确号码量', '正确名称量', '错误号码量', '错误名称量',
        '名称查询有效查询量', '地址查询有效查询量', '名称覆盖率', '地址覆盖率', '名称&地址覆盖率', '名称一致率', '名称模糊一致率',
        '名称不一致率', '名称无法匹配率', '地址一致率', '地址模糊一致率', '地址不一致率', '地址无法匹配率', '名称比对羽乐科技查得量',
        '名称比对羽乐科技查得占比', '名称比对企查查查得量', '名称比对企查查查得占比', '名称比对天眼查查得量', '名称比对天眼查查得占比',
        '名称比对有数金服工商接口查得量', '名称比对有数金服工商接口查得占比', '名称比对有数金服模糊查询查得量',
        '名称比对有数金服模糊查询查得占比', '名称比对搜索引擎查得量', '名称比对搜索引擎查得占比'
    ];

    /*
    * 列表的title
    * */
    protected $list_title_day = [
        '日期', '账号ID', '账号名称', '客户名称', '总查询量', '总查得量', '名称查得量', '地址查得量', '正确号码量', '正确名称量', '错误号码量', '错误名称量',
        '名称查询有效查询量', '地址查询有效查询量', '名称覆盖率', '地址覆盖率', '名称&地址覆盖率', '名称一致率', '名称模糊一致率',
        '名称不一致率', '名称无法匹配率', '地址一致率', '地址模糊一致率', '地址不一致率', '地址无法匹配率', '名称比对羽乐科技查得量',
        '名称比对羽乐科技查得占比', '名称比对企查查查得量', '名称比对企查查查得占比', '名称比对天眼查查得量', '名称比对天眼查查得占比',
        '名称比对有数金服工商接口查得量', '名称比对有数金服工商接口查得占比', '名称比对有数金服模糊查询查得量',
        '名称比对有数金服模糊查询查得占比', '名称比对搜索引擎查得量', '名称比对搜索引擎查得占比'
    ];

    /*
     * 按天导出文件的title
     * */
    protected $list_map_day = [
        '日期' => 'day_time',
        '账号ID' => 'id',
        '账号名称' => 'name',
        '客户名称' => 'name_account',
        '总查询量' => 'total_num',
        '总查得量' => 'valid_name_or_address_num',
        '名称查得量' => 'valid_name_total',
        '地址查得量' => 'valid_address_total',
        '正确号码量' => 'tel_right',
        '正确名称量' => 'name_right',
        '错误号码量' => 'tel_wrong',
        '错误名称量' => 'name_wrong',
        '名称查询有效查询量' => 'valid_num',
        '地址查询有效查询量' => 'valid_address_num',
        '名称覆盖率' => 'name_cover_chance',
        '地址覆盖率' => 'address_cover_chance',
        '名称&地址覆盖率' => 'name_address_cover_chance',
        '名称一致率' => 'name_is_same_chance',
        '名称模糊一致率' => 'name_blur_num_chance',
        '名称不一致率' => 'name_not_same_chance',
        '名称无法匹配率' => 'name_not_right_chance',
        '地址一致率' => 'address_is_same_chance',
        '地址模糊一致率' => 'address_blur_num_chance',
        '地址不一致率' => 'address_not_same_chance',
        '地址无法匹配率' => 'address_not_right_chance',
        '名称比对羽乐科技查得量' => 'name_dianhua_num',
        '名称比对羽乐科技查得占比' => 'name_dianhua_num_chance',
        '名称比对企查查查得量' => 'name_dianhua_no_phone_num',
        '名称比对企查查查得占比' => 'name_dianhua_no_phone_num_chance',
        '名称比对天眼查查得量' => 'name_tianyancha_num',
        '名称比对天眼查查得占比' => 'name_tianyancha_num_chance',
        '名称比对有数金服工商接口查得量' => 'name_yscredit_num',
        '名称比对有数金服工商接口查得占比' => 'name_yscredit_num_chance',
        '名称比对有数金服模糊查询查得量' => 'name_ysfuzzycredit_num',
        '名称比对有数金服模糊查询查得占比' => 'name_ysfuzzycredit_num_chance',
        '名称比对搜索引擎查得量' => 'name_webengine_num',
        '名称比对搜索引擎查得占比' => 'name_webengine_num_chance',
    ];

    /*
    *  列表title和字段之间的关系，不这样的一定会脑袋疼
    * */
    protected $list_map = [
        '账号ID' => 'id',
        '账号名称' => 'name',
        '客户名称' => 'name_account',
        '总查询量' => 'total_num',
        '总查得量' => 'valid_name_or_address_num',
        '名称查得量' => 'valid_name_total',
        '地址查得量' => 'valid_address_total',
        '正确号码量' => 'tel_right',
        '正确名称量' => 'name_right',
        '错误号码量' => 'tel_wrong',
        '错误名称量' => 'name_wrong',
        '名称查询有效查询量' => 'valid_num',
        '地址查询有效查询量' => 'valid_address_num',
        '名称覆盖率' => 'name_cover_chance',
        '地址覆盖率' => 'address_cover_chance',
        '名称&地址覆盖率' => 'name_address_cover_chance',
        '名称一致率' => 'name_is_same_chance',
        '名称模糊一致率' => 'name_blur_num_chance',
        '名称不一致率' => 'name_not_same_chance',
        '名称无法匹配率' => 'name_not_right_chance',
        '地址一致率' => 'address_is_same_chance',
        '地址模糊一致率' => 'address_blur_num_chance',
        '地址不一致率' => 'address_not_same_chance',
        '地址无法匹配率' => 'address_not_right_chance',
        '名称比对羽乐科技查得量' => 'name_dianhua_num',
        '名称比对羽乐科技查得占比' => 'name_dianhua_num_chance',
        '名称比对企查查查得量' => 'name_dianhua_no_phone_num',
        '名称比对企查查查得占比' => 'name_dianhua_no_phone_num_chance',
        '名称比对天眼查查得量' => 'name_tianyancha_num',
        '名称比对天眼查查得占比' => 'name_tianyancha_num_chance',
        '名称比对有数金服工商接口查得量' => 'name_yscredit_num',
        '名称比对有数金服工商接口查得占比' => 'name_yscredit_num_chance',
        '名称比对有数金服模糊查询查得量' => 'name_ysfuzzycredit_num',
        '名称比对有数金服模糊查询查得占比' => 'name_ysfuzzycredit_num_chance',
        '名称比对搜索引擎查得量' => 'name_webengine_num',
        '名称比对搜索引擎查得占比' => 'name_webengine_num_chance',
    ];

    /*
     * 详情title和字段之间的关系，不这样的一定会脑袋疼
     * */
    protected $list_map_detail = [
        '日期' => 'day_time',
        '总查询量' => 'total_num',
        '总查得量' => 'valid_name_or_address_num',
        '名称查得量' => 'valid_name_total',
        '地址查得量' => 'valid_address_total',
        '正确号码量' => 'tel_right',
        '正确名称量' => 'name_right',
        '错误号码量' => 'tel_wrong',
        '错误名称量' => 'name_wrong',
        '名称查询有效查询量' => 'valid_num',
        '地址查询有效查询量' => 'valid_address_num',
        '名称覆盖率' => 'name_cover_chance',
        '地址覆盖率' => 'address_cover_chance',
        '名称&地址覆盖率' => 'name_address_cover_chance',
        '名称一致率' => 'name_is_same_chance',
        '名称模糊一致率' => 'name_blur_num_chance',
        '名称不一致率' => 'name_not_same_chance',
        '名称无法匹配率' => 'name_not_right_chance',
        '地址一致率' => 'address_is_same_chance',
        '地址模糊一致率' => 'address_blur_num_chance',
        '地址不一致率' => 'address_not_same_chance',
        '地址无法匹配率' => 'address_not_right_chance',
        '名称比对羽乐科技查得量' => 'name_dianhua_num',
        '名称比对羽乐科技查得占比' => 'name_dianhua_num_chance',
        '名称比对企查查查得量' => 'name_dianhua_no_phone_num',
        '名称比对企查查查得占比' => 'name_dianhua_no_phone_num_chance',
        '名称比对天眼查查得量' => 'name_tianyancha_num',
        '名称比对天眼查查得占比' => 'name_tianyancha_num_chance',
        '名称比对有数金服工商接口查得量' => 'name_yscredit_num',
        '名称比对有数金服工商接口查得占比' => 'name_yscredit_num_chance',
        '名称比对有数金服模糊查询查得量' => 'name_ysfuzzycredit_num',
        '名称比对有数金服模糊查询查得占比' => 'name_ysfuzzycredit_num_chance',
        '名称比对搜索引擎查得量' => 'name_webengine_num',
        '名称比对搜索引擎查得占比' => 'name_webengine_num_chance',
    ];

    /*
     * 统计详情cell列表 34项
     *
     * */
    protected $list_cell_detail;

    /*
     * 列表cell 36项
     * */
    protected $list_cell;

    /*
     * 按天导出的cell 37项
     * */
    protected $list_cell_day;

    public function __construct()
    {
        $this->repository_stat = new BangProductStatRepository();

        // 初始化cell, cell太长了 所以封装个函数
        $this->list_cell_detail = iterator_to_array($this->excelColumnRange('A', 'AH'));
        $this->list_cell = iterator_to_array($this->excelColumnRange('A', 'AJ'));
        $this->list_cell_day = iterator_to_array($this->excelColumnRange('A', 'AK'));
    }

    /**
     * 邦企查导出统计文件
     * @throws \Exception
     */
    public function downloadFile()
    {
        $request_body = $this->genParamsForPost();

        // 分流
        switch ($request_body['type']) {
            case 'detail':
                $this->downloadDetail();
                break;
            case 'list':
                $this->downloadList();
                break;
            case 'day':
                $this->downloadDay();
                break;
        }
    }

    /**
     * 导出按天列表文件
     * @throws \Exception
     */
    protected function downloadDay()
    {
        // 列表数据
        $list_data = $this->repository_stat->getListDay();

        // 数据反序列化
        $list_data = $this->unFormatData($list_data);

        // 生成文件
        $this->genFileForDay($list_data);

        // file_download return
        $file_name = $this->genFileNameForDay();
        $this->fileDownload($file_name);
    }

    /**
     * 反格式化(将数值型的数据','去掉)
     * @param array $list_data
     * @return array
     */
    protected function unFormatData($list_data)
    {
        return array_map(function($item_data){
            $item = [];
            array_walk($item_data, function($value, $field) use (&$item){
                // 如果是数值型的参数， 则将逗号去掉
                $item[$field] = in_array($field, $this->list_field_number) ? str_replace(',', '', $value) : $value;
            });
            return $item;
        }, $list_data);

    }


    /**
     * @param array $list_data 统计数据
     * @return string
     */
    protected function genFileForDay($list_data)
    {
        // 为按天导出生成title
        $this->writeTitleForCsv();

        // 填充内容
        $this->writeContentForCsv($list_data);
    }

    /**
     * 为按天导出生成实体内容
     * @param array $list_data
     */
    protected function writeContentForCsv($list_data)
    {
        $file_name = $this->genFileNameForDay();
        array_walk($list_data, function($item) use($file_name){
            // 每行的内容
            $content_item = $this->genContentForCsv($item);
            file_put_contents($file_name, PHP_EOL . $content_item, FILE_APPEND);
        });
    }

    /**
     * 生成每行的内容
     * @param array $item
     * @return string
     */
    protected function genContentForCsv($item)
    {
        $content_item = '';
        array_walk($this->list_title_day, function($title) use (&$content_item, $item){
            $column = $this->list_map_day[$title];
            $delimiter = $content_item !== '' ? ',' : '';
            $content_item .= $delimiter . $item[$column];
        });
        return $content_item;
    }

    /**
     * 为按天导出生成Title
     */
    protected function writeTitleForCsv()
    {
        $file_name_day = $this->genFileNameForDay();
        $content_title = implode(',', $this->list_title_day);
        file_put_contents($file_name_day, $content_title);
    }

    /**
     * 生成文件
     * @param \PHPExcel $excel_php
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genExcelFileForDay($excel_php)
    {
        // 文件名
        $name_report_file = $this->genFileNameForDay();

        // 生成文件
        $this->genExcelFile($excel_php, $name_report_file);
    }

    /**
     * 生成文件
     * @param $excel_php
     * @param string $file_name
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genExcelFile($excel_php, $file_name)
    {
        $objWriter = \PHPExcel_IOFactory::createWriter($excel_php, 'Excel2007');
        ob_clean();
        $objWriter->save($file_name);
    }

    /**
     * 生成详情的文件名
     * @return string
     */
    protected function genFileNameForDay()
    {
        // 路径
        $path_name = RUNTIME_PATH;
        $file_prefix = '邦企查按天导出列表文档-';

        // 文件名
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = $file_prefix . $begin . '-' . $end . '.csv';;
        return $path_name . $file_name;
    }

    /**
     * 为文件设置sheet
     * @param $excel_php
     * @return mixed
     */
    protected function setSheetForDay($excel_php)
    {
        // Rename worksheet
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $name_sheet = '邦企查统计按天导出列表文档_' . $begin . '-' . $end;
        $excel_php->getActiveSheet()->setTitle($name_sheet);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $excel_php->setActiveSheetIndex(0);
        return $excel_php;
    }

    /**
     * 将数据写入excel
     * @param \PHPExcel $excel_php
     * @param array $list_info 统计信息
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     * @throws \Exception
     */
    protected function writeDataIntoExcelDay($excel_php, $list_info)
    {
        // 写入title
        $excel_php = $this->writeTitleForDay($excel_php);

        // 写入统计数据
        return $this->writeProductInfoForDay($list_info, $excel_php);
    }


    /**
     * 为统计详情写入每天的信息
     * @param array $list_info 统计信息
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \Exception
     */
    protected function writeProductInfoForDay($list_info, $excel_php)
    {
        // 开始写的行
        $row_begin = 2;
        array_walk($list_info, function ($item_info) use (&$row_begin, &$excel_php) {
            $excel_php = $this->writeSingleProductIntoDay($item_info, $row_begin, $excel_php);

            // 本轮单元结束，进行下一个产品
            $row_begin++;
        });

        // 全局居中
        $excel_php->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $excel_php->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        return $excel_php;
    }

    /**
     * 将单个产品的信息 写入excel
     * @param array $list_item 统计单元
     * @param integer $row 插入第几行
     * @param $excel_php
     * @return mixed
     */
    protected function writeSingleProductIntoDay($list_item, $row, $excel_php)
    {
        //  拼装这种形式 $excel_php->setActiveSheetIndex(0)->setCellValue('A' . $row, $list_item['date_key']);
        array_walk($this->list_cell_day, function ($item_cell, $item_index) use (&$excel_php, $list_item, $row) {
            $cell = $item_cell . $row;
            $title = $this->list_title_day[$item_index];
            $key = $this->list_map_day[$title];
            $value = $list_item[$key];

            $excel_php->setActiveSheetIndex(0)->setCellValue($cell, $value);
        });

        return $excel_php;
    }

    /**
     * 为详情页写入标题
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     */
    protected function writeTitleForDay($excel_php)
    {

        array_walk($this->list_cell_day, function ($item_cell, $item_index) use (&$excel_php) {
            $value = $this->list_title_day[$item_index];
            $excel_php->setActiveSheetIndex(0)
                ->setCellValue($item_cell . 1, $value);

            // 加粗 居中
            $excel_php->setActiveSheetIndex(0)->getStyle($item_cell . 1)->applyFromArray(
                [
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER]
                ]
            );

            // 自动调整行宽
            $excel_php->setActiveSheetIndex(0)
                ->getColumnDimension($item_cell)
                ->setAutoSize(true);
        });

        // 这个是执行自动宽度的地方， 因为计算出来的一列的最大长度总是小了一些 所以代码中手动加上10
        $excel_php->setActiveSheetIndex(0)->calculateColumnWidths();
        return $excel_php;
    }

    /**
     * 为excel设置属性
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     */
    protected function setPropertyForExcelDay($excel_php)
    {
        $excel_php->getProperties()->setCreator("后台开发人员")
            ->setLastModifiedBy("后台开发人员")
            ->setTitle("邦企查按天导出列表文档")
            ->setSubject("邦企查统计按天导出列表文档")
            ->setDescription("调试的朋友你知道吗? 这个类是已经被废弃了的")
            ->setKeywords("office 2007 openxml php")
            ->setCategory("统计详情");
        return $excel_php;
    }

    /**
     * 为统计列表导出文件
     * @throws \Exception
     */
    protected function downloadList()
    {
        // 列表数据
        $list_data = $this->repository_stat->getStatData();

        // 数据反格式化
        $list_data = $this->unFormatData($list_data['list_stat_tidy']);

        // 生成文件
        $this->genFileForList($list_data);

        // file_download return
        $file_name = $this->genFileNameForList();

        $this->fileDownload($file_name);
    }

    /**
     * @param array $list_info 详情信息
     * @return string
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genFileForList($list_info)
    {
        // 加载PHPEXCEL
        require_once LIB_PATH . '/Org/PHPExcel/PHPExcel.php';

        $excel_php = new \PHPExcel();

        // 设置基本的属性
        $excel_php = $this->setPropertyForExcelList($excel_php);

        // 将数据写 入excel
        $excel_php = $this->writeDataIntoExcelList($excel_php, $list_info);

        // 设置sheet
        $excel_php = $this->setSheetForList($excel_php);

        // 生成excel文件
        $this->genExcelFileForList($excel_php);
    }

    /**
     * 生成文件
     * @param \PHPExcel $excel_php
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genExcelFileForList($excel_php)
    {
        $name_report_file = $this->genFileNameForList();
        $this->genExcelFile($excel_php, $name_report_file);
    }

    /**
     * 生成详情的文件名
     * @return string
     */
    protected function genFileNameForList()
    {
        return $this->genFileName('邦企查列表文档-');
    }


    /**
     * 文件名字
     * @param string $file_prefix 前缀
     * @return string
     */
    protected function genFileName($file_prefix)
    {
        // 路径
        $path_name = RUNTIME_PATH;

        // 文件名
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $file_name = $file_prefix . $begin . '-' . $end . '.xlsx';;
        return $path_name . $file_name;
    }

    /**
     * 为文件设置sheet
     * @param $excel_php
     * @return mixed
     */
    protected function setSheetForList($excel_php)
    {
        // Rename worksheet
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $name_sheet = '邦企查统计列表文档_' . $begin . '-' . $end;
        $excel_php->getActiveSheet()->setTitle($name_sheet);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $excel_php->setActiveSheetIndex(0);
        return $excel_php;
    }

    /**
     * 将数据写入excel
     * @param \PHPExcel $excel_php
     * @param array $list_info 统计信息
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     * @throws \Exception
     */
    protected function writeDataIntoExcelList($excel_php, $list_info)
    {
        // 写入title
        $excel_php = $this->writeTitleForList($excel_php);

        // 写入统计数据
        return $this->writeProductInfoForList($list_info, $excel_php);
    }

    /**
     * 为统计详情写入每天的信息
     * @param array $list_info 统计信息
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \Exception
     */
    protected function writeProductInfoForList($list_info, $excel_php)
    {
        // 开始写的行
        $row_begin = 2;
        array_walk($list_info, function ($item_info) use (&$row_begin, &$excel_php) {
            $excel_php = $this->writeSingleProductIntoList($item_info, $row_begin, $excel_php);

            // 本轮单元结束，进行下一个产品
            $row_begin++;
        });

        // 全局居中
        $excel_php->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $excel_php->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        return $excel_php;
    }

    /**
     * 将单个产品的信息 写入excel
     * @param array $list_item 统计单元
     * @param integer $row 插入第几行
     * @param $excel_php
     * @return mixed
     */
    protected function writeSingleProductIntoList($list_item, $row, $excel_php)
    {
        //  拼装这种形式 $excel_php->setActiveSheetIndex(0)->setCellValue('A' . $row, $list_item['date_key']);
        array_walk($this->list_cell, function ($item_cell, $item_index) use (&$excel_php, $list_item, $row) {
            $cell = $item_cell . $row;
            $title = $this->list_title[$item_index];
            $key = $this->list_map[$title];
            $value = $list_item[$key];

            $excel_php->setActiveSheetIndex(0)->setCellValue($cell, $value);
        });

        return $excel_php;
    }

    /**
     * 为详情页写入标题
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     */
    protected function writeTitleForList($excel_php)
    {

        array_walk($this->list_cell, function ($item_cell, $item_index) use (&$excel_php) {
            $value = $this->list_title[$item_index];
            $excel_php->setActiveSheetIndex(0)
                ->setCellValue($item_cell . 1, $value);

            // 加粗 居中
            $excel_php->setActiveSheetIndex(0)->getStyle($item_cell . 1)->applyFromArray(
                [
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER]
                ]
            );

            // 自动调整行宽
            $excel_php->setActiveSheetIndex(0)
                ->getColumnDimension($item_cell)
                ->setAutoSize(true);
        });

        // 这个是执行自动宽度的地方， 因为计算出来的一列的最大长度总是小了一些 所以代码中手动加上10
        $excel_php->setActiveSheetIndex(0)->calculateColumnWidths();
        return $excel_php;
    }


    /**
     * 为excel设置属性
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     */
    protected function setPropertyForExcelList($excel_php)
    {
        $excel_php->getProperties()->setCreator("后台开发人员")
            ->setLastModifiedBy("后台开发人员")
            ->setTitle("邦企查统计列表文档")
            ->setSubject("邦企查统计列表文档")
            ->setDescription("调试的朋友你知道吗? 这个类是已经被废弃了的")
            ->setKeywords("office 2007 openxml php")
            ->setCategory("统计详情");
        return $excel_php;
    }

    /**
     * 为统计详情导出文件
     * @throws \Exception
     */
    protected function downloadDetail()
    {
        // 详情数据
        $list_data = $this->repository_stat->getDayInfo();

        $list_data = $this->unFormatData($list_data['list_day_data']);

        // 生成文件
        $this->genFileForDetail($list_data);

        // file_download return
        $file_name = $this->genFileNameForDetail();

        $this->fileDownload($file_name);
    }

    /**
     * @param array $list_info 详情信息
     * @return string
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genFileForDetail($list_info)
    {
        // 加载PHPEXCEL
        require_once LIB_PATH . '/Org/PHPExcel/PHPExcel.php';

        $excel_php = new \PHPExcel();

        // 设置基本的属性
        $excel_php = $this->setPropertyForExcelDetail($excel_php);

        // 将数据写 入excel
        $excel_php = $this->writeDataIntoExcelDetail($excel_php, $list_info);

        // 设置sheet
        $excel_php = $this->setSheetForReport($excel_php);

        // 生成excel文件
        $this->genExcelFileForDetail($excel_php);
    }

    /**
     * 为文件设置sheet
     * @param $excel_php
     * @return mixed
     */
    protected function setSheetForReport($excel_php)
    {
        // Rename worksheet
        $request_body = $this->genParamsForPost();
        $begin = date('Ymd', strtotime($request_body['begin']));
        $end = date('Ymd', strtotime($request_body['end']));
        $name_sheet = '邦企查统计详情文档_' . $begin . '-' . $end;
        $excel_php->getActiveSheet()->setTitle($name_sheet);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $excel_php->setActiveSheetIndex(0);
        return $excel_php;
    }

    /**
     * 生成文件
     * @param \PHPExcel $excel_php
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    protected function genExcelFileForDetail($excel_php)
    {
        // 文件名
        $name_report_file = $this->genFileNameForDetail();

        // 生成文件
        $this->genExcelFile($excel_php, $name_report_file);
    }

    /**
     * 生成详情的文件名
     * @return string
     */
    protected function genFileNameForDetail()
    {
        return $this->genFileName('邦企查详情文档-');
    }

    /**
     * 为excel设置属性
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     */
    protected function setPropertyForExcelDetail($excel_php)
    {
        $excel_php->getProperties()->setCreator("后台开发人员")
            ->setLastModifiedBy("后台开发人员")
            ->setTitle("邦企查统计详情文档")
            ->setSubject("邦企查统计详情文档")
            ->setDescription("调试的朋友你知道吗? 这个类是已经被废弃了的")
            ->setKeywords("office 2007 openxml php")
            ->setCategory("统计详情");
        return $excel_php;
    }

    /**
     * 将数据写入excel
     * @param \PHPExcel $excel_php
     * @param array $list_info 统计信息
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     * @throws \Exception
     */
    protected function writeDataIntoExcelDetail($excel_php, $list_info)
    {
        // 写入title
        $excel_php = $this->writeTitleForDetail($excel_php);

        // 写入统计数据
        return $this->writeProductInfoForDetail($list_info, $excel_php);
    }

    /**
     * 为统计详情写入每天的信息
     * @param array $list_info 统计信息
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \Exception
     */
    protected function writeProductInfoForDetail($list_info, $excel_php)
    {
        // 开始写的行
        $row_begin = 2;
        array_walk($list_info, function ($item_info) use (&$row_begin, &$excel_php) {
            $excel_php = $this->writeSingleProductIntoDetail($item_info, $row_begin, $excel_php);

            // 本轮单元结束，进行下一个产品
            $row_begin++;
        });

        // 全局居中
        $excel_php->getDefaultStyle()->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        $excel_php->getDefaultStyle()->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        return $excel_php;
    }

    /**
     * 将单个产品的信息 写入excel
     * @param array $list_item 统计单元
     * @param integer $row 插入第几行
     * @param $excel_php
     * @return mixed
     */
    protected function writeSingleProductIntoDetail($list_item, $row, $excel_php)
    {
        //  拼装这种形式 $excel_php->setActiveSheetIndex(0)->setCellValue('A' . $row, $list_item['date_key']);
        array_walk($this->list_cell_detail, function ($item_cell, $item_index) use (&$excel_php, $list_item, $row) {
            $cell = $item_cell . $row;
            $title = $this->list_title_detail[$item_index];
            $key = $this->list_map_detail[$title];
            $value = $list_item[$key];

            $excel_php->setActiveSheetIndex(0)->setCellValue($cell, $value);
        });

        return $excel_php;
    }

    /**
     * 为详情页写入标题
     * @param \PHPExcel $excel_php
     * @return \PHPExcel
     * @throws \PHPExcel_Exception
     */
    protected function writeTitleForDetail($excel_php)
    {

        array_walk($this->list_cell_detail, function ($item_cell, $item_index) use (&$excel_php) {
            $value = $this->list_title_detail[$item_index];
            $excel_php->setActiveSheetIndex(0)
                ->setCellValue($item_cell . 1, $value);

            // 加粗 居中
            $excel_php->setActiveSheetIndex(0)->getStyle($item_cell . 1)->applyFromArray(
                [
                    'font' => ['bold' => true],
                    'alignment' => ['horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER]
                ]
            );

            // 自动调整行宽
            $excel_php->setActiveSheetIndex(0)
                ->getColumnDimension($item_cell)
                ->setAutoSize(true);
        });

        // 这个是执行自动宽度的地方， 因为计算出来的一列的最大长度总是小了一些 所以代码中手动加上10
        $excel_php->setActiveSheetIndex(0)->calculateColumnWidths();
        return $excel_php;
    }

}