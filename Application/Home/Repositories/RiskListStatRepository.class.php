<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-03 14:29:32
 */
namespace Home\Repositories;

class RiskListStatRepository extends BaseRepository
{
    //命中种类
    protected $hitCountsType = [
        'phoneget_counts' => '本人手机命中',
        'idnumget_counts' => '本人身份证命中',
        'input_phoneget_counts' => '联系人手机命中',
        // 'devget_counts' => '本人设备号命中',//后期补充
        // 'input_idnumget_counts' => '联系人身份证命中',//后期补充
        // 'input_devget_counts' => '联系人设备号命中'//后期补充
    ];
    //重度程度
    protected $severity = ['可疑', '中度', '重度'];

    /**
     * 获取命中种类
     * @return list
     */
    public function getHitCountsType()
    {
        array_walk($this->hitCountsType, function(&$v, $k, $p) {
            foreach ($p as $key => $value) {
                $mm[$k.'.'.$key] = $v.$value;
            }
            $v = $mm;
        }, $this->severity);
        $list = array_reduce($this->hitCountsType, 'array_merge', array());
        return $list;
    }

    /**
     * 获取查询参数
     * @return array
     */
    public function getUserListParam()
    {
        $account_id = I('get.account_id', '', 'trim');
        $status = I('get.status', '', 'trim');
        $user_id = I('get.id', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');

        if ($status) {
            $where['status'] = new \MongoInt32($status);
        }
        if ($user_id) {
            $where['id'] = new \MongoInt32($user_id);
        }
        if ($account_id) {
            $ids = $this->getUserIdByAccountId($account_id);
            if ($user_id && !in_array($user_id, $ids)) {
                $where['id'] = 'what happened';
            } elseif (!$user_id) {
                $where['id'] = ['$in' => $ids];
            }
        }
        if ($contract_status) {
            $where['contract_status'] = new \MongoInt32($contract_status);
        }

        return $where;
    }

    /**
     * 根据客户ID获取账号ID
     * @param  number $account_id 客户ID
     * @return array
     */
    public function getUserIdByAccountId($account_id)
    {
        $type_id = 6;
        $account_info = D('FinanceAccountProduct')->where(compact('type_id', 'account_id'))
                                                  ->field('account_id,product_id')
                                                  ->index('product_id')
                                                  ->select();
        $ids = array_keys($account_info);
        return $ids;
    }

    /**
     * 时间条件
     * @return array
     */
    public function getRistListStatTime()
    {
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        if (!$begin && !$end) {
            $where['time'] = date('Ymd');
        } elseif ($begin && !$end) {
            $where['time'] = ['$gte' => date('Ymd', strtotime($begin))];
        } elseif ($end && !$begin) {
            $where['time'] = ['$lte' => date('Ymd', strtotime($end))];
        } else {
            $where['time'] = ['$gte' => date('Ymd', strtotime($begin)), '$lte' => date('Ymd', strtotime($end))];
        }
        return $where;
    }

    /**
     * 获取账号信息
     * @param  array $where 查新条件
     * @return list
     */
    public function getUserList($where)
    {
        $list = D('RiskListUser')->field(['_id' => 0, 'id', 'developer'])
                                 ->where($where)
                                 ->order('_id desc')
                                 ->select();
        $account_info = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($list, 'id'), 6);
        array_walk($list, function(&$v, $k, $p) {
            $v['account_id'] = isset($p[$v['id']]['account_id']) ? $p[$v['id']]['account_id'] : '';
            $v['account_name'] = isset($p[$v['id']]['account_name']) ? $p[$v['id']]['account_name'] : '';
        }, $account_info);
        return $list;
    }

    /**
     * 列表统计
     * @param array $where 查询条件
     * @param array $hit_type 命中种类
     * @return list
     */
    public function getRistListStatList($where,$hit_type)
    {
        $param = $this->getRistListStatTime();
        $user_list = $this->getUserList($where);
        if ($user_list) {
            $param['uid'] = ['$in' => array_column($user_list, 'id')];
        }
        $stat_list = $this->getRistListStatByParam($param, $hit_type);
        array_walk($user_list, function(&$v, $k, $p) {
            if (isset($p[$v['id']])) {
                $v = array_merge($v, $p[$v['id']]);
            }
        }, $stat_list['list']);
        return ['list' => $user_list, 'total_data' => $stat_list['total_data']];
    }

    /**
     * 获取统计数据
     * @param  array $param 查询条件
     * @return list
     */
    public function getRistListStatByParam($param, $hit_type)
    {
        $stat_list = D('RiskListStat')->field(['_id' => 0])
                                      ->where($param)
                                      ->select();
        $list = $total_data = [];
        foreach ($stat_list as $key => $value) {
            $list[$value['uid']]['all_counts'] += isset($value['all_counts']) ? $value['all_counts'] : 0;
            $list[$value['uid']]['success_counts'] += isset($value['success_counts']) ? $value['success_counts'] : 0;
            $list[$value['uid']]['get_counts'] += isset($value['get_counts']) ? $value['get_counts'] : 0;
            $is_get_counts = isset($value['is_get_counts']) ? $value['is_get_counts'] : [];
            $info = $this->getIsGetCounts($is_get_counts);
            $list[$value['uid']]['own_get_counts'] += $info['own_get_counts'];
            $list[$value['uid']]['input_get_counts'] += $info['input_get_counts'];
            foreach ($hit_type as $kk => $vv) {
                $hit = explode('.', $kk);
                $list[$value['uid']][$kk] += isset($value[$hit[0]][$hit[1]]) ? $value[$hit[0]][$hit[1]] : 0;
                $total_data[$kk] += isset($value[$hit[0]][$hit[1]]) ? $value[$hit[0]][$hit[1]] : 0;
            }
            $total_data['all_counts'] += isset($value['all_counts']) ? $value['all_counts'] : 0;
            $total_data['success_counts'] += isset($value['success_counts']) ? $value['success_counts'] : 0;
            $total_data['get_counts'] += isset($value['get_counts']) ? $value['get_counts'] : 0;
            $total_data['own_get_counts'] += $info['own_get_counts'];
            $total_data['input_get_counts'] += $info['input_get_counts'];
        }
        return ['list' => $list, 'total_data' => $total_data];
    }

    /**
     * 获取本人查得量和联系人查得量
     * @param  array $data 查得量
     * @return list
     */
    public function getIsGetCounts($data)
    {
        if (empty($data)) {
            return ['own_get_counts' => 0, 'input_get_counts' => 0];
        }
        $own_data = isset($data[0]) ? (int)$data[0] : 0;
        $input_data = isset($data[1]) ? (int)$data[1] : 0;
        $own_input = isset($data[2]) ? (int)$data[2] : 0;
        $own_get_counts = $own_data + $own_input;
        $input_get_counts = $input_data + $own_input;
        return compact('own_get_counts', 'input_get_counts');
    }

    /**
     * 获取列表数量
     * @param  array $where 查询参数
     * @return number
     */
    public function getUserListNum($where)
    {
        return D('RiskListUser')->where($where)->count();
    }

    /**
     * 获取时间列表
     * @param  number $begin 开始时间
     * @param  number $end   结束时间
     * @return list
     */
    public function getDateList()
    {
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end = empty($end) ? strtotime(date('Y-m-d 23:59:59')) : strtotime($end);
        $begin = empty($begin) ? ($end-86399) : strtotime($begin);

        $date_list = [];
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }
        return $date_list;
    }

    /**
     * 获取详情统计
     * @return list
     */
    public function getRiskListDetail($hit_type)
    {
        $where = $this->getRistListStatTime();
        $where['uid'] = (int)I('get.id', '', 'trim');
        $stat_list = $this->getRistListDetailByParam($where, $hit_type);
        $date_list = $this->getDateList();

        $list = array_merge($date_list, $stat_list['list']);

        return ['list' => $list, 'total_data' => $stat_list['total_data']];
    }

    /**
     * 详情统计数据
     * @param  array $where    查询条件
     * @param  array $hit_type 命中种类
     * @return list
     */
    public function getRistListDetailByParam($where, $hit_type)
    {
        $stat_list = D('RiskListStat')->field(['_id' => 0])
                                      ->where($where)
                                      ->select();
        $list = $total_data = [];
        foreach ($stat_list as $key => $value) {
            $date = date('Y-m-d', strtotime($value['time']));
            $list[$date]['all_counts'] += isset($value['all_counts']) ? $value['all_counts'] : 0;
            $list[$date]['success_counts'] += isset($value['success_counts']) ? $value['success_counts'] : 0;
            $list[$date]['get_counts'] += isset($value['get_counts']) ? $value['get_counts'] : 0;
            $is_get_counts = isset($value['is_get_counts']) ? $value['is_get_counts'] : [];
            $info = $this->getIsGetCounts($is_get_counts);
            $list[$date]['own_get_counts'] += $info['own_get_counts'];
            $list[$date]['input_get_counts'] += $info['input_get_counts'];
            foreach ($hit_type as $kk => $vv) {
                $hit = explode('.', $kk);
                $list[$date][$kk] += isset($value[$hit[0]][$hit[1]]) ? $value[$hit[0]][$hit[1]] : 0;
                $total_data[$kk] += isset($value[$hit[0]][$hit[1]]) ? $value[$hit[0]][$hit[1]] : 0;
            }
            $total_data['all_counts'] += isset($value['all_counts']) ? $value['all_counts'] : 0;
            $total_data['success_counts'] += isset($value['success_counts']) ? $value['success_counts'] : 0;
            $total_data['get_counts'] += isset($value['get_counts']) ? $value['get_counts'] : 0;
            $total_data['own_get_counts'] += $info['own_get_counts'];
            $total_data['input_get_counts'] += $info['input_get_counts'];
        }
        return ['list' => $list, 'total_data' => $total_data];
    }

    /**
     * 提供一个联动的用户筛选
     */
    public function clientList()
    {
        $contract_status = I('post.contract_status', '', 'trim');
        $status = I('post.status', '', 'trim');

        $where = [];
        if ($contract_status) {
            $where['contract_status'] = $contract_status;
        }
        if ($status) {
            $where['status'] = $status;
        }

        $user_list = D('Auth')->field('id,developer')
                              ->where($where)
                              ->index('id')
                              ->select();
        return $user_list;
    }
}