<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmMatchingFileRepository;
use Home\Repositories\BmMatchingStatRepository;

class BmMatchingStatController extends AdminController
{
    // stat repository
    private $repository_bm_matching_stat;

    // file repository
    protected $repository_matching_file;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_bm_matching_stat = new BmMatchingStatRepository();
        $this->repository_matching_file = new BmMatchingFileRepository();
    }

    /**
     * 统计排序
     */
    public function sortBy()
    {
        try {
            $list_stat = $this->repository_bm_matching_stat->sortBy();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 邦秒配导出（按天）
     */
    public function exportByDay()
    {
        try {
            $this->repository_matching_file->genFile();
        } catch (\Exception $e) {
            $status = 0;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 总计详情导出文件
     */
    public function downloadTotalShow()
    {
        try {
            // 导出文件
            $this->repository_bm_matching_stat->downloadTotalShow();

        } catch (\Exception $e) {
            $status = 4178;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 总计详情
     */
    public function totalShow()
    {
        if (IS_GET) {

            // 客户列表 && 产品列表 && 输入的参数
            $list_account = $this->repository_bm_matching_stat->getAccountList();
            $list_product = $this->repository_bm_matching_stat->getProductList();
            $request_params = I('get.');

            // 作为属性传递给组件
            $init_params = json_encode(compact('list_account', 'list_product', 'request_params'), JSON_UNESCAPED_UNICODE);

            $this->assign(compact('init_params'));
            $this->display('total_show');
            exit();
        }

        try {
            // 获取总计详情列表
            $list_info = $this->repository_bm_matching_stat->getTotalInfoByDay();
            $status = 0;

            $this->ajaxResponse(compact('list_info', 'status'));

        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();

            $this->ajaxResponse(compact('msg', 'status'));
        }
    }

    /**
     * 统计列表初始页面
     */
    public function index()
    {
        // 请求的初始参数
        $params_request = $this->repository_bm_matching_stat->paramsForList();

        // 客户列表
        $list_account = $this->repository_bm_matching_stat->getAccountList();

        // 产品列表
        $list_product = $this->repository_bm_matching_stat->getProductList();

        // 参数初始化
        $params_init = json_encode(compact('params_request', 'list_product', 'list_account'));

        $this->assign(compact('params_init'));
        $this->display();
    }

    /**
     * 获取list的统计信息
     * @return string
     */
    public function statList()
    {
        try {
            $list_stat_data = $this->repository_bm_matching_stat->getStatInfoForList();
            $status = 0;

            $this->ajaxResponse(compact('list_stat_data', 'status'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }


    public function detail()
    {
        if (IS_GET) {
            // 页面参数
            $params_request= $this->repository_bm_matching_stat->paramsForDetail();

            // 客户列表
            $list_account = $this->repository_bm_matching_stat->getAccountList();

            // 产品列表
            $list_product = $this->repository_bm_matching_stat->getProductList();

            // 参数初始化
            $params_init = json_encode(compact('params_request', 'list_product', 'list_account'));
            $this->assign(compact('params_init'));

            $this->display();
            exit();
        }

        try {
            // 获取数据
            $list_stat_data = $this->repository_bm_matching_stat->getStatDataForDetail();
            $status = 0;

            $this->ajaxResponse(compact('list_stat_data', 'status'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

}
