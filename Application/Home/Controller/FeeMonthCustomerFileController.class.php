<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\FeeMonthCustomerRepository;
use Home\Repositories\FeeMonthCustomerFileRepository;

class FeeMonthCustomerFileController extends AdminController
{
    protected $feeMonth;
    protected $feeFile;

    public function _initialize()
    {
        parent::_initialize();
        $this->feeMonth = new FeeMonthCustomerRepository();
        $this->feeFile = new FeeMonthCustomerFileRepository();
    }

    public function index()
    {
        $where = $this->feeMonth->getFeeMonthCustomerParam();
        $list = $this->feeMonth->getFeeMonthCustomerList($where, 0, 0);
        $this->feeFile->getFeeMonthCustomerDownload($list);
    }

    public function detail()
    {
        $where = $this->feeMonth->getFeeMonthCustomerParam();
        $list = $this->feeMonth->getFeeMonthProductList($where, 0, 0);
        $this->feeFile->getFeeMonthCustomerProductDownload($list);
    }

    public function feeMonth()
    {
        $where = $this->feeMonth->getFeeMonthCustomerParam();
        $list = $this->feeMonth->getFeeMonthProductDetail($where);
        $product_info = $this->feeMonth->getProductInfo();

        $this->feeFile->getFeeMonthProductDownload($list, $product_info);
    }

    public function feeDay()
    {
        $where = $this->feeMonth->getFeeDayCustomerParam();
        $product_info = $this->feeMonth->getProductInfo();
        $list = $this->feeMonth->getFeeDayProductDetail($where);

        $this->feeFile->getFeeDayProductDownload($list, $product_info);
    }
}