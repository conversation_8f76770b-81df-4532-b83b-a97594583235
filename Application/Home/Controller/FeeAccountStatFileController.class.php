<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\Model\FinanceAccountsModel;
use Common\Model\FinanceAccountProductModel;
use Common\Model\CuishouUserModel;
use Common\Model\FeeCrawlerConfigModel;
use Common\Model\FeeCuishouConfigModel;
use Common\Model\FeeMatchingConfigModel;
use Common\Model\FeeCrawlerStatModel;
use Common\Model\FeeCuishouStatModel;
use Common\Model\FeeMatchingStatModel;

class FeeAccountStatFileController extends AdminController
{
    protected $finace_account;
    protected $crawler_fee;
    protected $cuishou_fee;
    protected $matching_fee;

    public function __construct()
    {
        parent::__construct();
        $this->finace_account = new FinanceAccountsModel();
        $this->crawler_fee = new FeeCrawlerStatModel();
        $this->cuishou_fee = new FeeCuishouStatModel();
        $this->matching_fee = new FeeMatchingStatModel();
        set_time_limit(600);
    }

    public function index()
    {
        // 获取统计信息
        $params = I('get.');
        $account_map = $map = [];
        if(!empty($params['id'])){
            $account_map['id'] = $map['account_id'] = $params['id'];
        }
        if(!empty($params['name'])){
            $account_map['name'] = $map['account_name'] = $params['name'];
        }
        $account_info = $this->finace_account->where($account_map)->order('id DESC')->select();
        if($params['begin'] && $params['end']){
            $start_time = $params['begin'];
            $end_time = $params['end'];
        }else{
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $map['fee_date'] = [['egt', $start_time], ['elt', $end_time]];
        $fee_crawler = $this->crawler_fee
            ->where($map)
            ->index('account_id')
            ->sum('fee_price');
        $fee_cuishou = $this->cuishou_fee
            ->where($map)
            ->index('account_id')
            ->sum('fee_price');
        $fee_matching = $this->matching_fee
            ->where($map)
            ->index('account_id')
            ->sum('fee_price');
        $fee_bang = D('BangProductFeeStat')->where($map)->index('account_id')->sum('fee_price');
        $fee_risklist = D('RiskListFeeStat')->where($map)->index('account_id')->sum('fee_price');
        $list_stat['fee_total'] = round($fee_crawler+$fee_cuishou+$fee_matching+$fee_bang+$fee_risklist,2);
        foreach ($account_info as $key=>$value){
            $map['account_id'] = $value['id'];
            $crawler_stat = $this->crawler_fee
                ->where($map)
                ->index('account_id')
                ->sum('fee_price');
            $cuishou_stat = $this->cuishou_fee
                ->where($map)
                ->index('account_id')
                ->sum('fee_price');
            $matching_stat = $this->matching_fee
                ->where($map)
                ->index('account_id')
                ->sum('fee_price');
            $bang_stat = D('BangProductFeeStat')->where($map)->index('account_id')->sum('fee_price');
            $risklist_stat = D('RiskListFeeStat')->where($map)->index('account_id')->sum('fee_price');
            $info_stat[$key]['id'] = $value['id'];
            $info_stat[$key]['name'] = $value['name'];
            $info_stat[$key]['company'] = $value['company'];
            $info_stat[$key]['fee_stat'] = round($crawler_stat+$cuishou_stat+$matching_stat+$bang_stat+$risklist_stat,2);
        }
        $list_stat['data'] = $info_stat;
        //文件名
        $file_name = RUNTIME_PATH . 'Cache/account_fee_'.$params['begin'].'_'.$params['end'].'.csv';

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->genTempFileIndexForRequest($list_stat, $file_name);

        //为fileDownload插件生成文件
        $this->genFileForFileDownload($file_name);
        exit();
    }

    public function productList()
    {
        // init params
        $params = I('get.');

        if(!$params['id']){
            $this->__Return('缺少必要的参数', '', 'tip_error');
        }
        $account_info = (new FinanceAccountProductModel())->where(['account_id'=>$params['id']])->order('type_id, product_id')->select();
        if($params['begin'] && $params['end']){
            $start_time = $params['begin'];
            $end_time = $params['end'];
        }else{
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $map['fee_date'] = [['egt', $start_time], ['elt', $end_time]];
        $map['account_id'] = $params['id'];
        //客户产品总计
        $crawler_amount_stat = $this->crawler_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_amount');
        $crawler_fee_stat = $this->crawler_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_price');
        $matching_amount_stat = $this->matching_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_amount');
        $matching_fee_stat = $this->matching_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_price');
        $cuishou_amount_stat = $this->cuishou_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_amount');
        $cuishou_fee_stat = $this->cuishou_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_price');
        $bang_amount_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_amount');
        $bang_fee_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_price');
        $risklist_amount_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_amount');
        $risklist_fee_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_price');
        $list_stat['amount_total'] = intval($crawler_amount_stat)+intval($matching_amount_stat)+intval($cuishou_amount_stat)+intval($bang_amount_stat)+intval($risklist_amount_stat);
        $list_stat['fee_total'] = round($crawler_fee_stat+$matching_fee_stat+$cuishou_fee_stat+$bang_fee_stat+$risklist_fee_stat,2);
        $CuishouUser = new CuishouUserModel();
        if($account_info){
            foreach ($account_info as $key=>$value){
                $product_id = $value['product_id'];
                $map['product_id'] = $product_id;
                $info_stat[$key]['product_id'] = $product_id;
                $type_id = $value['type_id'];
                $info_stat[$key]['type_id'] = $type_id;
                switch ($type_id) {
                    case 1:
                        $info_stat[$key]['product_type'] = '邦秒爬';
                        // 条件
                        $product_info = D('Auth')
                            ->field('id,developer')
                            ->index('id')
                            ->find($product_id);
                        $info_stat[$key]['product_name'] = $product_info['developer'];
                        //产品账号配置
                        $config_product = (new FeeCrawlerConfigModel())
                            ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                            ->order('id DESC')
                            ->find();
                        if($config_product){
                            $fee_basis_list = $this->crawler_fee->getFeeBasis();
                            $fee_method_list = $this->crawler_fee->getFeeMethod();
                            $info_stat[$key]['fee_basis'] = $fee_basis_list[$config_product['fee_basis']];
                            $info_stat[$key]['fee_method'] = $fee_method_list[$config_product['fee_method']];
                        }else{
                            $info_stat[$key]['fee_basis'] = '未配置';
                            $info_stat[$key]['fee_method'] = '未配置';
                        }
                        //用量计费
                        $amount_stat = $this->crawler_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_amount');
                        $fee_stat = $this->crawler_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_price');
                        break;
                    case 2:
                        $info_stat[$key]['product_type'] = '邦秒配';
                        $product_info = D('AdminApikey')
                            ->index('id')
                            ->field('id,owner')
                            ->find($product_id);
                        $info_stat[$key]['product_name'] = $product_info['owner'];
                        //产品账号配置
                        $config_product = (new FeeMatchingConfigModel())
                            ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                            ->order('id DESC')
                            ->find();
                        if($config_product){
                            $fee_basis_list = $this->matching_fee->getFeeBasis();
                            $fee_method_list = $this->matching_fee->getFeeMethod();
                            $info_stat[$key]['fee_basis'] = $fee_basis_list[$config_product['fee_basis']];
                            $info_stat[$key]['fee_method'] = $fee_method_list[$config_product['fee_method']];
                        }else{
                            $info_stat[$key]['fee_basis'] = '未配置';
                            $info_stat[$key]['fee_method'] = '未配置';
                        }
                        //用量计费
                        $amount_stat = $this->matching_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_amount');
                        $fee_stat = $this->matching_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_price');
                        break;
                    case 3:
                        $info_stat[$key]['product_type'] = '催收分';
                        $product_info = $CuishouUser
                            ->where(['id' => intval($product_id)])
                            ->find();
                        $info_stat[$key]['product_name'] = $product_info['developer'];
                        //产品账号配置
                        $config_product = (new FeeCuishouConfigModel())
                            ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                            ->order('id DESC')
                            ->find();
                        if($config_product){
                            $fee_basis_list = $this->cuishou_fee->getFeeBasis();
                            $fee_method_list = $this->cuishou_fee->getFeeMethod();
                            $info_stat[$key]['fee_basis'] = $fee_basis_list[$config_product['fee_basis']];
                            $info_stat[$key]['fee_method'] = $fee_method_list[$config_product['fee_method']];
                        }else{
                            $info_stat[$key]['fee_basis'] = '未配置';
                            $info_stat[$key]['fee_method'] = '未配置';
                        }
                        //用量计费
                        $amount_stat = $this->cuishou_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_amount');
                        $fee_stat = $this->cuishou_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_price');
                        break;
                    case 4:
                        $info_stat[$key]['product_type'] = '邦企查';
                        $product_info = D('BangProducts')->where(['id' => $product_id])->field('name')->find();
                        $info_stat[$key]['product_name'] = $product_info['name'];
                        $config_product = D('BangProductFeeConfig')->where(['product_id' => $product_id, 'is_delete' => '0'])
                                                                   ->order('id desc')
                                                                   ->find();
                        $fee_basis_list = D('BangProductFeeConfig')->getFeeBasis();
                        $fee_method_list = D('BangProductFeeConfig')->getFeeMethod();
                        $info_stat[$key]['fee_basis'] = isset($config_product['fee_basis']) ? $fee_basis_list[$config_product['fee_basis']] : '未配置';
                        $info_stat[$key]['fee_method'] = isset($config_product['fee_method']) ? $fee_method_list[$config_product['fee_method']] : '未配置';
                        $amount_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_amount');
                        $fee_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_price');
                        break;
                    case 6:
                        $info_stat[$key]['product_type'] = '风险名单';
                        $product_info = D('RiskListUser')->field(['_id' => 0])->where(['id' => (int)$product_id])->find();
                        $info_stat[$key]['product_name'] = $product_info['developer'];
                        $config_product = D('RiskListFeeConfig')->where(['product_id' => $product_id, 'is_delete' => 0])
                                                                ->order('id desc')
                                                                ->find();
                        $fee_basis_list = D('RiskListFeeConfig')->getFeeBasis();
                        $fee_method_list = D('RiskListFeeConfig')->getFeeMethod();
                        $info_stat[$key]['fee_basis'] = !empty($config_product['fee_basis']) ? $fee_basis_list[$config_product['fee_basis']] : '未设置';
                        $info_stat[$key]['fee_method'] = !empty($config_product['fee_method']) ? $fee_method_list[$config_product['fee_method']] : '未设置';
                        $amount_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_amount');
                        $fee_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_price');
                        break;
                }
                $info_stat[$key]['amount_total'] = $amount_stat ? $amount_stat : 0;
                $info_stat[$key]['fee_total'] = round($fee_stat,2);
            }
        }
        $list_stat['data'] = $info_stat;
        //文件名
        $file_name = RUNTIME_PATH . 'Cache/product_fee_'.$params['begin'].'_'.$params['end'].'.csv';

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->genTempFileListForRequest($list_stat, $file_name);

        // 为fileDownload插件生成文件
        $this->genFileForFileDownload($file_name);
        exit();
    }

    public function productDetail()
    {
        // init params
        $params = I('get.');

        if(!$params['product_id'] || !$params['type_id']){
            $this->__Return('缺少必要的参数', '', 'tip_error');
        }
        $product_id = $params['product_id'];
        $info_stat['product_id'] = $product_id;
        $type_id = $params['type_id'];
        $info_stat['type_id'] = $type_id;

        $map = [];
        if($params['begin'] && $params['end']){
            $start_time = $params['begin'];
            $end_time = $params['end'];
        }else{
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $map['fee_date'] = [['egt', $start_time], ['elt', $end_time]];

        //产品账号配置

        $map['product_id'] = $params['product_id'];
        $info_stat['fee_method'] = 0;

        switch ($type_id) {
            case 1:
                $product_info = D('Auth')
                    ->field('id,developer')
                    ->index('id')
                    ->find($product_id);
                $info_stat['product_name'] = $product_info['developer'];
                //产品账号配置
                $config_product = (new FeeCrawlerConfigModel())
                    ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                    ->order('id DESC')
                    ->find();
                if($config_product){
                    $info_stat['fee_method'] = $config_product['fee_method'];
                }
                //用量计费
                $amount_stat = $this->crawler_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_amount');
                $fee_stat = $this->crawler_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_price');
                $info_stat['data'] = $this->crawler_fee
                    ->where($map)
                    ->field(['fee_method','fee_amount','fee_price','fee_date'])
                    ->order('fee_date desc')
                    ->select();
                break;
            case 2:
                $product_info = D('AdminApikey')
                    ->index('id')
                    ->field('id,owner')
                    ->find($product_id);
                $info_stat['product_name'] = $product_info['owner'];
                //产品账号配置
                $config_product = (new FeeMatchingConfigModel())
                    ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                    ->order('id DESC')
                    ->find();
                if($config_product){
                    $info_stat['fee_method'] = $config_product['fee_method'];
                }
                //用量计费
                $amount_stat = $this->matching_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_amount');
                $fee_stat = $this->matching_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_price');
                $info_stat['data'] = $this->matching_fee
                    ->where($map)
                    ->field(['fee_method','fee_amount','fee_price','fee_date'])
                    ->order('fee_date desc')
                    ->select();
                break;
            case 3:
                $CuishouUser = new CuishouUserModel();
                $product_info = $CuishouUser
                    ->where(['id' => intval($product_id)])
                    ->find();
                $info_stat['product_name'] = $product_info['developer'];
                //产品账号配置
                $config_product = (new FeeCuishouConfigModel())
                    ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                    ->order('id DESC')
                    ->find();
                if($config_product){
                    $info_stat['fee_method'] = $config_product['fee_method'];
                }
                //用量计费
                $amount_stat = $this->cuishou_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_amount');
                $fee_stat = $this->cuishou_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_price');
                $info_stat['data'] = $this->cuishou_fee
                    ->where($map)
                    ->field(['fee_method','fee_amount','fee_price','fee_date'])
                    ->order('fee_date desc')
                    ->select();
                break;
        }
        $info_stat['amount_total'] = $amount_stat ? $amount_stat : 0;
        $info_stat['fee_total'] = round($fee_stat,2);

        // 临时文件名
        $file_name = RUNTIME_PATH . 'Cache/detail_fee_'.$params['begin'].'_'.$params['end'].'.csv';

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->genTempFileDetailForRequest($info_stat, $file_name);

        // 为fileDownload插件生成文件
        $this->genFileForFileDownload($file_name);
        exit();
    }

    /**
     * (客户列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileIndexForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '客户ID,客户名称,公司名称,费用（元）';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);

        $show_str = '' . ',,总计,' . $stat_list['fee_total'];
        $show_str = mb_convert_encoding($show_str,'gb2312','utf-8');
        file_put_contents($file_name, PHP_EOL . $show_str, FILE_APPEND);
        foreach ($stat_list['data'] as $stat_data) {
            // 数据补全
            $file_str = $stat_data['id'] . ',' . str_replace(',', '，', $stat_data['name']) .',' . str_replace(',', '，', $stat_data['company']) . ',' . $stat_data['fee_stat'];
            $file_str = mb_convert_encoding($file_str,'gb2312','utf-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * (产品列表)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileListForRequest($stat_list, $file_name)
    {
        // gen file
        $title_list = '产品账号ID,产品账号名称,账号类型,计费依据,计费方式,计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);
        $show_str = ',总计' . ',,,,' . $stat_list['amount_total'] . ',' . $stat_list['fee_total'];
        $show_str = mb_convert_encoding($show_str,'gb2312','utf-8');
        file_put_contents($file_name, PHP_EOL . $show_str, FILE_APPEND);
        foreach ($stat_list['data'] as $stat_data) {
            if($stat_data['fee_method']=='按用量'){$amount_total = $stat_data['amount_total'];}else{$amount_total = 'NA';}
            // 数据补全
            $file_str = $stat_data['product_id'] . ',' . str_replace(',', '，', $stat_data['product_name']) . ',' . $stat_data['product_type'].','
                . $stat_data['fee_basis'] . ',' . $stat_data['fee_method'] . ',' .
                $amount_total . ',' . $stat_data['fee_total'];
            $file_str = mb_convert_encoding($file_str,'gb2312','utf-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
    }

    /**
     * (产品详情)生成临时文件,为fileDownLoad插件铺垫
     * @param $stat_list
     * @param $file_name
     */
    public function genTempFileDetailForRequest($stat_list, $file_name)
    {
        $title_list = '日期,	计费用量,费用（元）';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);
        if($stat_list['fee_method']==2){
            $fee_amount_total = $stat_list['amount_total'];
        }else{
            $fee_amount_total = 'NA';
        }
        $show_str = '总计' . ',' . $fee_amount_total . ',' . $stat_list['fee_total'];
        $show_str = mb_convert_encoding($show_str,'gb2312','utf-8');
        file_put_contents($file_name, PHP_EOL . $show_str, FILE_APPEND);
        foreach ($stat_list['data'] as $stat_data) {

            if($stat_list['fee_method'] == 1){
                $fee_amount = 'NA';
            }else{
                $fee_amount = $stat_data['fee_amount'];
            }
            $show_str = $stat_data['fee_date'] . ',' . $fee_amount . ',' . $stat_data['fee_price'];
            $show_str = mb_convert_encoding($show_str,'gb2312','utf-8');
            file_put_contents($file_name, PHP_EOL . $show_str, FILE_APPEND);
        }
    }

    /**
     * 为fileDownload插件生成文件
     * @param $file_name
     */
    public function genFileForFileDownload($file_name)
    {
        // file download
        $file_size = filesize($file_name);

        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $file_size);
        header("Content-Disposition: attachment; filename=" . basename($file_name));
        header('Set-Cookie: fileDownload=true; path=/');

        // read file
        $file = new \SplFileObject($file_name, 'r');
        echo $file->fread($file_size);

        file_exists($file_name) && @unlink($file_name);
    }
}
