<?php

namespace Home\Controller;

use Home\Model\ChannelInterfaceModel;
use Common\Controller\AdminController;


/**
 * 测试用例页面
 * Class TestCaseController
 * @package Home\Controller
 * <AUTHOR>
 */
class PriceInterfaceController extends AdminController
{
    private $channel;
    private $channel_interface;
    private $config_price_interface;
    private $operator_arr = [
        '中国移动' => 'CMCC',
        '中国联通' => 'CUCC',
        '中国电信' => 'CTCC',
        '中国广电' => 'CBN'
    ];
    private $encrypt_arr = [
        '明文' => 'CLEAR',
        'MD5' => 'MD5',
        'SHA256' => 'SHA256'
    ];


    public function __construct()
    {
        parent::__construct();
        $this->channel = D('Channel');
        $this->channel_interface = D('Channel_interface');
        $this->config_price_interface = D('ConfigPriceInterface');

    }


    /**
     * 用例测试页面
     */
    public function index()
    {
        $data = I('post.');

        $if_time = C('ALLOW_INTERFACE_PRICE_TIME_CONFIG');
        /*if($data['flag'] == 'change_product'){
            $id = $data['id'];
            $channel_info = $this->channel->where(['father_id'=>$id])->select();
            $option = '<option value="">选择渠道</option>';
            if(!empty($channel_info)){
                foreach($channel_info as $key=>$value){
                    $option .= '<option value="'.$value["channel_id"].'">'.$value["label"].'</option>';
                }
                $result = ['status' => 'ok', 'msg' => '成功', 'option'=>$option];
                $this->ajaxReturn($result);
            }else{
                $result = ['status' => 'error', 'msg' => '没有对应的渠道'];
                $this->ajaxReturn($result);
            }

        }else */
        if($data['flag'] == 'change_channel'){
            $id = $data['id'];
            $is_qufen = $data['is_qufen'];
            $is_qufen_operator = $data['is_qufen_operator'];
            $is_qufen_encrypt = $data['is_qufen_encrypt'];

            $selectInfos = $this->channel_interface->where(['channel_id'=>$id])->select();
            if (!$data['select_interface']) {
                $interFaceSelect = "<option value='' selected='selected'>全部</option>";
            } else {
                $interFaceSelect = "<option value=''>全部</option>";
            }

            foreach ($selectInfos as $t) {
                $interfaceId = intval($t['id']);
                $interfacelabel = $t['label'];
                if ($data['select_interface'] == $interfaceId) {
                    $interFaceSelect .= "<option value='{$interfaceId}' selected='selected'>{$interfacelabel}</option>";
                } else {
                    $interFaceSelect .= "<option value='{$interfaceId}'>{$interfacelabel}</option>";
                }

            }

            $where = ['channel_id'=>$id];
            if ($data['select_interface']) {
                $where['id'] = $data['select_interface'];
            }
            $info = $this->channel_interface->where($where)->select();
            //看看是否区分接口
            if($is_qufen == 1){
                //区分
                if($is_qufen_operator == 2){
                    //不区分运营商
                    $this -> operator_arr = ['不区分运营商'=>'ALL'];
                }
                if($is_qufen_encrypt == 2){
                    //不加密
                    $this -> encrypt_arr = ['不区分加密方式'=>'ALL'];
                }
                $arr = [];
                $arr2 = [];

                foreach($this->operator_arr as $k=>$v){
                    foreach($this->encrypt_arr as $kk=>$vv){
                        $str2 = $k.'#'.$kk;
                        $arr2[] = $str2;
                    }
                }
                foreach($info as $kkk=>$vvv){
                    foreach($arr2 as $kkkk=>$vvvv){
                        $str = $vvv['id'].'@'.$vvv['label'].'#'.$vvvv;
                        $arr[$vvv['id'].'@'.$vvv['label']][] = $str;
                    }
                }
                $input = '';
                foreach($arr as $kk=>$val){
                    $i = 1;
                    foreach($val as $key=>$value){
                        $value_arr = explode('#', $value);
                        $interface_arr = explode('@', $value_arr[0]);
                        if($i == 1){
                            $input .= '<div class="form-group jj"><div align="center" style="color: red;font-size: large">'.$interface_arr[1].'</div><div align="center" style="color: red;font-size: large"><input type="date" name="arr_time['.$interface_arr[0].']" value=""/></div><div class="col-sm-3">运营商名:<input type="text" disabled name="operator[]" class="form-control" value="'.$value_arr[1].'"></div><div class="col-sm-3">加密方式:<input type="text" disabled name="encrypt[]" class="form-control" value="'.$value_arr[2].'"></div><div class="col-sm-3">接口价格:<input type="text" name="price[]" data-id="'.$interface_arr[0].'" class="form-control" maxlength="50" value=""></div><div class="col-sm-3">计费依据:<select name="price_model[]" class="form-control"><option value="1">成功调用量</option><option value="2">查得量</option><option value="3">计费量</option></select></div></div>';
                        }else{
                            $input .= '<div class="form-group jj"><div class="col-sm-3">运营商名:<input type="text" disabled name="operator[]" class="form-control" value="'.$value_arr[1].'"></div><div class="col-sm-3">加密方式:<input type="text" disabled name="encrypt[]" class="form-control" value="'.$value_arr[2].'"></div><div class="col-sm-3">接口价格:<input type="text" name="price[]" data-id="'.$interface_arr[0].'" class="form-control" maxlength="50" value=""></div><div class="col-sm-3">计费依据:<select name="price_model[]" class="form-control"><option value="1">成功调用量</option><option value="2">查得量</option><option value="3">计费量</option></select></div></div>';
                        }
                        $i++;
                    }

                }
                /*foreach($arr as $key=>$value){
                    $value_arr = explode('#', $value);
                    $input .= '<div class="form-group jj"><div align="center" style="color: red;font-size: large">".$value_arr[0]."</div><div class="col-sm-2">运营商名:<input type="text" disabled name="operator[]" class="form-control" value="'.$value_arr[1].'"></div><div class="col-sm-2">加密方式:<input type="text" disabled name="encrypt[]" class="form-control" value="'.$value_arr[2].'"></div><div class="col-sm-2">接口价格:<input type="text" name="price[]" class="form-control" maxlength="50" value=""></div><div class="col-sm-2">计费依据:<select name="price_model[]" class="form-control"><option value="1">成功调用量</option><option value="2">查得量</option></select></div></div>';

                }*/
                if(empty($input)){
                    $result = ['status' => 'error', 'msg' => '没有对应的接口'];
                    $this->ajaxReturn($result);
                }
                $result = ['status' => 'ok', 'msg' => '成功', 'input'=>$input, 'interFaceSelect' => $interFaceSelect];
                $this->ajaxReturn($result);

            }else{
                //不区分
                if($is_qufen_operator == 2){
                    //不区分运营商
                    $this -> operator_arr = ['不区分运营商'=>'ALL'];
                }
                if($is_qufen_encrypt == 2){
                    //不加密
                    $this -> encrypt_arr = ['不区分加密方式'=>'ALL'];
                }
                $arr = [];

                foreach($this->operator_arr as $k=>$v){
                    foreach($this->encrypt_arr as $kk=>$vv){
                        $str = $k.'#'.$kk;
                        $arr[] = $str;
                    }
                }
                $input = '';
                foreach($arr as $key=>$value){
                    $value_arr = explode('#', $value);
                    $input .= '<div class="form-group jj"><div class="col-sm-3">运营商名:<input type="text" disabled name="operator[]" class="form-control" value="'.$value_arr[0].'"></div><div class="col-sm-3">加密方式:<input type="text" disabled name="encrypt[]" class="form-control" value="'.$value_arr[1].'"></div><div class="col-sm-3">接口价格:<input type="text" name="price[]" class="form-control" maxlength="50" value=""></div><div class="col-sm-3">计费依据:<select name="price_model[]" class="form-control"><option value="1">成功调用量</option><option value="2">查得量</option><option value="3">计费量</option></select></div></div>';

                }
                if(empty($input)){
                    $result = ['status' => 'error', 'msg' => '没有对应的接口'];
                    $this->ajaxReturn($result);
                }
                $result = ['status' => 'ok', 'msg' => '成功', 'input'=>$input];
                $this->ajaxReturn($result);
            }


        }else if($data['flag'] == 'add'){

            $is_qufen = $data['is_qufen'];
            $channel_id = $data['channel_id'];//不区分的时候用来获取所有的接口id
            $arr = $data['arr'];
            if($is_qufen == 1){
                //区分接口
                $info = [];
                foreach($arr as $key=>$value){
                    $a = explode('#', $value);
                    //$b = explode('@', $a[0]);
                    $interface_id = $a[0];
                    $operator = isset($this->operator_arr[$a[1]]) ? $this->operator_arr[$a[1]] : 'ALL';
                    $encrypt_way = isset($this->encrypt_arr[$a[2]]) ? $this->encrypt_arr[$a[2]] : 'ALL';
                    $price = $a[3];
                    $price_model = $a[4];
                    $start_date = $a[5];
                    $info[$interface_id.'@'.$start_date][] = [
                        'operator'=>$operator,
                        'encrypt_way'=>$encrypt_way,
                        'price' => trim($price),
                        'price_model' => $price_model
                    ];
                }
                //father_id不在使用
                $tips = [];
                $add_arr = [];
                foreach($info as $key=>$value){
                    $key_arr = explode('@', $key);
                    $interface_id = $key_arr[0];
                    $start_date = $key_arr[1];
                    $info_json = json_encode($value, JSON_UNESCAPED_UNICODE);
                    $find_one = $this->config_price_interface
                        ->where(['interface_id'=>$interface_id, 'start_date'=>$start_date])
                        ->where('delete_time is null')
                        ->find();

                    if(empty($find_one)){
                        $add_arr[] = ['interface_id'=>$interface_id, 'price'=>$info_json, 'start_date'=>$start_date, 'create_time' => date('Y-m-d H:i:s')];
                    }else{
                        $tips[$find_one['interface_id']] = ['interface_id' => $find_one['interface_id'], 'start_date' => $find_one['start_date']];
                        //$this->config_price_interface->where(['interface_id'=>$interface_id, 'start_date'=>$start_date])->save(['interface_id'=>$interface_id, 'price'=>$info_json, 'start_date'=>$start_date]);
                    }
                }

                if(!empty($tips)){
                    $interface_id_arr = array_column($tips, 'interface_id');
                    $interface_label_arr = ChannelInterfaceModel::getInterfaceByIds($interface_id_arr);
                    $interface_label_arr = array_column($interface_label_arr, 'label', 'id');
                    $msg = '';
                    foreach($tips as $val){
                        $msg .= '接口:'.$interface_label_arr[$val['interface_id']].',计费开始日期:'.$val['start_date'].'已存在, 请确认删除后在添加'."\n";
                    }

                    $result = ['status' => 'fail', 'msg' => $msg];

                }else{
                    foreach($add_arr as $item){
                        $this->config_price_interface->add($item);
                    }

                    $result = ['status' => 'ok', 'msg' => '成功'];
                }

                $this->ajaxReturn($result);

            }else{
                $start_date = $data['start_time'];
                if($if_time){
                    if($start_date < date('Ymd')){
                        $result = ['status' => 'error', 'msg' => '配置开始时间不能小于当前时间'];
                        $this->ajaxReturn($result);
                    }
                }
                //不区分接口
                $interface_info = $this->channel_interface->where(['channel_id'=>$channel_id])->select();
                $interface_ids = array_column($interface_info, 'id');
                $info = [];
                foreach($interface_ids as $k=>$val){
                    foreach($arr as $key=>$value){
                        $a = explode('#', $value);
                        $operator = isset($this->operator_arr[$a[0]]) ? $this->operator_arr[$a[0]] : 'ALL';
                        $encrypt_way = isset($this->encrypt_arr[$a[1]]) ? $this->encrypt_arr[$a[1]] : 'ALL';
                        $price = $a[2];
                        $price_model = $a[3];
                        $info[$val][] = [
                            'operator'=>$operator,
                            'encrypt_way'=>$encrypt_way,
                            'price' => trim($price),
                            'price_model' => $price_model,
                        ];
                    }
                }
                foreach($info as $key=>$value){
                    $interface_id = $key;
                    $info_json = json_encode($value, JSON_UNESCAPED_UNICODE);
                    $find_one = $this->config_price_interface->where(['interface_id'=>$interface_id, 'start_date'=>$start_date])->find();
                    if(empty($find_one)){
                        $this->config_price_interface->add(['interface_id'=>$interface_id, 'price'=>$info_json, 'start_date'=>$start_date, 'create_time' => date('Y-m-d H:i:s')]);
                    }else{
                        $this->config_price_interface->where(['interface_id'=>$interface_id, 'start_date'=>$start_date])->save(['interface_id'=>$interface_id, 'price'=>$info_json, 'start_date'=>$start_date]);
                    }
                }
                $result = ['status' => 'ok', 'msg' => '成功'];
                $this->ajaxReturn($result);
            }
        }else{
            $channel_id =  I('get.channel_id', '');
            $input = '';
            if(!empty($channel_id)){
                //默认都是不区分
                $this -> operator_arr = ['不区分运营商'=>'ALL'];
                $this -> encrypt_arr = ['不区分加密方式'=>'ALL'];
                $arr = [];
                foreach($this->operator_arr as $k=>$v){
                    foreach($this->encrypt_arr as $kk=>$vv){
                        $str = $k.'#'.$kk;
                        $arr[] = $str;
                    }
                }
                foreach($arr as $key=>$value){
                    $value_arr = explode('#', $value);
                    $input .= '<div class="form-group jj"><div class="col-sm-3">运营商名:<input type="text" disabled name="operator[]" class="form-control" value="'.$value_arr[0].'"></div><div class="col-sm-3">加密方式:<input type="text" disabled name="encrypt[]" class="form-control" value="'.$value_arr[1].'"></div><div class="col-sm-3">接口价格:<input type="text" name="price[]" class="form-control" maxlength="50" value=""></div><div class="col-sm-3">计费依据:<select name="price_model[]" class="form-control"><option value="1">成功调用量</option><option value="2">查得量</option><option value="3">计费量</option></select></div></div>';

                }

            }
            $channel_info = $this->channel->select();
            $this->assign('if_time', $if_time);
            $this->assign('input', $input);
            $this->assign('channel_id', $channel_id);
            $this->assign('channel_info', $channel_info);
            $this->display();
        }
    }
}

