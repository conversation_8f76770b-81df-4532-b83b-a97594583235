<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmMatchingFileRepository;

class BmMatchingStatFileController extends AdminController
{
    protected $repository_matching_file;

    public function __construct()
    {
        parent::__construct();
        $this->repository_matching_file = new BmMatchingFileRepository();
        set_time_limit(600);
    }

    /**
     * 列表统计
     */
    public function index()
    {
        try {
            $this->repository_matching_file->genFileForList();
        } catch (\Exception $e) {
            $status = 0;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 详情统计
     */
    public function detail()
    {
        try {
            $this->repository_matching_file->genFileForDetail();
        } catch (\Exception $e) {
            $status = 0;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }

    }
}
