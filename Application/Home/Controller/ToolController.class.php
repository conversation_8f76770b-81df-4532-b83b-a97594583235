<?php
namespace Home\Controller;

use Common\Controller\ApiController;

class ToolController extends ApiController{
    // 生成唯一字符串
    public function hashid(){
        $hash_string = '';
        for ($i=0; $i < 10; $i++) {
            $hash_string .= md5(uniqid().mt_rand(1,9999999));
        }
        $length = I('get.length','32','intval');

        $string = substr($hash_string,0,$length);
        $this->__Return('操作成功',$string, 'success');
    }

    function gen_apikey()
    {
        $sea = ['e','a','f'];
        $rep = array_map('strtoupper', $sea);
        $str = md5(microtime(true) . uniqid(rand(), true));
        $apikey = str_replace($sea, $rep, $str);
        $this->__Return('操作成功',$apikey, 'success');
    }

    function gen_sig_str($pwd)
    {
        $str = substr($pwd, 3, 3).'{tel}'.substr($pwd, 10, 4).'{uid}'.substr($pwd, 20, 3).'{app}'.substr($pwd, 28, 3).'{app_ver}'.substr($pwd, 32, 3).'{apikey}'.substr($pwd, 40, 4).'{country}'.substr($pwd, 50, 3).'{version}'.substr($pwd, 65, 2);
        return $str;
    }

    function gen_sig($sign_str, $tel,$uid, $app, $app_ver, $apikey, $country, $version)
    {
        $pattern = [
            '/({tel})/',
            '/({uid})/',
            '/({app})/',
            '/({app_ver})/',
            '/({apikey})/',
            '/({country})/',
            '/({version})/',
        ];
        $replace = [
            $tel,
            $uid,
            $app,
            $app_ver,
            $apikey,
            $country,
            $version,
        ];
        $sign_str = preg_replace($pattern, $replace, $sign_str);
        return substr(sha1($sign_str), 4, 32);
    }
}