<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Think\Cache\Driver\Redis;

class ChannelChangeController extends AdminController
{
    //yulore
    protected $switch_tag = 'calls_switch_channel_tag';
    protected $channels = ['xinde', 'yulore', 'ext'];
    //爬虫渠道地址
    protected $bmp_crawl_channel = 'bmp_crawl_channel';
    //重置密码渠道地址
    protected $bmp_pwd_channel = 'bmp_pwd_channel';
    //不支持的省份
    protected $crawler_unsurport = 'crawler_unsurport';

    protected $redis;

    public function _initialize()
    {
        parent::_initialize();
        $this->redis = new Redis(C('REDIS_CHANNEL_CONFIG'));
    }

    /**cl 如果是yulore,那么还原
     * @param $query_params
     * @return string
     */
    public function switchBack($query_params)
    {
        $kw = isset($query_params['kw']) ? trim($query_params['kw']) : '';
        $dist = isset($query_params['dist']) ? trim($query_params['dist']) : '';
        $channel = isset($query_params['cl']) ? trim($query_params['cl']) : 'yulore';

        if (!$kw) {
            return json_encode(['code' => '8000', 'msg' => '缺少kw参数'], JSON_UNESCAPED_UNICODE);
        }

        if (!in_array($channel, $this->channels)) {
            return json_encode(['code' => '8000', 'msg' => '要切换的服务器不是合法的'], JSON_UNESCAPED_UNICODE);
        }

        //ext参数特殊处理
        if ($channel == 'ext') {
            if (!$dist || !filter_var($dist, FILTER_VALIDATE_URL)) {
                return json_encode(['code' => '8000', 'msg' => '备用服务器的url不合法'], JSON_UNESCAPED_UNICODE);
            }
        }

        return $this->ChannelChange(md5($kw), $channel, $dist);
    }

    /**
     * 切换通道
     * @param $kw
     * @param $channel
     * @param $dist
     * @return string
     */
    private function ChannelChange($kw, $channel, $dist)
    {
        $bool = true;
        switch (strtolower($channel)) {
            case 'xinde': //储存
                $value = ['dist' => '', 'des' => '第三方通道：信德', 'type' => 'thd'];
                $result = $this->redis->hset($this->switch_tag, $kw, json_encode($value, JSON_UNESCAPED_UNICODE));
                if ($result === false) {
                    $bool = false;
                }
                break;
            case 'ext': //存储
                $value = ['dist' => $dist, 'des' => '邦秒爬备用通道', 'type' => 'ext'];
                $result = $this->redis->hset($this->switch_tag, $kw, json_encode($value, JSON_UNESCAPED_UNICODE));
                if ($result === false) {
                    $bool = false;
                }
                break;
            default: //还原
                if ($this->redis->hexists($this->switch_tag, $kw)) {
                    if (!$this->redis->hdel($this->switch_tag, $kw)) {
                        $bool = false;
                    }
                }
                break;
        }
        return $bool ? json_encode(['code' => 0, 'msg' => 'ok']) : json_encode(['code' => 8000, 'msg' => '通道切换失败'], JSON_UNESCAPED_UNICODE);
    }

    /**获取相应省份的运营商再redis里面的统计数据
     * @param $query_params
     * @return array
     */
    public function ChanelInfo($query_params)
    {

        // filter kw
        $kw = isset($query_params['kw']) ? trim($query_params['kw']) : '';
        if (!$kw) {
            return json_encode(['code' => 8000, 'msg' => '缺少kw参数']);
        }

        // get cache
        $kw = md5($kw);
        $cache = '';
        if ($this->redis->hexists($this->switch_tag, $kw)) {
            $cache = $this->redis->hget($this->switch_tag, $kw);
        }

        return ['code' => 0, 'data' => $cache];
    }

    /**
     * 设置渠道地址
     * @param array $query_params 参数
     * @param string $url 渠道地址
     */
    public function SetBmpCrawlChannel($query_params, $url)
    {
        $flow_type = isset($query_params['flow_type']) ? trim($query_params['flow_type']) : '';
        $flow_type = ($flow_type == '189') ? '10000' : $flow_type;
        $area = isset($query_params['area']) ? trim($query_params['area']) : '';
        if (!$flow_type || !$area) {
            return json_encode(['code' => 8000, 'msg' => '缺少参数']);
        }

        $this->redis->select(1);
        $key = $flow_type.'-'.$area;
        $res = $this->redis->hset($this->bmp_crawl_channel, $key, $url);
        if (!$res) {
            return json_encode(['code' => 8000, 'msg' => '爬虫渠道切换失败']);
        }
        return json_encode(['code' => 0, 'msg' => 'ok']);
    }

    /**
     * 获取渠道地址相关信息
     * @return [type] [description]
     */
    public function BmpCrawlChannel($query_params)
    {
        $flow_type = isset($query_params['flow_type']) ? trim($query_params['flow_type']) : '';
        $flow_type = ($flow_type == '189') ? '10000' : $flow_type;
        $area = isset($query_params['area']) ? trim($query_params['area']) : '';
        if (!$flow_type || !$area) {
            return json_encode(['code' => 8000, 'msg' => '缺少参数']);
        }
        $key = $flow_type.'-'.$area;
        $cache = '';
        $this->redis->select(1);
        if ($this->redis->hexists($this->bmp_crawl_channel, $key)) {
            $cache = $this->redis->hget($this->bmp_crawl_channel, $key);
        }
        return ['code' => 0, 'data' => $cache];
    }

    /**
     * 不支持运营商省份
     * @param array $query_params 参数
     */
    public function Unsurports($query_params)
    {
        $unsur = C('UNSURPORTS');
        $this->redis->select(1);
        $flow_type = isset($query_params['flow_type']) ? $query_params['flow_type'] : '';
        $area = isset($query_params['area']) ? $query_params['area'] : '';
        if (!$flow_type || !$area) {
            return json_encode(['code' => 8000, 'msg' => '缺少参数']);
        }
        $key = $flow_type.'-'.$area;
        $cache = $this->redis->hget($this->crawler_unsurport, $key);
        if ($cache) {
            return true;
        }
        return false;
    }

    /**
     * 设置未支持运营商省份
     * @param array  $query_params 参数
     * @param string $unsur       设置数据
     */
    public function SetUnsurports($query_params, $unsur)
    {
        $flow_type = isset($query_params['flow_type']) ? $query_params['flow_type'] : '';
        $area = isset($query_params['area']) ? $query_params['area'] : '';
        if (!$flow_type || !$area) {
            return json_encode(['code' => 8000, 'msg' => '缺少参数']);
        }
        $key = $flow_type.'-'.$area;
        if ($unsur == '未支持') {
            $kw = md5($area.$flow_type);
            $this->redis->hdel($this->switch_tag, $kw);
            $flow_type = ($flow_type == '189') ? '10000' : $flow_type;
            $craw_key = $flow_type.'-'.$area;
            $this->redis->select(1);
            $this->redis->hdel($this->bmp_crawl_channel, $craw_key);
            $cache = $this->redis->hset($this->crawler_unsurport, $key, 1);
        } else {
            $this->redis->select(1);
            $cache = $this->redis->hdel($this->crawler_unsurport, $key);
        }
        return ['code' => 0, 'data' => $cache];
    }

    /**
     * 设置重置密码渠道地址
     * @param array $query_params 参数
     * @param array $url 重置密码渠道地址
     */
    public function SetPwdChannel($query_params, $url)
    {
        $flow_type = isset($query_params['flow_type']) ? trim($query_params['flow_type']) : '';
        $area = isset($query_params['area']) ? trim($query_params['area']) : '';

        if (!$flow_type || !$area || !$url) {
            return json_encode(['code' => 8000, 'msg' => '缺少参数']);
        }

        $this->redis->select(1);
        $key = $flow_type.'-'.$area;
        $res = $this->redis->hset($this->bmp_pwd_channel, $key, $url);
        if (!$res) {
            return json_encode(['code' => 8000, 'msg' => '重置密码渠道切换失败']);
        }
        return json_encode(['code' => 0, 'msg' => 'ok']);
    }
}