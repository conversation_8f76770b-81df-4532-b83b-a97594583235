<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmpaPushLogRepository;

class BmpaPushLogController extends AdminController
{
    protected $repository;
    public function __construct()
    {
        parent::__construct();
        $this->repository = new BmpaPushLogRepository();
    }
    public function index()
    {
        $data = $this->repository->index();
        $this->assign($data);
        $this->display();
    }
    public function send()
    {
        $res = $this->repository->send();
        if ($res === true) {
            $this->success('推送成功', I('get.callback'));
        } else {
            $this->error('推送失败，失败原因：' . $res, I('get.callback'));
        }
    }
    public function detail()
    {
        $data = $this->repository->detail();
        if ($data===false) {
            $this->redirect('index');
        }
        $this->assign('data', $data);
        $this->display();
    }
}