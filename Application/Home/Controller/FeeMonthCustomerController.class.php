<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\FeeMonthCustomerRepository;
use Common\ORG\Page;

class FeeMonthCustomerController extends AdminController
{
    protected $feeMonth;

    public function _initialize()
    {
        parent::_initialize();
        $this->feeMonth = new FeeMonthCustomerRepository();
    }

    /**
     * 客户产品对账单
     */
    public function customerProductBill()
    {
        list ($product_id, $month_end, $month_begin, $customer_id, $list_customers, $backend_customer_product_bill, $backend_api_sorts, $backend_customer_product_download) = [
            I('product_id', ''),
            date('Y-m', strtotime('first day of last month')),
            date('Y-m', strtotime('first day of last month')),
            I('get.customer_id'),
            $this->feeMonth->getCustomerAll(),
            C('LIST_API_URL')['backend_customer_product_bill'],
            C('LIST_API_URL')['backend_api_sorts'],
            C('LIST_API_URL')['backend_customer_product_download']
        ];

        if (!$customer_id) {
            $this->error('缺少必需参数customer_id');
        }


        $this->assign(compact('month_begin', 'month_end', 'list_customers', 'customer_id', 'product_id',
            'backend_customer_product_bill', 'backend_api_sorts', 'backend_customer_product_download'));
        $this->display();
    }

    /**
     * 天对账单
     */
    public function dayBillList()
    {
        // 初始化参数
        list($month_end, $month_begin, $customer_id, $list_customers, $backend_customer_product_month_bill,
            $backend_api_sorts, $backend_customer_month_bill_download, $product_id, $product_name,
            $backend_day_bill, $date_begin, $date_end) = [
            date('Y-m', strtotime('first day of last month')),
            date('Y-m', strtotime('first day of last month')),
            I('get.customer_id'),
            $this->feeMonth->getCustomerAll(),
            C('LIST_API_URL')['backend_customer_product_month_bill'],
            C('LIST_API_URL')['backend_api_sorts'],
            C('LIST_API_URL')['back_day_bill_download'],
            I('get.product_id'),
            I('get.product_name'),
            C('LIST_API_URL')['backend_day_bill'],
            date('Y-m-d', strtotime('first day of this month')),
            date('Y-m-d')
        ];

        if (!$customer_id) {
            $this->error('缺少必需参数customer_id');
        }

        if (!$product_id) {
            $this->error('缺少必需参数product_id');
        }

        $this->assign(compact('month_begin', 'month_end', 'list_customers','backend_api_sorts',
            'product_id', 'product_name','backend_day_bill','date_end', 'date_begin',
            'customer_id', 'backend_customer_product_month_bill', 'backend_customer_month_bill_download'));
        $this->display();

    }

    /**
     * 客户产品月对账单
     */
    public function monthBillList()
    {
        // 初始化参数
        $month_end = $month_begin = date('Y-m', strtotime('first day of last month'));
        $customer_id = I('get.customer_id');
        $list_customers = $this->feeMonth->getCustomerAll();
        $backend_customer_product_month_bill = C('LIST_API_URL')['backend_customer_product_month_bill'];
        $backend_api_sorts = C('LIST_API_URL')['backend_api_sorts'];
        $backend_customer_month_bill_download = C('LIST_API_URL')['backend_customer_month_bill_download'];
        list ($product_id, $product_name) = [
            I('get.product_id'),
            I('get.product_name'),
        ];

        if (!$customer_id) {
            $this->error('缺少必需参数customer_id');
        }

        if (!$product_id) {
            $this->error('缺少必需参数product_id');
        }

        $this->assign(compact('month_begin', 'month_end', 'list_customers','backend_api_sorts',
            'product_id', 'product_name',
            'customer_id', 'backend_customer_product_month_bill', 'backend_customer_month_bill_download'));
        $this->display();
    }

    public function index()
    {
        // 客户列表
        $list_customers = $this->feeMonth->getCustomerAll();
        $month_end = $month_begin = date('Y-m', strtotime('first day of last month'));
        $api_customer_bill = C('LIST_API_URL')['backend_api_bill_customer'];
        $backend_api_sorts = C('LIST_API_URL')['backend_api_sorts'];
        $backend_history_download = C('LIST_API_URL')['backend_history_download'];

        $this->assign(compact('list_customers', 'month_begin', 'month_end', 'api_customer_bill', 'backend_api_sorts', 'backend_history_download'));
        $this->display();
    }

    public function detail()
    {
        $input = I('get.');
        $customer = $this->feeMonth->getCustomerAll();
        $where = $this->feeMonth->getFeeMonthCustomerParam();
        $count = $this->feeMonth->getFeeMonthProductNum($where);
        $page = new Page($count, 25);
        $list = $this->feeMonth->getFeeMonthProductList($where, $page->firstRow, $page->listRows);
        $total = $this->feeMonth->getFeeMonthProductSum($where);

        $this->assign('input', $input);
        $this->assign('customer', $customer);
        $this->assign('list', $list);
        $this->assign('total', $total);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function feeMonth()
    {
        $input = I('get.');
        $where = $this->feeMonth->getFeeMonthCustomerParam();
        $product_info = $this->feeMonth->getProductInfo();
        $customer = $this->feeMonth->getCustomerAccountList();
        $list = $this->feeMonth->getFeeMonthProductDetail($where);
        $count = count($list);
        $page = new Page($count, 25);
        $list = array_slice($list, $page->firstRow, $page->listRows);
        $total = $this->feeMonth->getFeeMonthProductSum($where);

        $this->assign('input', $input);
        $this->assign('product_info', $product_info);
        $this->assign('customer', $customer);
        $this->assign('customer_json', json_encode($customer));
        $this->assign('list', $list);
        $this->assign('total', $total);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function feeDay()
    {
        $input = I('get.');
        $where = $this->feeMonth->getFeeDayCustomerParam();
        $product_info = $this->feeMonth->getProductInfo();
        $customer = $this->feeMonth->getCustomerAccountList();
        $list = $this->feeMonth->getFeeDayProductDetail($where);
        $count = count($list);
        $page = new Page($count, 25);
        $list = array_slice($list, $page->firstRow, $page->listRows);
        $total = $this->feeMonth->getFeeDayProductSum($where);
        $this->assign('input', $input);
        $this->assign('product_info', $product_info);
        $this->assign('customer', $customer);
        $this->assign('customer_json', json_encode($customer));
        $this->assign('list', $list);
        $this->assign('total', $total);
        $this->assign('page', $page->show());
        $this->display();
    }

    /**
     * 发送月对账单
     */
    public function emailBill()
    {
        // 邮件发送对账单需要的接口地址
        $list_apis = [
            'backend_api_history' => C('LIST_API_URL')['backend_api_history'],
            'backend_api_email_bill' => C('LIST_API_URL')['backend_api_email_bill'],
            'backend_api_email' => C('LIST_API_URL')['backend_api_email'],
        ];

        $this->assign($list_apis);
        $this->assign(I('get.'));
        $this->display('month_bills');
    }
}