<?php

namespace Home\Controller;

use Common\Model\AuxiliaryServices;
use Common\ORG\Page;
use Common\Controller\AdminController;

class SpController extends AdminController
{
    private $db;

    public function _initialize()
    {
        parent::_initialize();
        $this->db = D('Auth');
    }

    //账户列表
    public function index()
    {
        $input['status'] = I('get.status');
        $input['active'] = I('get.active');
        $where = $input['status'] ? ['status' => $input['status']] : [];
        $condition = $input['active'] ? ['active' => $input['active']] : [];

        $statuslists = $this->db->getStatusLists();
        $lists = $this->db
            ->field('id,developer,created_at,status,apikeyid')
            ->order(['id' => 'desc'])
            ->where($where)
            ->select();

        $adminapikey = D('AdminApikey');
        $fields = 'id,owner,active,created_at as created_time';
        $infos = $adminapikey->getInfoList($fields, $condition);

        foreach ($lists as $key => &$value) {
            if (!empty($value['apikeyid'])) {
                if (isset($infos[$value['apikeyid']])) {
                    $value['active'] = $infos[$value['apikeyid']]['active'];
                    $value['created_time'] = $infos[$value['apikeyid']]['created_time'];
                    unset($infos[$key]);
                }
            }
        }

        if (!empty($input['active'])) {
            foreach ($lists as $key => &$value) {
                if (!isset($value['active'])) {
                    unset($lists[$key]);
                }
            }
        }

        $count = count($lists);
        $Page = new Page($count, C('LIST_ROWS'));
        $lists = array_slice($lists, $Page->firstRow, $Page->listRows);

        $this->assign('input', $input);
        $this->assign('rst', $lists);
        $this->assign('page', $Page->show());
        $this->assign('status_lists', $statuslists);
        $this->display();
    }

    //账户编辑
    public function setAccount()
    {
        if (!IS_POST) {
            $id = I('get.id', 0, 'intval');
            $statuslists = $this->db->getStatusLists();
            $adminapikey = D('AdminApikey');
            $adminapictrl = D('AdminApictrl');
            $dictapictrlfields = D('DictApictrlFields');

            // get pei,api field info
            $apikeyid = $this->db
                ->field('apikeyid')
                ->where(['id' => $id])
                ->find();
            $apikeyid = $apikeyid['apikeyid'];
            $field_info = $dictapictrlfields->getFieldList();
            $pei_info = $adminapikey->getInfoById($apikeyid);
            $outFields = D('AdminOutputFields')
                ->field('id,name,remark')
                ->where('status=1')
                ->select();
            $chooseFileds = D('AdminApikeyFields')->getOutFields($apikeyid);

            // generate sig_str
            $pei_info['sig_str'] = (new ToolController())->gen_sig_str($pei_info['password']);
            $pei_info['sig'] = (new ToolController())->gen_sig($pei_info['sig_str'], '10086', 'NMKJ', 'myApp',
                '1.0', $pei_info['apikey'], '86', '1');
            $where = ['apikeyid' => $apikeyid];
            $apictrlinfo = $adminapictrl->getInfo($where);

            // get auth,cuishou info
            $auth_info = [];
            $cuishou_info = [];
            if ($id) {
                $auth_info = $this->db->getSpById($id);
                $cuishou_info = D('AuxiliaryServices')
                    ->where(['uid' => $id, 'service_cateid' => 2])
                    ->find();
            }

            $this->assign('lists', $pei_info);
            $this->assign('ctrlinfo', $apictrlinfo);
            $this->assign('statuslists', $statuslists);
            $this->assign('info', $auth_info);
            $this->assign('cuishou_info', $cuishou_info);
            $this->assign('fieldlist', $field_info);
            $this->assign('status_lists', $this->db->getStatusLists());
            $this->assign('out_fields', $outFields);
            $this->assign('choose_fields', $chooseFileds);
            $this->display();
        }
    }

    // 账户详情
    public function accountMsg()
    {
        $this->setAccount();
    }

    // 邦秒配列表
    public function bmMatching()
    {
        $adminapikey = D('AdminApikey');
        $input['active'] = I('get.active', '', 'trim');
        $where = array();

        if (!empty($input['active'])) {
            $where['active'] = $input['active'];
        }
        $count = $adminapikey->where($where)->count();
        $Page = new Page($count, C('LIST_ROWS'));

        $lists = $adminapikey
            ->limit($Page->firstRow . ',' . $Page->listRows)
            ->getInfos(true, $where);
        $statuslists = $this->db->getStatusLists();

        $this->assign('input', $input);
        $this->assign('fields', $lists);
        $this->assign('status_lists', $statuslists);
        $this->assign('page', $Page->show());
        $this->display();
    }

    // 邦秒配增加客户
    public function bmMatchingAdd()
    {
        $adminapikey = D('AdminApikey');
        $adminapictrl = D('AdminApictrl');
        $dictapictrlfields = D('DictApictrlFields');

        if (!IS_POST) {
            $fieldlist = $dictapictrlfields->getFieldList();
            $outFields = D('AdminOutputFields')
                ->field('id,name,remark')
                ->where('status=1')
                ->select();
            $this->assign('fieldlist', $fieldlist);
            $this->assign('out_fields', $outFields);
            $this->display();
        } else {
            $input = I('post.');

            $apikey['owner'] = trim($input['owner']);
            $apikey['active'] = $input['active'];
            $apikey['created_at'] = time();
            $outFields = isset($input['out_fields']) ? $input['out_fields'] : '';

            if (!empty($input['apikey'])) {
                $apikey['apikey'] = $input['apikey'];
            } else {
                $this->__Return('apikey不能为空', '', 'tip_error');
            }

            if (!empty($input['validuntil']) && strtotime($input['validuntil']) > time()) {
                $apikey['validuntil'] = $input['validuntil'];
            } else {
                $this->__Return('截止日期输入有误', '', 'tip_error');
            }

            unset($input['owner']);
            unset($input['active']);
            unset($input['apikey']);
            unset($input['validuntil']);
            unset($input['out_fields']);

            try {
                $apictrl = $adminapictrl->filterInfo($input);
                $apikeyId = $adminapikey->addInfo($apikey, $apictrl);
                if ($outFields) {
                    $result = D('AdminApikeyFields')->addInfo($apikeyId, $outFields);
                }

            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }

            if ($result !== false) {
                $this->__Return('操作成功', '', 'tip_success');
            }
        }
    }

    //邦秒配编辑
    public function bmMatchingSet()
    {
        $id = I('get.id');
        $adminapikey = D('AdminApikey');
        $adminapictrl = D('AdminApictrl');
        $dictapictrlfields = D('DictApictrlFields');
        $adminApikeyFields = D('AdminApikeyFields');

        if (!IS_POST) {
            $fieldlist = $dictapictrlfields->getFieldList();
            $lists = $adminapikey->getInfoById($id);
            $statuslists = $this->db->getStatusLists();

            $where = ['apikeyid' => $id];
            $apictrlinfo = $adminapictrl->getInfo($where);

            $this->assign('fieldlist', $fieldlist);
            $this->assign('info', $apictrlinfo);
            $this->assign('lists', $lists);
            $this->assign('statuslists', $statuslists);
            $this->display();
        } else {
            $input = I('post.');
            $apikey['owner'] = $input['owner'];
            $outFields = isset($input['out_fields']) ? $input['out_fields'] : '';
            if (!empty($input['active'])) {
                $apikey['active'] = $input['active'];
            }

            if (!empty($input['validuntil']) && strtotime($input['validuntil']) > time()) {
                $apikey['validuntil'] = $input['validuntil'];
            } else {
                $this->__Return('截止日期输入有误', '', 'tip_error');
            }
            unset($input['owner']);
            unset($input['active']);
            unset($input['apikey']);
            unset($input['validuntil']);
            unset($input['out_fields']);

            try {
                $fieldResult = $adminApikeyFields->updateInfo($id, $outFields);
                $apictrl = $adminapictrl->filterInfo($input);
                $result = $adminapikey->setInfo($id, $apikey, $apictrl);

            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }

            if ($result !== false && $fieldResult === true) {
                $this->__Return('操作成功', '', 'tip_success');
            }
        }
    }

    //邦秒爬列表
    public function bmCrawling()
    {
        $input['status'] = I('get.status', '', 'trim');
        $where = array();
        if (!empty($input['status'])) {
            $where['status'] = $input['status'];
        }
        $count = $this->db->where($where)->count();
        $Page = new Page($count, C('LIST_ROWS'));

        $lists = $this->db
            ->field('developer,email,status,expiration_date,created_at,id')
            ->where($where)
            ->limit($Page->firstRow . ',' . $Page->listRows)
            ->order(['id' => 'DESC'])
            ->select();
        $this->assign('input', $input);
        $this->assign('lists', $lists);
        $this->assign('status_lists', $this->db->getStatusLists());
        $this->assign('page', $Page->show());
        $this->display();
    }

    // 邦秒爬添加客户
    public function bmCrawlingAdd()
    {
        if (!IS_POST) {
            $this->assign('status_lists', $this->db->getStatusLists());
            $this->display();
        } else {
            try {
                // add  auth
                $info = array();
                $info['email'] = I('post.email', '', 'trim');
                $info['status'] = I('post.status', '1', 'intval');
                $info['appid'] = I('post.appid', '', 'trim');
                $info['appsecret'] = I('post.appsecret', '', 'trim');
                $info['developer'] = I('post.developer', '', 'trim');
                $info['source'] = I('post.source', 'ui', 'trim');
                $info['notify_url'] = I('post.notify_url', '', 'trim');
                $info['close_redirect_url'] = I('post.close_redirect_url', '', 'trim');
                $info['need_report'] = I('post.need_report', 0, 'intval');
                $info['need_dunning'] = I('post.need_dunning', 2, 'intval');
                $info['token_due_date'] = I('post.token_due_date', 0, 'trim');
                $info['contactor_page'] = I('post.contactor_page', 'N', 'trim');
                $info['expiration_date'] = I('post.expiration_date', 0, 'trim');

                // check cuishou
                $info['service_key'] = I('post.service_key', '', 'trim');
                $info['service_secret'] = I('post.service_secret', '', 'trim');

                $id = D('Auth')->setinfo($info);

                // add cuishou
                $data = [];
                $data['uid'] = $id;
                $data['service_cateid'] = 2;
                $data['service_key'] = I('post.service_key', '', 'trim');
                $data['service_secret'] = I('post.service_secret', '', 'trim');
                $data['created_at'] = time();
                $AuxiliaryServicesModel = new AuxiliaryServices();
                if (($info['need_dunning'] == 1) && $AuxiliaryServicesModel->filterParam($data)) {
                    if (($AuxiliaryServicesModel->add($data)) === false) {
                        throw new \Exception('催收分参数插入失败,请在编辑模式添加');
                    }
                }

            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        }
    }

    // 设置SP
    public function setinfo()
    {
        if (!IS_POST) {
            $info = array();
            $id = I('get.id', 0, 'intval');
            if ($id) {
                $info = $this->db->getSpById($id);
            }
            $this->assign('info', $info);
            $this->assign('status_lists', $this->db->getStatusLists());
            $this->display();
        } else {
            try {

                // update crs_auth
                $id = I('post.id', 0, 'intval');
                $info = [];
                $info['email'] = I('post.email', '', 'trim');
                $info['status'] = I('post.status', '1', 'intval');
                $info['appid'] = I('post.appid', '', 'trim');
                $info['appsecret'] = I('post.appsecret', '', 'trim');
                $info['developer'] = I('post.developer', '', 'trim');
                $info['token_due_date'] = I('post.token_due_date', 0, 'trim');
                $info['expiration_date'] = I('post.expiration_date', 0, 'trim');
                $info['source'] = I('post.source', 'ui', 'trim');
                $info['contactor_page'] = I('post.contactor_page', 'N', 'trim');
                $info['notify_url'] = I('post.notify_url', '', 'trim');
                $info['close_redirect_url'] = I('post.close_redirect_url', '', 'trim');
                $info['need_report'] = I('post.need_report', 0, 'intval');
                $info['need_dunning'] = I('post.need_dunning', 2, 'intval');

                // check cuishou
                $info['service_key'] = I('post.service_key', '', 'trim');
                $info['service_secret'] = I('post.service_secret', '', 'trim');

                $save_result = $this->db->setinfo($info, $id);

                // update cuishou （must use updateParam）
                $data = [];
                $data['uid'] = $id;
                $data['service_cateid'] = 2;
                $data['service_key'] = I('post.service_key', '', 'trim');
                $data['service_secret'] = I('post.service_secret', '', 'trim');

                if (($save_result !== false) && ($info['need_dunning'] == 1)) {
                    if ((new AuxiliaryServices())->updateParam($data) === false) {
                        throw new \Exception('催收分参数更新失败');
                    }
                }
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        }
    }

    // SP配置修改
    public function setconfig()
    {
        $sp_config_db = D('SpConfig');
        if (!IS_POST) {
            $info = array();
            $sid = I('get.sid', 0, 'intval');
            if ($sid) {
                $info = $sp_config_db->getConfigBySid($sid);
            }

            $this->assign('sid', $sid);
            $this->assign('info', $info);
            $this->assign('field_lists', $sp_config_db->getConfigField());
            $this->display();
        } else {
            try {
                $sid = I('post.sid', 0, 'intval');
                $config = array_map('trim', $_POST['config']);
                $sp_config_db->setconfig($config, $sid);
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        }
    }

    // 账户信息
    public function accountinfo()
    {
        $sid = I("get.sid", 0, 'intval');
        $sp_info = $this->db->getSpById($sid);
        if (empty($sp_info)) {
            $this->__Return('SP不存在');
        }
        $sp_recharge_db = D('SpRecharge');
        $lists = $sp_recharge_db->getSpRechargeInfo($sid);

        $this->assign('lists', $lists);
        $this->assign('sp_info', $sp_info);
        $this->assign('level_lists', C('SP_NSM_LEVEL'));
        $this->display();
    }

    // SP充值
    public function recharge()
    {
        $sp_recharge_db = D('SpRecharge');
        if (!IS_POST) {
            $sid = I("get.sid", 0, 'intval');
            $this->assign('sid', $sid);
            $this->assign('recharge_type_lists', $sp_recharge_db->getRechargeTypeLists());
            $this->assign('level_lists', C('SP_NSM_LEVEL'));
            $this->display();
        } else {
            try {
                $type = I('post.type', 0, 'intval');

                $info = array();
                $info['sid'] = I('post.sid', 0, 'intval');
                $info['level'] = I('post.level', 0, 'intval');
                $info['account'] = I('post.account', 0, 'intval');
                $info['remark'] = I('post.remark', '', 'trim');

                $sp_recharge_db->setRecharge($info, $type);
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        }
    }

    // SP充值记录
    public function rechargerecord()
    {
        $sp_recharge_record_db = D('SpRechargeRecord');
        $input = $where = array();
        $input['sid'] = I('get.sid', 0, 'intval');
        if (!empty($input['sid'])) {
            $where['sid'] = $input['sid'];
        }

        $input['level'] = I('get.level', 0, 'intval');
        if (!empty($input['level'])) {
            $where['level'] = $input['level'];
        }

        $count = $sp_recharge_record_db->where($where)->count();
        $Page = new Page($count, 50);
        $lists = $sp_recharge_record_db->where($where)->order(['id' => 'DESC'])->limit($Page->firstRow . ',' . $Page->listRows)->select();

        $this->assign('input', $input);
        $this->assign('lists', $lists);
        $this->assign('page', $Page->show());
        $this->assign('sp_lists', $this->db->getAllSp());
        $this->assign('level_lists', C('SP_NSM_LEVEL'));
        $this->display();
    }
}
