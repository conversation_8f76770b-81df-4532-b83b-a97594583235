<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmCrawlerRateRepository;

class BmCrawlerRateController extends AdminController
{
    // 运营商
    protected $flow = ['10086' => '移动', '10010' => '联通', '189' => '电信'];

    // 供应商
    protected $channel;

    protected $bmCrawlerRateRepository;

    public function _initialize()
    {
        parent::_initialize();
        $this->bmCrawlerRateRepository = new BmCrawlerRateRepository();
        $this->channel = $this->bmCrawlerRateRepository->getChannelList();
    }

    // 列表
    public function index()
    {
        $flow_type = I('get.flow', '', 'trim');
        $channel_type = I('get.channel', '', 'trim');
        $server_list = $this->bmCrawlerRateRepository->getServerList();
        $server_list = array_column($server_list, null, 'type');
        $list = $this->bmCrawlerRateRepository->getChannelRateList();
        if ($list && is_array($list)) {
            foreach ($list as $key => &$value) {
                if ($flow_type && $value['channel']['carrier'] != $flow_type) {
                    continue;
                }
                if ($channel_type && !$value['rate'][$channel_type]) {
                    continue;
                }
                $flow_cn = $this->flow[$value['channel']['carrier']];
                $value['channel_cn'] = $value['channel']['location'].$flow_cn;
                $value['support_cn'] = ($value['support']['yulore'] == 1) ? '已支持' : '未支持';
                $value['server_cn'] = $server_list[$value['dist']['yulore']]['name'];
                $rate = '';
                foreach ($value['rate'] as $kk => $vv) {
                    $rate .= $this->channel[$kk].$vv.'% ';
                }
                $value['rate_cn'] = $rate;
            }
        }

        $channel = $this->bmCrawlerRateRepository->getChannelList();

        $this->assign('input', I('get.'));
        $this->assign('flow', $this->flow);
        $this->assign('channel', $channel);
        $this->assign('list', $list);
        $this->assign('server_list', $server_list);
        $this->display();
    }

    // 设置流量分配
    public function rate()
    {
        try {
            $res = $this->bmCrawlerRateRepository->setChannelRate();
            if ($res) {
                return $this->__Return('流量分配设置成功', '', 'success');
            }
            return $this->__Return('流量分配设置失败', '', 'error');
        } catch (\Exception $e) {
            return $this->__Return($e->getMessage(), '', 'error');
        }
    }

    // 设置支持状态
    public function support()
    {
        try {
            $res = $this->bmCrawlerRateRepository->setChannelSupport();
            if ($res) {
                return $this->__Return('支持状态切换成功', 'success');
            }
            return $this->__Return('支持状态切换失败', '', 'error');
        } catch (\Exception $e) {
            return $this->__Return($e->getMessage(), '', 'error');
        }
    }

    // 切换主备
    public function server()
    {
        try {
            $res = $this->bmCrawlerRateRepository->setYuloreServer();
            if ($res) {
                return $this->__Return('切换服务器成功', '', 'success');
            }
            return $this->__Return('切换服务器失败', '', 'error');
        } catch (\Exception $e) {
            return $this->__Return($e->getMessage(), '', 'error');
        }
    }

    // 一键切换
    public function channelCrawler()
    {
        try {
            $res = $this->bmCrawlerRateRepository->setChannelSwitch();
            if ($res) {
                return $this->__Return('切换供应商成功', '', 'success');
            }
            return $this->__Return('切换供应商失败', '', 'error');
        } catch (\Exception $e) {
            return $this->__Return($e->getMessage(), '', 'error');
        }
    }
}