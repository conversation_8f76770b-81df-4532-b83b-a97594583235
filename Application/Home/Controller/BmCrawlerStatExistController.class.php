<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-23 13:58:34
 */
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmCrawlerStatRepository;
use Home\Repositories\BmCrawlerStatApiRepository;
use Home\Repositories\BmCrawlerStatExistFileRepository;
use Common\ORG\Page;

class BmCrawlerStatExistController extends AdminController
{
    protected $stat_repository;
    protected $stat_api_repository;
    //详单来源 1 爬虫 2 既有详单
    protected $record_source = 2;

    public function __construct()
    {
        parent::__construct();

        $this->stat_repository = new BmCrawlerStatRepository($this->record_source);

        $this->stat_api_repository = new BmCrawlerStatApiRepository();
    }

    //列表
    public function index()
    {
        $input = I('get.');

        //账号列表
        $product_list = $this->stat_repository->getProductList();
        //查询条件
        $where = $this->stat_repository->getCrawlerListParam();
        $where['cids'] = json_encode(array_keys($product_list));

        //列表
        $data = $this->stat_api_repository->getReportList($product_list, $where);

        $count = count($product_list);
        $page = new Page($count, C('LIST_ROWS'));

        $list = array_slice($data['user_list'], $page->firstRow, $page->listRows);
        $total_data = $data['total_data'];

        $list_account = $this->stat_repository->getAccountListAll();
        $list_product = $this->stat_repository->getProductListAll();

        //签约状态
        $contract_status = $this->stat_repository->getContractStatus();

        $this->assign('input', $input);
        $this->assign('list', $list);
        $this->assign('total_data', $total_data);
        $this->assign('contract_status', $contract_status);
        $this->assign('list_account', $list_account);
        $this->assign('list_product', $list_product);
        $this->assign('page', $page->show());
        $this->display();
    }

    //详情
    public function detail()
    {
        set_time_limit(0);
        $input = I('get.');
        //账号列表
        $user_list = $this->stat_repository->getProductList();

        $where = $this->stat_repository->getCrawlerDetailParam();
        $where['cids'] = json_encode(array_keys($user_list));

        $stat_list = $this->stat_api_repository->getReportDetail($where);

        $date_list = $this->stat_repository->dateList();

        $count = count($date_list);
        $page = new Page($count, C('LIST_ROWS'));

        $date_stat_show = array_merge($date_list, $stat_list['date_stat_show']);

        $date_stat_show = array_slice($date_stat_show, $page->firstRow, $page->listRows);

        $this->assign('input', $input);
        $this->assign('user_list', $user_list);
        $this->assign('date_stat_show', $date_stat_show);
        $this->assign('total_data', $stat_list['total_data']);
        $this->assign('page', $page->show());
        $this->display();
    }

    //列表导出
    public function downloadList()
    {
        //账号列表
        $product_list = $this->stat_repository->getProductList();

        //查询条件
        $where = $this->stat_repository->getCrawlerListParam();
        $where['cids'] = json_encode(array_keys($product_list));
        //列表
        $data = $this->stat_api_repository->getReportList($product_list, $where);

        $file = new BmCrawlerStatExistFileRepository();
        $file->getCrawlerStatExistList($data['user_list']);
        exit;
    }

    //详情导出
    public function downloadDetail()
    {
        //账号列表
        $user_list = $this->stat_repository->getProductList();

        $where = $this->stat_repository->getCrawlerListParam();
        $where['cids'] = json_encode(array_keys($user_list));
        $stat_list = $this->stat_api_repository->getReportDetail($where);

        $date_list = $this->stat_repository->dateList();

        $date_stat_show = array_merge($date_list, $stat_list['date_stat_show']);

        $file = new BmCrawlerStatExistFileRepository();
        $file->getCrawlerStatExistDetail($date_stat_show);
        exit;
    }
}