<?php
namespace Home\Controller;

class IndexController extends \Common\Controller\AdminController {
	//默认入口
	public function index() {
        addBreakPoint('[index]进入');
		//获取当前用户默认页面
		$defaulturl = D('SystemRole')->where(array('id'=>$this->loginuser['defaultroleid']))->getField('default_url');
		property_test();
		if(!empty($defaulturl)){
			redirect(U($defaulturl));
			exit;
		}
		//没有用户默认页面 使用当前页面
		$this->display();
		property_test();
        addBreakPoint('[index]结束');
        printBreakPoint();
	}
}
