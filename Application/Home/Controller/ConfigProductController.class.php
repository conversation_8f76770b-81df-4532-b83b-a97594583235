<?php

namespace Home\Controller;

use Common\Controller\AdminController;


class ConfigProductController extends AdminController
{
    private $repository;
    private $product_config;
    private $config_product_value;

    public function __construct()
    {
        parent::__construct();
        $this->repository = D('Server');
        $this->product_config = D('ProductConfig');
        $this->config_product_value = D('ConfigProductValue');
    }

    public function index()
    {

        $data = I('get.');
        $input = $where = [];
        $input['product_id_search'] = $data['product_id_search'];
        if(!empty($input['product_id_search'])){
            $where['pid'] = $input['product_id_search'];
        }

        $count = $this->config_product_value->where($where)->count();
        $Page  = new \Common\ORG\Page($count, 100);
        $config_list = $this->config_product_value->where($where)->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();
        $this->assign(['input'=>$input]);
        $this->assign(['data'=>$config_list]);
        $this->display();
    }

    public function add()
    {
        $data = I('post.');
        $value = trim($data['val']);
        if($data['type'] == 4){
            $value = '('.trim($data['val_min']).','.trim($data['val_max']).')';
        }
        $arr = [
            'pid' => trim($data['pid']),
            'type' => $data['type'],
            'value' => $value,
        ];
        $product_info = $this->product_config->where(['father_id'=>['neq', 0]])->field(['product_id'])->select();
        $product_ids = array_column($product_info, 'product_id');

        if(!in_array($arr['pid'], $product_ids)){
            $result = ['status' => 'error', 'msg' => '该产品id不存在'];
            $this->ajaxReturn($result);
        }

        $find_one = $this->config_product_value->where($arr)->find();
        if(!empty($find_one)){
            $result = ['status' => 'error', 'msg' => '该配置已存在'];
            $this->ajaxReturn($result);
        }
        $res = $this->config_product_value->add($arr);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }

    public function getEdit()
    {
        $data = I('post.');
        $id = $data['id2'];
        $find_one = $this->config_product_value->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }
        if($find_one['type'] == 4){
            $value = ltrim($find_one['value'], '(');
            $value = rtrim($value, ')');
            $val_arr = explode(',', $value);
            $find_one['val_min'] = $val_arr[0];
            $find_one['val_max'] = $val_arr[1];
        }
        $result = ['status' => 'ok', 'msg' => '成功', 'data'=>$find_one];
        $this->ajaxReturn($result);
    }

    public function edit()
    {
        $data = I('post.');
        $id = $data['id2'];
        if(empty($id)){
            $result = ['status' => 'error', 'msg' => 'id不能为空'];
            $this->ajaxReturn($result);
        }

        $find_one = $this->config_product_value->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }

        $value = trim($data['val2']);
        if($data['type2'] == 4){
            $value = '('.trim($data['val_min2']).','.trim($data['val_max2']).')';
        }
        $arr = [
            'pid' => trim($data['pid2']),
            'type' => $data['type2'],
            'value' => $value,
        ];
        $product_info = $this->product_config->where(['father_id'=>['neq', 0]])->field(['product_id'])->select();
        $product_ids = array_column($product_info, 'product_id');

        if(!in_array($arr['pid'], $product_ids)){
            $result = ['status' => 'error', 'msg' => '该产品id不存在'];
            $this->ajaxReturn($result);
        }

        //判断记录是否有重复
        $find_where = [];
        $find_where['pid'] = $arr['pid'];
        $find_where['type'] = $arr['type'];
        $find_where['value'] = $arr['value'];
        $find_where['id'][] = array('NEQ', $id);
        $find_one2 = $this->config_product_value->where($find_where)->find();
        if(!empty($find_one2)){
            $result = ['status' => 'error', 'msg' => '该条记录已存在'];
            $this->ajaxReturn($result);
        }

        $res = $this->config_product_value->where(['id'=>$id])->save($arr);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }
}
