<?php
/**
 * @Author: lidandan
 * @Date:   2018-08-15 16:35:06
 */
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\FeeProductStatFileRepository;

class FeeProductStatFileController extends AdminController
{
    //产品列表 对应的Model名
    protected $product = ['FeeCrawlerStat' => '邦秒爬', 'FeeMatchingStat' => '邦秒配', 'FeeCuishouStat' => '催收分', 'BangProductFeeStat' => '邦企查', 'RiskListFeeStat' => '风险名单'];

    protected $feeProductStatFile;

    public function _initialize()
    {
        parent::_initialize();
        $this->feeProductStatFile = new FeeProductStatFileRepository();
    }
    //产品对账单
    public function index()
    {
        $input = I('get.');
        $where = $this->getTimeParam($input);
        $list = [];
        $i = 1;
        foreach ($this->product as $key => $value) {
            $fee_amount = D($key)->where($where)->sum('fee_amount');
            $fee_price = D($key)->where($where)->sum('fee_price');
            $list[$i]['name'] = $value;
            $list[$i]['fee_amount'] = $fee_amount ? round($fee_amount, 2) : 0;
            $list[$i]['fee_price'] = $fee_price ? round($fee_price, 2) : '0.00';
            $i++;
        }

        $this->feeProductStatFile->getFeeProductStatDownload($list);
        exit;
    }

    //产品-客户对账单
    public function account()
    {
        $input = I('get.');
        $list = [];
        if (in_array($input['model'], array_keys($this->product))) {
            $where = $this->getTimeParam($input);
            if ($input['name']) {
                $where['account_name'] = $input['name'];
            }
            $list = D($input['model'])->where($where)
                                      ->field('account_id, account_name, sum(fee_amount) as fee_amount, sum(fee_price) as fee_price')
                                      ->group('account_id')
                                      ->order('account_id desc')
                                      ->select();
            $account_list = D('FinanceAccounts')->field('id, name, company')->index('id')->select();
            array_walk($list, function(&$v, $k, $p) {
                $v['company'] = $p[$v['account_id']]['company'];
                $v['account_name'] = $v['account_name'] ? $v['account_name'] : $p[$v['account_id']]['name'];
            }, $account_list);
        }
        $this->feeProductStatFile->getFeeAccountStatDownload($list);
        exit;
    }

    //产品-客户日对账单
    public function detail()
    {
        $input = I('get.');
        $date_list = $this->getDateList();
        $list = [];
        if (in_array($input['model'], array_keys($this->product))) {
            $where = $this->getTimeParam($input);
            $where['account_id'] = $input['id'];
            if ($input['model'] == 'RiskListFeeStat') { //风险名单日对账单
                $fee_list = D($input['model'])->where($where)
                                              ->field('fee_date, sum(fee_own_num) as fee_own_num, sum(fee_own_price) as fee_own_price, sum(fee_input_num) as fee_input_num, sum(fee_input_price) as fee_input_price')
                                              ->group('fee_date')
                                              ->order('fee_date desc')
                                              ->index('fee_date')
                                              ->select();
                $list = array_merge($date_list, $fee_list);
                $this->feeProductStatFile->getRistStatFeeDayStatDownload($list);
                exit;
            } else { //邦秒爬、邦秒配、催收分、邦企查日对账单
                $fee_list = D($input['model'])->where($where)
                                              ->field('fee_date, sum(fee_amount) as fee_amount, sum(fee_price) as fee_price')
                                              ->group('fee_date')
                                              ->order('fee_date desc')
                                              ->index('fee_date')
                                              ->select();
                $list = array_merge($date_list, $fee_list);
                $this->feeProductStatFile->getFeeDayStatDownload($list);
                exit;
            }
        }
        $this->feeProductStatFile->getRistStatFeeDayStatDownload($list);
        exit;
    }

    protected function getDateList()
    {
        $date_list = [];
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end = empty($end) ? strtotime(date('Y-m-d 23:59:59')) : strtotime($end);
        $begin = empty($begin) ? ($end-86400*30) : strtotime($begin);
        while ($end >= $begin) {
            $date = date('Y-m-d', $end);
            $date_list[$date] = 0;
            $end -= 86400;
        }

        return $date_list;
    }

    protected function getTimeParam($input)
    {
        if($input['begin'] && $input['end']){
            $start_time = $input['begin'];
            $end_time = $input['end'];
        }else{
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $where['fee_date'] = ['between', [$start_time, $end_time]];

        return $where;
    }
}