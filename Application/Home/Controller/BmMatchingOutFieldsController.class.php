<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Think\Cache\Driver\Memcache;

class BmMatchingOutFieldsController extends AdminController
{
    public function index()
    {
        $where = [];
        if ($status = I('get.status', '', 'trim')) {
            $where['status'] = $status;
        }

        $list_show = D('AdminOutputFields')->where($where)->select();

        $this->assign('check_query', I('get.'));
        $this->assign('list_show', $list_show);
        $this->display();
    }

    public function create()
    {
        if (IS_GET) {
            $this->display();
            exit();
        }

        // check field
        if (!D('AdminOutputFields')->create(I('post.'), 1)) {
            $this->__Return(D('AdminOutputFields')->getError());
        }

        // add new item
        D('AdminOutputFields')->created_at = time();
        D('AdminOutputFields')->add();


        // clear memcache
        $memcache_config = C("MEMCACHE_CONFIG_ITAG");

        $main_clear_res = false;
        $list_api_key = D('AdminApikey')->field('id')->where(['active' => 1])->select();

        foreach ($memcache_config as $config) {
            $memcache = new Memcache($config);

            foreach ($list_api_key as $api_key) {
                $key_mem = 'fieldCtrl_' . $api_key['id'];
                $i =0;
                while ($i<3) {
                    $main_clear_res = $memcache->set($key_mem, '');
                    if ($main_clear_res) {
                        break;
                    }
                    $i++;
                }

                // log
                if (!$main_clear_res) {
                    $msg = "添加输出字段 account_id: {$api_key['id']}  memcache host: {$config['host']}  memcached failed to clear" . PHP_EOL;
                    $file_debug = LOG_PATH . '/debug';
                    file_put_contents($file_debug, $msg, 8);
                }
            }
        }

        $this->__Return('添加字段成功', '', 'success');
    }

    public function edit()
    {
        if (IS_GET) {
            $id = I('get.id', '', 'trim');
            if (!$id) {
                $this->__Return('编辑模式需要提供id');
            }

            $item = D('AdminOutputFields')->where(['id' =>$id])->find();
            $this->assign('field_item', $item);
            $this->display();
            exit();
        }

        // check field
        if (!D('AdminOutputFields')->create(I('post.'), 2)) {
            $this->__Return(D('AdminOutputFields')->getError());
        }
        D('AdminOutputFields')->updated_at = time();

        // check unique field
        $id = I('post.id', '', 'trim');
        $name = I('post.name', '', 'trim');
        $search_name_id_list = D('AdminOutputFields')->where(['name' => $name])->select();

        // when this name exists,  only it just has one item and only it's id is equal to $id  then go on
        $check_unique = false;
        if (1 == count($search_name_id_list) && $search_name_id_list[0]['id'] == $id) {
            $check_unique = true;
        }

        if (!$search_name_id_list) {
            $check_unique = true;
        }

        if (!$check_unique) {
            $this->__Return('字段已经存在');
        }

        // when this remark exists,  only it just has one item and only it's id is equal to $id  then go on
        $check_unique = false;
        $remark = I('post.remark', '', 'trim');
        $search_remark_id_list = D('AdminOutputFields')->where(['remark' => $remark])->select();
        if (1 == count($search_remark_id_list) && $search_remark_id_list[0]['id'] == $id) {
            $check_unique = true;
        }

        if (!$search_remark_id_list) {
            $check_unique = true;
        }

        if (!$check_unique) {
            $this->__Return('备注已经存在');
        }

        try {
            D('AdminOutputFields')->save();
            // clear memcache
            $memcache_config = C("MEMCACHE_CONFIG_ITAG");

            $main_clear_res = false;
            $list_api_key = D('AdminApikey')->field('id')->where(['active' => 1])->select();

            foreach ($memcache_config as $config) {
                $memcache = new Memcache($config);

                foreach ($list_api_key as $api_key) {
                    $key_mem = 'fieldCtrl_' . $api_key['id'];
                    $i =0;
                    while ($i<3) {
                        $main_clear_res = $memcache->set($key_mem, '');
                        if ($main_clear_res) {
                            break;
                        }
                        $i++;
                    }

                    // log
                    if (!$main_clear_res) {

                        $msg = "更新输出字段 account_id: {$api_key['id']}  memcache host: {$config['host']}  memcached failed to clear" . PHP_EOL;
                        $file_debug = LOG_PATH . '/debug';
                        file_put_contents($file_debug, $msg, 8);
                    }
                }
            }

            $this->__Return('更新成功', '', 'success');
        } catch (\Exception $e) {
            $this->__Return('更新失败, 请稍后重试', '', 'error');
        }
    }
}
