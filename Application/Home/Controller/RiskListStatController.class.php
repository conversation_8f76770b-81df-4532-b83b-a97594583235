<?php
/**
 * 风险名单统计管理
 * @Author: lidandan
 * @Date:   2018-07-03 14:20:53
 */
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\RiskListRepository;
use Home\Repositories\RiskListStatRepository;
use Home\Repositories\RiskListFileRepository;
use Home\Repositories\FinanceAccountRepository;
use Think\Cache;

class RiskListStatController extends AdminController
{
    protected $riskListStat;

    public function _initialize()
    {
        parent::_initialize();
        $this->riskListStat = new RiskListStatRepository();
    }

    //列表统计
    public function index()
    {
        $input = I('get.');
        //查询条件
        $where = $this->riskListStat->getUserListParam();
        //账号列表
        $product_list = $this->getUserListAll();
        //客户列表
        $account_list = $this->getAccountListAll();
        //命中种类
        $hit_type = $this->riskListStat->getHitCountsType();
        //数量
        $count = $this->riskListStat->getUserListNum($where);
        //分页
        $page = new Page($count, C('LIST_ROWS'));
        //列表
        $list_data = $this->riskListStat->getRistListStatList($where, $hit_type);
        $list = array_slice($list_data['list'], $page->firstRow, $page->listRows);
        $total_data = $list_data['total_data'];

        $this->assign('input', $input);
        $this->assign('product_list', $product_list);
        $this->assign('account_list', $account_list);
        $this->assign('hit_type', $hit_type);
        $this->assign('list', $list);
        $this->assign('total_data', $total_data);
        $this->assign('page', $page->show());
        $this->display();
    }

    //详情统计
    public function detail()
    {
        $input = I('get.');
        $info = $this->getRiskUserInfo((int)$input['id']);

        $input['developer'] = $info['developer'];

        //命中种类
        $hit_type = $this->riskListStat->getHitCountsType();
        //列表
        $list_data = $this->riskListStat->getRiskListDetail($hit_type);
        $count = count($list_data['list']);
        //分页
        $page = new Page($count, C('LIST_ROWS'));

        $list = array_slice($list_data['list'], $page->firstRow, $page->listRows);
        $total_data = $list_data['total_data'];

        $this->assign('input', $input);
        $this->assign('hit_type', $hit_type);
        $this->assign('list', $list);
        $this->assign('total_data', $total_data);
        $this->assign('page', $page->show());
        $this->display();
    }

    /**
     * 用户联动
     * @return list
     */
    public function clientList()
    {
        $user_list = $this->riskListStat->clientList();
        $this->__Return($user_list, '', 'success');
    }

    //列表导出
    public function downloadList()
    {
        $input = I('get.');
        //查询条件
        $where = $this->riskListStat->getUserListParam();
        //命中种类
        $hit_type = $this->riskListStat->getHitCountsType();
        //列表
        $list_data = $this->riskListStat->getRistListStatList($where, $hit_type);

        $risk_file = new RiskListFileRepository();
        $risk_file->getRistStatListFile($list_data['list'], $hit_type);
        exit;
    }

    //详情导出
    public function downloadDetail()
    {
        //命中种类
        $hit_type = $this->riskListStat->getHitCountsType();
        //列表
        $list_data = $this->riskListStat->getRiskListDetail($hit_type);

        $risk_file = new RiskListFileRepository();
        $risk_file->getRistStatDetail($list_data['list'], $hit_type);
        exit;
    }

    /**
     * 获取客户所有信息
     * @return array
     */
    protected function getAccountListAll()
    {
        $repository_account = new FinanceAccountRepository();
        $account_list = $repository_account->accountListForGet();
        return $account_list;
    }

    /**
     * 获取账号所有信息
     * @return array list
     */
    protected function getUserListAll()
    {
        $risk = new RiskListRepository();
        $list = $risk->getUserListAll();
        return $list;
    }

    /**
     * 根据ID获取账号信息
     * @param  number $id 账号ID
     * @return array
     */
    protected function getRiskUserInfo($id)
    {
        $risk = new RiskListRepository();
        $info = $risk->getUserInfo($id);
        return $info;
    }
}
