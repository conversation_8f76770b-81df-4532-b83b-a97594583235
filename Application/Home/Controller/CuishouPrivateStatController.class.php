<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CuishouPrivateStatRepository;

class CuishouPrivateStatController extends AdminController
{
    protected $repository_stat;

    /**
     * CuishouPrivateStatController constructor.
     * @param $repository_stat
     */
    public function __construct()
    {
        parent::__construct();
        $this->repository_stat = new CuishouPrivateStatRepository();
    }

    /**
     * 列表页
     */
    public function index()
    {
        // 初始化参数
        $ini_params = $this->repository_stat->genIniParamsForList();
        $ini_params = json_encode($ini_params, JSON_UNESCAPED_UNICODE);

        $this->assign(compact('ini_params'));
        $this->display();
    }

    /**
     * 统计详情
     */
    public function detail()
    {
        // 初始化参数
        $ini_params = $this->repository_stat->genIniParamsForDetail();
        $ini_params = json_encode($ini_params, JSON_UNESCAPED_UNICODE);

        $this->assign(compact('ini_params'));
        $this->display();
    }

}
