<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\DataCheckApiRepository;
use Home\Repositories\DataCheckStatRepository;

class DataCheckApiStatController extends AdminController
{
    protected $dataCheckApi;
    protected $dataCheckStat;

    public function __construct()
    {
        parent::__construct();
        $this->dataCheckApi = new DataCheckApiRepository();
        $this->dataCheckStat = new DataCheckStatRepository();
    }

    public function index()
    {
        $input = I('get.');
        $product_list = $this->dataCheckApi->getOpdataDailProductApi();

        $data = $this->dataCheckApi->getOpdataDailApiStat($product_list);

        $date_list = $this->dataCheckStat->getApiDateList();
        $date_list = $data['list'] ? array_merge($date_list, $data['list']) : $date_list;
        $total = $data['total'];

        $count = count($date_list);
        $page = new Page($count, C('LIST_ROWS'));

        $list = array_slice($date_list, $page->firstRow, $page->listRows);

        $this->assign('input', $input);
        $this->assign('product_list', $product_list);
        $this->assign('list', $list);
        $this->assign('total', $total);
        $this->assign('page', $page->show());

        $this->display();
    }
}