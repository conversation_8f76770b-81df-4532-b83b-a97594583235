<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CrawlerStatListRepository;

class CrawlerStatListController extends AdminController
{
    private $repository_watch_crawler;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_watch_crawler = new CrawlerStatListRepository();
    }

    public function index()
    {
        try {
            $list_watch = $this->repository_watch_crawler->watchCrawlerList();
            $this->assign('show_list', $list_watch);
            $this->display();
        } catch (\Exception $e) {

            $response = [
                'msg' => '捕获错误，请刷新后重试',
                'reason' => $e->getMessage()
            ];
            exit(json_encode($response));
        }
    }
}
