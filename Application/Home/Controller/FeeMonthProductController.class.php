<?php

namespace Home\Controller;

use Account\Model\CustomerExpendModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Controller\AdminController;
use Home\Repositories\FeeMonthProductRepository;
use Common\ORG\Page;

class FeeMonthProductController extends AdminController
{
	protected $feeProduct;
	
	public function _initialize()
	{
		parent::_initialize();
		$this->feeProduct = new FeeMonthProductRepository();
	}
	
	/**
	 * 产品客户对账单
	 */
	public function productBillOfCustomer()
	{
		list ($product_id, $month_end, $month_begin, $backend_product_customer_bill, $backend_api_sorts, $backend_product_customer_bill_excel) = [
			I('get.product_id', ''),
			date('Y-m', strtotime('first day of last month')),
			date('Y-m', strtotime('first day of last month')),
			C('LIST_API_URL')['backend_product_customer_bill'],
			C('LIST_API_URL')['backend_api_sorts'],
			C('LIST_API_URL')['backend_product_customer_bill_excel'],
		];
		
		// 校验
		if (!$product_id) {
			$this->error('缺少必选的参数 product_id');
		}
		
		$this->assign(compact('product_id', 'month_begin', 'month_end', 'backend_product_customer_bill', 'backend_api_sorts', 'backend_product_customer_bill_excel'));
		$this->display();
	}
	
	/**
	 * 产品账单列表
	 */
	public function index()
	{
		$list_products                 = $this->feeProduct->getProductAll();
		$month_end                     = $month_begin = date('Y-m', strtotime('first day of last month'));
		$backend_api_sorts             = C('LIST_API_URL')['backend_api_sorts'];
		$backend_product_bill          = C('LIST_API_URL')['backend_product_bill'];
		$backend_product_bill_download = C('LIST_API_URL')['backend_product_bill_download'];
		
		$this->assign(compact('list_products', 'month_end', 'month_begin', 'backend_api_sorts', 'backend_product_bill', 'backend_product_bill_download'));
		$this->display();
	}
	
	/**
	 * 产品月收入
	 */
	public function detail()
	{
		list ($product_id, $month_end, $month_begin, $backend_product_month_bill, $backend_api_sorts, $backend_product_month_bill_excel) = [
			I('get.product_id', ''),
			date('Y-m', strtotime('first day of last month')),
			date('Y-m', strtotime('first day of last month')),
			C('LIST_API_URL')['backend_product_month_bill'],
			C('LIST_API_URL')['backend_api_sorts'],
			C('LIST_API_URL')['backend_product_month_bill_excel'],
		];
		
		if (!$product_id) {
			$this->error('缺少必选的参数 product_id');
		}
		$this->assign(compact('list_products', 'product_id', 'month_end', 'month_begin', 'backend_api_sorts', 'backend_product_month_bill', 'backend_product_month_bill_excel'));
		$this->display();
	}
	
	public function customer()
	{
		$input    = I('get.');
		$where    = $this->feeProduct->getFeeProductParam();
		$product  = $this->feeProduct->getProductAll();
		$customer = $this->feeProduct->getCustomerAll();
		$count    = $this->feeProduct->getFeeProductNum($where, 'customer_id');
		$page     = new Page($count, 25);
		$list     = $this->feeProduct->getFeeProductCustomer($where, $page->firstRow, $page->listRows);
		$total    = $this->feeProduct->getFeeProductSum($where);
		
		$this->assign('input', $input);
		$this->assign('product', $product);
		$this->assign('customer', $customer);
		$this->assign('list', $list);
		$this->assign('total', $total);
		$this->assign('page', $page->show());
		$this->display();
	}
	
	public function expendList()
	{
		$model = new CustomerExpendModel();
		
		//获取查询参数
		$params['min_money']   = I('get.min_money');
		$params['max_money']   = I('get.max_money');
		$params['start_month'] = I('get.start_month', date('Y-m', strtotime('first day of last month')));
		$params['end_month']   = I('get.end_month', date('Y-m', strtotime('first day of last month')));
		$params['customer_id'] = I('get.customer_id');
		$params['type']        = I('get.type');
		$params['start_date'] = I('get.start_date', date('Y-m-d', strtotime('last month')));
		$params['end_date'] = I('get.end_date', date('Y-m-d'));
		
		//客户
		$customerId = $model->field('customer_id')
							->select();
		$customerId = array_unique(array_column($customerId, 'customer_id'));
		//所有存在特殊费用的客户
		$customerModel  = new CustomerModel();
		$customerData   = $customerModel->field('customer_id, name')
										->where(['customer_id' => ['in', $customerId]])
										->select();
		$customerData   = array_map(function ($item) {
			return "[{$item['customer_id']}]{$item['name']}";
		}, array_column($customerData, null, 'customer_id'));
		$customerOption = makeOption($customerData, $params['customer_id']);
		
		
		$where = [];
		if (!empty($params['min_money']) && !empty($params['max_money'])) {
			$min_money      = floatval($params['min_money']);
			$max_money      = floatval($params['max_money']);
			$min_money      = min($min_money, $max_money);
			$max_money      = max($min_money, $max_money);
			$where['money'] = ['between', array_values(compact('min_money', 'max_money'))];
		} else if (!empty($params['min_money'])) {
			$where['money'] = ['egt', floatval($params['min_money'])];
		} else if (!empty($params['max_money'])) {
			$where['money'] = ['elt', floatval($params['max_money'])];
		}
		
		if (!empty($params['start_month']) && !empty($params['end_month'])) {
			$start_month         = implode('', explode('-', $params['start_month']));
			$end_month           = implode('', explode('-', $params['end_month']));
			$start_month         = min($start_month, $end_month);
			$end_month           = max($start_month, $end_month);
			$where['start_date'] = ['between', array_values(compact('start_month', 'end_month'))];
		} else if (!empty($params['start_month'])) {
			$where['start_date'] = ['egt', implode('', explode('-', $params['start_month']))];
		} else if (!empty($params['end_month'])) {
			$where['start_date'] = ['elt', implode('', explode('-', $params['end_month']))];
		}
		
		if (!empty($params['customer_id'])) {
			$where['customer.customer_id'] = ['eq', $params['customer_id']];
		}
		
		if (!empty($params['type'])){
			$where['customer_expend.type'] = ['eq', $params['type']];
		}

		if (!empty($params['start_date']) && !empty($params['end_date'])) {
		    $start_date = strtotime($params['start_date']);
		    $end_date = strtotime($params['end_date']) + 86400;
            $where['update_time'] = ['between', array_values(compact('start_date', 'end_date'))];
        } else if (!empty($params['start_date'])) {
		    $where['update_time'] = ['egt', strtotime($params['start_date'])];
        } else if (!empty($params['end_date'])) {
		    $where['update_time'] = ['lt', strtotime($params['end_date']) + 86400];
        }
		
		$temp_where = $where;
		if (array_key_exists('customer.customer_id', $temp_where)) {
			$temp_where['customer_id'] = $temp_where['customer.customer_id'];
			unset($temp_where['customer.customer_id']);
		}
		$total = $model->field('sum(`money`) as sum, count(1) as count')
					   ->where($temp_where)
					   ->find();
		
		$page = new Page($total['count'], 15);
		$list = $model->where($where)
					  ->field('customer.name as customer_name,customer_expend.*,customer_expend.type')
					  ->order('start_date desc, money desc')
					  ->join('left join customer ON customer.customer_id = customer_expend.customer_id')
					  ->limit($page->firstRow, $page->listRows)
					  ->select();

		$this->assign('customer_option', $customerOption);
		$this->assign('list', $list);
		$this->assign('params', $params);
		$this->assign('page', $page);
		$this->assign('total', $total);
		$this->display();
		
	}
}