<?php

namespace Home\Controller;

use Common\Controller\AdminController;

class TelTagBatchController extends AdminController
{
    public function index()
    {
        if (IS_GET) {
            $tag_list = D('DictItag')
                ->where("status=1")
                ->order('id')
                ->select();

            $this->assign('tag_list', $tag_list);
            $this->display();
        } else {
            try {

                ini_set('max_execution_time', 0);
                set_time_limit(0);
                ini_set("memory_limit", "1024M");

                $up_status = I('post.up_status', '0', 'intval');
                $itag_id = I('post.itag_id', '', 'intval');
                $tmp_name = $_FILES['tel_list']['tmp_name'];

                // check tel itag_id
                if (!$tmp_name || !file_exists($tmp_name)) {
                    throw new \Exception('请上传电话文件');
                }

                if (!is_numeric($itag_id)) {
                    throw new \Exception('请选择有效的金融标签');
                }

                $file_name = $_FILES['tel_list']['name'];
                $file_name = explode('.',$file_name);
                if (in_array($itag_id, [13, 14]) && !in_array($file_name[1], ['xlsx', 'xls'])) {
                    throw new \Exception("请按照规定的模版进行上传");
                } else if (!in_array($itag_id, [13, 14]) && $file_name[1] != 'txt') {
                    throw new \Exception("请按照规定的模版进行上传");
                }

                $dict_itag_tel_obj = D('DictItagtel');

                //标记号码净增数量
                $dict_itag_total_num = $dict_itag_tel_obj->where(['itag_num'=>['gt', 0]])->count();

                // gen sql
                $sql_param = [
                    'up_status' => $up_status,
                    'itag_id' => $itag_id,
                    'file_name' => $tmp_name
                ];
                $sql_param['source'] = 'new';
                $sql_new = $dict_itag_tel_obj->filterDataAndGenSql($sql_param);

                // gen sql without tel_md5 filed
//                $sql_param['source'] = 'old';
//                $sql_old = D('DictItagtel')->filterDataAndGenSql($sql_param);

//                if (!$sql_new || !$sql_old) {
                if (!$sql_new) {
                    throw new \Exception('请检查文件，没有可以更新的内容');
                }

                // insert into new database
                $result_new= $dict_itag_tel_obj->execute($sql_new);

                if ($result_new === false) {
                    throw new \Exception('上传数据失败, 请刷新后重试');
                }

                // insert into old database
//                $result_old = D('DictItagtelOld')->execute($sql_old);

                // log
                $file_name = $file_name[0];

                // record new database info
                if ($result_new !== false) {
                    $this->logSql($sql_new, $file_name,'new');
                }

                // dict_itagtel_log
                //标记号码净增数量
                $dict_itag_exec_num = $dict_itag_tel_obj->where(['itag_num'=>['gt', 0]])->count();
                $params = [
                    'itag_id'=>$itag_id,
                    'up_status'=>$up_status,
                    'tel_num'=>$dict_itag_tel_obj->insertValidTelNum,
                    'handle_person'=>$this->loginuser['username'],
                    'inc_num'=>$dict_itag_exec_num-$dict_itag_total_num
                ];
                D('DictItagtelLog')->addDictItagtelLogInfo($params);

                // record old database info
//                if ($result_old !== false) {
//                    $this->logSql($sql_old, $file_name, 'old');
//                } else {
//                    throw new \Exception('上传数据到老服务器失败, 请联系开发人员');
//                }

            } catch (\Exception $e){

                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功');
        }

    }

    public function tagList()
    {
        if (IS_GET) {

            // itag list show
            $itag_list = D('DictItag')
                ->where('status=1')
                ->select();

            $this->assign('itag_list', $itag_list);
            $this->display();
        } else {

            // init param
            $input = I('post.');

            // stat by itag_id
            $where = [
                'itag_num' => [
                    'neq', 0
                ],
                'itag_id' => $input['itag_id']
            ];

            $itag_stat = D('DictItagtel')
                ->where($where)
                ->count();

            // show itag name
            $itag_name = D('DictItag')
                ->field('itag')
                ->where("id=" . $input['itag_id'])
                ->find();
            $input['itag'] = $itag_name['itag'];

            // itag list show
            $itag_list = D('DictItag')
                ->where('status=1')
                ->select();

            $this->assign('itag_list', $itag_list);
            $this->assign('input', $input);
            $this->assign('itag_stat', $itag_stat);
            $this->display();
        }

    }

    protected function logSql($sql, $tmp_name, $extend)
    {

        // create sql folder  if it dose not exist
        $date = date('Ymd');
        $dir_name = RUNTIME_PATH . DIRECTORY_SEPARATOR . 'sql' . DIRECTORY_SEPARATOR . $date;
        if (!file_exists($dir_name) || !is_dir($dir_name)) {
            mkdir($dir_name, 0777, true);
        }

        // create backup file
        $login_user = $this->loginuser['username'];
        $file_name = $dir_name . DIRECTORY_SEPARATOR . $tmp_name . '_' . $extend . '_' . time();
        if (file_exists($file_name)) {
            $version = mt_rand();
            $file_name .= '_' . $version .'版本';
        }

        $file_name .= '.log';

        // divisive sql str by ';'
        $sql = explode(';', $sql);

        // gen file
        file_put_contents($file_name, 'user_name : ' . $login_user);
        foreach ($sql as $sql_one_time) {
            file_put_contents($file_name, PHP_EOL . var_export($sql_one_time, true), 8);
        }

    }
}
