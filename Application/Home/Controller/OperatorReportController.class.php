<?php

namespace Home\Controller;

use Account\Model\AccountModel;
use Common\Controller\AdminController;
use Home\Repositories\OperatorReportRepository;

/*
 * 日志管理->运营商报告查询
 */
class OperatorReportController extends AdminController
{
    private $operatorReport_repository;
    /**
     * 初始化函数，实例化本类依赖的资源类
     * <AUTHOR>
     * @datetime 10:20 2018/12/12
     **/
    public function __construct()
    {
        parent::__construct();
        $this->operatorReport_repository = new OperatorReportRepository();
    }
    /**
     * 首页（列表页）
     * <AUTHOR>
     * @datetime 10:19 2018/12/12
     **/
    public function index()
    {
        //获取列表数据
        $data = $this->operatorReport_repository->getListData();

        //获取所有账号数据
        $accountData = (new AccountModel())->getAccountListByWhere([], 'account_name, apikey');
        $accountData = array_column($accountData, 'account_name', 'apikey');
        //获取当前查询的时间区间
        $this->assign('time', $this->operatorReport_repository->getTimeAreaByGet());
        //获取爬取状态不同情况
        $this->assign('recordStatus', $this->operatorReport_repository->recordStatus);
        //获取生成PDF状态的不同情况
        $this->assign('pdfStatus', $this->operatorReport_repository->pdfStatus);
        $this->assign('data', $data);
        $this->assign('accountData', $accountData);
        $this->display();
    }
    /**
     * 查看详细
     * <AUTHOR>
     * @datetime 9:50 2018/12/13
     **/
    public function look()
    {
        //对接接口获取详细数据
        $result = $this->operatorReport_repository->getViewUrl();
        $this->ajaxReturn($result);
    }
    /**
     * 下载报告
     * <AUTHOR>
     * @datetime 10:36 2018/12/13
     **/
    public function download()
    {
        $result = $this->operatorReport_repository->getDownloadUrl();
        $this->ajaxReturn($result);
    }
}