<?php

namespace Home\Controller;

use Common\Common\FlushOldCrawlerToken;
use Common\Controller\AdminController;
use Common\Model\AuxiliaryServices;
use Common\Model\CuishouUserModel;
use Common\Model\FeeCrawlerConfigModel;
use Common\ORG\Page;
use Home\Repositories\BmCrawlerRepository;
use Home\Repositories\FinanceAccountRepository;
use Common\Common\ResponseTrait;

class BmCrawlerController extends AdminController
{
    use ResponseTrait, FlushOldCrawlerToken;
    private static $relation_ships = [
        '配偶',
        '父母',
        '兄弟姐妹',
        '子女',
        '亲戚',
        '同事',
        '朋友',
        '同学',
        '其他'];

    protected $config_info_shenqing = [
        '姓名', '身份证号', '手机号码', '所在地区', '居住地址'
    ];

    protected $config_default_shenqing = [
//        '姓名', '身份证号', '手机号码',
    ];

    private $repository_bm_crawler;

    public function _initialize()
    {
        parent::_initialize();

        $this->repository_bm_crawler = new BmCrawlerRepository();
    }

    public function index()
    {
        //  search condition from client
        $check_query = I('get.');

        // init params
        $status = I('get.status', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $id = I('get.id', '', 'trim');
        $appid = I('get.appid', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        // filter status, user, contract_status
        $where = $status ? ['status' => $status] : [];
        $where = $appid ? (['appid' => $appid] + $where) : $where;
        $where = $contract_status ? (['contract_status' => $contract_status] + $where) : $where;
        if ($begin && $begin_e) {
            $where['created_at'] = ['between', [strtotime($begin), strtotime($begin_e . ' 23:59:59')]];
        } elseif ($begin) {
            $where['created_at'] = ['egt', strtotime($begin)];
        } elseif ($begin_e) {
            $where['created_at'] = ['elt', strtotime($begin_e . ' 23:59:59')];
        }
        if ($end && $end_e) {
            $where['expiration_date'] = ['between', [strtotime($end), strtotime($end_e . ' 23:59:59')]];
        } elseif ($end) {
            $where['expiration_date'] = ['egt', strtotime($end)];
        } elseif ($end_e) {
            $where['expiration_date'] = ['elt', strtotime($end_e . ' 23:59:59')];
        }

        // 如果输入的用户id不同那么获取不到数据值
        if ($choose_id && $id && $choose_id != $id) {
            $where['id'] = 'what happened';
        } elseif ($choose_id) {
            $where['id'] = $choose_id;
        } elseif ($id) {
            $where['id'] = $id;
        }

        // set page
        if (!$choose_id && !$id && !$appid) {
            $account_number = D('Auth')
                ->where($where)
                ->count();
        } else {
            $account_number = 1;
        }
        $page_obj = new Page($account_number, C('LIST_ROWS'));

        // account list
        $account_list = D('Auth')
            ->where($where)
            ->order('id desc')
            ->limit($page_obj->firstRow, $page_obj->listRows)
            ->select();

        //查询账号关联所属用户信息
        $account_product_infos = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($account_list, 'id'), 1);
        foreach ($account_list as $k => $item) {
            if ($account_product_infos[$item['id']]) {
                $account_list[$k]['account_product'] = $account_product_infos[$item['id']];
            }
        }

        // all user
        $user_list = D('Auth')
            ->index('id')
            ->field('id, developer')
            ->order('id desc')
            ->select();

        // user info which is choosed
        if ($choose_id) {
            $check_query['developer'] = $user_list[$choose_id]['developer'];
        }

        $this->assign('account_list', $account_list);
        $this->assign('check_query', $check_query);
        $this->assign('page', $page_obj->show());
        $this->assign('user_list', $user_list);
        $this->assign('contract_status', (new CuishouUserModel())->getContractStatus());
        $this->display();
    }

    public function addAccount()
    {
        if (IS_GET) {
            // GET方式获取用户列表客户(select2使用)
            $repository_account = new FinanceAccountRepository();
            $list_account_for_select2 = $repository_account->accountListForGet();

            $this->assign('list_account_for_select2', $list_account_for_select2);
            $this->assign('relation_ships', self::$relation_ships);
            $this->assign('preview_url', C('PREVIEW_URL'));
            $this->display();
            exit();
        }
        try {
            // init params
            $data = I('post.');
            $account_name = $data['name'];
            unset($data['name']);
            $created_at = time();

            // filter params
            $data = D('Auth')->filterDataNew($data);
            $data['created_at'] = $created_at;

            // add account
            $account_result = D('Auth')->addAccount($data);

            //绑定所属客户
            if (!empty($account_name)) {
                D('FinanceAccountProduct')->bindToAccounts($account_result, $account_name, 1);
            }

            // check cuishou service
            if ($data['status'] == 1 && $data['need_dunning'] == 1) {

                // tidy service params
                $service_data['uid'] = $account_result;
                $service_data['service_cateid'] = 2;
                $service_data['service_key'] = isset($data['service_key']) ? $data['service_key'] : '';
                $service_data['service_secret'] = isset($data['service_secret']) ? $data['service_secret'] : '';
                $service_data['created_at'] = $created_at;

                // filter service params
                (new AuxiliaryServices())->filterParam($service_data);

                // add cuishou service
                $services_result = D('AuxiliaryServices')->add($service_data);

                if (!$services_result) {
                    throw new \Exception('添加催收分参数失败，请在编辑模式添加');
                }
            }

            // 添加默认的API配置项
            $this->repository_bm_crawler->initApiConfigForAccount($account_result, $data);

        } catch (\Exception $e) {

            $this->__Return($e->getMessage());
        }

        $this->__Return('账户添加成功', '', 'tip_success');
    }

    public function setAccount()
    {
        if (IS_GET) {
            try {
                // check id
                $id = I('get.id', '', 'intval');

                if (!$id) {
                    throw new \Exception('请刷新页面后, 再次尝试编辑');
                }

                // account info
                $account_info = D('Auth')
                    ->where("id=$id")
                    ->find();

                // service info
                $service_info = [];
                if ($account_info['need_dunning'] == 1) {

                    $where = ['uid' => $id, 'service_cateid' => 2];
                    $service_info = D('AuxiliaryServices')
                        ->where($where)
                        ->find();
                }

                // GET方式获取用户列表客户(select2使用)
                $repository_account = new FinanceAccountRepository();
                $list_account_for_select2 = $repository_account->accountListForGet();
                //查询产品关联的用户信息
                $account_product_info = D('FinanceAccountProduct')->getAccountIdByProductId($id, 1);

                $this->assign('list_account_for_select2', $list_account_for_select2);
                $this->assign('account_product_info', $account_product_info);
                $this->assign('relation_ships', self::$relation_ships);
                $this->assign('account_info', $account_info);
                $this->assign('service_info', $service_info);
                $this->assign('preview_url', C('PREVIEW_URL'));

                $this->display();
                exit();
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }

        }

        try {
            // check id
            $id = I('get.id', '', 'intval');

            if (!$id) {
                throw new \Exception('请刷新页面后, 再次尝试编辑');
            }

            // init params
            $data = I('post.');
            $data['id'] = $id;
            $data['updated_at'] = time();
            $account_name = $data['name'];
            unset($data['name']);

            // filter params
            $data = D('Auth')->filterDataNew($data);


            // update account
            D('Auth')->setAccount($data);

            // save account product
            D('FinanceAccountProduct')->updateBindToAccounts($id, $account_name, 1);

            // check cuishou service
            if ($data['status'] == 1 && $data['need_dunning'] == 1) {

                // tidy service params
                $service_data['uid'] = $id;
                $service_data['service_cateid'] = 2;
                $service_data['service_key'] = isset($data['service_key']) ? $data['service_key'] : '';
                $service_data['service_secret'] = isset($data['service_secret']) ? $data['service_secret'] : '';

                // filter service params
                (new AuxiliaryServices())->filterParam($service_data);

                // update cuishou service
                $services_result = (new AuxiliaryServices())->updateParam($service_data);

                if ($services_result === false) {
                    throw new \Exception('添加催收分参数失败，请在编辑模式添加');
                }
            }

            // 刷新token
            $this->flushToken(compact('id'));

        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }

        $this->__Return('账号更新成功', '', 'tip_success');
    }

    public function h5Config()
    {
        $product_info = $this->repository_bm_crawler->h5Config();
        $this->assign($product_info);
        $this->display();
        exit();
    }

    /**
     * API 配置
     */
    public function apiConfig()
    {
        try {
            if (IS_GET) {
                $api_config_info = $this->repository_bm_crawler->getApiConfig();
                $id = I('get.id', '', 'trim');

                $this->assign('id', $id);
                $this->assign('api_config_info', $api_config_info['api_config_info']);
                $this->display();
                exit();
            }

        } catch (\Exception $e) {
            exit(json_encode(['msg' => $e->getMessage()]));

        }

        // 更新配置
        try {
            $this->repository_bm_crawler->updateApiConfig();

            $response = [
                'status' => 0,
                'msg' => '更新成功'
            ];

        } catch (\Exception $e) {
            $response = [
                'status' => 9999,
                'msg' => '更新失败，请稍后再试',
                'error' => $e->getMessage()
            ];
        }
        $this->ajaxResponse($response);
    }

    /**
     * 邦秒爬计费配置
     * @throws \Exception
     */
    public function configFee()
    {
        $crawlerFeeConfig = new FeeCrawlerConfigModel();
        if (IS_POST) {
            $post = $input = I('post.');
            $crawlerFeeConfig->startTrans();
            try {
                $data = $this->checkParamsForPost($post);
                if ($data['id']) {
                    $info = $crawlerFeeConfig->find($data['id']);
                    try {
                        $params_delete = ['is_delete' => 1];
                        $result_delete = $crawlerFeeConfig->where('product_id=' . $info['product_id'])->save($params_delete);
                        if ($result_delete === false) {
                            throw new \Exception('操作失败，请刷新后再试');
                        }
                    } catch (\Exception $e) {
                        throw new \Exception($e->getMessage());
                    }
                    unset($data['id']);
                }
                $data['create_time'] = time();
                $data['is_delete'] = 0;
                if (!$crawlerFeeConfig->add($data)) {
                    // 回滚错误
                    $crawlerFeeConfig->rollback();
                    throw new \Exception('操作失败');
                } else {
                    $crawlerFeeConfig->commit();
                }

                $this->__Return('操作成功', '', 'tip_success');
            } catch (\Exception $e) {
                $this->__Return('操作失败', $e->getMessage(), 'tip_error');
            }
            exit;
        } else {
            $list = [];
            $product_id = I('get.id', 0, 'intval');
            if ($product_id) {
                $list = D('Auth')
                    ->where(['id' => $product_id])
                    ->find();
                $info = $crawlerFeeConfig
                    ->where(['product_id' => $product_id, 'is_delete' => 0])
                    ->order('id DESC')
                    ->find();
            } else {
                $this->__Return('缺少必要参数', '', 'tip_error');
            }
            if ($info && $info['fee_amount_rule'] == 2) {
                $info['fee_price'] = json_decode($info['fee_price'], true);
            }
            $this->assign('list', $list);
            $this->assign('info', $info);
            $this->assign('feeBasis', $crawlerFeeConfig->getFeeBasis());//计费依据
            $this->assign('feeMethod', $crawlerFeeConfig->getFeeMethod());//计费方式
            $this->assign('timeRule', $crawlerFeeConfig->getFeeTimeRule());//计费时间规则
            $this->assign('amountRule', $crawlerFeeConfig->getFeeAmountRule());//用量计费规则
            $this->assign('stepRule', $crawlerFeeConfig->getFeeStepRule());//阶梯计费规则
            $this->display();
        }
    }


    /**
     * 检查更新客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsForPost($params)
    {
        if (!$params['product_id'] || !$params['fee_basis'] || !$params['fee_method'] || !$params['start_date']) {
            throw new \Exception('缺少必要参数');
        }
        if ($params['fee_method'] == 1) {
            if (!$params['fee_time_rule']) {
                throw new \Exception('请选择时间计费规则');
            } else {
                if (!$params['fee_time_price']) {
                    throw new \Exception('请填入时间计费价格');
                } else {
                    unset($params['fee_amount_rule']);
                    $params['fee_price'] = $params['fee_time_price'];
                }
            }
        } elseif ($params['fee_method'] == 2) {
            if (!$params['fee_amount_rule']) {
                throw new \Exception('请选择用量计费规则');
            } else {
                if ($params['fee_amount_rule'] == 1) {
                    if (!$params['fee_amount_price']) {
                        throw new \Exception('请填入固定用量计费价格');
                    } else {
                        unset($params['fee_time_rule']);
                        $params['fee_price'] = $params['fee_amount_price'];
                    }
                } elseif ($params['fee_amount_rule'] == 2) {
                    if (!$params['fee_step_price']) {
                        throw new \Exception('请填入阶梯用量计费价格');
                    } else {
                        unset($params['fee_time_rule']);
                        if ($params['fee_step_price']) {
                            $step_price_one = explode('|', $params['fee_step_price']);
                            foreach ($step_price_one as $key => $value) {
                                $step_price_two = explode(':', $value);
                                $new_arr[$key] = $step_price_two;
                            }
                        }
                        $params['fee_price'] = json_encode($new_arr);
                    }
                }
            }
        } else {
            throw new \Exception('错误的计费方式');
        }
        unset($params['fee_time_price']);
        unset($params['fee_amount_price']);
        unset($params['fee_step_price']);
        return $params;
    }
}