<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmCrawlerRepository;
use Home\Repositories\BmCrawlerFileRepository;

class BmCrawlerFileController extends AdminController
{
    //  统计的repository
    protected $stat_repository;

    // 生成文件的repository
    protected $stat_file_repository;

    public function __construct()
    {
        parent::__construct();
        $this->stat_repository = (new BmCrawlerRepository());
        $this->stat_file_repository = (new BmCrawlerFileRepository());
        set_time_limit(600);
    }

    public function index()
    {
        // 获取统计信息
        $stat_list = $this->listNoCache();
        $file_name = RUNTIME_PATH . 'Cache/bmcrawlerfile_list.csv';

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->stat_file_repository->genTempFileListForRequest($stat_list, $file_name);

        //为fileDownload插件生成文件
        $this->stat_file_repository->genFileForFileDownload($file_name);
        exit();
    }

    /**
     *  获取列表数据
     * @return array
     */
    protected function listNoCache()
    {
        // 选择所有客户
        return $this->chooseAllForList();
    }

    /**
     * 列表页选择所有用户的数据统计
     * @return array
     */
    protected function chooseAllForList()
    {
        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // 所有用户的当前条件下的统计信息 && 分页
        return $this->stat_repository->geBmCrawlerForList($where_date);
    }
}
