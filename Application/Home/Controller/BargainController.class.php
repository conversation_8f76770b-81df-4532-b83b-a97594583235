<?php

namespace Home\Controller;


use Common\Controller\AdminController;
use Home\Repositories\BargainRepository;

class BargainController extends AdminController
{
    protected $repository;
    public function __construct()
    {
        $this->repository = new BargainRepository();
        parent::__construct();

    }
    public function index()
    {
        $this->assign($this->repository->index());
        $this->display();
    }

    public function add()
    {
        if (IS_POST) {
            //监听切换客户事件
            $this->repository->listenCutCustomer();

            $this->repository->run_add();
            return;
        }
        $this->assign($this->repository->add());
        $this->display();
    }

    public function detail()
    {
        try {
            $data = $this->repository->detail();
            $this->assign($data);
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }

    public function edit()
    {
        if (IS_POST) {
            //监听切换客户事件
            $this->repository->listenCutCustomer();

            $this->repository->run_edit();
            return;
        }
        try {
            $data = $this->repository->edit();
            $this->assign($data);
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }

    public function del()
    {
        try {
            $this->repository->del();
            $this->success('删除成功', U('index'));
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }

    }
}