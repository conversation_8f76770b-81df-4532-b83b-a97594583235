<?php

namespace Home\Controller;

use Common\Common\CurlTrait;
use Common\Controller\AdminController;


class YujinglistController extends AdminController
{
	use CurlTrait;
	private $repository;
	
	private $allShow = [
		'chang.liu',
		'yanming.li',
		'xiaogang.cui'
	];
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = D('Alarmlog');
	}
	
	/**
	 * 项目组模块
	 */
	
	public function index()
	{
		$input               = [];
		$where               = ['haveo' => 0];
		$input['start_time'] = I('get.start_time', '', 'trim');
		$input['end_time']   = I('get.end_time', '', 'trim');
        $input['level']   = I('get.level', '', 'trim');
        $input['haveo']   = I('get.haveo', '', 'trim');
		if (!empty($input['start_time'])) {
			$where['ctime'][] = ['egt', $input['start_time'] . ' 00:00:00'];
		}
		if (!empty($input['end_time'])) {
			$where['ctime'][] = ['elt', $input['end_time'] . ' 23:59:59'];
		}
        if(!empty($input['level'])){
            $where['level'] = $input['level'];
        }
        if(!empty($input['haveo'])){
            $where['haveo'] = $input['haveo'];
        }
		
		if (!in_array($this->loginuser['username'], $this->allShow)) {
			$where['cname'] = $this->loginuser['username'];
		}
		
		$count = $this->repository->where($where)
								  ->count();
		$Page  = new \Common\ORG\Page($count, 300);
		$data  = $this->repository->where($where)
								  ->limit($Page->firstRow . ',' . $Page->listRows)
								  ->order('id DESC')
								  ->select();
		
		$this->assign(['input' => $input]);
		$this->assign(['data' => $data]);
		$this->display();
	}
	
	public function chuli()
	{
		$data     = I('post.');
		$alram_id = $data['alram_id'];
		$arr      = [
			'otype' => $data['otype'],
			'note'  => $data['note'],
			'haveo' => 1,
			'otime' => time(),
			'oname' => $this->loginuser['username'],
		];
		
		$res = $this->repository->where(['id' => $alram_id])
								->save($arr);
		
		$key = $this->repository->where(['id' => $alram_id])
								->find();
		if (!empty($key) && !empty($key['key'])) {
			$this->post(C('BACK_API_DOMAIN') . '/setMonitorTtl', [
				'key'   => $key['key'],
				'otype' => $data['otype'],
			]);
		}


		if ($res) {
			$result = ['status' => 'ok', 'msg' => '处理成功'];
			$this->ajaxReturn($result);
		}
		$result = ['status' => 'error', 'msg' => '处理失败'];
		$this->ajaxReturn($result);
	}

    public function piliang()
    {
        $content = file_get_contents('php://input');
        if (!is_string($content)) {
            $result = ['status' => 'error', 'msg' => '参数类型错误'];
            $this->ajaxReturn($result);
        }
        $arr = json_decode($content, true);
        if (empty($arr)) {
            $result = ['status' => 'error', 'msg' => '分配数据为空'];
            $this->ajaxReturn($result);
        }

        $map = [];
        $map['id'] = ['in', $arr['arr']];
        $res = $this->repository->where($map)->save(['haveo'=>1,'otype' => 6, 'oname' => $this->loginuser['username']]);
        if($res) {
            $result = ['status' => 'ok', 'msg' => '处理成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '处理失败'];
        $this->ajaxReturn($result);
    }
}


