<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\UploadRepository;
use Home\Repositories\BillNotesRepository;


class UploadController extends AdminController
{
    private $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new UploadRepository();
    }

    //列表页
    public function index()
    {
        $data = $this->repository->getListData();
        $this->assign($data);
        $this->display();
    }

    //增加
    public function add()
    {
        $info = $this->repository->file_in($this->loginuser['username']);
        if($info){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }
    /**
     * 增加账单备注
     */
    public function billNotes()
    {
        $repository = new BillNotesRepository();
        $data = $repository->getListData();
        $customer_info = $repository->getCustomerInfo();

        $this->assign(compact('customer_info'));
        $this->assign($data);
        $this->display();
    }
    /**
     * 添加账单备注
     */
    public function addBillNotes()
    {
        $repository = new BillNotesRepository();
        $info = $repository->addBillNotes();
        $this->ajaxReturn($info);
    }
    /**
     * 编辑账单备注
     */
    public function editBillNotes()
    {
        $repository = new BillNotesRepository();
        $info = $repository->editBillNotes();
        $this->ajaxReturn($info);
    }

    /**
     * 获取编辑信息
     */
    public function getEditBillNotes()
    {
        $repository = new BillNotesRepository();
        $info = $repository->getEditBillNotes();
        $this->ajaxReturn($info);
    }

}