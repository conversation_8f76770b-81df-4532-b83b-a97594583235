<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-19 11:37:48
 */

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\RiskListFeeStatRepository;
use Common\ORG\Page;

class FeeRiskListStatController extends AdminController
{
    protected $riskListFeeStat;

    public function _initialize()
    {
        parent::_initialize();
        $this->riskListFeeStat = new RiskListFeeStatRepository();
    }

    //日对账单
    public function feeDay()
    {
        $input = I('get.');

        $list = $this->riskListFeeStat->getRiskListFeeStatDay();
        $count = count($list['list']);
        $total_data = $list['total_data'];
        //分页
        $page = new Page($count, C('LIST_ROWS'));
        $list = array_slice($list['list'], $page->firstRow, $page->listRows);

        $this->assign('input', $input);
        $this->assign('list', $list);
        $this->assign('total_data', $total_data);
        $this->assign('page', $page->show());
        $this->display();
    }

    //导出
    public function download()
    {
        $list = $this->riskListFeeStat->getRiskListFeeStatDay();
        $this->riskListFeeStat->getRistStatFeeStatDownload($list);
        exit;
    }
}
