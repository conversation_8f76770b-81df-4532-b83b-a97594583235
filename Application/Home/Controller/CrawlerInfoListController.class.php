<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CrawlerInfoListRepository;

class CrawlerInfoListController extends AdminController
{
    protected $crawler;

    public function _initialize()
    {
        parent::_initialize();
        $this->crawler = new CrawlerInfoListRepository();
    }

    public function index()
    {
        if (IS_GET) {
            $this->display();
            exit();
        }

        $sid = I('post.sid', '', 'trim');
        $type = I('post.collection', '', 'trim');
        $input = I('post.');
        $request_type = I('post.request_type', '', 'trim');
        try {
            if (!$sid || !$type) {
                throw new \Exception("参数错误");
            }

            $msg = $this->crawler->getCrawlerInfoApi(compact('sid','type'));
            if (!$msg) {
                throw new \Exception('您查询的Collection ' . $type . ' 使用条件 Sid: ' . $sid . ' 查询结果为空，请检查查询条件');
            }

            if ($request_type == 'file_down') {
                $this->crawler->getCrawlerInfoDownload($msg, $type, $sid);
                exit;
            }

            foreach ($msg as $key => $value) {
                $msg[$key] = json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            }

            $this->assign('input', $input);
            $this->assign('msg', $msg);
            $this->display();
        } catch (\Exception $e) {
            $this->assign('msg', [$e->getMessage()]);
            $this->assign('input', $input);
            $this->display();
        }
    }
}