<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CuishouShortStatRepository;

class CuishouShortStatController extends AdminController
{
    protected $repository_short;

    public function __construct()
    {
        parent::__construct();
        $this->repository_short = new CuishouShortStatRepository();
    }

    /**
     * 统计列表 view
     */
    public function index()
    {
        $ini_params = $this->repository_short->index();
        $this->assign(compact('ini_params'));
        $this->display();
    }

    /**
     * 统计详情
     */
    public function detail()
    {
        // 初始化参数
        $ini_params = $this->repository_short->genIniParamsForDetail();
        $ini_params = json_encode($ini_params, JSON_UNESCAPED_UNICODE);

        $this->assign(compact('ini_params'));
        $this->display();
    }
}