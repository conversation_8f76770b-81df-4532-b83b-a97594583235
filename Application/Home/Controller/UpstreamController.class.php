<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\UpstreamRepository;
use Home\Model\UpstreamChannelPriceModel;
use Account\Model\ProductModel;
use Home\Model\UpstreamChannelModel;
use Common\ORG\Page;

class UpstreamController extends AdminController
{
	private $repository;
	private $upstream_channel_price_model;
	private $product_model;
	private $upstream_channel_model;
	
	/**
	 * UpstreamController constructor.
	 */
	public function __construct()
	{
		parent::__construct();
		$this->repository                   = new UpstreamRepository();
		$this->upstream_channel_price_model = new UpstreamChannelPriceModel();
		$this->product_model                = new ProductModel();
		$this->upstream_channel_model       = new UpstreamChannelModel();
	}
	
	/**
	 * 邦信分快捷版上游数据统计
	 */
	public function shortcuts()
	{
		// 初始化的变量
		$params = $this->repository->shortcuts();
		$this->assign($params);
		$this->display('shortcut-list');
	}
	
	/**
	 * 邦信分快捷版上游数据统计统计详情
	 */
	public function shortcutDetails()
	{
		// 初始化的变量
		$params = $this->repository->shortcutDetails();
		$this->assign($params);
		$this->display('shortcut-detail');
	}
	
	/**
	 * 上游邦妙验统计列表
	 */
	public function verifications()
	{
		// 初始化的变量
		$params = $this->repository->verifications();
		$this->assign($params);
		$this->display('verification-list');
	}
	
	/**
	 * 上游邦妙验统计详情
	 */
	public function verificationDetails()
	{
		// 初始化的变量
		$params = $this->repository->verificationDetails();
		$this->assign($params);
		$this->display('verification-detail');
	}
	
	/**
	 * 产品列表
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function product()
	{
		$this->display();
	}
	
	/**
	 * 渠道列表
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function upstream()
	{
		$data = $this->repository->getUpstreamList();
		$this->assign($data);
		$this->display();
	}
	
	/**
	 * 获取编辑信息
	 */
	public function getEdit()
	{
		$data = $this->repository->getUpstreamEdit();
		echo $data;
	}
	
	/**
	 * 设置编辑信息
	 */
	public function setEdit()
	{
		$data = $this->repository->setUpstreamEdit();
		if ($data) {
			$result = ['status' => 'ok', 'msg' => '更新成功'];
			$this->ajaxReturn($result);
		}
		$result = ['status' => 'error', 'msg' => '更新失败'];
		$this->ajaxReturn($result);
	}
	
	/**
	 * 计费配置
	 */
	public function priceConfig()
	{
		$dat  = I('get.');
		$data = $this->repository->priceConfig($dat);
		$this->assign(['input' => $dat]);
		$this->assign(['data' => $data['arr']]);
		$this->assign(['upstream_info' => $data['upstream_info']]);
		$this->display();
	}
	
	/**
	 * 计费配置设置
	 */
	public function setPriceConfig()
	{
		$admin = $this->loginuser['username'];
		$data  = $this->repository->setPriceConfig($admin);
		$this->ajaxReturn($data);
	}
	
	/**
	 * 获取价格配置
	 */
	public function getPriceEdit()
	{
		$data = $this->repository->getPriceEdit();
		$this->ajaxReturn($data);
		
	}
	
	/**
	 * 编辑渠道价格配置
	 */
	public function editPriceConfig()
	{
		$admin = $this->loginuser['username'];
		$data  = $this->repository->editPriceConfig($admin);
		$this->ajaxReturn($data);
		
	}
	
	/**
	 * 邦信分快捷版计费配置
	 */
	public function bxfShortConfig()
	{
		$product_id           = 210;
		$data                 = I('get.');
		$asc                  = $data['asc'];
		$order                = $data['order'];
		$input['start_time']  = $data['start_time'];
		$input['end_time']    = $data['end_time'];
		$input['channel_name_search']    = $data['channel_name_search'];
		$where                = ['product_id' => $product_id];
		$where['delete_time'] = 0;
		if (!empty($input['start_time'])) {
			$where['start_date'][] = ['EGT', $input['start_time']];
		}
		if (!empty($input['end_time'])) {
			$where['start_date'][] = ['ELT', $input['end_time']];
		}
        if (!empty($input['channel_name_search'])) {
            $where['upstream_channel_id'] = $input['channel_name_search'];
        }

		//如果有排序请求
		if (($asc == 0 || $asc == 1) && !empty($order)) {
			//升序为1,降序为0
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->select();
			if (!empty($channel_price_config_info)) {
				foreach ($channel_price_config_info as $key => &$value) {
					$price_config      = json_decode($value['price'], true);
					$value['all']      = isset($price_config['all']) ? $price_config['all'] : 0;
					$value['succ']     = isset($price_config['succ']) ? $price_config['succ'] : 0;
					$value['yd']       = isset($price_config['yd']) ? $price_config['yd'] : 0;
					$value['liantong'] = isset($price_config['lt']) ? $price_config['lt'] : 0;
					$value['dx']       = isset($price_config['dx']) ? $price_config['dx'] : 0;
				}
				//对数据进行排序
				$field = $order;
				$asc   = $asc ? SORT_ASC : SORT_DESC;
				array_multisort(array_map(function ($item) {
					return floatval($item);
				}, array_column($channel_price_config_info, $field)), $asc, $channel_price_config_info);
				
				//对数据进行分页
				$count     = count($channel_price_config_info);
				$list_rows = 30;
				if ($count > $list_rows) {
					$start                     = (I('get.page', 1, 'intval') - 1) * $list_rows;
					$page                      = (new Page($count, $list_rows))->show();
					$channel_price_config_info = array_slice($channel_price_config_info, $start, $list_rows);
				} else {
					$page = '';
				}
			}
		} else {
			$count = $this->upstream_channel_price_model->where($where)
														->count();
			//分页
			$listRow                   = 30;
			$page                      = new Page($count, $listRow);
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->order('id desc')
																			->limit($page->firstRow, $page->listRows)
																			->select();
			$page                      = $page->show();
		}
		
		$product_name = '邦信分快捷版';
		$field        = 'id, product_id, name, channel';
		$channel_info = $this->upstream_channel_model->where(['product_id' => $product_id])
													 ->field($field)
													 ->select();
		
		//通过channel确定其名称
		$channel_name_arr = array_column($channel_info, null, 'channel');
		
		$channel_price_list = [];
		array_walk($channel_price_config_info, function (&$val) use (&$channel_price_list, $channel_name_arr, $product_name) {
			$val['channel_name']   = isset($channel_name_arr[$val['channel']]['name']) ? $channel_name_arr[$val['channel']]['name'] : '';
			$json                  = json_decode($val['price'], JSON_UNESCAPED_UNICODE);
			$val['peizhi']['all']  = isset($json['all']) ? $json['all'] : '无';
			$val['peizhi']['succ'] = isset($json['succ']) ? $json['succ'] : '无';
			$val['peizhi']['yd']   = isset($json['yd']) ? $json['yd'] : '无';
			$val['peizhi']['lt']   = isset($json['lt']) ? $json['lt'] : '无';
			$val['peizhi']['dx']   = isset($json['dx']) ? $json['dx'] : '无';
			$val['product_name']   = $product_name;
			$channel_price_list[]  = $val;
		});
		
		
		$this->assign('channel_name_arr', $channel_name_arr);
		$this->assign('channel_price_list', $channel_price_list);
		$this->assign('page', $page);
		$this->assign('input', $input);
        $this->assign('channel_name_search', makeOption( array_column($channel_name_arr, 'name','id'), $input['channel_name_search']) );
		
		$this->display();
	}
	
	/**
	 * 添加邦信分配置
	 * 添加的时候公共方法
	 */
	public function setBxfShortConfig()
	{
		$admin      = $this->loginuser['username'];
		$data       = I('post.');
		$id         = $data['id'];//如果是邦秒验,这个id对应的是channle,别的产品对应的channle_id
		$start_date = $data['start_date'];
		$is_billing = $data['is_billing'];
		$type       = $data['type'];
		
		if ($type == 'bmy') {
			$find_one = $this->upstream_channel_model->where(['product_id' => $data['product_id'], 'channel' => $id])
													 ->find();
			if (empty($find_one)) {
				$result = ['status' => 'error', 'msg' => '对应的渠道不存在'];
				$this->ajaxReturn($result);
			}
			$id = $find_one['id'];
		}
		
		$now_time  = date('Y-m-d');
		$if_config = C('ALLOW_HISTORY_FEE_CONFIG');
		if (!$if_config) {
			if ($start_date < $now_time) {
				$result = ['status' => 'error', 'msg' => '计费日期不能小于当前日期'];
				$this->ajaxReturn($result);
			}
		}
		$where = ['product_id' => $data['product_id'], 'upstream_channel_id' => $id, 'start_date' => $start_date];
		
		$find_one = $this->upstream_channel_price_model->where($where)
													   ->find();
		if (!empty($find_one)) {
			$result = ['status' => 'error', 'msg' => '该渠道计费日期已配置'];
			$this->ajaxReturn($result);
		}
		$price_res = [];
		$key_arr   = [];
		array_walk($data['arr'], function ($val) use (&$price_res, &$key_arr) {
			$p_arr                = explode('#', $val);
			$key_arr[]            = $p_arr[0];
			$price_res[$p_arr[0]] = $p_arr[1];
		});
		if (count($key_arr) != count(array_unique($key_arr))) {
			$result = ['status' => 'error', 'msg' => '请检查配置项是否有重复字段'];
			$this->ajaxReturn($result);
		}
		//获取该渠道信息
		$channel_info = $this->upstream_channel_model->where(compact('id'))
													 ->find();
		if (empty($channel_info)) {
			$result = ['status' => 'error', 'msg' => '渠道数据不存在'];
			$this->ajaxReturn($result);
		}
		$res_data = [
			'upstream_channel_id' => $id,
			'product_id'          => $channel_info['product_id'],
			'channel'             => $channel_info['channel'],
			'price'               => json_encode($price_res, JSON_UNESCAPED_UNICODE),
			'start_date'          => $start_date,
			'admin'               => $admin,
			'create_time'         => time(),
			'is_billing'          => $is_billing,
		];
		$r        = $this->upstream_channel_price_model->add($res_data);
		if ($r) {
			$result = ['status' => 'ok', 'msg' => '添加成功'];
			$this->ajaxReturn($result);
		}
		$result = ['status' => 'error', 'msg' => '添加失败'];
		$this->ajaxReturn($result);
		
	}
	
	/**
	 * 获取邦信分快捷版配置
	 */
	public function getBxfShortConfig()
	{
		$data       = I('post.');
		$type       = $data['type'];
		$product_id = 210;
		if ($type == 'bqc') {
			$product_id = 401;
		}
		if ($type == 'haoma') {
			$product_id = 801;
		}
		
		$info      = $this->upstream_channel_price_model->where(['id' => $data['id']])
														->find();
		$json_text = json_decode($info['price'], true);
		$out_count = count($json_text);
		$all_arr   = ['all' => '查询量', 'succ' => '查得量', 'yd' => '移动查得量', 'lt' => '联通查得量', 'dx' => '电信查得量'];
		$string    = '';
		foreach ($json_text as $k => $v) {
			$string        .= '<div class="dialogue_form_child_html_edit" style="background-color: #FCFCFC;">';
			$str_intent    = '<div class="form-group"><label for="tips" class="col-sm-2 control-label">选项</label><div>';
			$str_intent    .= '<div class="col-sm-2 indent-html"><select class="intents_name" name="peizhi_edit[]" style="width: 120px;"><option value="">请选择</option>';
			$option_intent = '';
			foreach ($all_arr as $kk => $vv) {
				$selected = '';
				if ($k == $kk) {
					$selected = 'selected';
				}
				$option_intent .= '<option ' . $selected . ' value="' . $kk . '">' . $vv . '</option>';
			}
			$str_intent .= $option_intent . '</select></div></div><label class="col-sm-2 control-label">价格</label><div><div class="col-sm-2 indent-html"><input type="text" name="price_edit[]" value="' . $v . '" /></div></div></div></div>';
			$string     .= $str_intent;
		}
		
		$field        = 'id, product_id, name, channel';
		$channel_info = $this->upstream_channel_model->where(['product_id' => $product_id])
													 ->field($field)
													 ->select();
		$option       = '<option value="">请选择</option>';
		foreach ($channel_info as $key => $value) {
			if ($info['upstream_channel_id'] == $value['id']) {
				$option .= '<option selected value="' . $value['id'] . '">' . $value['name'] . '</option>';
			} else {
				$option .= '<option value="' . $value['id'] . '">' . $value['name'] . '</option>';
			}
		}
		$result = ['text' => $string, 'info' => $info, 'option' => $option];
		$this->ajaxReturn($result);
		
	}
	
	/**
	 * 编辑的时候公用方法
	 */
	
	public function editBxfShortConfig()
	{
		$admin = $this->loginuser['username'];
		
		$data = I('post.');
		$id   = $data['id'];
		$type = $data['type'];
		
		$start_date = $data['start_date'];
		$is_billing = $data['is_billing'];
		$where      = ['id' => $id];
		$find_one   = $this->upstream_channel_price_model->where($where)
														 ->find();
		if (empty($find_one)) {
			$result = ['status' => 'error', 'msg' => '该配置项不存在'];
			$this->ajaxReturn($result);
		}
		//如果日期不一样
		if ($start_date != $find_one['start_date']) {
			$if_config = C('ALLOW_HISTORY_FEE_CONFIG');
			if (!$if_config) {
				if ($start_date < $find_one['start_date']) {
					$result = ['status' => 'error', 'msg' => '计费日期不能小于当前配置日期'];
					$this->ajaxReturn($result);
				}
			}
		}
		//用传过来的channel_id做为条件,如果是邦秒验传过来的是channel
		$channel_id = $data['channel_id'];
		if ($type == 'bmy') {
			$find_one = $this->upstream_channel_model->where([
				'product_id' => $data['product_id'],
				'channel'    => $channel_id,
			])
													 ->find();
			if (empty($find_one)) {
				$result = ['status' => 'error', 'msg' => '对应的渠道不存在'];
				$this->ajaxReturn($result);
			}
			$channel_id = $find_one['id'];
		}
		
		$where_not['id']                  = ['neq', $id];
		$where_not['product_id']          = $data['product_id'];
		$where_not['upstream_channel_id'] = $channel_id;
		$where_not['start_date']          = $start_date;
		$find_one                         = $this->upstream_channel_price_model->where($where_not)
																			   ->find();
		
		if (!empty($find_one)) {
			$result = ['status' => 'error', 'msg' => '该渠道计费日期已配置'];
			$this->ajaxReturn($result);
		}
		//获取channel的信息
		$channel_info = $this->upstream_channel_model->where(['id' => $channel_id])
													 ->find();
		
		$price_res = [];
		$key_arr   = [];
		array_walk($data['arr'], function ($val) use (&$price_res, &$key_arr) {
			$p_arr                = explode('#', $val);
			$key_arr[]            = $p_arr[0];
			$price_res[$p_arr[0]] = $p_arr[1];
		});
		if (count($key_arr) != count(array_unique($key_arr))) {
			$result = ['status' => 'error', 'msg' => '请检查配置项是否有重复字段'];
			$this->ajaxReturn($result);
		}
		
		$res_data = [
			'product_id'          => $data['product_id'],
			'upstream_channel_id' => $channel_id,
			'channel'             => $channel_info['channel'],
			'price'               => json_encode($price_res, JSON_UNESCAPED_UNICODE),
			'start_date'          => $start_date,
			'admin'               => $admin,
			'update_time'         => time(),
			'is_billing'          => $is_billing,
		];
		$r        = $this->upstream_channel_price_model->where(['id' => $id])
													   ->save($res_data);
		if ($r) {
			$result = ['status' => 'ok', 'msg' => '编辑成功'];
			$this->ajaxReturn($result);
		}
		$result = ['status' => 'error', 'msg' => '编辑失败'];
		$this->ajaxReturn($result);
	}
	
	/**
	 * 邦秒验计费配置
	 *
	 */
	public function bmyNewConfig()
	{
		//把邦秒验的所有子产品的id查找出来
		$product_info      = $this->product_model->where(['father_id' => 200])
												 ->field('id, product_id, product_name')
												 ->select();
		$product_sub_ids   = array_column($product_info, 'product_id');
		$product_sub_names = array_column($product_info, 'product_name', 'product_id');
		
		$data                 = I('get.');
		$asc                  = $data['asc'];
		$order                = $data['order'];
		$input['start_time']  = $data['start_time'];
		$input['end_time']    = $data['end_time'];
		$where['product_id']  = ['in', $product_sub_ids];
		$where['delete_time'] = 0;
		if (!empty($data['channel'])) {
			$where['channel'] = $data['channel'];
		}
		if (!empty($data['product_id'])) {
			$where['product_id'] = $data['product_id'];
		}
		if (!empty($input['start_time'])) {
			$where['start_date'][] = ['EGT', $input['start_time']];
		}
		if (!empty($input['end_time'])) {
			$where['start_date'][] = ['ELT', $input['end_time']];
		}
		//如果有排序请求
		if (($asc == 0 || $asc == 1) && !empty($order)) {
			//升序为1,降序为0
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->select();
			if (!empty($channel_price_config_info)) {
				foreach ($channel_price_config_info as $key => &$value) {
					$price_config      = json_decode($value['price'], true);
					$value['all']      = isset($price_config['all']) ? $price_config['all'] : 0;
					$value['succ']     = isset($price_config['succ']) ? $price_config['succ'] : 0;
					$value['yd']       = isset($price_config['yd']) ? $price_config['yd'] : 0;
					$value['liantong'] = isset($price_config['lt']) ? $price_config['lt'] : 0;
					$value['dx']       = isset($price_config['dx']) ? $price_config['dx'] : 0;
				}
				//对数据进行排序
				$field = $order;
				$asc   = $asc ? SORT_ASC : SORT_DESC;
				array_multisort(array_map(function ($item) {
					return floatval($item);
				}, array_column($channel_price_config_info, $field)), $asc, $channel_price_config_info);
				
				//对数据进行分页
				$count     = count($channel_price_config_info);
				$list_rows = 30;
				if ($count > $list_rows) {
					$start                     = (I('get.page', 1, 'intval') - 1) * $list_rows;
					$page                      = (new Page($count, $list_rows))->show();
					$channel_price_config_info = array_slice($channel_price_config_info, $start, $list_rows);
				} else {
					$page = '';
				}
			}
		} else {
			$count = $this->upstream_channel_price_model->where($where)
														->count();
			//分页
			$listRow                   = 30;
			$page                      = new Page($count, $listRow);
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->order('id desc')
																			->limit($page->firstRow, $page->listRows)
																			->select();
			$page                      = $page->show();
		}
		
		$field        = 'id, product_id, name, channel';
		$channel_info = $this->upstream_channel_model->where(['product_id' => ['in', $product_sub_ids]])
													 ->field($field)
													 ->select();
		
		//通过channel确定其名称
		$channel_name_arr = array_column($channel_info, null, 'channel');
		
		$channel_price_list = [];
		array_walk($channel_price_config_info, function (&$val) use (&$channel_price_list, $channel_name_arr, $product_sub_names) {
			$val['channel_name']   = isset($channel_name_arr[$val['channel']]['name']) ? $channel_name_arr[$val['channel']]['name'] : '';
			$json                  = json_decode($val['price'], JSON_UNESCAPED_UNICODE);
			$val['peizhi']['all']  = isset($json['all']) ? $json['all'] : '无';
			$val['peizhi']['succ'] = isset($json['succ']) ? $json['succ'] : '无';
			$val['peizhi']['yd']   = isset($json['yd']) ? $json['yd'] : '无';
			$val['peizhi']['lt']   = isset($json['lt']) ? $json['lt'] : '无';
			$val['peizhi']['dx']   = isset($json['dx']) ? $json['dx'] : '无';
			$val['product_name']   = isset($product_sub_names[$val['product_id']]) ? $product_sub_names[$val['product_id']] : '';
			$channel_price_list[]  = $val;
		});
		
		
		//产品信息也得重新分配
		$channelOptionString = makeOption(array_column($channel_name_arr, 'name', 'channel'), I('get.channel'));
		$productOptionString = makeOption($product_sub_names, I('get.product_id'));
		
		$this->assign('channelOptionString', $channelOptionString);
		$this->assign('productOptionString', $productOptionString);
		$this->assign('product_sub_names', $product_sub_names);
		$this->assign('channel_name_arr', $channel_name_arr);
		$this->assign('channel_price_list', $channel_price_list);
		$this->assign('page', $page);
		$this->assign('input', $input);
		
		$this->display();
	}
	
	public function getBmyNewConfig()
	{
		$data      = I('post.');
		$info      = $this->upstream_channel_price_model->where(['id' => $data['id']])
														->find();
		$json_text = json_decode($info['price'], true);
		$out_count = count($json_text);
		$all_arr   = ['all' => '查询量', 'succ' => '查得量', 'yd' => '移动查得量', 'lt' => '联通查得量', 'dx' => '电信查得量'];
		$string    = '';
		foreach ($json_text as $k => $v) {
			$string        .= '<div class="dialogue_form_child_html_edit" style="background-color: #FCFCFC;">';
			$str_intent    = '<div class="form-group"><label for="tips" class="col-sm-2 control-label">选项</label><div>';
			$str_intent    .= '<div class="col-sm-2 indent-html"><select class="intents_name" name="peizhi_edit[]" style="width: 120px;"><option value="">请选择</option>';
			$option_intent = '';
			foreach ($all_arr as $kk => $vv) {
				$selected = '';
				if ($k == $kk) {
					$selected = 'selected';
				}
				$option_intent .= '<option ' . $selected . ' value="' . $kk . '">' . $vv . '</option>';
			}
			$str_intent .= $option_intent . '</select></div></div><label class="col-sm-2 control-label">价格</label><div><div class="col-sm-2 indent-html"><input type="text" name="price_edit[]" value="' . $v . '" /></div></div></div></div>';
			$string     .= $str_intent;
		}
		
		//把邦秒验的所有子产品的id查找出来
		$product_info      = $this->product_model->where(['father_id' => 200])
												 ->field('id, product_id, product_name')
												 ->select();
		$product_sub_ids   = array_column($product_info, 'product_id');
		$product_sub_names = array_column($product_info, 'product_name', 'product_id');
		
		
		$where['product_id'] = ['in', $product_sub_ids];
		$field               = 'id, product_id, name, channel';
		$channel_info        = $this->upstream_channel_model->where($where)
															->field($field)
															->select();
		$channel_name_info   = array_column($channel_info, 'name', 'channel');
		
		$option = '<option value="">请选择</option>';
		foreach ($channel_name_info as $key => $value) {
			if ($info['channel'] == $key) {
				$option .= '<option selected value="' . $key . '">' . $value . '</option>';
			} else {
				$option .= '<option value="' . $key . '">' . $value . '</option>';
			}
		}
		
		$option2 = '<option value="">请选择</option>';
		
		foreach ($product_sub_names as $key => $value) {
			if ($info['product_id'] == $key) {
				$option2 .= '<option selected value="' . $key . '">' . $value . '</option>';
			} else {
				$option2 .= '<option value="' . $key . '">' . $value . '</option>';
			}
		}
		$result = ['text' => $string, 'info' => $info, 'option' => $option, 'option2' => $option2];
		$this->ajaxReturn($result);
		
	}
	
	/**
	 * 邦企查计费配置
	 */
	public function bqcNewConfig()
	{
		$product_id           = 401;
		$data                 = I('get.');
		$asc                  = $data['asc'];
		$order                = $data['order'];
		$input['start_time']  = $data['start_time'];
		$input['end_time']    = $data['end_time'];
		$where                = ['product_id' => $product_id];
		$where['delete_time'] = 0;
		if (!empty($input['start_time'])) {
			$where['start_date'][] = ['EGT', $input['start_time']];
		}
		if (!empty($input['end_time'])) {
			$where['start_date'][] = ['ELT', $input['end_time']];
		}
		//如果有排序请求
		if (($asc == 0 || $asc == 1) && !empty($order)) {
			//升序为1,降序为0
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->select();
			if (!empty($channel_price_config_info)) {
				foreach ($channel_price_config_info as $key => &$value) {
					$price_config      = json_decode($value['price'], true);
					$value['all']      = isset($price_config['all']) ? $price_config['all'] : 0;
					$value['succ']     = isset($price_config['succ']) ? $price_config['succ'] : 0;
					$value['yd']       = isset($price_config['yd']) ? $price_config['yd'] : 0;
					$value['liantong'] = isset($price_config['lt']) ? $price_config['lt'] : 0;
					$value['dx']       = isset($price_config['dx']) ? $price_config['dx'] : 0;
				}
				//对数据进行排序
				$field = $order;
				$asc   = $asc ? SORT_ASC : SORT_DESC;
				array_multisort(array_map(function ($item) {
					return floatval($item);
				}, array_column($channel_price_config_info, $field)), $asc, $channel_price_config_info);
				
				//对数据进行分页
				$count     = count($channel_price_config_info);
				$list_rows = 30;
				if ($count > $list_rows) {
					$start                     = (I('get.page', 1, 'intval') - 1) * $list_rows;
					$page                      = (new Page($count, $list_rows))->show();
					$channel_price_config_info = array_slice($channel_price_config_info, $start, $list_rows);
				} else {
					$page = '';
				}
			}
		} else {
			$count = $this->upstream_channel_price_model->where($where)
														->count();
			//分页
			$listRow                   = 30;
			$page                      = new Page($count, $listRow);
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->order('id desc')
																			->limit($page->firstRow, $page->listRows)
																			->select();
			$page                      = $page->show();
		}
		
		$product_name = '邦企查';
		$field        = 'id, product_id, name, channel';
		$channel_info = $this->upstream_channel_model->where(['product_id' => $product_id])
													 ->field($field)
													 ->select();
		
		//通过channel确定其名称
		$channel_name_arr = array_column($channel_info, null, 'channel');
		
		$channel_price_list = [];
		array_walk($channel_price_config_info, function (&$val) use (&$channel_price_list, $channel_name_arr, $product_name) {
			$val['channel_name']   = isset($channel_name_arr[$val['channel']]['name']) ? $channel_name_arr[$val['channel']]['name'] : '';
			$json                  = json_decode($val['price'], JSON_UNESCAPED_UNICODE);
			$val['peizhi']['all']  = isset($json['all']) ? $json['all'] : '无';
			$val['peizhi']['succ'] = isset($json['succ']) ? $json['succ'] : '无';
			$val['peizhi']['yd']   = isset($json['yd']) ? $json['yd'] : '无';
			$val['peizhi']['lt']   = isset($json['lt']) ? $json['lt'] : '无';
			$val['peizhi']['dx']   = isset($json['dx']) ? $json['dx'] : '无';
			$val['product_name']   = $product_name;
			$channel_price_list[]  = $val;
		});
		
		
		$this->assign('channel_name_arr', $channel_name_arr);
		$this->assign('channel_price_list', $channel_price_list);
		$this->assign('page', $page);
		$this->assign('input', $input);
		
		$this->display();
	}
	
	/**
	 * 号码状态查询计费配置
	 */
	public function haomaNewConfig()
	{
		$product_id           = 801;
		$data                 = I('get.');
		$asc                  = $data['asc'];
		$order                = $data['order'];
		$input['start_time']  = $data['start_time'];
		$input['end_time']    = $data['end_time'];
		$where                = ['product_id' => $product_id];
		$where['delete_time'] = 0;
		if (!empty($input['start_time'])) {
			$where['start_date'][] = ['EGT', $input['start_time']];
		}
		if (!empty($input['end_time'])) {
			$where['start_date'][] = ['ELT', $input['end_time']];
		}
		//如果有排序请求
		if (($asc == 0 || $asc == 1) && !empty($order)) {
			//升序为1,降序为0
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->select();
			if (!empty($channel_price_config_info)) {
				foreach ($channel_price_config_info as $key => &$value) {
					$price_config      = json_decode($value['price'], true);
					$value['all']      = isset($price_config['all']) ? $price_config['all'] : 0;
					$value['succ']     = isset($price_config['succ']) ? $price_config['succ'] : 0;
					$value['yd']       = isset($price_config['yd']) ? $price_config['yd'] : 0;
					$value['liantong'] = isset($price_config['lt']) ? $price_config['lt'] : 0;
					$value['dx']       = isset($price_config['dx']) ? $price_config['dx'] : 0;
				}
				//对数据进行排序
				$field = $order;
				$asc   = $asc ? SORT_ASC : SORT_DESC;
				array_multisort(array_map(function ($item) {
					return floatval($item);
				}, array_column($channel_price_config_info, $field)), $asc, $channel_price_config_info);
				
				//对数据进行分页
				$count     = count($channel_price_config_info);
				$list_rows = 30;
				if ($count > $list_rows) {
					$start                     = (I('get.page', 1, 'intval') - 1) * $list_rows;
					$page                      = (new Page($count, $list_rows))->show();
					$channel_price_config_info = array_slice($channel_price_config_info, $start, $list_rows);
				} else {
					$page = '';
				}
			}
		} else {
			$count = $this->upstream_channel_price_model->where($where)
														->count();
			//分页
			$listRow                   = 30;
			$page                      = new Page($count, $listRow);
			$channel_price_config_info = $this->upstream_channel_price_model->where($where)
																			->order('id desc')
																			->limit($page->firstRow, $page->listRows)
																			->select();
			$page                      = $page->show();
		}
		
		$product_name = '号码状态查询';
		$field        = 'id, product_id, name, channel';
		$channel_info = $this->upstream_channel_model->where(['product_id' => $product_id])
													 ->field($field)
													 ->select();
		
		//通过channel确定其名称
		$channel_name_arr = array_column($channel_info, null, 'channel');
		
		$channel_price_list = [];
		array_walk($channel_price_config_info, function (&$val) use (&$channel_price_list, $channel_name_arr, $product_name) {
			$val['channel_name']   = isset($channel_name_arr[$val['channel']]['name']) ? $channel_name_arr[$val['channel']]['name'] : '';
			$json                  = json_decode($val['price'], JSON_UNESCAPED_UNICODE);
			$val['peizhi']['all']  = isset($json['all']) ? $json['all'] : '无';
			$val['peizhi']['succ'] = isset($json['succ']) ? $json['succ'] : '无';
			$val['peizhi']['yd']   = isset($json['yd']) ? $json['yd'] : '无';
			$val['peizhi']['lt']   = isset($json['lt']) ? $json['lt'] : '无';
			$val['peizhi']['dx']   = isset($json['dx']) ? $json['dx'] : '无';
			$val['product_name']   = $product_name;
			$channel_price_list[]  = $val;
		});
		
		
		$this->assign('channel_name_arr', $channel_name_arr);
		$this->assign('channel_price_list', $channel_price_list);
		$this->assign('page', $page);
		$this->assign('input', $input);
		
		$this->display();
	}
}
