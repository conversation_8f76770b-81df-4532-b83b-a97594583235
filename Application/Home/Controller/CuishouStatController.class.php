<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CuishouStatLineChartRepository;
use Home\Repositories\CuishouStatRepository;
use Home\Repositories\CuishouStatFileRepository;

class C<PERSON>shouStatController extends AdminController
{
    protected $stat_repository;
    protected $line_chart_repository;

    // 不支持依赖注入
    public function __construct()
    {
        parent::__construct();
        $this->stat_repository = new CuishouStatRepository();
        $this->line_chart_repository = new CuishouStatLineChartRepository();
    }

    /**
     * 总计详情导出文件
     */
    public function downloadTotalShow()
    {
        try {
            // 导出文件
            $this->stat_repository->downloadTotalShow();

        } catch (\Exception $e) {
            $status = 4178;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 总计详情
     */
    public function totalShow()
    {
        if (IS_GET) {
            // 客户列表 && 产品列表 && 输入的参数
            $list_account = $this->stat_repository->getAccountList();
            $list_product = $this->stat_repository->getProductList();
            $request_params = I('get.');

            // 作为属性传递给组件
            $init_params = json_encode(compact('list_account', 'list_product', 'request_params'), JSON_UNESCAPED_UNICODE);
            $this->assign(compact('init_params'));
            $this->display('total_show');
            exit();
        }

        try {
            // 获取总计详情列表
            $list_info = $this->stat_repository->getTotalInfoByDay();
            $status = 0;

            $this->ajaxResponse(compact('list_info', 'status'));

        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();

            $this->ajaxResponse(compact('msg', 'status'));
        }

    }

    /**
     * 统计列表页
     */
    public function index()
    {
        // GET方法获取(不限制ID)产品列表
        $user_list = $this->stat_repository->userListNoIdForGet();

        // 客户列表cuishouDetail
        $list_account = $this->stat_repository->getAccountList();

        // 传递的参数
        $input = I('get.');
        $params = compact('list_account', 'user_list', 'input');
        $this->assign($params);
        $this->display();
    }

    public function ajaxList()
    {
        try {
            $stat_data = $this->listNoCache();
            $info['total_data'] = $stat_data['total_data'];
            $info['user_show'] = $stat_data['user_show'];
            $info['draw'] = (int)$stat_data['input']['draw'];
            $info['recordsTotal'] = $stat_data['count'];
            $info['recordsFiltered'] = $stat_data['count'];
            echo json_encode($info);
            exit;
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 统计详情页
     */
    public function cuishouDetail()
    {
        $user_list = $this->stat_repository->userListDependGetForDetail();

        $this->assign('user_list', $user_list);
        $this->assign('input', I('get.'));
        $this->display();
    }

    /**
     * ajax返回催收详情页的信息
     */
    public function ajaxDetail()
    {
        try {
            $stat_data = $this->detailNoCache();

            // 折线图的数据
            $stat_data = $this->line_chart_repository->lineInfoForDetail($stat_data);

            $this->__Return($stat_data, '', 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 列表页数据统计
     * @return array
     */
    protected function listNoCache()
    {
        $choose_id = I('get.id', '', 'trim');

        // 选择特定客户
        if ($choose_id) {
            return $this->chooseOneForList();
        }

        // 选择所有客户
        return $this->chooseAllForList();
    }

    /**
     * 列表页选择所有用户的数据统计
     * @return array
     */
    protected function chooseAllForList()
    {
        // 符合条件的产品列表
        $user_list = $this->stat_repository->userListDependGet();

        // 给产品追加需要展示的产品
        $user_list = $this->stat_repository->appendAccountToProductList($user_list);

        // init params
        $input = I('get.');

        // 时间限制
        $where_date = $this->stat_repository->timeLimitForList();

        // 分页
        $count = count($user_list);

        // 所有用户的当前条件下的统计信息 && 分页
        $user_list_stat = $this->stat_repository->personalStatForList($user_list, ['time' => $where_date]);
        $user_show = $this->stat_repository->pageShowForList($user_list_stat);
        $total_data = $user_list_stat['total_stat'];

        return compact('user_show', 'total_data', 'input', 'count');
    }

    /**
     * 列表页选择单个账户的时候的统计
     * @return array
     */
    protected function chooseOneForList()
    {
        // 客户列表
        $user_list = $this->stat_repository->userListDependGet();

        // 合并产品和客户
        $user_list = $this->stat_repository->appendAccountToProductList($user_list);

        // init params
        $input = I('get.');
        $choose_id = I('get.id', '', 'trim');

        // 时间限制
        $where_date = $this->stat_repository->timeLimitForList();

        // 选中用户当前条件下的统计信息
        $where['time'] = $where_date;
        $where['uid'] = new \MongoInt32($choose_id);
        $total_data = $this->stat_repository->chooseOneStat($where);

        // 配置展示的产品
        $user_index_id = array_column($user_list, null, 'id');
        $input['developer'] = $user_index_id[$choose_id]['developer'];
        $user_show[0] = array_merge($total_data, $user_index_id[$choose_id]);
        $count = 1;
        return compact('user_show', 'total_data', 'input', 'count');
    }

    protected function detailNoCache()
    {
        // all cuishou user
        $user_list = $this->stat_repository->userListDependGetForDetail();

        // init params
        $input = I('get.');
        $choose_id = I('get.id', '', 'trim');

        // 时间限制 && 需要展示的日期列表
        $where_date = $this->stat_repository->timeLimitForDetail();
        $date_list = $this->stat_repository->showDateList();

        // 这段日期 各天的统计信息
        $where = [
            'uid' => new \MongoInt32($choose_id),
            'time' => $where_date
        ];
        $date_stat_show = $this->stat_repository->dailyStat($where, $date_list);

        // 这段日期的数据总量
        $total_data = $this->stat_repository->chooseOneStat($where);

        // show choose user
        $user_list_show = array_column($user_list, null, 'id');
        $input['developer'] = $user_list_show[$choose_id]['developer'];

        return compact('date_stat_show', 'total_data', 'input', 'user_list', 'date_list');
    }

    // 供筛选的客户列表
    public function clientList()
    {
        $user_list = $this->stat_repository->clientListForPost();
        $this->__Return($user_list, '', 'success');
    }

    /**
     * 催收分导出（按天）
     */
    public function exportByDay()
    {
        $stat_data = $this->listByDayNoCache();

        // 客户列表
        $list_account = $this->stat_repository->getAccountList();

        // init params
        // 时间限制 && 需要展示的日期列表
        $where_date = $this->stat_repository->timeLimitForDetail();
        $date_list = $this->stat_repository->showDateList();

        $data = isset($stat_data['user_show']) ? $stat_data['user_show'] : (isset($stat_data['user_list']) ? $stat_data['user_list'] : []);
        foreach ($data as $key => $item) {
            // 这段日期 各天的统计信息
            $where = [
                'uid' => new \MongoInt32($item['id']),
                'time' => $where_date
            ];
            $date_stat_show = $this->stat_repository->dailyStat($where, $date_list);
            ksort($date_stat_show);
            $data[$key]['date_stat_show'] = $date_stat_show;
        }

        $file_name = RUNTIME_PATH . 'Cache/cuishou_by_day_list.csv';
        $cuishouFileRepository = new CuishouStatFileRepository();
        $cuishouFileRepository->genTempFileListByDayForRequest($data, $list_account, $file_name);
        $cuishouFileRepository->genFileForFileDownload($file_name);
    }

    /**
     * 列表页数据统计
     * @return array
     */
    protected function listByDayNoCache()
    {
        $choose_id = I('get.id', '', 'trim');

        // 选择特定客户
        if ($choose_id) {
            return $this->chooseOneForList();
        }

        // 选择所有客户
        return $this->chooseAllByDayForList();
    }

    /**
     * 列表页选择所有用户的数据统计
     * @return array
     */
    protected function chooseAllByDayForList()
    {
        // 符合条件的产品列表
        $user_list = $this->stat_repository->userListDependGet();

        // 给产品追加需要展示的产品
        $user_list = $this->stat_repository->appendAccountToProductList($user_list);

        // 时间限制
        $where_date = $this->stat_repository->timeLimitForList();

        // 所有用户的当前条件下的统计信息
        return $this->stat_repository->personalStatForList($user_list, ['time' => $where_date]);
    }
}
