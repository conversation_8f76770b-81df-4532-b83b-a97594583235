<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BmCrawlerStatApiRepository;
use Home\Repositories\BmCrawlerStatRepository;

class BmCrawlerStatFileController extends AdminController
{
    //api
    protected $stat_api_repository;

    protected $stat_repository;

    public function __construct()
    {
        parent::__construct();
        $this->stat_api_repository = new BmCrawlerStatApiRepository();
        $this->stat_repository = new BmCrawlerStatRepository();
        set_time_limit(300);
    }

    //统计列表导出
    public function index()
    {
        //账号列表
        $product_list = $this->stat_repository->getProductList();
        //查询条件
        $where = $this->stat_repository->getCrawlerListParam();
        $where['cids'] = json_encode(array_keys($product_list));
        //列表
        $data = $this->stat_api_repository->getReportList($product_list, $where);

        $this->getCrawlerListDownload($data['user_list']);
    }

    //统计详情导出
    public function detail()
    {
        //账号列表
        $user_list = $this->stat_repository->getProductList();

        $where = $this->stat_repository->getCrawlerListParam();
        $where['cids'] = json_encode(array_keys($user_list));
        $stat_list = $this->stat_api_repository->getReportDetail($where);

        $date_list = $this->stat_repository->dateList();
        $date_stat_show = array_merge($date_list, $stat_list['date_stat_show']);

        $this->getCrawlerDetailDownload($date_stat_show);
    }

    /**
     * 统计列表导出
     * @param  array $stat_list 统计列表
     * @return
     */
    public function getCrawlerListDownload($stat_list)
    {
        $file_name = RUNTIME_PATH . 'Cache/crawler_list.csv';
        $title_list = '账号ID,账号名称,尝试授权量,授权成功量,授权成功率,爬取成功量,爬取成功率,报告生成量,爬取成功号码量,尝试重置密码量,重置密码成功量,重置密码成功率,详单缺失率';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as &$stat_data) {
            $stat_data['total_nums'] = isset($stat_data['total_nums']) ? $stat_data['total_nums'] : 0;
            $stat_data['authen_nums'] = isset($stat_data['authen_nums']) ? $stat_data['authen_nums'] : 0;
            $stat_data['authen_pct'] = isset($stat_data['authen_pct']) ? $stat_data['authen_pct'] : "0.00%";
            $stat_data['crawl_nums'] = isset($stat_data['crawl_nums']) ? $stat_data['crawl_nums'] : 0;
            $stat_data['crawl_pct'] = isset($stat_data['crawl_pct']) ? $stat_data['crawl_pct'] : "0.00%";
            $stat_data['report_nums'] = isset($stat_data['report_nums']) ? $stat_data['report_nums'] : 0;
            $stat_data['tel_num'] = isset($stat_data['tel_num']) ? $stat_data['tel_num'] : 0;
            $stat_data['pwd_rt_total'] = isset($stat_data['pwd_rt_total']) ? $stat_data['pwd_rt_total'] : 0;
            $stat_data['pwd_rt_success'] = isset($stat_data['pwd_rt_success']) ? $stat_data['pwd_rt_success'] : 0;
            $stat_data['pwd_rt_pct'] = isset($stat_data['pwd_rt_pct']) ? $stat_data['pwd_rt_pct'] : "0.00%";
            $stat_data['log_loss_pct'] = isset($stat_data['log_loss_pct']) ? $stat_data['log_loss_pct'] : "0.00%";

            $show_str = '"'.$stat_data['id'] . '","' . $stat_data['developer'] . '","' . $stat_data['total_nums'] . '","'. $stat_data['authen_nums'] . '","' .$stat_data['authen_pct'] .'","'. $stat_data['crawl_nums'] . '","'. $stat_data['crawl_pct'].'","'.$stat_data['report_nums'].'","'.$stat_data['tel_num'].'","'.$stat_data['pwd_rt_total'].'","'.$stat_data['pwd_rt_success'].'","'.$stat_data['pwd_rt_pct'].'","'.$stat_data['log_loss_pct'].'"';

            $show_str = mb_convert_encoding($show_str,'gb2312','utf-8');
            // log file
            file_put_contents($file_name, PHP_EOL . $show_str, 8);
        }

        // file download
        $this->stat_repository->fileDownload($file_name);
    }

    /**
     * 统计详情导出
     * @param  array $stat_list 详情列表
     * @return
     */
    public function getCrawlerDetailDownload($stat_list)
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $choose_id = I('get.id', '');

        if (!$begin && !$end) {
            $begin = time() - 86400 * 30;
            $end = time();
        }
        $day_begin = date('Ymd', $begin);
        $day_end = date('Ymd', $end);

        $file_name = RUNTIME_PATH . 'Cache/' . $day_begin . '-' . $day_end . '-' . $choose_id . '.csv';
        $title_list = '时间,  尝试授权量,授权成功量,授权成功率,爬取成功量,爬取成功率,报告生成量,爬取成功号码量,尝试重置密码量,重置密码成功量,重置密码成功率,详单缺失率';
        $title_list = mb_convert_encoding($title_list,'gb2312','utf-8');
        file_put_contents($file_name, $title_list);

        foreach ($stat_list as $date => $stat_data) {
            $stat = [];
            $stat['total_nums'] = isset($stat_data['total_nums']) ? $stat_data['total_nums'] : 0;
            $stat['authen_nums'] = isset($stat_data['authen_nums']) ? $stat_data['authen_nums'] : 0;
            $stat['authen_pct'] = isset($stat_data['authen_pct']) ? $stat_data['authen_pct'] : "0.00%";
            $stat['crawl_nums'] = isset($stat_data['crawl_nums']) ? $stat_data['crawl_nums'] : 0;
            $stat['crawl_pct'] = isset($stat_data['crawl_pct']) ? $stat_data['crawl_pct'] : "0.00%";
            $stat['report_nums'] = isset($stat_data['report_nums']) ? $stat_data['report_nums'] : 0;
            $stat['tel_num'] = isset($stat_data['tel_num']) ? $stat_data['tel_num'] : 0;
            $stat['pwd_rt_total'] = isset($stat_data['pwd_rt_total']) ? $stat_data['pwd_rt_total'] : 0;
            $stat['pwd_rt_success'] = isset($stat_data['pwd_rt_success']) ? $stat_data['pwd_rt_success'] : 0;
            $stat['pwd_rt_pct'] = isset($stat_data['pwd_rt_pct']) ? $stat_data['pwd_rt_pct'] : "0.00%";
            $stat['log_loss_pct'] = isset($stat_data['log_loss_pct']) ? $stat_data['log_loss_pct'] : "0.00%";

            $show_str = '"'. $date . '","' . $stat['total_nums'] . '","'. $stat['authen_nums'] . '","' .$stat['authen_pct'] .'","'. $stat['crawl_nums'] . '","'. $stat['crawl_pct'].'","'.$stat['report_nums'].'","'.$stat['tel_num'].'","'.$stat['pwd_rt_total'].'","'.$stat['pwd_rt_success'].'","'.$stat['pwd_rt_pct'].'","'.$stat['log_loss_pct'].'"';

            $show_str = mb_convert_encoding($show_str,'gb2312','utf-8');
            file_put_contents($file_name, PHP_EOL . $show_str, 8);
        }
        // file download
        $this->stat_repository->fileDownload($file_name);
    }
}