<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\ReceiptRepository;
use Home\Repositories\RemitRepository;
use Common\Common\CurlTrait;

/**
 * @title    收款单控制器
 * <AUTHOR>
 * @datetime 16:26 2019/2/26 0026
 **/
class ReceiptController extends AdminController
{
    use CurlTrait;
    private $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new ReceiptRepository();
    }

    //列表页
    public function index()
    {
        $data = $this->repository->getListData();
        $this->assign($data);
        $this->display();
    }

    public function cancel_remit()
    {
        $this->repository->cancel_remit();
        redirect($_SERVER['HTTP_REFERER']);
    }

    //增加
    public function add()
    {
        if (IS_POST) {
            switch (I('post.type', '', 'trim')) {
                case 'valid_serial' :
                    //验证流水单号唯一性
                    $res = $this->repository->valid_serial(I('post.serial', '', 'trim'));
                    $status = 0;
                    $result = $res ? 1 : 0;
                    $this->ajaxReturn(compact('status', 'result'));
                    break;
                case 'file_in' :
                    //excel导入收款单
                    try {
                        $data = $this->repository->file_in($this->loginuser['username']);
                    } catch (\Exception $e) {
                        exit('error：' . $e->getMessage());
                    }
                    if (empty($data['copy_row']) && empty($data['exists_row'])) {
                        exit('成功导入' . $data['count'] . '条数据');
                    } else {
                        $this->assign($data);
                        $this->display('file_in');
                    }
                    break;
                case 'file_in_wangshang' :
                    //excel导入收款单
                    try {
                        $data = $this->repository->file_in_wangshang($this->loginuser['username']);
                    } catch (\Exception $e) {
                        exit('error：' . $e->getMessage());
                    }
                    if (empty($data['copy_row']) && empty($data['exists_row'])) {
                        exit('成功导入' . $data['count'] . '条数据');
                    } else {
                        $this->assign($data);
                        $this->display('file_in');
                    }
                    break;
                default :
                    //增加收款单
                    $res = $this->repository->add($this->loginuser['username']);
                    if ($res === true) {
                        $this->redirect('index');
                    } else {
                        exit($res);
                    }
                    break;
            }
        } else {
            $this->display();
        }
    }

    //删除
    public function del()
    {
        $this->repository->del();
        redirect($_SERVER['HTTP_REFERER']);
    }

    //重新推送认款

    /**
     * @throws \Exception
     */
    public function re_push(){
        $this->repository->re_push();
    }

    //重新推送认款撤销

    /**
     * @throws \Exception
     */
    public function re_push_cancle(){
        $this->repository->re_push_cancle();
    }


    //批量导出
    public function file_out()
    {
        $this->repository->file_out();
    }

    //提交打款单
    public function add_remit()
    {
        if (IS_POST) {
            if (I('post.type') == 'valid_serial') {
                //验证流水号是否存在
                $remit_id = I('post.remit_id', '', 'intval') ?: null;
                $res = (new RemitRepository($this->loginuser))->valid_serial(I('post.serial', '', 'trim'), $remit_id);
                $status = 0;
                $result = $res ? 1 : 0;
                $this->ajaxReturn(compact('status', 'result'));
            } elseif (I('post.type') == 'option_customer') {
                $customer_id = I('post.customer_id', '') ?: null;
                $data = $this->repository->getOptionCustomer($customer_id);
                $this->ajaxReturn($data);
            } else {
                try {
                    $this->repository->run_admit($this->loginuser);
                    $this->redirect('index');
                } catch (\Exception $exception) {
                    $this->error('增加失败' . $exception->getMessage(), 'index');
                }
            }
        } else {
            $data = $this->repository->getReceiptDataForAddRemite(I('get.receipt_serial', ''));
            $this->assign('data', $data);
            $this->display();
        }
    }

    public function split_price()
    {
        if (IS_POST) {
            try {
                $this->repository->add_split_price($this->loginuser['username']);
                $this->redirect('index');
            } catch (\Exception $e) {
                $this->error('增加失败' . $e->getMessage(), 'index');
            }
        } else {
            // 如果征信机构有子收款单 页面给出提示，不能拆分
            $this->repository->checkChildReceipt();
            $data = $this->repository->getSplitPrice();
            $this->assign('data', $data);
            $this->display();
        }
    }

    public function del_split_price()
    {
        try {
            $result = $this->repository->del_split_price($this->loginuser['username']);
            $status = $result ? 0 : 1;
            $msg = $result ? 'ok' : '删除失败';
            $this->ajaxReturn(compact('status', 'msg'));
        } catch (\Exception $e) {
            $this->ajaxReturn(['status' => 1, 'msg' => '删除失败'.$e->getMessage()]);
        }
    }

    public function split_price_list()
    {
        $this->display();
    }

    public function receipt_split()
    {
        $this->assign('receipt_serial',I('get.receipt_serial', '', 'trim'));
        $this->assign('source',I('get.source', '', 'trim'));
        $this->display();
    }





    //认票
    public function add_remit_rel_invoice() {
        $receipt_serial = I('get.receipt_serial', '');
        $data = $this->repository->getReceiptDataForAddRemite($receipt_serial);
        $this->assign('data', $data);
        $this->assign('rri_receipt_serial', $receipt_serial);
        $this->display();
    }
}