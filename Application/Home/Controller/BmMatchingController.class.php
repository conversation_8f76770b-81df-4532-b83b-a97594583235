<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\Model\CuishouUserModel;
use Common\ORG\Page;
use Think\Cache\Driver\Memcache;
use Home\Repositories\FinanceAccountRepository;
use Common\Model\FeeMatchingConfigModel;

class BmMatchingController extends AdminController
{
    public function index()
    {
        // init params
        $input = I('get.');
        $active = I('get.active', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $id = I('get.id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        // all user list
        $user_list = D('AdminApikey')
            ->index('id')
            ->order('id desc')
            ->field('id,owner')
            ->select();

        // check active user
        $where = $active ? ['active' => $active] : [];
        $where = $apikey ? (['apikey' => $apikey] + $where) : $where;
        $where = $contract_status ? (['contract_status' => $contract_status] + $where) : $where;

        if ($begin && $begin_e){
            $where['created_at'] = ['between', [strtotime($begin), strtotime($begin_e.' 23:59:59')]];
        }elseif($begin){
            $where['created_at'] = ['egt',strtotime($begin)];
        }elseif($begin_e){
            $where['created_at'] = ['elt', strtotime($begin_e. ' 23:59:59')];
        }
        if ($end && $end_e){
            $where['validuntil'] = ['between', [$end.' 00:00:00', $end_e.' 23:59:59']];
        }elseif($end){
            $where['validuntil'] = ['egt', $end.' 00:00:00'];
        }elseif ($end_e){
            $where['validuntil'] = ['elt', $end_e.' 23:59:59'];
        }
        // 如果输入的用户id不同那么获取不到数据值
        if ($choose_id && $id && $choose_id != $id) {
            $where['id'] = 'what happened';
        } elseif ($choose_id) {
            $where['id'] = $choose_id;
        } elseif ($id) {
            $where['id'] = $id;
        }

        // gen page object
        if ($choose_id || $id || $apikey) {
            $count = 1;
        } else {
            $count = D('AdminApikey')
                ->where($where)
                ->count();
        }
        $page_obj = new Page($count, C('LIST_ROWS'));

        // account list
        $account_list = D('AdminApikey')
            ->where($where)
            ->order('id desc')
            ->limit($page_obj->firstRow, $page_obj->listRows)
            ->select();

        //查询账号关联所属用户信息
        $account_product_infos = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($account_list, 'id'));
        foreach ($account_list as $k=>$item){
            if ($account_product_infos[$item['id']]){
                $account_list[$k]['account_product'] = $account_product_infos[$item['id']];
            }
        }


        // gen api example and sig_str
        $account_list = array_map(
            function ($account) {

                // generate sig_str
                $account['sig_str'] = (new ToolController())->gen_sig_str($account['password']);
                $sig = (new ToolController())->gen_sig($account['sig_str'], '10086', 'NMKJ', 'myApp',
                    '1.0', $account['apikey'], '86', '1');

                // gen api example
                $account['api_example'] = "https://itag.dianhua.cn/itag/?apikey=" . $account['apikey'] .
                    "&app=myApp&app_ver=1.0&country=86&version=1&tel=10086&uid=NMKJ&sig=" . $sig;
                return $account;
            }, $account_list
        );

        // user info which is choosed
        if ($choose_id) {
            $input['owner'] = $user_list[$choose_id]['owner'];
        }

        $this->assign('account_list', $account_list);
        $this->assign('page', $page_obj->show());
        $this->assign('user_list', $user_list);
        $this->assign('input', $input);
        $this->assign('contract_status', (new CuishouUserModel())->getContractStatus());
        $this->display();
    }

    public function addAccount()
    {
        if (IS_GET) {
            // account property
            $field_list = D('DictApictrlFields')
                ->where('status=1')
                ->field('name,remark')
                ->select();

            // get api fields List
            $out_fields = D('AdminOutputFields')
                ->field('id,name,remark')
                ->where('status=1')
                ->select();

            // GET方式获取用户列表客户(select2使用)
            $repository_account = new FinanceAccountRepository();
            $list_account_for_select2 = $repository_account->accountListForGet();

            $this->assign('list_account_for_select2', $list_account_for_select2);
            $this->assign('field_list', $field_list);
            $this->assign('out_fields', $out_fields);
            $this->display();
        } else {
            $model = M('', '', 'MYSQL_CRSAPI');
            try {
                // start transaction
                $model->startTrans();

                $data = I('post.');
                $data['created_at'] = time();
                $account_name = $data['name'];
                unset($data['name']);

                // check property
                $data = D('AdminApikey')->filterData($data);

                // add account
                $account_add_result = D('AdminApikey')->add($data);

                // add account property
                if ($account_add_result) {
                    //绑定所属客户
                    if (!empty($account_name)){
                        D('FinanceAccountProduct')->bindToAccounts($account_add_result, $account_name);
                    }

                    $field_list = D('DictApictrlFields')
                        ->where('status=1')
                        ->field('name,remark')
                        ->select();

                    foreach ($field_list as $filed) {
                        $index = $filed['name'];
                        $property[$index] = isset($data[$index]) ? $data[$index] : '';
                    }

                    $property_result = D('AdminApictrl')->addApictrl($account_add_result, $property);
                }

                // add api fields
                if (isset($property_result) && $property_result) {
                    $api_field_result = D('AdminApikeyFields')->addInfo($account_add_result, $data['out_fields']);
                }

                //  rollback where add action failed
                if ($account_add_result === false || !isset($property_result) || !$property_result || !isset($api_field_result) || !$api_field_result) {
                    throw new \Exception('添加账户失败, 请重新尝试');
                }

            } catch (\Exception $e) {

                // rollback
                $model->rollback();
                $this->__Return($e->getMessage());
            }

            // commit
            $model->commit();
            $this->__Return('添加账户成功', '', 'tip_success');
        }
    }

    /**
     * edit
     * @throws \Exception
     */
    public function setAccount()
    {
        if (IS_GET) {
            try {
                // check id
                $id = I('get.id', '', 'intval');
                if (!$id) {
                    throw new \Exception("请刷新页面后, 再次尝试编辑");
                }

                // base account info
                $account_info = D('AdminApikey')
                    ->where(['id' => $id])
                    ->find();

                // get account property
                $account_property = D('AdminApictrl')
                    ->field('field,value')
                    ->index('field')
                    ->where(['apikeyid' => $id])
                    ->select();

                // unserialize  limit_access_ip   bind_domain
                if (isset($account_property['limit_access_ip']['value']) && $account_property['limit_access_ip']['value']) {
                    $account_property['limit_access_ip']['value'] = implode(PHP_EOL, unserialize($account_property['limit_access_ip']['value']));
                }

                if (isset($account_property['bind_domain']['value']) && $account_property['bind_domain']['value']) {
                    $account_property['bind_domain']['value'] = implode(PHP_EOL, unserialize($account_property['bind_domain']['value']));
                }

                // get property list
                $property_list = D('DictApictrlFields')
                    ->where('status=1')
                    ->field('name,remark')
                    ->select();

                // get choose api field list
                $choosed_out_filed = D('AdminApikeyFields')
                    ->where(['apikeyid' => $id, 'status' => 1])
                    ->index('fid')
                    ->select();

                // get api fields List
                $out_fields = D('AdminOutputFields')
                    ->field('id,name,remark')
                    ->where('status=1')
                    ->select();

                // GET方式获取用户列表客户(select2使用)
                $repository_account = new FinanceAccountRepository();
                $list_account_for_select2 = $repository_account->accountListForGet();
                //查询产品关联的用户信息
                $account_product_info = D('FinanceAccountProduct')->getAccountIdByProductId($id, 2);

                $this->assign('list_account_for_select2', $list_account_for_select2);
                $this->assign('account_product_info', $account_product_info);
                $this->assign('account_info', $account_info);
                $this->assign('account_property', $account_property);
                $this->assign('property_list', $property_list);
                $this->assign('choosed_out_filed', $choosed_out_filed);
                $this->assign('out_fields', $out_fields);
                $this->display();
            } catch (\Exception $e) {
                throw new \Exception($e->getMessage());
            }
        } else {
            $this->setInfo();
        }
    }

    protected function setInfo()
    {

        $model = M('', '', 'MYSQL_CRSAPI');

        try {

            // begin transaction
            $model->startTrans();

            // check id
            $id = I('get.id', '', 'intval');
            if (!$id) {
                throw new \Exception("请刷新页面后, 再次尝试编辑");
            }

            // init params
            $data = I('post.');
            $data['id'] = $id;
            $data['updated_at'] = time();
            $account_name = $data['name'];
            unset($data['name']);

            // check property
            $data = D('AdminApikey')->filterData($data);

            // save account
            $account_save_result = D('AdminApikey')->where("id=$id")->save($data);

            // save account product
            D('FinanceAccountProduct')->updateBindToAccounts($id, $account_name);

            // save account property
            if ($account_save_result !== false) {
                $field_list = D('DictApictrlFields')
                    ->where('status=1')
                    ->field('name,remark')
                    ->select();

                foreach ($field_list as $filed) {
                    $index = $filed['name'];
                    $property[$index] = isset($data[$index]) ? $data[$index] : '';
                }

                $property_result = D('AdminApictrl')->setInfo($id, $property);
            }

            // save api fields
            if (isset($property_result) && $property_result !== false) {
                $api_field_result = D('AdminApikeyFields')->updateFields($id, $data['out_fields']);
            }

            //  rollback where add action failed
            if ($account_save_result === false || !isset($property_result) || ($property_result === false) || !isset($api_field_result) || ($api_field_result === false)) {
                throw new \Exception('账户更新失败, 请重新尝试');
            }

            // clear memcache
            $memcache_config = C("MEMCACHE_CONFIG_ITAG");
            $key_main = "apikey_" . $data['apikey'] . "_ctrl";
            $key_field = 'fieldCtrl_' . $id;

            foreach ($memcache_config as $config) {
                $memcache = new Memcache($config);

                $i = 0;
                while ($i < 3) {
                    $main_clear_res = $memcache->set($key_main, '');
                    $field_clear_res = $memcache->set($key_field, '');
                    if ($main_clear_res && $field_clear_res) {
                        break;
                    }
                    $i++;
                }

                // log
                if (!$main_clear_res || !$field_clear_res) {
                    $msg = "account_id: {$id}  memcache host: {$config['host']}  memcached failed to clear" . PHP_EOL;
                    $file_debug = LOG_PATH . '/debug';
                    file_put_contents($file_debug, $msg, 8);
                }
            }

        } catch (\Exception $e) {

            // rollback
            $model->rollback();
            $this->__Return($e->getMessage());
        }

        // commit
        $model->commit();
        $this->__Return('账号更新成功', '', 'tip_success');
    }

    /**
     * 邦秒配计费配置
     * @throws \Exception
     */
    public function configFee(){
        $matchingFeeConfig = new FeeMatchingConfigModel();
        if (IS_POST) {
            $post = $input = I('post.');
            try {
                $data = $this->checkParamsForPost($post);
                if($data['id']){
                    $info = $matchingFeeConfig->find($data['id']);
                    try {
                        $matchingFeeConfig->startTrans();
                        $params_delete = ['is_delete'=>1];
                        $result_delete = $matchingFeeConfig->where('product_id='.$info['product_id'])->save($params_delete);
                        if ($result_delete === false) {
                            throw new \Exception('操作失败，请刷新后再试');
                        }
                    } catch (\Exception $e) {
                        throw new \Exception($e->getMessage());
                    }
                    unset($data['id']);
                }
                $data['create_time'] = time();
                $data['is_delete'] = 0;
                if (!$matchingFeeConfig->add($data)) {
                    // 回滚错误
                    $matchingFeeConfig->rollback();
                    throw new \Exception('操作失败');
                }else{
                    $matchingFeeConfig->commit();
                }
                $this->__Return('操作成功', '', 'tip_success');
            } catch (\Exception $e) {
                $this->__Return('操作失败', $e->getMessage(), 'tip_error');
            }
            exit;
        } else {
            $list = [];
            $product_id = I('get.id', 0, 'intval');
            if (!$product_id) {
                throw new \Exception("请刷新页面后, 再次尝试编辑");
            }
            if ($product_id) {
                // base account info
                $list = D('AdminApikey')
                    ->where(['id' => $product_id])
                    ->find();
                $info = $matchingFeeConfig
                    ->where(['product_id' => $product_id,'is_delete'=>0])
                    ->order('id DESC')
                    ->find();
            }else{
                $this->__Return('缺少必要参数', '', 'tip_error');
            }
            if($info && $info['fee_amount_rule']==2){
                $info['fee_price'] = json_decode($info['fee_price'],true);
            }
            $this->assign('list', $list);
            $this->assign('info', $info);
            $this->assign('feeMethod',$matchingFeeConfig->getFeeMethod());//计费方式
            $this->assign('timeRule',$matchingFeeConfig->getFeeTimeRule());//计费时间规则
            $this->assign('amountRule',$matchingFeeConfig->getFeeAmountRule());//用量计费规则
            $this->assign('stepRule',$matchingFeeConfig->getFeeStepRule());//阶梯计费规则
            $this->display();
        }
    }

    /**
     * 检查更新客户的参数
     * @throws \Exception
     */
    protected function checkParamsForPost($params)
    {
        if (!$params['product_id'] || !$params['fee_basis'] || !$params['fee_method'] || !$params['start_date']) {
            throw new \Exception('缺少必要参数');
        }
        if ($params['fee_method'] == 1) {
            if (!$params['fee_time_rule']) {
                throw new \Exception('请选择时间计费规则');
            }else{
                if (!$params['fee_time_price']) {
                    throw new \Exception('请填入时间计费价格');
                }else{
                    unset($params['fee_amount_rule']);
                    $params['fee_price'] = $params['fee_time_price'];
                }
            }
        }elseif($params['fee_method'] == 2){
            if (!$params['fee_amount_rule']) {
                throw new \Exception('请选择用量计费规则');
            }else{
                if($params['fee_amount_rule'] == 1){
                    if (!$params['fee_amount_price']) {
                        throw new \Exception('请填入固定用量计费价格');
                    }else{
                        unset($params['fee_time_rule']);
                        $params['fee_price'] = $params['fee_amount_price'];
                    }
                }elseif($params['fee_amount_rule'] == 2){
                    if (!$params['fee_step_price']) {
                        throw new \Exception('请填入阶梯用量计费价格');
                    }else{
                        unset($params['fee_time_rule']);
                        if($params['fee_step_price']){
                            $step_price_one = explode('|',$params['fee_step_price']);
                            foreach ($step_price_one as $key=>$value){
                                $step_price_two = explode(':',$value);
                                $new_arr[$key] = $step_price_two;
                            }
                        }
                        $params['fee_price'] = json_encode($new_arr);
                    }
                }
            }
        }else{
            throw new \Exception('错误的计费方式');
        }
        unset($params['fee_time_price']);
        unset($params['fee_amount_price']);
        unset($params['fee_step_price']);
        return $params;
    }

}
