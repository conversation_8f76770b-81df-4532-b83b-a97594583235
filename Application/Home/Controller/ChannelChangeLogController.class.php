<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CrawlerChannelRepository;
use Common\ORG\Page;

class ChannelChangeLogController extends AdminController
{
    protected $crawler_areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西', '广东',
        '广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏', '贵州', '辽宁',
        '重庆', '陕西', '青海', '黑龙江',];

    protected $crawlerChannel;
    public function _initialize()
    {
        parent::_initialize();
        $this->crawlerChannel = new CrawlerChannelRepository();
    }

    public function index()
    {
        // check params
        $where = [];
        $flow_type_list = ['10086' => '移动', '189' => '电信', '10010' => '联通'];
        $channel_list = $this->crawlerChannel->getCrawlerlList();;
        $params = array_filter(I('get.'), function ($item) {
            return trim($item);
        });

        if (isset($params['flow_type'])) {
            $where['flow_type'] = trim($params['flow_type']);

        }
        if (isset($params['area'])) {
            $where['area'] = trim($params['area']);
        }
        if (isset($params['nchannel'])) {
            $channel_a_key = $channel_list[$params['nchannel']];
            $where['_string'] = '(nchannel = "' . $params['nchannel'] . '" or nchannel like  "%' . $channel_a_key . '%")';
        }

        // created_at
        if (isset($params['begin'], $params['end'])) {
            $end = strtotime($params['end']) + 86400;
            if (isset($where['_string'])) {
                $where['_string'] .= ' and (created_at >= ' . strtotime($params['begin']) . ' and created_at <' . $end . ')';
            } else {
                $where['_string'] = 'created_at >= ' . strtotime($params['begin']) . ' and created_at <' . $end;
            }
        } elseif (isset($params['begin'])) {
            $where['created_at'] = ['egt', strtotime($params['begin'])];
        } elseif (isset($params['end'])) {
            $where['created_at'] = ['lt', strtotime($params['end']) + 86400];
        } else {
            // default time
            $timestamp_today = strtotime(date('Y-m-d'));
            $where['created_at'] = ['egt', $timestamp_today];
        }

        $total = D('ChannelSwitchLog')->where($where)->count();
        $Page = new Page($total, C('LIST_ROWS'));
        $list_show = D('ChannelSwitchLog')->where($where)->limit($Page->firstRow, $Page->listRows)->order('id desc')->select();

        $this->assign('list_show', $list_show);
        $this->assign('channel_list', $channel_list);
        $this->assign('flow_type_list', $flow_type_list);
        $this->assign('crawler_areas', $this->crawler_areas);
        $this->assign('input', I('get.'));
        $this->assign('page', $Page);
        $this->display();
    }
}
