<?php

namespace Home\Controller;

use Common\Controller\AdminController;

class HandleLogController extends AdminController
{
    /**
     * log view
     */
    public function listLog()
    {
        $this->display('list');
    }

    public function apiLog()
    {
        $log_api = C('LIST_API_URL')['backend_api_log'];
        $bill_api = C('LIST_API_URL')['back_api_bill_month'];
        $bill_day_api = C('LIST_API_URL')['back_api_bill_day_list'];
        $bill_day_log_api = C('LIST_API_URL')['back_api_bill_day_log'];
        $backend_api_stat_source_list = C('LIST_API_URL')['backend_api_stat_source_list'];
        $backend_api_stat_log = C('LIST_API_URL')['backend_api_stat_log'];
        $backend_api_crawler_tel = C('LIST_API_URL')['backend_api_crawler_tel'];


        $this->assign(compact('log_api', 'bill_api', 'bill_day_api', 'bill_day_log_api', 'backend_api_crawler_tel',
            'backend_api_stat_source_list', 'backend_api_stat_log'));
        $this->display('api_log');
    }
}
