<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\AccountCuishouChartRepository;
use Home\Repositories\CuishouStatLineChartRepository;
use Home\Repositories\CuishouStatRepository;

class CuishouStatLineChartController extends AdminController
{

    protected $cache_key = 'cuishou_line_stat';

    /*
     * 催收统计的Repository
     * */
    protected $stat_repository;

    /*
     * 折线图资源
     * */
    protected $line_chart_repository;

    /*
     * 以客户为单位的折线图资源
     * */
    protected $account_chart_repository;

    /*
     * 总计信息
     * */
    protected $total_stat;

    // 不支持依赖注入
    public function __construct()
    {
        parent::__construct();
        $this->stat_repository = new CuishouStatRepository();
        $this->line_chart_repository = new CuishouStatLineChartRepository();
        $this->account_chart_repository = new AccountCuishouChartRepository();
    }

    /**
     * 客户为单位的折线图
     */
    public function lineChartClient()
    {
        if (IS_GET) {
            // 默认的可用的客户
            $list_account = $this->account_chart_repository->getDefaultListAccount();
            $list_account = json_encode($list_account, JSON_UNESCAPED_UNICODE);

            // GET请求的参数
            $params_init = $this->account_chart_repository->tidyInitParamsForDefaultPage();
            $params_init = json_encode($params_init, JSON_UNESCAPED_UNICODE);

            $this->assign(compact('list_account', 'params_init'));
            $this->display('line_charts_client');
            exit();
        }

        try {
            // 覆盖率列表
            $list_account = $this->account_chart_repository->getCoverageList();

            $this->__Return($list_account, '', 'success');
        } catch (\Exception $e) {
            $response = [
                'msg' => $e->getMessage(),
                'status' => '7777'
            ];

            $this->__Return($response, '', 'error');
        }

    }

    /**
     * 折线图默认展示页面
     */
    public function coverageList()
    {
        // 用户列表
        $user_List = $this->stat_repository->userListDependGet();

        // GET请求的参数
        $params_init = $this->stat_repository->tidyInitParamsForDefaultPage();

        $this->assign('user_list', json_encode($user_List, JSON_UNESCAPED_UNICODE));
        $this->assign('params_init', json_encode($params_init, JSON_UNESCAPED_UNICODE));
        $this->display();
    }

    /**
     *  列表的折线图动态响应
     */
    public function ajaxForList()
    {
        $stat_data = $this->ajaxAllForList();

        // 对统计结果按照产品的这段时间的调用总量,进行排序
        $stat_data = $this->line_chart_repository->sortDescByTotalInvokingForList($stat_data);

        $this->__Return($stat_data, '', 'success');
    }


    /**
     * 折线图动态的获取覆盖率信息
     * @return  array
     */
    protected function ajaxAllForList()
    {
        $user_list = $this->stat_repository->userListDependGet();

        // 折线图横坐标的数据
        $info_series = $this->getInfoSeries($user_list);

        // 日期展示的范围
        $date_range = $this->line_chart_repository->showDateForList();

        // 用户的折线图展示
        $user_range = $this->line_chart_repository->userRangeForLegend($user_list);

        // 总量覆盖率
        $info_total = $this->line_chart_repository->totalInfoLineInfoForList($this->total_stat);

        // 总量覆盖率和各个用户的覆盖率合并
        $info_series = $this->line_chart_repository->mergeTotalAndSingleCoverage($info_total, $info_series);
        array_unshift($user_range, '总计');

        return compact('info_series', 'date_range', 'user_range');
    }


    /**
     * 横坐标的数据
     * @param array $user_list
     * @return array
     */
    protected function getInfoSeries($user_list)
    {
        $info_series = [];
        array_map(function ($user) use (&$info_series) {
            $name = $user['developer'];
            $date_info = $this->ajaxOneForList($user['id']);

            // 将数据打入$total_stat属性，为每天的总计做准备
            $this->totalStatSum($date_info);

            // 为折线图整理数据 && 二次整理结构
            $info_line = $this->line_chart_repository->tidyLineChartDataForList($date_info);

            // 注入容器
            $data = $info_line['converage_dunning'];
            $info_series['converage_dunning'][] = compact('name', 'data');
            $data = $info_line['converage_not_sure_dunning'];
            $info_series['converage_not_sure_dunning'][] = compact('name', 'data');
            $data = $info_line['converage_all'];
            $info_series['converage_all'][] = compact('name', 'data');
            $data = $info_line['coverage_both'];
            $info_series['coverage_both'][] = compact('name', 'data');
        }, $user_list);
        return $info_series;
    }

    /**
     * 将各个用户的数据累加，方便计算总体覆盖率
     * @param $date_info
     */
    protected function totalStatSum($date_info)
    {
        array_walk($date_info, function ($item, $date) {
            if (!isset($this->total_stat[$date]['success_counts'])) {
                $this->total_stat[$date] = $item;
                return '';
            }
            $this->total_stat[$date]['success_counts'] += $item['success_counts'];
            $this->total_stat[$date]['dunning_times'] += $item['dunning_times'];
            $this->total_stat[$date]['not_sure_dunning_times'] += $item['not_sure_dunning_times'];
            $this->total_stat[$date]['not_times'] += $item['not_times'];
            $this->total_stat[$date]['both_times'] += $item['both_times'];
        });
    }


    /**
     * 折线图的动态的获取一个用户的信息
     * @param integer $choose_id 用户ID
     * @return array
     */
    protected function ajaxOneForList($choose_id)
    {
        // 时间限制 && 需要展示的日期列表
        $where_date = $this->stat_repository->timeLimitForDetail();
        $date_list = $this->line_chart_repository->showDateListAsc();

        // 这段日期 各天的统计信息
        $where = [
            'uid' => new \MongoInt32($choose_id),
            'time' => $where_date
        ];
        return $this->stat_repository->dailyStat($where, $date_list);
    }

    /**
     * 统计详情页
     */
    public function cuishouDetail()
    {
        $stat_data = $this->detailNoCache();

        $this->__Return($stat_data, '', 'success');
    }

    /**
     * 列表页数据统计
     * @return array
     */
    protected function listNoCache()
    {
        $choose_id = I('get.id', '', 'trim');

        // 选择特定客户
        if ($choose_id) {
            return $this->chooseOneForList();
        }

        // 选择所有客户
        return $this->chooseAllForList();
    }

    /**
     * 列表页选择所有用户的数据统计
     * @return array
     */
    protected function chooseAllForList()
    {
        $user_list = $this->stat_repository->userListDependGet();

        // init params
        $input = I('get.');

        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // set Page show
        $count = count($user_list);
        $Page = new Page($count, C('LIST_ROWS'));

        // 所有用户的当前条件下的统计信息 && 分页
        $user_list_stat = $this->stat_repository->personalStatForList($user_list, ['time' => $where_date]);
        $user_show = $this->stat_repository->pageShowForList($user_list_stat, $Page);

        $total_data = $user_list_stat['total_stat'];

        // 当前需要展示的日期范围
        $date_range = $this->line_chart_repository->showDateForList();

        // cache && return
        $page = $Page->show();
        return compact('user_list', 'user_show', 'total_data', 'input', 'page', 'date_range');
    }

    /**
     * 列表页选择单个账户的时候的统计
     * @return array
     */
    protected function chooseOneForList()
    {

        $user_list = $this->stat_repository->userListDependGet();

        // init params
        $input = I('get.');
        $choose_id = I('get.id', '', 'trim');

        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // set Page show
        $Page = new Page(1, C('LIST_ROWS'));

        // 选中用户当前条件下的统计信息
        $where['time'] = $where_date;
        $where['uid'] = new \MongoInt32($choose_id);
        $total_data = $this->stat_repository->chooseOneStat($where);

        // show choose user
        $user_index_id = array_column($user_list, null, 'id');
        $input['developer'] = $user_index_id[$choose_id]['developer'];
        $user_show[0] = array_merge($total_data, $user_index_id[$choose_id]);

        // 当前需要展示的日期范围
        $date_range = $this->line_chart_repository->showDateForList();

        // cache && return
        $page = $Page->show();
        return compact('user_list', 'user_show', 'total_data', 'input', 'page', 'date_range');
    }

    protected function detailNoCache()
    {
        // all cuishou user
        $user_list = $this->stat_repository->userListDependGet();

        // init params
        $input = I('get.');
        $choose_id = I('get.id', '', 'trim');

        // 时间限制 && 需要展示的日期列表
        $where_date = $this->stat_repository->timeLimitForDetail();
        $date_list = $this->stat_repository->showDateList();

        // 这段日期 各天的统计信息
        $where = [
            'uid' => new \MongoInt32($choose_id),
            'time' => $where_date
        ];
        $date_stat_show = $this->stat_repository->dailyStat($where, $date_list);

        // 这段日期的数据总量
        $total_data = $this->stat_repository->chooseOneStat($where);

        // show choose user
        $user_list_show = array_column($user_list, null, 'id');
        $input['developer'] = $user_list_show[$choose_id]['developer'];

        $stat_data = compact('date_stat_show', 'total_data', 'input', 'user_list');

        return $stat_data;
    }

    // 供筛选的客户列表
    public function clientList()
    {
        $this->stat_repository->clientListForPost();
    }
}
