<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\FinanceAccountRepository;

class FinanceAccountController extends AdminController
{
    /*
     * 客户Repository
     * */
    private $repository_account;
    public function __construct()
    {
        parent::__construct();
        $this->repository_account = (new FinanceAccountRepository());
    }

    /**
     * 客户列表
     */
    public function index()
    {
        // GET方式获取客户信息 && 产品信息
        $list = $this->repository_account->getAccountWithProductForListByGet();

        // GET方式获取用户列表客户(select2使用)
        $list_account_for_select2 = $this->repository_account->accountListForGet();

        // GET 方法的参数
        $request = I('get.');

        $this->assign('list_account', $list['list_account']);
        $this->assign('list_account_for_select2', $list_account_for_select2);
        $this->assign('obj_page', $list['obj_page']);
        $this->assign('request', $request);
        $this->display();
    }

    /**
     * 添加客户
     */
    public function add()
    {
        // 创建客户页面
        if (IS_GET) {
            $list_crawler = $this->repository_account->getProductListCrawler();
            $list_matching = $this->repository_account->getProductListMatching();
            $list_cuishou = $this->repository_account->getProductListCuishou();

            $this->assign('list_crawler', $list_crawler);
            $this->assign('list_cuishou', $list_cuishou);
            $this->assign('list_matching', $list_matching);
            $this->display();
            exit();
        }

        // 创建客户
        try {
            $id = $this->repository_account->createAccount();

            //修改操作人
            $this->repository_account->updateAdminInfo($id, $this->loginuser['username']);

            $this->__Return('添加客户客户成功', '', 'success');

        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 客户编辑页面
     */
    public function edit()
    {
        // 编辑界面
        if (IS_GET) {
            // 通过ID,GET的方式获取客户的信息 && 一对多的关系
            $info_account = $this->repository_account->getAccountAndRelationByIdForGet();
            $this->assign('account', $info_account['account']);
            $this->display();
            exit();
        }

        // 更新
        try {
            // post方式通过id修改客户 && relationship
            $this->repository_account->updateAccountAndRelationshipByIdForPost();

            //修改操作人
            $id = I('post.id', '', 'trim');
            $this->repository_account->updateAdminInfo($id, $this->loginuser['username']);

            $this->__Return('更新客户成功','' , 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 客户管理编辑页面重置密码功能
     */
    public function resetPwd(){
        if (!IS_POST) {
            $id = I('get.id', 0, 'intval');

            $this->assign('id', $id);
            $this->display();
        } else {
            try {
                $id = I('post.id', 0, 'intval');
                $this->repository_account->updatePwdById($id);
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        }
    }
}
