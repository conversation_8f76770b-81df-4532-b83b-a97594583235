<?php
/**
 * Created by PhpStorm.
 * User: gang8
 * Date: 2020/6/22
 * Time: 11:40
 */

namespace Home\Controller;


use Common\Controller\AdminController;

//性能分析脚本
class PropertyController extends AdminController
{
	public function index()
	{
		define('PROPERTY_LAST', microtime());
		
		$this->showPropertyResult('整个项目加载所需时间', $this->compute(PROPERTY_FIRST, PROPERTY_LAST));
	}
	
	protected function compute($start_time, $end_time)
	{
		list($start_microsecond, $start_second) = explode(' ', $start_time);
		list($end_microsecond, $end_second) = explode(' ', $end_time);
		
		$start_millisecond = $start_second * 1000 + $start_microsecond * 1000;
		echo $start_millisecond .'<br/>';
		$end_millisecond   = $end_second * 1000 + $end_microsecond * 1000;
		echo $end_millisecond .'<br/>';
		
		return round($end_millisecond - $start_millisecond, 2);
	}
	
	protected function showPropertyResult($title, $result)
	{
		header('content-type: text/html; charset=utf-8');
		echo "<h3>{$title}</h3>";
		echo $result . 'MS';
		echo "<hr>";
		
	}
}