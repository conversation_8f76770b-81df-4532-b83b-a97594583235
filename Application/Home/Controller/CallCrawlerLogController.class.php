<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\CallCrawlerLogRepository;
use Home\Repositories\BmCrawlerRateRepository;


class CallCrawlerLogController extends AdminController
{
    protected $areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西', '广东', '广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏', '贵州', '辽宁', '重庆', '陕西', '青海', '黑龙江'];

    protected $rate;

    protected $crawler;

    public function _initialize()
    {
        parent::_initialize();
        $this->crawler = new CallCrawlerLogRepository();
        $this->rate = new BmCrawlerRateRepository();
    }

    public function index()
    {
        $input = I('get.');

        // 爬虫渠道列表
        $channel = $this->rate->getChannelList();

        $limit = 20;
        $list = $this->crawler->getCrawlerLogList($limit);

        $page = new Page($list['total'], $limit);

        $this->assign('input', $input);
        $this->assign('channel', $channel);
        $this->assign('crawler_areas', $this->areas);
        $this->assign('list', $list['list']);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function msg()
    {
        $msg = $this->crawler->getCrawlerLogDetail();

        $sid = I('get.sid', '', 'trim');

        $this->assign('sid', $sid);
        $this->assign('msg', $msg);
        $this->display();
    }

    /**
     *  修复错误
     */
    public function repair()
    {
        $user = $_SESSION['site_login_name'];
        $errorRepair = D('ErrorRepair');

        if (IS_POST) {
            try {
                $input['remark'] = I('post.remark', '', 'trim');
                $input['sid'] = I('get.sid', '', 'trim');
                $input['error_time'] = I('get.error_time', 0);
                $input['admin'] = $user;

                $errorRepair->setInfo($input);
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');

        } else {
            $repairid = I('get.repairid', 0, 'trim');
            if (!empty($repairid)) {
                $repairinfo = $errorRepair
                    ->field('admin,remark,created_at')
                    ->where(['id' => $repairid])
                    ->find();
            }

            $this->assign('repairid', $repairid);
            $this->assign('remark', $repairinfo['remark']);
            $this->assign('admin', $repairinfo['admin']);
            $this->assign('created_at', $repairinfo['created_at']);
            $this->assign('user', $user);
            $this->display();
        }
    }

    public function export()
    {
        set_time_limit(0);

        $limit = 1000;

        $list = $this->crawler->getCrawlerLogList($limit);

        $this->crawler->getCrawlerLogDownload($list, $limit);
    }

    public function locus()
    {
        try {
            $sid = I('get.sid', '', 'trim');
            if (empty($sid)) {
                throw new \Exception('sid不存在');
            }
            $this->assign('html', file_get_contents('http://172.18.52.217/trace/detail?render=quick_view&sid=' . $sid));
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }

    }
}
