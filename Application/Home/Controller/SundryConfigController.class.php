<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/14 0014
 * Time: 15:14
 */

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\ConfigRepository;

class SundryConfigController extends AdminController
{
    public function config1()
    {
        $config = new ConfigRepository(1);

        if (IS_POST) {
            $res    = $config->update();
            $status = 1;
            if (is_bool($res)) {
                if ($res) {
                    $status = 0;
                    $info   = '修改成功';
                    $this->ajaxReturn(compact('status', 'info'));
                    return;
                }
                $info = '修改失败';
                $this->ajaxReturn(compact('status', 'info'));
                return;
            }
            $info = $res;
            $this->ajaxReturn(compact('status', 'info'));
            return;
        }

        $data = $config->get();
        $editUrl = U('config1');

        $this->assign(compact('data', 'editUrl'));
        $this->display();
    }

    public function config2()
    {
        $config = new ConfigRepository(2);

        if (IS_POST) {
            $res    = $config->update();
            $status = 1;
            if (is_bool($res)) {
                if ($res) {
                    $status = 0;
                    $info   = '修改成功';
                    $this->ajaxReturn(compact('status', 'info'));
                    return;
                }
                $info = '修改失败';
                $this->ajaxReturn(compact('status', 'info'));
                return;
            }
            $info = $res;
            $this->ajaxReturn(compact('status', 'info'));
            return;
        }

        $data    = $config->get();
        $editUrl = U('config2');

        $this->assign(compact('data', 'editUrl'));
        $this->display('config1');
    }
}