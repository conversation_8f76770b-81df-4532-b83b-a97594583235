<?php

namespace Home\Controller;

use Common\Controller\AdminController;



class TestChannelController extends AdminController
{
    private $repository;
    public function __construct()
    {
        parent::__construct();
        $this->repository = D('Channel');
    }

    /**
     * 用例测试页面
     */
    public function index()
    {
        $this->display();
    }
    /**
     * 运行项目测试用例
     *
     */

    public function run_case(){
        $data = I('post.');
        $url = C('TEST_CHANNEL_URL');
        $post = [
           'num' => $data['i'],
           'channel' => $data['channel'],
           'pid' => $data['product']
        ];
        $res = $this->curlHandler($url, $post, 'http', 'post');
        if(empty($res)){
            $info = ['i'=>$data['i'], 'msg'=>'请求超时', 'phone'=>'请求超时', 'json_res'=>'请求超时'];
            $result = ['status' => 'error', 'info' => $info];
            $this->ajaxReturn($result);
        }
        $res_arr = json_decode($res, true);
        if(!isset($res_arr['status'])){
            $msg = '后台接口调用失败(与邦信分无关)';
            $phone = isset($res_arr['data']['phone']) ? $res_arr['data']['phone'] : '';
            $json_res = $res;
            $info = ['i'=>$data['i'], 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone];
            $result = ['status' => 'error', 'info' => $info];
            $this->ajaxReturn($result);
        }else{
            if(!isset($res_arr['data']['status'])){
                $msg = '邦信分接口失败';
                $phone = isset($res_arr['data']['phone']) ? $res_arr['data']['phone'] : '';
                $json_res = $res;
                $info = ['i'=>$data['i'], 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone];
                $result = ['status' => 'error', 'info' => $info];
                $this->ajaxReturn($result);
            }else{
                if($res_arr['status'] == 0 && $res_arr['data']['status'] == 0){
                    //成功
                    $msg = '请求成功';
                    $phone = isset($res_arr['data']['phone']) ? $res_arr['data']['phone'] : '';
                    $json_res = isset($res_arr['data']) ? json_encode($res_arr['data'], JSON_UNESCAPED_UNICODE) : '';
                    $info = ['i'=>$data['i'], 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone];
                    $result = ['status' => 'ok', 'info' => $info];
                    $this->ajaxReturn($result);
                }else if($res_arr['status'] == 0 && $res_arr['data']['status'] != 0){
                    $msg = '邦信分接口失败';
                    $phone = isset($res_arr['data']['phone']) ? $res_arr['data']['phone'] : '';
                    $json_res = isset($res_arr['data']) ? json_encode($res_arr['data'], JSON_UNESCAPED_UNICODE) : '';
                    $info = ['i'=>$data['i'], 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone];
                    $result = ['status' => 'error', 'info' => $info];
                    $this->ajaxReturn($result);
                }else if($res_arr['status'] != 0){
                    $msg = '后台接口调用失败(与邦信分无关)';
                    $phone = isset($res_arr['data']['phone']) ? $res_arr['data']['phone'] : '';
                    $json_res = isset($res_arr['data']) ? json_encode($res_arr['data'], JSON_UNESCAPED_UNICODE) : '';
                    $info = ['i'=>$data['i'], 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone];
                    $result = ['status' => 'error', 'info' => $info];
                    $this->ajaxReturn($result);
                }
            }
        }
    }
    /**
     * 判断是否是json
     */
    protected function isJson($data)
    {
        $res = @json_decode($data, true);
        if (is_null($res)) {
            return false;
        }
        return true;
    }
    /**
     * 远程调用url
     */
    private function curlHandler($request_url, $post_data = array(), $type = 'http', $method = 'get', $json = false, $inner = false)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $request_url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        if($type == 'https')
        {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
            //curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, true);  // 从证书中检查SSL加密算法是否存在
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if($method == 'post'){
            curl_setopt($ch, CURLOPT_POST, 1);
            if($json){
                $headers = ['Content-Type: application/json;charset=utf-8'];
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            }else{
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
            }
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        $output = curl_exec($ch);
        if($inner){
            return $output;
        }
        $err = curl_errno($ch);
        $httpCode = curl_getinfo($ch,CURLINFO_HTTP_CODE);
        if($err == 0 && $httpCode == 200){
            return $output;
        }else{
            return false;
        }
    }
    /**
     * 生成签名算法
     */
    private function getSign($apikey = '', $secret = '', $nonce = '', $timestamp = '')
    {
        if(!$apikey || !$secret || !$nonce || !$timestamp){
            return false;
        }
        $tmpArr = array($timestamp, $apikey, $secret, $nonce);
        sort($tmpArr, SORT_STRING);
        $signature = sha1( implode( $tmpArr ) );
        return $signature;
    }

}
