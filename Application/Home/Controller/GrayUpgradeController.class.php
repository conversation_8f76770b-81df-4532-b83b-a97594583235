<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\GrayUpgradeRepository;

class GrayUpgradeController extends AdminController
{
    private $repository_gray_upgrade;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_gray_upgrade = new GrayUpgradeRepository();
    }

    /**
     * 列表
     */
    public function index()
    {
        if (IS_GET) {
            $this->display();
            exit();
        }

        // 带参数的请求
        try {
            $list_upgrade = $this->repository_gray_upgrade->getGrayUpgradeList();

            $response = [
                'success' => true,
                'msg' => '查询成功',
                'data' => $list_upgrade
            ];

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'msg' => $e->getMessage()
            ];
        }

        $this->ajaxReturn($response);
    }

    /**
     * 添加客户
     */
    public function add()
    {
        if (IS_GET) {
            $this->display();
            exit();
        }
        try {
            // 新增版本
            $this->repository_gray_upgrade->add();
            $response = [
                'success' => true,
                'msg' => '添加成功'
            ];

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'msg' => $e->getMessage()
            ];
        }

        $this->ajaxReturn($response);
    }

    /**
     * 查看但是不可以编辑
     */
    public function showMsg()
    {
        // 获取详细的信息
        try {
            $upgrade_special = $this->repository_gray_upgrade->getSpecialMsg();

            $upgrade_special = json_encode($upgrade_special, JSON_UNESCAPED_UNICODE);
            $this->assign('upgrade_special', $upgrade_special);
            $this->display();
        } catch (\Exception $e) {
            var_dump(I('get.'));
            var_dump($e->getMessage());
        }

    }
}