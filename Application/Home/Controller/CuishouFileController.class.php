<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CuishouRepository;
use Home\Repositories\CuishouFileRepository;

class CuishouFileController extends AdminController
{
    //  统计的repository
    protected $stat_repository;

    // 生成文件的repository
    protected $stat_file_repository;

    public function __construct()
    {
        parent::__construct();
        $this->stat_repository = (new CuishouRepository());
        $this->stat_file_repository = (new CuishouFileRepository());
        set_time_limit(600);
    }

    public function index()
    {
        // 获取统计信息
        $stat_list = $this->listNoCache();
        $file_name = RUNTIME_PATH . 'Cache/cuishou_list.csv';

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->stat_file_repository->genTempFileListForRequest($stat_list, $file_name);

        //为fileDownload插件生成文件
        $this->stat_file_repository->genFileForFileDownload($file_name);
        exit();
    }

    /**
     *  获取列表数据
     * @return array
     */
    protected function listNoCache()
    {
        // 选择所有客户
        return $this->chooseAllForList();
    }

    /**
     * 列表页选择所有用户的数据统计
     * @return array
     */
    protected function chooseAllForList()
    {
        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // 所有用户的当前条件下的统计信息 && 分页
        return $this->stat_repository->getCuishouForList($where_date);
    }

    /**
     * 列表页选择单个账户的时候的统计
     * @return array
     */
    protected function chooseOneForList()
    {

        $user_list = $this->stat_repository->userListDependGet();

        // init params
        $input = I('get.');
        $choose_id = I('get.id', '', 'trim');

        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // 选中用户当前条件下的统计信息
        $where['time'] = $where_date;
        $where['uid'] = new \MongoInt32($choose_id);
        $total_data = $this->stat_repository->chooseOneStat($where);

        // show choose user
        $user_index_id = array_column($user_list, null, 'id');
        $input['developer'] = $user_index_id[$choose_id]['developer'];
        $user_show[0] = array_merge($total_data, $user_index_id[$choose_id]);

        return $user_show;
    }

    /**
     * 详情页获取数据
     * @return mixed
     */
    protected function detailNoCache()
    {
        // init params
        $choose_id = I('get.id', '', 'trim');

        // 时间限制 && 需要展示的日期列表
        $where_date = $this->stat_repository->timeLimitForDetail();
        $date_list = $this->stat_repository->showDateList();

        // 这段日期 各天的统计信息
        $where = [
            'uid' => new \MongoInt32($choose_id),
            'time' => $where_date
        ];
        return $this->stat_repository->dailyStat($where, $date_list);
    }
}
