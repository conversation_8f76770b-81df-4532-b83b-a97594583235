<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Model\ChannelInterfaceModel;
use Home\Model\ChannelModel;
use Home\Model\ChannelProductModel;
use Home\Model\ConfigPriceInterfaceModel;
use Home\Repositories\UpstreamRepository;
use Home\Model\UpstreamChannelPriceModel;
use Account\Model\ProductModel;
use Home\Model\UpstreamChannelModel;

class InterfacePriceController extends AdminController
{
	private $repository;
	private $upstream_channel_price_model;
	private $product_model;
	private $upstream_channel_model;
	
	/**
	 * UpstreamController constructor.
	 */
	public function __construct()
	{
		parent::__construct();
		$this->repository                   = new UpstreamRepository();
		$this->upstream_channel_price_model = new UpstreamChannelPriceModel();
		$this->product_model                = new ProductModel();
		$this->upstream_channel_model       = new UpstreamChannelModel();
	}

    /**
     * 快捷版210计费配置
     *
     */
    public function bxfConfig()
    {
        $fatherId = 210;

        //获取所有渠道
        $channels = ChannelProductModel::getChannelByPid($fatherId);

        $this->assign('channels', $channels);
        $this->assign('pid', $fatherId);

        $this->display("priceConfig");
    }

	/**
	 * 邦秒验计费配置
	 *
	 */
	public function bmyNewConfig()
	{
	    $fatherId = 200;

	    //获取所有渠道
        $channels = ChannelProductModel::getChannelByPid($fatherId);

		$this->assign('channels', $channels);
        $this->assign('pid', $fatherId);

		$this->display("priceConfig");
	}

	public function getInterfaceWithChannel()
    {
        $data = I('post.');
        $fatherId = $data['father_id'];

        //获取所有渠道
        $channels = ChannelProductModel::getChannelByPid($fatherId);
        $channelsId = array_column($channels, 'cid');
        $interfacesRow = ChannelInterfaceModel::getInterfaceByCids($channelsId);
        //通过渠道 将 接口分组
        $interfaces = [];
        foreach ( $interfacesRow as $v ){
            $interfaces[$v['cid']][] = $v;
        }

        $result = ['status'=>'ok', 'msg'=>'success', "data"=>$interfaces];
        $this->ajaxReturn($result);
    }

    /**
     * 获取接口计费配置列表
     */
    public function getInterfacePriceList()
    {
        $data = I('post.');
        $fatherId = $data['father_id'];
        $interfaceId = $data['interface_id'];

        if( !$interfaceId ){
            //获取该产品下的所有渠道
            $channels = ChannelProductModel::getChannelByPid($fatherId);
            $channelsId = array_column($channels, 'cid');

            //根据 渠道id 获取渠道下的所有接口
            $channelInterface = ChannelInterfaceModel::getInterfaceByCids($channelsId);
            $interfacesId = array_column($channelInterface, 'id');
        }else{
            $interfacesId[] = $interfaceId;
        }

        //获取该产品下的 所有计费配置
        $interfacePriceList = ConfigPriceInterfaceModel::getPriceConfigListByIids($interfacesId);

        //格式化 价格
        foreach ( $interfacePriceList as $k=>$v ){
            $interfacePriceList[$k]['CMCC'] = $this->getOperatorPrice('CMCC', $v['price']);
            $interfacePriceList[$k]['CUCC'] = $this->getOperatorPrice('CUCC', $v['price']);
            $interfacePriceList[$k]['CTCC'] = $this->getOperatorPrice('CTCC', $v['price']);
        }

        $result = ['status'=>'ok', 'msg'=>'success', "data"=>$interfacePriceList];
        $this->ajaxReturn($result);
    }

    /**
     * 根据id获取计费配置详情
     */
	public function getPriceConfigById()
    {
        $id = I("id", 0);

        $data = ConfigPriceInterfaceModel::getPriceConfigById($id);
        if( !$data ){
            $result = ['status'=>'error', 'msg'=>'获取计费配置失败', "data"=>$data];
            $this->ajaxReturn($result);
        }else{
            $data['date'] = date('Y-m-d', strtotime($data['date']));
        }

        $result = ['status'=>'ok', 'msg'=>'success', "data"=>$data];
        $this->ajaxReturn($result);
    }

	private function getOperatorPrice($operator, $priceConfig)
    {
        $result = '';

        $priceConfig = json_decode($priceConfig, true);
        foreach ( $priceConfig as $v ){
            if( $v['operator'] == $operator || $v['operator'] == 'ALL' ){
                if( $v['encrypt_way'] == 'ALL' ){
                    $result .= $v['price'];
                    break;
                }else{
                    $encryptLabel = $v['encrypt_way'] == 'CLEAR' ? "明文" : $v['encrypt_way'];
                    $result .= $encryptLabel.':'.$v['price']."<br>";
                }
            }
        }

        return $result;
    }

    /**
     * 添加计费配置
     *
     */
	public function addInterfacePrice()
    {
        $data = I('post.');
        if( !$data ){
            $result = ['status' => 'error', 'msg' => '参数为空'];
            $this->ajaxReturn($result);
        }

        $fatherId = $data['father_id'];
        $interfaceId = $data['interface_id'];
        $date = str_replace('-','',  $data['date']);
        $priceConfigCheck = $this->checkPriceConfig( $data['price_config'] );
        if( !$priceConfigCheck ){
            $result = ['status' => 'error', 'msg' => '计费配置有误，同一运营商和同一渠道有重复'];
            $this->ajaxReturn($result);
        }
        $priceConfig = json_encode($data['price_config']);

        //同一接口，同一日期，不能有两条计费配置
        $same = ConfigPriceInterfaceModel::getPriceConfigByIdAndDate($interfaceId, $date);
        if( $same ){
            $result = ['status' => 'error', 'msg' => '该接口在相同日期下 已经配置，请勿重复添加'];
            $this->ajaxReturn($result);
        }

        //添加计费配置
        $add['iid'] = $interfaceId;
        $add['date'] = $date;
        $add['price'] = $priceConfig;
        $add['father_id'] = $fatherId;
        ConfigPriceInterfaceModel::addConfig($add);

        $result = ['status' => 'ok', 'msg' => '成功'];
        $this->ajaxReturn($result);
    }

    public function deleteInterfacePrice()
    {
        $data = I('post.');
        if( !$data ){
            $result = ['status' => 'error', 'msg' => '参数为空'];
            $this->ajaxReturn($result);
        }

        $id = $data['id'];

        //添加计费配置
        $update['delete_time'] = time();
        ConfigPriceInterfaceModel::updateConfig($id, $update);

        $result = ['status' => 'ok', 'msg' => '成功'];
        $this->ajaxReturn($result);
    }

    /**
     * 编辑计费配置
     *
     */
    public function editInterfacePrice()
    {
        $data = I('post.');
        if( !$data ){
            $result = ['status' => 'error', 'msg' => '参数为空'];
            $this->ajaxReturn($result);
        }

        $id = $data['id'];
        $interfaceId = $data['interface_id'];
        $date = str_replace('-','',  $data['date']);
        $priceConfigCheck = $this->checkPriceConfig( $data['price_config'] );
        if( !$priceConfigCheck ){
            $result = ['status' => 'error', 'msg' => '计费配置有误，同一运营商和同一渠道有重复'];
            $this->ajaxReturn($result);
        }
        $priceConfig = json_encode($data['price_config']);

        //同一接口，同一日期，不能有两条计费配置
        $same = ConfigPriceInterfaceModel::getPriceConfigByIdAndDate($interfaceId, $date);
        if( $same ){
            $result = ['status' => 'error', 'msg' => '该接口在相同日期下 已经配置，请勿重复添加'];
            $this->ajaxReturn($result);
        }

        //添加计费配置
        $update['date'] = $date;
        $update['price'] = $priceConfig;
        ConfigPriceInterfaceModel::updateConfig($id, $update);

        $result = ['status' => 'ok', 'msg' => '成功'];
        $this->ajaxReturn($result);
    }

    private function checkPriceConfig($priceConfig)
    {
        //同一运营商同一加密方式 不能重复
        $operatorEncrypt = [];
        foreach ( $priceConfig as $v ){
            $operatorEncryptItem = $v['operator'].'_'.$v['encrypt_way'];
            if( in_array($operatorEncryptItem, $operatorEncrypt) ){
                return false;
            }else{
                $operatorEncrypt[] = $operatorEncryptItem;
            }
        }

        return true;
    }
}
