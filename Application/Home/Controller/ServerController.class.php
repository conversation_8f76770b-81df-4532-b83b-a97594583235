<?php

namespace Home\Controller;

use Common\Controller\AdminController;


class ServerController extends AdminController
{
    private $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = D('Server');
    }

    public function index()
    {
        $get = I('get.');
        $post = I('post.');
        if(isset($post['search_id']) && $post['search_id'] == 1){
            $input = $where = [];
            $input['ip_search'] = $post['ip_search'];
            $input['product_search'] = $post['product_search'];
            $input['owner_search'] = $post['owner_search'];
            $input['type_search'] = $post['type_search'];
            if(!empty($input['ip_search'])){
                $where['ip'] = trim($input['ip_search']);
            }
            if(!empty($input['product_search'])){
                $where['product'] = $input['product_search'];
            }
            if(!empty($input['owner_search'])){
                $where['owner'] = $input['owner_search'];
            }
            $where['type'] = $input['type_search'];

            $count = $this->repository->where($where)->count();
            $Page  = new \Common\ORG\Page($count, 500);
            $data = $this->repository->where($where)->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();
            if(!empty($data)){
                foreach($data as $key=>&$value){
                    switch ($value['type'])
                    {
                        case 0:
                            $type_name = '未知';
                            break;
                        case 1:
                            $type_name = '深圳线上';
                            break;
                        case 2:
                            $type_name = '北京线上';
                            break;
                        case 3:
                            $type_name = '无重要项目的线上机器';
                            break;
                    }
                    $value['type_name'] = $type_name;
                    switch ($value['status'])
                    {
                        case 0:
                            $status_name = '未知';
                            break;
                        case 1:
                            $status_name = '正常';
                            break;
                        case -1:
                            $status_name = '回收';
                            break;
                    }
                    $value['status_name'] = $status_name;

                }
                $res = ['status'=>'ok', 'msg'=>'请求成功', 'data'=>$data];
            }else{
                $res = ['status'=>'error', 'msg'=>'未查到符合条件的数据', 'data'=>[]];
            }
            $this->ajaxReturn($res);
        }

        $input = $where = [];
        $input['ip_search'] = $get['ip_search'];
        $input['product_search'] = $get['product_search'];
        $input['owner_search'] = $get['owner_search'];
        $where['type'] = 1;
        if(!empty($input['ip_search'])){
            $where['ip'] = trim($input['ip_search']);
        }
        if(!empty($input['product_search'])){
            $where['product'] = $input['product_search'];
        }
        if(!empty($input['owner_search'])){
            $where['owner'] = $input['owner_search'];
        }

        $count = $this->repository->where($where)->count();
        $Page  = new \Common\ORG\Page($count, 500);
        $data = $this->repository->where($where)->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();

        $this->assign(['input'=>$input]);
        $this->assign(['data'=>$data]);
        $this->display();
    }

    public function add()
    {
        $data = I('post.');
        $arr = [
            'type' => $data['type'],
            'status' => $data['status'],
            'ip' => trim($data['ip']),
            'real_name' => $data['real_name'],
            'name' => $data['name'],
            'cpu' => $data['cpu'],
            'mem' => $data['mem'],
            'disk_root' => $data['disk_root'],
            'disk' => $data['disk'],
            'owner' => $data['owner'],
            'useto' => $data['useto'],
            'product' => $data['product'],
            'notes' => $data['notes'],
            'father_id'=> $data['father_id'],
        ];
        $find_one = $this->repository->where(['ip'=>$arr['ip']])->find();
        if(!empty($find_one)){
            $result = ['status' => 'error', 'msg' => '该ip已存在'];
            $this->ajaxReturn($result);
        }
        $res = $this->repository->add($arr);
        if($res){
            $data = $this->repository->where(['id'=>$res])->find();
            switch ($data['type'])
            {
                case 0:
                    $type_name = '未知';
                    break;
                case 1:
                    $type_name = '深圳线上';
                    break;
                case 2:
                    $type_name = '北京线上';
                    break;
                case 3:
                    $type_name = '无重要项目的线上机器';
                    break;
            }
            $data['type_name'] = $type_name;
            switch ($data['status'])
            {
                case 0:
                    $status_name = '未知';
                    break;
                case 1:
                    $status_name = '正常';
                    break;
                case -1:
                    $status_name = '回收';
                    break;
            }
            $data['status_name'] = $status_name;
            $result = ['status' => 'ok', 'msg' => '处理成功','data'=>$data];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '处理失败'];
        $this->ajaxReturn($result);
    }

    public function getEdit()
    {
        $data = I('post.');
        $id = $data['id2'];
        $find_one = $this->repository->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'ok', 'msg' => '成功', 'data'=>$find_one];
        $this->ajaxReturn($result);
    }

    public function edit()
    {
        $data = I('post.');
        $id = $data['id2'];
        if(empty($id)){
            $result = ['status' => 'error', 'msg' => 'id不能为空'];
            $this->ajaxReturn($result);
        }
        $arr = [
            'type' => $data['type2'],
            'status' => $data['status2'],
            'ip' => trim($data['ip2']),
            'real_name' => $data['real_name2'],
            'name' => $data['name2'],
            'cpu' => $data['cpu2'],
            'mem' => $data['mem2'],
            'disk_root' => $data['disk_root2'],
            'disk' => $data['disk2'],
            'owner' => $data['owner2'],
            'useto' => $data['useto2'],
            'product' => $data['product2'],
            'notes' => $data['notes2'],
            'father_id' => $data['father_id'],
        ];
        $find_one = $this->repository->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }
        //判断ip是否有重复
        $find_where = [];
        $find_where['ip'] = $arr['ip'];
        $find_where['id'][] = array('NEQ', $id);
        $find_one2 = $this->repository->where($find_where)->find();
        if(!empty($find_one2)){
            $result = ['status' => 'error', 'msg' => '该ip已存在'];
            $this->ajaxReturn($result);
        }

        $res = $this->repository->where(['id'=>$id])->save($arr);
        if($res){
            $data = $this->repository->where(['id'=>$id])->find();
            switch ($data['type'])
            {
                case 0:
                    $type_name = '未知';
                    break;
                case 1:
                    $type_name = '深圳线上';
                    break;
                case 2:
                    $type_name = '北京线上';
                    break;
                case 3:
                    $type_name = '无重要项目的线上机器';
                    break;
            }
            $data['type_name'] = $type_name;
            switch ($data['status'])
            {
                case 0:
                    $status_name = '未知';
                    break;
                case 1:
                    $status_name = '正常';
                    break;
                case -1:
                    $status_name = '回收';
                    break;
            }
            $data['status_name'] = $status_name;
            $result = ['status' => 'ok', 'msg' => '编辑成功', 'data'=>$data];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败', 'data'=>[]];
        $this->ajaxReturn($result);
    }

    public function del()
    {
        $data = I('post.');
        $id = $data['id'];
        if(empty($id)){
            $result = ['status' => 'error', 'msg' => 'id不能为空'];
            $this->ajaxReturn($result);
        }
        $arr = [
            'status' => '-1'
        ];
        $find_one = $this->repository->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }
        $res = $this->repository->where(['id'=>$id])->save($arr);
        if($res){
            $result = ['status' => 'ok', 'msg' => '删除成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '删除失败'];
        $this->ajaxReturn($result);
    }
}
