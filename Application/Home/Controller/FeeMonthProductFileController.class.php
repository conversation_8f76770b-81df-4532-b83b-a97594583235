<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\FeeMonthProductRepository;
use Home\Repositories\FeeMonthProductFileRepository;

class FeeMonthProductFileController extends AdminController
{
    protected $feeProduct;
    protected $feeFile;
    public function _initialize()
    {
        parent::_initialize();
        $this->feeProduct = new FeeMonthProductRepository();
        $this->feeFile = new FeeMonthProductFileRepository();
    }

    public function index()
    {
        $where = $this->feeProduct->getFeeProductParam();
        $list = $this->feeProduct->getFeeProductList($where, 0, 0);
        $this->feeFile->getFeeMonthProductDownload($list);
    }

    public function detail()
    {
        $where = $this->feeProduct->getFeeProductParam();
        $list = $this->feeProduct->getFeeProductDetail($where);
        $this->feeFile->getFeeMonthDetailDownload($list);
    }

    public function customer()
    {
        $where = $this->feeProduct->getFeeProductParam();
        $list = $this->feeProduct->getFeeProductCustomer($where, 0, 0);
        $this->feeFile->getFeeMonthCustomerDownload($list);
    }
}