<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\Model\CuishouUserModel;
use Common\Model\CuishouIdsModel;
use Common\ORG\Page;
use Home\Repositories\CuishouRepository;
use Home\Repositories\FinanceAccountRepository;
use Common\Model\FeeCuishouConfigModel;

class CuishouController extends AdminController
{
    private $repository_cuishou;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_cuishou = new CuishouRepository();
    }

    public function index()
    {
        // init params
        $CuishouUser = new CuishouUserModel();
        $input = I('get.');
        $status = I('get.status', '', 'trim');
        $choose_id = I('get.user_id', '', 'trim');
        $user_id = I('get.id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');
        $contract_status = I('get.contract_status', '', 'trim');
        $begin = I('get.begin', '', 'trim');
        $begin_e = I('get.begin_e', '', 'trim');
        $end = I('get.end', '', 'trim');
        $end_e = I('get.end_e', '', 'trim');

        // check status, user
        $where = $status ? ['status' => new \MongoInt32($status)] : [];
        $where = $apikey ? (compact('apikey') + $where) : $where;

        //  输入的id冲突，拿不到数据
        if ($user_id && $choose_id && $choose_id != $user_id) {
            $where['id'] = 'what happened';
        } elseif ($user_id) {
            $where['id'] = new \MongoInt32($user_id);
        } elseif ($choose_id) {
            $where['id'] = new \MongoInt32($choose_id);
        }
        //检索签约状态、开通时间、到期时间
        if ($contract_status){
            $where['contract_status'] = new \MongoInt32($contract_status);
        }

        if ($begin && $begin_e){
            $where['created_at'] = ['between', [new \MongoInt32(strtotime($begin)), new \MongoInt32(strtotime($begin_e.' 23:59:59'))]];
        }elseif($begin){
            $where['created_at'] = ['egt', new \MongoInt32(strtotime($begin))];
        }elseif($begin_e){
            $where['created_at'] = ['elt', new \MongoInt32(strtotime($begin_e.' 23:59:59'))];
        }
        if ($end && $end_e){
            $where['validuntil'] = ['between', [new \MongoInt32(strtotime($end)), new \MongoInt32(strtotime($end_e.' 23:59:59'))]];
        }elseif($end){
            $where['validuntil'] = ['egt', new \MongoInt32(strtotime($end))];
        }elseif ($end_e){
            $where['validuntil'] = ['elt', new \MongoInt32(strtotime($end_e.' 23:59:59'))];
        }

        // Page Object
        if ($choose_id || $user_id || $apikey) {
            $count = 1;
        } else {
            $count = $CuishouUser->where($where)->count();
        }
        $Page = new Page($count, C('LIST_ROWS'));

        $lists = $CuishouUser
            ->field(['_id' => 0])
            ->where($where)
            ->limit($Page->firstRow, $Page->listRows)
            ->order('_id desc')
            ->select();

        //查询账号关联所属用户信息
        $account_product_infos = D('FinanceAccountProduct')->getAccountIdsByProductIds(array_column($lists, 'id'), 3);
        foreach ($lists as $k=>$item){
            if ($account_product_infos[$item['id']]){
                $lists[$k]['account_product'] = $account_product_infos[$item['id']];
            }
        }

        // all user
        $user_list = $CuishouUser
            ->field(['_id' => 0, 'id', 'developer'])
            ->order('_id desc')
            ->select();

        // user info which is choosed
        if ($choose_id) {
            $user_column = array_column($user_list, null, 'id');
            $input['developer'] = $user_column[$choose_id]['developer'];
        }

        $this->assign('input', $input);
        $this->assign('lists', $lists);
        $this->assign('user_list', $user_list);
        $this->assign('page', $Page->show());
        $this->assign('contract_status', $CuishouUser->getContractStatus());
        $this->display();
    }

    /**
     * 编辑账户
     */
    public function setInfo()
    {
        $CuishouUser = new CuishouUserModel();
        if (IS_GET) {
            $list = [];
            $id = I('get.id', 0, 'intval');
            if ($id) {
                $list = $CuishouUser
                    ->where(['id' => $id])
                    ->find();
            }
            // GET方式获取用户列表客户(select2使用)
            $repository_account = new FinanceAccountRepository();
            $list_account_for_select2 = $repository_account->accountListForGet();
            //查询产品关联的用户信息
            $account_product_info = D('FinanceAccountProduct')->getAccountIdByProductId($id, 3);

            $this->assign('list_account_for_select2', $list_account_for_select2);
            $this->assign('account_product_info', $account_product_info);
            $this->assign('list', $list);
            $this->display();
        } else {
            try {
                $id = I('get.id', '', 'intval');
                $data = I('post.');
                $account_name = $data['name'];
                unset($data['name']);

                // filter $_POST
                $data = $this->repository_cuishou->filterInfo($data);

                // change type before update
                $data['id'] = new \MongoInt32($data['id']);
                $data['updated_at'] = new \MongoInt32(time());

                if (isset($data['itag_vip_queue'])) {
                    $data['itag_vip_queue'] = boolval($data['itag_vip_queue']);
                }

                $where = [
                    'id' => $data['id']
                ];

                // update
                $result = $CuishouUser->updateInfo(['$set' => $data], $where);
                if ($result === false) {
                    throw new \Exception('更新失败');
                }
                // save account product
                D('FinanceAccountProduct')->updateBindToAccounts($id, $account_name, 3);
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        }
    }

    public function addAccount()
    {
        if (IS_POST) {
            $CuishouUser = new CuishouUserModel();
            $data = $input = I('post.');
            $account_name = $data['name'];
            unset($data['name']);
            try {
                // filter $_POST
                $data = $this->repository_cuishou->filterInfo($data);

                // 设置默认的API配置
                $data = $this->repository_cuishou->setDefaultConfigForInsert($data);

                // change type before insert
                $data['id'] = new \MongoInt32((new CuishouIdsModel())->getNextSequence());
                $data['created_at'] = new \MongoInt32(time());

                if (isset($data['itag_vip_queue'])) {
                    $data['itag_vip_queue'] = boolval($data['itag_vip_queue']);
                }

                // insert
                $account_result = $CuishouUser->add($data);
                if (!$account_result) {
                    throw new \Exception('添加失败');
                }
                //绑定所属客户
                if (!empty($account_name)){
                    $account_id = get_object_vars($data['id']);
                    $id = $account_id['value'];
                    D('FinanceAccountProduct')->bindToAccounts($id, $account_name, 3);
                }
            } catch (\Exception $e) {
                $this->__Return($e->getMessage());
            }
            $this->__Return('操作成功', '', 'tip_success');
        } else {
            // GET方式获取用户列表客户(select2使用)
            $repository_account = new FinanceAccountRepository();
            $list_account_for_select2 = $repository_account->accountListForGet();

            $this->assign('list_account_for_select2', $list_account_for_select2);
            $this->display();
        }
    }

    public function apiConfig()
    {
        try {
            if (IS_GET) {
                $api_config_info = $this->repository_cuishou->getApiConfig();
                $id = I('get.id', '', 'trim');

                $this->assign('id', $id);
                $this->assign('api_config_info', $api_config_info);
                $this->display();
                exit();
            }

        } catch (\Exception $e) {
            exit(json_encode(['msg' => $e->getMessage()]));

        }

        // 更新配置
        try {
            $this->repository_cuishou->updateApiConfig();

            $response = [
                'status' => 0,
                'msg' => '更新成功'
            ];

        } catch (\Exception $e) {
            $response = [
                'status' => 9999,
                'msg' => '更新失败，请稍后再试',
                'error' => $e->getMessage()
            ];
        }
        $this->ajaxResponse($response);
    }

    /**
     * 催收分计费配置
     * @throws \Exception
     */
    public function configFee(){
        $cuishouFeeConfig = new FeeCuishouConfigModel();
        if (IS_POST) {
            $post = $input = I('post.');
            try {
                $data = $this->checkParamsForPost($post);
                $cuishouFeeConfig->startTrans();
                if($data['id']){
                    $info = $cuishouFeeConfig->find($data['id']);
                    try {
                        $params_delete = ['is_delete'=>1];
                        $result_delete = $cuishouFeeConfig->where('product_id='.$info['product_id'])->save($params_delete);
                        if ($result_delete === false) {
                            throw new \Exception('操作失败，请刷新后再试');
                        }
                    } catch (\Exception $e) {
                        throw new \Exception($e->getMessage());
                    }
                    unset($data['id']);
                }
                $data['create_time'] = time();
                $data['is_delete'] = 0;
                if (!$cuishouFeeConfig->add($data)) {
                    throw new \Exception('操作失败');
                }else{
                    $cuishouFeeConfig->commit();
                }
                $this->__Return('操作成功', '', 'tip_success');
            } catch (\Exception $e) {
                $this->__Return('操作失败', $e->getMessage(), 'tip_error');
            }
            exit;
        } else {
            $cuishouUser = new CuishouUserModel();
            $list = [];
            $product_id = I('get.id', 0, 'intval');
            if ($product_id) {
                $list = $cuishouUser
                    ->where(['id' => $product_id])
                    ->find();
                $info = $cuishouFeeConfig
                    ->where(['product_id' => $product_id,'is_delete'=>0])
                    ->order('id DESC')
                    ->find();
            }else{
                $this->__Return('缺少必要参数', '', 'tip_error');
            }
            if($info && $info['fee_amount_rule']==2){
                $info['fee_price'] = json_decode($info['fee_price'],true);
            }
            $this->assign('list', $list);
            $this->assign('info', $info);
            $this->assign('feeBasis',$cuishouFeeConfig->getFeeBasis());//计费依据
            $this->assign('feeMethod',$cuishouFeeConfig->getFeeMethod());//计费方式
            $this->assign('timeRule',$cuishouFeeConfig->getFeeTimeRule());//计费时间规则
            $this->assign('amountRule',$cuishouFeeConfig->getFeeAmountRule());//用量计费规则
            $this->assign('stepRule',$cuishouFeeConfig->getFeeStepRule());//阶梯计费规则
            $this->display();
        }
    }

    /**
     * 检查更新客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsForPost($params)
    {
        if (!$params['product_id'] || !$params['fee_basis'] || !$params['fee_method'] || !$params['start_date']) {
            throw new \Exception('缺少必要参数');
        }
        if ($params['fee_method'] == 1) {
            if (!$params['fee_time_rule']) {
                throw new \Exception('请选择时间计费规则');
            }else{
                if (!$params['fee_time_price']) {
                    throw new \Exception('请填入时间计费价格');
                }else{
                    unset($params['fee_amount_rule']);
                    $params['fee_price'] = $params['fee_time_price'];
                }
            }
        }elseif($params['fee_method'] == 2){
            if (!$params['fee_amount_rule']) {
                throw new \Exception('请选择用量计费规则');
            }else{
                if($params['fee_amount_rule'] == 1){
                    if (!$params['fee_amount_price']) {
                        throw new \Exception('请填入固定用量计费价格');
                    }else{
                        unset($params['fee_time_rule']);
                        $params['fee_price'] = $params['fee_amount_price'];
                    }
                }elseif($params['fee_amount_rule'] == 2){
                    if (!$params['fee_step_price']) {
                        throw new \Exception('请填入阶梯用量计费价格');
                    }else{
                        unset($params['fee_time_rule']);
                        if($params['fee_step_price']){
                            $step_price_one = explode('|',$params['fee_step_price']);
                            foreach ($step_price_one as $key=>$value){
                                $step_price_two = explode(':',$value);
                                $new_arr[$key] = $step_price_two;
                            }
                        }
                        $params['fee_price'] = json_encode($new_arr);
                    }
                }
            }
        }else{
            throw new \Exception('错误的计费方式');
        }
        unset($params['fee_time_price']);
        unset($params['fee_amount_price']);
        unset($params['fee_step_price']);
        return $params;
    }
}
