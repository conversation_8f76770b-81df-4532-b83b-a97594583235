<?php
/**
 * @Author: Administrator
 * @Date:   2018-07-16 14:06:16
 */
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\ChannelConfigRepository;
use Common\ORG\Page;

class CrawlerConfigController extends AdminController
{
    protected $flow_map = ['移动' =>'10086', '联通' => '10010', '电信' => '189'];
    protected $crawler_areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西', '广东','广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏', '贵州', '辽宁','重庆', '陕西', '青海', '黑龙江'];
    //渠道名称
    protected $channel_list = ['营业厅', '商城', 'wap', 'app'];
    //爬虫种类 1 爬虫 2 重置密码
    protected $channel_type = 1;
    protected $channel_config;

    public function _initialize()
    {
        parent::_initialize();
        $this->channel_config = new ChannelConfigRepository();
    }
    //列表
    public function index()
    {
        $input = I('get.');
        //查询条件
        $where = $this->channel_config->getChannelConfigParam($this->channel_type);
        $count = $this->channel_config->getChannelConfigNum($where);
        //分页
        $page = new Page($count, C('LIST_ROWS'));

        $list = $this->channel_config->getChannelConfigList($where, $page->firstRow, $page->listRows);

        $this->assign('input', $input);
        $this->assign('list', $list);
        $this->assign('crawler_areas', $this->crawler_areas);
        $this->assign('flow_map', $this->flow_map);
        $this->assign('channel_list', $this->channel_list);
        $this->assign('page', $page->show());
        $this->display();
    }

    //添加
    public function add()
    {
        if (!IS_POST) {
            $crawler_list = $this->getCrawlerListAll();

            $this->assign('crawler_list', $crawler_list);
            $this->assign('channel_list', $this->channel_list);
            $this->display();
            exit;
        }

        try {
            $res = $this->channel_config->addChannelConfig($this->channel_type);
            if (!$res) {
                $this->__Return('爬虫渠道配置添加失败');
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }

        $this->__Return('爬虫渠道配置添加成功', '', 'tip_success');
    }

    //编辑
    public function edit()
    {
        $id = I('get.id', '', 'intval');
        if (!IS_POST) {
            $crawler_list = $this->getCrawlerListAll();
            $info = $this->channel_config->getChannelConfigInfo(compact('id'));

            $this->assign('crawler_list', $crawler_list);
            $this->assign('info', $info);
            $this->assign('channel_list', $this->channel_list);
            $this->display('add');
            exit;
        }

        try {
            $res = $this->channel_config->updateChannelConfig($this->channel_type);
            if (!$res) {
                $this->__Return('爬虫渠道配置更新失败');
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }
        $this->__Return('爬虫渠道配置更新成功', '', 'tip_success');
    }

    //删除
    public function del()
    {
        try {
            $res = $this->channel_config->delChannelConfig();
            if (!$res) {
                $this->__Return('爬虫渠道配置删除失败');
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }
        $this->__Return('爬虫渠道配置删除成功', '', 'tip_success');
    }

    /**
     * 获取所有爬虫名称
     * @return list
     */
    public function getCrawlerListAll()
    {
        $list = [];
        foreach ($this->crawler_areas as $key => $value) {
            foreach ($this->flow_map as $kk => $vv) {
                $list[] = $value.$kk;
            }
        }
        return $list;
    }
}