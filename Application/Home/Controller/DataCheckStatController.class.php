<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\DataCheckStatRepository;
use Home\Repositories\DataCheckApiRepository;

class DataCheckStatController extends AdminController
{
    protected $dataCheckStat;
    protected $dataCheckApi;
    public function __construct()
    {
        parent::__construct();
        $this->dataCheckStat = new DataCheckStatRepository();
        $this->dataCheckApi = new DataCheckApiRepository();
    }

    public function index()
    {
        $input = I('get.');

        //接口列表
        $product_list = $this->dataCheckApi->getOpdataDailProductApi();
        $product_ids = array_column($product_list, 'id');

        $customer = $this->dataCheckStat->getCustomerListParam($product_ids);
        //统计列表
        $data = $this->dataCheckApi->getOpdataDailStatList($customer);

        $count = count($list);
        $page = new Page($count, C('LIST_ROWS'));

        $list = array_slice($data['list'], $page->firstRow, $page->listRows);

        //签约状态
        $contract_status = $this->dataCheckStat->getContractStatus();
        //客户列表
        $customer_list = $this->dataCheckStat->getCustomerList($product_ids);
        $customer_list = $customer_list['list'];

        $this->assign('input', $input);
        $this->assign('list', $list);
        $this->assign('total', $data['total']);
        $this->assign('contract_status', $contract_status);
        $this->assign('customer_list', $customer_list);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function detail()
    {
        $input = I('get.');
        $product_list = $this->dataCheckApi->getOpdataDailProductApi();
        $product_ids = array_column($product_list, 'id');

        $customer = $this->dataCheckStat->getDetailCustomerList($product_ids);
        //统计详情
        $data = $this->dataCheckApi->getOpdataDailStatDetail($customer, $product_list);

        $date_list = $this->dataCheckStat->getDateList();
        $count = count($date_list);
        $page = new Page($count, C('LIST_ROWS'));

        $date_list = $data['list'] ? array_merge($date_list, $data['list']) : $date_list;
        $list = array_slice($date_list, $page->firstRow, $page->listRows);
        $total = $data['total'];

        //客户账号列表
        $customer_list = isset($customer['list']) ? $customer['list'] : [];

        $this->assign('input', $input);
        $this->assign('product_list', $product_list);
        $this->assign('list', $list);
        $this->assign('total', $total);
        $this->assign('customer_list', $customer_list);
        $this->assign('page', $page->show());
        $this->display();
    }
}