<?php

namespace Home\Controller;

use Common\Controller\AdminController;


/**
 * 测试用例页面
 * Class TestCaseController
 * @package Home\Controller
 * <AUTHOR>
 */
class TestCaseController extends AdminController
{
    private $repository;
    private $test_item;
    private $test_case_admin;
    private $test_team;
    private $test_node;

    public function __construct()
    {
        parent::__construct();
        $this->repository = D('TestCase');
        $this->test_item = D('TestItem');
        $this->test_case_admin = D('TestCaseAdmin');
        $this->test_team = D('TestTeam');
        $this->test_node = D('TestNode');
    }
    /**
     * 项目组模块
     */

    public function team()
    {
        $data = $this->test_team->order('id DESC')->select();
        $this->assign(['user'=>$this->loginuser['username']]);
        $this->assign(['data'=>$data]);
        $this->display();
    }

    /**
     * 测试项目页面
     */
    public function item()
    {
        $input = $where = [];
        //交易日期
        $input['start_time'] = I('get.start_time', '', 'trim');
        $input['end_time']   = I('get.end_time', '', 'trim');
        $input['node_search'] = I('get.node_search', '', 'trim');
        $input['item_search'] = I('get.item_search', '', 'trim');
        $input['name_search'] = I('get.name_search', '', 'trim');
        $input['test_team_id'] = $where['test_team_id'] = I('get.team_id', '', 'trim');

        if(!empty($input['start_time'])){
            $where['created_at'] = ['egt', $input['start_time']];
        }
        if(!empty($input['end_time'])){
            $where['created_at'] = ['elt', $input['end_time']];
        }
        if(!empty($input['node_search'])){
            $where['node'] = $input['node_search'];
        }

        if(!empty($input['item_search'])){
            $where['item'] = $input['item_search'];
        }

        if(!empty($input['name_search'])){
            $where['name'] = $input['name_search'];
        }
        $count = $this->test_item->where($where)->count();
        $Page  = new \Common\ORG\Page($count, 300);
        $data = $this->test_item->where($where)->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();

        $this->assign(['input'=>$input]);
        $this->assign(['data'=>$data]);
        $this->assign(['page'=>$Page->show()]);
        $this->display();

    }

    /**
     * 节点列表
     */
    public function node()
    {
        $input = $where = [];
        $input['team_id'] = $where['test_item_id'] = I('get.team_id', '', 'trim');
        $team_info = $this->test_team->where(['id'=>$input['team_id']])->find();

        $data = $this->test_node->where($where)->order('id DESC')->select();

        $this->assign(['user'=>$this->loginuser['username']]);
        $this->assign(['team_info'=>$team_info]);
        $this->assign(['input'=>$input]);
        $this->assign(['data'=>$data]);

        $this->display();
    }

    /**
     * 用例测试页面
     */
    public function index()
    {
        $input = $where = [];
        $input['item_id'] = $where['test_item_id'] = I('get.item_id', '', 'trim');
        $item_info = $this->test_item->where(['id'=>$input['item_id']])->find();
        //交易日期
        $input['start_time'] = I('get.start_time', '', 'trim');
        $input['end_time']   = I('get.end_time', '', 'trim');
        $input['node_search'] = I('get.node_search', '', 'trim');
        $input['item_search'] = I('get.item_search', '', 'trim');
        $input['name_search'] = I('get.name_search', '', 'trim');
        if(!empty($input['start_time'])){
            $where['created_at'] = ['egt', $input['start_time']];
        }
        if(!empty($input['end_time'])){
            $where['created_at'] = ['elt', $input['end_time']];
        }
        if(!empty($input['node_search'])){
            $where['node'] = $input['node_search'];
        }

        if(!empty($input['item_search'])){
            $where['item'] = $input['item_search'];
        }

        if(!empty($input['name_search'])){
            $where['name'] = $input['name_search'];
        }
        $field = 'sum(money) as total_sum';
        $total_sum = $this->repository->where($where)->field($field)->find();
        $count = $this->repository->where($where)->count();
        $Page  = new \Common\ORG\Page($count, 300);
        $data = $this->repository->where($where)->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();

        //获取项目名称
        //$item_select = $this->test_item->field('item')->select();

       // $this->assign(['item_select'=>$item_select]);

        //获取对应的节点信息
        $node_info = $this->test_node->where(['test_item_id'=>$item_info['test_team_id']])->select();

        $this->assign(['total_sum'=>$total_sum]);
        $this->assign(['node_info'=>$node_info]);
        $this->assign(['item_info'=>$item_info]);
        $this->assign(['input'=>$input]);
        $this->assign(['data'=>$data]);
        $this->assign(['page'=>$Page->show()]);
        $this->display();
    }

    /**
     * 增加测试用例
     */
    public function add()
    {
        $data = I('post.');
        $data = array_map('trim', $data);
        $json_result = $data['json_result'];
        $get_param = html_entity_decode($data['get_param']);
        $post_param = $data['post_param'];
        $apikey = $data['apikey'];
        $secret = $data['secret'];
        $is_json = $data['is_json'];
        /*if(!empty($get_param)){
            $get_param = urldecode($get_param);
            if(!is_string($get_param)){
                $result = ['status' => 'error', 'msg' => 'get参数不是json格式'];
                $this->ajaxReturn($result);
            }
            if($this->isJson($get_param)){
                //判断不是数字的化进行解析
                if(is_numeric($get_param)){
                    $result = ['status' => 'error', 'msg' => 'get参数不能是单独数字'];
                    $this->ajaxReturn($result);
                }
                $get_param = json_decode($get_param, true);
                $get_param = json_encode($get_param, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
            }else{
                //不是json
                $result = ['status' => 'error', 'msg' => 'get参数不是json格式'];
                $this->ajaxReturn($result);
            }
        }*/
        if(!empty($post_param)){
            $post_param = urldecode($post_param);
            $post_param = html_entity_decode($post_param);
            if($is_json == 1){
                if(!is_string($post_param)){
                    $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                    $this->ajaxReturn($result);
                }
                if($this->isJson($post_param)){
                    //判断不是数字的化进行解析
                    if(is_numeric($post_param)){
                        $result = ['status' => 'error', 'msg' => 'post参数不能是单独数字'];
                        $this->ajaxReturn($result);
                    }
                    $post_param = json_decode($post_param, true);
                    $post_param = json_encode($post_param, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
                }else{
                    //不是json
                    $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                    $this->ajaxReturn($result);
                }
            }
        }

        //对json字段解码
        $json_result = urldecode($json_result);

        if(!is_string($json_result)){
            $result = ['status' => 'error', 'msg' => '结果值不是字符串'];
            $this->ajaxReturn($result);
        }
        //判断是否是json
        if($this->isJson($json_result)){
            //判断不是数字的化进行解析
            if(is_numeric($json_result)){
                $result = ['status' => 'error', 'msg' => '结果值不能是单独数字'];
                $this->ajaxReturn($result);
            }
            $json_arr = json_decode($json_result, true);
            $json_result = json_encode($json_arr, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
        }else{
            //不是json
            $result = ['status' => 'error', 'msg' => '结果值不是json数据'];
            $this->ajaxReturn($result);
        }

        if(!is_numeric($data['money'])){
            $result = ['status' => 'error', 'msg' => '金额必须为数字类型'];
            $this->ajaxReturn($result);
        }

        $item_info = $this->test_item->where(['id'=>$data['item_id']])->find();
        $info = [
            'item' => isset($item_info['item']) ? $item_info['item'] : '',
            'test_item_id' => $data['item_id'],
            'name' => $data['name'],
            'url' => trim($data['url'], '/'),
            'money' => $data['money'],
            'admin' => $this->loginuser['username'],
            'get_param' => $get_param,
            'post_param' => $post_param,
            'json_result' => $json_result,
            'api_result' => '',
            'created_at' => date('Y-m-d H:i:s'),
            'apikey' => $apikey,
            'secret' => $secret,
            'is_json' => $is_json
        ];

        $res = $this->repository->add($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 添加测试节点
     */
    public function add_node()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        $preg = "/^http(s)?:\\/\\/.+/";
        if(!preg_match($preg,$data['url'])){
            $result = ['status' => 'error', 'msg' => '请输入正确的url'];
            $this->ajaxReturn($result);
        }
        $team_info = $this->test_team->where(['id'=>$data['team_id']])->find();
        if(empty($team_info)){
            $result = ['status' => 'error', 'msg' => '项目组不存在'];
            $this->ajaxReturn($result);
        }
        $find_one = $this->test_node->where(['test_item_id'=>$team_info['id'], 'name'=> $data['name']])->find();
        if(!empty($find_one)){
            $result = ['status' => 'error', 'msg' => '该节点已存在'];
            $this->ajaxReturn($result);
        }
        $info = [
            'test_item_id' => $team_info['id'],
            'name' => $data['name'],
            'url' => trim($data['url'], '/'),
            'admin' => $this->loginuser['username'],
            'created_at' => date('Y-m-d H:i:s')
        ];

        $res = $this->test_node->add($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 添加项目测试用例
     */
    public function add_item()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        $info = [
            'test_team_id' => $data['test_team_id'],
            'item' => '',
            'node' => '',
            'name' => $data['name'],
            'admin' => $this->loginuser['username'],
            'mode' => $data['mode'],
            'created_at' => date('Y-m-d H:i:s')
        ];

        $res = $this->test_item->add($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 添加项目组
     */
    public function add_team()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        $info = [
            'name' => $data['name'],
            'apikey' => $data['apikey'],
            'secret' => $data['secret'],
            'admin' => trim($data['admin']),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $res = $this->test_team->add($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 获取编辑信息
     */
    public function getEdit(){
        $data = I('post.');
        $info = $this->repository->where(['id'=>$data['id']])->find();
        $this->ajaxReturn($info);
    }

    /**
     * 获取编辑节点信息
     */
    public function getEditNode(){
        $data = I('post.');
        $info = $this->test_node->where(['id'=>$data['id']])->find();
        $this->ajaxReturn($info);
    }
    /**
     * 获取项目编辑信息
     */
    public function getEditItem(){
        $data = I('post.');
        $info = $this->test_item->where(['id'=>$data['id']])->find();
        $this->ajaxReturn($info);
    }
    /**
     * 获取项目组编辑信息
     */
    public function getEditTeam(){
        $data = I('post.');
        $info = $this->test_team->where(['id'=>$data['id']])->find();
        $this->ajaxReturn($info);
    }

    /**
     * 编辑用例
     */
    public function edit()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        $get_param = html_entity_decode($data['get_param2']);
        $post_param = $data['post_param2'];
        $json_result = $data['json_result2'];
        $apikey = $data['apikey2'];
        $secret = $data['secret2'];
        $is_json = $data['is_json2'];

        /*if(!empty($get_param)){
            $get_param = urldecode($get_param);
            if(!is_string($get_param)){
                $result = ['status' => 'error', 'msg' => 'get参数不是json格式'];
                $this->ajaxReturn($result);
            }
            if($this->isJson($get_param)){
                //判断不是数字的化进行解析
                if(is_numeric($get_param)){
                    $result = ['status' => 'error', 'msg' => 'get参数不能是单独数字'];
                    $this->ajaxReturn($result);
                }
                $get_param = json_decode($get_param, true);
                $get_param = json_encode($get_param, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
            }else{
                //不是json
                $result = ['status' => 'error', 'msg' => 'get参数不是json格式'];
                $this->ajaxReturn($result);
            }
        }*/

        if(!empty($post_param)){
            $post_param = urldecode($post_param);
            $post_param = html_entity_decode($post_param);
            if($is_json == 1){
                if(!is_string($post_param)){
                    $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                    $this->ajaxReturn($result);
                }
                if($this->isJson($post_param)){
                    //判断不是数字的化进行解析
                    if(is_numeric($post_param)){
                        $result = ['status' => 'error', 'msg' => 'post参数不能是单独数字'];
                        $this->ajaxReturn($result);
                    }
                    $post_param = json_decode($post_param, true);
                    $post_param = json_encode($post_param, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
                }else{
                    //不是json
                    $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                    $this->ajaxReturn($result);
                }
            }
        }

        $json_result = urldecode($json_result);

        if(!is_string($json_result)){
            $result = ['status' => 'error', 'msg' => '结果值不是字符串'];
            $this->ajaxReturn($result);
        }
        //判断是否是json
        if($this->isJson($json_result)){
            //判断不是数字的化进行解析
            if(is_numeric($json_result)){
                $result = ['status' => 'error', 'msg' => '结果值不能是单独数字'];
                $this->ajaxReturn($result);
            }
            $json_arr = json_decode($json_result, true);
            $json_result = json_encode($json_arr, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
        }else{
            //不是json
            $result = ['status' => 'error', 'msg' => '结果值不是json数据'];
            $this->ajaxReturn($result);
        }

        if(!is_numeric($data['money2'])){
            $result = ['status' => 'error', 'msg' => '金额必须为数字类型'];
            $this->ajaxReturn($result);
        }

        //只要编辑则把相应的状态改成未测试
        $item_info = $this->test_item->where(['id'=>$data['item_id2']])->find();
        $info = [
            'item' => isset($item_info['item']) ? $item_info['item'] : '',
            'name' => $data['name2'],
            'url' => trim($data['url2'], '/'),
            'money' => $data['money2'],
            'admin' => $this->loginuser['username'],
            'get_param' => $get_param,
            'post_param' => $post_param,
            'json_result' => $json_result,
            'result' => 0,
            'api_result' => '',
            'created_at' => date('Y-m-d H:i:s'),
            'apikey' => $apikey,
            'secret' => $secret,
            'is_json' => $is_json
        ];

        $res = $this->repository->where(['id'=>$data['id2']])->save($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 编辑节点
     */
    public function edit_node()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        $preg = "/^http(s)?:\\/\\/.+/";
        if(!preg_match($preg,$data['url2'])){
            $result = ['status' => 'error', 'msg' => '请输入正确的url'];
            $this->ajaxReturn($result);
        }

        $find_one = $this->test_node->where(['id'=>$data['id2']])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '编辑信息不存在'];
            $this->ajaxReturn($result);
        }

        $find_where = [];
        $find_where['test_item_id'] = $data['team_id2'];
        $find_where['name'] = $data['name2'];
        $find_where['id'][] = array('NEQ', $data['id2']);

        $find_one2 = $this->test_node->where($find_where)->find();
        if(!empty($find_one2)){
            $result = ['status' => 'error', 'msg' => '此节点已存在'];
            $this->ajaxReturn($result);
        }

        $info = [
            'test_item_id' => $data['team_id2'],
            'name' => $data['name2'],
            'url' => trim($data['url2'], '/'),
            'admin' => $this->loginuser['username'],
            'created_at' => date('Y-m-d H:i:s')
        ];
        $res = $this->test_node->where(['id'=>$data['id2']])->save($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 编辑项目测试用例
     */
    public function edit_item()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        //只要编辑则把相应的状态改成未测试
        $info = [
            'item' => '',
            'node' => '',
            'name' => $data['name2'],
            'admin' => $this->loginuser['username'],
            'created_at' => date('Y-m-d H:i:s')
        ];

        $res = $this->test_item->where(['id'=>$data['id2']])->save($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }

    public function edit_team()
    {
        $data = I('post.');
        $data = array_map('trim', $data);

        //只要编辑则把相应的状态改成未测试
        $info = [
            'name' => $data['name2'],
            'apikey' => $data['apikey2'],
            'secret' => $data['secret2'],
            'admin' => trim($data['admin2']),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $res = $this->test_team->where(['id'=>$data['id2']])->save($info);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }

    /**
     * 编辑项目信息
     */

    /**
     * 执行测试用例
     */
    public function run(){
        $data = I('post.');
        $date = date('Y-m-d H:i:s');
        /*$admin_info = [
            'test_case_id' => $data['id'],
            'admin' => $this->loginuser['username'],
            'created_at' => $date
        ];
        $this->test_case_admin->add($admin_info);*/

        //去找对应的节点信息
        $node_info = $this->test_node->where(['id'=>$data['test_node_id']])->find();
        if(empty($node_info)){
            $result = ['status' => 'error', 'msg' => '节点信息不存在'];
            $a = [
                'result' => 2,
                'msg' => '节点信息不存在',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($a);
            $this->ajaxReturn($result);
        }
        if(empty($node_info['url'])){
            $result = ['status' => 'error', 'msg' => '节点没有配置url'];
            $a = [
                'result' => 2,
                'msg' => '节点没有配置url',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($a);
            $this->ajaxReturn($result);
        }

        $info = $this->repository->where(['id'=>$data['id']])->find();
        if(empty($info)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $a = [
                'result' => 2,
                'msg' => '数据不存在',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($a);
            $this->ajaxReturn($result);
        }
        if(empty($info['url'])){
            $result = ['status' => 'error', 'msg' => '没有配置接口地址'];
            $a = [
                'result' => 2,
                'msg' => '没有配置接口地址',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($a);
            $this->ajaxReturn($result);
        }

        //查找对应的item信息
        $item_info = $this->test_item->where(['id'=>$info['test_item_id']])->find();
        //查找对应team信息
        $team_info = $this->test_team->where(['id'=>$item_info['test_team_id']])->find();
        if(!empty($info['apikey']) && !empty($info['secret'])){
            $apikey = $info['apikey'];
            $secret = $info['secret'];
        }else{
            if(empty($team_info['apikey']) || empty($team_info['secret'])){
                $result = ['status' => 'error', 'msg' => 'apikey或secret没有配置'];
                $a = [
                    'result' => 2,
                    'msg' => 'apikey或secret没有配置',
                    'api_result' => '',
                    'created_at' => $date
                ];
                $this->repository->where(['id'=>$data['id']])->save($a);
                $this->ajaxReturn($result);
            }
            $apikey = $team_info['apikey'];
            $secret = $team_info['secret'];
        }

        $nonce = '1349';
        $timestamp = time();
        $signature = $this->getSign($apikey, $secret, $nonce, $timestamp);

        $url = $node_info['url'].'/'.$info['url'];
        $url = $url . '?timestamp='.$timestamp.'&apikey='.$team_info['apikey'].'&nonce='.$nonce.'&signature='.$signature;
        if(!empty($info['get_param'])){
            $url = $url.'&'.$info['get_param'];
        }
        $type = 'get';
        if(!empty($info['post_param'])){
            $type = 'post';
        }

        if($type == 'get'){
            $res = $this->curlHandler($url);
        }else{
            if($info['is_json'] == 1){
                $res = $this->curlHandler($url, $info['post_param'], 'http', 'post', true);
            }else{
                $res = $this->curlHandler($url, $info['post_param'], 'http', 'post');
            }

        }
        //$res = '{"status":0,"sid":"2159979385083014417","operator":"2","msg":"OK","data":{"phone":"13312644078","name":"杨小艺","idnum":"530425199502260528","result":{"result_201":{"status":0,"sid":"2159979385083014417","msg":"OK","data":{"rescode":"1"}}}}}';
        //$res = '{"status":0,"sid":"@string","operator":"3","msg":"OK","data":{"phone":"13757584721","name":"","idnum":"","result":{"result_202":{"status":0,"sid":"@string","msg":"OK","data":{"rescode":null}}}}}';
        if(empty($res)){
            $result = ['status' => 'error', 'msg' => '请求异常'];
            $a = [
                'result' => 2,
                'msg' => '请求异常',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($a);
            $this->ajaxReturn($result);
        }

        if($this->isJson($info['json_result'])){
            //判断不是数字的化进行解析
            if(is_numeric($info['json_result'])){
               if($info['json_result'] == $res){
                   $result = ['status' => 'ok', 'msg' => '测试成功'];
                   $a = [
                       'result' => 1,
                       'api_result' => $res,
                       'created_at' => $date
                   ];
               }else{
                   $result = ['status' => 'error', 'msg' => '测试失败'];
                   $a = [
                       'result' => 2,
                       'api_result' => $res,
                       'created_at' => $date
                   ];
               }
                //进行入库操作
                $this->repository->where(['id'=>$data['id']])->save($a);
                $this->ajaxReturn($result);
            }else{
                if(!is_string($res) || !$this->isJson($res)){
                    $result = ['status' => 'error', 'msg' => '结果值不是json'];
                    $a = [
                        'result' => 2,
                        'msg' => '结果值不是json',
                        'api_result' => '',
                        'created_at' => $date
                    ];
                    $this->repository->where(['id'=>$data['id']])->save($a);
                    $this->ajaxReturn($result);
                }
                $res_arr = json_decode($res, true);
                $res = json_encode($res_arr, JSON_UNESCAPED_UNICODE);
                $json_arr1 = json_decode($info['json_result'], true);
                $json_arr2 = $res_arr;

                $r = $this->isEqualArr($json_arr1, $json_arr2);
                if($r){
                    $result = ['status' => 'ok', 'msg' => '测试成功'];
                    $a = [
                        'result' => 1,
                        'msg' => '测试成功',
                        'api_result' => $res,
                        'created_at' => $date
                    ];
                }else{
                    $result = ['status' => 'error', 'msg' => '测试失败'];
                    $a = [
                        'result' => 2,
                        'msg' => '测试失败',
                        'api_result' => $res,
                        'created_at' => $date
                    ];
                }
                //进行入库操作
                $this->repository->where(['id'=>$data['id']])->save($a);
                $this->ajaxReturn($result);
            }
        }else{
            if($info['json_result'] == $res){
                $result = ['status' => 'ok', 'msg' => '测试成功'];
                $a = [
                    'result' => 1,
                    'msg' => '测试成功',
                    'api_result' => $res,
                    'created_at' => $date
                ];
            }else{
                $result = ['status' => 'error', 'msg' => '测试失败'];
                $a = [
                    'result' => 2,
                    'msg' => '测试失败',
                    'api_result' => $res,
                    'created_at' => $date
                ];
            }
            //进行入库操作
            $this->repository->where(['id'=>$data['id']])->save($a);
            $this->ajaxReturn($result);
        }

    }

    /**
     * 运行项目测试用例
     *
     */

    public function run_item(){
        $data = I('post.');
        $date = date('Y-m-d H:i:s');
        $i = $data['i'];
        if($i == 0){
            $admin_info = [
                'test_item_id' => $data['item_id'],
                'admin' => $this->loginuser['username'],
                'created_at' => $date
            ];
            $this->test_case_admin->add($admin_info);
        }

        //去找对应的节点信息
        $node_info = $this->test_node->where(['id'=>$data['node']])->find();
        if(empty($node_info)){
            $aa = [
                'result' => 2,
                'msg' => 'ID: '.$data['id'].'的节点信息不存在',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($aa);
            $result = ['status' => 'error', 'name'=>'节点信息不存在', 'res1' => '', 'res2'=>'ID: '.$data['id'].'的节点信息不存在'];
            $this->ajaxReturn($result);
        }
        if(empty($node_info['url'])){
            $aa = [
                'result' => 2,
                'msg' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].'===>没有配置url',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($aa);
            $result = ['status' => 'error', 'name'=>'没有配置url', 'res1'=>'', 'res2' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].' 没有配置url'];
            $this->ajaxReturn($result);
        }

        $info = $this->repository->where(['id'=>$data['id']])->find();
        if(empty($info)){
            $result = ['status' => 'error', 'name'=>'数据不存在', 'res1'=>'', 'res2' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].' 数据不存在'];
            $aa = [
                'result' => 2,
                'msg' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].'===>数据不存在',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($aa);
            $this->ajaxReturn($result);
        }
        if(empty($info['url'])){
            $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>'', 'res2' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].' 没有配置接口地址'];
            $aa = [
                'result' => 2,
                'msg' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].'===>数据不存在',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($aa);
            $this->ajaxReturn($result);
        }

        //查找对应的item信息
        $item_info = $this->test_item->where(['id'=>$info['test_item_id']])->find();
        //查找对应team信息
        $team_info = $this->test_team->where(['id'=>$item_info['test_team_id']])->find();
        if(!empty($info['apikey']) && !empty($info['secret'])){
            $apikey = $info['apikey'];
            $secret = $info['secret'];
        }else{
            if(empty($team_info['apikey']) || empty($team_info['secret'])){
                $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>'', 'res2' => 'apikey或secret没有配置'];
                $aa = [
                    'result' => 2,
                    'msg' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].'apikey或secret没有配置',
                    'api_result' => '',
                    'created_at' => $date
                ];
                $this->repository->where(['id'=>$data['id']])->save($aa);
                $this->ajaxReturn($result);
            }
            $apikey = $team_info['apikey'];
            $secret = $team_info['secret'];
        }

        $nonce = '1349';
        $timestamp = time();
        $signature = $this->getSign($apikey, $secret, $nonce, $timestamp);

        $url = $node_info['url'] . '/'. $info['url'];
        $url = $url . '?timestamp='.$timestamp.'&apikey='.$team_info['apikey'].'&nonce='.$nonce.'&signature='.$signature;
        if(!empty($info['get_param'])){
            $url = $url.'&'.$info['get_param'];
        }

        $type = 'get';
        if(!empty($info['post_param'])){
            $type = 'post';
        }

        if($type == 'get'){
            $res = $this->curlHandler($url);
        }else{
            if($info['is_json'] == 1){
                $res = $this->curlHandler($url, $info['post_param'], 'http', 'post', true);
            }else{
                $res = $this->curlHandler($url, $info['post_param'], 'http', 'post');
            }
        }

        //$res = '{"status":0,"sid":"2159979385083014417","operator":"2","msg":"OK","data":{"phone":"13312644078","name":"杨小艺","idnum":"530425199502260528","result":{"result_201":{"status":0,"sid":"2159979385083014417","msg":"OK","data":{"rescode":"1"}}}}}';
        if(empty($res)){
            $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>'', 'res2' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].' 请求异常'];
            $aa = [
                'result' => 2,
                'msg' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].'===>请求异常',
                'api_result' => '',
                'created_at' => $date
            ];
            $this->repository->where(['id'=>$data['id']])->save($aa);
            $this->ajaxReturn($result);
        }

        if($this->isJson($info['json_result'])){
            //判断不是数字的化进行解析
            if(is_numeric($info['json_result'])){
                if($info['json_result'] == $res){
                    $result = ['status' => 'ok', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => $res];
                    $aa = [
                        'result' => 1,
                        'msg' => '测试成功',
                        'api_result' => $res,
                        'created_at' => $date
                    ];
                }else{
                    $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => $res];
                    $aa = [
                        'result' => 2,
                        'msg' => '测试失败',
                        'api_result' => $res,
                        'created_at' => $date
                    ];
                }
                //进行入库操作
                $this->repository->where(['id'=>$data['id']])->save($aa);
                $this->ajaxReturn($result);
            }else{
                if(!is_string($res) || !$this->isJson($res)){
                    $aa = [
                        'result' => 2,
                        'msg' => 'ID: '.$data['id'].', 节点: '.$node_info['name'].'===>结果值不是json',
                        'api_result' => '',
                        'created_at' => $date
                    ];
                    $this->repository->where(['id'=>$data['id']])->save($aa);

                    $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => '结果值不是json'];
                    $this->ajaxReturn($result);
                }
                $res_arr = json_decode($res, true);
                $res = json_encode($res_arr, JSON_UNESCAPED_UNICODE);
                $json_arr1 = json_decode($info['json_result'], true);
                $json_arr2 = $res_arr;

                $r = $this->isEqualArr($json_arr1, $json_arr2);
                if($r){
                    $result = ['status' => 'ok', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => $res];
                    $aa = [
                        'result' => 1,
                        'msg' => '测试成功',
                        'api_result' => $res,
                        'created_at' => $date
                    ];
                }else{
                    $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => $res];
                    $aa = [
                        'result' => 2,
                        'msg' => '测试失败',
                        'api_result' => $res,
                        'created_at' => $date
                    ];
                }
                //进行入库操作
                $this->repository->where(['id'=>$data['id']])->save($aa);
                $this->ajaxReturn($result);
            }
        }else{
            if($info['json_result'] == $res){
                $result = ['status' => 'ok', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => $res];
                $aa = [
                    'result' => 1,
                    'msg' => '测试成功',
                    'api_result' => $res,
                    'created_at' => $date
                ];
            }else{
                $result = ['status' => 'error', 'name'=>$info['name'], 'res1'=>$info['json_result'], 'res2' => $res];
                $aa = [
                    'result' => 2,
                    'msg' => '测试失败',
                    'api_result' => $res,
                    'created_at' => $date
                ];
            }
            //进行入库操作
            $this->repository->where(['id'=>$data['id']])->save($aa);
            $this->ajaxReturn($result);
        }
    }

    /**
     * 递归处理多维数组
     */
    private function digui_arr($arr = []){
        $result = array();
        foreach($arr as $key => $val){
            if(is_array($val)){
                $result = array_merge($result, $this->digui_arr($val));
            }else{
                $result[$key] = $val;
            }
        }
        return $result;
    }

    /**
     * 比较两个数组是否相等
     */
    private function compareFunc($res1 = [], $res2 = []){
        if(!is_array($res1)){
            //判断是否是是否是字符串
            if(is_string($res1)){
                //判断是否是json
                if($this->isJson($res1)){
                    //判断是否是数字
                    if(is_numeric($res1)){
                        if($res1 == $res2){
                            return 'ok';
                        }else{
                            return 'error';
                        }
                    }else{
                        //两个保持同样的格式
                        $res1 = json_decode($res1, true);
                        $res2 = json_decode($res2, true);
                    }
                }else{
                    //不是json,直接匹配是否相等
                    if($res1 == $res2){
                        return 'ok';
                    }else{
                        return 'error';
                    }
                }
            }
        }
        foreach($res1 as $key=>$value){
            //如果存在的key,进行匹配
            if(isset($res2[$key])){
                if($res2[$key] != $value){
                    return 'error';
                }
            }else{
                //如果这个key不存在也是错误
                return 'error';
            }
        }
        return 'ok';
    }

    /**
     * 判断是否是json
     */
    protected function isJson($data)
    {
        $res = @json_decode($data, true);
        if (is_null($res)) {
            return false;
        }
        return true;
    }


    /**
     * 远程调用url
     */
    private function curlHandler($request_url, $post_data = array(), $type = 'http', $method = 'get', $json = false, $inner = false)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $request_url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        if($type == 'https')
        {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
            //curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, true);  // 从证书中检查SSL加密算法是否存在
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if($method == 'post'){
            curl_setopt($ch, CURLOPT_POST, 1);
            if($json){
                $headers = ['Content-Type: application/json;charset=utf-8'];
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            }else{
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            }
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        $output = curl_exec($ch);
        if($inner){
            return $output;
        }
        $err = curl_errno($ch);
        $httpCode = curl_getinfo($ch,CURLINFO_HTTP_CODE);
        if($err == 0 && $httpCode == 200){
            return $output;
        }else{
            return false;
        }
    }
    /**
     * 获取执行调用者
     */
    public function getAdmin()
    {
        $data = I('post.');
        $type = $data['type'];
        $where = [];
        if($type == 'item'){
            $where['test_item_id'] = $data['id'];
        }else{
            $where['test_case_id'] = $data['id'];
        }
        $info = $this->test_case_admin->where($where)->order('created_at desc')->limit(15)->select();
        $str = '';
        foreach($info as $key=>$value){
            $str .= "<p>&nbsp;操作人: &nbsp;&nbsp;{$value['admin']}, &nbsp;操作时间:&nbsp;{$value['created_at']} </p>";
        }
        $result = ['status' => 'ok', 'msg' => '执行完毕', 'str'=>$str];
        $this->ajaxReturn($result);
    }
    /**
     * 获取执行记录
     */
    public function getRunLog()
    {
        $data = I('post.');

        $where = [];
        $where['test_item_id'] = $data['id'];
        $where['result'] = ['NEQ', 0];

        $info = $this->repository->where($where)->order('id desc')->select();
        $str = '';
        foreach($info as $key=>$value){
            $res = '成功';
            if($value['result'] == 2){
                $res = '失败';
            }
            $str .= "<p>id: {$value['id']}, 用例名称: {$value['name']}, 测试结果: {$res}</p>";
        }
        $result = ['status' => 'ok', 'msg' => '执行完毕', 'str'=>$str];
        $this->ajaxReturn($result);
    }

    /**
     * 比较数组是否相等
     * 整型:integer
     * 字符串:string
     * 布尔类型:boolean
     */
    private function isEqualArr($arr1 = [],$arr2 = []) {
        if(is_string($arr1)){
            if($arr1 != $arr2){
                return false;
            }else{
                return true;
            }
        }

        if(count($arr1) != count($arr2)){
            return false;
        }
        foreach($arr1 as $k => $v){
            if(is_array($v) && is_array($arr2[$k])){
                /*if(!$this->isEqualArr($v, $arr2[$k])){
                    return false;
                }*/
                return $this->isEqualArr($v, $arr2[$k]);
            }
            if(!isset($arr2[$k])){
                if($arr2[$k] != null){
                    return false;
                }
            }
            //在这个地方进行匹配,@进行类型匹配,#进行范围匹配,没有则进行值匹配
            if(strpos($v,'@') !== false){
                //进行类型匹配,格式:@integer
                $res_type = ltrim($v, '@');
                if($res_type != gettype($arr2[$k])){
                    return false;
                }
            }else if(strpos($v,'#') !== false){
                //进行范围匹配格式:1#100
                $res_arr = explode('#', $v);
                $res_arr[0] = isset($res_arr[0]) ? $res_arr[0] : 0;
                $res_arr[1] = isset($res_arr[1]) ? $res_arr[1] : 0;
                if($arr2[$k] > $res_arr[1] || $arr2[$k] < $res_arr[0]){
                    return false;
                }
            }else{
                if($arr2[$k] != $v){
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 生成签名算法
     */
    private function getSign($apikey = '', $secret = '', $nonce = '', $timestamp = '')
    {
        if(!$apikey || !$secret || !$nonce || !$timestamp){
            return false;
        }
        $tmpArr = array($timestamp, $apikey, $secret, $nonce);
        sort($tmpArr, SORT_STRING);
        $signature = sha1( implode( $tmpArr ) );
        return $signature;
    }

}

