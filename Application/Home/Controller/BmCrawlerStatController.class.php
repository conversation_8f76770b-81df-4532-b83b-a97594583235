<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\BmCrawlerStatRepository;
use Home\Repositories\BmCrawlerRepository;
use Home\Repositories\BmCrawlerStatApiRepository;
use Home\Repositories\BmCrawlerRateRepository;

class BmCrawlerStatController extends AdminController
{
    protected $crawler_channel_list;

    protected $crawler_areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西','广东', '广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏','贵州', '辽宁', '重庆', '陕西', '青海', '黑龙江'];

    // Repository Class
    protected $stat_repository;
    //api
    protected $stat_api_repository;

    protected $rate;

    //详单来源 1 爬虫 2 既有详单
    protected $record_source = 1;

    public function __construct()
    {
        parent::__construct();

        $this->stat_repository = new BmCrawlerStatRepository($this->record_source);

        $this->stat_api_repository = new BmCrawlerStatApiRepository();

        $this->rate = new BmCrawlerRateRepository();

        $this->crawler_channel_list = $this->rate->getChannelList();
    }

    //列表统计
    public function index()
    {
        $input = I('get.');

        //账号列表
        $product_list = $this->stat_repository->getProductList();

        //查询条件
        $where = $this->stat_repository->getCrawlerListParam();
        $where['cids'] = json_encode(array_keys($product_list));
        //列表
        $data = $this->stat_api_repository->getReportList($product_list, $where);

        $count = count($product_list);
        $page = new Page($count, C('LIST_ROWS'));

        //对数据进行排序
        $this->indexOrder($data['user_list']);
        $user_show = array_slice($data['user_list'], $page->firstRow, $page->listRows);
        $total_data = $data['total_data'];

        $list_account = $this->stat_repository->getAccountListAll();
        $list_product = $this->stat_repository->getProductListAll();
        //签约状态
        $contract_status = $this->stat_repository->getContractStatus();


        $this->assign('input', $input);
        $this->assign('crawler_channel_list', $this->crawler_channel_list);
        $this->assign('crawler_areas', $this->crawler_areas);
        $this->assign('user_show', $user_show);
        $this->assign('total_data', $total_data);
        $this->assign('list_account', $list_account);
        $this->assign('list_product', $list_product);
        $this->assign('contract_status', $contract_status);
        $this->assign('page', $page->show());
        $this->display();
    }

    //详情统计
    public function detail()
    {
        set_time_limit(0);
        $input = I('get.');
        //账号列表
        $user_list = $this->stat_repository->getProductList();

        $where = $this->stat_repository->getCrawlerDetailParam();
        $where['cids'] = json_encode(array_keys($user_list));
        $stat_list = $this->stat_api_repository->getReportDetail($where);

        $date_list = $this->stat_repository->dateList();
        $count = count($date_list);
        $page = new Page($count, C('LIST_ROWS'));
        $date_stat_show = array_merge($date_list, $stat_list['date_stat_show']);

        $this->detailOrder($date_stat_show);
        //dump($date_stat_show);die;

        $date_stat_show = array_slice($date_stat_show, $page->firstRow, $page->listRows);

        $this->assign('input', $input);
        $this->assign('crawler_channel_list', $this->crawler_channel_list);
        $this->assign('crawler_areas', $this->crawler_areas);
        $this->assign('user_list', $user_list);
        $this->assign('date_stat_show', $date_stat_show);
        $this->assign('total_data', $stat_list['total_data']);
        $this->assign('page', $page->show());
        $this->display();
    }

    //按天导出
    public function exportByDay()
    {
        ini_set('max_execution_time', 0);
        set_time_limit(0);
        ini_set("memory_limit", "1024M");
        //账号列表
        $product_list = $this->stat_repository->getProductList();
        $list = [];
        $data = isset($product_list) ? $product_list : [];

        $where = $this->stat_repository->getCrawlerListParam();

        foreach ($data as $k => &$v) {
            $where['cids'] = json_encode([$v['id']]);
            $date_list = $this->stat_repository->dateList();
            $stat_list = $this->stat_api_repository->getReportDetail($where);
            $date_stat_show = array_merge($date_list, $stat_list['date_stat_show']);
            foreach ($date_stat_show as $kk => $vv) {
                $mm = [
                    'date' => $kk,
                    'total_nums' => 0,
                    'authen_nums' => 0,
                    'authen_pct' => '0.00%',
                    'crawl_nums' => 0,
                    'crawl_pct' => '0.00%',
                    'report_nums' => 0,
                    'tel_num' => 0,
                    'pwd_rt_total' => 0,
                    'pwd_rt_success' => 0,
                    'pwd_rt_pct' => '0.00%',
                    'log_loss_pct' => '0.00%',
                ];
                $date_stat_show[$kk] = $vv ? array_merge($mm, $vv, $v) : array_merge($mm, $v);
            }
            $list[] = $date_stat_show;
        }

        //导出文件
        $file_name = RUNTIME_PATH . 'Cache/bm_crawler_stat_by_day.csv';
        $matching = new BmCrawlerRepository();
        $matching->genTempFileListByDayForRequest($list, $file_name);
        $matching->genFileForFileDownload($file_name);
    }

    /**
     * 提供一个联动的用户筛选
     */
    public function clientList()
    {
        $user_list = $this->stat_repository->clientList();
        $this->__Return($user_list, '', 'success');
    }
    /*
     * 对列表统计数据进行排序
     *
     * @access protected
     * @param $data array 数据
     *
     * @return array
     */
    protected function indexOrder(&$data)
    {
        $field = I('get.field');
        if (empty($field)) {
            return $data;
        }
        $orderFirstArr = [];
        $orderTwiceArr = [];
        foreach ($data as $key=>$item) {
            $orderFirstArr[$key] = isset($item[$field]) ? round($item[$field], 2) : 0;
            $orderTwiceArr[$key] = $item['created_at'];
        }
        $order = I('get.order');
        if ($order=='desc') {
            array_multisort($orderFirstArr, SORT_DESC, $orderTwiceArr, SORT_DESC, $data);
        } else {
            array_multisort($orderFirstArr, SORT_ASC, $orderTwiceArr, SORT_DESC, $data);
        }
        return $data;
    }
    /*
     * 对详细统计数据进行排序
     *
     * @access protected
     * @param $data array 数据
     *
     * @return array
     */
    protected function detailOrder(&$data)
    {
        $field = I('get.field');
        if (empty($field)) {
            return $data;
        }
        $orderFirstArr = [];
        foreach ($data as $key=>$item) {
            $orderFirstArr[$key] = isset($item[$field]) ? round($item[$field], 2) : 0;
        }
        $order = I('get.order');
        if ($order=='desc') {
            array_multisort($orderFirstArr, SORT_DESC, $data);
        } else {
            array_multisort($orderFirstArr, SORT_ASC, $data);
        }
        return $data;
    }
}
