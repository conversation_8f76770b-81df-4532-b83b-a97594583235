<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\WechatManageRepository;

class WechatManageController extends AdminController
{
    private $repository;

    /**
     * WechatManageController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->repository = new WechatManageRepository();
    }

    /**
     * 微信通知配置列表view
     */
    public function listWechat()
    {
        $this->display('list');
    }

    /**
     * 微信通知create view
     */
    public function create()
    {
        $this->display('create');
    }

    /**
     * 微信通知edit view
     */
    public function edit()
    {
        try {
            // 编辑的微信配置
            $wechat_warning = $this->repository->edit();
            $this->assign(compact('wechat_warning'));
            $this->display('edit');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
