<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\CuishouStatRepository;
use Home\Repositories\CuishouStatFileRepository;

class CuishouStatFileController extends AdminController
{
    //  统计的repository
    protected $stat_repository;

    // 生成文件的repository
    protected $stat_file_repository;

    public function __construct()
    {
        parent::__construct();
        $this->stat_repository = (new CuishouStatRepository());
        $this->stat_file_repository = (new CuishouStatFileRepository());
        set_time_limit(600);
    }

    public function index()
    {
        // 获取统计信息
        $stat_list = $this->listNoCache();
        $file_name = RUNTIME_PATH . 'Cache/cuishou_list.csv';

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->stat_file_repository->genTempFileListForRequest($stat_list, $file_name);

        //为fileDownload插件生成文件
        $this->stat_file_repository->genFileForFileDownload($file_name);
        exit();
    }

    public function cuishouDetail()
    {
        // 数据统计
        $stat_list = $this->detailNoCache();

        // 临时文件名
        $file_name = $this->tempFileNameForDetail();

        // 生成临时文件,为fileDownLoad插件铺垫
        $this->stat_file_repository->genTempFileDetailForRequest($stat_list, $file_name);

        // 为fileDownload插件生成文件
        $this->stat_file_repository->genFileForFileDownload($file_name);
        exit();
    }

    /**
     * 详情页临时文件名
     * @return string
     */
    protected function tempFileNameForDetail()
    {
        $begin = I('get.begin', '', 'strtotime');
        $end = I('get.end', '', 'strtotime');
        $choose_id = I('get.id', '');
        if (!$begin && !$end) {
            $begin = time() - 86400 * 30;
            $end = time();
        }
        $day_begin = date('Ymd', $begin);
        $day_end = date('Ymd', $end);
        return RUNTIME_PATH . 'Cache/' . $day_begin . '-' . $day_end . '-' . $choose_id . '.csv';
    }

    /**
     *  获取列表数据
     * @return array
     */
    protected function listNoCache()
    {
        $choose_id = I('get.id', '', 'trim');

        // 选择特定客户
        if ($choose_id) {
            return $this->chooseOneForList();
        }

        // 选择所有客户
        return $this->chooseAllForList();
    }

    /**
     * 列表页选择所有用户的数据统计
     * @return array
     */
    protected function chooseAllForList()
    {
        $user_list = $this->stat_repository->userListDependGet();

        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // 所有用户的当前条件下的统计信息 && 分页
        $user_list_stat = $this->stat_repository->personalStatForList($user_list, ['time' => $where_date]);
        return $user_list_stat['user_list'];
    }

    /**
     * 列表页选择单个账户的时候的统计
     * @return array
     */
    protected function chooseOneForList()
    {

        $user_list = $this->stat_repository->userListDependGet();

        // init params
        $input = I('get.');
        $choose_id = I('get.id', '', 'trim');

        // time limit
        $where_date = $this->stat_repository->timeLimitForList();

        // 选中用户当前条件下的统计信息
        $where['time'] = $where_date;
        $where['uid'] = new \MongoInt32($choose_id);
        $total_data = $this->stat_repository->chooseOneStat($where);

        // show choose user
        $user_index_id = array_column($user_list, null, 'id');
        $input['developer'] = $user_index_id[$choose_id]['developer'];
        $user_show[0] = array_merge($total_data, $user_index_id[$choose_id]);

        return $user_show;
    }

    /**
     * 详情页获取数据
     * @return mixed
     */
    protected function detailNoCache()
    {
        // init params
        $choose_id = I('get.id', '', 'trim');

        // 时间限制 && 需要展示的日期列表
        $where_date = $this->stat_repository->timeLimitForDetail();
        $date_list = $this->stat_repository->showDateList();

        // 这段日期 各天的统计信息
        $where = [
            'uid' => new \MongoInt32($choose_id),
            'time' => $where_date
        ];
        return $this->stat_repository->dailyStat($where, $date_list);
    }
}
