<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\TelStatusStatRepository;

class TelStatusStatController extends AdminController
{
    protected $telStatusStat;

    public function _initialize()
    {
        parent::_initialize();
        $this->telStatusStat = new TelStatusStatRepository();
    }

    public function index()
    {
        $input = I('get.');
        // 签约状态列表
        $contract_status = $this->telStatusStat->getContractStatus();
        //客户列表
        $customer_list = $this->telStatusStat->getCustomerList();
        // 调用量
        $data = $this->telStatusStat->getTelStatusListStat();

        $count = count($data['list']);
        $page = new Page($count, C('LIST_ROWS'));
        $list = array_slice($data['list'], $page->firstRow, $page->listRows);
        $this->assign('input', $input);
        $this->assign('contract_status', $contract_status);
        $this->assign('customer_list', $customer_list);
        $this->assign('list', $list);
        $this->assign('total', $data['total']);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function detail()
    {
        $input = I('get.');

        $customer_list = $this->telStatusStat->getCustomerList($input);

        $data = $this->telStatusStat->getTelStatusDetailStat();

        $count = count($data['list']);
        $page = new Page($count, C('LIST_ROWS'));
        $list = array_slice($data['list'], $page->firstRow, $page->listRows);

        $this->assign('input', $input);
        $this->assign('customer_list', $customer_list);
        $this->assign('list', $list);
        $this->assign('total', $data['total']);
        $this->assign('page', $page->show());
        $this->display();
    }
}