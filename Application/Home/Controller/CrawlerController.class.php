<?php

namespace Home\Controller;

use \Common\Controller\AdminController;
use Home\Repositories\CrawlerRepository;
use Home\Repositories\CrawlerChannelRepository;
use Home\Repositories\ChannelConfigRepository;

class CrawlerController extends AdminController
{
    private $crawler_areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西', '广东',
        '广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏', '贵州', '辽宁',
        '重庆', '陕西', '青海', '黑龙江',];

    private $channel_type;

    private $flow_map = ['10086' =>'移动', '10010' => '联通', '189' => '电信'];

    private $repository_crawler;

    private $crawler_channel;

    private $channel_config;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_crawler = new CrawlerRepository();
        $this->crawler_channel = new CrawlerChannelRepository();
        $this->channel_config = new ChannelConfigRepository();
        $this->channel_type = $this->crawler_channel->getCrawlerlList();
    }

    //爬虫列表
    public function lists()
    {
        $flow_type = I('get.flow_type', 'all', 'trim');
        $channelCheck = I('get.channel', 'all', 'trim');
        $whsupport = I('get.whsupport', 'all', 'trim');

        //服务器切换列表
        $switch_list = $this->crawler_channel->getSwitchList();
        //不支持省份列表
        $unsur_list = $this->crawler_channel->getUnsurportList();
        //爬虫渠道地址列表
        $channel_list = $this->crawler_channel->getChannelList($this->flow_map);
        //重置密码渠道地址列表
        $pwd_channel = $this->crawler_channel->getPwdChannelList();

        $flow = ($flow_type == 'all') ? $this->flow_map : [$flow_type => $this->flow_map[$flow_type]];
        $list = [];
        $i = 0;
        foreach ($flow as $key => $val) {
            foreach ($this->crawler_areas as $kk => $vv) {
                $surpport = isset($unsur_list[$key.'-'.$vv]) ? '未支持' : '已支持';
                $switch_key = md5($vv.$key);
                $channel = isset($unsur_list[$key.'-'.$vv]) ? 'thd' : 'loc';
                $channel = isset($switch_list[$switch_key]['type']) ? $switch_list[$switch_key]['type'] : $channel;

                if ($channelCheck != 'all' && $channelCheck != $channel) {
                    continue;
                }
                if ($whsupport != 'all' && $whsupport != $surpport) {
                    continue;
                }
                $list[$i]['area'] = $vv;
                $list[$i]['flow_type'] = $key;
                $list[$i]['name'] = $vv.$val;
                $list[$i]['surpport'] = $surpport;
                $list[$i]['channel'] = $channel;
                $list[$i]['channel_name'] = $this->channel_type[$channel];
                $list[$i]['dist'] = isset($switch_list[$switch_key]['dist']) ? $switch_list[$switch_key]['dist'] : '';
                $list[$i]['crawler_channel'] = isset($channel_list[$vv.$val]) ? $channel_list[$vv.$val] : '';
                $list[$i]['pwd_channel'] = isset($pwd_channel[$vv.$val]) ? $pwd_channel[$vv.$val] : '';
                $i++;
            }
        }

        // 是否显示一键切换
        $show_channel_one_key = $this->repository_crawler->showOneKeyChannel();

        $yulore_spare = C('yulore_spare');
        $this->assign('yulore_spare', $yulore_spare);
        $this->assign('show_channel_one_key', $show_channel_one_key);
        $this->assign('flow_type', $flow_type);
        $this->assign('flow_map', $this->flow_map);
        $this->assign('channelCheck', $channelCheck);
        $this->assign('channel', $this->channel_type);
        $this->assign('whsupport', $whsupport);
        $this->assign('list', $list);
        $this->display();
    }

    //服务器切换
    public function channelSwitch()
    {
        if (!IS_POST) {
            $yulore_spare = C('yulore_spare');
            $selchannel = I('get.channel', '', 'trim');
            $surpport = I('get.surpport', '', 'trim');
            $this->assign('channel', $this->channel_type);
            $this->assign('yulore_spare', $yulore_spare);
            $this->assign('selchannel', $selchannel);
            $this->assign('surpport', $surpport);
            $this->assign('areas', $this->crawler_areas);
            $this->display();
            exit;
        }

        try {
            $param = $this->crawler_channel->getCheckParam();
            $this->crawler_channel->setCrawlerSwitch($param);
        } catch (\Exception $e) {
            $this->__Return($e->getMessage(), '', 'tip_error');
        }
        // log info
        $ochannel = I('post.ochannel', '', 'trim');
        $admin = $_SESSION['site_login_name'];
        $created_at = time();
        $nchannel = I('post.channel', '', 'trim');
        $flow_type = $param['flow_type'];
        $area = $param['area'];
        $reason = I('post.reason', '', 'trim');
        $data = compact('ochannel', 'admin', 'area', 'flow_type', 'nchannel', 'created_at', 'reason');
        M('ChannelSwitchLog')->add($data);
        $this->__Return('切换成功！', '', 'tip_success');
    }

    //支持未支持
    public function switchBack()
    {
        // filter flow_type area
        $flow_type = I('post.flow_type', '', 'trim');
        $area = I('post.area', '', 'trim');
        if (!$area || !in_array($area, $this->crawler_areas)) {
            $this->__Return('请选择有效的羽乐服务器所属省份！');
        }
        if (!$flow_type || !in_array($flow_type, array_keys($this->flow_map))) {
            $this->__Return('请选择有效的运营商！');
        }

        try {
            $unsur = I('post.surpport', '', 'trim');
            $this->crawler_channel->setUnsurports(compact('flow_type', 'area'), $unsur);
        } catch (\Exception $e) {
            $this->__Return($e->getMessage(), '', 'error');
        }
        // gen log info
        $nchannel = ($unsur == '未支持') ? '未支持' : '支持';
        $admin = $_SESSION['site_login_name'];
        $ochannel = I('post.channel', '', 'trim');
        $created_at = time();
        M('ChannelSwitchLog')->add(compact('admin', 'ochannel', 'nchannel', 'created_at', 'flow_type', 'area'));
        $this->__Return('支持状态设置成功！', '', 'success');
    }

    //爬虫渠道切换
    public function crawlerChannel()
    {
        $type = I('post.type', 0, 'intval');
        $channel_type = 1;
        $flow_type = I('post.flow_type', '', 'trim');
        $area = I('post.area', '', 'trim');
        if (!$area || !in_array($area, $this->crawler_areas)) {
            $this->__Return('请选择有效的羽乐科技服务器所属省份！');
        }
        if (!$flow_type || !in_array($flow_type, array_keys($this->flow_map))) {
            $this->__Return('请选择有效的运营商！');
        }

        $crawler_name = $area.$this->flow_map[$flow_type];
        if ($type == 1) {
            $list = $this->crawler_channel->getChannelConfigList(compact('area', 'flow_type', 'channel_type'), $this->flow_map);
            if (!$list) {
                $this->__Return('请先到爬虫渠道进行配置', '', 'error');
            }
            $this->__Return('获取渠道名称成功', $list, 'success');
        }
        if ($type == 2) {
            $channel_name = I('post.channel_name', '', 'trim');
            $channel_info = $this->channel_config->getChannelConfigInfo(compact('channel_name', 'crawler_name', 'channel_type'));
            if (!$channel_info) {
                $this->__Return('请先到爬虫渠道进行配置', '', 'error');
            }
            try {
                $this->crawler_channel->setBmpCrawlChannel(compact('flow_type', 'area'), $channel_info['channel_path']);
                $this->channel_config->updateChannelFlagByParam(compact('channel_name', 'crawler_name', 'channel_type'));
            } catch (\Exception $e) {
                $this->__Return($e->getMessage(), '', 'error');
            }
            $admin = $_SESSION['site_login_name'];
            $nchannel = '爬虫渠道切换';
            $created_at = time();
            M('ChannelSwitchLog')->add(compact('admin', 'nchannel', 'created_at', 'flow_type', 'area'));
            $this->__Return('爬虫渠道切换成功', '','success');
        }
    }

    /**
     * 重置密码渠道切换
     */
    public function pwdChannel()
    {
        $type = I('post.type', 0, 'intval');
        $channel_type = 2;
        $flow_type = I('post.flow_type', '', 'trim');
        $area = I('post.area', '', 'trim');
        if (!$area || !in_array($area, $this->crawler_areas)) {
            $this->__Return('请选择有效的羽乐科技服务器所属省份！');
        }
        if (!$flow_type || !in_array($flow_type, array_keys($this->flow_map))) {
            $this->__Return('请选择有效的运营商！');
        }
        $crawler_name = $area.$this->flow_map[$flow_type];
        if ($type == 1) {
            $list = $this->crawler_channel->getChannelConfigList(compact('area', 'flow_type', 'channel_type'), $this->flow_map);
            if (!$list) {
                $this->__Return('请先到重置密码渠道进行配置', '', 'error');
            }
            $this->__Return('获取渠道名称成功', $list, 'success');
        }
        if ($type == 2) {
            $channel_name = I('post.channel_name', '', 'trim');
            $channel_info = $this->channel_config->getChannelConfigInfo(compact('channel_name', 'crawler_name', 'channel_type'));
            if (!$channel_info) {
                $this->__Return('请先到重置密码渠道进行配置', '', 'error');
            }
            try {
                $this->crawler_channel->setPwdChannel(['flow_type' => $this->flow_map[$flow_type], 'area' => $area], $channel_info['channel_path']);
                $this->channel_config->updateChannelFlagByParam(compact('channel_name', 'crawler_name', 'channel_type'));
            } catch (\Exception $e) {
                $this->__Return($e->getMessage(), 'error');
            }
            $admin = $_SESSION['site_login_name'];
            $nchannel = '重置密码渠道切换';
            $created_at = time();
            M('ChannelSwitchLog')->add(compact('admin', 'nchannel', 'created_at', 'flow_type', 'area'));
            $this->__Return('重置密码渠道切换成功', '','success');
        }
    }
}
