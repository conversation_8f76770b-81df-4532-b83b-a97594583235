<?php
namespace Home\Controller;


use Common\ORG\Page;
use Common\Controller\AdminController;
use Common\Model\EmailConfigModel;

class EmailConfigController extends AdminController
{
    protected $emailconfig;
    public function __construct()
    {
        parent::__construct();
        $this->emailconfig = new EmailConfigModel();

    }

    public function index()
    {
        $input = I('get.');

        $where = [];
        if(!empty($input['search_address'])){
            $where['address'] = $input['search_address'];
        }

        if(!empty($input['search_scene'])){
            $where['scene'] = $input['search_scene'];
        }
        $count = $this->emailconfig
            ->where($where)
            ->count();
        //分页
        $listRow = 30;
        $page = new Page($count, $listRow);
        //查询数据
        $list_data = $this
            ->emailconfig
            ->where($where)
            ->order('id desc')
            ->limit($page->firstRow, $page->listRows)
            ->select();
        //获取所有邮箱

        $this->assign("list_data", $list_data);
        $this->assign("page", $page->show());
        $this->assign("input", $input);
        $this->display();
    }

    /**
     * 添加邮箱
     */
    public function add(){
        $param = I('post.');
        $name = trim($param['name']);
        $address = trim($param['address']);
        $scene = trim($param['scene']);
        $info = $this->emailconfig->where(['address'=>$address, 'scene'=>$scene])->find();
        if(!empty($info)){
            $result = ['status' => 'error', 'msg' => '该场景已存在'];
            $this->ajaxReturn($result);
        }
        $data = [
            'name' => $name,
            'address' => $address,
            'scene' => $scene,
            'type' => $param['type'],
            'status' => $param['status'],
            'remark' => $param['remark']
        ];
        $res = $this->emailconfig->add($data);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }
    /**
     * 获取编辑信息
     *
     */
    public function getEdit(){
        $id = I('post.id');
        $res = $this->emailconfig->where(['id'=>$id])->find();
        $this->ajaxReturn($res);
    }

    public function edit(){
        $param = I('post.');
        $name = trim($param['name2']);
        $address = trim($param['address2']);
        $scene = trim($param['scene2']);
        $where['id'] = ['neq', $param['id2']];
        $where['address'] = $address;
        $where['scene'] = $scene;
        $info = $this->emailconfig->where($where)->find();
        if(!empty($info)){
            $result = ['status' => 'error', 'msg' => '该场景已存在'];
            $this->ajaxReturn($result);
        }
        $data = [
            'name' => $name,
            'address' => $address,
            'scene' => $scene,
            'type' => $param['type2'],
            'status' => $param['status2'],
            'remark' => $param['remark2']
        ];
        $res = $this->emailconfig->where(['id'=>$param['id2']])->save($data);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }

}