<?php

namespace Home\Controller;

use Common\Model\DictItagtelLogModel;
use Common\ORG\Page;
use Common\Controller\AdminController;

class DictItagtelLogController extends AdminController
{

    public function index()
    {
        $input = I('get.');
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $itag_id = I('get.itag_id', '', 'trim');
        $up_status = I('get.up_status', '', 'trim');

        $where = [];
        if ($begin && $end){
            $where['utime'] = ['between', [$begin, $end.' 23:59:59']];
        }elseif($begin){
            $where['utime'] = ['egt', $begin];
        }elseif($end){
            $where['utime'] = ['elt', $end. ' 23:59:59'];
        }
        if ($itag_id){
            $where['itag_id'] = $itag_id;
        }
        if ($up_status !== ''){
            $where['up_status'] = $up_status;
        }

        //号码类型
        $tag_list = D('DictItag')->where("status=1")->order('id')->select();

        //日志列表
        $count = D('DictItagtelLog')->where($where)->count();
        $page_obj = new Page($count, C('LIST_ROWS'));

        // log list
        $log_list = D('DictItagtelLog')->where($where)->order('id desc')->limit($page_obj->firstRow, $page_obj->listRows)->select();
        // 格式化输出
        $log_list = D('DictItagtelLog')->formatDictItagtelLogs($log_list, $tag_list);

        $this->assign('tag_list', $tag_list);
        $this->assign('log_list', $log_list);
        $this->assign('page', $page_obj->show());
        $this->assign('input', $input);
        $this->display();
    }

    /**
     * 导出记录日志
     */
    public function export(){
        $dict_itagtel_log = new DictItagtelLogModel();
        $log_list = $dict_itagtel_log->getDictItagtelLogList();

        //号码类型
        $tag_list = D('DictItag')->where("status=1")->order('id')->select();
        //格式化输出
        $log_list = $dict_itagtel_log->formatDictItagtelLogs($log_list, $tag_list);

        $file_name = RUNTIME_PATH . 'Cache/dict_itagtel_log_list.csv';
        $dict_itagtel_log->genTempFileListForRequest($log_list, $file_name);
        $dict_itagtel_log->genFileForFileDownload($file_name);
        exit();
    }
}
