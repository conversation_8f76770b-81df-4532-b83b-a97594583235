<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\Model\FinanceAccountsModel;
use Common\Model\FinanceAccountProductModel;
use Common\Model\CuishouUserModel;
use Common\Model\FeeCrawlerConfigModel;
use Common\Model\FeeCuishouConfigModel;
use Common\Model\FeeMatchingConfigModel;
use Common\Model\FeeCrawlerStatModel;
use Common\Model\FeeCuishouStatModel;
use Common\Model\FeeMatchingStatModel;
use Common\ORG\Page;

class FeeAccountStatController extends AdminController
{
    protected $finace_account;
    protected $crawler_fee;
    protected $cuishou_fee;
    protected $matching_fee;

    public function __construct()
    {
        parent::__construct();
        $this->finace_account = new FinanceAccountsModel();
        $this->crawler_fee = new FeeCrawlerStatModel();
        $this->cuishou_fee = new FeeCuishouStatModel();
        $this->matching_fee = new FeeMatchingStatModel();
    }

    /**
     * 统计列表页
     */
    public function index()
    {
        $params = I('get.');
        $account_map = $map = [];
        if (!empty($params['id'])) {
            $account_map['id'] = $map['account_id'] = $params['id'];
        }
        if (!empty($params['name'])) {
            $account_map['name'] = $map['account_name'] = $params['name'];
        }
        $account_info = $this->finace_account->where($account_map)->order('id DESC')->select();
        if ($params['begin'] && $params['end']) {
            $start_time = $params['begin'];
            $end_time = $params['end'];
        } else {
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $map['fee_date'] = [['egt', $start_time], ['elt', $end_time]];
        $fee_crawler = $this->crawler_fee
            ->where($map)
            ->index('account_id')
            ->sum('fee_price');
        $fee_cuishou = $this->cuishou_fee
            ->where($map)
            ->index('account_id')
            ->sum('fee_price');
        $fee_matching = $this->matching_fee
            ->where($map)
            ->index('account_id')
            ->sum('fee_price');
        $fee_bang = D('BangProductFeeStat')->where($map)->index('account_id')->sum('fee_price');
        $fee_risklist = D('RiskListFeeStat')->where($map)->index('account_id')->sum('fee_price');
        $list_stat['fee_total'] = round($fee_crawler+$fee_cuishou+$fee_matching+$fee_bang+$fee_risklist,2);
        foreach ($account_info as $key=>$value){
            $map['account_id'] = $value['id'];
            $crawler_stat = $this->crawler_fee
                ->where($map)
                ->index('account_id')
                ->sum('fee_price');
            $cuishou_stat = $this->cuishou_fee
                ->where($map)
                ->index('account_id')
                ->sum('fee_price');
            $matching_stat = $this->matching_fee
                ->where($map)
                ->index('account_id')
                ->sum('fee_price');
            $bang_stat = D('BangProductFeeStat')->where($map)->index('account_id')->sum('fee_price');
            $risklist_stat = D('RiskListFeeStat')->where($map)->index('account_id')->sum('fee_price');
            $info_stat[$key]['id'] = $value['id'];
            $info_stat[$key]['name'] = $value['name'];
            $info_stat[$key]['company'] = $value['company'];
            $info_stat[$key]['fee_stat'] = round($crawler_stat+$cuishou_stat+$matching_stat+$bang_stat+$risklist_stat,2);
        }
        $Page = new Page(count($info_stat),20);
        $info_stat = array_slice($info_stat, $Page->firstRow, $Page->listRows);
        $show = $Page->show();
        $list_stat['data'] = $info_stat;
        $this->assign('list_stat', $list_stat);
        $this->assign('page', $show);
        $this->assign('input', $params);
        $this->display();
    }

    /**
     * 产品对账单页
     */
    public function productList()
    {
        // init params
        $params = I('get.');

        if(!$params['id']){
            $this->__Return('缺少必要的参数', '', 'tip_error');
        }
        $account_info = (new FinanceAccountProductModel())->where(['account_id'=>$params['id']])->order('type_id, product_id')->select();
        $map = [];
        if($params['begin'] && $params['end']){
            $start_time = $params['begin'];
            $end_time = $params['end'];
        }else{
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $map['fee_date'] = [['egt', $start_time], ['elt', $end_time]];
        $map['account_id'] = $params['id'];
        //客户产品总计
        $crawler_amount_stat = $this->crawler_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_amount');
        $crawler_fee_stat = $this->crawler_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_price');
        $matching_amount_stat = $this->matching_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_amount');
        $matching_fee_stat = $this->matching_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_price');
        $cuishou_amount_stat = $this->cuishou_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_amount');
        $cuishou_fee_stat = $this->cuishou_fee
            ->where($map)
            ->index('product_id')
            ->sum('fee_price');
        $bang_amount_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_amount');
        $bang_fee_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_price');
        $risklist_amount_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_amount');
        $risklist_fee_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_price');

        $list_stat['amount_total'] = intval($crawler_amount_stat)+intval($matching_amount_stat)+intval($cuishou_amount_stat)+intval($bang_amount_stat)+intval($risklist_amount_stat);
        $list_stat['fee_total'] = round($crawler_fee_stat+$matching_fee_stat+$cuishou_fee_stat+$bang_fee_stat+$risklist_fee_stat,2);
        $CuishouUser = new CuishouUserModel();
        if($account_info){
            foreach ($account_info as $key=>$value){
                $product_id = $value['product_id'];
                $map['product_id'] = $product_id;
                $info_stat[$key]['product_id'] = $product_id;
                $type_id = $value['type_id'];
                $info_stat[$key]['type_id'] = $type_id;
                switch ($type_id) {
                    case 1:
                        $info_stat[$key]['product_type'] = '邦秒爬';
                        // 条件
                        $product_info = D('Auth')
                            ->field('id,developer')
                            ->index('id')
                            ->find($product_id);
                        $info_stat[$key]['product_name'] = $product_info['developer'];
                        //产品账号配置
                        $config_product = (new FeeCrawlerConfigModel())
                            ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                            ->order('id DESC')
                            ->find();
                        if($config_product){
                            $fee_basis_list = $this->crawler_fee->getFeeBasis();
                            $fee_method_list = $this->crawler_fee->getFeeMethod();
                            $info_stat[$key]['fee_basis'] = $fee_basis_list[$config_product['fee_basis']];
                            $info_stat[$key]['fee_method'] = $fee_method_list[$config_product['fee_method']];
                        }else{
                            $info_stat[$key]['fee_basis'] = '未配置';
                            $info_stat[$key]['fee_method'] = '未配置';
                        }
                        //用量计费
                        $amount_stat = $this->crawler_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_amount');
                        $fee_stat = $this->crawler_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_price');
                        break;
                    case 2:
                        $info_stat[$key]['product_type'] = '邦秒配';
                        $product_info = D('AdminApikey')
                            ->index('id')
                            ->field('id,owner')
                            ->find($product_id);
                        $info_stat[$key]['product_name'] = $product_info['owner'];
                        //产品账号配置
                        $config_product = (new FeeMatchingConfigModel())
                            ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                            ->order('id DESC')
                            ->find();
                        if($config_product){
                            $fee_basis_list = $this->matching_fee->getFeeBasis();
                            $fee_method_list = $this->matching_fee->getFeeMethod();
                            $info_stat[$key]['fee_basis'] = $fee_basis_list[$config_product['fee_basis']];
                            $info_stat[$key]['fee_method'] = $fee_method_list[$config_product['fee_method']];
                        }else{
                            $info_stat[$key]['fee_basis'] = '未配置';
                            $info_stat[$key]['fee_method'] = '未配置';
                        }
                        //用量计费
                        $amount_stat = $this->matching_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_amount');
                        $fee_stat = $this->matching_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_price');
                        break;
                    case 3:
                        $info_stat[$key]['product_type'] = '催收分';
                        $product_info = $CuishouUser
                            ->where(['id' => intval($product_id)])
                            ->find();
                        $info_stat[$key]['product_name'] = $product_info['developer'];
                        //产品账号配置
                        $config_product = (new FeeCuishouConfigModel())
                            ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                            ->order('id DESC')
                            ->find();
                        if($config_product){
                            $fee_basis_list = $this->cuishou_fee->getFeeBasis();
                            $fee_method_list = $this->cuishou_fee->getFeeMethod();
                            $info_stat[$key]['fee_basis'] = $fee_basis_list[$config_product['fee_basis']];
                            $info_stat[$key]['fee_method'] = $fee_method_list[$config_product['fee_method']];
                        }else{
                            $info_stat[$key]['fee_basis'] = '未配置';
                            $info_stat[$key]['fee_method'] = '未配置';
                        }
                        //用量计费
                        $amount_stat = $this->cuishou_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_amount');
                        $fee_stat = $this->cuishou_fee
                            ->where($map)
                            ->index('product_id')
                            ->sum('fee_price');
                        break;
                    case 4:
                        $info_stat[$key]['product_type'] = '邦企查';
                        $product_info = D('BangProducts')->where(['id' => $product_id])->field('name')->find();
                        $info_stat[$key]['product_name'] = $product_info['name'];
                        $config_product = D('BangProductFeeConfig')->where(['product_id' => $product_id, 'is_delete' => '0'])
                                                                   ->order('id desc')
                                                                   ->find();
                        $fee_basis_list = D('BangProductFeeConfig')->getFeeBasis();
                        $fee_method_list = D('BangProductFeeConfig')->getFeeMethod();
                        $info_stat[$key]['fee_basis'] = isset($config_product['fee_basis']) ? $fee_basis_list[$config_product['fee_basis']] : '未配置';
                        $info_stat[$key]['fee_method'] = isset($config_product['fee_method']) ? $fee_method_list[$config_product['fee_method']] : '未配置';
                        $amount_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_amount');
                        $fee_stat = D('BangProductFeeStat')->where($map)->index('product_id')->sum('fee_price');
                        break;
                    case 6:
                        $info_stat[$key]['product_type'] = '风险名单';
                        $product_info = D('RiskListUser')->field(['_id' => 0])->where(['id' => (int)$product_id])->find();
                        $info_stat[$key]['product_name'] = $product_info['developer'];
                        $config_product = D('RiskListFeeConfig')->where(['product_id' => $product_id, 'is_delete' => 0])
                                                                ->order('id desc')
                                                                ->find();
                        $fee_basis_list = D('RiskListFeeConfig')->getFeeBasis();
                        $fee_method_list = D('RiskListFeeConfig')->getFeeMethod();
                        $info_stat[$key]['fee_basis'] = !empty($config_product['fee_basis']) ? $fee_basis_list[$config_product['fee_basis']] : '未设置';
                        $info_stat[$key]['fee_method'] = !empty($config_product['fee_method']) ? $fee_method_list[$config_product['fee_method']] : '未设置';
                        $amount_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_amount');
                        $fee_stat = D('RiskListFeeStat')->where($map)->index('product_id')->sum('fee_price');
                        break;
                }
                $info_stat[$key]['amount_total'] = $amount_stat ? $amount_stat : 0;
                $info_stat[$key]['fee_total'] = round($fee_stat,2);
            }
        }
        $Page = new Page(count($info_stat),20);
        $info_stat = array_slice($info_stat, $Page->firstRow, $Page->listRows);
        $list_stat['data'] = $info_stat;
        $show = $Page->show();
        $this->assign('list_stat', $list_stat);
        $this->assign('page', $show);
        $this->assign('input', $params);
        $this->display();
    }

    /**
     * 产品计费日详情页
     */
    public function productDetail()
    {
        // init params
        $params = I('get.');

        if(!$params['product_id'] || !$params['type_id']){
            $this->__Return('缺少必要的参数', '', 'tip_error');
        }
        $product_id = $params['product_id'];
        $info_stat['product_id'] = $product_id;
        $type_id = $params['type_id'];
        $info_stat['type_id'] = $type_id;

        $map = [];
        if($params['begin'] && $params['end']){
            $start_time = $params['begin'];
            $end_time = $params['end'];
        }else{
            $start_time = date('Y-m-d',time()-3600*24);
            $end_time = date('Y-m-d',time()-3600*24);
        }
        $map['fee_date'] = [['egt', $start_time], ['elt', $end_time]];

        //产品账号配置
        $map['product_id'] = $params['product_id'];
        $info_stat['fee_method'] = 0;

        switch ($type_id) {
            case 1:
                $product_info = D('Auth')
                    ->field('id,developer')
                    ->index('id')
                    ->find($product_id);
                $info_stat['product_name'] = $product_info['developer'];
                //产品账号配置
                $config_product = (new FeeCrawlerConfigModel())
                    ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                    ->order('id DESC')
                    ->find();
                if($config_product){
                    $info_stat['fee_method'] = $config_product['fee_method'];
                }
                //用量计费
                $amount_stat = $this->crawler_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_amount');
                $fee_stat = $this->crawler_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_price');
                $info_stat['data'] = $this->crawler_fee
                    ->where($map)
                    ->field(['fee_method','fee_amount','fee_price','fee_date'])
                    ->order('fee_date desc')
                    ->select();
                break;
            case 2:
                $product_info = D('AdminApikey')
                    ->index('id')
                    ->field('id,owner')
                    ->find($product_id);
                $info_stat['product_name'] = $product_info['owner'];
                //产品账号配置
                $config_product = (new FeeMatchingConfigModel())
                    ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                    ->order('id DESC')
                    ->find();
                if($config_product){
                    $info_stat['fee_method'] = $config_product['fee_method'];
                }
                //用量计费
                $amount_stat = $this->matching_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_amount');
                $fee_stat = $this->matching_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_price');
                $info_stat['data'] = $this->matching_fee
                    ->where($map)
                    ->field(['fee_method','fee_amount','fee_price','fee_date'])
                    ->order('fee_date desc')
                    ->select();
                break;
            case 3:
                $CuishouUser = new CuishouUserModel();
                $product_info = $CuishouUser
                    ->where(['id' => intval($product_id)])
                    ->find();
                $info_stat['product_name'] = $product_info['developer'];
                //产品账号配置
                $config_product = (new FeeCuishouConfigModel())
                    ->where(['product_id'=>$product_id,'is_delete'=>'0'])
                    ->order('id DESC')
                    ->find();
                if($config_product){
                    $info_stat['fee_method'] = $config_product['fee_method'];
                }
                //用量计费
                $amount_stat = $this->cuishou_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_amount');
                $fee_stat = $this->cuishou_fee
                    ->where($map)
                    ->index('product_id')
                    ->sum('fee_price');
                $info_stat['data'] = $this->cuishou_fee
                    ->where($map)
                    ->field(['fee_method','fee_amount','fee_price','fee_date'])
                    ->order('fee_date desc')
                    ->select();
                break;
        }
        $info_stat['amount_total'] = $amount_stat ? $amount_stat : 0;
        $info_stat['fee_total'] = round($fee_stat,2);
        $this->assign('info_stat', $info_stat);
        $this->assign('input', $params);
        $this->display();
    }
}
