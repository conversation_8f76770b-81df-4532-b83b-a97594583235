<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\ChannelAKeySwitchRepository;
use Home\Repositories\CrawlerChannelRepository;
use Think\Cache\Driver\Redis;

class ChannelAKeySwitchController extends AdminController
{
    private static $flow_type = ['189', '10086', '10010'];
    private static $crawler_areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西', '广东',
        '广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏', '贵州', '辽宁',
        '重庆', '陕西', '青海', '黑龙江',];
    private static $switch_tag = 'calls_switch_channel_tag';

    private $repository_channel_key;
    private $crawler_channel;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_channel_key = new ChannelAKeySwitchRepository();
        $this->crawler_channel = new CrawlerChannelRepository();

        // 是否有一键切换的权限
        $permission_access = $this->repository_channel_key->canAccess();
        if (!$permission_access) {
            return $this->__Return('抱歉 您没有一键切换服务器的权限');
        }
    }

    public function ChannelKeySwitch()
    {
        $channel = I('get.channel', '', 'trim');
        $param['flow_type'] = I('get.flow_type', '', 'trim');
        $param['sel_channel'] = I('get.sel_channel', '', 'trim');
        $param['support'] = I('get.support', '', 'trim');
        $param['channel'] = $channel;

        $list = $this->crawler_channel->getCrawlerlList();
        if (!in_array($channel, array_keys($list))) {
            $this->__Return('请选择有效的一键切换类型', '', 'error');
        }
        $this->channelCrawler($param, $list);
    }

    public function channelCrawler($param, $list)
    {
        try {
            $ext_url = I('get.ext_url', '', 'trim');
            $redis = new Redis(C('REDIS_CHANNEL_CONFIG'));
            // 通道切换
            $info_redis = [
                'dist' => $ext_url,
                'des' => '第三方通道：'.$list[$param['channel']],
                'type' => $param['channel']
            ];
            $key_channel_list = $this->channelKeys($param, $info_redis);
            if ($param['channel'] == 'loc') {
                $result = $redis->hdel(self::$switch_tag, array_keys($key_channel_list));
            } else {
                $result = $redis->hmset(self::$switch_tag, $key_channel_list);
            }
            // 回复 && log
            if ($result) {
                $info_log = [
                    'admin' => $_SESSION['site_login_name'],
                    'nchannel' => '一键切换'.$param['flow_type'].$param['sel_channel'].$param['surpport'].'到'.$list[$param['channel']],
                    'created_at' => time(),
                    'reason' => I('get.reason', '', 'trim')
                ];
                $this->log($info_log);
                $this->__Return('一键切换成功', '', 'success');
            }

            $this->__Return('一键切换失败', '', 'error');

        } catch (\Exception $e) {
            $msg_error = '一键切换失败 : ' . $e->getMessage();
            $this->__Return($msg_error, '', 'error');
        }
    }

    /**
     * 记录日志
     * @param $info_log
     */
    protected function log($info_log)
    {
        M('ChannelSwitchLog')->add($info_log);
    }

    /**
     * 获取切换的key value 集合
     * @param $flow_type_list
     * @param array $info_redis redis中存储的value
     * @return array
     */
    protected function channelKeys($param, $info_redis)
    {
        //服务器切换列表
        $switch_list = $this->crawler_channel->getSwitchList();
        $unsur_list = $this->crawler_channel->getUnsurportList();
        $flow_type = ($param['flow_type'] == 'all') ? self::$flow_type : [$param['flow_type']];
        $redis_keys = [];
        foreach ($flow_type as $key => $value) {
            foreach (self::$crawler_areas as $kk => $vv) {
                // 电信不支持的省份不在切换的范围内
                if (isset($unsur_list[$value.'-'.$vv]) && in_array($param['channel'], ['loc', 'ext'])) {
                    continue;
                }
                $surpport = isset($unsur_list[$value.'-'.$vv]) ? '未支持' : '已支持';
                $switch_key = md5($vv.$value);
                $switch_channel = isset($switch_list[$switch_key]['type']) ? $switch_list[$switch_key]['type'] : (isset($unsur_list[$value.'-'.$vv]) ? 'thd' : 'loc');
                if ($param['sel_channel'] != 'all' && $param['sel_channel'] != $switch_channel) {
                    continue;
                }
                if ($param['support'] != 'all' && $param['support'] != $surpport) {
                    continue;
                }
                array_push($redis_keys, $switch_key);
            }
        }
        // gen redis info
        if (is_array($info_redis)) {
            $info_redis = json_encode($info_redis, JSON_UNESCAPED_UNICODE);
        }
        return array_fill_keys($redis_keys, $info_redis);
    }
}
