<?php
/**
 * 风险名单账号管理
 * @Author: lidandan
 * @Date:   2018-06-28 14:20:04
 */
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\RiskListRepository;
use Home\Repositories\RiskListFileRepository;
use Home\Repositories\RiskListFeeRepository;
use Home\Repositories\FinanceAccountRepository;

class RiskListController extends AdminController
{
    protected $riskListRepository;

    protected $riskListFeeRepository;

    public function _initialize()
    {
        parent::_initialize();
        $this->riskListRepository = new RiskListRepository();
        $this->riskListFeeRepository = new RiskListFeeRepository();
    }

    //列表
    public function index()
    {
        $input = I('get.');
        //条件查询
        $where = $this->riskListRepository->getRistListParam();
        // Page Object
        if ($choose_id || $user_id || $apikey) {
            $count = 1;
        } else {
            $count = $this->riskListRepository->getRistListNum($where);
        }
        //分页
        $page = new Page($count, C('LIST_ROWS'));
        //列表数据
        $list = $this->riskListRepository->getUserListByParam($where, $page->firstRow, $page->listRows);

        //所有账号列表
        $user_list = $this->riskListRepository->getUserListAll();
        //客户列表
        $account_list = $this->getAccountListAll();

        $this->assign('input', $input);
        $this->assign('list', $list);
        $this->assign('account_list', $account_list);
        $this->assign('user_list', $user_list);
        $this->assign('page', $page->show());
        $this->assign('contract_status', $this->riskListRepository->getContractStatus());
        $this->display();
    }

    //添加
    public function add()
    {
        if (!IS_POST) {
            $account_list = $this->getAccountListAll();

            $this->assign('account_list', $account_list);
            $this->assign('contract_status', $this->riskListRepository->getContractStatus());
            $this->display();
            exit;
        }
        try {
            $input = I('post.');
            $res = $this->riskListRepository->addUser($input);
            if (!$res) {
                $this->__Return("添加风险名单账号失败");
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }
        $this->__Return('添加风险名单账号成功', '', 'tip_success');
    }

    //编辑
    public function edit()
    {
        $id = I('get.id', '', 'intval');
        if (IS_GET) {
            $account_list = $this->getAccountListAll();

            $info = $this->riskListRepository->getUserInfo($id);

            //查询产品关联的用户信息
            $account_info = D('FinanceAccountProduct')->getAccountIdByProductId($id, 6);
            // print_r($account_info);die;

            $this->assign('info', $info);
            $this->assign('account_list', $account_list);
            $this->assign('account_info', $account_info);
            $this->assign('contract_status', $this->riskListRepository->getContractStatus());
            $this->display('add');
            exit;
        }

        try {
            $input = I('post.');
            $res = $this->riskListRepository->updateUser($input, $id);
            if (!$res) {
                $this->__Return("更新风险名单失败");
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }
        $this->__Return('更新风险名单成功', '', 'tip_success');
    }

    /**
     * 导出数据
     * @return list
     */
    public function download()
    {
        //条件查询
        $where = $this->riskListRepository->getRistListParam();

        $list = $this->riskListRepository->getUserListByParam($where, 0, 0);
        $risk_file = new RiskListFileRepository();
        $risk_file->getUserListFile($list);
        exit;
    }

    /**
     * 计费配置
     * @return
     */
    public function feeConfig()
    {
        $id = I('get.id', 0, 'intval');
        if (IS_GET) {
            $info = $this->riskListFeeRepository->getFeeConfig($id);
            $this->assign('id', $id);
            $this->assign('info', $info);
            $this->display();
            exit;
        }
        try {
            $res = $this->riskListFeeRepository->saveFeeConfig($id);
            if (!$res) {
                $this->__Return("风险名单计费配置失败");
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage(), 'error');
        }
        $this->__Return('风险名单计费配置成功', ['id' => $res], 'success');
    }

    /**
     * 获取客户信息
     * @return array
     */
    protected function getAccountListAll()
    {
        $repository_account = new FinanceAccountRepository();
        $account_list = $repository_account->accountListForGet();
        return $account_list;
    }
}