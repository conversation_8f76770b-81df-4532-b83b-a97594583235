<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BangProductStatFileRepository;
use Home\Repositories\BangProductStatRepository;

class BangProductStatController extends AdminController
{
    /*
     * 生成文件资源
     * */
    protected $repository_file;

    /*
     * 统计资源
     * */
    protected $repository_stat;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_stat = new BangProductStatRepository();
        $this->repository_file = new BangProductStatFileRepository();
    }

    /**
     * 统计数据导出文件
     */
    public function downloadFile()
    {
        try {
            set_time_limit(0);
            ini_set('max_execution_time', 0);
            // 导出文件
            $this->repository_file->downloadFile();

        } catch (\Exception $e) {
            $status = 4178;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 总计详情
     */
    public function detail()
    {
        if (IS_GET) {
            $list_product = $this->repository_stat->getProductList();

            // 初始化参数
            $request_params = $this->repository_stat->initParamsForDetail();
            $init_params = json_encode(compact('list_product',  'request_params'));

            $this->assign(compact('init_params'));
            $this->display();
            exit();
        }

        try {
            // 统计信息
            $list_stat = $this->repository_stat->getDayInfo();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $status = 1748;
            $this->ajaxResponse(compact('msg', 'status'));
        }
    }

    /**
     * 统计排序
     */
    public function sortBy()
    {
        try {
            $list_stat = $this->repository_stat->sortBy();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $status = 1478;
            $msg = $e->getMessage();
            $this->ajaxResponse(compact('status', 'msg'));
        }
    }

    /**
     * 统计列表
     */
    public function index()
    {
        if (IS_GET) {
            // 产品 && 账号列表
            $list_product = $this->repository_stat->getProductList();
            $list_account = $this->repository_stat->getAccountList();

            // 初始化参数
            $request_params = $this->repository_stat->initParams();
            $init_params = json_encode(compact('list_product', 'list_account', 'request_params'));

            $this->assign(compact('init_params'));
            $this->display();
            exit();
        }

        try {
            // 统计信息
            $list_stat = $this->repository_stat->getStatData();
            $status = 0;
            $this->ajaxResponse(compact('status', 'list_stat'));
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $status = 1748;
            $this->ajaxResponse(compact('msg', 'status'));
        }
    }
}
