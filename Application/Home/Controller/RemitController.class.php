<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/2/28 0028
 * Time: 15:40
 */

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\RemitRepository;

//打款单管理
class RemitController extends AdminController
{
    private $repository;
    public function __construct()
    {
        parent::__construct();
        $this->repository = new RemitRepository($this->loginuser);
    }
    public function index()
    {
        if (IS_POST) {
            //查看日志
            $id = I('get.id', '', 'trim');
            $data = $this->repository->remit_log($id);
            $type = ['提交', '修改', '驳回', '确认'];
            $this->assign('type', $type);
            $this->assign('data', $data);
            $this->display('log');
        } else {
            $data = $this->repository->getListData();
            $this->assign($data);
            $this->display();
        }
    }
    public function add()
    {
        if (IS_POST) {
            if (I('post.type') == 'valid_serial') {
                //验证流水号是否存在
                $remit_id = I('post.remit_id', '', 'intval')?:null;
                $res = $this->repository->valid_serial(I('post.serial', '', 'trim'), $remit_id);
                $status = 0;
                $result = $res?1:0;
                $this->ajaxReturn(compact('status', 'result'));
            } else {
                try {
                    $this->repository->run_add();
                    $this->redirect('index');
                } catch (\Exception $exception) {
                    $this->error('增加失败' . $exception->getMessage(), 'index');
                }
            }
        } else {
            try {
                $this->assign($this->repository->add());
                $this->display();
            } catch (\Exception $exception) {
                echo 'sorry, you cannot add data, ' . $exception->getMessage();
            }
        }
    }
    public function edit()
    {
        if (IS_POST) {
            try {
                $this->repository->run_edit();
                $this->redirect('index');
            } catch (\Exception $exception) {
                $this->error('编辑失败' . $exception->getMessage(), U('index'));
            }
        } else {
            try {
                $data = $this->repository->edit();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                echo 'sorry, you cannot edit data ' . $exception->getMessage();
            }
        }
    }
    public function admit()
    {
        //认款
        if (IS_POST) {
            if (I('post.type')=='reject') {
                //驳回
                $res = $this->repository->run_reject();
                if ($res) {
                    $this->redirect('index');
                } else {
                    $this->error('驳回失败', 'index');
                }
            } else {
                try {
                    $this->repository->run_admit();
                    $this->redirect('index');
                } catch (\Exception $exception) {
                    $this->error('认款失败，' . $exception->getMessage(), 'index');
                }
            }
        } else {
            try {
                $data = $this->repository->admit();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                echo 'sorry, you cannot admit data ' . $exception->getMessage();
            }
        }
    }


    //批量导出
    public function file_out()
    {
        $this->repository->file_out();
    }




}