<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BangProductRepository;
use Home\Repositories\BangProductFeeRepository;

class BangProductController extends AdminController
{
    /*
     * Repository
     * */
    private $repository_bang_product;

    public function _initialize()
    {
        parent::_initialize();
        $this->repository_bang_product = new BangProductRepository();
    }

    /**
     * 列表页
     */
    public function index()
    {
        // 符合条件的产品列表
        $list_request_product = $this->repository_bang_product->getProductListForGet();
        $page_show = $list_request_product['page_show'];
        $list_product = $list_request_product['list_product'];

        // 所有产品
        $list_all_product = $this->repository_bang_product->getProductListOfAll();

        // 请求条件
        $request_params = I('get.');

        $this->assign('request_params', $request_params);
        $this->assign('page_show', $page_show);
        $this->assign('list_product', $list_product);
        $this->assign('list_all_product', $list_all_product);
        $this->display();
    }

    /**
     * 新增用户
     */
    public function add()
    {
        if (IS_GET) {
            // 客户列表
            $list_account = $this->repository_bang_product->getAccountList();
            $init_params = json_encode(compact('list_account'));

            // 输出字段列表
            $this->assign(compact('init_params'));
            $this->display();
            exit();
        }

        // POST新增客户
        try {
            // 添加客户
            $this->repository_bang_product->addAccount();

            $response = [
                'success' => true,
                'msg' => '添加成功'
            ];

            $this->ajaxReturn($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'msg' => $e->getMessage()
            ];
            $this->ajaxReturn($response);
        }
    }

    /**
     * 编辑产品
     */
    public function edit()
    {
        if (IS_GET) {
            // 获取要编辑的产品的信息
            $product = $this->repository_bang_product->getEditProductForGet();
            $product = json_encode($product, JSON_UNESCAPED_UNICODE);

            // 要展示的字段
            $list_fields = $this->repository_bang_product->getEditProductField();
            $list_fields = json_encode($list_fields, JSON_UNESCAPED_UNICODE);

            // 客户列表
            $list_account = $this->repository_bang_product->getAccountList();
            $list_account = json_encode($list_account);

            // 客户绑定信息
            $relation_account = $this->repository_bang_product->getRelationShipForGet();
            $relation_account = json_encode($relation_account);

            $this->assign(compact('list_account', 'list_fields', 'product', 'relation_account'));
            $this->display();
            exit();
        }

        try {
            // 编辑产品
            $this->repository_bang_product->editProduct();
            $response = [
                'success' => true,
                'msg' => '更新成功'
            ];
            $this->ajaxReturn($response);

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'msg' => $e->getMessage()
            ];
            $this->ajaxReturn($response);
        }
    }

    //计费配置
    public function feeConfig()
    {
        $id = I('get.id', 0, 'intval');
        $fee_config = new BangProductFeeRepository();
        if (IS_GET) {
            $info = $fee_config->getFeeConfigInfo($id);
            $this->assign('id', $id);
            $this->assign('info', $info);
            $this->display();
            exit;
        }
        try {
            $res = $fee_config->saveFeeConfig($id);
            if (!$res) {
                $this->__Return("邦企查计费配置失败");
            }
        } catch (\Exception $e) {
            $this->__Return($e->getMessage(), 'error');
        }
        $this->__Return('邦企查计费配置成功', ['id' => $res], 'success');
    }
}
