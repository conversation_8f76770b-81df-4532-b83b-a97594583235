<?php

namespace Home\Controller;

use Common\Controller\AdminController;


class TestMgmtController extends AdminController
{
    private $test_mgmt;

    public function __construct()
    {
        parent::__construct();
        $this->test_mgmt = D('TestMgmt');
    }
    /**
     * 用例测试页面
     */
    public function index()
    {
        $data = I('post.');
        $data = array_map('trim', $data);
        $flag = $data['flag'];
        if($flag == 'add'){
            $get_param = html_entity_decode($data['get_param']);
            $post_param = $data['post_param'];
            $post_param = urldecode($post_param);
            $post_param = html_entity_decode($post_param);
            if($data['is_json'] == 1){
                if(!empty($post_param)){
                    if(!is_string($post_param)){
                        $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                        $this->ajaxReturn($result);
                    }
                    if($this->isJson($post_param)){
                        //判断不是数字的化进行解析
                        if(is_numeric($post_param)){
                            $result = ['status' => 'error', 'msg' => 'post参数不能是单独数字'];
                            $this->ajaxReturn($result);
                        }
                        $post_param = json_decode($post_param, true);
                        $post_param = json_encode($post_param, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
                    }else{
                        //不是json
                        $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                        $this->ajaxReturn($result);
                    }
                }
            }
            $info = [
                'name' => $data['name'],
                'url1' => trim($data['url1'], '/'),
                'url2' => trim($data['url2'], '/'),
                'api' => trim($data['api'], '/'),
                'admin' => $this->loginuser['username'],
                'get_param' => $get_param,
                'post_param' => $post_param,
                'created_at' => date('Y-m-d H:i:s'),
                'result1' => '',
                'result2' => '',
                'is_json' => $data['is_json']
            ];

            $res = $this->test_mgmt->add($info);
            if($res){
                $result = ['status' => 'ok', 'msg' => '添加成功'];
                $this->ajaxReturn($result);
            }
            $result = ['status' => 'error', 'msg' => '添加失败'];
            $this->ajaxReturn($result);

        }else if($flag == 'get_edit'){
            $info = $this->test_mgmt->where(['id'=>$data['id']])->find();
            $this->ajaxReturn($info);
        }else if($flag == 'edit'){
            $get_param = html_entity_decode($data['get_param22']);
            $post_param = $data['post_param22'];
            $post_param = urldecode($post_param);
            $post_param = html_entity_decode($post_param);
            if($data['is_json22'] == 1){
                if(!empty($post_param)){
                    if(!is_string($post_param)){
                        $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                        $this->ajaxReturn($result);
                    }
                    if($this->isJson($post_param)){
                        //判断不是数字的化进行解析
                        if(is_numeric($post_param)){
                            $result = ['status' => 'error', 'msg' => 'post参数不能是单独数字'];
                            $this->ajaxReturn($result);
                        }
                        $post_param = json_decode($post_param, true);
                        $post_param = json_encode($post_param, JSON_UNESCAPED_UNICODE);//为了都是压缩数据
                    }else{
                        //不是json
                        $result = ['status' => 'error', 'msg' => 'post参数不是json格式'];
                        $this->ajaxReturn($result);
                    }
                }
            }

            $info = [
                'name' => $data['name22'],
                'url1' => trim($data['url122'], '/'),
                'url2' => trim($data['url222'], '/'),
                'api' => trim($data['api22'], '/'),
                'admin' => $this->loginuser['username'],
                'get_param' => $get_param,
                'post_param' => $post_param,
                'result1' => '',
                'result2' => '',
                'status' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'is_json' => $data['is_json22']
            ];

            $res = $this->test_mgmt->where(['id'=>$data['id22']])->save($info);
            if($res){
                $result = ['status' => 'ok', 'msg' => '编辑成功'];
                $this->ajaxReturn($result);
            }
            $result = ['status' => 'error', 'msg' => '编辑失败'];
            $this->ajaxReturn($result);
        }else if($flag == 'run'){
            $no = $data['no'];
            $info = $this->test_mgmt->where(['id'=>$data['id']])->find();
            if(empty($info)){
                $result = ['status' => 'error1', 'msg' => '数据不存在'];
                $this->ajaxReturn($result);
            }
            if(empty($info['api'])){
                $result = ['status' => 'error1', 'msg' => '接口名称为空'];
                $this->ajaxReturn($result);
            }
            $type = 'get';
            if(!empty($info['post_param'])){
                $type = 'post';
            }

            if($no == 1){
                if(empty($info['url1'])){
                    $result = ['status' => 'error1', 'msg' => '域名1为空'];
                    $this->ajaxReturn($result);
                }
                $url1 = $info['url1'] . '/' . $info['api'];
                if(!empty($info['get_param'])){
                    $url1 = $url1 . '?' . $info['get_param'];
                }
                if($type == 'get'){
                    $res1 = $this->curlHandler($url1);
                }else{
                    if($info['is_json'] == 1){
                        $res1 = $this->curlHandler($url1, $info['post_param'], 'http', 'post', true);
                    }else{
                        $res1 = $this->curlHandler($url1, $info['post_param'], 'http', 'post');
                    }
                }
                if(empty($res1)){
                    $result = ['status' => 'error1', 'msg' => '请求异常'];
                    $this->ajaxReturn($result);
                }

                if(!is_string($res1)){
                    $result = ['status' => 'error1', 'msg' => '结果值不是字符串'];
                    $this->ajaxReturn($result);
                }

                $arr = [
                    'result1' => $res1,
                    'status' => 0
                ];
                $this->test_mgmt->where(['id'=>$info['id']])->save($arr);
                $result = ['status' => 'ok', 'msg' => '接口1执行成功', 'res1'=>$res1];
                $this->ajaxReturn($result);

            }else if($no == 2){
                if(empty($info['url2'])){
                    $result = ['status' => 'error1', 'msg' => '域名2为空'];
                    $this->ajaxReturn($result);
                }
                $url2 = $info['url2'] . '/' . $info['api'];
                if(!empty($info['get_param'])){
                    $url2 = $url2 . '?' . $info['get_param'];
                }
                if($type == 'get'){
                    $res2 = $this->curlHandler($url2);
                }else{
                    if($info['is_json'] == 1){
                        $res2 = $this->curlHandler($url2, $info['post_param'], 'http', 'post', true);
                    }else{
                        $res2 = $this->curlHandler($url2, $info['post_param'], 'http', 'post');
                    }
                }
                if(empty($res2)){
                    $result = ['status' => 'error1', 'msg' => '请求异常'];
                    $this->ajaxReturn($result);
                }
                if(!is_string($res2) || !$this->isJson($res2)){
                    $result = ['status' => 'error1', 'msg' => '结果值不是字符串'];
                    $this->ajaxReturn($result);
                }
                $arr = [
                    'result2' => $res2,
                    'status' => 0
                ];
                $this->test_mgmt->where(['id'=>$info['id']])->save($arr);
                $result = ['status' => 'ok', 'msg' => '接口2执行成功', 'res2'=>$res2];
                $this->ajaxReturn($result);
            }else{
                //进行比较
                if(empty($info['result1']) || empty($info['result2'])){
                    $result = ['status' => 'error1', 'msg' => '接口1或者接口2请求结果有空值'];
                    $this->ajaxReturn($result);
                }

                if(is_numeric($info['result1'])){
                    if($info['result1'] == $info['result2']){
                        $arr = [
                            'status' => 1,
                        ];
                        $this->test_mgmt->where(['id'=>$info['id']])->save($arr);
                        $result = ['status' => 'ok', 'msg' => '测试成功', 'res1'=>$info['result1'], 'res2'=>$info['result2']];
                    }else{
                        $arr = [
                            'status' => 2,
                        ];
                        $this->test_mgmt->where(['id'=>$info['id']])->save($arr);
                        $result = ['status' => 'error', 'msg' => '测试失败', 'res1'=>$info['result1'], 'res2'=>$info['result2']];
                    }
                    $this->ajaxReturn($result);
                }else{
                    if(!$this->isJson($info['result1'])){
                        $result = ['status' => 'error1', 'msg' => '结果值不是json'];
                        $this->ajaxReturn($result);
                    }
                    $res_arr1 = json_decode($info['result1'], true);
                    $res1 = json_encode($res_arr1, JSON_UNESCAPED_UNICODE);
                    $res_arr2 = json_decode($info['result2'], true);
                    $res2 = json_encode($res_arr2, JSON_UNESCAPED_UNICODE);

                    $r = $this->isEqualArr($res1, $res2);
                    if($r){
                        $arr = [
                            'status' => 1
                        ];
                        $this->test_mgmt->where(['id'=>$info['id']])->save($arr);
                        $result = ['status' => 'ok', 'msg' => '测试成功', 'res1'=>$res1, 'res2'=>$res2];

                    }else{
                        $arr = [
                            'status' => 2
                        ];
                        $this->test_mgmt->where(['id'=>$info['id']])->save($arr);
                        $result = ['status' => 'error', 'msg' => '测试失败', 'res1'=>$res1, 'res2'=>$res2];
                    }
                    $this->ajaxReturn($result);
                }

            }
        }else if($flag == 'piliang1'){
            $info = $this->test_mgmt->field('id, url1, url2, api, get_param, post_param, is_json')->select(['url1']);
            if(empty($info)){
                $result = ['status' => 'error', 'msg' => '暂无数据'];
                $this->ajaxReturn($result);
            }
            foreach($info as $key=>$value){
                if(!empty($value['url1']) && !empty($value['api'])){
                    $url1 = $value['url1'] . '/' . $value['api'];
                    if(!empty($value['get_param'])){
                        $url1 = $url1 . '?' . $value['get_param'];
                    }
                    $type = 'get';
                    if(!empty($value['post_param'])){
                        $type = 'post';
                    }
                    if($type == 'get'){
                        $res1 = $this->curlHandler($url1);
                    }else{
                        if($value['is_json'] == 1){
                            $res1 = $this->curlHandler($url1, $value['post_param'], 'http', 'post', true);
                        }else{
                            $res1 = $this->curlHandler($url1, $value['post_param'], 'http', 'post');
                        }
                    }
                    if(!empty($res1) && is_string($res1)){
                        $arr = [
                            'result1' => $res1,
                            'status' => 0
                        ];
                        $this->test_mgmt->where(['id'=>$value['id']])->save($arr);
                    }
                    usleep(1000);
                }
            }
            $result = ['status' => 'ok', 'msg' => '批量1执行成功'];
            $this->ajaxReturn($result);
        }else if($flag == 'piliang2'){
            $info = $this->test_mgmt->field('id, url1, url2, api, get_param, post_param, is_json')->select();

            if(empty($info)){
                $result = ['status' => 'error', 'msg' => '暂无数据'];
                $this->ajaxReturn($result);
            }
            foreach($info as $key=>$value){
                if(!empty($value['url2']) && !empty($value['api'])){
                    $url2 = $value['url2'] . '/' . $value['api'];
                    if(!empty($value['get_param'])){
                        $url2 = $url2 . '?' . $value['get_param'];
                    }
                    $type = 'get';
                    if(!empty($value['post_param'])){
                        $type = 'post';
                    }
                    if($type == 'get'){
                        $res2 = $this->curlHandler($url2);
                    }else{
                        if($value['is_json'] == 1){
                            $res2 = $this->curlHandler($url2, $value['post_param'], 'http', 'post', true);
                        }else{
                            $res2 = $this->curlHandler($url2, $value['post_param'], 'http', 'post');
                        }
                    }
                    if(!empty($res2) && is_string($res2)){
                        $arr = [
                            'result2' => $res2,
                            'status' => 0
                        ];
                        $this->test_mgmt->where(['id'=>$value['id']])->save($arr);
                    }
                    usleep(1000);
                }
            }
            $result = ['status' => 'ok', 'msg' => '批量2执行成功'];
            $this->ajaxReturn($result);

        }else if($flag == 'duibi'){
            $info = $this->test_mgmt->field('id, result1, result2')->select();
            if(empty($info)){
                $result = ['status' => 'error', 'msg' => '暂无数据'];
                $this->ajaxReturn($result);
            }
            $str = '';
            foreach($info as $key=>$value){
                if(is_numeric($value['result1'])){
                    if($value['result1'] == $value['result2']){
                        $arr = [
                            'status' => 1,
                        ];
                        $this->test_mgmt->where(['id'=>$value['id']])->save($arr);
                        $str .= '<tr> <td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">.$value["id"].</td><td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">成功</td><td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'.$value['result1'].'</td> <td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'.$value['result2'].'</td> </tr>';
                    }else{
                        $arr = [
                            'status' => 2,
                        ];
                        $this->test_mgmt->where(['id'=>$value['id']])->save($arr);
                        $str .= '<tr> <td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">'.$value["id"].'</td><td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">失败</td><td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">'.$value['result1'].'</td> <td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">'.$value['result2'].'</td> </tr>';
                    }
                }else{
                    if(!$this->isJson($value['result1'])){
                        //$result = ['status' => 'error1', 'msg' => '结果值不是json'];
                       // $this->ajaxReturn($result);
                        $str .= '<tr> <td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">'.$value["id"].'</td><td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">失败</td><td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">不是json</td> <td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">不是json</td> </tr>';
                    }else{
                        $res_arr1 = json_decode($value['result1'], true);
                        $res1 = json_encode($res_arr1, JSON_UNESCAPED_UNICODE);
                        $res_arr2 = json_decode($value['result2'], true);
                        $res2 = json_encode($res_arr2, JSON_UNESCAPED_UNICODE);

                        $r = $this->isEqualArr($res1, $res2);
                        if($r){
                            $arr = [
                                'status' => 1
                            ];
                            $this->test_mgmt->where(['id'=>$value['id']])->save($arr);
                            $str .= '<tr> <td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'.$value["id"].'</td><td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">成功</td><td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'.$value['result1'].'</td> <td align="center" style="color: green; word-wrap:break-word;word-break:break-all;">'.$value['result2'].'</td> </tr>';

                        }else{
                            $arr = [
                                'status' => 2
                            ];
                            $this->test_mgmt->where(['id'=>$value['id']])->save($arr);
                            $str .= '<tr> <td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">'.$value["id"].'</td><td align="center" style="color: black; word-wrap:break-word;word-break:break-all;">失败</td><td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">'.$value['result1'].'</td> <td align="center" style="color: red; word-wrap:break-word;word-break:break-all;">'.$value['result2'].'</td> </tr>';
                        }
                    }

                }
            }
            $result = ['status' => 'ok', 'msg' => '完成', 'str'=>$str];
            $this->ajaxReturn($result);
        }else{
            $count = $this->test_mgmt->count();
            $Page  = new \Common\ORG\Page($count, 300);
            $data = $this->test_mgmt->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();

            $this->assign(['data'=>$data]);
            $this->assign(['page'=>$Page->show()]);
            $this->display();
        }

    }

    /**
     * 判断是否是json
     */
    protected function isJson($data)
    {
        $res = @json_decode($data, true);
        if (is_null($res)) {
            return false;
        }
        return true;
    }


    /**
     * 远程调用url
     */
    private function curlHandler($request_url, $post_data = array(), $type = 'http', $method = 'get', $json = false, $inner = false)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $request_url);
        curl_setopt($ch, CURLOPT_HEADER, false);
        if($type == 'https')
        {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
            //curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, true);  // 从证书中检查SSL加密算法是否存在
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if($method == 'post'){
            curl_setopt($ch, CURLOPT_POST, 1);
            if($json){
                $headers = ['Content-Type: application/json;charset=utf-8'];
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            }else{
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            }
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        $output = curl_exec($ch);
        if($inner){
            return $output;
        }
        $err = curl_errno($ch);
        $httpCode = curl_getinfo($ch,CURLINFO_HTTP_CODE);
        if($err == 0 && $httpCode == 200){
            return $output;
        }else{
            return false;
        }
    }

    /**
     * 比较数组是否相等
     * 整型:integer
     * 字符串:string
     * 布尔类型:boolean
     */
    private function isEqualArr($arr1 = [],$arr2 = []) {
        if(is_string($arr1)){
            if($arr1 != $arr2){
                return false;
            }else{
                return true;
            }
        }

        if(count($arr1) != count($arr2)){
            return false;
        }
        foreach($arr1 as $k => $v){
            if(is_array($v) && is_array($arr2[$k])){
                return $this->isEqualArr($v, $arr2[$k]);
            }
            if(!isset($arr2[$k])){
                if($arr2[$k] != null){
                    return false;
                }
            }
            //在这个地方进行匹配,@进行类型匹配,#进行范围匹配,没有则进行值匹配
            if(strpos($v,'@') !== false){
                //进行类型匹配,格式:@integer
                $res_type = ltrim($v, '@');
                if($res_type != gettype($arr2[$k])){
                    return false;
                }
            }else if(strpos($v,'#') !== false){
                //进行范围匹配格式:1#100
                $res_arr = explode('#', $v);
                $res_arr[0] = isset($res_arr[0]) ? $res_arr[0] : 0;
                $res_arr[1] = isset($res_arr[1]) ? $res_arr[1] : 0;
                if($arr2[$k] > $res_arr[1] || $arr2[$k] < $res_arr[0]){
                    return false;
                }
            }else{
                if($arr2[$k] != $v){
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 生成签名算法
     */
    private function getSign($apikey = '', $secret = '', $nonce = '', $timestamp = '')
    {
        if(!$apikey || !$secret || !$nonce || !$timestamp){
            return false;
        }
        $tmpArr = array($timestamp, $apikey, $secret, $nonce);
        sort($tmpArr, SORT_STRING);
        $signature = sha1( implode( $tmpArr ) );
        return $signature;
    }

}

