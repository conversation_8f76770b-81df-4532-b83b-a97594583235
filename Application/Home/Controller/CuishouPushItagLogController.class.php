<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;

class CuishouPushItagLogController extends AdminController
{
    public function index()
    {
        $input = array_map(function ($item) {
            return trim($item);
        }, I('get.'));

        $params = array_filter(I('get.'), function ($item) {
            return trim($item) != '';
        });

        // filter sid itag_push_status cid itag_push_number itag_push_delay_seconds
        if (isset($params['sid'])) {
            $where['sid'] = trim($params['sid']);
        }

        if (isset($params['itag_push_status'])) {
            $where['itag_push_status'] = trim($params['itag_push_status']);
        }

        if (isset($params['cid'])) {
            $where['cid'] = new \MongoInt32(trim($params['cid']));
        }

        if (isset($params['itag_push_number'])) {
            $where['itag_push_number'] = new \MongoInt32(trim($params['itag_push_number']));
        }

        if (isset($params['itag_push_delay_seconds'])) {
            $where['itag_push_delay_seconds'] = [
                '$gte' => (float)$params['itag_push_delay_seconds']
            ];
        }

        // filter time
        $where_created_at = isset($params['begin']) ? ['$gte' => strtotime($params['begin'])] : ['$gte' => strtotime(date('Y-m-d'))];
        if (isset($params['end'])) {
            $end = strtotime($params['end']) + 86399;
            $where_created_at = $where_created_at + ['$lte' => $end];
        } else {
            $end = strtotime(date('Y-m-d')) + 86399;
            $where_created_at = $where_created_at + ['$lte' => $end];
        }

        if ($where_created_at) {
            $where['created_at'] = $where_created_at;
        }

        // set page class
        $count = D('CuishouItagPush')->where($where)->count();
        $page = new Page($count, C('LIST_ROWS'));

        // show list
        $fields = [
            'sid' => 1,
            'cid' => 1,
            'itag_pushed_need' => 1,
            'itag_push_status' => 1,
            '_id' => 0,
            'itag_push_number' => 1,
            'itag_push_take_seconds' => 1,
            'itag_push_starttime' => 1,
            'itag_push_client_response_ok' => 1,
            'itag_push_client_response_str' => 1,
            'itag_push_delay_seconds' => 1
        ];

        $list_show = D('CuishouItagPush')->field($fields)->where($where)->limit($page->firstRow, $page->listRows)->order('_id desc')->select();

        // tidy data
        $list_show = array_map(function ($item) {
            $item['itag_pushed_need'] = $item['itag_pushed_need'] ? '是' : '否';
            $item['itag_push_client_response_ok'] = $item['itag_push_client_response_ok'] ? '是' : '否';
            $item['itag_push_number'] = isset($item['itag_push_number']) ? $item['itag_push_number'] : '';
            $item['itag_push_client_response_str'] = isset($item['itag_push_client_response_str']) ? $item['itag_push_client_response_str'] : '';

            if (isset($item['itag_push_starttime'], $item['itag_push_take_seconds'])) {
                $itag_push_end_time = strtotime($item['itag_push_starttime']) + $item['itag_push_take_seconds'];
                $item['itag_push_end_time'] = date('Y-m-d H:i:s', $itag_push_end_time);
            } else {
                $item['itag_push_end_time'] = '';
            }
            $item['itag_push_starttime'] = isset($item['itag_push_starttime']) ? $item['itag_push_starttime'] : '';
            $item['itag_push_take_seconds'] = isset($item['itag_push_take_seconds']) ? round($item['itag_push_take_seconds'], 4) : '';
            $item['itag_push_delay_seconds'] = isset($item['itag_push_delay_seconds']) ? round($item['itag_push_delay_seconds'], 4) : '';

            return $item;
        }, $list_show);

        $this->assign('input', $input);
        $this->assign('list_show', $list_show);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function msg()
    {
        $sid = I('get.sid', '', 'trim');
        !$sid && exit('缺少sid');
        $show_data = D('CuishouItagPush')->where(['sid' => $sid])->select();

        $show_data = array_map(function ($item) {
            return json_encode($item, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }, $show_data);

        $this->assign('sid', $sid);
        $this->assign('msg', $show_data);
        $this->display();
    }
}
