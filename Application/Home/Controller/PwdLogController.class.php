<?php
namespace Home\Controller;

use Common\Controller\AdminController;
use Common\ORG\Page;
use Home\Repositories\PwdLogRepository;

class PwdLogController extends AdminController
{
    protected $areas = ['上海', '云南', '内蒙古', '北京', '吉林', '四川', '天津', '宁夏', '安徽', '山东', '山西', '广东', '广西', '新疆', '江苏', '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '甘肃', '福建', '西藏', '贵州', '辽宁', '重庆', '陕西', '青海', '黑龙江',
    ];

    protected $pwd;

    public function _initialize()
    {
        parent::_initialize();
        $this->pwd = new PwdLogRepository();
    }

    public function index()
    {
        $request_params = I('get.');

        $limit = 20;
        $list = $this->pwd->getPwdLogList($limit);

        $total = isset($list['page_info']['total']) ? $list['page_info']['total'] : 0;

        $page = new Page($total, $limit);

        $list = isset($list['list']) ? $list['list'] : [];

        $this->assign('request_params', $request_params);
        $this->assign('crawler_areas', $this->areas);
        $this->assign('list', $list);
        $this->assign('page', $page->show());

        $this->display();
    }

    public function msg()
    {
        $list = $this->pwd->getPwdLogDetail();

        $this->assign('list', $list);
        $this->display();
    }
}