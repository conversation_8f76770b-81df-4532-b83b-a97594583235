<?php

namespace Home\Controller;

use Common\Controller\AdminController;
use Common\Model\AdminApistatModel;
use Common\Model\AuthModel;
use Common\Model\CrsMongoModel;
use \Common\ORG\Page;

class CrawlerStatController extends AdminController
{
    protected $list_row;
    protected $user_model;
    protected $crsmongo;

    public function _initialize()
    {
        parent::_initialize();
        $this->dealTime();
        $this->dealSearchType();
        $this->list_row = C('LIST_ROWS');
        $this->user_model = new AuthModel();
        $this->crsmongo = new CrsMongoModel();
    }

    public function index()
    {

        $searchType = I('get.search_type', '', 'trim');
        //获取有效用户的信息,需要在展示的页面
        $userInfo = $this->user_model->getUserInfo('all');
        switch ($searchType) {
            case 0: //总览
                $list = $this->getSurvey();
                $list['lChart'] = $this->getLineChart();

                break;
            case 1://邦秒爬
                $list = $this->getSearchInfo();
                break;
            case 2://邦秒配
                $list = $this->getCallLog();
                break;
            case 3:
                //邦秒爬的详情页
                $list = $this->detailOperation();
                break;
            case 4://邦秒配的详情页
                $list = $this->detailPe();
                break;
            default://默认总览

        }
        $list['user_info'] = $userInfo;
        $this->assign('list', $list);
        $this->display();
    }

    /**
     *折线图的
     */
    public function getLineChart()
    {
        $list = $this->getSurvey(1);
        //对数据进行整理  生成需要的格式
        $date = array_keys($list['info']);

        $data = [];
        foreach ($list['info'] as $key => $value) {
            $data['login_status'][] = $value['login_status'];
            $data['status'][] = $value['status'];
            $data['status_report'][] = $value['status_report'];
            $data['total'][] = $value['total'];
        }
        $info['date'] = implode(',', $date);

        $info['info']['login_status'] = implode(',', $data['login_status']);
        $info['info']['status'] = implode(',', $data['status']);
        $info['info']['total'] = implode(',', $data['total']);
        $info['info']['status_report'] = implode(',', $data['status_report']);

        return $info;
    }


    /**
     *获取邦秒配的信息
     *
     */
    protected function getCallLog()
    {
        //获取需要的apikey
        $count = $this->getUserNum();
        $Page = new Page($count, $this->list_row);
        $userInfo = $this->user_model->getUserInfo(null, I('get.cid', '', 'trim'), $Page->firstRow, $Page->listRows);
        $this->assign('page', $Page->show());
        //时间范围的处理
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        if (empty($begin)) {
            $time = getTodayStamp();
            $begin = $time['begin'];
            $end = $time['end'];
        }

        //获取邦秒配的总计的数量
        $id = I('get.cid', '', 'trim');
        $appkeyid = $id ? ($this->getUserByAppkeyId($id)) : '';
        $validAppkeyid = $id ? '' : $this->getValidApikeyId();
        $sum = getNotNull((new AdminApistatModel())->getApiTimes('', $begin, $end,
            true, $appkeyid, $validAppkeyid, $id)['total'], 0);

        //单个用户选定时间内的邦秒配的数据统计
        foreach ($userInfo as $key => $value) {
            $data = (new AdminApistatModel())->getApiTimes($value['apikeyid'], $begin, $end);
            $data['daily_used'] = isset($data['daily_used']) ? $data['daily_used'] : 0;
            $data['developer'] = $value['developer'];
            $data['apikey'] = $value['apikeyid'];
            $data['cid'] = $value['id'];
            $list[] = $data;
        }
        return [
            'header' => ['客户', '查询量'],
            'index' => ['developer', 'daily_used'],
            'info' => $list,
            'total' => ['总计', $sum,],
            'detail' => ['apikey', 4, 'cid']
        ];

    }

    /**
     * 获取邦秒爬的信息
     *
     */
    protected function getSearchInfo()
    {

        $data = $this->dealOperation();
        $list['info'] = $data['list'];
        $list['total'] = [
            '总计', $data['total']['login_status'], $data['total']['status'], $data['total']['status_report']
        ];
        $list['header'] = ['客户名称', '授权成功量', '爬取成功量', '报告生成量'];
        $list['index'] = ['developer', 'login_status', 'status', 'status_report'];
        $list['detail'] = ['cid', 3];

        //数据重新组合
        return $list;
    }

    /**
     * 为邦秒爬服务,获取客户详细的信息
     * @return mixed 返回特定格式的数组
     */
    protected function detailOperation()
    {
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $cid = I('get.cid', '', 'trim');
        //对时间进行处理 因为一天作为展示单位 所以总数量就是天的数量
        if (empty($begin)) {
            $time = getMonStamp();
            $begin = $time['begin'];
            $end = $time['end'];
        }
        $timeArr = sliceTime($begin, $end, 3);
        //展示时间需要倒叙
        $timeArr = array_reverse($timeArr);
        $count = count($timeArr);
        $Page = new Page($count, $this->list_row);
        $timeArr = array_slice($timeArr, $Page->firstRow, $Page->listRows);

        //获取每天的数据
        foreach ($timeArr AS $value) {
            $condition = [];
            //公用条件
            $condition['end_time'] = [
                '$gte' => intval($value['beginTime']),
                '$lte' => intval($value['endTime']),
            ];
            $condition['cid'] = "$cid";
            //获取三种统计的数据
            $list[$value['date']] = $this->subStatPa($condition);
            $list[$value['date']]['date'] = $value['date'];
        }
        $this->assign('page', $Page->show());
        return [
            'header' => ['时间', '授权成功量', '爬虫成功量', '报告生成量'],
            'info' => $list,
            'index' => ['date', 'login_status', 'status', 'status_report'],
        ];
    }

    /**
     * 为邦秒爬服务  获取各个客户在相应的时间段范围内 相应的信息
     * @return mixed 返回特定格式的数组
     */
    protected function dealOperation()
    {
        $count = $this->getUserNum();
        $Page = new Page($count, $this->list_row);
        $this->assign('page', $Page->show());
        $userInfo = $this->user_model->getUserInfo(null, I('get.cid', '', 'trim'), $Page->firstRow, $Page->listRows);
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');

        if (empty($begin)) {
            $begin = getTodayStamp()['begin'];
            $end = time();
        }

        $data = [
            'list' => [],
            'total' => ['status' => 0, 'login_status' => 0, 'status_report' => 0],
        ];
        $condition = [];
        $condition['end_time'] = [
            '$gte' => intval($begin),
            '$lte' => intval($end)

        ];
        //总计条件  因为sid_info中有一些status=0的用户的数据
        if ($count == 1) {
            $condition['cid'] = I('get.cid', '', 'trim');
        }else{
             $validIds = $this->getValidUserId();
             $condition['cid'] = [
                 '$in'=>explode(',',$validIds)
             ];
        }
        //获取总计的信息
        $data['total'] = $this->subStatPa($condition);
        //获取单个用户的信息
        foreach ($userInfo AS $value) {
            $condition['cid'] = "{$value['id']}";
            //获取当前条件下的三种情况的统计
            $list = $this->subStatPa($condition);
            $list['developer'] = $value['developer'];
            $list['cid'] = $value['id'];
            $data['list'][] = $list;
        }

        return $data;
    }


    /**
     *邦秒配详情页
     *
     */
    protected function detailPe()
    {
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $apikey = I('get.apikey', 0, 'trim');
        //进入详情页 再次刷新之后apikeyid会消失，那么需要根据cid算出 apikeyid
        if (!$apikey) {
            $apikey = $this->getUserByAppkeyId(I('get.cid', '', 'trim'));

        }

        if (empty($begin)) {
            $time = getMonStamp();
            $begin = $time['begin'];
            $end = $time['end'];
        }
        $timeArr = array_reverse(sliceTime($begin, $end, 3));
        $count = count($timeArr);
        $Page = new Page($count, $this->list_row);
        $timeArr = array_slice($timeArr, $Page->firstRow, $Page->listRows);
        $this->assign('page', $Page->show());

        foreach ($timeArr AS $value) {
            $list[$value['date']] = (new AdminApistatModel())->getApiTimes($apikey, $value['beginTime'], $value['endTime']);
            $list[$value['date']]['date'] = $value['date'];
            $list[$value['date']]['daily_used'] = isset($list[$value['date']]['daily_used']) ? $list[$value['date']]['daily_used'] : 0;
        }
        return [
            'header' => ['时间', '查询量'],
            'info' => $list,
            'index' => [
                'date', 'daily_used'
            ]
        ];
    }

    /**
     * 总览
     * @param null $LineChart 补丁(这个函数是折线图的数据源 但是列表和折线图在默认时候展现的形式有是不一样的 so $LineChart=1的时候进行特殊处理 )
     * @return array
     */
    public function getSurvey($LineChart = null)
    {
        $begin = I('get.begin', '', 'trim');
        $end = I('get.end', '', 'trim');
        $id = I('get.cid', '', 'trim');
        //获取当前查询的条件 如果限定了特定的用户的话,则获取到相应的apikeyid; 否则的则会用到不可以使用的用户的apikeyid字符串的集合，全部获取供model判断
        $appkeyid = $id ? ($this->getUserByAppkeyId($id)) : '';
        $validAppkeyid = $id ? '' : $this->getValidApikeyId();
        //默认的展示  今天昨天一周
        if (empty($begin)) {
            $time = getWeekStamp();
            $beginW = $time['begin'];
            $endW = $time['end'];
            /*提供折线图格式的数据*/
            if ($LineChart === 1) {
                $timeArr = sliceTime($beginW, $endW, 3);
                $list = $this->getSurveyCus($timeArr, $appkeyid, $validAppkeyid, $id);

            } else {
                $time = getYesStamp();
                $beginY = $time['begin'];
                $endY = $time['end'];
                $time = getTodayStamp();
                $beginT = $time['begin'];
                $endT = $time['end'];
                //获取今天 昨天 近一周的邦秒爬统计信息
                $list['aT'] = $this->getBmpaInfo($beginT, $endT);
                $list['bY'] = $this->getBmpaInfo($beginY, $endY);
                $list['cW'] = $this->getBmpaInfo($beginW, $endW);
                //获取今天 昨天 近一周的邦秒配统计信息
                $list['aT']['total'] = getNotNull((new AdminApistatModel())->getApiTimes('', $beginT, $endT,
                    true, $appkeyid, $validAppkeyid, $id)['total'], 0);
                $list['bY']['total'] = getNotNull((new AdminApistatModel())->getApiTimes('', $beginY, $endY,
                    true, $appkeyid, $validAppkeyid, $id)['total'], 0);
                $list['cW']['total'] = getNotNull((new AdminApistatModel())->getApiTimes('', $beginW, $endW,
                    true, $appkeyid, $validAppkeyid, $id)['total'], 0);
                $list['aT']['date'] = '今天';
                $list['bY']['date'] = '昨天';
                $list['cW']['date'] = '近一周';

            }

            $index = [
                'header' => ['时间', '邦秒爬', '邦秒配'],
                'index' => ['date', 'status', 'total']
            ];

        } else {
            // 限定了特定的时间范围,倒叙排列
            $timeArr = array_reverse(sliceTime($begin, $end, 3));
            $count = count($timeArr);
            $Page = new Page($count, $this->list_row);
            $timeArr = array_slice($timeArr, $Page->firstRow, $Page->listRows);
            $list = $this->getSurveyCus($timeArr, $appkeyid, $validAppkeyid, $id);
            if ($LineChart) {
                //折线图的时间要正序排列
                $list = array_reverse($list);
            }
            $this->assign('page', $Page->show());
            $index = [
                'header' => ['时间', '邦秒爬', '邦秒配'],
                'index' => ['date', 'status', 'total']
            ];
        }

        return array_merge(['info' => $list], $index);
    }


    /**
     * 获取邦秒爬的统计信息 只是加上了时间的筛选条件
     * @param $begin  开始时间
     * @param $end    结束时间
     * @return array
     */
    protected function getBmpaInfo($begin, $end)
    {
        //公用条件
        $condition = [];
        $condition['end_time'] = [
            '$gte' => intval($begin),
            '$lte' => intval($end)
        ];
        //排除不可用的邦秒爬的统计信息
        $cid = I('get.cid', '', 'trim');
        if ($cid) {
            $condition = array_merge($condition, ['cid' => "$cid"]);
        } else {
            //不使用$nin 是因为sid_info有些数据居然没有cid
            $validId = $this->getValidUserId();
            $condition = array_merge($condition, [
                'cid' => [
                    '$in' => explode(',', $validId)
                ]
            ]);

        }
        $list = $this->subStatPa($condition);

        return $list;
    }

    /**
     * 总览 自定义时间的分支
     * @param $timeArr    自定义时间  经过函数处理过的状态
     * @param $appkeyid   apikeyid 或者是  '' false
     * @param $validAppkeyid 在检测全部用户起限定作用(可以正常使用的apikeyid)
     * @param $id  当前用户的id
     * @return mixed
     */
    protected function getSurveyCus($timeArr, $appkeyid, $validAppkeyid, $id)
    {
        $list = [];
        foreach ($timeArr as $value) {
            $info = (new AdminApistatModel())->getApiTimes('', $value['beginTime'], $value['endTime'],
                true, $appkeyid, $validAppkeyid, $id);
            $info['total'] = $info['total'] ? $info['total'] : 0 ;
            $list[$value['date']] = array_merge($this->getBmpaInfo($value['beginTime'], $value['endTime']),
                $info);
            $list[$value['date']]['date'] = $value['date'];
        }
        return $list;
    }

    /**
     * 对页面传递过来的事件参数进行修正,  @a 如果没有开始时间是没有意义的(多个客户的开始统计的时间是不同的)
     *
     */
    protected function dealTime()
    {
        //如果是默认的时间那么不进行处理
        $begin = !empty($_GET['begin']) ? $_GET['begin'] : '';
        $end = !empty($_GET['end']) ? $_GET['end'] : '';
        if (!empty($begin)) {
            //如果时间已经处理过了 那么是不需要处理的
            if (strpos($begin, '-') === FALSE) {
                return;
            }

            $_GET['begin'] = strtotime(trim($begin));
            //如果没有结束时间 默认是现在（不可以替换成time()，因为还是要参与下面的运算）
            if (empty($end)) {
                $_GET['end'] = getTodayStamp()['end'];
            } else {
                $_GET['end'] = strtotime(trim($end)) + 86399;
            }
            //如果是同一天的话  那么 end 加上一天的时间
            if ($_GET['begin'] == $_GET['end']) {
                $_GET['end'] += 86399;
            }
        }
    }


    /**
     * 对url里面传递过来的search_type进行处理,兼容详情页的时间参数(内部逻辑 和页面展示有关)
     *
     */
    protected function dealSearchType()
    {
        $searchType = (int)I('get.search_type', '', trim);
        $searchFirst = (int)I('get.search_first', '', trim);

        if (!empty($searchType) && !empty($searchFirst) && $searchType != $searchFirst) {
            if (in_array($searchFirst, [3, 4])) {
                if ($searchFirst == 4 and $searchType == 2) {
                    $_GET['search_type'] = $searchFirst;

                }
                if ($searchFirst == 3 and $searchType == 1) {
                    $_GET['search_type'] = $searchFirst;

                }
            }

        }
    }


    /**
     * 获取所有的用户的数量
     * @return mixed
     */
    protected function getUserNum()
    {
        $id = I('get.cid', '', 'trim');
        if ($id) {
            return 1;
        }
        $where = " (status=1 || apikeyid is not null) ";
        $count = $this->user_model->where($where)->count('id');
        return $count;
    }


    /**
     * 根据 appkeyid获取用户对应的id　(是邦秒配的补丁方法)
     * @param $id 用户id
     * @return  int  返回用户id
     */
    protected function getUserByAppkeyId($id)
    {
        if (!$id) {
            return false;
        }
        $info = $this->user_model->field('apikeyid')->where(['id' => $id])->find();

        return $info['apikeyid'] ? $info['apikeyid'] : false;
    }


    /**
     * 可以使用apikeyid (之所以不获取非常场合的apikeyid的原因是  eg: apikeid=298在crs_auth没有匹配,但是邦秒配中有数据  )
     * @return apikeyid 字符串  or  ''
     *
     */
    protected function getValidApikeyId()
    {
        $where = " (status=1 || apikeyid is not null) ";
        $info = $this->user_model->field('group_concat(apikeyid) as apikeyid')->where($where)->find();

        return $info ? $info['apikeyid'] : '';
    }


    /**
     * 获取不可以用的用户的id
     * @return string
     */
    protected function getValidUserId()
    {
        $where = " status=1 ";
        $info = $this->user_model->field('group_concat(id) as id')->where($where)->find();

        return $info ? $info['id'] : '';
    }

    /**
     * 提取邦秒爬的统计的公用部分的代码（有必要 eg:邦秒爬的）
     * @param array $condition eg: ['cid'=>'1','end_time'=>['$gt'=>124231412,...]]
     * @return array
     */
    protected function subStatPa($condition)
    {
        $list = [];
        $condition2 = array_merge($condition, ['status' => 0]);
        $condition3 = array_merge($condition, ['login_status' => '0']);
        $condition4 = array_merge($condition, ['status_report' => 0]);
        
        $list['status'] = $this->crsmongo->queryMongo('sid_info', '_id', $condition2,
            '', '', 'count');

        $list['login_status'] = $this->crsmongo->queryMongo('sid_info', '_id', $condition3,
            '', '', 'count');

        $list['status_report'] = $this->crsmongo->queryMongo('sid_info', '_id', $condition4,
            '', '', 'count');
        return $list;
    }

}
