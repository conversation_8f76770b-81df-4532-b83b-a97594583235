<?php
/**
 * @Author: lidandan
 * @Date:   2018-07-30 14:00:35
 */
namespace Home\Controller;

use Common\Controller\AdminController;
use Home\Repositories\BangProductFeeStatRepository;
use Common\ORG\Page;

class FeeBangProductStatController extends AdminController
{
    protected $bangProductFeeStat;

    public function _initialize()
    {
        parent::_initialize();
        $this->bangProductFeeStat = new BangProductFeeStatRepository();
    }

    public function feeDay()
    {
        $input = I('get.');

        $list = $this->bangProductFeeStat->getBangProductFeeStatDay();
        $total_data = $list['total_data'];
        $count = count($list['list']);

        $page = new Page($count, C('LIST_ROWS'));
        $list = array_slice($list['list'], $page->firstRow, $page->listRows);
        $this->assign('input', $input);
        $this->assign('list', $list);
        $this->assign('total_data', $total_data);
        $this->assign('page', $page->show());
        $this->display();
    }

    public function download()
    {
        $list = $this->bangProductFeeStat->getBangProductFeeStatDay();
        $this->bangProductFeeStat->getBangProductFeeStatDownload($list);
        exit;
    }
}