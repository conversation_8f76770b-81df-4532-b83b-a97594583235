<?php

namespace Home\Controller;

use Common\Controller\AdminController;


class AntConfigController extends AdminController
{
    private $repository;


    public function __construct()
    {
        parent::__construct();
        $this->repository = D('ConfigAntFinancialBatch');
    }

    public function index()
    {

        $data = I('get.');
        $input = $where = [];
        $input['name_search'] = $data['name_search'];
        if(!empty($input['name_search'])){
            $where['name'] = $input['name_search'];
        }
        $count = $this->repository->where($where)->count();
        $Page  = new \Common\ORG\Page($count, 100);
        $config_list = $this->repository->where($where)->limit($Page->firstRow . ',' . $Page->listRows)->order('id DESC')->select();
        $this->assign(['input'=>$input]);
        $this->assign(['data'=>$config_list]);
        $this->display();
    }

    public function add()
    {
        $data = I('post.');
        $name = trim($data['name']);
        $arr = [
            'name' => $name,
            'start_time' => strtotime($data['start_time']),
            'end_time' => strtotime($data['end_time']),
            'number' => $data['number']
        ];
        $find_one = $this->repository->where(['name'=>$name])->find();
        if(!empty($find_one)){
            $result = ['status' => 'error', 'msg' => '该配置已存在'];
            $this->ajaxReturn($result);
        }
        $res = $this->repository->add($arr);
        if($res){
            $result = ['status' => 'ok', 'msg' => '添加成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '添加失败'];
        $this->ajaxReturn($result);
    }

    public function getEdit()
    {
        $data = I('post.');
        $id = $data['id2'];
        $find_one = $this->repository->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }
        $find_one['start_time'] = date('Y-m-d H:i:s', $find_one['start_time']);
        $find_one['end_time'] = date('Y-m-d H:i:s', $find_one['end_time']);
        $result = ['status' => 'ok', 'msg' => '成功', 'data'=>$find_one];
        $this->ajaxReturn($result);
    }

    public function edit()
    {
        $data = I('post.');
        $id = $data['id2'];
        $name = trim($data['name2']);
        if(empty($id)){
            $result = ['status' => 'error', 'msg' => 'id不能为空'];
            $this->ajaxReturn($result);
        }

        $find_one = $this->repository->where(['id'=>$id])->find();
        if(empty($find_one)){
            $result = ['status' => 'error', 'msg' => '数据不存在'];
            $this->ajaxReturn($result);
        }

        $arr = [
            'name' => $name,
            'start_time' => strtotime($data['start_time2']),
            'end_time' => strtotime($data['end_time2']),
            'number' => $data['number2']
        ];

        //判断记录是否有重复
        $find_where = [];
        $find_where['name'] = $name;
        $find_where['id'][] = array('NEQ', $id);
        $find_one2 = $this->repository->where($find_where)->find();
        if(!empty($find_one2)){
            $result = ['status' => 'error', 'msg' => '该条记录已存在'];
            $this->ajaxReturn($result);
        }

        $res = $this->repository->where(['id'=>$id])->save($arr);
        if($res){
            $result = ['status' => 'ok', 'msg' => '编辑成功'];
            $this->ajaxReturn($result);
        }
        $result = ['status' => 'error', 'msg' => '编辑失败'];
        $this->ajaxReturn($result);
    }
}
