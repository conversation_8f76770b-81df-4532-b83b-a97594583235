<?php

namespace ReportTask\Controller;

use Common\Common\CurlTrait;
use Common\Common\HandlerLog;

class ModianController
{
    use CurlTrait;
    protected $channel = [
        '72e89e761906a613332442a36578d893'    => 200,
        'f5a80fd13c569f8a871ba4b256d3bb9a'    => 201
    ];
    protected $time;      //要查询的时间
    public function __construct()
    {
        $time = I('get.time', '', 'trim');
        if (empty($time)) {
            $this->time = strtotime('-1 days');
        } else {
            $maxTime = strtotime('-1 days');
            $this->time = min($maxTime, strtotime($time));
        }
    }
    //陌电数据统计接口
    public function index()
    {
        if (APP_STATUS=='prod') {
            $code = I('get.authorization_code');
            if ($code!='rlF4dXzY6PqKxKudWhU5itoUykuNUVraq5KsspKF') {
                $this->jsonResponse('authorization_code validate falied', []);
            }
        }
        $this->sendData($this->getData());
        $this->jsonResponse('success', [], 0);
    }
    /**
     * 推送统计数据
     * <AUTHOR>
     * @datetime 13:16 2019/1/29
     *
     * @access protected
     * @param $data array 推送的数据
     *
     * @return array
     **/
    protected function sendData($data)
    {
        $url = C('LIST_API_URL')['stat_bm_pei_modian'];
        $json = json_encode($data, JSON_UNESCAPED_UNICODE);
        $res = $this->post($url, $json);
        if ($res['status']!=0) {
            $error = [
                'result'    => $res,
                'url'       => $url,
                'param'     => $data
            ];
            HandlerLog::log([
                'handle_type' => 'api',
                'description' => '发送陌电数据失败',
                'content' => json_encode($error),
                'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: "system",
                'handle_result' => 1
            ]);
            $this->jsonResponse('send data failed', $error);
        }
        return $res;
    }
    /**
     * 获取陌电的数据
     * <AUTHOR>
     * @datetime 11:27 2019/1/29
     *
     * @access protected

     *
     * @return array
     **/
    protected function getData()
    {
        $data = $this->channel;
        array_walk($data, function (&$channel) {
            $res = $this->getSingleDataForCurl($channel);
            $channel = [
                'itag'          => $res['data'][0]['pv']?:0,
                'batch'         => 0,
                'batchResult'   => 0
            ];
        });
        return [
            'key'           => 'fb53fddb7157dd76fd1bb656df4980a3',
            'product_id'    => 601,
            'amount_date'   => date('Y-m-d', $this->time),
            'node_area'     => 'shenzhen',
            'data'          => $data
        ];
    }
    /**
     * 通过接口获取单条数据
     * <AUTHOR>
     * @datetime 13:34 2019/1/29
     *
     * @access protected
     * @param $channel string 渠道 201/202
     *
     * @return array
     **/
    protected function getSingleDataForCurl($channel)
    {
        $url = C('LIST_API_URL')['matching_modian_stat'];
        $param = [
            'start'     => date('Ymd', $this->time),
            'channel'   => $channel
        ];
        if (strstr($url, '?')===false) {
            $url .= '?' . http_build_query($param);
        } else {
            $url .= '&' . http_build_query($param);
        }
        $res = $this->get($url);
        if ($res['status']!=0) {
            $error = [
                'result'    => $res,
                'url'       => $url,
                'param'     => $param
            ];
            HandlerLog::log([
                'handle_type' => 'api',
                'description' => '获取陌电统计数据(' . $channel . ')',
                'content' => json_encode($error),
                'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: "system",
                'handle_result' => 1
            ]);
            $this->jsonResponse('get data failed, channel:' . $channel, $error);
        }
        return $res;
    }
    /**
     * api失败返回
     * <AUTHOR>
     * @datetime 13:39 2019/1/29
     *
     * @access protected
     * @param $res array 返回数组
     *
     * @return void
     **/
    protected function jsonResponse($msg, $res, $code = 1)
    {
        header('Content-Type:application/json; charset=utf-8');
        exit(json_encode([
            'status'    => $code,
            'msg'       => $msg,
            'data'      => $res
        ]));
    }

}