<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/8/12 0012
 * Time: 15:15
 */

namespace ReportTask\Controller;


use Common\Controller\AdminController;

//class MobileStatController  extends AdminController
class MobileStatController  extends \Common\Controller\BaseController
{
    public function index(){
        $date = I('get.date');
        $formt_date = substr($date,4, 4);
        $formt_year = substr($date,0, 4);
        $this->assign('formt_date', $formt_date);
        $this->assign('formt_year', $formt_year);
        $this->assign('date', $date);
        $this->display();
    }

    public function authLogin(){
        $code = I('get.code', '');
        $from_page = I('get.from_page', '');

        if(empty($code)){
            #todo 可以重定向一个错误页面
        }

        $this->assign('auth_code', $code);
        $this->assign('from_page', $from_page);
        $this->display();
    }

    public function noPermission(){
        $error_type = I('get.error_type', '1');
        $get_error_msg = I('get.error_msg', '');

        if($error_type == 1){
            $error_msg = '无权限';
        }else if($error_type == 2){
            $error_msg = '获取用户信息出现异常';
        }else{
            $error_msg = '获取用户访问令牌出现异常';
        }

        if(!empty($get_error_msg)){
            $error_msg = $get_error_msg;
        }

        $this->assign('error_msg', $error_msg);
        $this->display();
    }


}