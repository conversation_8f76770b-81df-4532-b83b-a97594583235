<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<!--    <script type="application/javascript" src="__JS__vue/vue.js"></script>-->
<!--    <script type="application/javascript" src="__JS__vue/index.js"></script>-->
    <script type="application/javascript" src="__JS__vue/axios.min.js"></script>
<!--    <script type="application/javascript" src="__JS__jquery.min.js"></script>-->
</head>

<body>

<div id="app">
<!--    <h1>飞书授权登录{$auth_code}</h1>-->
</div>
</body>
<script>
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days*24*60*60*1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "")  + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++) {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }
</script>
<script>

    var url = "{$Think.config.FINANCE_STAT_API_DOMAIN}/mobile/getFeiShuUserToken";

    var auth_code = "{$auth_code}";
    var from_page = "{$from_page}";
    let where = {auth_code:auth_code};

    axios.post(url, where).then(function (response) {
        // console.log('from_page', from_page);return;
        if(response.data.status != 0){
            window.location.href = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/ReportTask/MobileStat/noPermission.html?error_type=3";
            return;
        }
        let access_token = response.data.data.access_token;
        if(access_token){
            getUserInfo(access_token);
        }
    }).catch(function (error) {
        console.log(error);
    });

    function getUserInfo(access_token){
        let user_info_url = "{$Think.config.FINANCE_STAT_API_DOMAIN}/mobile/getFeiShuUserInfo";

        let where = {access_token:access_token};

        let user_token_name = 'user_mobile_token';

        axios.post(user_info_url, where).then(function (response) {
            // console.log('from_page', from_page);return;
            if(response.data.status == 0){
                setCookie(user_token_name, response.data.data.user_token, 7);
                if(from_page == 'mobile'){
                    let date = getCookie('flag_day');
                    window.location.href = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/ReportTask/MobileStat/index.html?date="+date;
                }else{
                    window.location.href = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/Stat/Dashboard/Index.html";
                }
            }else if(response.data.status == 403){
                window.location.href = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/ReportTask/MobileStat/noPermission.html?error_type=1";
            }else{
                window.location.href = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/ReportTask/MobileStat/noPermission.html?error_type=2";
            }

        }).catch(function (error) {
            console.log(error);
        });
    }

</script>


</html>
