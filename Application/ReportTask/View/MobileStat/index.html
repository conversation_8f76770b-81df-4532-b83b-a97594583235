<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <link rel="stylesheet" type="text/css" href="/statics/js/vue/index.css"/>
    <script type="application/javascript" src="__JS__vue/vue.js"></script>
    <script type="application/javascript" src="__JS__vue/index.js"></script>
    <script type="application/javascript" src="__JS__vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__jquery.min.js"></script>

</head>

<script>
    // 给固定头设置样式
    function doFix(dom, top) {
        dom.style.position = 'fixed'
        dom.style.zIndex = '2001'
        dom.style.top = top + 'px'
        dom.parentNode.style.paddingTop = top + 'px'
    }
    // 给固定头取消样式
    function removeFix(dom) {
        dom.parentNode.style.paddingTop = 0
        dom.style.position = 'static'
        dom.style.top = '0'
        dom.style.zIndex = '0'
    }
    // 给固定头添加class
    function addClass(dom, fixtop) {
        const old = dom.className
        if (!old.includes('fixed')) {
            dom.setAttribute('class', old + ' fixed')
            doFix(dom, fixtop)
        }
    }
    // 给固定头移除class
    function removeClass(dom) {
        const old = dom.className
        const idx = old.indexOf('fixed')
        if (idx !== -1) {
            const newClass = old.substr(0, idx - 1)
            dom.setAttribute('class', newClass)
            removeFix(dom)
        }
    }
    // 具体判断是否固定头的主函数
    function fixHead(parent, el, top) {
        /**
         * myTop 当前元素距离滚动父容器的高度，
         * fixtop 当前元素需要设置的绝对定位的高度
         * parentHeight 滚动父容器的高度
         */
        let myTop, fixtop, parentHeight
        // 表头DOM节点
        const dom = el.children[1]

        if (parent.tagName) {
            // 如果是DOM内局部滚动
            // 当前元素距离滚动父容器的高度= 当前元素距离父元素的高度-父容器的滚动距离-表头的高度
            myTop = el.offsetTop - parent.scrollTop - dom.offsetHeight
            // 父元素高度
            const height = getComputedStyle(parent).height
            parentHeight = Number(height.slice(0, height.length - 2))
            // 绝对定位高度 = 滚动父容器相对于视口的高度 + 传入的吸顶高度
            fixtop = top + parent.getBoundingClientRect().top
            // 如果自己距离顶部距离大于父元素的高度，也就是自己还没在父元素滚动出来，直接return
            if (myTop > parentHeight) {
                return
            }
        } else {
            // document节点滚动
            // 当前元素距离滚动父容器的高度 = 当前元素距离视口顶端的距离
            myTop = el.getBoundingClientRect().top
            // 父元素高度 = 视口的高度
            parentHeight = window.innerHeight
            //  绝对定位高度 = 传入的吸顶高度
            fixtop = top
            // 如果自己距离顶部距离大于父元素的高度，也就是自己还没在父元素滚动出来，直接return
            if (myTop > document.documentElement.scrollTop + parentHeight) {
                return
            }
        }
        // 如果 已经滚动的上去不在父容器显示了。直接return
        if (Math.abs(myTop) > el.offsetHeight + 100) {
            return
        }
        if (myTop < 0 && Math.abs(myTop) > el.offsetHeight) {
            // 如果当前表格已经完全滚动到父元素上面，也就是不在父元素显示了。则需要去除fixed定位
            removeClass(dom)
        } else if (myTop <= 0) {
            // 如果表头滚动到 父容器顶部了。fixed定位
            addClass(dom, fixtop)
        } else if (myTop > 0) {
            // 如果表格向上滚动 又滚动到父容器里。取消fixed定位
            removeClass(dom);
        } else if (Math.abs(myTop) < el.offsetHeight) {
            // 如果滚动的距离的绝对值小于自身的高度，也就是说表格向上滚动，刚刚显示出表格的尾部是需要将表头fixed定位
            addClass(dom, fixtop)
        }
    }
    // 设置头部固定时表头外容器的宽度写死为表格body的宽度
    function setHeadWidth(el) {
        // 获取到当前表格个表格body的宽度
        const width = getComputedStyle(
            el.getElementsByClassName('el-table__body-wrapper')[0]
        ).width
        // 给表格设置宽度。这里默认一个页面中的多个表格宽度是一样的。所以直接遍历赋值，也可以根据自己需求，单独设置
        const tableParent = el.getElementsByClassName('el-table__header-wrapper')
        for (let i = 0; i < tableParent.length; i++) {
            tableParent[i].style.width = width
        }
    }


    /**
     * 这里有三个全局对象。用于存放监听事件。方便组件销毁后移除监听事件
     */
    const fixFunObj = {}      // 用于存放滚动容器的监听scroll事件
    const setWidthFunObj = {}   // 用于存放页面resize后重新计算head宽度事件
    const autoMoveFunObj ={}    // 用户存放如果是DOM元素内局部滚动时，document滚动时，fix布局的表头也需要跟着document一起向上滚动

    // 全局注册 自定义事件
    Vue.directive('sticky', {
        // 当被绑定的元素插入到 DOM 中时……
        inserted(el, binding, vnode) {
            // 首先设置表头宽度
            setHeadWidth(el)
            // 获取当前vueComponent的ID。作为存放各种监听事件的key
            const uid = vnode.componentInstance._uid
            // 当window resize时 重新计算设置表头宽度，并将监听函数存入 监听函数对象中，方便移除监听事件
            window.addEventListener(
                'resize',
                (setWidthFunObj[uid] = () => {
                    setHeadWidth(el)
                })
            )
            // 获取当前滚动的容器是什么。如果是document滚动。则可默认不传入parent参数
            const scrollParent =
                document.querySelector(binding.value.parent) || document
            // 给滚动容器加scroll监听事件。并将监听函数存入 监听函数对象中，方便移除监听事件
            scrollParent.addEventListener(
                'scroll',
                (fixFunObj[uid] = () => {
                    //console.log('dddd', uid, scrollParent, el, binding.value.top);
                    fixHead(scrollParent, el, binding.value.top)
                })
            )
            // 如果是局部DOM元素内滚动。则需要监听document滚动，document滚动是同步让表头一起滚动。并将监听函数存入 监听函数对象中，方便移除监听事件
            if (binding.value.parent) {
                document.addEventListener('scroll', autoMoveFunObj[uid] = ()=> {
                    // 获取到表头DOM节点
                    const dom = el.children[1]
                    // 如果当前表头是fixed定位。则跟着document滚动一起滚
                    if(getComputedStyle(dom).position=== 'fixed'){
                        // 滚动的距离是： 滚动父容器距离视口顶端高度 + 传入的吸顶固定距离
                        const fixtop =
                            binding.value.top + scrollParent.getBoundingClientRect().top
                        doFix(dom, fixtop, 'fixed')
                    }
                })
            }
        },
        // component 更新后。重新计算表头宽度
        componentUpdated(el) {
            setHeadWidth(el)
        },
        // 节点取消绑定时 移除各项监听事件。
        unbind(el, binding, vnode) {
            const uid = vnode.componentInstance._uid
            window.removeEventListener('resize', setWidthFunObj[uid])
            const scrollParent =
                document.querySelector(binding.value.parent) || document
            scrollParent.removeEventListener('scroll', fixFunObj[uid])
            if (binding.value.parent) {
                document.removeEventListener('scroll', autoMoveFunObj[uid])
            }
        }
    })
</script>

<body style="margin: 0px">

<div id="app">
    <template>

            <div class="header-card" style="background-color: #5F8DF1;height: 198px;position: relative;">
                <div style="height:249px;margin: 8px;position: absolute;width:-webkit-fill-available;top:18px">
                <!-- 头部区域 -->
                <div class="header" style="margin-top:-36px;margin-bottom:-10px">
                    <h3>{$formt_date}日报统计</h3>
                    <!--                    0715日报统计-->
                </div>

                <!-- 经营分析数据区域 -->
                <div class="analysis-section">
                    <!--                    <h2>经营分析数据汇总</h2>-->
                    <!-- 这里可以使用卡片视图或自定义组件展示数据 -->
                    <el-card class="mb-20">

                        <div>
                            <!-- 放置你的经营分析数据，例如数字、图表等 -->

                            <el-row style="margin-bottom: 17px;margin-top:-6px" :gutter="10">
                                <el-col :span="12">
<!--                                    <div class="clearfix">-->
<!--                                        <span>销售收入</span>-->
<!--                                    </div>-->
                                    <div class="card-content">
                                        <div class="indicator">
                                            <span>当日收入</span>
                                            <!--                                            <span>环比上月 <i class="el-icon-bottom"></i> 5%</span>-->
                                        </div>
                                        <div class="number">{{cardData.income}}</div>
                                        <div class="indicator" style="min-width: 125px;">
                                            <span>
                                                <i style="color:indianred;font-weight: bold" class="el-icon-bottom" v-if="cardData.currentDateChange.income_diff_type=='down'"></i>
                                                <i style="color: #00cc00;font-weight: bold" class="el-icon-top" v-if="cardData.currentDateChange.income_diff_type=='up'"></i>
                                                <span>{{cardData.currentDateChange.income_diff}}&nbsp;</span>{{cardData.currentDateChange.income_ratio}}%
                                            </span>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="card-content">
                                        <div class="indicator">
                                            <!--                                            <span>同比去年 <i class="el-icon-top"></i> 15%</span>-->
                                            <span>当日毛利</span>
                                        </div>
                                        <div class="number">{{cardData.profit}}</div>
                                        <div class="indicator" style="min-width: 125px;">
<!--                                            <span>同比去年 <i class="el-icon-top"></i> 15%</span>-->
                                            <span>
                                                <i style="color:indianred;font-weight: bold" class="el-icon-bottom" v-if="cardData.currentDateChange.profit_diff_type=='down'"></i>
                                                <i style="color: #00cc00;font-weight: bold" class="el-icon-top" v-if="cardData.currentDateChange.profit_diff_type=='up'"></i>

                                                <span>{{cardData.currentDateChange.profit_diff}}&nbsp;</span>{{cardData.currentDateChange.profit_ratio}}%</span>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>

                            <el-row style="margin-bottom: 17px" :gutter="10">
                                <el-col :span="12">

                                    <div class="card-content">
                                        <div class="indicator">
                                            <!--                                            <span>日收入<i style="color: #00cc00;font-weight: bold" class="el-icon-top"></i> <span>10.5万&nbsp;</span>15%</span>-->
                                            <span>当月收入</span>
                                        </div>
                                        <div class="number">{{cardData.month_income}}</div>
                                        <div class="indicator" style="min-width: 125px;">
                                            <span>
                                                <i style="color:indianred;font-weight: bold" class="el-icon-bottom" v-if="cardData.currentMonthChange.month_income_diff_type=='down'"></i>
                                                <i style="color: #00cc00;font-weight: bold" class="el-icon-top" v-if="cardData.currentMonthChange.month_income_diff_type=='up'"></i>
                                                <span>{{cardData.currentMonthChange.month_income_diff}}&nbsp;</span>{{cardData.currentMonthChange.month_income_ratio}}%
                                            </span>
                                        </div>

                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="card-content">
                                        <div class="indicator">
                                            <!--                                            <span>日毛利 <i style="color:indianred;font-weight: bold" class="el-icon-bottom"></i> 5%</span>-->
                                            <span>当月毛利</span>
                                        </div>
                                        <div class="number">{{cardData.month_profit}}</div>
                                        <div class="indicator" style="min-width: 125px;">
                                            <!--                                            <span>同比去年 <i class="el-icon-top"></i> 15%</span>-->
                                            <span>
                                                <i style="color:indianred;font-weight: bold" class="el-icon-bottom" v-if="cardData.currentMonthChange.month_profit_diff_type=='down'"></i>
                                                <i style="color: #00cc00;font-weight: bold" class="el-icon-top" v-if="cardData.currentMonthChange.month_profit_diff_type=='up'"></i>

                                                <span>{{cardData.currentMonthChange.month_profit_diff}}&nbsp;</span>{{cardData.currentMonthChange.month_profit_ratio}}%</span>
                                        </div>

                                    </div>
                                </el-col>
                            </el-row>

                            <el-row style="margin-bottom: 8px" :gutter="10">
                                <el-col :span="12">

                                    <div class="card-content">
                                        <div class="indicator">
                                            <!--                                            <span>日收入<i style="color: #00cc00;font-weight: bold" class="el-icon-top"></i> <span>10.5万&nbsp;</span>15%</span>-->
                                            <span>当年收入</span>
                                        </div>
                                        <div class="number">{{cardData.year_income}}</div>
                                        <div class="indicator" style="min-width: 125px;">
                                            <span>
                                                <i style="color:indianred;font-weight: bold" class="el-icon-bottom" v-if="cardData.currentYearChange.year_income_diff_type=='down'"></i>
                                                <i style="color: #00cc00;font-weight: bold" class="el-icon-top" v-if="cardData.currentYearChange.year_income_diff_type=='up'"></i>
                                                <span>{{cardData.currentYearChange.year_income_diff}}&nbsp;</span>{{cardData.currentYearChange.year_income_ratio}}%
                                            </span>
                                        </div>

                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="card-content">
                                        <div class="indicator">
                                            <!--                                            <span>日毛利 <i style="color:indianred;font-weight: bold" class="el-icon-bottom"></i> 5%</span>-->
                                            <span>当年毛利</span>
                                        </div>
                                        <div class="number">{{cardData.year_profit}}</div>
                                        <div class="indicator" style="min-width: 125px;">
                                            <!--                                            <span>同比去年 <i class="el-icon-top"></i> 15%</span>-->
                                            <span>
                                                <i style="color:indianred;font-weight: bold" class="el-icon-bottom" v-if="cardData.currentYearChange.year_profit_diff_type=='down'"></i>
                                                <i style="color: #00cc00;font-weight: bold" class="el-icon-top" v-if="cardData.currentYearChange.year_profit_diff_type=='up'"></i>

                                                <span>{{cardData.currentYearChange.year_profit_diff}}&nbsp;</span>{{cardData.currentYearChange.year_profit_ratio}}%</span>
                                        </div>

                                    </div>
                                </el-col>
                            </el-row>

                        </div>
                    </el-card>
                </div>
                </div>

            </div>


        <div class="mobile-stats-wrapper" v-if="isConditionMet" style="margin: 8px;margin-top: 80px">
            <div class="mobile-stats-container">
                <div style="height: 105px;background-color: #FFFFFF;margin-bottom: 10px;padding-top:4px;border-radius:4px;display: flex;flex-direction: column;">
                    <div style="text-align: center;">
                        <span style="padding: 10px 0px;color: #808080;font-size: smaller;font-weight: bold">{$formt_year}年任务完成情况</span>
                    </div>
                    <div style="text-align: center;margin-top: 12px;font-size: 12px">
                        <span style="color: #409EFF;">已完成</span>
                        <span style="color: #409EFF;">{{task_data.finish_task}}</span>
                        <span style="color: #a9a9a9">&nbsp|&nbsp</span>
                        <span style="color: #808080;">未完成</span>
                        <span style="color: #808080;">{{task_data.unfinish_task}}</span>
                    </div>
                    <div style="padding: 0 15px;">
                        <el-progress :text-inside="true" :stroke-width="16" :percentage="task_data.task_ratio"></el-progress>
                    </div>
                    <div style="text-align: center;margin-top: 12px;font-size: 12px;color: salmon">
                        <span style="">任务完成比例</span>
                        <span >{{task_data.task_ratio}}%</span>
                        <span style="">时间比例</span>
                        <span >{{task_data.time_ratio}}%</span>
                    </div>
                </div>

                <div style="height: 105px;background-color: #FFFFFF;margin-bottom: 10px;padding-top:4px;border-radius:4px">
                    <div style="text-align: center;">
                        <span style="padding: 10px 0px;color: #808080;font-size: smaller;font-weight: bold">{$formt_year}年权责回款情况</span>
                    </div>
                    <div style="text-align: center;margin-top: 12px;font-size: 14px">
                        <span style="color: #67C23A;">已回款</span>
                        <span style="color: #67C23A;">{{remit_data.remit_money}}</span>
                        <span style="color: #a9a9a9">&nbsp|&nbsp</span>
                        <span style="color: #808080;">未回款</span>
                        <span style="color: #808080;">{{remit_data.unremit_money}}</span>
                        <br/>
                        <!--                        <span style="color: #808080;font-size: x-small;">(-->
                        <span style="color: #909399;font-size: 12px">(
                            <font style="">
                                未回款
                            </font>
                            )
                        </span>
                        <!--                        <span  v-for="(item, index) in remit_data.unremit_detail" :key="index" style="color: #808080;font-size: x-small">-->
                        <span  v-for="(item, index) in remit_data.unremit_detail" :key="index" style="color: #909399;font-size: 12px">
                            <span style="">{{item.name}}</span>:{{item.money}}
                            <span v-if="index !== remit_data.unremit_detail.length - 1" style="color: #a9a9a9;">&nbsp|&nbsp</span>
                        </span>
                    </div>
                    <div style="padding: 0 15px;">
                        <el-progress :text-inside="true" :stroke-width="16" :percentage="remit_data.remit_ratio" status="success"></el-progress>
                    </div>
                </div>

                <!-- 客户收入变化区域 -->
                <div class="sales-report-section-v2" style="margin-bottom: 12px;min-height:262px;background-color: #FFFFFF; display: flex;flex-direction: column;">

                    <div class="product_income" style="height: 32px;line-height:32px;background-color: #FFFFFF;text-align: center;border-radius:0px 0px 4px 4px">
<!--                        <span style="font-size:small;color: #808080;font-size: smaller;font-family:cursive;">客户收入变化情况</span>-->
                        <span style="font-size:small;color: #808080;font-size: smaller;font-weight: bold">客户收入变化情况</span>
                    </div>
                    <el-table
                            :data="customerChangeRateTableData"
                            :span-method="customerChangeRateTableSpanMethod"
                            border
                            style="width: 100%;font-size: 10px;min-width: 100%;table-layout: auto;border-radius:0px 0px 6px 6px">
                        <el-table-column
                                prop="cate_name"
                                label="类别"
                                min-width="46px"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="customer_name"
                                label="客户"
                                min-width="58px">
                        </el-table-column>
                        <el-table-column
                                prop="change_income"
                                label="变化收入"
                                min-width="60px">
                        </el-table-column>
                        <el-table-column
                                prop="change_ratio"
                                label="变化率"
                                min-width="50px">
                        </el-table-column>

                    </el-table>

                </div>

                <!-- TOP10客户情况 -->
                <div class="sales-report-section-v2" style="margin-bottom: 12px;min-height:262px;background-color: #FFFFFF; display: flex;flex-direction: column;">

                    <div class="product_income" style="height: 32px;line-height:32px;background-color: #FFFFFF;text-align: center;border-radius:0px 0px 4px 4px">
                        <!--                        <span style="font-size:small;color: #808080;font-size: smaller;font-family:cursive;">客户收入变化情况</span>-->
                        <span style="font-size:small;color: #808080;font-size: smaller;font-weight: bold">TOP10客户情况</span>
                    </div>
                    <el-table
                            :data="customerTOP10TableData"
                            border
                            :row-style="top10CustomerIncomeRowStyle"
                            style="width: 100%;font-size: 10px;min-width: 100%;table-layout: auto;border-radius:0px 0px 6px 6px">
<!--                        <el-table-column label="日" align="center">-->
                            <el-table-column
                                    prop="day_customer_name"
                                    label="客户名(日)"
                                    min-width="58px"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="day_customer_income"
                                    label="收入(日)"
                                    min-width="48px">
                            </el-table-column>
                            <el-table-column
                                    prop="day_customer_ratio"
                                    label="占比(日)"
                                    min-width="48px">
                            </el-table-column>
<!--                        </el-table-column>-->
<!--                        <el-table-column label="年" align="center">-->
                            <el-table-column
                                    prop="year_customer_name"
                                    label="客户名(年)"
                                    min-width="58px"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="year_customer_income"
                                    label="收入(年)"
                                    min-width="48px">
                            </el-table-column>
                            <el-table-column
                                    prop="year_customer_ratio"
                                    label="占比(年)"
                                    min-width="48px">
                            </el-table-column>
<!--                        </el-table-column>-->
                    </el-table>

                </div>

                <!-- 重点产品收入区域 -->
                <div class="sales-report-section" style="margin-bottom: 12px;border-radius:4px">

                    <div class="product_income" style="height: 32px;line-height:32px;background-color: #FFFFFF;text-align: center;border-radius:4px 4px 0px 0px">
                        <span style="font-size:small;color: #808080;font-size: smaller;font-weight: bold">重点产品收入</span>
                    </div>
                    <el-table
                            :data="keyTableData"
                            :span-method="keyTableSpanMethod"
                            border
                            :cell-style="keyTableTdStyle"
                            style="width: 100%;font-size: 10px;min-width: 100%;table-layout: auto;border-radius:0px 0px 4px 4px">
                        <el-table-column
                                prop="show_prodcut_name"
                                label="产品"
                                min-width="46px"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="customer_name"
                                label="客户"
                                min-width="54px">
                        </el-table-column>
                        <el-table-column
                                prop="total"
                                label="日调用量"
                                min-width="60px">
                        </el-table-column>
                        <el-table-column
                                prop="income"
                                label="日收入"
                                min-width="50px">
                        </el-table-column>
                        <el-table-column
                                prop="profit"
                                label="日毛利"
                                min-width="50px">
                        </el-table-column>
                        <el-table-column
                                prop="month_income"
                                label="月收入"
                                min-width="50px">
                        </el-table-column>
                        <el-table-column
                                prop="month_profit"
                                label="月毛利"
                                min-width="50px">
                        </el-table-column>
                    </el-table>

                </div>

                <!-- 产品分类收入区域   :span-method="categoryTableSpanMethod" -->

                <div class="sales-report-section" style="margin-bottom: 12px;">
                    <div class="product_income" style="height: 32px;line-height:32px;background-color: #FFFFFF;text-align: center;border-radius:4px 4px 0px 0px">
                        <span style="font-size:small;color: #808080;font-size: smaller;font-weight: bold">产品分类收入</span>
                    </div>
                    <el-table
                            :data="categoryTableData"
                            ref="stickyTable"
                            v-sticky="{top: 0}"
                            border
                            :row-style="categoryTableRowStyle"
                            style="width: 100%;font-size: 10px;min-width: 100%;table-layout: auto;border-radius:0px 0px 4px 4px">
<!--                        <el-table-column-->
<!--                                prop="category_name"-->
<!--                                label="类别"-->
<!--                                min-width="46px"-->
<!--                        >-->
                        </el-table-column>
                        <el-table-column
                                prop="show_prodcut_name"
                                label="产品"
                                min-width="60px">
                        </el-table-column>
                        <el-table-column
                                prop="total"
                                label="日调用量"
                                min-width="55px">
                        </el-table-column>
                        <el-table-column
                                prop="income"
                                label="日收入"
                                min-width="48px">
                        </el-table-column>
                        <el-table-column
                                prop="profit"
                                label="日毛利"
                                min-width="48px">
                        </el-table-column>
                        <el-table-column
                                prop="year_income"
                                label="年收入"
                                min-width="52px">
                        </el-table-column>
                        <el-table-column
                                prop="year_profit"
                                label="年毛利"
                                min-width="52px">
                        </el-table-column>
                    </el-table>

                </div>

                <div class="sales-report-section" style="margin-bottom: 12px;">

                    <div class="product_income" style="height: 52px;line-height:52px;background-color: #FFFFFF;text-align: center;border-radius:4px">
                        <span style="font-size:small;cursor: pointer;">
                        <el-link type="primary" @click="goToUrl" style="color: #6a5acd" target="_blank">
                            <font style="">查看</font>
                            <font>PC</font>
                            <font style="">端</font>
                        </el-link>
                        </span>
                    </div>

                </div>

                <!-- 起量客户 产品  -->
            <!--
            <div class="sales-report-section" style="margin-bottom: 12px;">

                <div class="product_income" style="height: 52px;line-height:52px;background-color: #FFFFFF;text-align: center">
                    <span style="font-size:small;color: #808080;font-size: smaller;font-family:cursive;">突然起/降量客户/产品、重大新客户...</span>
                </div>

            </div>
            -->


            </div>
        </div>
    </template>

</div>
</body>
<script>
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days*24*60*60*1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "")  + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for(var i=0;i < ca.length;i++) {
            var c = ca[i];
            while (c.charAt(0)==' ') c = c.substring(1,c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
        }
        return null;
    }

</script>
<script>
    var vm = new Vue({
        el:'#app',
        data:{
            user_token:'user_mobile_token',
            isConditionMet: false,
            cardData:{
                'income':'0.0',
                'profit':'0.0',
                'year_income':'0.0',
                'year_profit':'0.0',
                'currentDateChange':{},
                'currentMonthChange':{},
                'currentYearChange':{}
            },
            task_data:{
                'finish_task':'0.0万',
                'unfinish_task':'0.0万',
                'task_ratio':0
            },
            remit_data:{
                'remit_money':'0.0万',
                'unremit_money':'0.0万',
                'remit_ratio':0,
                'unremit_detail':[]
            },
            customerChangeRateTableData: [],
            customerTOP10TableData: [],
            keyTableData: [],
            categoryTableData: []
        },
        created: function(){
            let user_token_name = this.user_token;
            let user = getCookie(user_token_name); // 获取名为'user_token'的cookie值
            //console.log('user_token:'+user);
            if(user){
                this.isConditionMet = true;
                this.getCardData();
            }else{
                this.authLogin();
            }

        },
        methods:{
            authLogin:function (){
                //授权登录
                console.log('授权登录');
                setCookie('flag_day', "{$date}", 1);
                var redirect_uri = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/ReportTask/MobileStat/authLogin.html?from_page=mobile";
                var jmup_url = 'https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=cli_a425066d24a9900b&'
                    +'redirect_uri='+ encodeURIComponent(redirect_uri) +'&state=STATE';
                window.location.href = jmup_url;
            },
            getCardData:function(){
                var self = this;
                var url = "{$Think.config.FINANCE_STAT_API_DOMAIN}/dashboard/mobile/reportData/list";

                var date = {$date};
                var where = {date:date};

                let user_token_name = this.user_token;
                axios.post(url, where, {
                    headers: {
                        'mobile-token':  getCookie(user_token_name)
                    }
                }).then(function (response) {
                    if(response.data.status == 0){
                        self.cardData = response.data.data.cardData;
                        self.task_data = response.data.data.task_data;
                        self.remit_data = response.data.data.remit_data;
                        self.customerChangeRateTableData = response.data.data.change_rate_data;
                        self.customerTOP10TableData = response.data.data.customer_top10_income;
                        self.keyTableData = response.data.data.key_product_data;
                        self.categoryTableData = response.data.data.category_table;
                    }else if(response.data.status == 101){
                        //当token过期失效，从新授权登录
                        this.authLogin();
                    }else{
                        window.location.href = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/ReportTask/MobileStat/noPermission.html?error_msg="+response.data.msg;
                    }

                }).catch(function (error) {
                    console.log(error);
                });

            },
            keyTableTdStyle:function ({row, column, rowIndex, columnIndex}){
                if(row.customer_name == '合计'){
                    return {'color':'red','font-weight':'bolder'}
                }
                if(row.customer_name == '小计' && columnIndex > 0){
                    return {'color':'#1e90ff','font-weight':'bolder'}
                }
            },
            customerChangeRateTableSpanMethod:function ({ row, column, rowIndex, columnIndex }){

                if(columnIndex == 0){
                    return [row.category_merge_count, 1];
                }

                return [1, 1]; //不合并
            },
            keyTableSpanMethod:function ({ row, column, rowIndex, columnIndex }){
                if (rowIndex === 0) {
                    if (columnIndex === 0) {
                        return {
                            rowspan: 1,
                            colspan: 2
                        };
                    }else if(columnIndex ==1 ) {
                        return {
                            rowspan: 1,
                            colspan: 0
                        };
                    }else{
                        return {
                            rowspan: 1,
                            colspan: 1
                        };
                    }
                }

                if(columnIndex == 0 && rowIndex > 0){
                    return [row.prodcut_name_merge_count, 1];
                }

                return [1, 1]; //不合并
            },
            top10CustomerIncomeRowStyle:function ({row, rowIndex}){
                if(row.day_customer_name == '合计' || row.year_customer_name == '合计'){
                    return {'color':'red','font-weight':'bolder'}
                }
            },
            categoryTableRowStyle:function ({row, rowIndex}){
                if(row.show_prodcut_name == '合计'){
                    return {'color':'red','font-weight':'bolder'}
                }
                if(row.show_prodcut_name == '运营商-小计'){
                    return {'color':'#1e90ff','font-weight':'bolder'}
                }
                if(row.show_prodcut_name == '核验-小计'){
                    return {'color':'#1e90ff','font-weight':'bolder'}
                }
                if(row.show_prodcut_name == '代理-小计'){
                    return {'color':'#1e90ff','font-weight':'bolder'}
                }
                if(row.show_prodcut_name == '自有-小计'){
                    return {'color':'#1e90ff','font-weight':'bolder'}
                }
                if(row.show_prodcut_name == '存量洞察-小计'){
                    return {'color':'#1e90ff','font-weight':'bolder'}
                }
            },
            categoryTableSpanMethod:function ({ row, column, rowIndex, columnIndex }){
                if (rowIndex === 0) {
                    if (columnIndex === 0) {
                        return {
                            rowspan: 1,
                            colspan: 2
                        };
                    }else if(columnIndex ==1 ) {
                        return {
                            rowspan: 1,
                            colspan: 0
                        };
                    }else{
                        return {
                            rowspan: 1,
                            colspan: 1
                        };
                    }
                }

                if(columnIndex == 0 && rowIndex > 0){
                    return [row.category_merge_count, 1];
                }

                return [1, 1]; //不合并
            },
            // 这个方法用来动态设置 height
            getAutoHeight() {
                let el = document.querySelector("#app"),
                    elParent = el.parentNode;
                // 一定要使用 nextTick 来改变height 不然不会起作用
                var self = this;
                self.$nextTick(() => {
                    self.height = elParent.clientHeight + 'px';
                });

            },
            goToUrl() {
                //在这里可以添加跳转前的逻辑，例如数据验证等
                var goUrl = "{$Think.config.FINANCE_STAT_WEB_DOMAIN}/Stat/Dashboard/Index.html";
                window.open(goUrl, '_blank'); // 在新标签页中打开链接

            }
        }

    })
</script>

<style scoped>
    .mobile-stats-wrapper {
        /* 渐变背景色 */
        /*background: linear-gradient(to right, #303F9F, #009688);*/
        height: 100vh; /* 占据全屏高度 */
        /*display: flex;*/
        /*justify-content: center;*/
        /*align-items: flex-start;*/
        padding-top: 10px; /* 头部空间 */
        box-sizing: border-box;
    }

    .mobile-stats-container {
        max-width: 100%; /* 容器宽度，根据手机屏幕自动调整 */
        /*background-color: rgba(255, 255, 255, 0.9); !* 半透明白色背景 *!*/
        /*background: linear-gradient(to right, #303F9F, #009688);*/
        /*padding: 15px;*/
        background-color:#a9a9a91c;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: auto; /* 允许内容滚动 */
    }

    .header {
        text-align: center;
        /*padding: 10px 0;*/
        color: #fff;
        /*background-color: rgba(0, 0, 0, 0.1);*/
        border-radius: 10px 10px 0 0;
    }

    .analysis-section,
    .sales-report-section {
        margin-bottom: 10px;
    }
    .sales-report-section-v2 {
        margin-bottom: 10px;
    }

    /* 其他样式... */
    .transition-box {
        margin-bottom: 10px;
        width: 162px;
        /*height: 100px;*/
        border-radius: 4px;
        /*background-color: #409EFF;*/
        text-align: center;
        color: #fff;
        /*color: red;*/
        /*padding: 40px 20px;*/
        /*padding: 10px 10px;*/
        box-sizing: border-box;
        /*margin-right: 20px;*/
        background: linear-gradient(to right, #303F9F, #009688);
        height: 60px;
        /*line-height: 80px;*/
        /*padding: 5px 5px 0px 0px;*/
    }
    .box-card1{
        height: 66px;
        padding-top:5px;
    }
    .box-card2{
        height: 66px;
        padding-top:5px;
    }
    .box-card3{
        /*background: linear-gradient(to right, #303F9F, #009688);*/
        line-height: 60px;
        padding-top:0px;
    }
    .box-card4{
        /*background: linear-gradient(to right, #303F9F, #009688);*/
        line-height: 60px;
        padding-top:0px;
    }
    .box-card5{
        /*background: linear-gradient(to right, #303F9F, #009688);*/
        line-height: 60px;
        padding-top:0px;
    }
    .box-card6{
        /*background: linear-gradient(to right, #303F9F, #009688);*/
        line-height: 60px;
        padding-top:0px;
    }
    .sales-report-section .el-table .cell{
        line-height: 18px;
    }
    .sales-report-section-v2 .el-table .cell{
        line-height: 18px;
    }
    .sales-report-section .el-table .cell, .el-table th div, .el-table--border td:first-child .cell, .el-table--border th:first-child .cell{
        /*padding-left: 1px;*/
    }
    .sales-report-section-v2 .el-table .cell, .el-table th div, .el-table--border td:first-child .cell, .el-table--border th:first-child .cell{
        /*padding-left: 1px;*/
    }
    .sales-report-section, .el-table .cell, .el-table th div{
        padding-right: 0px;
    }
    .sales-report-section-v2, .el-table .cell, .el-table th div{
        padding-right: 0px;
    }

    .el-card__body{
        padding-bottom: 5px;
    }
</style>

<style scoped>
    .dashboard-container {
        margin: -10px;
    }

    .card-item {
        padding: 10px;
    }

    .box-card {
        height: 100%;
    }

    .card-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .number {
        /*font-size: 24px;*/
        font-size: 18px;
        /*margin-bottom: 5px;*/
        margin-bottom: -2px;
        font-weight: bold;
    }

    .indicator {
        font-size: 12px;
        color: #909399;
    }

    .el-icon-top, .el-icon-bottom {
        margin: 0 5px;
    }

    .el-table .el-table__cell{
        padding: 7px 0px;
    }
</style>

</html>
