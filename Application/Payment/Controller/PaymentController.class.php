<?php

namespace Payment\Controller;

use Common\Controller\AdminController;
use Payment\Repositories\PaymentRepository;
use Payment\Repositories\ChannelRemitRepository;


/**
 * @title    渠道打款单控制器
 * <AUTHOR>
 * @datetime 16:26 2021/9/10
 **/
class PaymentController extends AdminController
{
    private $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new PaymentRepository();
    }

    //列表页
    public function index()
    {

        $data = $this->repository->getListData();
        $this->assign($data);
        $this->display();
    }

    //增加
    public function add()
    {
        if (IS_POST) {
            switch (I('post.type', '', 'trim')) {
                case 'valid_serial' :
                    //验证流水单号唯一性
                    $res    = $this->repository->valid_serial(I('post.serial', '', 'trim'));
                    $status = 0;
                    $result = $res ? 1 : 0;
                    $this->ajaxReturn(compact('status', 'result'));
                    break;
                case 'file_in' :
                    //excel导入收款单
                    try {
                        $data = $this->repository->file_in($this->loginuser['username']);
                    } catch (\Exception $e) {
                        exit('error：' . $e->getMessage());
                    }
                    if (empty($data['copy_row']) && empty($data['exists_row'])) {
                        exit('成功导入' . $data['count'] . '条数据');
                    } else {
                        $this->assign($data);
                        $this->display('file_in');
                    }
                    break;
                default :
                    //增加收款单
                    $res = $this->repository->add($this->loginuser['username']);
                    if ($res === true) {
                        $this->redirect('index');
                    } else {
                        exit($res);
                    }
                    break;
            }
        } else {
            $this->display();
        }
    }

    //删除
    public function del()
    {
        $this->repository->del();
        redirect($_SERVER['HTTP_REFERER']);
    }

    //批量导出
    public function file_out()
    {
        $this->repository->file_out();
    }

    //提交打款单
    public function add_remit()
    {
        if (IS_POST) {
            if (I('post.type') == 'valid_serial') {
                //验证流水号是否存在
                $remit_id = I('post.remit_id', '', 'intval')?:null;
                $res = (new ChannelRemitRepository($this->loginuser))->valid_serial(I('post.serial', '', 'trim'), $remit_id);
                $status = 0;
                $result = $res?1:0;
                $this->ajaxReturn(compact('status', 'result'));
            } else {
                try {
                    $this->repository->add_remit($this->loginuser);
                    $this->redirect('index');
                } catch (\Exception $exception) {
                    $this->error('增加失败' . $exception->getMessage(), 'index');
                }
            }
        } else {
            $data = $this->repository->getReceiptDataForAddRemite(I('get.pay_serial', ''));
            $this->assign('data', $data);
            $this->display();
        }
    }

}