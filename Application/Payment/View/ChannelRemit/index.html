<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        .row-first {
            margin-bottom: 10px;
        }
        label {
                     margin-left: 10px;
                 }
        .row_second{
            margin-bottom:10px;
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .admit_input{
            width: 52%;
            display: inline-block;
            border: none;
            color: #333;
            box-shadow: none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        /*日志模板*/
        .log_container{
            width:80%;
            margin:0 auto;
        }
        .log_grid{
            width: 100%;
        }
        .log_raw{
            width: 100%;
            height: 30px;
            line-height: 30px;
            color:#666;
        }
        .log_circle{
            width:14px;
            height:14px;
            border-radius:7px;
            float: left;
            margin-top:8px;
            background:#286090;
        }
        .log_admin{
            float: left;
            margin-left:20px;
            font-size:18px;
            color:#333;
        }
        .log_date{
            float: right;
        }
        .log_content{
            width: calc(100% - 14px);
            margin:0 auto;
            border-left:1px solid #ccc;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            padding:10px 20px;
        }
        .log_grid:last-child .log_content{
            border-left:1px solid #fff;
        }
        .log_remark{
            width:98%;
            margin:10px auto 0;
            min-height:24px;
            line-height:24px;
            border:1px solid #ccc;
            background:#f5f5f5;
            padding:0 10px;
            border-radius:4px;
        }
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container index_search">
    <form action="{:U('index')}" class="form-inline" method="get">
        <div class="form-group">
            <label class="control-label" for="start_time">打款日期：</label>
            <input type="date" name="start_time" id="start_time" class="form-control" value="{$input.start_time}"/>
            -
            <input type="date" name="end_time" id="end_time" class="form-control" value="{$input.end_time}"/>
        </div>
        <div class="form-group">
            <label for="min_money">金额（元）：</label>
            <input type="text" name="min_money" id="min_money" class="form-control" value="{$input.min_money}" placeholder="最小金额" maxlength="16" autocomplete="off"/>
            -
            <input type="text" name="max_money" id="max_money" class="form-control" value="{$input.max_money}" placeholder="最大金额" maxlength="16" autocomplete="off"/>
        </div>

        <div class="form-group">
            <label class="control-label" for="name">收款方名称：</label>
            <select name="name" id="name" class="form-contorl">
                <option value="">选择收款方</option>
                {$input.name_option}
            </select>
        </div>
        <div class="form-group">
            <label class="control-label" for="remit_serial">流水号（打款单）：</label>
            <input type="text" name="remit_serial" id="remit_serial" class="form-control" maxlength="20" value="{$input.remit_serial}" placeholder="流水号（打款单）" autocomplete="off" />
        </div>
        <div class="form-group">
            <label class="control-label" for="status">状态：</label>
            <select class="form-control" name="status" id="status">
                {$input.status_select}
            </select>
        </div>
        <div class="form-group">
            <label class="control-label" for="channel_id">选择渠道：</label>
            <select name="channel_id" id="channel_id">
                {$input.channel_select}
            </select>
        </div>
        <div class="form-group">
            <label class="control-label" for="company">公司名称：</label>
            <input type="text" name="company" id="company" class="form-control" value="{$input.company}" placeholder="公司名称" autocomplete="off"  />
        </div>
        <div class="form-group">
            <label class="control-label" for="contract_no">合同编号：</label>
            <input type="text" name="contract_no" id="contract_no" class="form-control" value="{$input.contract_no}" placeholder="合同编号" autocomplete="off" />
        </div>
        <div class="form-group">
            <input id="list_submit" type="submit" class="btn btn-primary btn-sm" value="查询">
        </div>
        <!--
        <div class="form-group">
            <button type="button" id="add" class="btn btn-success btn-sm">新增打款单</button>
        </div>
        -->
    </form>
</div>
<div class="container">
    <div class="table_title">
        数据统计：<b>{$count}</b>条打款记录，合计金额：<b>{$total_money|round=###,2}</b>（其中已认款金额：<b>{$affirm_money|round=###,2}</b>）
    </div>
    <div class="panel panel-default table-responsive">
        <table id="table_dataTable" class="table table-bordered table-striped table-hover">
            <thead>
            <tr align="center">
                <th style="text-align:center;">渠道名称</th>
                <th style="text-align:center;">公司名称</th>
                <th style="text-align:center;">合同编号</th>
                <th style="text-align:center;">收款方名称</th>
                <th style="text-align:center;">金额（元）</th>
                <th style="text-align:center;">收款银行</th>
                <th style="text-align:center;">流水单号</th>
                <th style="text-align:center;">打款日期</th>
                <th style="text-align:center;">打款单凭证</th>
                <th style="text-align:center;">状态</th>
                <neq name="role_id" value="3">
                    <th style="text-align:center;">操作</th>
                </neq>
            </tr>
            </thead>
            <tbody>
            <volist name="list_data" id="vo">
                <tr>
                    <td align="center">{$vo.customer_name}</td>
                    <td align="center">{$vo.company}</td>
                    <td align="center">{:autoEnter($vo['contract_no'])}</td>
                    <td align="center">{$vo.name}</td>
                    <td align="center">{$vo.money|round=###,2}</td>
                    <td align="center">{$vo.bank}</td>
                    <td align="center">{:autoEnter($vo['remit_serial'])}</td>
                    <td align="center"><nobr>{$vo.remit_date|date='Y-m-d',###}</nobr></td>
                    <td align="center">
                        <notempty name="vo['proof_image']">
                            <img src="/Uploads/channel_remit_proof/{$vo.proof_image}" class="proof">
                            <else />
                            暂无图片
                        </notempty>
                    </td>
                    <td align="center">
                        <a href="javascript:log('{$vo.id}');"><nobr>{$status[$vo['status']]}</nobr></a>
                    </td>
                    <neq name="role_id" value="3">
                        <td align="center">
                            <!--商务部门可编辑-->
                            <neq name="vo['status']" value="3">
                                <a href="javascript:edit('{$vo.id}');"><nobr>编辑</nobr></a>
                            </neq>
                            <if condition="$vo['status']!=3 && $vo['status']!=2">
                                <a href="javascript:reject('{$vo.id}');"><nobr>驳回</nobr></a>
                                <a href="javascript:admit('{$vo.id}');"><nobr>认款</nobr></a>
                            </if>
                        </td>
                    </neq>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
    <if condition="$page">
        <ul class="pagination">
            {$page}
        </ul>
    </if>
</div>
</div>
<div class="modal fade" id="add_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" style="position:absolute;overflow-y:auto;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">请填写打款信息</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="add_form" method="post" enctype="multipart/form-data"></form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="add_submit">增加</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="edit_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="">请编辑打款信息</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('edit')}" id="edit_form" method="post" enctype="multipart/form-data"></form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="edit_submit">保存</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="log_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="">打款单日志</h4>
            </div>
            <div class="modal-body" id="log_modal_body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">我知道了</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="admit_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="exampleModalLabel">请验证认款信息</h4>
            </div>
            <div class="modal-body" id="admit_modal_body">
                <form action="{:U('admit')}" id="admit_modal_form" method="post"></form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="admit_submit">认款</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="reject_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="exampleModalLabel">驳回打款单</h4>
            </div>
            <div class="modal-body" id="reject_modal_body">
                <form action="{:U('admit')}" id="reject_modal_form" method="post">
                    <input type="hidden" name="type" value="reject">
                    <div class="form-group">
                        <textarea class="form-control" name="remark" id="reject_remark" cols="30" rows="10" placeholder="请填写驳回原因" style="width:80%;margin:10px auto;display:block;resize: none;"></textarea>
                    </div>
                    <input type="hidden" name="id" value="" id="reject_id" />
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="reject_submit">驳回</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="img_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <img src="" alt="点击退出" style="width:80%;margin:20px auto;display:block;">
</div>
<script type="text/javascript">
    //客户的数据
    var customer = {$customer};
    $(document).ready(function () {
        $("#name").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择打款方',
            width: '200px'
        });
        $("#channel_id").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择渠道',
            width: '200px'
        });
        $("#add").click(function () {
            $.get("{:U('add')}", function (res) {
                $("#add_form").html(res);
                $("#add_modal").modal({});
                $("#add_customer_id").select2({
                    allowClear: true,
                    theme: "bootstrap",
                    width: '100%',
                    dropdownParent:$("#add_modal")
                });
            });
        });
        //切换客户修改公司名称
        $("#add_form").on('change', '#add_customer_id', function() {
            //let company = customer[$(this).val()].company;
            let company = customer[$(this).val()];
            $("#add_form").find("#add_company").html(company);
        }).on('click', '#upload_image', function () {
            //单图上传并展示缩略图
            $("#add_proof_image").trigger('click');
        }).on('click', '#add_proof_image', function(event) {
            //阻止事件冒泡
            event.stopImmediatePropagation();
        }).on('change', "#add_proof_image", function () {
            let file = $(this)[0].files[0];
            if (typeof file=='undefined') {
                $("#upload_image").find('img').remove();
                alert('请选择需要上传的凭证图片');
                return false;
            }
            let size = file.size;
            let ext = file.name.split('.')[1];
            if (size>2*1024*1024) {
                alert('您上传的图片文件过大，请处理后重新上传');
                $(this).val('');
                return false;
            }
            if (ext!='gif' && ext!='jpg' && ext!='png' && ext!='jpeg') {
                alert("您上传的图片格式暂不支持，请处理后重新上传");
                $(this).val('');
                return false;
            }
            let reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = function(e) {
                let image_html = '<image src="' + this.result + '" style="height:100%;" />';
                if ($("#upload_image").find("img").length>0) {
                    $("#upload_image").find('img').remove();
                }
                $("#upload_image").append(image_html);
            };
        });
        //提交增加的数据
        $("#add_submit").click(function () {
            //验证打款方
            if (valid_empty($('#add_name'))) {
                alert('请填写打款方');
                return false;
            }
            //打款金额
            if (valid_empty($('#add_money'))) {
                alert('请填写打款金额');
                return false;
            }
            //金额格式验证
            if (!valid_money_format($('#add_money').val())) {
                alert('打款金额格式不正确');
                return false;
            }
            //打款银行
//            if (valid_empty($('#add_bank'))) {
//                alert('请填写打款银行');
//                return false;
//            }
            //打款日期
            if (valid_empty($('#add_remit_date'))) {
                alert('请选择付款日期');
                return false;
            }
            //打款日期不可超过当前时间
            if (valid_date($("#add_remit_date").val())) {
                alert ("请选择小于当前时间的的打款日期");
                return false;
            }
            //验证流水号
            if (valid_empty($("#add_remit_serial"))) {
                alert ("请填写流水号");
                return false;
            }
            //验证流水号格式
            if (!valid_remit_serial_format($("#add_remit_serial").val())) {
                alert('流水号格式不正确');
                return false;
            }
            //流水号唯一性验证
            if (valid_serial_exists($("#add_remit_serial").val())) {
                alert('该流水单号已存在,请核对后重试');
                return false;
            }

            //验证凭证
//            if (valid_empty($("#add_proof_image"))) {
//                alert('请选择凭证图片');
//                return false;
//            }
            $("#add_form").submit();
        });
        //条件查询
        $("#list_submit").click(function () {
            //验证金额是否符合标准
            if (!valid_empty($("#max_money"))) {
                if (!valid_money_format($("#max_money").val())) {
                    alert('最大金额格式不符合标准');
                    return false;
                }
            }
            if (!valid_empty($("#min_money"))) {
                if (!valid_money_format($("#min_money").val())) {
                    alert('最小金额格式不符合标准');
                    return false;
                }
            }
            $("#list_form").submit();
        });
        //编辑模态框
        //切换客户修改公司名称
        $("#edit_form").on('change', '#edit_customer_id', function() {
            //let company = customer[$(this).val()].company;
            let company = customer[$(this).val()];
            $("#edit_form").find("#edit_company").html(company);
        }).on('click', '#edit_image', function () {
            //单图上传并展示缩略图
            $("#edit_proof_image").trigger('click');
        }).on('click', '#edit_proof_image', function(event) {
            //阻止事件冒泡
            event.stopImmediatePropagation();
        }).on('change', "#edit_proof_image", function () {
            let file = $(this)[0].files[0];
            if (typeof file=='undefined') {
                $("#edit_image").find('img').remove();
                alert('请选择需要上传的凭证图片');
                return false;
            }
            let size = file.size;
            let ext = file.name.split('.')[1];
            if (size>2*1024*1024) {
                alert('您上传的图片文件过大，请处理后重新上传');
                $(this).val('');
                return false;
            }
            if (ext!='gif' && ext!='jpg' && ext!='png' && ext!='jpeg') {
                alert("您上传的图片格式暂不支持，请处理后重新上传");
                $(this).val('');
                return false;
            }
            let reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = function(e) {
                let image_html = '<image src="' + this.result + '" style="height:100%;" />';
                if ($("#edit_image").find("img").length>0) {
                    $("#edit_image").find('img').remove();
                }
                $("#edit_image").append(image_html);
            };
        });
        //提交编辑数据
        $("#edit_submit").click(function () {
            //验证打款方
            if (valid_empty($('#edit_name'))) {
                alert('请填写打款方');
                return false;
            }
            //打款金额
            if (valid_empty($('#edit_money'))) {
                alert('请填写打款金额');
                return false;
            }
            //金额格式验证
            if (!valid_money_format($('#edit_money').val())) {
                alert('打款金额格式不正确');
                return false;
            }
            //打款银行
//            if (valid_empty($('#edit_bank'))) {
//                alert('请填写打款银行');
//                return false;
//            }
            //打款日期
            if (valid_empty($('#edit_remit_date'))) {
                alert('请选择付款日期');
                return false;
            }
            //验证打款日期是否超过当前时间
            if (valid_date($("#edit_remit_date").val())) {
                alert ("请选择小于当前时间的的打款日期");
                return false;
            }
            //验证流水号
            if (valid_empty($("#edit_remit_serial"))) {
                alert ("请填写流水号");
                return false;
            }
            //验证流水号格式
            if (!valid_remit_serial_format($("#edit_remit_serial").val())) {
                alert('流水号格式不正确');
                return false;
            }
            //流水号唯一性验证
            if (valid_serial_exists($("#edit_remit_serial").val(), $("#edit_remit_id").val())) {
                alert('该流水单号已存在,请核对后重试');
                return false;
            }
            $("#edit_form").submit();
        });
        //提交认款数据
        $("#admit_submit").click(function () {
            //验证收款流水单号
            if ($("#admit_receipt_serial").length==0) {
                alert('抱歉，未找到符合条件的收款流水号，不可认款');
                return false;
            }
            if (valid_empty($("#admit_receipt_serial"))) {
                alert('请选择收款流水单号');
                return false;
            }
            $("#admit_modal_form").submit();
        });
        //驳回数据提交
        $("#reject_submit").click(function () {
            //验证驳回原因
            if (valid_empty($("#reject_remark"))) {
                alert("请填写驳回原因");
                return false;
            }
            $("#reject_modal_form").submit();
        });
        //图片放大预览
        $("#table_dataTable img").click(function () {
            let src = $(this).attr('src');
            $("#img_modal").find('img').attr('src', src);
            $("#img_modal").modal();
        });
        //图片预览关闭
        $("#img_modal").click(function () {
            $("#img_modal").modal('hide');
        });
    });
    //验证制定的数据是否为空
    function valid_empty(ele) {
        let val = ele.val();
        if (val=='' || val==null) {
            return true;
        }
        return false;
    }
    //验证某个时间是否超过当前时间
    function valid_date(value)
    {
        let date = new Date();
        let value_arr = value.split('-');
        date.setFullYear(value_arr[0]);
        date.setMonth(value_arr[1] - 1);
        date.setDate(value_arr[2]);
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        date.setMilliseconds(0);
        let date_time = date.getTime();
        let current = new Date();
        let current_time = current.getTime();
        return date_time>current_time;
    }
    //流水号格式验证
    function valid_remit_serial_format(serial)
    {
        let regex = /^\w{10,20}$/;
        return regex.test(serial);
    }
    //流水号是否存在验证
    function valid_serial_exists(serial, remit_id)
    {
        //设置需要排除的打款ID（编辑时不需要验证的ID值）
        let data = {
            serial : serial,
            type :  'valid_serial'
        };
        if (typeof remit_id!='undefined') {
            data.remit_id = remit_id;
        }
        let is_exists = false;
        $.ajax({
            url : "{:U('add')}",
            data : data,
            async : false,
            type : 'post',
            success : function (res) {
                if (res.result==1) {
                    is_exists = true;
                }
            }
        });
        return is_exists;
    }
    //交易金额格式验证
    function valid_money_format(money)
    {
        let regex = /^(\-?)\d{1,9}(\.\d{1,6})?$/;
        if (regex.test(money)) {
            return true;
        }
        return false;
    }
    //编辑事件
    function edit(id) {
        $.get("{:U('edit')}?id="+id, function(res){
            $("#edit_form").html(res);
            $("#edit_modal").modal({});
            $("#edit_customer_id").select2({
                allowClear: true,
                theme: "bootstrap",
                width: '100%',
                dropdownParent:$("#edit_modal")
            });
        });
    }
    //查看日志
    function log(id)
    {
        $.post("{:U('index')}?id="+id, function (res) {
            $("#log_modal_body").html(res);
            $("#log_modal").modal();
        });
    }
    //认款
    function admit(id)
    {
        $.get("{:U('admit')}?id="+id, function (res) {
            $("#admit_modal_form").html(res);
            $("#admit_modal").modal();
        });
    }
    //驳回
    function reject(id)
    {
        $("#reject_remark").val("");
        $("#reject_id").val(id);
        $("#reject_modal").modal();
    }
</script>
</body>
</html>
