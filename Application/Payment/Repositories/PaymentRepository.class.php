<?php

namespace Payment\Repositories;


use Account\Model\CustomerModel;
use Home\Model\ChannelModel;
use Payment\Model\ChannelAccountModel;
use Payment\Model\PaymentModel;
use Payment\Model\ChannelRemitModel;
use Common\Model\SystemUserModel;
use Common\ORG\Page;
use Think\Model;
use Think\Upload;

//收款单管理
class PaymentRepository
{
    private $model;
    /**
     * @var \PHPExcel
     **/
    private $objPHPExcel;

    public function __construct()
    {
        $this->model = new PaymentModel();
    }

    /**
     * 获取收款单的数据
     *
     * @access public
     *
     * @param $where array 查询的条件
     * @param $field string|array 需要查询的字段
     * @param $order string 排序号
     * @param $page  Page 如果存在这个参数，
     *
     * @return array
     **/
    public function data($where = [], $field = '*', $order = null, $page = null)
    {
        $where = array_merge([
            'delete_time' => 0
        ], $where);
        $model = $this->model->field($field)->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        if (!is_null($page)) {
            $model = $model->limit($page->firstRow, $page->listRows);
        }
        return $model->select();
    }

    /**
     * 获取收款单数据的数量
     *
     * @access public
     *
     * @param $where array 查询的条件
     *
     * @return integer
     **/
    public function count($where = [])
    {
        $where = array_merge([
            'delete_time' => 0
        ], $where);
        $field = 'count(*) as c';
        $count = $this->data($where, $field);
        return $count[0]['c'];
    }

    /**
     * 获取list数据
     *
     * @access public
     *
     * @return array
     **/
    public function getListData()
    {
        //获取get参数
        $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);
        //获取符合当前条件的数据数量
        $count = $this->count($where);
        //分页
        $listRow = 15;
        $page    = new Page($count, $listRow);
        //查询数据
        $list_data = $this->data($where, '*', 'pay_date desc', $page);
        $page      = $page->show();
        //获取input内容
        $input = $this->getInputByParam($params);
        //统计合计金额
        $where['delete_time'] = 0;
        $total_money          = $this->model->where($where)->sum('money');
        //已认款金额
        $where['status'] = 2;
        $affirm_money    = $this->model->where($where)->sum('money');
        //用户数据
        $user_data = (new SystemUserModel())->field('realname,username')->select();
        $user_data = array_column($user_data, 'realname', 'username');
        //所有客户数据
        $customer = (new ChannelModel)->field('label, name, channel_id')
            ->where([
                'status' => 1
            ])->select();
        //渠道账户数据
        $channel_account_data = (new ChannelAccountModel)->select();
        $channel_account_option = [];
        foreach ($channel_account_data as $k=>$v){
            $channel_account_option[$v['channel_id']]  .= makeOption([$v['id']=>$v['account']]);
        }
        $channel_account_option = json_encode($channel_account_option,JSON_UNESCAPED_UNICODE);
        $customer = json_encode(array_column($customer, 'label', 'channel_id'), JSON_UNESCAPED_UNICODE);
        return compact('list_data', 'page', 'input', 'count', 'total_money', 'affirm_money', 'user_data', 'customer','channel_account_option');


    }

    /**
     * 通过GET参数获取查询条件
     *
     * @access protected
     *
     * @param $params array 通过Get获取的参数
     *
     * @return array
     **/
    protected function getWhereByParam($params)
    {
        $where = [];
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time                = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $start_time          = min($time);
            $end_time            = max($time) + 86399;
            $where['pay_date'] = ['between', [$start_time, $end_time]];
        } elseif (!empty($params['start_time'])) {
            $where['pay_date'] = ['egt', strtotime($params['start_time'])];
        } elseif (!empty($params['end_time'])) {
            $where['pay_date'] = ['elt', strtotime($params['end_time'])];
        }

        //金额区间
        if (!empty($params['max_money']) && !empty($params['min_money'])) {
            if ($params['min_money'] < $params['max_money']) {
                $where['money'] = ['between', [$params['min_money'], $params['max_money']]];
            } else {
                $where['money'] = ['between', [$params['max_money'], $params['min_money']]];
            }
        } elseif (!empty($params['max_money'])) {
            $where['money'] = ['elt', $params['max_money']];
        } elseif (!empty($params['min_money'])) {
            $where['money'] = ['egt', $params['min_money']];
        }
        //付款方名称
        if (!empty($params['name'])) {
            $where['name'] = ['eq', $params['name']];
        }
        //流水号
        if (!empty($params['pay_serial'])) {
            $where['pay_serial'] = ['eq', $params['pay_serial']];
        }
        //状态
        if ($params['status'] != '-1') {
            $where['status'] = ['eq', $params['status']];
        }
        return $where;
    }

    /**
     * 通过GET参数获取页面中显示的表单内容Input
     *
     * @access protected
     *
     * @param $params array 通过Get获取的参数
     *
     * @return array
     **/
    protected function getInputByParam($params)
    {
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time                 = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $params['start_time'] = date('Y-m-d', min($time));
            $params['end_time']   = date('Y-m-d', max($time));
        }
        //付款方名称Option
        $name                  = $this->model->where([
            'delete_time' => 0
        ])->select();
        $params['name_option'] = makeOption(array_column($name, 'name', 'name'), $params['name']);
        return $params;
    }

    /**
     * 获取GET的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getParamByGet()
    {
        $params = [];
        //交易日期
        $params['start_time'] = I('get.start_time', '', 'trim');
        $params['end_time']   = I('get.end_time', '', 'trim');
        //最小最大的金额
        $params['max_money'] = I('get.max_money', '', 'trim');
        $params['min_money'] = I('get.min_money', '', 'trim');
        //付款方名称
        $params['name'] = I('get.name', '', 'trim');
        //流水号(收款单)
        $params['pay_serial'] = I('get.pay_serial', '', 'trim');
        //状态
        $params['status'] = I('get.status', '-1', 'trim');
        return $params;
    }

    /**
     * AJAX验证某个流水号是否存在
     *
     * @access public
     *
     * @param $serial string 流水号
     *
     * @return boolean
     **/
    public function valid_serial($serial)
    {
        $serial = $this->count([
            'pay_serial' => $serial
        ]);
        return $serial != 0;
    }

    /**
     * 收款单数据增加
     *
     * @access public
     *
     * @param $admin string 操作人
     *
     * @return boolean|string
     **/
    public function add($admin = '')
    {
        //获取POST的数据
        $data = $this->getDataByPost();
        //补充操作人数据
        $data['admin']       = $admin;
        $data['create_time'] = time();
        //验证某流水号是否存在，存在则返回,不存在则增加
        if (!$this->valid_serial($data['pay_serial'])) {
            $this->model->add($data);
            return true;
        }
        return 'this pay_serial is exists';
    }

    /**
     * 获取POST传输的数据
     *
     * @access public
     *
     * @return array
     **/
    private function getDataByPost()
    {
        //流水号
        $pay_serial = I('post.pay_serial', '', 'trim');
        //付款方名称
        $name = I('post.name', '', 'trim');
        $name = str_replace(['(', ')'], ['（', '）'], $name);
        //付款方账号
        $account = I('post.account', '', 'trim');
        //交易金额（元）
        $money = I('post.money', '', 'trim');
        //付款开户行名
        $bank = I('post.bank', '', 'trim');
        //付款日期
        $pay_date = I('post.pay_date', '', 'strtotime');
        return compact('pay_serial', 'name', 'account', 'money', 'bank', 'pay_date');
    }

    /**
     * excel导入收款单
     *
     * @access public
     *
     * @param $admin string 操作人
     *
     * @return mixed
     **/
    public function file_in($admin = '')
    {
        //将文件上传至服务器
        $filePath = $this->upload();
        //读取Excel内容
        $res  = $this->getDataByFile($filePath);
        $data = $res['data'];
        //excel中流水号重复的数据
        $copy_row = $res['copy_row'];
        //对获取到的数据进行验证
        $res  = $this->validData($data, $admin);
        $data = $res['data'];
        //数据库中已存在的流水单号
        $exists_row = $res['exists_list'];
        $count      = count($data);
        if (!empty($data)) {
            //批量增加数据
            $this->model->addAll(array_values($data));
        }
        //删除文件
        @unlink($filePath);
        return compact('count', 'exists_row', 'copy_row');
    }

    /**
     * 上传的文件至服务器
     *
     * @access public
     *
     * @return string|boolean 文件的路径
     **/
    private function upload()
    {
        $config = [
            'savePath' => '',
            'exts'     => ['xls', 'xlsx'],
            'autoSub'  => false,
            'rootPath' => CACHE_PATH,
            'maxSize'  => 1024 * 3 * 1024
        ];
        $upload = new Upload($config);// 实例化上传类
        $res    = $upload->upload();
        if ($res !== false) {
            return CACHE_PATH . $res['file']['savename'];
        } else {
            throw new \Exception('upload excel failed, error message is ' . $upload->getError());
        }
    }

    /**
     * 读取Excel内的数据内容
     *
     * @access private
     *
     * @param $filePath string 文件地址
     *
     * @return array|boolean
     **/
    private function getDataByFile($filePath)
    {
        if (!file_exists($filePath)) {
            throw new \Exception('excel not defined');
        }
        //引入PHPExcel
        $this->include_phpExcel();
        //实例化excel读取对象
        $this->ini_read_excel($filePath);
        //读取Excel的数据
        $res  = $this->getDataForExcel();
        $data = $res['data'];
        //Excel中重复的流水单号
        $copy_row = $res['copy_row'];
        return compact('data', 'copy_row');
    }

    /**
     * 引入Excel
     *
     * @access private
     *
     * @return void
     **/
    private function include_phpExcel()
    {
        include LIB_PATH . '/Org/PHPExcel/PHPExcel.php';
        include LIB_PATH . '/Org/PHPExcel/PHPExcel/Writer/Excel2007.php';
        //$this->objPHPExcel = new \PHPExcel();
    }

    /**
     * excel文件读取预先实例化
     *
     * @access private
     *
     * @return void
     **/
    private function ini_read_excel($filePath)
    {
        try {
            $inputFileType     = \PHPExcel_IOFactory::identify($filePath);
            $objReader         = \PHPExcel_IOFactory::createReader($inputFileType);
            $this->objPHPExcel = $objReader->load($filePath);
        } catch (\Exception $e) {
            die('read excel failed, message : ' . $e->getMessage());
        }
    }

    /**
     * 获取Excel文件中的数据
     *
     * @access private
     *
     * @return array
     **/
    private function getDataForExcel()
    {
        $sheet         = $this->objPHPExcel->getSheet(0);
        $highestRow    = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        $data          = [];
        //用于记录流水号的变量，防止excel中存在相同的流水号
        $serial = [];
        //用于记录存在重复流水号的行
        $copy_row = [];
        //遍历获取每条数据
        for ($row = 10; $row <= $highestRow; $row ++) {
            $temp = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, NULL, TRUE, FALSE);
            //对每行数据进行格式校验，并返回处理后的数据
            $temp = $this->valid_format_v2($temp[0], $row);//使用最新版本验证
            if (empty($temp)) {
                continue;
            }
            //验证之前的遍历是否存在相同的流水单号
            $temp_serial = $temp['pay_serial'];
            if (in_array($temp_serial, $serial)) {
                $copy_row[array_search($temp_serial, $serial)][] = $row;
            } else {
                $serial[$row] = $temp_serial;
                $data[$row]   = $temp;
            }
        }
        return compact('data', 'copy_row');
    }

    /**
     * 验证Excel中的数据
     *
     * @access private
     *
     * @param $data  array 数据
     * @param $admin string 当前操作人
     *
     * @return boolean|array
     **/
    private function validData($data, $admin)
    {
        //验证唯一性
        $pay_serial = ['in', array_column($data, 'pay_serial')];
        $exists_data    = $this->data(compact('pay_serial'), 'pay_serial');
        $exists_list    = [];
        $new_data       = [];
        $exists_data    = array_column($exists_data, 'pay_serial');
        array_walk($data, function ($value, $row) use (&$exists_list, &$new_data, $exists_data, $admin) {
            $serial = $value['pay_serial'];
            if (in_array($serial, $exists_data)) {
                $exists_list[] = $row;
                return false;
            }
            $value['admin']       = $admin;
            $value['create_time'] = time();
            $new_data[]           = $value;
            return true;
        });
        $data = $new_data;
        //将数据库中已经存在的数据删除
        array_walk($exists_list, function ($value) use (&$data) {
            unset($data[$value]);
        });
        return compact('data', 'exists_list');
    }

    /**
     * 对每行的数据的D/I/L/S/T/V的数据进行校验
     *
     * @access protected
     *
     * @param $data array 数据
     * @param $row  integer 行号
     *
     * @return array 处理过的数据
     *
     * @throws \Exception
     */
    protected function valid_format_v2($data, $row)
    {
        //时间格式的校验
        $a = trim($data[3]);//D
        //金额格式
        $f = $data[7];//H
        //流水单号
        $i = $data[11];//L
        //付款方名称
        $q = $data[19];//T
        //付款方账号
        $r = $data[20];//U
        //付款开户行名
        $t = $data[22];//W
        if (empty($a) && empty($f) && empty($i) && empty($r) && empty($q) && empty($t)) {
            return null;
        }
        if (!preg_match('/^(20[0-5][0-9])(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])$/', $a)) {
            throw new \Exception('第' . $row . '行D列值【' . $a . '】格式不符合规则');
        }
        $pay_date = strtotime($a);
        if ($pay_date > time()) {
            throw new \Exception('第' . $row . '行D列值【' . $a . '】超过当前时间，不可录入');
        }
        if (!is_numeric($f)) {
             throw new \Exception('第' . $row . '行H列值【' . $f . '】格式不符合规则');
        }
        $money = round($f,2);// 四舍五入保留两位小数$f;
        if (!preg_match('/^[0-9a-zA-Z]{10,20}$/', $i)) {
            throw new \Exception('第' . $row . '行L列值【' . $i . '】格式不符合规则');
        }
        $pay_serial = $i;
        if (mb_strlen($q, 'UTF-8') > 50 || empty($q)) {
            throw new \Exception('第' . $row . '行T列值【' . $q . '】格式不符合规则');
        }
        $name = str_replace(['(', ')'], ['（', '）'], $q);
        if (mb_strlen($r, 'UTF-8') > 50 || empty($r)) {
            throw new \Exception('第' . $row . '行U列值【' . $r . '】格式不符合规则');
        }
        $account = $r;
        if (mb_strlen($t, 'UTF-8') > 50 || empty($t)) {
            throw new \Exception('第' . $row . '行W列值【' . $t . '】格式不符合规则');
        }
        $bank = $t;
        return compact('pay_date', 'money', 'pay_serial', 'name', 'account', 'bank');
    }


    /**
     * 对每行的数据的A/F/I/Q/R/T的数据进行校验
     *
     * @access protected
     *
     * @param $data array 数据
     * @param $row  integer 行号
     *
     * @return array 处理过的数据
     **/
    protected function valid_format($data, $row)
    {
        //时间格式的校验
        $a = trim($data[0]);
        //金额格式
        $f = $data[5];
        //流水单号
        $i = $data[8];
        //付款方名称
        $q = $data[16];
        //付款方账号
        $r = $data[17];
        //付款开户行名
        $t = $data[19];
        if (empty($a) && empty($f) && empty($i) && empty($r) && empty($q) && empty($t)) {
            return null;
        }
        if (!preg_match('/^(20[0-5][0-9])(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])$/', $a)) {
            throw new \Exception('第' . $row . '行A列值【' . $a . '】格式不符合规则');
        }
        $pay_date = strtotime($a);
        if ($pay_date > time()) {
            throw new \Exception('第' . $row . '行A列值【' . $a . '】超过当前时间，不可录入');
        }
        if (!preg_match('/^(\-?)\d{1,9}(\.\d{1,6})?$/', $f)) {
            throw new \Exception('第' . $row . '行F列值【' . $f . '】格式不符合规则');
        }
        $money = $f;
        if (!preg_match('/^[0-9a-zA-Z]{10,20}$/', $i)) {
            throw new \Exception('第' . $row . '行I列值【' . $i . '】格式不符合规则');
        }
        $pay_serial = $i;
        if (mb_strlen($q, 'UTF-8') > 50 || empty($q)) {
            throw new \Exception('第' . $row . '行Q列值【' . $q . '】格式不符合规则');
        }
        $name = str_replace(['(', ')'], ['（', '）'], $q);
        if (mb_strlen($r, 'UTF-8') > 50 || empty($r)) {
            throw new \Exception('第' . $row . '行R列值【' . $r . '】格式不符合规则');
        }
        $account = $r;
        if (mb_strlen($t, 'UTF-8') > 50 || empty($t)) {
            throw new \Exception('第' . $row . '行T列值【' . $t . '】格式不符合规则');
        }
        $bank = $t;
        return compact('pay_date', 'money', 'pay_serial', 'name', 'account', 'bank');
    }

    /**
     * 根据流水号删除数据
     *
     * @access public
     *
     * @return array
     **/
    public function del()
    {
        $serial = I('get.serial');
        $this->model->where([
            'pay_serial' => $serial,
            'status'         => 0
        ])->save([
            'delete_time' => time()
        ]);
    }

    /**
     * 查询数据的批量导出
     *
     * @access public
     *
     * @return void
     **/
    public function file_out()
    {
        //获取get参数
        $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);
        //查询数据
        $list_data = $this->data($where, '*', 'pay_date desc');
        $this->ini_write_excel();
        //将数据写入Excel中
        $max_row = $this->write_data($list_data);
        //统一设置Excel样式
        $this->setStyle($max_row);
        //下载Excel
        $this->download($params);
        dump($list_data);
        die;

    }

    /**
     * Excel导出初始化对象
     *
     * @access private
     *
     * @return void
     **/
    private function ini_write_excel()
    {
        //引入phpExcel
        $this->include_phpExcel();
        //初始化phpExcelObject
        $this->objPHPExcel = new \PHPExcel();
        //设置Sheet标题
        $this->objPHPExcel->setActiveSheetIndex(0);
        $this->objPHPExcel->getActiveSheet()->setTitle('打款单');
        //设置标题信息
        $title = [
            'A'    => [
                'name'  => '流水号',
                'width' => 18
            ], 'B' => [
                'name'  => '收款方名称',
                'width' => 25
            ], 'C' => [
                'name'  => '收款方账号',
                'width' => 25
            ], 'D' => [
                'name'  => '金额（元）',
                'width' => 18
            ], 'E' => [
                'name'  => '收方开户行名',
                'width' => 25,
            ], 'F' => [
                'name'  => '交易日期',
                'width' => 18,
            ], 'G' => [
                'name'  => '状态',
                'width' => 18
            ]
        ];
        foreach ($title as $col => $value) {
            $this->objPHPExcel->getActiveSheet()->setCellValue($col . '1', $value['name']);
            //设置宽度
            $this->objPHPExcel->getActiveSheet()->getColumnDimension($col)->setWidth($value['width']);
        }
        //设置文字加粗
        $this->objPHPExcel->getActiveSheet()->getStyle('A1:H1')->getFont()->setBold(true);
    }

    /**
     * 将数据写到Excel中
     *
     * @access private
     *
     * @param $data array 数据
     *
     * @return int 行数
     **/
    private function write_data($data)
    {
        $this->objPHPExcel;
        $row = 2;
        foreach ($data as $item) {
            $this->objPHPExcel->getActiveSheet()->setCellValueExplicit('A' . $row, $item['pay_serial'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $item['name'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $item['account'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('D' . $row, $item['money']);
            $this->objPHPExcel->getActiveSheet()->setCellValue('E' . $row, $item['bank'], \PHPExcel_Cell_DataType::TYPE_STRING);
            $this->objPHPExcel->getActiveSheet()->setCellValue('F' . $row, date('Y-m-d', $item['pay_date']));
            $this->objPHPExcel->getActiveSheet()->setCellValue('G' . $row, ($item['status'] ? ($item['status'] == 1 ? '未认款' : '已认款') : '未提交'));
            $row ++;
        }
        return $row --;
    }

    /**
     * 设置Excel样式
     *
     * @access private
     *
     * @param $max_row integer 行数
     *
     * @return void
     **/
    private function setStyle($max_row)
    {
        $cell = 'A1:G' . $max_row;
        //水平居中
        $this->objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        //垂直居中
        $this->objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
        //设置表格高度
        $this->objPHPExcel->getActiveSheet()->getRowDimension('1:' . $max_row)->setRowHeight(18);
    }

    /**
     * 下载Excel
     *
     * @access private
     *
     * @param $params array 当前get的参数
     *
     * @return void
     **/
    private function download($params)
    {
        $filename = '渠道打款单数据' . $params['start_time'] . '_' . $params['end_time'];
        ob_end_clean();//清除缓冲区,避免乱码
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');
        $objWriter = new \PHPExcel_Writer_Excel5($this->objPHPExcel);
        $objWriter->save('php://output');
    }

    /**
     * 获取某条收款单数据
     *
     * @access public
     *
     * @return array
     **/
    public function getReceiptDataForAddRemite($pay_serial)
    {
        $data = $this->model
            ->where([
                'status'         => 0,
                'delete_time'    => 0,
                'pay_serial' => $pay_serial
            ])->find();

        //所有渠道数据
        $channel = (new ChannelModel)->field('label, channel_id, name')->select();
        $channel_account_data = (new ChannelAccountModel)->select();
        $channel_account_option = [];
        foreach ($channel_account_data as $k=>$v){
            $channel_account_option[$v['channel_id']]  .= makeOption([$v['id']=>$v['account']]);
        }

        $data['company']         = $channel[0]['label'];
        $data['channel_option'] = makeOption(array_column($channel, 'label', 'channel_id'));
        $data['channel_account_option'] = $channel_account_option;
        return $data;
    }

    /**
     * 提交打款单
     *
     * @access public
     *
     * @param $admin array
     *
     * @return boolean
     **/
    public function add_remit($admin)
    {
        $repository = new ChannelRemitRepository($admin);
        $repository->run_add();
        $this->model->where([
            'status'      => 0,
            'delete_time' => 0,
            'pay_serial'    => I('post.pay_serial')
        ])->save([
            'status' => 1
        ]);
        return true;
    }
}