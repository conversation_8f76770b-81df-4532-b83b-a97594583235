<?php

namespace Payment\Repositories;

use Account\Model\CustomerModel;
use Common\Controller\DataAuthController;

//use Common\Model\ReceiptModel;
//use Common\Model\RemitLogModel;
//use Common\Model\RemitModel;

use Payment\Model\PaymentModel;
use Payment\Model\ChannelRemitModel;
use Home\Model\ChannelModel;
use Payment\Model\ChannelRemitLogModel;
use Common\ORG\Page;
use Think\Upload;

//打款单
class ChannelRemitRepository
{
    /*
     * @var RemitModel
     */
    private $model;
    private $customerModel;
    private $admin;
    private $status = [ 0 => '已提交', 1 => '已修改', 2 => '已驳回', 3 => '已认款'];
    public function __construct($admin)
    {
        $this->admin = $admin;
        $this->model = new ChannelRemitModel();
        $this->customerModel = new CustomerModel();
    }
    /**
     * 获取打款单的数据
     *
     * @access public
     * @param $where array 查询的条件
     * @param $field string|array 需要查询的字段
     * @param $order string 排序号
     * @param $page Page 如果存在这个参数，
     *
     * @return array
     **/
    public function data($where = [], $field = '*', $order = null, $page = null)
    {
        $model = $this->model->field($field)->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        if (!is_null($page)) {
            $model = $model->limit($page->firstRow, $page->listRows);
        }
        return $model->select();
    }
    /**
     * 获取打款单数据的数量
     *
     * @access public
     * @param $where array 查询的条件
     *
     * @return integer
     **/
    public function count($where = [])
    {
        $field = 'count(*) as c';
        $count = $this->data($where, $field);
        return $count[0]['c'];
    }
    /**
     * 获取list数据
     *
     * @access public
     *
     * @return array
     **/
    public function getListData()
    {
        //获取get参数
        $params = $this->getParamByGet();
        //获取查询条件
        $where = $this->getWhereByParam($params);
        //获取符合当前条件的数据数量
        $count = $this
            ->model
            //->where(DataAuthController::instance()->getRemiteWhere())
            ->where($where)
            ->count();
        //分页
        $listRow = 15;
        $page = new Page($count, $listRow);
        //查询数据
        //$page->firstRow, $page->listRows
        $list_data = $this
            ->model
            ->field('*')
            //->where(DataAuthController::instance()->getRemiteWhere())
            ->where($where)
            ->order('remit_date desc')
            ->limit($page->firstRow, $page->listRows)
            ->select();
        //查询客户数据
        /*
        $customer = $this
            ->customerModel
            ->field('channel_id, name, company, is_delete')
            ->where(DataAuthController::instance()->getCustomerWhere())
            ->select();
        //将客户数据、打款单数据糅合
        $list_data = $this->mergeCustomerRemit($customer, $list_data);
        */
        $channel = (new ChannelModel)->field('label, name, channel_id')->select();
        $list_data = $this->mergeChannelRemit($channel, $list_data);

        $page = $page->show();
        //获取input内容
        $input = $this->getInputByParam($params, $channel);
        //客户的数据JSON值，目的是为了当用户选择不同的客户是需要展示客户的公司
        $customer = json_encode(array_column($channel, 'label', 'channel_id'), JSON_UNESCAPED_UNICODE);
        //计算当前数据的金额和
        $total_money = $this->model->where($where)->sum('money');
        //计算当前数据的已认款金额和
        $where['status'] = 3;
        $affirm_money = $this->model->where($where)->sum('money');
        //状态集合
        $status = $this->status;
        return compact('list_data', 'page', 'input', 'customer', 'status', 'total_money', 'affirm_money', 'count');
    }
    /**
     * 通过GET参数获取查询条件
     *
     * @access protected
     * @param $params array 通过Get获取的参数
     *
     * @return array
     **/
    protected function getWhereByParam($params)
    {
        $where = [];
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time  = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $start_time = min($time);
            $end_time = max($time) + 86399;
            $where['remit_date'] = ['between', [$start_time, $end_time]];
        } elseif (!empty($params['start_time'])) {
            $where['remit_date'] = ['egt', strtotime($params['start_time'])];
        } elseif (!empty($params['end_time'])) {
            $where['remit_date'] = ['elt', strtotime($params['end_time'])];
        }
        //金额区间
        if (!empty($params['max_money']) && !empty($params['min_money'])) {
            if ($params['min_money']<$params['max_money']) {
                $where['money'] = ['between', [$params['min_money'], $params['max_money']]];
            } else {
                $where['money'] = ['between', [$params['max_money'], $params['min_money']]];
            }
        } elseif (!empty($params['max_money'])) {
            $where['money'] = ['elt', $params['max_money']];
        } elseif (!empty($params['min_money'])) {
            $where['money'] = ['egt', $params['min_money']];
        }
        //打款方名称
        if (!empty($params['name'])) {
            $where['name'] = ['eq', $params['name']];
        }
        //流水号
        if (!empty($params['remit_serial'])) {
            $where['remit_serial'] = ['eq', $params['remit_serial']];
        }
        //状态
        if ($params['status']!='-1') {
            $where['status'] = ['eq', $params['status']];
        }
        //客户ID
        if (!empty($params['channel_id'])) {
            $where['channel_id'] = ['eq', $params['channel_id']];
        } else {
            if (!empty($params['company'])) {
                //公司名称
                $customer_data = $this->getCustomerData([
                    'company'   => ['eq', $params['company']]
                ], 'channel_id');
                if (empty($customer_data)) {
                    $channel_id = 'not find channel_id'; //设置一个找不到的客户ID
                } else {
                    $channel_id = array_column($customer_data, 'channel_id');
                }
                $where['channel_id'] = ['in', $channel_id];
            }
        }
        //合同编号
        if (!empty($params['contract_no'])) {
            $where['contract_no'] = ['eq', $params['contract_no']];
        }
        return $where;
    }
    /**
     * 通过GET参数获取页面中显示的表单内容Input
     *
     * @access protected
     * @param $params array 通过Get获取的参数
     * @param $customer array 客户数据
     *
     * @return array
     **/
    protected function getInputByParam($params, $channel)
    {
        $channel = array_column($channel, 'label', 'channel_id');
        //交易日期
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $time  = [strtotime($params['start_time']), strtotime($params['end_time'])];
            $params['start_time'] = date('Y-m-d', min($time));
            $params['end_time'] = date('Y-m-d', max($time));
        }

        //客户选择列表
        $channel_select = '';
        if (!empty($params['channel_id'])) {
            $channel_select .= '<option value="' . $params['channel_id'] . '">' . $channel[$params['channel_id']] . '</option>';
        } else {
            $channel_select .= '<option></option>';
        }
        array_walk($channel, function ($item, $key) use (&$channel_select) {

            $channel_select .= '<option value="' . $key . '">' . $item . '</option>';
        });
        $params['channel_select'] = $channel_select;
        //状态
        $status = $this->status;
        $status_select = '<option value="-1">全部</option>';
        array_walk($status, function ($item, $key) use (&$status_select, $params) {
            $status_select .= '<option value="' . $key . '" ' . ($key==$params['status']?'selected':'') . '>' . $item. '</option>';
        });
        $params['status_select'] = $status_select;
        //打款方名称、公司名称
        $name_data = $this->model->field('name')->select();
        $params['name_option'] = makeOption(array_column($name_data, 'name', 'name'), $params['name']);
        return $params;
    }
    /**
     * 获取GET的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getParamByGet()
    {
        $params = [];
        //交易日期
        $params['start_time'] = I('get.start_time', '', 'trim');
        $params['end_time'] = I('get.end_time', '', 'trim');
        //最小最大的金额
        $params['max_money'] = I('get.max_money', '', 'trim');
        $params['min_money'] = I('get.min_money', '', 'trim');
        //客户ID
        $params['channel_id'] = I('get.channel_id', '', 'trim');
        //公司名称
        $params['company'] = I('get.company', '', 'trim');
        //打款方名称
        $params['name'] = I('get.name', '', 'trim');
        //流水号(打款单)
        $params['remit_serial'] = I('get.remit_serial', '', 'trim');
        //合同编号
        $params['contract_no'] = I('get.contract_no', '', 'trim');
        //状态
        $params['status'] = I('get.status', '-1', 'trim');
        return $params;
    }
    /**
     * AJAX验证某个流水号是否存在
     *
     * @access public
     * @param $serial string 流水号
     * @param $remit_id integer 编辑的数据ID
     *
     * @return boolean
     **/
    public function valid_serial($serial, $remit_id = null)
    {
        $where = ['remit_serial'    => $serial];
        if (!empty($remit_id)) {
            $where['id'] = ['neq', $remit_id];
        }
        $serial = $this->count($where);
        return $serial!=0;
    }
    /**
     * 获取客户数据
     *
     * @access private
     * @param $where array 条件
     * @param $field string 查询的字段
     * @param $order string 排序
     *
     * @return array
     **/
    private function getCustomerData($where = [], $field = '*', $order = null)
    {
        $model = $this->customerModel->where($where)->field($field);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        return $model->select();
    }
    /**
     * 为增加打款信息准备数据
     *
     * @access public
     *
     * @return array
     **/
    public function add()
    {
        //客户数据
        $customer = $this
            ->customerModel
            ->field('channel_id, name, company')
            ->where(['is_delete' => 0])
            ->where(DataAuthController::instance()->getCustomerWhere())
            ->select();
        if (empty($customer)) {
            throw new \Exception('暂无可分配的客户数据');
        }
        $customer_select = '';
        array_walk($customer, function ($item) use (&$customer_select) {
            $customer_select .= '<option value="' . $item['channel_id'] . '"> ' . $item['name'] . ' </option>';
        });
        //获取第一个公司
        $company = $customer[0]['company'];
        return compact('customer_select', 'company');
    }
    /**
     * 将客户数据与打款单数据糅合到一起
     *
     * @access private
     * @param $customer array 客户数据
     * @param $data array 打款单数据
     *
     * @return array
     **/
    private function mergeCustomerRemit($customer, $data)
    {
        $customer = array_column($customer, null, 'channel_id');
        $result = [];
        array_walk($data, function ($item) use (&$result, $customer) {
            $item['customer_name'] = $customer[$item['channel_id']]['name'] . (($customer[$item['channel_id']]['is_delete'])?'<span style="color:red;">（已删除）</span>':'');
            $item['customer_is_delete'] = $customer[$item['channel_id']]['is_delete'];
            $item['company'] = $customer[$item['channel_id']]['company'];
            $result[] = $item;
        });
        return $result;
    }

    private function mergeChannelRemit($channel, $data)
    {
        $channel = array_column($channel, 'label', 'channel_id');
        $result = [];
        array_walk($data, function ($item) use (&$result, $channel) {
            $item['customer_name'] = $channel[$item['channel_id']];
            $item['company'] = $channel[$item['channel_id']];
            $result[] = $item;
        });
        return $result;
    }

    /**
     * 增加数据执行
     *
     * @access public
     *
     * @return void
     **/
    public function run_add()
    {
        $data['channel_id'] = I('post.channel_id', '', 'trim');
        //数据权限校验
        //DataAuthController::instance()->validAllowDoCustomer($data['channel_id']);
        $admin = $this->admin;
        $data['contract_no'] = I('post.contract_no', '', 'trim');
        $data['name'] = str_replace(['(', ')'], ['（','）'], I('post.name', '', 'trim'));
        $data['money'] = floatval(I('post.money', '', 'trim'));
        $data['bank'] = I('post.bank', '', 'trim');
        $data['remit_date'] = strtotime(I('post.remit_date', '', 'trim'));
        $data['remit_serial'] = I('post.remit_serial', '', 'trim');
        $data['admin'] = $this->admin['username'];
        $data['channel_account_id'] = I('post.channel_account_id','','trim');
        $data['create_time'] = time();
        //上传文件
        $proof_image = $this->upload();
        $data['proof_image'] = $proof_image;
        //验证流水单号的唯一性
        if (!empty($data['remit_serial']) && $this->valid_serial($data['remit_serial'])) {
            throw new \Exception('当前流水号已存在，请核对后重试');
        }
        $res = $this->model->add($data);
        if ($res) {
            $this->log($res, 0, $admin['username']);
            return;
        }
        throw new \Exception('数据增加失败');
    }
    /**
     * 将凭证图片保存至Uploads/remit_proof文件夹中
     *
     * @access private
     *
     * @return string
     **/
    private function upload()
    {
        if ($_FILES['file']['size']==0) {
            return '';
        }
        $rootPath = SYSTEM_PATH . 'Uploads/channel_remit_proof/';
        $childrenPath = date('Ymd') . '/';
        $config = [
            'savePath'  => $childrenPath,
            'exts'      => ['png', 'jpg', 'gif'],
            'autoSub'   => false,
            'rootPath'  => $rootPath,
            'maxSize'   => 1024*3*1024
        ];
        $upload = new Upload($config);// 实例化上传类
        $res = $upload->upload();
        if ($res!==false) {
            return $childrenPath . $res['file']['savename'];
        } else {
            throw new \Exception($upload->getError());
        }
    }
    /**
     * 打款单日志
     *
     * @access private
     * @param $remit_id string 打款单ID
     * @param $type integer 操作类型
     * @param $admin string 操作人
     * @param $remark string 备注
     *
     * @return void
     **/
    private function log($remit_id, $type, $admin, $remark = '')
    {
        $model = new ChannelRemitLogModel();
        $do_time = time();
        $model->add(compact('remit_id', 'type', 'admin', 'do_time', 'remark'));
    }
    /**
     * 获取需要编辑的数据
     *
     * @access public
     * @param $role_id integer 角色的ID（1-财务 2-商务 3-其他）
     *
     * @return array
     **/
    public function edit()
    {
        $id = I('get.id');
        $data = $this->data(compact('id'));
        $data = $data[0];
        //数据权限的校验
        $channel_id = $data['channel_id'];
        //DataAuthController::instance()->validAllowDoCustomer($channel_id);
        if ($data['status']==3) {
            throw new \Exception('该条数据已被认款，不允许修改');
        }
        //客户数据
        /*
        $customer = $this
            ->customerModel
            ->field('channel_id, name, company')
            ->where(['is_delete' => 0])
            ->where(DataAuthController::instance()->getCustomerWhere())
            ->select();
        $customer = array_column($customer, null, 'channel_id');
        */

        $channel = (new ChannelModel)->field('label, name, channel_id')
            ->where([
                'status' => 1
            ])->select();
        $channel = array_column($channel, 'label', 'channel_id');

        $channel_select = '';
        array_walk($channel, function ($item, $key) use (&$channel_select, $channel_id) {
            $selected = ($channel_id==$key)?'selected':'';
            $channel_select .= '<option value="' . $key . '" ' . $selected . '> ' . $item . ' </option>';
        });
        //获取第一个公司
        $company = $channel[$data['channel_id']];
        return compact('data', 'company', 'channel_select');
    }
    /**
     * 执行编辑功能
     *
     * @access public
     * @param $admin array 当前登录管理员数据
     *
     * @return void
     **/
    public function run_edit()
    {
        $admin = $this->admin;
        //需要验证当前数据的状态不能为认款状态
        $id = I('post.id');
        $oldData = $this
            ->model
            ->field('status, channel_id')
            ->where(compact('id'))
            ->where(DataAuthController::instance()->getRemiteWhere())
            ->find();
        if (empty($oldData)) {
            throw new \Exception('该数据不存在或不具备该客户的数据权限');
        }
        $status = $oldData['status'];
        $channel_id = $oldData['channel_id'];

        if ($status==3) {
            throw new \Exception('该条数据已被认款，不可编辑');
        }
        $data['channel_id'] = I('post.channel_id', '', 'trim');

        //再次校验新的客户的数据权限
        DataAuthController::instance()->validAllowDoCustomer($data['channel_id']);

        $data['contract_no'] = I('post.contract_no', '', 'trim');
        $data['name'] = str_replace(['(', ')'], ['（','）'], I('post.name', '', 'trim'));
        $data['money'] = floatval(I('post.money', '', 'trim'));
        $data['bank'] = I('post.bank', '', 'trim');
        $data['remit_date'] = strtotime(I('post.remit_date', '', 'trim'));
        $data['remit_serial'] = I('post.remit_serial', '', 'trim');
        $data['status'] = 1;
        //上传文件
        $proof_image = $this->upload();
        if (!empty($proof_image)) {
            $data['proof_image'] = $proof_image;
        }
        //验证流水单号的唯一性
        if (!empty($data['remit_serial']) && $this->valid_serial($data['remit_serial'], $id)) {
            throw new \Exception('当前流水号已存在，请核对后重试');
        }
        $where = compact('id');
        $where['status'] = ['neq', 3];
        $res = $this->model->where($where)->save($data);
        if ($res) {
            $this->log($id, 1, $admin['username']);
            return;
        }
        throw new \Exception('数据未发生变化');
    }
    /**
     * 获取制定打款流水单号的日志
     *
     * @access public
     * @param $id string 流水单ID
     *
     * @return array
     **/
    public function remit_log($id)
    {
        return $this->getRemitLog($id);
    }
    /**
     * 根据remit_id获取日志数据
     *
     * @access private
     * @param $remit_id integer 打款单流水号
     *
     * @return array
     **/
    private function getRemitLog($remit_id)
    {
        $model = new ChannelRemitLogModel();
        return $model->where(compact('remit_id'))->order('do_time desc')->select();
    }
    /**
     * 根据一个打款流水单号获取打款单ID
     *
     * @access private
     * @param $remit_serial string 打款流水单号
     *
     * @return integer
     **/
    private function getIdBySerial($remit_serial)
    {
        return $this->model->where(compact('remit_serial'))->getField('id');
    }
    /**
     * 获取认款页面所需数据
     *
     * @access public
     *
     * @return array
     **/
    public function admit()
    {
        $id = I('get.id');
        $data = $this
            ->model
            ->where(compact('id'))
            //->where(DataAuthController::instance()->getRemiteWhere())
            ->find();
        if (empty($data)) {
            throw new \Exception('该数据不存在或不具备该客户的数据权限');
        }

        //验证打款数据的状态，不能为2/3
        if ($data['status']==2) {
            throw new \Exception('该条数据已被驳回，不可认款');
        } elseif ($data['status']==3) {
            throw new \Exception('该条数据已被认款，不可重复认款');
        }
        //获取金额相同并且打款名相同的收款单数据
        $receipt = $this->getReceiptData($data['money'], $data['name']);
        //获取客户数据
        $channel_id = $data['channel_id'];
        $channel = (new ChannelModel())
            ->field('channel_id, name, label')
            ->where(compact('channel_id'))
            ->find();
        return compact('data', 'channel', 'receipt');
    }
    /**
     * 认款执行
     *
     * @access public
     *
     * @return void
     **/
    public function run_admit()
    {
        $receipt_serial = I('post.receipt_serial');
        $id = I('post.id');
        $paymentModel = new PaymentModel();
        //验证receipt的状态
        $delete_time = 0;
        $receipt = $paymentModel->field('status')->where(['pay_serial' => $receipt_serial, 'delete_time' => $delete_time])->select();
        if (empty($receipt) || $receipt[0]['status']==2) {
            throw new \Exception('该条打款单不符合要求，请重试');
        }
        //验证打款单数据
        $remit = $this
            ->model
            ->where(compact('id'))
            //->where(DataAuthController::instance()->getRemiteWhere())
            ->field('status')
            ->find();
        if (empty($remit)) {
            throw new \Exception('该数据不存在或不具备该客户的数据权限');
        }
        if ($remit['status']==2) {
            throw new \Exception('该打款单已被驳回，不可认款');
        } elseif ($remit['status']==3) {
            throw new \Exception('该打款单已被认款，不可重复认款');
        }
        //设置多表事务
        $transtionModel = M();
        $transtionModel->startTrans();
        $res1 = $this->model->where(compact('id'))->save([
            'receipt_serial'    => $receipt_serial,
            'status'            => 3
        ]);
        $res2 = $paymentModel->where(['pay_serial' => $receipt_serial])->save([
            'status'    => 2
        ]);
        $this->log($id, 3, $this->admin['username'], '<h4>认款明细</h4>打款流水号：' . $receipt_serial);
        if ($res1 && $res2) {
            $transtionModel->commit();
            $this->cleanBalanceSign($id);
            return;
        } else {
            $transtionModel->rollback();
            throw new \Exception('数据保存失败');
        }
    }
    /**
     * 根据打款单金额获取同等金额的收款单数据
     *
     * @access private
     * @param $money float 金额
     * @param $name string 打款方名称
     *
     * @return array
     **/
    private function getReceiptData($money, $name)
    {
        $model = new PaymentModel();
        $where = [
            'money' => $money,
            'name'  => $name,
            'delete_time'   => 0,
            'status'    => ['neq', 2]
        ];
        return $model->where($where, 'pay_serial')->order('create_time desc')->select();
    }
    /**
     * 驳回打款单
     *
     * @access public
     *
     * @return boolean
     **/
    public function run_reject()
    {
        $remark = I('post.remark');
        $id = I('post.id');
        //验证打款单数据
        $remit = $this
            ->model
            ->field('status')
            ->where(DataAuthController::instance()->getRemiteWhere())
            ->where(compact('id'))
            ->find();
        if (empty($remit)) {
            throw new \Exception('该数据不存在或不具备该客户的数据权限');
        }
        if ($remit['status']==2) {
            throw new \Exception('该打款单已被驳回，不可认款');
        } elseif ($remit['status']==3) {
            throw new \Exception('该打款单已被认款，不可重复认款');
        }

        $status = 2;
        $res = $this->model->where(compact('id'))->save(compact('remark', 'status'));
        if ($res) {
            $this->log($id, 2, $this->admin['username'], $remark);
            return true;
        }
        return false;
    }
    /**
     * 认款后通知back-api清除余额预警的标记
     *
     * @access protected
     * @param $id string 认款数据ID
     *
     * @return void
     **/
    protected function cleanBalanceSign($id)
    {
        $data = $this->model->where(compact('id'))->find();
        $channel_id = $data['channel_id'];
        $url = str_replace('{channel_id}', $channel_id, C('LIST_API_URL.backend_clean_balance_sign'));
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt ($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt ($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt ($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($curl, CURLOPT_POSTFIELDS,[
            'key'   => '5168b337cb7cdc2cd11675d634719ee9'
        ]);
        @curl_exec($curl);
    }
}