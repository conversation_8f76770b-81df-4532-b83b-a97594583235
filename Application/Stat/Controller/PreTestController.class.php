<?php

namespace Stat\Controller;

use Common\Controller\AdminController;

/**
 * 售前测试申请列表
 * @uses PreTestController
 */
class PreTestController extends AdminController
{
    /**
     * 测试明细
     * @return void
     */
    public function index(){
        $this->assign('apply_start_time', I('get.apply_start_time'));
        $this->assign('apply_end_time', I('get.apply_end_time'));
        $this->assign('status', I('get.status'));
        $this->assign('salesman', I('get.salesman'));
        $this->assign('have_objective', I('get.have_objective'));
        $this->assign('test_result', I('get.test_result'));
        $this->assign('is_call', I('get.is_call'));
        $this->assign('access_action', I('get.access_action'));
        $this->assign('father_id', I('get.father_id'));
        $this->assign('sys_father_id', I('get.sys_father_id'));
        $this->assign('data_source', I('get.data_source'));
        $this->assign('focus_product', I('get.focus_product'));
        $this->assign('focus_call', I('get.focus_call'));
        $this->assign('is_gather', (int)I('get.is_gather', 1));
        $this->assign('sort_by', I('get.sort_by'));
        $this->assign('sort', I('get.sort', 1));
        $this->assign('purpose', I('get.purpose'));

        $this->display();
    }

    /**
     * 测试明细v1
     * @return void
     */
    public function index_v1(){
        $this->display();
    }


    /**
     * h5明细
     * @return void
     */
    public function h5(){
        $this->assign('status', I('get.status'));
        $this->assign('company_short_name', I('get.company_short_name'));
        $this->assign('sort', I('get.sort'));
        $this->display();
    }

    /**
     * h5-反馈信息
     * @return void
     */
    public function h5_feedback(){
        $this->assign('apply_id', I('get.apply_id'));
        $this->assign('status', I('get.status'));
        $this->assign('company_short_name', I('get.company_short_name'));
        $this->assign('sort', I('get.sort'));
        $this->display();
    }

    /**
     * h5-接入信息
     * @return void
     */
    public function h5_access(){
        $this->assign('apply_id', I('get.apply_id'));
        $this->assign('status', I('get.status'));
        $this->assign('company_short_name', I('get.company_short_name'));
        $this->assign('sort', I('get.sort'));
        $this->display();
    }

    /**
     * PC端-数据统计
     * @return void
     */
    public function statistics(){
        $this->display();
    }

    /**
     * PC端-部门统计
     * @return void
     */
    public function statistics_v1(){
        $this->display();
    }

    /**
     * PC端-销售统计
     * @return void
     */
    public function sales_statistics(){
        $this->display();
    }

    /**
     * PC端-风险统计
     * @return void
     */
    public function risk(){
        $this->display();
    }

    /**
     * PC端-日志列表
     * @return void
     */
    public function log(){
        $this->display();
    }

    /**
     * PC端-申请信息详情
     * @return void
     */
    public function apply_info(){
        $this->assign('apply_id', I('get.apply_id'));
        $this->display();
    }

    /**
     * PC端-申请信息详情
     * @return void
     */
    public function runScore(){
        $this->display();
    }


    /**
     * PC端-申请信息详情
     * @return void
     */
    public function fileUpload(){
        $this->display();
    }

    /**
     * PC端-操作统计列表
     * @return void
     */
    public function action_record(){
        $this->display();
    }
}