<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\BmPeiSingleRepository;

class BmPeiSingleController extends AdminController
{
    public function index()
    {
        $repository = new BmPeiSingleRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new BmPeiSingleRepository();
        if (IS_GET) {
            $data = $repository->detail();
            $this->assign($data);
            $this->display();
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new BmPeiSingleRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}