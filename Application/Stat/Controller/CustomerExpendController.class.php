<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Common\Model\CommonEnumModel;

class CustomerExpendController extends AdminController
{

    public function index(){
        $_source_map = (new CommonEnumModel())->getEnumPairs('1');

        $source_map = [];
        foreach($_source_map as $k => $v){
            $source_map[] = [
                "value" => strval($k),
                "label" => $v,
            ];
        }
        $this->assign('source_map', $source_map);

        $this->display();
    }
}
