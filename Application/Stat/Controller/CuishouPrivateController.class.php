<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/8/6 0006
 * Time: 14:39
 */

namespace Stat\Controller;


use Common\Controller\AdminController;
use Stat\Repositories\CuishouPrivateRepository;

class CuishouPrivateController extends AdminController
{
    public function index()
    {
        $repository = new CuishouPrivateRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new CuishouPrivateRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new CuishouPrivateRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}