<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\BmPeiBatchRepository;

/*邦秒配详单版统计*/
class BmPeiBatchController extends AdminController
{
    public function index()
    {
        $repository = new BmPeiBatchRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new BmPeiBatchRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new BmPeiBatchRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}