<?php

namespace Stat\Controller;

use Account\Model\CustomerModel;
use Common\Controller\AdminController;
use Home\Model\UpstreamChannelModel;
use Home\Model\ChannelModel;
use Opdata\Model\AccountModel;
use Stat\Model\ProductModel;
use Stat\Model\UpstreamBillAdjust;
use Stat\Model\ChannelAccountAdjust;
use Common\Model\CommonEnumModel;
use Home\Repositories\CostAdjustRepository;

//成本统计量调整
class CostAdjustController extends AdminController
{
	protected $model;
	
	public function __construct()
	{
		parent::__construct();
		
		$this->repo = new CostAdjustRepository();
		$this->model = new ChannelAccountAdjust();
	}
	
	public function index()
	{
		$input = I('get.');// 回填数据
		$where = $this->repo->getParamByGet();
        $productModel = new ProductModel();
        $pids = $productModel->getGeneralProduct();
        $pids = array_column($pids, 'product_id');
        $productInfo    = $productModel->field('product_id,product_name')
            ->where([
                'product_id' => ['in', $pids],
                '_logic'     => 'OR',
                'father_id'  => ['gt', 0],
            ])
            ->select();

		$productInfo    = array_column($productInfo, 'product_name', 'product_id');
		$productOptions = makeOption($productInfo, I('get.product_id'));
		
		$customerInfo    = (new CustomerModel())->field('name,customer_id')
												->select();
		$customerInfo    = array_column($customerInfo, 'name', 'customer_id');
		$customerOptions = makeOption($customerInfo, I('get.customer_id'));

        $channelInfo    = (new ChannelModel())->field('label, channel_id')
            ->select();
        $channelInfo    = array_column($channelInfo, 'label', 'channel_id');
        $channelOptions = makeOption($channelInfo, I('get.channel'));
		
		$sourceOptions   = makeOption(array('-1'=>'全部来源')+ (new CommonEnumModel())->getEnumPairs(1),I('get.source','-1'));
		//获取查询条件
		$list = $this->model->field([
			'channel_account_adjust.id',
			'channel_account_adjust.customer_id',
			'account.account_name',
            'channel_interface.label as interface_name',
			'channel_account_adjust.product_id',
			//'channel_account_adjust.channel',
            'channel_account_adjust.channel_id',
            'channel_account_adjust.encrypt',
			'channel_account_adjust.date',
			'channel_account_adjust.title',
			'channel_account_adjust.money',
			'channel_account_adjust.fee_number',
			'channel_account_adjust.source',
			'channel_account_adjust.create_time',
			'channel_account_adjust.update_time',
		])
							->join('account ON account.account_id = channel_account_adjust.account_id')
                            ->join('left join channel_interface ON channel_interface.id = channel_account_adjust.interface_id')
							->where($where)
							->order('channel_account_adjust.id DESC')
							->select();
			$sourcePairs = (new CommonEnumModel())->getEnumPairs(1);
			array_walk($list, function (&$item) use ($sourcePairs) {
				$item['source_name'] = isset($sourcePairs[$item['source']])?$sourcePairs[$item['source']]:'--';
                if($item['update_time'] ==0) {
                    $item['update_time'] = $item['create_time'];
                }
				$item['create_time'] = date("Y-m-d H:i:s",$item['create_time']);
				$item['update_time'] = date("Y-m-d H:i:s",$item['update_time']);
			});
		//费用总计
		$total_money = array_sum(array_column($list,'money'));
		$total_num = count($list);
		$this->assign(compact('list', 'channelInfo', 'channelOptions', 'customerInfo', 'customerOptions', 'productInfo', 'productOptions', 'input','total_money','total_num','sourceOptions'));
		$this->display();
	}

    public function add()
    {
        header('Content-type: text/html; charset=UTF8');

        $productModel = new ProductModel();
        $pids = $productModel->getGeneralProduct();
        $pids = array_column($pids, 'product_id');
        $productInfo = $productModel->field('product_id,product_name')
            ->where([
                'product_id' => ['in', $pids],
                '_logic' => 'OR',
                'father_id' => ['gt', 0],
            ])
            ->select();


        $productInfo = array_column($productInfo, 'product_name', 'product_id');
        $productOptions = makeOption($productInfo, I('get.product_id'));

        $accountInfo = (new AccountModel())->field([
            'customer.name' => 'customer_name',
            'customer.customer_id' => 'customer_id',
            'account.account_id' => 'account_id',
            'account.account_name' => 'account_name',
        ])
            ->join('customer ON customer.customer_id = account.customer_id')
            ->where([
                'account.father_id' => ['NEQ', '0'],
                'account.is_delete' => 0,
                'customer.is_delete' => 0,
            ])
            ->select();

        $accountInfo = array_column(array_map(function ($item) {
            $id = $item['customer_id'] . '_' . $item['account_id'];
            $name = '[' . $item['customer_name'] . ']_' . $item['account_name'];

            return compact('id', 'name');
        }, $accountInfo), 'name', 'id');
        $accountOptions = makeOption($accountInfo);

        $channelInfo = (new ChannelModel())->field('label, channel_id')->select();
        $channelInfo = array_column($channelInfo, 'label', 'channel_id');
        $channelOptions = makeOption($channelInfo, I('get.channel'));
        $sourceOptions = makeOption((new CommonEnumModel())->getEnumPairs(1));

        $this->assign(compact('accountOptions', 'productOptions', 'channelOptions', 'sourceOptions'));
        $this->display();

        return;

    }
	
	public function edit()
	{
		IF (IS_GET) {
			$id             = I('get.id');
			$data           = $this->model->find($id);

            $productModel = new ProductModel();
            $pids = $productModel->getGeneralProduct();
            $pids = array_column($pids, 'product_id');
            $productInfo    = $productModel->field('product_id,product_name')
                ->where([
                    'product_id' => ['in', $pids],
                    '_logic'     => 'OR',
                    'father_id'  => ['gt', 0],
                ])
                ->select();
			$productInfo    = array_column($productInfo, 'product_name', 'product_id');
			$productOptions = makeOption($productInfo, $data['product_id']);
			
			$accountInfo = (new AccountModel())->field([
				'customer.name'        => 'customer_name',
				'customer.customer_id' => 'customer_id',
				'account.account_id'   => 'account_id',
				'account.account_name' => 'account_name',
			])
											   ->join('customer ON customer.customer_id = account.customer_id')
											   ->where([
												   'account.father_id'  => ['NEQ', '0'],
												   'account.is_delete'  => 0,
												   'customer.is_delete' => 0,
											   ])
											   ->select();
			
			$accountInfo    = array_column(array_map(function ($item) {
				$id   = $item['customer_id'] . '_' . $item['account_id'];
				$name = '[' . $item['customer_name'] . ']_' . $item['account_name'];
				
				return compact('id', 'name');
			}, $accountInfo), 'name', 'id');
			$accountOptions = makeOption($accountInfo, $data['customer_id'] . '_' . $data['account_id']);

            $channelInfo    = (new ChannelModel())->field('label, channel_id')->select();
            $channelInfo    = array_column($channelInfo, 'label', 'channel_id');
			$channelOptions = makeOption($channelInfo, $data['channel_id']);
			$sourceOptions   = makeOption((new CommonEnumModel())->getEnumPairs(1),$data['source']);

			$this->assign(compact('accountOptions', 'productOptions', 'channelOptions', 'sourceOptions', 'data'));
			$this->assign('channel_id', $data['channel_id']);
            $this->assign('interface_id', $data['interface_id']);
            $this->assign('encrypt', $data['encrypt']);
            $this->assign('operator', $data['operator']);
			$this->display();
			
			return;
		}
		
		try {
			$account_id = I('post.account_id');
			list($customer_id, $account_id) = explode('_', $account_id);
			
			$apikey = (new AccountModel())->field('apikey')
										  ->where(['account_id' => $account_id])
										  ->find()['apikey'];
			
			$date       = I('post.date');
			$product_id = I('post.product_id');
            $channel_id     = I('post.channel');//channel_id
            $interface_id     = I('post.interface');//interface_id
            $encrypt  = I('post.encrypt');
            $operator  = I('post.operator');
			$money       = I('post.money');
			$fee_number  = I('post.fee_number');
			$id         = I('post.id');
			$title      = I('post.title');
			$remark     = I('post.remark');
			$source      = I('post.source');
			$is_rerun_month_data = 0;
			$update_time      = time();

			$this->model->where(['id' => $id])
						->save(compact('remark', 'title', 'account_id', 'customer_id', 'apikey', 'date', 'channel_id', 'product_id', 'interface_id', 'encrypt', 'operator', 'money', 'fee_number', 'source', 'is_rerun_month_data', 'update_time'));
			$this->ajaxResponse(['status' => 'success',]);
		} catch (\Exception $exception) {
			$info = $exception->getMessage();
			$this->ajaxResponse(['status' => 'error', 'info' => $info]);
		}
	}
	
	public function del()
	{
		$id = I('get.id');
		
		$this->model->where(compact('id'))
					->delete();
		
		$this->redirect('index');
	}


	public function customer(){
        $_source_map = (new CommonEnumModel())->getEnumPairs('1');

        $source_map = [];
        foreach($_source_map as $k => $v){
            $source_map[] = [
                "value" => intval($k),
                "label" => $v,
            ];
        }
        $this->assign('source_map', $source_map);

        $this->display();
    }

	public function spreadrecord(){
		$this->display();
	}
    /**
     * 查询数据的批量导出
     *
     * @access public
     *
     * @return void
     **/
    public function file_out()
    {

        //获取get参数
        $this->repo->file_out();
        die;

    }
	

}