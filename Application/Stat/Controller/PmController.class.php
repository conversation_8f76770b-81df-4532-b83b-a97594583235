<?php

namespace Stat\Controller;

use Account\Model\CustomerModel;
use Common\Controller\AdminController;
use Common\Controller\DataAuthController;

class PmController extends AdminController
{

    /** 主产品数据 */
    public function main() {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    /** 客户维度 */
    public function customer() {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    /** 客户维度 */
    public function product() {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    /** 渠道维度 */
    public function channel() {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    /** 客户维度-详情 */
    public function customer_compare() {
        //近一周时间段
        // $last_week = $this->lastWeek(5);
        // $this->assign('last_week', $last_week);
        $this->display();
    }

    /** 产品维度-详情 */
    public function product_compare() {
        //近一周时间段
        // $last_week = $this->lastWeek(5);
        // $this->assign('last_week', $last_week);
        $this->display();
    }


    /**
     * 获取近一周时间段（以周五为基准）
     */
    private function lastWeek($benchmark)
    {
        $map = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
        ];
        $last_week = ['start_date' => date('Y-m-d'), 'end_date' => date('Y-m-d')];
        if (!isset($map[$benchmark])) {
            return $last_week;
        }
        $weekNum = date('w');
        $last_week['start_date'] = date('Y-m-d',strtotime('last ' . $map[$benchmark]));
        if ($weekNum == $benchmark) {
            $last_week['end_date'] = date('Y-m-d', strtotime('-1 days'));
        }
        return $last_week;
    }
}