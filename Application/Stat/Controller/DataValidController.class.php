<?php
namespace Stat\Controller;

//邦秒验统计
use Common\Controller\AdminController;
use Stat\Repositories\DataValidRepository;

class DataValidController extends AdminController
{
    public function index()
    {
        $repository = new DataValidRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->assign('product_option', $repository->getProductOption($data['input']['product_id']));
        $this->assign('telecom_option', $repository->getTelecomOption($data['input']['telecom']));
        $this->display();
    }
    public function detail()
    {
        $repository = new DataValidRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->assign('product_option', $repository->getProductOption($data['input']['product_id']));
                $this->assign('telecom_option', $repository->getTelecomOption($data['input']['telecom']));
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new DataValidRepository();
            $type = I('get.type', '', 'trim');
            if ($type=='product_detail') {
                $repository->exportForProduct();
            } else {
                $repository->file();
            }
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}