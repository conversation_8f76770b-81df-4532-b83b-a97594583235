<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\BmPeiBillRepository;

/*邦秒配详单版统计*/
class BmPeiBillController extends AdminController
{
    public function index()
    {
        $repository = new BmPeiBillRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new BmPeiBillRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new BmPeiBillRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}