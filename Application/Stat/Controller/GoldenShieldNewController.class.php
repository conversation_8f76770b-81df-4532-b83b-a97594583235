<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\GoldenShieldNewRepository;
use Stat\Repositories\GoldenShieldRepository;

//金盾相关产品统计
class GoldenShieldNewController extends AdminController
{
    public function index()
    {
        $repository = new GoldenShieldNewRepository();
        $data       = $repository->index();
        $data['product_option'] = $repository->getChildrenProductOptions();
        $this->assign($data);
        $this->display();
    }

    public function detail()
    {
        $repository = new GoldenShieldNewRepository();
        try {
            $data = $repository->detail();
            $data['product_option'] = $repository->getChildrenProductOptions();
            $this->assign($data);
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }

    }

    public function file()
    {
        try {
            $repository = new GoldenShieldNewRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }

    public function product()
    {
        try {
            $repository = new GoldenShieldNewRepository();
            $data = $repository->productStatistic();
            //halt($data['data_item']);
            $this->assign($data);
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }

    public function response_type()
    {
        $repository = new GoldenShieldNewRepository();
        $option = $repository->getResponseOptions();
        $this->ajaxReturn(compact('option'));
    }
}