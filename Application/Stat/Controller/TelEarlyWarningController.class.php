<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/13 0013
 * Time: 11:22
 */

namespace Stat\Controller;


use Common\Controller\AdminController;
use Stat\Repositories\TelEarlyWarningRepository;

class TelEarlyWarningController extends AdminController
{
    public function index()
    {
        $repository = new TelEarlyWarningRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new TelEarlyWarningRepository();
        try {
            $data = $repository->detail();
            $this->assign($data);
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }

    }
    public function file()
    {
        try {
            $repository = new TelEarlyWarningRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}