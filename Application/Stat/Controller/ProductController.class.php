<?php

namespace Stat\Controller;

use Account\Model\CustomerModel;
use Common\Controller\AdminController;
use Common\Controller\DataAuthController;

class ProductController extends AdminController
{
    public function main()
    {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }
    public function mainCustomer(){
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $qifuCustomerIds = (new CustomerModel())->field('customer_id')->where(['customer_type' => CustomerModel::$customerType['企服用户']])->select();
        $qifuCustomerIds = $qifuCustomerIds ? array_column($qifuCustomerIds, 'customer_id') : [];
        $allCustomerIds = (new CustomerModel())->field('customer_id, salesman')->select();
        $allCustomerIds = array_column($allCustomerIds, 'salesman' ,'customer_id');
        $this->assign('qifuCustomerIds', json_encode($qifuCustomerIds));
        $this->assign('allCustomerIds', json_encode($allCustomerIds));
        $this->display();
    }
    public function mainChannel(){
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }
    public function product()
    {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    public function channel()
    {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    public function channelCompare()
    {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    public function customer()
    {
        //近一周时间段
        $last_week = $this->lastWeek(5);
        $this->assign('last_week', $last_week);
        $this->display();
    }

    public function compare(){
        $this->display();
    }
    public function customerCompare(){
        $this->display();
    }
    public function productCompare(){
        $this->display();
    }
    //客户统计趋势图
    public function customerChartCompare(){
        $this->display();
    }

    //产品统计趋势图
    public function productChartCompare(){
        $this->display();
    }

    //客户产品首次收入
    public function customerProductFirstIncome(){
        $this->display();
    }

    //账期列表
    public function peroidList(){
        $this->display();
    }


    public function chart()
    {
        $customer = (new CustomerModel())->field('name, customer_id')
            ->where(DataAuthController::instance()->getRemiteWhere())->select();

        $father_id = I('get.father_id', '', 'trim');
        $this->assign('father_id', $father_id);
        $this->assign('customer', json_encode($customer));
        $this->display();
    }

    public function productStat()
    {
        $params = I('get.');
        $this->assign('params', $params);
        $this->display();
    }

    public function customerStat()
    {
        $params = I('get.');
        $this->assign('params', $params);
        $this->display();
    }

    public function channelIncomeStat()
    {
        $params = I('get.');
        $this->assign('params', $params);
        $this->display();
    }

    /**
     * 获取近一周时间段（以周五为基准）
     */
    private function lastWeek($benchmark)
    {
        $map = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
        ];
        $last_week = ['start_date' => date('Y-m-d'), 'end_date' => date('Y-m-d')];
        if (!isset($map[$benchmark])) {
            return $last_week;
        }
        $weekNum = date('w');
        $last_week['start_date'] = date('Y-m-d',strtotime('last ' . $map[$benchmark]));
        if ($weekNum == $benchmark) {
            $last_week['end_date'] = date('Y-m-d', strtotime('-1 days'));
        }
        return $last_week;
    }

    //在网时长状态复核
    public function netTimeRecheck(){
        $this->display();
    }

    public function customerCost(){
        $params['start_date'] = strtotime(date("Y-m-01 00:00:00",strtotime("-1 month"))) * 1000;
        // $params['end_date'] = strtotime(date("Y-m-d 00:00:00")) * 1000;
        $this->assign('params', $params);
        $this->display();
    }
}