<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\BxfPeriodStatRepository;

class BxfPeriodStatController extends AdminController
{
    private $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new BxfPeriodStatRepository();
    }

    //列表页
    public function stat()
    {
        $data = $this->repository->getListData();
        $this->assign($data);
        $this->display();
    }
}