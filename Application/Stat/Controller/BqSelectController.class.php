<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\BqSelectRepository;

class BqSelectController extends AdminController
{
    public function index()
    {
        $repository = new BqSelectRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new BqSelectRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new BqSelectRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}