<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\CuishouBillV2Repository;

class CuishouBillV2Controller extends AdminController
{
    public function index()
    {
        $repository = new CuishouBillV2Repository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new CuishouBillV2Repository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->assign('chart', $repository->getCharts($data['data']));
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new CuishouBillV2Repository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}