<?php

namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\GoldenShieldRepository;

//金盾相关产品统计
class GoldenShieldController extends AdminController
{
    public function index()
    {
        $repository = new GoldenShieldRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new GoldenShieldRepository();
        try {
            $data = $repository->detail();
            $this->assign($data);
            $this->display();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }

    }
    public function file()
    {
        try {
            $repository = new GoldenShieldRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}