<?php
namespace Stat\Controller;

use Common\Controller\AdminController;
use Stat\Repositories\BmPaRepository;

class BmPaController extends AdminController
{
    public function index()
    {
        $repository = new BmPaRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function detail()
    {
        $repository = new BmPaRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }

        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new BmPaRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}