<?php

namespace Stat\Controller;


use Common\Controller\AdminController;
use Stat\Repositories\CuishouShortRepository;

class CuishouShortController extends AdminController
{
    public function index()
    {
        $repository = new CuishouShortRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->assign('telecom_option', $repository->getTelecomOption($data['input']['telecom']));
        $this->display();
        printBreakPoint();
    }
    public function detail()
    {
        $repository = new CuishouShortRepository();
        if (IS_GET) {
            try {
                $data = $repository->detail();
                $this->assign($data);
                $this->assign('telecom_option', $repository->getTelecomOption($data['input']['telecom']));
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        } else {
            $customer_id = I('post.customer_id');
            $account_option = $repository->getStoreAccountOptionByCustomerId($customer_id);
            $this->ajaxReturn(compact('account_option'));
        }
    }
    public function file()
    {
        try {
            $repository = new CuishouShortRepository();
            $repository->file();
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}