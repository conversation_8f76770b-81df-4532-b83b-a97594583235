<?php

namespace Stat\Model;

use Think\Model;
use Common\ORG\Page;

class ProductModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'product';
    protected $tablePrefix = '';

    /**
     * 获取一个单元
     * @param array $where
     * @param string $field
     * @return mixed
     */
    public static function getOneItem(array $where, $field='*')
    {
        return (new static())->where($where)
            ->field($field)
            ->find();
    }

    /**
     * 获取列表
     * @param array $where
     * @param string $field
     * @return mixed
     */
    public static function getListProduct(array $where = [], $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 创建产品
     * @return mixed
     * @throws \Exception
     */
    public function createProduct()
    {
        // 检查参数
        $this->checkParamsByCreateForPost();

        // 获取参数
        $params = $this->getParamForCreate();
        return $this->add($params);
    }

    /**
     * 为创建产品过滤参数
     */
    protected function getParamForCreate()
    {
        $product_id = I('post.product_id', '', 'trim');
        $product_name = I('post.product_name', '', 'trim');
        $push_url = I('post.push_url', '', 'trim');
        $product_key = I('post.product_key', '', 'trim');
        $data = I('post.data', '', 'trim');
        $mark = I('post.mark', '', 'trim');
        $stat_config = I('post.stat_config', '', 'trim');
        $fee_config = I('post.fee_config', '', 'trim');

        $create_at = $update_at = time();

        return compact('product_id', 'product_name', 'push_url', 'product_key', 'data', 'update_at', 'create_at', 'mark', 'stat_config', 'fee_config');
    }

    /**
     * 为修改产品过滤参数
     */
    protected function getParamForUpdate()
    {
        $product_id = I('post.product_id', '', 'trim');
        $product_name = I('post.product_name', '', 'trim');
        $push_url = I('post.push_url', '', 'trim');
        $product_key = I('post.product_key', '', 'trim');
        $data = I('post.data', '', 'trim');
        $mark = I('post.mark', '', 'trim');
        $stat_config = I('post.stat_config', '', 'trim');
        $fee_config = I('post.fee_config', '', 'trim');
        $update_at = time();

        return compact('product_id', 'product_name', 'push_url', 'product_key', 'data', 'update_at', 'mark', 'stat_config', 'fee_config');
    }

    /**
     * 修改产品操作人
     * @param $id
     * @param $admin
     * @return bool|null
     */
    public function updateAdminInfo($id, $admin)
    {
        if (empty($id) || empty($admin)) {
            return null;
        }
        $data['admin'] = $admin;
        return $this->where('id=' . $id)->save($data);
    }

    /**
     * post方式通过id修改产品
     * @throws \Exception
     */
    public function updateProductById()
    {
        $id = I('post.id', '', 'trim');
        // 检查参数
        $this->checkParamsByUpdateForPost();

        // 更新产品
        $params_account = $this->getParamForUpdate();
        return $this->where(compact('id'))->save($params_account);
    }

    /**
     * 检查更新传递的name(id)是否是唯一的
     * @throws \Exception
     */
    protected function checkParamsByUpdateForPost()
    {
        $id = I('post.id', '', 'trim');

        // product_id的唯一性
        $product_id = I('post.product_id', '', 'trim');
        if (!$product_id) {
            throw new \Exception('产品编号未填写');
        }
        $product_id_unique = $this->where(compact('product_id'))->find();
        if ($product_id_unique && $product_id_unique['id'] != $id) {
            throw new \Exception('产品编号已经被占用');
        }

        // product_name的唯一性
        $product_name = I('post.product_name', '', 'trim');
        if (!$product_name) {
            throw new \Exception('产品名称未填写');
        }
        //暂时不要求必填
//        $push_url = I('post.push_url', '', 'trim');
//        if (!$push_url) {
//            throw new \Exception('推送地址未填写');
//        }
//        $product_key = I('post.product_key', '', 'trim');
//        if (!$product_key) {
//            throw new \Exception('推送key未填写');
//        }
        $product_name_unique = $this->where(compact('product_name'))->find();
        if ($product_name_unique && $product_name_unique['id'] != $id) {
            throw new \Exception('产品名称已经被占用');
        }

        $data = I('post.data', '', 'trim');
        $is_json = json_decode($data, true);
        if (!is_array($is_json)) {
            throw new \Exception('产品配置参数格式必须JSON字符串');
        }
        $stat_config = I('post.stat_config', '', 'trim');
        $is_json = empty($stat_config) ? '' : json_decode($stat_config, true);
        if ($stat_config && !is_array($is_json)) {
            throw new \Exception('统计配置参数格式必须JSON字符串');
        }
        $fee_config = I('post.fee_config', '', 'trim');
        $is_json = empty($fee_config) ? '' : json_decode($fee_config, true);
        if ($fee_config && !is_array($is_json)) {
            throw new \Exception('计费配置参数格式必须JSON字符串');
        }
    }

    /**
     * 检查新建客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByCreateForPost()
    {
        $product_id = I('post.product_id', '', 'trim');
        $product_name = I('post.product_name', '', 'trim');
        $data = I('post.data', '', 'trim');
        $fee_config = I('post.fee_config', '', 'trim');
        $stat_config = I('post.stat_config', '', 'trim');

        if (!$product_id) {
            throw new \Exception('产品编号未填写');
        }
        if (!$product_name) {
            throw new \Exception('产品名称未填写');
        }
        // product_id 的唯一性
        $id_unique = $this->where(compact('product_id'))->count();
        if ($id_unique) {
            throw new \Exception('产品编号已经被占用');
        }

        //暂时不要求必填
//        $push_url = I('post.push_url', '', 'trim');
//        if (!$push_url) {
//            throw new \Exception('推送地址未填写');
//        }
//        $product_key = I('post.product_key', '', 'trim');
//        if (!$product_key) {
//            throw new \Exception('推送key未填写');
//        }

        // product_name 的唯一性
        $name_unique = $this->where(compact('product_name'))->count();
        if ($name_unique) {
            throw new \Exception('产品名称已经被占用');
        }


        $is_json = json_decode($data, true);
        if (!is_array($is_json)) {
            throw new \Exception('产品配置参数格式必须JSON字符串');
        }

        $is_json = empty($stat_config) ? '' : json_decode($stat_config, true);
        if ($stat_config && !is_array($is_json)) {
            throw new \Exception('统计配置参数格式必须JSON字符串');
        }
        $is_json = empty($fee_config) ? '' : json_decode($fee_config, true);
        if ($fee_config && !is_array($is_json)) {
            throw new \Exception('计费配置参数格式必须JSON字符串');
        }

    }

    /**
     * 获取产品信息
     * @return mixed
     */
    public function getProductInfoById()
    {
        $id = I('get.id', '', 'trim');
        return $this->where('id=' . $id)->find();
    }

    /**
     * 获取产品信息
     * @return mixed
     */
    public function getProductInfo($product_id)
    {
        return $this->where('product_id=' . $product_id)->find();
    }

    /**
     * GET方式获取产品列表
     * @return array
     */
    public function getProductList()
    {
        // 条件
        $where = $this->getConditionByGet();

        // 分页
        $count_total = $this->where($where)->count();
        $page = new Page($count_total, C('LIST_ROWS'));

        // 产品列表
        $list_product = $this
            ->where($where)
            ->limit($obj_page->firstRow, $obj_page->listRows)
            ->order('id DESC')
            ->select();

        return compact('page', 'list_product');
    }

    /**
     * GET方法获取客户列表的条件
     * @return array
     */
    protected function getConditionByGet()
    {
        $product_name = I('get.product_name', '', 'trim');
        if (!empty($product_name)) {
            $product_name = ['like', '%' . $product_name . '%'];
        }

        $where = compact('product_name');
        return array_filter($where, function ($item) {
            return $item !== '';
        });
    }

    /**
     * 根据条件返回相应的产品列表
     * @param $where
     * @param string $field
     * @return mixed
     */
    public function getProductListByWhere($where, $field = 'id,product_id,product_name,data')
    {
        $where = array_merge([
            'back_status' => 1
        ], $where);
        return $this->where($where)->field($field)->select();
    }


    /**
     * 获取所有的普通产品（没有子产品的产品）
     * @return mixed
     */
    public function getGeneralProduct() {
        $model = new self();
        $sql = "SELECT
                    product_id 
                FROM
                    product 
                WHERE
                    ( father_id = 0 AND ( SELECT count( * ) FROM product p WHERE p.father_id = product.product_id ) = 0 ) 
                    OR father_id = 401";
        $result = $model->query($sql);
        return $result;
    }




}