<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            height : 100px;
            width  : 100%;
        }
        .form_title {
            background    : #CCCCCC;
            padding       : 10px 10px;
            box-sizing    : border-box;
            font-size     : 16px;
            border-radius : 4px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回客户列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="{:U('add')}?callback_url={$Think.get.callback_url|urlencode}" method="post"
                              class="form-horizontal" id="form_account">
                            <h4 class="form_title" id="base">基本信息</h4>

                            <div class="form-group">
                                <label for="pre_sale_customer_id" class="control-label col-md-3">关联线下测试客户：</label>
                                <div class="col-md-4">
                                    <select name="pre_sale_customer_id" id="pre_sale_customer_id" class="form-control">
                                        <option value=""></option>
                                        <volist name="pre_sales_customer_data" id="vo">
                                            <option value="{$vo.ps_customer_id}">{$vo.ps_customer_name}</option>
                                        </volist>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="group_id" class="control-label col-md-3">主体：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="group_id" id="group_id">
                                        {$group_option}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="name" class="control-label col-md-3">客户名称：</label>
                                <div class="col-md-4">
                                    <input type="text" name="name" id="name" class="form-control" value="">
                                </div>
                            </div>

                            <?php foreach($source_list as $value => $name){?>
                            <div class="form-group">
                                <label for="company<?=$value?>" class="control-label col-md-3">公司名称-<?=$name?>：</label>
                                <div class="col-md-4">
                                    <input type="text" name="company[<?=$value?>]" id="company<?=$value?>" class="form-control" value="">
                                </div>
                            </div>
                            <?php } ?>

                            <div class="form-group">
                                <label for="status_radio1" class="control-label col-md-3">状态：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="status" id="status_radio1" value="1" checked>可用
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="status" id="status_radio2" value="0">禁用
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="payment_type_radio1" class="control-label col-md-3">付款类型：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="payment_type" id="payment_type_radio1" value="1" checked>预付费
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="payment_type" id="payment_type_radio2" value="2">后付费
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="channel_mode_radio1" class="control-label col-md-3">是否为渠道客户：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="channel_mode" id="channel_mode_radio1" value="1">是
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="channel_mode" id="channel_mode_radio2" value="2" checked>否
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="channel_follower" class="control-label col-md-3">客户分类：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="level" id="level_radio1" value="10">头部客户
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="level" id="level_radio2" value="50">重要客户
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="level" id="level_radio3" value="100" checked>一般客户
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="reconciliation_cycle" class="control-label col-md-3">对账周期：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="reconciliation_cycle" id="reconciliation_cycle">
                                        {$reconciliationCycleOption}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="operator" class="control-label col-md-3">运营跟进人：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="operator" id="operator">
                                        {$user_option}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="salesman" class="control-label col-md-3">商务跟进人：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="salesman" id="salesman">
                                        {$user_option}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="introduce_salesman" class="control-label col-md-3">商务推荐人：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="introduce_salesman" id="introduce_salesman">
                                        {$user_option}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="channel_follower" class="control-label col-md-3">渠道跟进人：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="channel_follower" id="channel_follower">
                                        {$channel_follower_option}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="status_radio1" class="control-label col-md-3">渠道跟进人可见数据：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="checkbox" name="source_id[]" id="source_id_radio" value="-1"  <?= in_array(-1,$source_id)? 'checked' : ''; ?> >全部
                                    </label>
                                    <?php foreach($source_list as $value => $label){?>
                                    <label class="radio-inline">
                                        <input type="checkbox" name="source_id[]" id="source_id_radio<?=$value?>" value="<?=$value?>"  <?= in_array($value,$source_id) ? 'checked' : ''; ?> ><?=$label?>
                                    </label>
                                    <?php } ?>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="sign_type" class="control-label col-md-3">征信客户分类：</label>
                                <div class="col-md-4">
                                    <?php foreach($signTypeOption as $value => $label){?>
                                    <label class="radio-inline">
                                        <input type="checkbox" name="sign_type[]" id="sign_type_radio<?=$value?>" value="<?=$value?>" ><?=$label?>
                                    </label>
                                    <?php } ?>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="customer_type" class="control-label col-md-3">客户类型：</label>
                                <div class="col-md-4">
                                    <select name="customer_type" id="customer_type" class="form-control">
                                        {$customerTypeOption}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="type" class="control-label col-md-3">公司类型：</label>
                                <div class="col-md-2">
                                    <select id="type" class="form-control">
                                        {$first_type_option}
                                    </select>
                                </div>
                                <div class="col-md-1"></div>
                                <div class="col-md-2">
                                    <select name="type" id="twice_type" class="form-control">
                                        {$twice_type_option}
                                    </select>
                                </div>
                            </div>

<!--                            <div class="form-group">-->
<!--                                <label class="col-sm-3 control-label">截止日期</label>-->
<!--                                <div class="col-sm-4">-->
<!--                                    <input type="date" class="form-control" name="end_time" id="end_time"-->
<!--                                           value="2099-12-31"/>-->
<!--                                </div>-->
<!--                            </div>-->

                            <div id="warning_info">
                            <h4 class="form_title" id="warning">预警信息</h4>

                            <div class="form-group">
                                <span class="glyphicon glyphicon-question-sign control-label col-md-1"
                                      data-trigger="hover"
                                      aria-hidden="true" style="color:#C9302C;" data-toggle="popover"
                                      title="预警余额" data-content="当客户余额小于等于预警余额时，将发送余额报警邮件；若不填写，则不发送对应预警邮件"></span>
                                <label for="balance" class="control-label col-md-2">
                                    预警余额：
                                </label>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="text" name="balance" id="balance" class="form-control"
                                               maxlength="10" aria-describedby="basic-addon2">
                                        <span class="input-group-addon" id="basic-addon2">元</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                            <span class="glyphicon glyphicon-question-sign control-label col-md-1" data-trigger="hover"
                                  aria-hidden="true" style="color:#C9302C;" data-toggle="popover"
                                  title="预警余额占比"
                                  data-content="当客户余额/客户最近一次充值金额*100%小于等于此值时，会发送余额报警邮件；若不填写，则按统一标准（30%，20%，10%）发送余额报警邮件"></span>
                                <label for="balance_percent" class="control-label col-md-2">
                                    预警余额占比：
                                </label>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="text" name="balance_percent" id="balance_percent"
                                               class="form-control" maxlength="5" aria-describedby="basic-addon3">
                                        <span class="input-group-addon" id="basic-addon3">%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                            <span class="glyphicon glyphicon-question-sign control-label col-md-1" data-trigger="hover"
                                  aria-hidden="true" style="color:#C9302C;" data-toggle="popover"
                                  title="预警剩余消耗天数"
                                  data-content="当客户余额预计剩余消耗天数小于等于此值时，会发送余额报警邮件；若不填写，则按统一标准（45天，30天，10天）发送余额报警邮件"></span>
                                <label for="available_days" class="control-label col-md-2">
                                    预警剩余消耗天数：
                                </label>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="number" name="available_days" id="available_days"
                                               class="form-control" maxlength="3" aria-describedby="basic-addon4">
                                        <span class="input-group-addon" id="basic-addon4">天</span>
                                    </div>
                                </div>
                            </div>
                            </div>
<!--                            <h4 class="form_title" id="bill">账单信息</h4>-->
<!--                            <div class="form-group">-->
<!--                                <label class="control-label col-md-3">账单特殊费用：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <span style="color:#C9302C;line-height:36px;font-size:14px;">请在表格中添加</span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="panel panel-default table-responsive" style="overflow-y: hidden">-->
<!--                                <table class="table table-hover table-striped table-bordered">-->
<!--                                    <thead class="center">-->
<!--                                    <tr>-->
<!--                                        <th>类型</th>-->
<!--                                        <th>日期</th>-->
<!--                                        <th>标题</th>-->
<!--                                        <th>金额</th>-->
<!--                                        <th>操作</th>-->
<!--                                    </tr>-->
<!--                                    </thead>-->
<!--                                    <tbody id="expend_tbody"></tbody>-->
<!--                                </table>-->
<!--                            </div>-->

<!--                            <div class="form-group" style="margin: 0 auto;">-->
<!--                                <button type="button" class="btn btn-info btn-sm" id="add_expend" style="float: right">-->
<!--                                    新增-->
<!--                                </button>-->
<!--                            </div>-->

<!--                            <div class="form-group">-->
<!--                                <label class="control-label col-md-3">账单发送频率：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="0">人工发送-->
<!--                                    </label>-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="1" checked>每月第1天-->
<!--                                    </label>-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="2">每季度第1天-->
<!--                                    </label>-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="3">每年第1天-->
<!--                                    </label>-->
<!--                                </div>-->
<!--                            </div>-->

                            <div class="form-group">
                                <label class="control-label col-md-3">账单发送邮件类型：</label>
                                <div class="col-sm-8">
                                    <label class="radio-inline">
                                        <input type="radio" name="email_type" value="1">标准
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="email_type" value="2" checked>非标准
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="bill_email" class="control-label col-md-3">账单收件人：</label>
                                <div class="col-md-4">
                                    <textarea name="bill_email" class="form-control" id="bill_email" rows="10"
                                              placeholder="每行一个邮箱地址，请输入符合邮箱规范的邮箱地址"></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="bill_cc_email" class="control-label col-md-3">账单抄送人：</label>
                                <div class="col-md-4">
                                    <textarea name="bill_cc_email" class="form-control" id="bill_cc_email" rows="10"
                                              placeholder="每行一个邮箱地址，请输入符合邮箱规范的邮箱地址"></textarea>
                                </div>
                            </div>


                            <div class="pull-right">
                                <input type="submit" class="btn btn-primary btn-sm" value="添加">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    new Vue({
        el : '#account_app'
    });
    //绑定特殊消耗事件
    function bindExpend() {
        $("#add_expend").click(function () {
            var len     = $(".expend_tr").length;
            var index   = (len > 0) ? (Number($(".expend_tr:last").attr('data-index')) + 1) : 0;
            var display = '<tr class="expend_tr" data-index="' + index + '">' +
                    '<td><select name="expend[' + index + '][type]" class="form-control expend_type">' +
                    '<option value="1">赠送</option>' +
                    '<option value="2" selected>消耗</option>' +
                '</select></td>' +
                '<td><input type="month" name="expend[' + index + '][date]" class="form-control expend_date" style="min-width: 180px;" placeholder="日期"></td> ' +
                '<td><input type="text" name="expend[' + index + '][title]" maxlength="15" class="form-control expend_title" style="min-width: 180px;" placeholder="标题"></td> ' +
                '<td><input type="text" name="expend[' + index + '][money]" maxlength="10" class="form-control expend_money" style="min-width: 180px;" placeholder="金额"></td> ' +
                '<td><input type="text" name="expend[' + index + '][remark]" maxlength="100" class="form-control expend_money" style="min-width: 180px;" placeholder="产品"></td>' +
                '<td><a href="javascript:;" class="del_expend">删除</a></td>' +
                '</tr>';
            $("#expend_tbody").append(display);
            $(".del_expend:last").click(function () {
                $(this).parent().parent().remove();
            });
        });
    }
    //校验特殊消耗数据
    function validExpend() {
        var check = true;
        $(".expend_tr").each(function () {
            var index = Number($(this).index('.expend_tr')) + 1;
            var expend_date = $(this).find(".expend_date").val();
            var expend_title = $(this).find(".expend_title").val();
            var expend_money = $(this).find(".expend_money").val();
            console.log(expend_date);
            if (expend_date=='') {
                modalExport('账单特殊消耗【第'+ index +'行】的日期为空');
                check = false;
                return false;
            }
            if (expend_title=='') {
                modalExport('账单特殊消耗【第'+ index +'行】的标题为空');
                check = false;
                return false;
            }
            if (expend_money=='') {
                modalExport('账单特殊消耗【第'+ index +'行】的金额为空');
                check = false;
                return false;
            }
            var money_regex = /^\d{1,6}(\.\d\d?)?$/;
            if (!money_regex.test(expend_money)) {
                modalExport('账单特殊消耗【第'+ index +'行】的金额格式不正确');
                check = false;
                return false;
            }
        });
        return check;
    }

    $(function () {
        //绑定特殊消耗事件
        //bindExpend();

        $("#pre_sale_customer_id").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '--无线下测试客户--',
            width       : '200px'
        });
        $("#operator").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择运营跟进人',
            width       : '200px'
        });
        $("#reconciliation_cycle").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择对账周期',
            width       : '200px'
        });
        $("#salesman").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择商务跟进人',
            width       : '200px'
        });
        $("#introduce_salesman").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择商务推荐人',
            width       : '200px'
        });
        $("#channel_follower").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择渠道跟进人',
            width       : '200px'
        });
        $("#group_id").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择主体',
            width       : '200px'
        });
        $("#pre_sale_customer_id").change(function () {
            var pre_sales_customer_data = {$pre_sales_customer_data|json_encode};
            var ps_sales_customer_id = $(this).val();
            if (ps_sales_customer_id != '') {
                $("#name").val(pre_sales_customer_data[ps_sales_customer_id].ps_customer_name);
                $("#company").val(pre_sales_customer_data[ps_sales_customer_id].company);
            }
        });
        $(".glyphicon").popover({});
        // 表单提交事件
        formSubmit();
        //切换一级菜单
        $("#type").change(function () {
            $.ajax({
                url     : "{:U('add')}",
                type    : 'post',
                data    : {
                    require_type  : 'cut_company_type',
                    first_type_id : $(this).val()
                },
                success : function (res) {
                    if (res.status == 0) {
                        $("#twice_type").html(res.data.option);
                    }
                }
            })
        });

        // 预付费展示预警信息
        $('#payment_type_radio1').click(function () {
            $('#warning_info').show();
        });
        // 后付费不展示预警信息
        $('#payment_type_radio2').click(function () {
            $('#warning_info').hide();
        });
    });

    // 表单提交事件
    function formSubmit() {
        $('#form_account').submit(function () {
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }
            // var result_expend = validExpend();
            // if (result_expend===false) {
            //     return false;
            // }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request  = $(that).attr('action');
        var url_redirect = '/Account/Customer/index';

        $.post(url_request, data_request).success(function (response) {
            if (response.status !== 'success') {
                modalExport(response.info);
                return '';
            }
            var customer_id = response.data.customer_id;
            var url         = '{:U(\'/Account/Customer/addSuccess\',array(\'customer_id\'=>0))}';
            url             = url.replace('customer_id/0', 'customer_id/' + customer_id);
            url             = url + '?callback_url={$Think.get.callback_url|urlencode}';
            DHB.INFO.view(url, '客户添加成功');
        }).error(function (response) {
            modalExport('创建用户出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var name     = $('#name').val().trim();
        // var email    = $('#email').val().trim();
        // var end_time = $('#end_time').val().trim();
        // console.log(end_time)
        if (!name) {
            console.log('客户名称未填写');
            modalExport('客户名称未填写');
            return false;
        }
        if (name.length < 2) {
            modalExport('客户的名字的长度需要不可以小于2');
            return false;
        }
        // if (!email) {
        //     modalExport('邮箱未填写');
        //     return false;
        // }
        // if (!end_time) {
        //     modalExport('截止日期未填写');
        //     return false;
        // } else {
        //     // 选定的时间必须是大于今天的
        //     var today_str = (new Date()).toDateString();
        //     var time_diff = new Date(Date.parse(end_time)) - new Date(Date.parse(today_str));
        //     if (time_diff < 0) {
        //         modalExport('截止日期不能小于当前日期');
        //         return false;
        //     }
        // }
        var balance = $("#balance").val();
        if (balance != '') {
            var balance_reg = /^\d{1,10}(\.\d{1,2})?$/;
            if (!balance_reg.test(balance)) {
                modalExport('预警余额格式不正确');
                return false;
            }
        }
        var balance_precent = $("#balance_percent").val();
        if (balance_precent != '') {
            var balance_precent_reg = /^\d{1,5}(\.\d{1,2})?$/;
            if (!balance_precent_reg.test(balance_precent)) {
                modalExport('预警余额占比格式不正确');
                return false;
            }
        }
        var available_days = $("#available_days").val();
        if (available_days != '') {
            var available_days_reg = /^\d{1,3}$/;
            if (!available_days_reg.test(available_days)) {
                modalExport('预警剩余消耗天数格式不正确');
                return false;
            }
        }
        return true;
    }

    function creat_hash(id, length) {
        DHB.ajax({
            url     : "{:U('Home/Tool/hashid')}",
            type    : 'get',
            data    : {"length" : length},
            success : function (r) {
                $("#" + id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length) {
        if (confirm('修改apikey,appsecret会使当前账号的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        }
    }
</script>
</body>
</html>
