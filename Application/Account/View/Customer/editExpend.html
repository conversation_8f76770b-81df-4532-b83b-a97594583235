<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            height : 100px;
            width  : 100%;
        }

        .form_title {
            background    : #CCCCCC;
            padding       : 10px 10px;
            box-sizing    : border-box;
            font-size     : 16px;
            border-radius : 4px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回客户列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="{:U('editExpend')}?callback_url={$Think.get.callback_url|urlencode}" method="post"
                              class="form-horizontal" id="form_account">

                            <div class="form-group">
                                <label for="type" class="control-label col-md-3">类型：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="type" id="type">
                                        <?php if ( 1==$data['type'] ) { ?>
                                        <option value="1" selected>赠送</option>
                                        <option value="2">消耗</option>
                                        <?php } else { ?>
                                        <option value="1">赠送</option>
                                        <option value="2" selected>消耗</option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>


                            <div class="form-group">
                                <label for="name" class="control-label col-md-3">标题：</label>
                                <div class="col-md-4">
                                    <input type="text" name="name" id="name" class="form-control" value="{$data.name}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="start_date" class="control-label col-md-3">月份：</label>
                                <div class="col-md-4">
                                    <input type="month" name="start_date" id="start_date" class="form-control" value="{$data.start_date|substr=###,0,4}-{$data.start_date|substr=###,4,2}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="profile_show_date" class="control-label col-md-3">权责利润展示日：</label>
                                <div class="col-md-4">
                                    <input type="date" name="profile_show_date" id="profile_show_date" class="form-control" value="{$data.profile_show_date}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="product_id" class="control-label col-md-3">选择产品：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="product_id" id="product_id">
                                        {$product_options}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="money" class="control-label col-md-3">金额：</label>
                                <div class="col-md-4">
                                    <input type="text" name="money" id="money" class="form-control" value="{$data.money}">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="fee_number" class="control-label col-md-3">计费用量：</label>
                                <div class="col-md-4">
                                    <input type="number" name="fee_number" id="fee_number" class="form-control" value="{$data.fee_number}">
                                </div>
                            </div>

                            <input type="hidden" name="id" value="{$data.id}">
                            <div class="pull-right">
                                <input type="submit" class="btn btn-primary btn-sm" value="保存">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    new Vue({
        el : '#account_app'
    });


    $(function () {
        //绑定特殊消耗事件
        $("#product_id").select2({
            allowClear  : false,
            theme       : "bootstrap",
            placeholder : '选择产品',
            width       : '200px'
        });

        // 表单提交事件
        formSubmit();
    });

    // 表单提交事件
    function formSubmit() {

        $('#form_account').submit(function () {
            if (checkForm()){
                // 发送表单
                event.preventDefault();
                formRequest($(this));
            }
            return false;
        });
    }

    function checkForm() {
        if ($("#start_date").val() == '') {
            modalExport('月份不可为空');
            return false;
        }
        if ($("#profile_show_date").val() == '') {
            modalExport('权责利润展示日不可为空');
            return false;
        }
        if ($("#name").val() == '') {
            modalExport('标题不可为空');
            return false;
        }
        if ($("#money").val() == '') {
            modalExport('金额不可为空');
            return false;
        }
        var money_regex = /^\-?\d{1,6}(\.\d\d?)?$/;
        if (!money_regex.test($("#money").val())) {
            modalExport('金额格式不正确');
            return false;
        }
        return true;
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request  = $(that).attr('action');
        var url_redirect = '/Account/Customer/index';

        $.post(url_request, data_request).success(function (response) {
            if (response.status !== 'success') {
                modalExport(response.info);
                return '';
            }


            //var customer_id = response.data.customer_id;
            //var url         = '{:U(\'/Account/Customer/addSuccess\',array(\'customer_id\'=>0))}';
            //url             = url.replace('customer_id/0', 'customer_id/' + customer_id);
            //url             = url + '?callback_url={$Think.get.callback_url|urlencode}';
            //var url =

           location.href= '{$Think.get.callback_url}';

            //DHB.INFO.view(url, '保存成功');
        }).error(function (response) {
            modalExport('创建用户出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }
</script>
</body>
</html>
