<!DOCTYPE html>
<html>
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    -->
    <include file="Common@Public/head"/>

    <script src="__JS__xm-select.js"></script>

    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }

        .index-btn {
            margin : 5px;
        }

        .table > thead > tr > th,
        .table > tbody > tr > td {
            padding:2px;
        }

        .acton_buttons > li{
            padding-right: 0;
            padding-left : 0;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<!-- 客户一键变更 -->
<include file="Common@components/upgrade_by_one_key"/>
<!-- 提示模态框(唯一性) -->
<include file="Common@components/prompt_modal_unique"/>

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form id="form_init" action="/Account/Customer/index" method="get" class="form-inline">
                <div class="form-group">
                    <label for="customer_id">ID：</label>
                    <input type="text" name="customer_id" id="customer_id" class="form-control"
                           value="<?= (isset($request['customer_id']) && $request['customer_id']) ? $request['customer_id'] : '';?>">
                </div>
                <div class="form-group">
                    <label for="status">状态：</label>
                    <select name="status" id="status" class="form-control">
                        <option value="-1">状态</option>
                        <option value="1"
                        <?= (isset($request['status']) && $request['status']==1) ? 'selected' : '' ?>>可用</option>
                        <option value="0"
                        <?= (isset($request['status']) && $request['status']==0) ? 'selected' : '' ?>>禁用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="name">主体名称：</label>
                    <select name="group_id" id="group_id" class="form-control">
                        <?php if (isset($request['group_id']) && $request['group_id']) {?>
                        <option value="<?= $request['group_id']; ?>" selected><?= $request['group_name']; ?></option>
                        <?php } ?>
                        <option value="">主体名称</option>
                        <?php foreach($list_group_for_select2 as $group) {?>
                        <option value="<?= $group['group_id']; ?>"
                        <?= I('get.') ?>><?= $group['group_name']; ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="name">客户名称：</label>
                    <select name="name" id="name" class="form-control">
                        <?php if (isset($request['name']) && $request['name']) {?>
                        <option value="<?= $request['name']; ?>" selected><?= $request['name']; ?></option>
                        <?php } ?>
                        <option value="">客户名称</option>
                        <?php foreach($list_customer_for_select2 as $customer) {?>
                        <option value="<?= $customer['name']; ?>"
                        <?= I('get.') ?>><?= $customer['name']; ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="company">公司名称：</label>
                    <select name="company" id="company" class="form-control">
                        <option value="">选择公司名称</option>
                        {$company_option}
                    </select>

                </div>
                <div class="form-group">
                    <label for="productName">产品名称：</label>
                    <select name="product_name" id="productName" class="form-control">
                        <?= $product_name = I('get.product_name'); ?>
                        <notempty name="product_name">
                            <option value="{$product_name}" selected>{$productList.$product_name}</option>
                        </notempty>
                        <option value=""></option>
                        <volist name="productList" id="vo" key="k">
                            <option value="{$key}">{$vo}</option>
                        </volist>
                    </select>
                </div>
                <div class="form-group">
                    <label for="operator">运营跟进人：</label>
                    <select name="operator" id="operator" class="form-control">
                        <option value="">选择运营跟进人</option>
                        {$operator_option}
                    </select>
                </div>
                <div class="form-group">
                    <label for="salesman">商务跟进人：</label>
                    <select name="salesman" id="salesman" class="form-control">
                        <option value="">选择商务跟进人</option>
                        {$salesman_option}
                    </select>
                </div>
                <div class="form-group">
                    <label for="introduce_salesman">商务推荐人：</label>
                    <select name="introduce_salesman" id="introduce_salesman" class="form-control">
                        <option value="">选择商务推荐人</option>
                        {$salesman_option}
                    </select>
                </div>
                <div class="form-group">
                    <label for="apikey">APIKEY：</label>
                    <input type="text" name="apikey" id="apikey" class="form-control"
                           value="<?= (isset($request['apikey']) && $request['apikey']) ? $request['apikey'] : '';?>">
                </div>
                <div class="form-group">
                    <label for="first_type">公司类型</label>
                    <select id="first_type" name="first_type" class="form-control">
                        <option value="">全部</option>
                        {$option.first_type_option}
                    </select>
                    -
                    <select name="type" id="type" class="form-control">
                        <option value="">全部</option>
                        {$option.twice_type_option}
                    </select>
                </div>
                <div class="form-group">
                    <label for="contract_status">签约状态</label>
                    <select name="contract_status[]" class="form-control selectpicker" id="contract_status" multiple data-live-search="true" data-actions-box="true">
                        {$contract_status_option}
                    </select>
                </div>
                <div class="form-group">
                    <label for="signType">征信客户分类：</label>
                    <select name="signType" id="signType" class="form-control">
                        {$signTypeOption}
                    </select>
                </div>
                <div class="form-group">
                    <label for="customerType">客户类型：</label>
                    <select name="customerType" id="customerType" class="form-control">
                        {$customerTypeOption}
                    </select>
                </div>
                <div class="form-group">
                    <label for="paymentType">付费类型：</label>
                    <select name="paymentType" id="paymentType" class="form-control">
                        {$paymentTypeOption}
                    </select>
                </div>
                <div class="form-group">
                    <label for="level_scale_income">客户级别3：</label>
                    <input type="text" name="level_scale_income" id="level_scale_income" class="form-control"
                           value="<?= (isset($request['level_scale_income']) && $request['level_scale_income']) ? $request['level_scale_income'] : '';?>">
                </div>
                <div class="form-group">
                    <input type="submit" id="submit_btn" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <a id="file_export" class="btn btn-success btn-sm">导出</a>
                </div>
<!--                <div class="form-group">-->
<!--                    <a id="fee_config" class="btn btn-success btn-sm">导出计费配置</a>-->
<!--                </div>-->
                <div class="form-group">
                    <a id="fee_config_new" class="btn btn-success btn-sm">导出计费配置(新)</a>
                </div>
                <div class="form-group">
                    <a href="{:U('add')}?callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn btn-info btn-sm">添加客户</a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue">
            <thead>
            <tr>
                <th width="140">客户ID</th>
                <th>客户名称</th>
                <th>主体</th>
                <th>公司名称</th>
<!--                <th>邮箱地址</th>-->
                <th>公司类型</th>
                <th width="110">签约状态</th>
                <th width="90">征信客户分类</th>
                <th width="85">客户级别3 (规模+收入)</th>
                <th width="20">付款类型</th>
                <th width="20">状态</th>
                <th>已开通账号</th>
                <th>区域</th>
                <th width="20">运营跟进人</th>
                <th width="20">商务跟进人</th>
                <th width="20">商务推荐人</th>
                <th width="128">TIME</th>
                <th width="20">操作人</th>
                <th width="235">操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($list_customer as $customer){ ?>
            <tr>
                <td><?= $customer['customer_id']; ?></td>
                <td><?= $customer['name']; ?></td>
                <td>{$GroupOption[$customer['group_id']]}</td>
                <td>
                    <?php

                        $agent_company = json_decode($customer['agent_company'], true);
                        $companyHtml = "<ul>";

                        foreach($agent_company as $source_name => $company_name){
                            $companyHtml .= "<li>".$source_name.": ".$company_name."</li>";
                        }
                        $companyHtml .= "</ul>";
                     ?>
                    <a href="javascript:;" style="color:#337ab7;" onclick="showAgentCompany('<?= $companyHtml; ?>')">
                    <?= $customer['company']; ?>
                    </a>
                </td>
<!--                <td><?= $customer['email']; ?></td>-->
                <td>{$company_type_data[$customer['type']]}</td>
                <td>{$contract_status[$customer['contract_status']]}</td>
                <!-- "0,1,10" => "朴道签约<br /> 郑数交签约" -->
                <td><?= implode('<br/>',array_map(function($item) use($sign_type_map){return $sign_type_map[$item];},explode(',',$customer['sign_type']))) ?></td>
                <td><?= $customer['level_scale_income'] ?></td>
                <td><?= ($customer['payment_type'] == 1) ? '预付款' : '后付款' ; ?></td>
                <td><?= ($customer['status'] == 1) ? '可用' : '禁用' ; ?></td>
                <td>
                    <?php foreach ($customer['account_list'] as $account){ ?>
                    <eq name="account.status" value="0">
                        <a href="javascript:;" style="color:#333333;"
                           onclick="DHB.INFO.view('{:U(\'/Account/Account/accountInfo\',array(\'id\'=>$account[\'id\']))}?callback_url={$Think.server.REQUEST_URI|urlencode}','账号详情')"><?= $account['account_name']?></a>
                        <else/>
                        <?php if (isset($request['apikey']) && $request['apikey'] == $account['apikey'] ) { ?>
                        <a href="javascript:;" onclick="DHB.INFO.view('{:U(\'/Account/Account/accountInfo\',array(\'id\'=>$account[\'id\']))}?callback_url={$Think.server.REQUEST_URI|urlencode}','账号详情')" style="background: yellow"><?= $account['account_name']?></a>
                        <?php } else { ?>
                        <a href="javascript:;" onclick="DHB.INFO.view('{:U(\'/Account/Account/accountInfo\',array(\'id\'=>$account[\'id\']))}?callback_url={$Think.server.REQUEST_URI|urlencode}','账号详情')"><?= $account['account_name']?></a>
                        <?php } ?>
                    </eq>
                    <br>
                    <?php } ?>
                </td>
                <td><?= $user_dept_map[$customer['salesman']]; ?></td>
                <td><?= $user_data[$customer['operator']]; ?></td>
                <td><?= $user_data[$customer['salesman']]; ?></td>
                <td><?= $user_data[$customer['introduce_salesman']]; ?></td>
                <td class="time_span">
                    <span>创建: <?= date('Y-m-d', $customer['create_at']) ?></span>
                    <span>更新: <?= date('Y-m-d', $customer['update_at']) ?></span>
                </td>
                <td><?= $user_data[$customer['admin']]; ?></td>
                <td>
                    <ul class="list-inline acton_buttons">
                        <li>
                            <a href="/Account/Customer/edit?customer_id=<?= $customer['customer_id'];?>&callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn index-btn btn-info btn-sm">编辑</a>
                        </li><li>
                            <a href="/Account/Account/add?id=<?= $customer['id'];?>&callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn index-btn btn-info btn-sm">添加账号</a>
                        <!--</li><li>-->
                        <!--    <a href="/Account/Customer/feeConfig?type=add&customer_id=<?= $customer['customer_id'];?>&callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn index-btn btn-info btn-sm">计费配置</a>-->
                        </li><li>
                            <a class="btn index-btn btn-info btn-sm" onclick="configCustomerPrice('{$customer.customer_id}')">计费配置(新)</a>
                        </li><li>
                            <upgrade_by_one_key_template customer_id='<?= $customer["customer_id"] ?>'></upgrade_by_one_key_template>
                        </li><li>
                            <a href="javascript:deleteCustomer('{$customer.customer_id}');" class="btn index-btn btn-danger btn-sm">删除</a>
                        </li>
                    </ul>
                </td>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
</div>
<div class="container">
    <nav>
        <ul class="pagination">
            {$obj_page->show()}
        </ul>
    </nav>
</div>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
<!--<script src="__STATICS__layui-v2.5.4/layui/layui.all.js"></script>-->
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>

<script type="text/javascript">
    function configCustomerPrice(customer_id) {
        layui.layer.open({
            type    : 2,
            title   : '计费配置',
            content : "{:U('Account/PriceCustomerConfig/index')}?customer_id=" + customer_id,
            area    : ['100%', '100%']
        });
    }
    new Vue({
        el      : '#target_vue',
        methods : {
            updateOneTime : function () {
                this.$refs.prompt_modal.open({
                    title : '提示',
                    body  : '您触发了一键变更流程'
                });
            }
        }

    });
    $(function () {
        $("#name").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择客户',
            width       : '200px'
        });
        $("#group_id").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择主体',
            width       : '200px'
        });
        $("#operator").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择运营跟进人',
            width       : '200px'
        });
        $("#company").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择公司名称',
            width       : '200px'
        });
        $("#salesman").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择商务跟进人',
            width       : '200px'
        });
        $("#introduce_salesman").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择商务推荐人',
            width       : '200px'
        });
        $("#productName").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择产品',
            width       : '200px'
        });
        $("#file_export").click(function () {
            var url  = "{:U('Account/Customer/export')}" + location.search;
            var conf = confirm("确定按照当前的条件导出文件？");
            if (conf) {
                window.open(url);
            }
            return false;
        });
        //切换一级菜单
        $("#first_type").change(function () {
            if ($(this).val() == '') {
                $("#type").html('<option value="">全部</option>');
            }
            $.ajax({
                url     : "{:U('index')}",
                type    : 'post',
                data    : {
                    require_type  : 'cut_company_type',
                    first_type_id : $(this).val()
                },
                success : function (res) {
                    if (res.status == 0) {
                        $("#type").html('<option value="">全部</option>' + res.data.option);
                    }
                }
            })
        });
    });

    function deleteCustomer(customer_id) {
        let conf = confirm('确定删除该客户数据，删除后客户数据与其子账号将会被删除，是否继续？');
        if (!conf) {
            return false;
        }
        $.post("{:U('Account/Customer/delete')}", {
            customer_id : customer_id
        }, function (res) {
            if (res.status == 0) {
                alert("删除成功");
                location.reload();
            } else {
                alert(res.message);
            }
        })
    }

    function showAgentCompany(ulHtml){
        // var ul = "<ul>" +
        //     "<li>朴道：测试专用-华夏卡中心</li>" +
        //     "<li>浙数交：测试专用-华夏卡中心</li>" +
        //     "</ul>"
        layer.open({
            title: '签约名称'
            ,content: ulHtml
        });
    }

    // $('#fee_config').click(function () {
    //     var param    = '';
    //     var formData = $("#form_init").serializeArray();//把form里面的数据序列化成数组
    //     formData.forEach(function (e) {
    //         param += e.name + '=' + e.value + '&';
    //     });
    //     param    = param.substring(0, param.length - 1);
    //     var url  = "/Account/Customer/feeConfigDownload.html?" + param;
    //     var conf = confirm("确定按照当前的条件导出文件？");
    //     if (conf) {
    //         window.open(url);
    //     }
    //     return false;
    // });

    $('#fee_config_new').click(function () {
        var formData = $('#form_init').serializeArray();
        var where = {};
        formData.forEach(function (e) {
            where['"'+e.name+'"'] = e.value;
        });
        where['export'] = 'feeConfig';
        var url  = "/Account/PriceCustomerConfig/feeConfigDown.html";

        Request.post(url, where).then(function (data) {

            let sheetHeader = ['客户ID', '客户名称', '账号','产品名称', '运营商', '计费方式', '计费依据', '多级模式', '转化后价格','价格', '开始时间'];
            let sheetData = [];
            $.each(data.data, function (key, val){
                    sheetData.push({
                        customer_id: val['customer_id'],
                        customer_name: val['customer_name'],
                        account_name: val['account_name'],
                        product_name: val['product_name'],
                        diff_operator: val['diff_operator'],
                        methods: val['methods'],
                        accord: val['accord'],
                        mode: val['mode'],
                        price_cn: val['price_cn'],
                        price: val['price'],
                        start_date: val['start_date']
                    })


            });
            let option = {};
            option.fileName = '计费配置导出';
            option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
            (new ExportJsonExcel(option)).saveExcel();

        });

        return false;
    });

    $("#contract_status").selectpicker({
        width : '220px'
    });

</script>
</body>
</html>
