<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        textarea {
            height : 100px;
            width  : 100%;
        }

        .form_title {
            background    : #CCCCCC;
            padding       : 10px 10px;
            box-sizing    : border-box;
            font-size     : 16px;
            border-radius : 4px;
        }

        .layui-input-block{
            margin-left: 0;
            width: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="update_account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回客户列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="/Account/Customer/edit" method="post" class="form-horizontal" id="form_account">
                            <input type="hidden" name="customer_id" id="customer_id"
                                   value="<?= $customer_info['customer_id'] ?>">
                            <input type="hidden" name="id" id="id" value="{$customer_info.id}">

                            <h4 class="form_title" id="base">基本信息</h4>

                            <notempty name="pre_sales_customer_data">
                                <div class="form-group">
                                    <label for="pre_sale_customer_id" class="control-label col-md-3">关联线下测试客户：</label>
                                    <div class="col-md-8">
                                        <input type="text" name="pre_sale_customer_id" id="pre_sale_customer_id"
                                               readonly disabled value="{$pre_sales_customer_data.ps_customer_name}"
                                               class="form-control">
                                    </div>
                                </div>
                            </notempty>
                            <br />

                            <div class="form-group">
                                <label for="group_id" class="control-label col-md-3">主体：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="group_id" id="group_id">
                                        {$group_option}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="name" class="control-label col-md-3">客户名称：</label>
                                <div class="col-md-8">
                                    <input type="text" name="name" id="name" class="form-control"
                                           value="<?= $customer_info['name'] ?>">
                                </div>
                            </div>

                            <?php foreach($companys as $s_id => $item){?>
                            <div class="form-group">
                                <label for="company<?=$s_id?>" class="control-label col-md-3">公司名称-<?=$item['source_name']?>：</label>
                                <div class="col-md-8">
                                    <input type="text" name="company[<?=$s_id?>]" id="company<?=$s_id?>" class="form-control"
                                           value="<?=$item['company_name'] ?>">
                                </div>
                            </div>
                            <?php } ?>


<!--                            <div class="form-group">-->
<!--                                <label for="email" class="control-label col-md-3">登陆邮箱：</label>-->
<!--                                <div class="col-md-5">-->
<!--                                    <input type="text" name="email" class="form-control" id="email"-->
<!--                                           value="<?= $customer_info['email'] ?>">-->
<!--                                </div>-->
<!--                                <div class="col-md-1"></div>-->
<!--                                <div class="col-md-2">-->
<!--                                    <input type="button"-->
<!--                                           onclick="DHB.INFO.set('{:U('resetPwd',array('id'=>$customer_info['id']))}','提示')"-->
<!--                                           class="btn btn-primary btn-sm" value="重置密码">-->
<!--                                </div>-->
<!--                            </div>-->

                            <div class="form-group">
                                <label for="status_radio1" class="control-label col-md-3">状态：</label>
                                <div class="col-md-8">
                                    <label class="radio-inline">
                                        <input type="radio" name="status" id="status_radio1"
                                               value="1" <?= $customer_info['status'] == 1 ? 'checked' : ''; ?>>可用
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="status" id="status_radio2"
                                               value="0" <?= $customer_info['status'] != 1 ? 'checked' : ''; ?>>禁用
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="payment_type_radio1" class="control-label col-md-3">付款类型：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="payment_type" id="payment_type_radio1" value="1"  <?= $customer_info['payment_type'] == 1 ? 'checked' : ''; ?> >预付费
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="payment_type" id="payment_type_radio2" value="2"  <?= $customer_info['payment_type'] == 2 ? 'checked' : ''; ?> >后付费
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="channel_mode_radio1" class="control-label col-md-3">是否为渠道客户：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="channel_mode" id="channel_mode_radio1" value="1" <?= $customer_info['channel_mode'] == 1 ? 'checked' : ''; ?>>是
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="channel_mode" id="channel_mode_radio2" value="2" <?= $customer_info['channel_mode'] == 2 ? 'checked' : ''; ?>>否
                                    </label>
                                </div>
                            </div>


                            <div class="form-group">
                                <label for="channel_follower" class="control-label col-md-3">客户分类：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="level" id="level_radio1" value="10" <?= $customer_info['level'] == 10 ? 'checked' : ''; ?>>头部客户
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="level" id="level_radio2" value="50" <?= $customer_info['level'] == 50 ? 'checked' : ''; ?>>重要客户
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="level" id="level_radio3" value="100" <?= $customer_info['level'] == 100 ? 'checked' : ''; ?>>一般客户
                                    </label>
                                </div>
                            </div>


                            <div class="form-group">
                                <label for="reconciliation_cycle" class="control-label col-md-3">对账周期：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="reconciliation_cycle" id="reconciliation_cycle">
                                        {$reconciliationCycleOption}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="operator" class="control-label col-md-3">运营跟进人：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="operator" id="operator">
                                        {$user_option_operator}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="salesman" class="control-label col-md-3">商务跟进人：</label>
                                <div class="col-md-3">
                                    <select class="form-control" name="salesman" id="salesman">
                                        {$user_option_salesman}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="introduce_salesman" class="control-label col-md-3">商务推荐人：</label>
                                <div class="col-md-3">
                                    <select class="form-control" name="introduce_salesman" id="introduce_salesman">
                                        {$user_option_introduce_salesman}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="change_month" class="control-label col-md-3">新商务跟进人切换日期：</label>
                                <div class="col-md-2">
                                    <div class="layui-input-block">
                                        <input type="text" name="change_month" placeholder="请选择月份" autocomplete="off" class="layui-input" id="change_month">
                                    </div>
                                </div>
                            </div>


                            <div class="form-group">
                                <label for="channel_follower" class="control-label col-md-3">渠道跟进人：</label>
                                <div class="col-md-4">
                                    <select class="form-control" name="channel_follower" id="channel_follower">
                                        {$user_option_channel_follower}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="status_radio1" class="control-label col-md-3">渠道跟进人可见数据：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="checkbox" name="source_id[]" id="source_id_radio" value="-1"  <?= in_array(-1,$source_id)? 'checked' : ''; ?> >全部
                                    </label>
                                    <?php foreach($source_list as $value => $label){?>
                                    <label class="radio-inline">
                                        <input type="checkbox" name="source_id[]" id="source_id_radio<?=$value?>" value="<?=$value?>"  <?= in_array($value,$source_id) ? 'checked' : ''; ?> ><?=$label?>
                                    </label>
                                    <?php } ?>
                                </div>
                            </div>

                            <div class="form-group" hidden>
                                <label for="c_type" class="control-label col-md-3">客户分类：</label>
                                <div class="col-md-8">
                                    <select name="c_type" id="c_type" class="form-control">
                                        <option value="1"
                                        <?=  ($customer_info['c_type'] === '1') ? 'selected' : '';?>>一般客户</option>
                                        <option value="2"
                                        <?=  ($customer_info['c_type'] === '2') ? 'selected' : '';?>>自管客户</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="sign_type" class="control-label col-md-3">征信客户分类：</label>
                                <div class="col-md-4">
                                    <!--<select name="sign_type" id="sign_type" class="form-control">-->
                                    <!--    {$signTypeOption}-->
                                    <!--</select>-->
                                    <?php foreach($signTypeOption as $value => $label){?>
                                    <label class="radio-inline">
                                        <input type="checkbox" name="sign_type[]" id="sign_type_radio<?=$value?>" value="<?=$value?>"  <?= in_array($value,$sign_type) ? 'checked' : ''; ?> ><?=$label?>
                                    </label>
                                    <?php } ?>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="customer_type" class="control-label col-md-3">客户类型：</label>
                                <div class="col-md-2">
                                    <select name="customer_type" id="customer_type" class="form-control">
                                        {$customerTypeOption}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="type" class="control-label col-md-3">公司类型：</label>
                                <div class="col-md-2">
                                    <select id="type" class="form-control">
                                        {$option.first_type_option}
                                    </select>
                                </div>
                                <div class="col-md-1"></div>
                                <div class="col-md-2">
                                    <select name="type" id="twice_type" class="form-control">
                                        {$option.twice_type_option}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group" style="display: none">
                                <label class="col-md-3 control-label">APIKEY</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" value="<?= $customer_info['apikey'] ?>"
                                           readonly="">
                                </div>
                            </div>

                            <div class="form-group" style="display: none">
                                <label class="col-md-3 control-label">APPSECRET</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" value="<?= $customer_info['appsecret'] ?>"
                                           readonly="">
                                </div>
                            </div>

<!--                            <div class="form-group">-->
<!--                                <label class="col-md-3 control-label">截止日期：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <input type="date" class="form-control" name="end_time" id="end_time"-->
<!--                                           value="<?= $customer_info['end_time'] ?>"/>-->
<!--                                </div>-->
<!--                            </div>-->


                            <div id="warning_info" style="<?= $customer_info['payment_type'] == 2 ? 'display: none;' : ''?> " >
                            <h4 class="form_title" id="warning">预警信息</h4>

                            <div class="form-group">
                            <span class="glyphicon glyphicon-question-sign control-label col-md-1" data-trigger="hover"
                                  aria-hidden="true" style="color:#C9302C;" data-toggle="popover"
                                  title="预警余额" data-content="当客户余额小于等于预警余额时，将发送余额报警邮件；若不填写，则不发送对应预警邮件"></span>
                                <label for="balance" class="control-label col-md-2">
                                    预警余额：
                                </label>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="text" name="balance" id="balance" value="{$customer_info.balance}"
                                               class="form-control" maxlength="10" aria-describedby="basic-addon2">
                                        <span class="input-group-addon" id="basic-addon2">元</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                            <span class="glyphicon glyphicon-question-sign control-label col-md-1" data-trigger="hover"
                                  aria-hidden="true" style="color:#C9302C;" data-toggle="popover"
                                  title="预警余额占比"
                                  data-content="当客户余额/客户最近一次充值金额*100%小于等于此值时，会发送余额报警邮件；若不填写，则按统一标准（30%，20%，10%）发送余额报警邮件"></span>
                                <label for="balance_percent" class="control-label col-md-2">
                                    预警余额占比：
                                </label>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="text"
                                               value="{$customer_info['balance_percent']?$customer_info['balance_percent']*100:''}"
                                               name="balance_percent" id="balance_percent" class="form-control"
                                               maxlength="5" aria-describedby="basic-addon3">
                                        <span class="input-group-addon" id="basic-addon3">%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                            <span class="glyphicon glyphicon-question-sign control-label col-md-1" data-trigger="hover"
                                  aria-hidden="true" style="color:#C9302C;" data-toggle="popover"
                                  title="预警剩余消耗天数"
                                  data-content="当客户余额预计剩余消耗天数小于等于此值时，会发送余额报警邮件；若不填写，则按统一标准（45天，30天，10天）发送余额报警邮件"></span>
                                <label for="available_days" class="control-label col-md-2">
                                    预警剩余消耗天数：
                                </label>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="number" value="{$customer_info.available_days}"
                                               name="available_days" id="available_days" class="form-control"
                                               maxlength="3" aria-describedby="basic-addon4">
                                        <span class="input-group-addon" id="basic-addon4">天</span>
                                    </div>
                                </div>
                            </div>
                            </div>
<!--                            <h4 class="form_title" id="bill">账单信息</h4>-->

<!--                            <div class="form-group">-->
<!--                                <label class="control-label col-md-3">账单特殊费用：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <span style="color:#C9302C;line-height:36px;font-size:14px;">请在表格中添加</span>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                            <div class="panel panel-default table-responsive" style="overflow-y: hidden">-->
<!--                                <table class="table table-hover table-striped table-bordered">-->
<!--                                    <thead class="center">-->
<!--                                    <tr>-->
<!--                                        <th>类型</th>-->
<!--                                        <th>月份</th>-->
<!--                                        <th>标题</th>-->
<!--                                        <th>权责利润展示日</th>-->
<!--                                        <th>选择产品</th>-->
<!--                                        <th>金额</th>-->
<!--                                        <th>计费用量</th>-->
<!--                                        <th>操作</th>-->
<!--                                    </tr>-->
<!--                                    </thead>-->
<!--                                    <tbody id="expend_tbody">-->
<!--                                    <volist name="expend" id="vo">-->
<!--                                        <tr>-->
<!--                                            <td><?php echo ($vo['type'] == 1 )? '赠送' : '消耗'; ?></td>-->
<!--                                            <td>{$vo.start_date|substr=###,0,4}-{$vo.start_date|substr=###,4,2}</td>-->
<!--                                            <td>{$vo.name}</td>-->
<!--                                            <td>{$vo.profile_show_date}</td>-->
<!--                                            <td>{$vo.remark}</td>-->
<!--                                            <td>{$vo.money}</td>-->
<!--                                            <td>{$vo.fee_number}</td>-->
<!--                                            <td>-->
<!--                                                <a href="/Account/Customer/editExpend?id={$vo.id}&callback_url={$Think.get.callback_url|urlencode=###}">编辑</a>-->
<!--                                                <a onclick="return confirm('确定要删除本条记录？')" href="/Account/Customer/delExpend?id={$vo.id}&callback_url={$Think.server.REQUEST_URI|urlencode}">删除</a>-->
<!--                                            </td>-->
<!--                                        </tr>-->
<!--                                    </volist>-->
<!--                                    </tbody>-->
<!--                                </table>-->
<!--                            </div>-->

<!--                            <div class="form-group" style="margin: 0 auto;">-->
<!--                                <button type="button" class="btn btn-info btn-sm" id="add_expend" style="float: right">-->
<!--                                    新增-->
<!--                                </button>-->
<!--                            </div>-->

<!--                            <div class="form-group">-->
<!--                                <label class="control-label col-md-3">账单发送频率：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="0" {$customer_info['frequency']==0?'checked':''}>人工发送-->
<!--                                    </label>-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="1" {$customer_info['frequency']==1?'checked':''}>每月第1天-->
<!--                                    </label>-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="2" {$customer_info['frequency']==2?'checked':''}>每季度第1天-->
<!--                                    </label>-->
<!--                                    <label class="radio-inline">-->
<!--                                        <input type="radio" name="frequency" value="3" {$customer_info['frequency']==3?'checked':''}>每年第1天-->
<!--                                    </label>-->
<!--                                </div>-->
<!--                            </div>-->

                            <div class="form-group">
                                <label class="control-label col-md-3">账单发送邮件类型：</label>
                                <div class="col-sm-8">
                                    <label class="radio-inline">
                                        <input type="radio" name="email_type" value="1" {$customer_info['email_type'] == 1 ? 'checked' : ''}>标准
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="email_type" value="2" {$customer_info['email_type'] == 2 ? 'checked' : ''}>非标准
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="bill_email" class="control-label col-md-3">账单收件人：</label>
                                <div class="col-md-8">
                                    <textarea name="bill_email" class="form-control" id="bill_email" rows="10"
                                              placeholder="每行一个邮箱地址，请输入符合邮箱规范的邮箱地址">{$customer_info.bill_email|explode=";",###|implode=PHP_EOL,###}</textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="bill_cc_email" class="control-label col-md-3">账单抄送人：</label>
                                <div class="col-md-8">
                                    <textarea name="bill_cc_email" class="form-control" id="bill_cc_email" rows="10"
                                              placeholder="每行一个邮箱地址，请输入符合邮箱规范的邮箱地址">{$customer_info.bill_cc_email|explode=";",###|implode=PHP_EOL,###}</textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-3 control-label">计费配置：</label>
                                <div class="col-sm-8">
                                    <span style="color:#C9302C;line-height:30px;font-size:14px;">详见表格内容</span>
                                </div>
                            </div>


                            <div class="panel panel-default table-responsive">
                                <table class="table table-hover table-striped table-bordered">
                                    <thead class="center">
                                    <tr>
                                        <th>计费开始时间</th>
                                        <th>产品名称</th>
                                        <th>计费账号</th>
                                        <th>计费依据</th>
                                        <th>计费方式</th>
                                        <th>计费规则</th>
                                        <th>区分运营商</th>
                                        <th>计费周期</th>
                                        <th>单价</th>
                                        <th>操作时间</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    if ($fee_config && is_array($fee_config)) {
                                        foreach ($fee_config as $key => $val) {
                                    ?>
                                    <tr>
                                        <td>{$val['start_date']}</td>
                                        <td>{$val['product_name']}</td>
                                        <td>{$val['account_name']}</td>
                                        <td>{$val['basis']}</td>
                                        <td>{$val['method']}</td>
                                        <td>{$val['rule']}</td>
                                        <td>{$val['price_rule']}</td>
                                        <td>{$val['period']}</td>
                                        <td>{$val['price']}</td>
                                        <td>
                                            <eq name="val['update_time']" value="0">
                                                {$val.create_time|date='Y-m-d H:i', ###}
                                                <else/>
                                                {$val.update_time|date='Y-m-d H:i', ###}
                                            </eq>
                                        </td>
                                        <td>
                                            {$val.remark}
                                        </td>
                                        <td>
                                            <eq name="val.is_delete" value="1">
                                                <a href='/Account/Customer/feeConfig?type=edit&id={$val.id}&callback_url={$Think.get.callback_url|urlencode}'>编辑</a>
                                                <a href='javascript:;' onclick='del_fee("{$val.id}")'>删除</a>
                                            </eq>
                                        </td>
                                    </tr>
                                    <?php }} ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="pull-right">
                                <ul class="list-inline">
                                    <li><input type="submit" class="btn btn-primary btn-sm" value="更新"></li>
                                    <li><a href="javascript:;" onclick="return goBack()"
                                           class="btn btn-info btn-sm">返回</a></li>
                                </ul>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="text/javascript">
    new Vue({
        el : '#update_account_app'
    });

    layui.laydate.render({
        elem  : '#change_month',
        type  : 'month',
        range : false,
        value : "{:$salesman_change_month}"
    });

    $("#operator").select2({
        allowClear  : false,
        theme       : "bootstrap",
        placeholder : '选择商务跟进人',
        width       : '200px'
    });
    $("#salesman").select2({
        allowClear  : false,
        theme       : "bootstrap",
        placeholder : '选择运营跟进人',
        width       : '200px'
    });
    $("#introduce_salesman").select2({
        allowClear  : false,
        theme       : "bootstrap",
        placeholder : '选择运营推荐人',
        width       : '200px'
    });
    $("#channel_follower").select2({
        allowClear  : false,
        theme       : "bootstrap",
        placeholder : '选择渠道跟进人',
        width       : '200px'
    });
    $("#reconciliation_cycle").select2({
        allowClear  : false,
        theme       : "bootstrap",
        placeholder : '选择对账周期',
        width       : '200px'
    });
    $("#group_id").select2({
        allowClear  : false,
        theme       : "bootstrap",
        placeholder : '选择主体',
        width       : '200px'
    });
    window.expend = 0;
    //绑定特殊消耗事件
    function bindExpend() {
        $("#add_expend").click(function () {

            var display = '<tr class="expend_tr" data-index="' + window.expend + '">' +
                '<td><select style="width:100px;" name="expend[' + window.expend + '][type]" class="form-control expend_type">' +
                '<option value="1">赠送</option>' +
                '<option value="2" selected>消耗</option>' +
                '</select></td>' +
                '<td><input type="month" name="expend[' + window.expend + '][start_date]" class="form-control expend_date" style="min-width: 180px;" placeholder="月份"></td> ' +
                '<td><input type="text" name="expend[' + window.expend + '][name]" maxlength="15" class="form-control expend_title" style="min-width: 180px;" placeholder="标题"></td> ' +
                '<td><input type="date" name="expend[' + window.expend + '][profile_show_date]" maxlength="15" class="form-control expend_profile_show_date" style="min-width: 180px;" value="{:date(\'Y-m-d\')}" placeholder="权责利润展示日"></td>' +
                '<td>' +
                '<select name="expend[' + window.expend + '][product_id]" class="form-control expend_product">' +
                `{$product_options}` +
                '</select>' +
                '</td>' +
                '<td><input type="text" name="expend[' + window.expend + '][money]" maxlength="10" class="form-control expend_money" style="min-width: 180px;" placeholder="金额"></td> ' +
                '<td><input type="number" name="expend[' + window.expend + '][fee_number]" maxlength="10" class="form-control expend_fee_number" style="min-width: 180px;" placeholder="计费用量"></td> ' +
                '<td><a href="javascript:;" class="del_expend">删除</a></td>' +
                '</tr>';
            $("#expend_tbody").append(display);
            window.expend++;
            $(".expend_product").last().select2({
                allowClear  : false,
                theme       : "bootstrap",
                placeholder : '选择产品',
                width       : '200px'
            });
            $(".del_expend:last").click(function () {
                $(this).parent().parent().remove();
            });
        });
    }
    //校验特殊消耗数据
    function validExpend() {
        var check = true;
        $(".expend_tr[data-index]").each(function () {
            var index                    = Number($(this).attr('data-index')) + 1;
            var expend_date              = $(this).find(".expend_date").val();
            var expend_title             = $(this).find(".expend_title").val();
            var expend_money             = $(this).find(".expend_money").val();
            var expend_profile_show_date = $(this).find(".expend_profile_show_date").val();
            if (expend_date == '') {
                modalExport('账单特殊消耗【第' + index + '行】的月份为空');
                check = false;
                return false;
            }
            if (expend_profile_show_date == '') {
                modalExport('账单特殊消耗【第' + index + '行】的权责利润展示日为空');
                check = false;
                return false;
            }
            if (expend_title == '') {
                modalExport('账单特殊消耗【第' + index + '行】的标题为空');
                check = false;
                return false;
            }
            if (expend_money == '') {
                modalExport('账单特殊消耗【第' + index + '行】的金额为空');
                check = false;
                return false;
            }
            var money_regex = /^\-?\d{1,6}(\.\d\d?)?$/;
            if (!money_regex.test(expend_money)) {
                modalExport('账单特殊消耗【第' + index + '行】的金额格式不正确');
                check = false;
                return false;
            }
        });
        return check;
    }
    $(function () {
        // bindExpend();

        $(".glyphicon").popover({});
        // 表单提交事件
        formSubmit();
        //切换一级菜单
        $("#type").change(function () {
            $.ajax({
                url     : "{:U('add')}",
                type    : 'post',
                data    : {
                    require_type  : 'cut_company_type',
                    first_type_id : $(this).val()
                },
                success : function (res) {
                    if (res.status == 0) {
                        $("#twice_type").html(res.data.option);
                    }
                }
            })
        });

        // 预付费展示预警信息
        $('#payment_type_radio1').click(function () {
            $('#warning_info').show();
        });
        // 后付费不展示预警信息
        $('#payment_type_radio2').click(function () {
            $('#warning_info').hide();
        });
    });

    // 返回上级菜单
    function goBack() {
        event.preventDefault();
        window.history.back();
    }

    // 表单提交事件
    function formSubmit() {
        $('#form_account').submit(function () {
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // var result_expend = validExpend();
            // if (result_expend === false) {
            //     return false;
            // }
            // 发送表单
            event.preventDefault();
            formRequest($(this));
            return false;
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request  = $(that).attr('action');
        var url_redirect = '{$Think.get.callback_url}';


        $.post(url_request, data_request).success(function (response) {
            modalExport(response.info);
            if (response.status === 'success') {
                window.location.href = url_redirect;
            }
            console.log(response.info);
        }).error(function (response) {
            modalExport('更新客户出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var name     = $('#name').val();
        var email    = $('#email').val();
        var id       = $('#id').val();
        var end_time = $('#end_time').val();
        if (!id) {
            modalExport('网络故障，请刷新后再试');
            return false;
        }
        if (!name) {
            modalExport('客户名称未填写');
            return false;
        }
        if (name.length < 2) {
            modalExport('客户的名字的长度需要不可以小于2');
            return false;
        }
        // if (!email) {
        //     modalExport('邮箱未填写');
        //     return false;
        // }
        // if (!end_time) {
        //     modalExport('截止日期未填写');
        //     return false;
        // } else {
        //     // 选定的时间必须是大于今天的
        //     var today_str = (new Date()).toDateString();
        //     var time_diff = new Date(Date.parse(end_time)) - new Date(Date.parse(today_str));
        //     if (time_diff < 0) {
        //         modalExport('截止日期不能小于当前日期');
        //         return false;
        //     }
        // }
        var balance = $("#balance").val();
        if (balance != '') {
            var balance_reg = /^\d{1,10}(\.\d{1,2})?$/;
            if (!balance_reg.test(balance)) {
                modalExport('预警余额格式不正确');
                return false;
            }
        }
        var balance = $("#balance").val();
        if (balance != '') {
            var balance_reg = /^\d{1,10}(\.\d{1,2})?$/;
            if (!balance_reg.test(balance)) {
                modalExport('预警余额格式不正确');
                return false;
            }
        }
        var balance_precent = $("#balance_percent").val();
        if (balance_precent != '') {
            var balance_precent_reg = /^\d{1,5}(\.\d{1,2})?$/;
            if (!balance_precent_reg.test(balance_precent)) {
                modalExport('预警余额占比格式不正确');
                return false;
            }
        }
        var available_days = $("#available_days").val();
        if (available_days != '') {
            var available_days_reg = /^\d{1,3}$/;
            if (!available_days_reg.test(available_days)) {
                modalExport('预警剩余消耗天数格式不正确');
                return false;
            }
        }
        var status = $('input[name="status"]:checked').val();
        if (status == 0) {
            return confirm('当客户状态设置为禁用时，此客户下所有计费配置会增加一条计费规则为：按用量-固定价格且单价为0的计费配置，计费开始日期为系统当前日期+1；请谨慎操作！谢谢！');
        }
        return true;
    }

    function del_fee(fee_id) {
        if (!confirm('确定要删除该计费配置吗？')) {
            return false;
        }
        var url  = '{:U("feeConfig", ["type" => "delete"])}';
        var info = {'id' : fee_id};
        $.post(url, info).success(function (data) {
            modalExport(data.info);
            if (data.status === 'success') {
                location.reload();
            }
            return true;
        }).error(function (data) {
            modalExport('删除失败');
            return false;
        });
    }
</script>
</body>
</html>
