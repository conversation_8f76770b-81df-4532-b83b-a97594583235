<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .form_grid{
            display: block;
            width: 100%;
            float: left;
            overflow: hidden;
        }
        .col-sm-10 .cr{
            line-height: 32px;
            display: block;
            height: 32px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container add_form" id="fee_config"></div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__ajaxform.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript" src="__JS__feeConfig.js"></script>
<script type="application/javascript">
     var feeConfig = new FeeConfig('#fee_config', "{:U('feeConfig', ['type' => 'edit'])}", '{$Think.get.callback_url}', '{$Think.get.callback_url}');
     feeConfig.setEditData({$data}).setProductList({$result}).init('{$customer_data.customer_id}', '{$customer_data.customer_name}');
</script>
