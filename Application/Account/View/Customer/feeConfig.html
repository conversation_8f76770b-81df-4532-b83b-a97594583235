<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<div class="container">
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <include file="Common@Public/nav"/>
    <input type="hidden" name="id" value="{$id}">
    <div class="pull-right" style="margin-right: 200px">
        <a class="btn btn-info" href="{$Think.get.callback_url}" role="button">返回客户列表</a>
    </div>
    <div class="row col-md-offset-2">
        <form id="form" class="form-horizontal" role="form" action="" method="post" data-info="<?= isset($fee_info) && !empty($fee_info) ? 1 : 0 ?>">
            <div class="form-group">
                <label class="col-sm-2 control-label">客户名称：</label>
                <div class="col-sm-4" style="padding-top: 7px"><span><?= $customer_info['name']; ?></span></div>
                <input type="hidden" name="customer_id" value="<?= $customer_info['customer_id'] ?>">
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label" for="product_id">选择产品：</label>
                <div class="col-sm-2">
                    <select id="product_id" name="product_id" class="form-control">
                        <option value="">请选择</option>
                        <?php if ($product_list && is_array($product_list)) {
                                foreach ($product_list as $key => $value) {
                        ?>
                        <option <?= ($value['product_id'] == $product_id) ? 'selected' : ''?> value="{$value['product_id']}">{$value['product_name']}</option>
                        <?php }} ?>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label" for="account_id">选择账号：</label>
                <div class="col-sm-4" id="account">
                    <label class="checkbox-inline">
                        <input type="checkbox" name="account_all" value="all" >全部
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label" for="">计费依据：</label>
                <div class="col-sm-2">
                    <select id="fee_basis" name="fee_basis" class="form-control">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="normal" <if condition="isset($product_id) && (in_array($product_id, $data_check_list) || $product_id == 210)">style="display: none;"</if>>
                <include file="Account@Customer/feeConfig/normal" />
            </div>
            <div class="data_check" <if condition="!isset($product_id) || !in_array($product_id, $data_check_list)">style="display: none;"</if>>
                <include file="Account@Customer/feeConfig/dataCheck" />
            </div>
            <div class="cuishou_short" <if condition="!isset($product_id) || $product_id != 210">style="display: none;"</if>>
                <include file="Account@Customer/feeConfig/cuishouShort" />
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">正式计费开始时间：</label>
                <div class="col-sm-4">
                    <input type="date" name="start_date" class="form-control" style="width: 300px" value="<?= isset($fee_info['start_date']) ? $fee_info['start_date'] : date('Y-m-d')?>">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-4">
                    <textarea id="remarks" rows="10" cols="35"><?= isset($fee_info['remarks']) ? $fee_info['remarks'] : '' ?></textarea>
                </div>
            </div>
            <div class="col-sm-offset-2 form-inline">
                <input type="hidden" value="{$f_id}" name="f_id" />
                <input type="button" onclick="save_fee()" class="btn btn-xm btn-primary" value="保 存" />
                <a href="{$Think.get.callback_url}" class="btn btn-xm btn-danger" style="margin-left: 50px" > 返 回</a>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    var data_info = $('#form').attr('data-info');
    var product_type = 'normal';
    var sub_product = [];
    var data_check_list = '{$data_check_list_json}';
    var data_check_list = JSON.parse(data_check_list);
    var product_list = '{$product_json}';
    var product_list = JSON.parse(product_list);

    data_info_list();
    function data_info_list()
    {
        if(data_info == 1) {
            $('input[type="radio"]').attr('disabled', true);
            $('select').attr('disabled', true);
            var account_id = '{$account_id}';
            var account_id = JSON.parse(account_id);
            var fee_basis = '{$fee_info['fee_basis']}';
            var product_id = $('#product_id').val();
            if (data_check_list.indexOf(product_id) > -1) { // 邦秒验
                product_type = 'data_check';
            } else if (product_id == 210) {
                product_type = 'cuishou_short';
                var product = product_list[210];
                sub_product = product.account_list;
            } else {
                product_type = 'normal';
            }
            product_info(account_id, fee_basis, product_id);
            $('input[type="checkbox"]').attr('disabled', true);
        }
    }


    // 选择账号 和 计费依据 根据产品联动

    product_change();
    function product_change()
    {
        $('#product_id').change(function() {
            var product_id = $(this).val().toString();
            // var product_id = parseInt(product_id);
            if (data_check_list.indexOf(product_id) > -1) { // 邦秒验
                $('.normal').hide();
                $('.cuishou_short').hide();
                $('.data_check').show();
                $('input[name="fee_method_data_check"][value="1"]').prop('checked', true);
                $('input[name="fee_time_rule_data_check"][value="1"]').prop('checked', true);
                product_type = 'data_check';
            } else if (product_id == '210') { // 邦信分快捷版
                $('.normal').hide();
                $('.cuishou_short').show();
                $('.data_check').hide();
                $('input[name="fee_method_cuishou_short"][value="1"]').prop('checked', true);
                $('input[name="fee_time_rule_cuishou_short"][value="1"]').prop('checked', true);
                $('input[name="fee_price_rule_cuishou_short"][value="1"]').prop('checked', true);
                product_type = 'cuishou_short';
            } else {
                $('.normal').show();
                $('.cuishou_short').hide();
                $('.data_check').hide();
                product_type = 'normal';
            }
            product_info('', '', product_id);
        });
    }

    // 产品账号列表
    function product_info(account, fee_basis, product_id)
    {
        var product_info = product_list[product_id];
        var check = '';
        if (account && account.length == product_info.account_list.length) {
            check = 'checked';
        }

        var html = '<label class="checkbox-inline">\
                        <input id="account_all" type="checkbox" '+check+' name="account_all" value="all" onclick="checkbox_all();" >全部\
                    </label>';
        $.each(product_info.account_list, function(index, content) {
            if (content != null) {
                var check = '';
                if (account && (account.indexOf(content.account_id) > -1)) {
                    check = 'checked';
                }
                html += '<label class="checkbox-inline">\
                            <input type="checkbox" name="account_id" '+check+' value="'+content.account_id+'" >'+content.account_name+'\
                        </label>';
            }
        });
        $('#account').html(html);
        if (product_type == 'cuishou_short') {
            sub_product = product_info.account_list;
            $('input[name="account_all"]').attr('disabled', true);
            $('input[name="account_id"]').attr('onclick', 'account_info(this)');
        }
        var html = '<option value="">请选择</option>';
        $.each(product_info.fee_config, function(index, content) {
            if (fee_basis) {
                var sel = content.val == fee_basis ? 'selected' : '';
            } else {
                var sel = (content.default == true) ? 'selected' : '';
            }
            html += '<option value="'+content.val+'" '+sel+'>'+content.cn_name+'</option>'
        });
        $('select[name="fee_basis"]').html(html);
    }

    // 全部选择
    function checkbox_all()
    {
        if ($('#account_all').is(":checked") == true) {
           $('input[name="account_id"]').prop("checked",true);
        } else {
           $('input[name="account_id"]').removeAttr("checked","checked");
        }
    }

    function account_info(obj)
    {
        var account_id = $(obj).val();
        var length = $('input[name="account_id"]:checked').length;
        if (length != 1) {
            alert('只能选择一个账号');
            $(obj).attr('checked', false);
            return false;
        }
        if (account_id != $('input[name="account_id"]:checked').val()) {
            return false;
        }
        var product_list_info = sub_product[account_id]['product_list'];
        fee_price_info_cuishou_short(product_type, product_list_info);
    }

    // 计费方式规则控制
    function fee_method(fee)
    {
        fee_method_normal(product_type, fee);
        if (product_type == 'data_check') {
            fee_method_data_check(product_type, fee);
        } else if (product_type == 'cuishou_short') {
            fee_method_cuishou_short(product_type, fee, 1);
        }
    }

    // 用量计费规则控制
    function fee_amount_rule(con_rule)
    {
        fee_amount_rule_normal(product_type, con_rule);
        if (product_type == 'data_check') {
            fee_amount_rule_data_check(product_type, con_rule);
        } else if (product_type == 'cuishou_short') {
            fee_amount_rule_cuishou_short(product_type, con_rule);
        }
    }

    // 累计阶梯设置
    function fee_step(product_id)
    {
        var product_id = (product_id == '') ? '' : '_'+product_id;
        var last = $('#step_list_'+product_type+product_id).find('tr:last').children('td').eq(1).find('input');
        var num = '';
        var last_num = $(last).val();
        if (last_num == '-1') {
            $(last).val("");
        } else {
            var num = parseInt(last_num)+1;
        }

        var list = $('.step_list_'+product_type+product_id+' thead tr').children("th").length;
        var html = '<tr>';
        for (var i = 1; i <= list; i++) {
            if (i == 1) {
                html += '<td><input type="text" value="'+num+'"></td>';
            } else if (i == 2) {
                html += '<td><input type="text" value="-1"></td>';
            } else if (i == list) {
                html += '<td><a href="javascript:;" onclick="del_step(this)">删除</a></td>';
            } else {
                html += '<td><input type="text" value=""></td>';
            }
        }
        html += '</tr>';
        $('#step_list_'+product_type+product_id).append(html);
    }

    // 累计阶梯和到达阶梯删除
    function del_step(obj)
    {
        if (!confirm('确定要删除该区间设置吗？')) {
            return false;
        }
        $(obj).parent().parent().remove();
    }

    // 到达阶梯设置
    function fee_reach(product_id)
    {
        var product_id = (product_id == '') ? '' : '_'+product_id;
        var list = $('.reach_list_'+product_type+product_id+' thead tr').children("th").length;
        var html = '<tr>';
        for (var i = 1; i <= list; i++) {
            if (i == list) {
                html += '<td><a href="javascript:;" onclick="del_step(this)">删除</a></td>';
            } else {
                html += '<td><input type="text" value=""></td>';
            }
        }
        html += '</tr>';
        $('#reach_list_'+product_type+product_id).append(html);
    }

    // 保存计费配置
    function save_fee()
    {
        var info = check_param();
        if (!info) {
            return false;
        }
        var id = $('input[name="id"]').val();
        info.f_id = $('input[name="f_id"]').val();
        DHB.ajax({
            url: "/Account/Customer/feeConfig/id/"+id,
            type: 'post',
            data: info,
            success: function(data) {
                alert(data.info);
                location.reload();
                return true;
            },
            error: function(data) {
                alert(data.info);
                return false;
            }
        });
    }

    //所有参数判断
    function check_param()
    {
        var customer_id = $('input[name="customer_id"]').val();
        var product_id = $('#product_id').val();
        var account_id = [];
        $('input[name="account_id"]:checked').each(function() {
            if($(this).prop("checked")) {
                 account_id.push($(this).val());
            }
        });
        var fee_basis = parseInt($('#fee_basis').val());
        var fee_method = parseInt($('input[name="fee_method_'+product_type+'"]:checked').val());
        var start_date = $('input[name="start_date"]').val();
        var remarks = $('#remarks').val();
        if (!product_id) {
            alert('请选择产品');
            return false;
        }
        if (product_type == 'cuishou_short' && account_id.length != 1) {
            alert('邦信分快捷版产品只能选择一个账号');
            return false;
        }
        if (account_id.length == 0) {
            alert('请至少选择一个账号');
            return false;
        }
        if (!fee_basis) {
            alert('请选择计费依据');
            return false;
        }
        if (!fee_method) {
            alert('请选择计费方式');
            return false;
        }
        if (fee_method == 1) {
            var info = check_time_param();
        } else if (fee_method == 2) {
            var info = check_amount_param();
        }
        if (!info) {
            return false;
        }
        if (remarks.length > 1000) {
            alert('备注不可超过1000个字符');
            return false;
        }
        var base = {"edit": data_info,"customer_id": customer_id, "product_id": product_id, "account_id": account_id, "fee_basis": fee_basis, "fee_method": fee_method, "start_date": start_date, "remarks": remarks, 'product_type': product_type};
        var param = $.extend(base, info);
        return param;
    }

    //按时间计费规则参数
    function check_time_param()
    {
        var fee_time_rule = parseInt($('input[name="fee_time_rule_'+product_type+'"]:checked').val());
        if (!fee_time_rule) {
            alert('请选择时间计费规则');
            return false;
        }
        var param = '';
        if (product_type == 'normal') {
            param = check_time_param_normal(product_type);
        } else if (product_type == 'data_check') {
            param = check_time_param_data_check(product_type);
        } else if (product_type == 'cuishou_short') {
            param = check_time_param_cuishou_short(product_type);
        }
        if (!param) {
            return false;
        }
        return $.extend({"fee_time_rule": fee_time_rule}, param);
    }

    // 用量计费判断
    function check_amount_param()
    {
        var fee_amount_rule = parseInt($('input[name="fee_amount_rule_'+product_type+'"]:checked').val());
        if (!fee_amount_rule) {
            alert('请选择用量计费规则');
            return false;
        }
        var param = '';
        if (fee_amount_rule == 1) { // 固定计价
            param = check_fixed_param();
        } else if (fee_amount_rule == 2) { // 累计阶梯
            param = check_step_param();
        } else if (fee_amount_rule == 3) { // 到达阶梯
            param = check_reach_param();
        }
        if (!param) {
            return false;
        }
        return $.extend({"fee_amount_rule": fee_amount_rule}, param);
    }

    // 固定价格参数
    function check_fixed_param()
    {
        if (product_type == 'normal') {
            var param = check_fixed_param_normal(product_type);
        } else if (product_type == 'data_check') {
            var param = check_fixed_param_data_check(product_type);
        } else if (product_type == 'cuishou_short') {
            var param = check_fixed_param_cuishou_short(product_type);
        }
        if (!param) {
            return false;
        }
        return param;
    }

    // 检查累进计费价格
    function check_step_param_normal(product_id)
    {
        var product_id = (product_id == '') ? '' : '_'+product_id;
        var one = parseInt($('#step_list_'+product_type+product_id+' tr:eq(0) td:eq(0)').find('input').val());
        var last = parseInt($('#step_list_'+product_type+product_id).find('tr:last').children('td').eq(1).find('input').val());
        if (one != 1) {
            alert('第一行左边界必须为1');
            return false;
        }
        if (last != -1) {
            alert('最后一行右边界必须为-1');
            return false;
        }
        var tr_list = $("#step_list_"+product_type+product_id).children("tr");
        var fee_price = [];
        for (var i = 0; i < tr_list.length; i++) {
            var fir = parseInt(tr_list.eq(i).children('td').eq(0).find('input').val());
            var pre = parseInt(tr_list.eq(i).children('td').eq(1).find('input').val());
            var next = parseInt(tr_list.eq(i+1).children('td').eq(0).find('input').val());
            if (i < (tr_list.length - 1) && next != (pre + 1)) {
                alert('前一行的右边界必须与后一行的左边界相差1');
                return false;
            }
            if (pre <= fir && pre != -1 || pre == NaN) {
                alert('请输入正确的右边界数值');
                return false;
            }
            var td_list = [];
            var sign = true;
            tr_list.eq(i).find('input').each(function(index) {
                var price = $.trim($(this).val());
                if (index != 0 && index != 1) {
                    if (!check_fee_price(price)) {
                        alert('请输入正确的单价');
                        sign = false;
                        return false;
                    }
                }
                td_list.push(price);
            });
            if (!sign) {
                return false;
            }
            fee_price.push(td_list);
        }
        if (fee_price.length == 0) {
            alert('请添加区间价格');
            return false;
        }
        return fee_price;
    }

    // 累计阶梯区间价格判断
    function check_step_param()
    {
        var fee_step_rule = parseInt($('input[name="fee_step_rule_'+product_type+'"]:checked').val());
        if (!fee_step_rule) {
            alert('请选择阶梯周期');
            return false;
        }
        var fee_price_rule = 0;
        if (product_type != 'normal') {
            fee_price_rule = check_fee_price_rule_data_check(product_type);
        }

        if (product_type == 'cuishou_short') {
            var account_id = $('input[name="account_id"]:checked').val();
            var sub_product_info = sub_product[account_id]['product_list'];
            var fee_price = check_step_param_cuishou_short(product_type, sub_product_info);
        } else {
            var fee_price = check_step_param_normal('');
        }
        if (!fee_price) {
            return false;
        }
        return {"fee_step_rule": fee_step_rule, "fee_price": fee_price, "fee_price_rule": fee_price_rule};
    }

    function check_reach_param_normal(product_id)
    {
        var product_id = (product_id == '') ? '' : '_'+product_id;
        var tr_list = $("#reach_list_"+product_type+product_id).children("tr");
        var fee_price = [];
        for (var i = 0; i < tr_list.length; i++) {
            var pre = parseInt(tr_list.eq(i).children('td').eq(0).find('input').val());
            var next = parseInt(tr_list.eq(i+1).children('td').eq(0).find('input').val());
            if (i < (tr_list.length - 1) && (pre >= next)) {
                alert('前一行的到达用量必须大于后一行的到达用量');
                return false;
            }
            var td_list = [];
            var sign = true;
            tr_list.eq(i).find("input").each(function(index) {
                var price = $.trim($(this).val());
                if (index != 0) {
                    if (!check_fee_price(price)) {
                        sign = false;
                        alert('请输入正确的单价');
                        return false;
                    }
                }
                td_list.push(price);
            });
            if (!sign) {
                return false;
            }
            fee_price.push(td_list);
        }
        if (fee_price.length == 0) {
            alert('请添加区间价格');
            return false;
        }
        return fee_price;
    }

    // 到达阶梯价格判断
    function check_reach_param()
    {
        var fee_step_rule = parseInt($('input[name="fee_step_rule_'+product_type+'"]:checked').val());
        if (!fee_step_rule) {
            alert('请选择阶梯周期');
            return false;
        }
        var fee_price_rule = 0;
        if (product_type != 'normal') {
            fee_price_rule = check_fee_price_rule_data_check(product_type);
        }
        if (product_type == 'cuishou_short') {
            var account_id = $('input[name="account_id"]:checked').val();
            var sub_product_info = sub_product[account_id]['product_list'];
            var fee_price = check_reach_param_cuishou_short(product_type, sub_product_info);
        } else {
            var fee_price = check_reach_param_normal('');
        }
        if (!fee_price) {
            return false;
        }
        return {"fee_step_rule": fee_step_rule, "fee_price": fee_price, 'fee_price_rule': fee_price_rule};
    }
</script>
</body>
</html>