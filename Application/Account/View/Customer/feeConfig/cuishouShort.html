<div class="form-group">
    <label class="col-sm-2 control-label">计费方式：</label>
    <div class="col-sm-4">
        <foreach name="fee_method" item="vo">
            <label class="radio-inline">
                <input type="radio" name="fee_method_cuishou_short" <if condition="$fee_info['fee_method'] == $key">checked</if> value="{$key}" onclick="fee_method({$key})">{$vo}
            </label>
        </foreach>
    </div>
</div>
<div id="fee_time_rule_cuishou_short" <if condition="isset($fee_info['fee_time_rule']) && empty($fee_info['fee_time_rule'])">style="display: none;"</if>>
    <div class="form-group">
        <label class="col-sm-2 control-label">时间计费规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_time_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_time_rule_cuishou_short" <if condition="$fee_info['fee_time_rule'] == $key">checked</if> value="{$key}">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">价格规则：</label>
            <div class="col-sm-4">
                <foreach name="fee_price_rule" item="vo">
                    <label class="radio-inline">
                        <input type="radio" name="fee_price_rule_cuishou_short" <if condition="(!isset($fee_info['fee_price_rule']) && $key == 1) || ($fee_info['fee_price_rule'] == $key && !$fee_info['fee_amount_rule'])">checked</if> value="{$key}" onclick="fee_price_cuishou_short({$key})" >{$vo}
                    </label>
                </foreach>
            </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">价格信息：</label>
        <div class="form-inline col-sm-8 fee_time_product_list">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
            <thead class="center">
                <tr>
                    <th>接口名称</th>
                    <?php
                    if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2 && $fee_info['fee_method'] == 1) {
                        foreach ($fee_flow_rule as $val) {
                            echo '<th>'.$val.'价格（元）</th>';
                        }
                    } else {
                        echo '<th>价格（元）</th>';
                    }
                    ?>
                </tr>
            </thead>
            <tbody>
                <?php
                    if (isset($fee_info['product_list']) && is_array($fee_info['product_list']) && $fee_info['fee_method'] == 1) {
                        $account = json_decode($account_id, true);
                        $product = $product_list[$product_id]['account_list'][$account[0]]['product_list'];
                        foreach ($product as $value) {
                ?>
                <tr>
                    <td>{$value['product_name']}
                        <input type="hidden" value="{$value['product_id']}" />
                    </td>
                    <?php
                        if (isset($fee_info['product_list'][$value['product_id']]) && is_array($fee_info['product_list'][$value['product_id']])) {
                            foreach ($fee_info['product_list'][$value['product_id']] as $vv) {
                    ?>
                    <td><input type="text" value="{$vv}" /></td>
                    <?php
                            }
                        } elseif (isset($fee_info['product_list'][$value['product_id']]) && is_string($fee_info['product_list'][$value['product_id']])) {
                    ?>
                    <td><input type="text" value="{$fee_info['product_list'][$value['product_id']]}" /></td>
                    <?php
                        }
                    ?>
                </tr>
                <?php
                        }
                    }
                ?>
            </tbody>
            </table>
        </div>
    </div>
</div>
<div id="fee_amount_rule_cuishou_short" <if condition="!isset($fee_info['fee_amount_rule']) || empty($fee_info['fee_amount_rule'])">style="display: none;"</if>>
    <div class="form-group">
        <label class="col-sm-2 control-label">用量计费规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_amount_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_amount_rule_cuishou_short" <if condition="isset($fee_info['fee_amount_rule']) && $fee_info['fee_amount_rule'] == $key">checked</if> value="{$key}" onclick="fee_amount_rule({$key})">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="ladder_rule_cuishou_short" <if condition="$fee_info['fee_amount_rule'] == 1">style="display: none;"</if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">阶梯周期：</label>
            <div class="col-sm-4">
                <foreach name="fee_step_rule" item="vo">
                    <label class="radio-inline">
                        <input type="radio" name="fee_step_rule_cuishou_short" <if condition="isset($fee_info['fee_step_rule']) && $fee_info['fee_step_rule'] == $key">checked</if> value="{$key}">{$vo}
                    </label>
                </foreach>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">价格规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_price_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_price_rule_amount_cuishou_short" <if condition="$fee_info['fee_price_rule'] == $key && !$fee_info['fee_time_rule']">checked</if> value="{$key}" onclick="fee_flow_rule_cuishou_short({$key})">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="amount_cuishou_short" <if condition="$fee_info['fee_amount_rule'] != 1"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格信息：</label>
            <div class="form-inline col-sm-8">
                <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>接口名称</th>
                        <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2 && $fee_info['fee_amount_rule'] == 1) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<th>'.$val.'价格（元）</th>';
                            }
                        } else {
                            echo '<th>价格（元）</th>';
                        }
                        ?>
                    </tr>
                </thead>
                <tbody>
                <?php
                if (isset($fee_info['product_list']) && is_array($fee_info['product_list']) && $fee_info['fee_amount_rule'] == 1) {
                    $account = json_decode($account_id, true);
                    $product = $product_list[$product_id]['account_list'][$account[0]]['product_list'];
                    foreach ($product as $value) {
                ?>
                <tr>
                    <td>{$value['product_name']}
                        <input type="hidden" value="{$value['product_id']}" />
                    </td>
                    <?php
                    if (isset($fee_info['product_list'][$value['product_id']]) && is_array($fee_info['product_list'][$value['product_id']])) {
                        foreach ($fee_info['product_list'][$value['product_id']] as $vv) {
                    ?>
                    <td><input type="text" value="{$vv}" /></td>
                    <?php
                        }
                    } elseif (isset($fee_info['product_list'][$value['product_id']]) && is_string($fee_info['product_list'][$value['product_id']])) {
                    ?>
                    <td><input type="text" value="{$fee_info['product_list'][$value['product_id']]}" /></td>
                    <?php
                    }
                ?>
                </tr>
                <?php
                    }
                }
                ?>
                </tbody>
                </table>
                </table>
            </div>
        </div>
    </div>
    <div id="fee_step_rule_cuishou_short">
        <?php
        if (isset($fee_info['product_list']) && is_array($fee_info['product_list']) && $fee_info['fee_amount_rule'] == 2) {
            $account = json_decode($account_id, true);
            $product = $product_list[$product_id]['account_list'][$account[0]]['product_list'];
            foreach ($product as $value) {
        ?>
        <div class="form-group">
            <label class="col-sm-2 control-label">{$value['product_name']}价格区间：</label>
            <div class="col-sm-4">
                <a onclick="fee_step({$value['product_id']})" class="btn btn-success btn-xs">添加</a>
            </div>
        </div>
        <div class="form-group step_list_cuishou_short_{$value['product_id']}">
            <div class="col-sm-offset-1 col-sm-8">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>左边界</th>
                        <th>右边界</th>
                    <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2 && $fee_info['fee_amount_rule'] == 2) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<th>'.$val.'单价（元）</th>';
                            }
                        } else {
                            echo '<th>单价（元）</th>';
                        }
                    ?>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="step_list_cuishou_short_{$value['product_id']}">
                    <?php
                    if (isset($fee_info['product_list'][$value['product_id']]) && is_array($fee_info['product_list'][$value['product_id']])) {
                        foreach ($fee_info['product_list'][$value['product_id']] as $key => $val) {
                    ?>
                    <tr>
                        <?php foreach ($val as $vv) { ?>
                            <td><input type="text" value="{$vv}"></td>
                        <?php }?>
                        <td><a href="javascript:;" onclick="del_step(this)">删除</a></td>
                    </tr>
                    <?php }} ?>
                </tbody>
            </table>
            </div>
        </div>
        <?php }} ?>
    </div>
    <div id="fee_step_reach_cuishou_short">
        <?php
        if (isset($fee_info['product_list']) && is_array($fee_info['product_list']) && $fee_info['fee_amount_rule'] == 3) {
            $account = json_decode($account_id, true);
            $product = $product_list[$product_id]['account_list'][$account[0]]['product_list'];
            foreach ($product as $value) {
        ?>
        <div class="form-group">
            <label class="col-sm-2 control-label">{$value['product_name']}价格区间（元）：</label>
            <div class="col-sm-4">
                <a href="javascript:;" onclick="fee_reach({$value['product_id']})" class="btn btn-success btn-xs">添加</a>
            </div>
        </div>
        <div class="form-group reach_list_cuishou_short_{$value['product_id']}">
            <div class="col-sm-offset-1 col-sm-8">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>到达用量</th>
                        <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2 && $fee_info['fee_amount_rule'] == 3) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<th>'.$val.'单价（元）</th>';
                            }
                        } else {
                            echo '<th>单价（元）</th>';
                        }
                        ?>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="reach_list_cuishou_short_{$value['product_id']}">
                    <?php
                    if (isset($fee_info['product_list'][$value['product_id']]) && is_array($fee_info['product_list'][$value['product_id']])) {
                        foreach ($fee_info['product_list'][$value['product_id']] as $key => $val) {
                    ?>
                    <tr>
                        <?php foreach ($val as $kk => $vv) { ?>
                        <td><input type="text" value="{$vv}"></td>
                        <?php } ?>
                        <td><a href="javascript:;" onclick="del_step(this)">删除</a></td>
                    </tr>
                    <?php }}?>
                </tbody>
            </table>
            </div>
        </div>
        <?php }} ?>
    </div>
</div>
<script type="text/javascript">
    var flow_list = '{$fee_flow_rule_json}';
        flow_list = JSON.parse(flow_list);
    var sub_product_list;

    // 初始值
    function fee_price_info_cuishou_short(product_type, product_list)
    {
        sub_product_list = product_list;
        var fee_method_rule = $('input[name="fee_method_'+product_type+'"]:checked').val();
        var fee_price_rule = $('input[name="fee_price_rule_'+product_type+'"]:checked').val();
        var fee_price_rule_amount = $('input[name="fee_price_rule_amount_'+product_type+'"]:checked').val();
        var fee_amount_rule = $('input[name="fee_amount_rule_'+product_type+'"]:checked').val();
        if (fee_method_rule == 1) {
            fee_method_cuishou_short(product_type, fee_method_rule, fee_price_rule);
        }
        if (fee_amount_rule == 1) {
            add_amount_flow_rule_cuishou_short(product_type, fee_price_rule_amount);
        }
        if (fee_amount_rule == 2) {
            add_step_flow_table_cuishou_short(product_type, fee_price_rule_amount);
        }
        if (fee_amount_rule == 3) {
            add_reach_flow_table_cuishou_short(product_type, fee_price_rule_amount);
        }
    }

    // 计费方式规则控制
    function fee_method_cuishou_short(product_type, fee, price_rule)
    {
        if (fee == 1) { // 时间计费规则
            add_time_flow_table_cuishou_short(product_type, price_rule, '.fee_time_product_list table');
        } else if (fee == 2) { // 用量计费
            $('input[name="fee_price_rule_'+product_type+'"][value="1"]').prop('checked', true);
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).show();
            $('.amount_flow_'+product_type).hide();
            $('#fee_flow_'+product_type).hide();
            add_time_flow_table_cuishou_short(product_type, 1, '.amount_cuishou_short table');
        }
    }

    // 用量计费规则控制
    function fee_amount_rule_cuishou_short(product_type, con_rule)
    {
        if (con_rule == 1) { // 固定单价
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).show();
            add_amount_flow_rule_cuishou_short(product_type, 1);
        } else if (con_rule == 2) { // 累进阶梯
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).hide();
            add_step_flow_table_cuishou_short(product_type, 1);
        } else if (con_rule == 3) { // 到达阶梯
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).hide();
            add_reach_flow_table_cuishou_short(product_type, 1)
        }
    }

    // 按时间中价格规则控制
    function fee_price_cuishou_short(fee)
    {
        add_time_flow_table_cuishou_short('product_type', fee, '.fee_time_product_list table');
    }

    // 按用量中价格规则控制
    function fee_flow_rule_cuishou_short(price_rule)
    {
        var product_type = 'cuishou_short';
        var fee_amount_rule = $('input[name="fee_amount_rule_'+product_type+'"]:checked').val();
        if (fee_amount_rule == 1) {
            add_amount_flow_rule_cuishou_short(product_type, price_rule);
        } else if (fee_amount_rule == 2) {
            add_step_flow_table_cuishou_short(product_type, price_rule);
        } else if (fee_amount_rule == 3) {
            add_reach_flow_table_cuishou_short(product_type, price_rule);
        }
    }

    // 固定价格价格规则控制
    function add_amount_flow_rule_cuishou_short(product_type, price_rule)
    {
        add_time_flow_table_cuishou_short(product_type, price_rule, '.amount_cuishou_short table')
    }

    function add_time_flow_table_cuishou_short(product_type, price_rule, class_name)
    {
        var html = '<thead class="center"><tr><th>接口名称</th>';
        if (price_rule == 2) {
            $.each(flow_list, function (index, content) {
                html += '<th>'+content+'价格（元）</th>';
            });
        } else {
            html += '<th>价格（元）</th>';
        }
        html += '</tr></thead>';
        html += '<tbody>';
        if (sub_product_list !==  undefined) {
            $.each(sub_product_list, function (index, content) {
                html += '<tr><td>'+content.product_name+'<input type="hidden" value="'+content.product_id+'"></td>';
                if (price_rule == 2) {
                    $.each(flow_list, function (i, mm) {
                        html += '<td><input type="text" value=""></td>';
                    });
                } else {
                    html += '<td><input type="text" value=""></td>';
                }
                html += '</tr>';
            });
        }
        html += '</tbody>';
        $(class_name).html(html);
    }

    // 累进阶梯价格规则控制
    function add_step_flow_table_cuishou_short(product_type, price_rule)
    {
        var html = '';
        if (sub_product_list == undefined) {
            return false;
        }
        $.each(sub_product_list, function (index, content) {
            html += '<div class="form-group">\
                <label class="col-sm-2 control-label">'+content.product_name+'价格区间：</label>\
                <div class="col-sm-4">\
                    <a onclick="fee_step('+content.product_id+')" class="btn btn-success btn-xs">添加</a>\
                </div>\
            </div>\
            <div class="form-group step_list_cuishou_short_'+content.product_id+'">\
                <div class="col-sm-offset-1 col-sm-8">\
                <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">\
                    <thead class="center">\
                        <tr>\
                            <th>左边界</th>\
                            <th>右边界</th>';
            if (price_rule == 2) {
                $.each(flow_list, function (index, content) {
                    html += '<th>'+content+'单价（元）</th>';
                });
            } else {
                html += '<th>单价（元）</th>';
            }
            html += '       <th>操作</th>\
                        </tr>\
                    </thead>\
                    <tbody id="step_list_cuishou_short_'+content.product_id+'">\
                        <tr>\
                            <td><input type="text" value="1"></td>\
                            <td><input type="text" value="-1"></td>';
            if (price_rule == 2) {
                $.each(flow_list, function (index, content) {
                    html += '<td><input type="text" value=""></td>';
                });
            } else {
                html += '<td><input type="text" value=""></td>';
            }
            html += '       <td></td>\
                        </tr>\
                    </tbody>\
                </table>\
                </div>\
            </div>';
        });
        $('#fee_step_rule_cuishou_short').html(html);
    }

    // 到达阶梯价格规则控制
    function add_reach_flow_table_cuishou_short(product_type, price_rule)
    {
        var html = '';
        if (sub_product_list == undefined) {
            return false;
        }
        $.each(sub_product_list, function (index, content) {
            html += '<div class="form-group">\
                <label class="col-sm-2 control-label">'+content.product_name+'价格区间：</label>\
                <div class="col-sm-4">\
                    <a onclick="fee_reach('+content.product_id+')" class="btn btn-success btn-xs">添加</a>\
                </div>\
            </div>\
            <div class="form-group reach_list_cuishou_short_'+content.product_id+'">\
                <div class="col-sm-offset-1 col-sm-8">\
                <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">\
                    <thead class="center">\
                        <tr>\
                            <th>到达用量</th>';
            if (price_rule == 2) {
                $.each(flow_list, function (index, content) {
                    html += '<th>'+content+'单价（元）</th>';
                });
            } else {
                html += '<th>单价（元）</th>';
            }
            html += '       <th>操作</th>\
                        </tr>\
                    </thead>\
                    <tbody id="reach_list_cuishou_short_'+content.product_id+'">\
                        <tr>\
                            <td><input type="text" value="0"></td>';
            if (price_rule == 2) {
                $.each(flow_list, function (index, content) {
                    html += '<td><input type="text" value=""></td>';
                });
            } else {
                html += '<td><input type="text" value=""></td>';
            }
            html += '       <td></td>\
                        </tr>\
                    </tbody>\
                </table>\
                </div>\
            </div>';
        });
        $('#fee_step_reach_cuishou_short').html(html);
    }

    // 按时间计费规则参数
    function check_time_param_cuishou_short(product_type)
    {
        var fee_price_rule = parseInt($('input[name="fee_price_rule_'+product_type+'"]:checked').val());
        if (!fee_price_rule || fee_price_rule == NaN) {
            alert('请选择价格规则');
            return false;
        }
        var fee_price = check_flow_param_cuishou_short('.fee_time_product_list table tbody', fee_price_rule);
        if (!fee_price) {
            return false;
        }

        return {"fee_price_rule": fee_price_rule, "fee_price": fee_price};
    }

    // 固定价格参数
    function check_fixed_param_cuishou_short(product_type)
    {
        var fee_price_rule = check_fee_price_rule_cuishou_short(product_type);
        if (!fee_price_rule) {
            return false;
        }
        var fee_price = check_flow_param_cuishou_short('.amount_'+product_type+' table tbody', fee_price_rule);
        if (!fee_price) {
            return false;
        }
        return {"fee_price_rule": fee_price_rule, "fee_price": fee_price};
    }

    // 按时间、固定价格运营商参数
    function check_flow_param_cuishou_short(class_name, fee_price_rule)
    {
        var fee_price = {};
        var sign = true;
        $(class_name).find('tr').each(function(index_tr) {
            $(this).find('input').each(function (index) {
                var price = $.trim($(this).val());
                if (index == 0) {
                    fee_price[price] = (fee_price_rule == 1) ? 0 : [];
                } else {
                    if (!check_fee_price(price)) {
                        sign = false;
                        alert('请输入相应的价格');
                        return false;
                    }
                    var key = $(class_name).find('tr').eq(index_tr).find('input').eq(0).val();
                    if (fee_price_rule == 1) {
                        fee_price[key] = price;
                    } else {
                        fee_price[key].push(price);
                    }
                }
            });
        });
        if (!sign) {
            return false;
        }
        if (fee_price.length == 0) {
            alert('请输入相应的价格');
            return false;
        }
        return fee_price;
    }

    // 检查价格规则
    function check_fee_price_rule_cuishou_short(product_type)
    {
        var fee_price_rule = parseInt($('input[name="fee_price_rule_amount_'+product_type+'"]:checked').val());
        if (!fee_price_rule || fee_price_rule == NaN) {
            alert('请选择价格规则');
            return false;
        }
        return fee_price_rule;
    }

    // 检查累进价格规则
    function check_step_param_cuishou_short(product_type, sub_product)
    {
        var fee_price = {};
        var count = 0;
        var total = $('#fee_step_rule_'+product_type).find('table').length;
        $.each(sub_product, function(index, content) {
            var price = check_step_param_normal(content.product_id);
            if (!price) {
                return false;
            }
            fee_price[content.product_id] = price;
            count++;
        });
        if (count == 0 || total != count) {
            return false;
        }
        return fee_price;
    }

    // 检查到达阶梯价格规则
    function check_reach_param_cuishou_short(product_type, sub_product)
    {
        var fee_price = {};
        var count = 0;
        var total = $('#fee_step_reach_'+product_type).find('table').length;
        $.each(sub_product, function(index, content) {
            var price = check_reach_param_normal(content.product_id);
            if (!price) {
                return false;
            }
            fee_price[content.product_id] = price;
            count++;
        });
        if (count == 0 || count != total) {
            return false;
        }
        return fee_price;
    }

</script>