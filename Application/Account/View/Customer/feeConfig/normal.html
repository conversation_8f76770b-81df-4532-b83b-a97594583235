<div class="form-group">
    <label class="col-sm-2 control-label">计费方式：</label>
    <div class="col-sm-4">
        <foreach name="fee_method" item="vo">
            <label class="radio-inline">
                <input type="radio" name="fee_method_normal" <if condition="$key == 1 || ($fee_info['fee_method'] == $key)">checked</if> value="{$key}" onclick="fee_method({$key})">{$vo}
            </label>
        </foreach>
    </div>
</div>
<div id="fee_time_rule_normal" <if condition="isset($fee_info['fee_time_rule']) && empty($fee_info['fee_time_rule'])">style="display: none;"</if>>
    <div class="form-group">
        <label class="col-sm-2 control-label">时间计费规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_time_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_time_rule_normal" <if condition="$key == 1 || ($fee_info['fee_time_rule'] == $key)">checked</if> value="{$key}">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">价格（元）：</label>
        <div class="form-inline col-sm-8">
            <input type="text" class="form-control" name="time_price_normal" value="{$fee_info['fee_price']}">
        </div>
    </div>
</div>
<div id="fee_amount_rule_normal" <if condition="!isset($fee_info['fee_amount_rule']) || empty($fee_info['fee_amount_rule'])">style="display: none;"</if>>
    <div class="form-group">
        <label class="col-sm-2 control-label">用量计费规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_amount_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_amount_rule_normal" <if condition="isset($fee_info['fee_amount_rule']) && $fee_info['fee_amount_rule'] == $key">checked</if> value="{$key}" onclick="fee_amount_rule({$key})">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="ladder_rule_normal" <if condition="$fee_info['fee_amount_rule'] == 1">style="display: none;"</if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">阶梯周期：</label>
            <div class="col-sm-4">
                <foreach name="fee_step_rule" item="vo">
                    <label class="radio-inline">
                        <input type="radio" name="fee_step_rule_normal" <if condition="isset($fee_info['fee_step_rule']) && $fee_info['fee_step_rule'] == $key">checked</if> value="{$key}">{$vo}
                    </label>
                </foreach>
            </div>
        </div>
    </div>
    <div id="fee_fixed_normal" <if condition="empty($fee_info) || $fee_info['fee_amount_rule'] != 1"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格（元）：</label>
            <div class="form-inline col-sm-8">
                <input type="text" class="form-control" name="amount_price_normal" value="{$fee_info['fee_price']}">
            </div>
        </div>
    </div>
    <div id="fee_step_rule_normal" <if condition="$fee_info['fee_amount_rule'] != 2"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格区间（元）：</label>
            <div class="col-sm-4">
                <a onclick="fee_step('')" class="btn btn-success btn-xs">添加</a>
            </div>
        </div>
        <div class="form-group step_list_normal">
            <div class="col-sm-offset-1 col-sm-6">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>左边界</th>
                        <th>右边界</th>
                        <th>单价（元）</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="step_list_normal">
                    <?php
                    if (isset($fee_info['fee_price']) && ($fee_info['fee_amount_rule'] == 2) && is_array($fee_info['fee_price']) && !$fee_info['fee_price_rule']) {
                        foreach ($fee_info['fee_price'] as $key => $val) {
                    ?>
                    <tr>
                        <td><input type="text" value="{$val[0]}"></td>
                        <td><input type="text" value="{$val[1]}"></td>
                        <td><input type="text" value="{$val[2]}"></td>
                        <td><a href="javascript:;" onclick="del_step(this)">删除</a></td>
                    </tr>
                    <?php }} else { ?>
                    <tr>
                        <td><input type="text" value="1"></td>
                        <td><input type="text" value="-1"></td>
                        <td><input type="text" value=""></td>
                        <td></td>
                    </tr>
                    <?php } ?>
                </tbody>
            </table>
            </div>
        </div>
    </div>
    <div id="fee_step_reach_normal" <if condition="$fee_info['fee_amount_rule'] != 3"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格区间（元）：</label>
            <div class="col-sm-4">
                <a href="javascript:;" onclick="fee_reach('')" class="btn btn-success btn-xs">添加</a>
            </div>
        </div>
        <div class="form-group reach_list_normal">
            <div class="col-sm-offset-1 col-sm-6">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>到达用量</th>
                        <th>单价（元）</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="reach_list_normal">
                    <?php
                    if (isset($fee_info['fee_price']) && ($fee_info['fee_amount_rule'] == 3) && is_array($fee_info['fee_price']) && !$fee_info['fee_price_rule']) {
                        foreach ($fee_info['fee_price'] as $key => $val) {
                    ?>
                    <tr>
                        <td><input type="text" value="{$val[0]}"></td>
                        <td><input type="text" value="{$val[1]}"></td>
                        <td><a href="javascript:;" onclick="del_step(this)">删除</a></td>
                    </tr>
                    <?php }} else { ?>
                    <tr>
                        <td><input type="text" value="0"></td>
                        <td><input type="text" value=""></td>
                        <td></td>
                    </tr>
                    <?php }?>
                </tbody>
            </table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 计费方式控制
    function fee_method_normal(product_type, fee)
    {
        if (fee == 1) { // 时间计费规则
            $('#fee_time_rule_'+product_type).show();
            $('#fee_amount_rule_'+product_type).hide();
        } else if (fee == 2) { // 用量计费
            $('#fee_time_rule_'+product_type).hide();
            $('#fee_amount_rule_'+product_type).show();
            $('input[name="fee_amount_rule_'+product_type+'"][value="1"]').prop('checked', true);
            $('#fee_fixed_'+product_type).show();
            $('#fee_step_rule_'+product_type).hide();
            $('#fee_step_reach_'+product_type).hide();
            $('.ladder_rule_'+product_type).hide();
        }
    }

    // 用量计费规则控制
    function fee_amount_rule_normal(product_type, con_rule)
    {
        if (con_rule == 1) { // 固定单价
            $('#fee_fixed_'+product_type).show();
            $('#fee_step_rule_'+product_type).hide();
            $('#fee_step_reach_'+product_type).hide();
            $('.ladder_rule_'+product_type).hide();
        } else if (con_rule == 2) { // 累计阶梯
            $('#fee_fixed_'+product_type).hide();
            $('input[name="fee_step_rule_'+product_type+'"][value="1"]').prop('checked', true);
            $('#fee_step_rule_'+product_type).show();
            $('#fee_step_reach_'+product_type).hide();
            $('.ladder_rule_'+product_type).show();
            add_step_flow_table(product_type, 1);
        } else if (con_rule == 3) { // 到达阶梯
            $('#fee_fixed_'+product_type).hide();
            $('#fee_step_rule_'+product_type).hide();
            $('#fee_step_reach_'+product_type).show();
            $('input[name="fee_step_rule_'+product_type+'"][value="1"]').prop('checked', true);
            $('.ladder_rule_'+product_type).show();
            add_reach_flow_table(product_type, 1);
        }
    }

    // 按时间计费规则参数
    function check_time_param_normal(product_type)
    {
        var price = $('input[name="time_price_'+product_type+'"]').val();
        if (!check_fee_price(price)) {
            alert('请输入价格');
            return false;
        }
        return {"fee_price": price};
    }

    function check_fixed_param_normal(product_type)
    {
        var amount_price = $('input[name="amount_price_'+product_type+'"]').val();
        if (!check_fee_price(amount_price)) {
            alert('请输入正确的固定价格');
            return false;
        }
        return {"fee_price": amount_price};
    }

    // 正则检查价格
    function check_fee_price(price)
    {
        var reg = /^(0(\.\d{1,6})?|[1-9](\d)*)(\.\d{1,6})?$/;
        if (!reg.test(price)) {
            return false;
        }
        return true;
    }

</script>