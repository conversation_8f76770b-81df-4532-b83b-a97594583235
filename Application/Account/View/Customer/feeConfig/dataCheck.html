<div class="form-group">
    <label class="col-sm-2 control-label">计费方式：</label>
    <div class="col-sm-4">
        <foreach name="fee_method" item="vo">
            <label class="radio-inline">
                <input type="radio" name="fee_method_data_check" <if condition="$fee_info['fee_method'] == $key">checked</if> value="{$key}" onclick="fee_method({$key})">{$vo}
            </label>
        </foreach>
    </div>
</div>
<div id="fee_time_rule_data_check" <if condition="isset($fee_info['fee_time_rule']) && empty($fee_info['fee_time_rule'])">style="display: none;"</if>>
    <div class="form-group">
        <label class="col-sm-2 control-label">时间计费规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_time_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_time_rule_data_check" <if condition="$fee_info['fee_time_rule'] == $key">checked</if> value="{$key}">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">价格规则：</label>
            <div class="col-sm-4">
                <foreach name="fee_price_rule" item="vo">
                    <label class="radio-inline">
                        <input type="radio" name="fee_price_rule_data_check" <if condition="(!isset($fee_info['fee_price_rule']) && $key == 1) || ($fee_info['fee_price_rule'] == $key && !$fee_info['fee_amount_rule'])">checked</if> value="{$key}">{$vo}
                    </label>
                </foreach>
            </div>
    </div>
    <div class="fee_time_data_check" <if condition="isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] != 1">style="display: none;"</if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格（元）：</label>
            <div class="form-inline col-sm-8">
                <input type="text" class="form-control" name="time_price_data_check" value="{$fee_info['fee_price']}">
            </div>
        </div>
    </div>
    <div class="fee_flow_data_check" <if condition="!isset($fee_info['fee_price_rule']) || $fee_info['fee_price_rule'] != 2">style="display: none;"</if>>
        <foreach name="fee_flow_rule" item="vo">
            <div class="form-group">
                <label class="col-sm-2 control-label">{$vo}价格（元）：</label>
                <div class="form-inline col-sm-8">
                    <input type="text" class="form-control" name="" value="{$fee_info['fee_price'][$key-1]}">
                </div>
            </div>
        </foreach>
    </div>
</div>
<div id="fee_amount_rule_data_check" <if condition="!isset($fee_info['fee_amount_rule']) || empty($fee_info['fee_amount_rule'])">style="display: none;"</if>>
    <div class="form-group">
        <label class="col-sm-2 control-label">用量计费规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_amount_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_amount_rule_data_check" <if condition="isset($fee_info['fee_amount_rule']) && $fee_info['fee_amount_rule'] == $key">checked</if> value="{$key}" onclick="fee_amount_rule({$key})">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="ladder_rule_data_check" <if condition="$fee_info['fee_amount_rule'] == 1">style="display: none;"</if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">阶梯周期：</label>
            <div class="col-sm-4">
                <foreach name="fee_step_rule" item="vo">
                    <label class="radio-inline">
                        <input type="radio" name="fee_step_rule_data_check" <if condition="isset($fee_info['fee_step_rule']) && $fee_info['fee_step_rule'] == $key">checked</if> value="{$key}">{$vo}
                    </label>
                </foreach>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">价格规则：</label>
        <div class="col-sm-4">
            <foreach name="fee_price_rule" item="vo">
                <label class="radio-inline">
                    <input type="radio" name="fee_price_rule_amount_data_check" <if condition="$fee_info['fee_price_rule'] == $key && !$fee_info['fee_time_rule']">checked</if> value="{$key}" onclick="fee_flow_rule({$key})">{$vo}
                </label>
            </foreach>
        </div>
    </div>
    <div class="amount_data_check" <if condition="$fee_info['fee_amount_rule'] != 1 || ($fee_info['fee_amount_rule'] == 1 && $fee_info['fee_price_rule'] != 1)"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格（元）：</label>
            <div class="form-inline col-sm-8">
                <input type="text" class="form-control" name="amount_price_data_check" value="{$fee_info['fee_price']}">
            </div>
        </div>
    </div>
    <div class="amount_flow_data_check" <if condition="$fee_info['fee_amount_rule'] != 1 || ($fee_info['fee_amount_rule'] == 1 && $fee_info['fee_price_rule'] != 2)"> style="display:none" </if>>
        <foreach name="fee_flow_rule" item="vo">
            <div class="form-group">
                <label class="col-sm-2 control-label">{$vo}价格（元）：</label>
                <div class="form-inline col-sm-8">
                    <input type="text" class="form-control" name="" value="{$fee_info['fee_price'][$key-1]}">
                </div>
            </div>
        </foreach>
    </div>
    <div id="fee_step_rule_data_check" <if condition="$fee_info['fee_amount_rule'] != 2"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格区间（元）：</label>
            <div class="col-sm-4">
                <a onclick="fee_step('')" class="btn btn-success btn-xs">添加</a>
            </div>
        </div>
        <div class="form-group step_list_data_check">
            <div class="col-sm-offset-1 col-sm-8">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>左边界</th>
                        <th>右边界</th>
                    <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<th>'.$val.'单价（元）</th>';
                            }
                        } else {
                            echo '<th>单价（元）</th>';
                        }
                    ?>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="step_list_data_check">
                    <?php
                    if (isset($fee_info['fee_price']) && ($fee_info['fee_amount_rule'] == 2) && is_array($fee_info['fee_price'])) {
                        foreach ($fee_info['fee_price'] as $key => $val) {
                    ?>
                    <tr>
                        <?php foreach ($val as $vv) { ?>
                            <td><input type="text" value="{$vv}"></td>
                        <?php }?>
                        <td><a href="javascript:;" onclick="del_step(this)">删除</a></td>
                    </tr>
                    <?php }} else { ?>
                    <tr>
                        <td><input type="text" value="1"></td>
                        <td><input type="text" value="-1"></td>
                        <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<td><input type="text" value=""></td>';
                            }
                        } else {
                            echo '<td><input type="text" value=""></td>';
                        }
                        ?>
                        <td></td>
                    </tr>
                    <?php } ?>
                </tbody>
            </table>
            </div>
        </div>
    </div>
    <div id="fee_step_reach_data_check" <if condition="$fee_info['fee_amount_rule'] != 3"> style="display:none" </if>>
        <div class="form-group">
            <label class="col-sm-2 control-label">价格区间（元）：</label>
            <div class="col-sm-4">
                <a href="javascript:;" onclick="fee_reach('')" class="btn btn-success btn-xs">添加</a>
            </div>
        </div>
        <div class="form-group reach_list_data_check">
            <div class="col-sm-offset-1 col-sm-8">
            <table class="table table-hover table-striped table-bordered" style="table-layout: fixed">
                <thead class="center">
                    <tr>
                        <th>到达用量</th>
                        <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<th>'.$val.'单价（元）</th>';
                            }
                        } else {
                            echo '<th>单价（元）</th>';
                        }
                        ?>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="reach_list_data_check">
                    <?php
                    if (isset($fee_info['fee_price']) && ($fee_info['fee_amount_rule'] == 3) && is_array($fee_info['fee_price'])) {
                        foreach ($fee_info['fee_price'] as $key => $val) {
                    ?>
                    <tr>
                        <?php foreach ($val as $kk => $vv) { ?>
                        <td><input type="text" value="{$vv}"></td>
                        <?php } ?>
                        <td><a href="javascript:;" onclick="del_step(this)">删除</a></td>
                    </tr>
                    <?php }} else { ?>
                    <tr>
                        <td><input type="text" value="0"></td>
                        <?php
                        if (isset($fee_info['fee_price_rule']) && $fee_info['fee_price_rule'] == 2) {
                            foreach ($fee_flow_rule as $val) {
                                echo '<td><input type="text" value=""></td>';
                            }
                        } else {
                            echo '<td><input type="text" value=""></td>';
                        }
                        ?>
                        <td></td>
                    </tr>
                    <?php }?>
                </tbody>
            </table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var flow_list = '{$fee_flow_rule_json}';
        flow_list = JSON.parse(flow_list);

    // 计费方式规则控制
    function fee_method_data_check(product_type, fee)
    {
        if (fee == 1) { // 时间计费规则
            $('.fee_time_'+product_type).show();
            $('.fee_flow_'+product_type).hide();
        } else if (fee == 2) { // 用量计费
            $('input[name="fee_price_rule_'+product_type+'"][value="1"]').prop('checked', true);
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).show();
            $('.amount_flow_'+product_type).hide();
            $('#fee_flow_'+product_type).hide();
        }
    }

    // 用量计费规则控制
    function fee_amount_rule_data_check(product_type, con_rule)
    {
        if (con_rule == 1) { // 固定单价
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).show();
            $('.amount_flow_'+product_type).hide();
        } else if (con_rule == 2) { // 累进阶梯
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).hide();
            $('.amount_flow_'+product_type).hide();
        } else if (con_rule == 3) { // 到达阶梯
            $('input[name="fee_price_rule_amount_'+product_type+'"][value="1"]').prop('checked', true);
            $('.amount_'+product_type).hide();
            $('.amount_flow_'+product_type).hide();
        }
    }

    // 按时间中价格规则控制
    $('input[name="fee_price_rule_data_check"]').click(function() {
        var product_type = 'data_check';
        var price_rule = $(this).val();
        if (price_rule == 1) {
            $('.fee_time_'+product_type).show();
            $('.fee_flow_'+product_type).hide();
        } else if (price_rule == 2) {
            $('.fee_time_'+product_type).hide();
            $('.fee_flow_'+product_type).show();
        }
    });

    // 按用量中价格规则控制
    function fee_flow_rule(price_rule)
    {
        var product_type = 'data_check';
        var fee_amount_rule = $('input[name="fee_amount_rule_'+product_type+'"]:checked').val();
        if (fee_amount_rule == 1) {
            add_amount_flow_rule(product_type, price_rule);
        } else if (fee_amount_rule == 2) {
            add_step_flow_table(product_type, price_rule);
        } else if (fee_amount_rule == 3) {
            add_reach_flow_table(product_type, price_rule);
        }
    }

    // 固定价格价格规则控制
    function add_amount_flow_rule(product_type, price_rule)
    {
        if (price_rule == 2) {
            $('.amount_'+product_type).hide();
            $('.amount_flow_'+product_type).show();
        } else {
            $('.amount_'+product_type).show();
            $('.amount_flow_'+product_type).hide();
        }
    }

    // 累进阶梯价格规则控制
    function add_step_flow_table(product_type, price_rule)
    {
        var html = '<thead class="center"><tr><th>左边界</th><th>右边界</th>';
        if (price_rule == 2) {
            $.each(flow_list, function (index, content) {
                html += '<th>'+content+'单价（元）</th>';
            });
        } else {
            html += '<th>单价（元）</th>';
        }
        html += '<th>操作</th></tr></thead>';
        html += '<tbody id="step_list_'+product_type+'">';
        html += '<tr><td><input type="text" value="1"></td><td><input type="text" value="-1"></td>';
        if (price_rule == 2) {
            $.each(flow_list, function (index, content) {
                html += '<td><input type="text" value=""></td>';
            });
        } else {
            html += '<td><input type="text" value=""></td>';
        }
        html += '<td></td></tr>';

        $('.step_list_'+product_type+' table').html(html);
    }

    // 到达阶梯价格规则控制
    function add_reach_flow_table(product_type, price_rule)
    {
        var html = '<thead class="center"><tr><th>到达用量</th>';
        if (price_rule == 2) {
            $.each(flow_list, function (index, content) {
                html += '<th>'+content+'单价（元）</th>';
            });
        } else {
            html += '<th>单价（元）</th>';
        }
        html += '<th>操作</th></tr></thead>';
        html += '<tbody id="reach_list_'+product_type+'">';
        html += '<tr><td><input type="text" value="0"></td>';
        if (price_rule == 2) {
            $.each(flow_list, function (index, content) {
                html += '<td><input type="text" value=""></td>';
            });
        } else {
            html += '<td><input type="text" value=""></td>';
        }
        html += '<td></td></tr>';

        $('.reach_list_'+product_type+' table').html(html);
    }

    // 按时间计费规则参数
    function check_time_param_data_check(product_type)
    {
        var fee_price_rule = parseInt($('input[name="fee_price_rule_'+product_type+'"]:checked').val());
        if (!fee_price_rule || fee_price_rule == NaN) {
            alert('请选择价格规则');
            return false;
        }
        if (fee_price_rule == 2) {
            var fee_price = check_flow_param_data_check('.fee_flow_'+product_type);
            if (!fee_price) {
                return false;
            }
        } else if (fee_price_rule == 1) {
            var fee_price = $('input[name="time_price_'+product_type+'"]').val();
            if (!check_fee_price(fee_price)) {
                alert('请输入时间计费价格');
                return false;
            }
        }

        return {"fee_price_rule": fee_price_rule, "fee_price": fee_price};
    }

    // 固定价格参数
    function check_fixed_param_data_check(product_type)
    {
        var fee_price_rule = check_fee_price_rule_data_check(product_type);
        if (!fee_price_rule) {
            return false;
        }
        if (fee_price_rule == 2) {
            var fee_price = check_flow_param_data_check('.amount_flow_'+product_type);
            if (!fee_price) {
                return false;
            }
        } else if (fee_price_rule == 1) {
            var fee_price = $('input[name="amount_price_'+product_type+'"]').val();
            if (!check_fee_price(fee_price)) {
                alert('请输入固定价格');
                return false;
            }
        }
        return {"fee_price_rule": fee_price_rule, "fee_price": fee_price};
    }

    // 固定价格运营商参数
    function check_flow_param_data_check(class_name)
    {
        var fee_price = [];
        var sign = true;
        $(class_name).find('input').each(function () {
            var price = $.trim($(this).val());
            if (!check_fee_price(price)) {
                sign = false;
                alert('请输入相应的价格');
                return false;
            }
            fee_price.push(price);
        });
        if (!sign) {
            return false;
        }
        if (fee_price.length == 0) {
            alert('请输入相应的价格');
            return false;
        }
        return fee_price;
    }

    // 检查价格规则
    function check_fee_price_rule_data_check(product_type)
    {
        var fee_price_rule = parseInt($('input[name="fee_price_rule_amount_'+product_type+'"]:checked').val());
        if (!fee_price_rule || fee_price_rule == NaN) {
            alert('请选择价格规则');
            return false;
        }
        return fee_price_rule;
    }
</script>