<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner {
        width: 256px;
    }

    .el-dialog {
        height: 600px;
        overflow-y: scroll;
    }

    .table_title {
        width: 100%;
        min-height: 40px;
        line-height: 40px;
        text-indent: 10px;
        font-size: 14px;
        color: red;
    }

    .table_title b {
        margin: 0 10px;
        font-size: 16px;
    }
</style>

<div id="app">
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-body">

                <el-form :inline="true" :model="searchForm" label-width="80px" class="demo-form-inline">

                    <el-form-item label="账号名称">
                        <el-select v-model="searchForm.apikey" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in accountList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="产品名称">
                        <el-select v-model="searchForm.product_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in productList"
                                    :key="item.product_id"
                                    :label="item.product_name"
                                    :value="item.product_id">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="searchTableData()">查询</el-button>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="success" @click="addTableData()">添加</el-button>
                    </el-form-item>
                </el-form>

            </div>
        </div>
    </div>

    <div class="container">
        <div id="app_body">
            <template>
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            prop="account_id"
                            label="账号ID"
                            width="120"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="account_name"
                            label="账号名称"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="product_name"
                            label="产品名称"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="start_time"
                            label="开始时间"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="end_time"
                            label="结束时间"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="total_limit"
                            label="限量"
                            width="120">
                    </el-table-column>

                    <el-table-column label="操作" width="180">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    @click="handleEdit(scope.$index, scope.row.id)">编辑
                            </el-button>

                            <el-button
                                    size="mini"
                                    type="danger"
                                    @click="handleDelete(scope.$index, scope.row.id)">删除
                            </el-button>

                        </template>
                    </el-table-column>
                </el-table>
            </template>

            <el-dialog :title="dialog_title" :visible.sync="dialogFormVisible" @close="closeDialogCallBack('form')"
                       v-if="dialogFormVisible">
                <el-form :model="form" :rules="rules" ref="form">
                    <el-form-item label="账号名称" prop="apikey" :label-width="formLabelWidth">
                        <el-select v-model="form.apikey" filterable clearable placeholder="请选择账号">
                            <el-option
                                    v-for="item in accountList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="产品名称" prop="product_id" :label-width="formLabelWidth">
                        <el-select v-model="form.product_id" filterable clearable placeholder="请选择产品">
                            <el-option
                                    v-for="item in productList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="开始时间" prop="start_time" :label-width="formLabelWidth">
                        <el-date-picker
                                v-model="form.start_time"
                                type="date"
                                value-format="yyyyMMdd"
                                format="yyyy-MM-dd"
                                placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="结束时间" prop="end_time" :label-width="formLabelWidth">
                        <el-date-picker
                                v-model="form.end_time"
                                type="date"
                                value-format="yyyyMMdd"
                                format="yyyy-MM-dd"
                                placeholder="选择日期">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="总限量" prop="total_limit" :label-width="formLabelWidth">
                        <el-input v-model.number="form.total_limit" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="备注信息" prop="remark" :label-width="formLabelWidth" style="width:420px;">
                        <el-input
                                type="textarea"
                                :rows="2"
                                placeholder="请输入内容"
                                v-model="form.remark"
                                maxlength="100"
                                show-word-limit
                        >
                        </el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onSubmit('form')">确 定</el-button>
                </div>
            </el-dialog>

        </div>

        <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40, 50]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalNum">
            </el-pagination>
        </div>

    </div>

</div>

<script type="application/javascript">

    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/account/product/getLimitList";

    var vm = new Vue({
        el: '#app',
        data: {
            tableData: [],
            addDataList: [],
            totalNum: 0,
            totalSum: 0,
            pageSize: 20,
            currentPage: 1,
            accountList: [],
            productList: [],
            getUrl: url,
            dialogFormVisible: false,
            dialog_title: '添加',
            operate: false,
            form: {
                id: '',
                account_id: '',
                product_id: '',
                start_time: '',
                end_time: '',
                total_limit: '',
                remark: ''
            },
            formLabelWidth: '140px',
            searchForm: {
                account_id: '',
                product_id: '',

            },
            rules: {
                account_id: [
                    {required: true, message: '请选择账号', trigger: 'change'}
                ],
                product_id: [
                    {required: true, message: '请选择产品', trigger: 'change'}
                ],
                start_time: [
                    {type: 'string', required: true, message: '请选择开始时间', trigger: 'change'}
                ],
                end_time: [
                    {type: 'string', required: true, message: '请选择结束时间', trigger: 'change'}
                ],
                total_limit: [
                    {required: true, message: '总限量不能为空'},
                    {type: 'number', message: '总限量必须为数字值'}
                ],
                remark: [
                    {required: true, message: '请输入备注信息', trigger: 'blur'},
                    {min: 2, max: 100, message: '内容长度在 2 到 100 个字符', trigger: 'blur'}
                ]
            }
        },
        created: function () {
            this.getTableData();
            this.getAccountSelectData();
            this.getProductSelectData();
        },
        methods: {
            getTableData: function () {
                var self = this;
                var apikey = this.searchForm.apikey;
                var product_id = this.searchForm.product_id;
                var where = {limit: this.pageSize, page: this.currentPage};
                if (apikey) {
                    where.apikey = apikey;
                }
                if (product_id) {
                    where.product_id = product_id;
                }
                // console.log(where)
                axios.post(url, where).then(function (response) {
                    // console.log(response.data);
                    self.tableData = response.data.data.list;
                    self.totalNum = response.data.data.count;
                }).catch(function (error) {
                    console.log(error);
                });

            },
            getProductSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                axios.get(url, {
                    params: {product_list: true}
                }).then(function (response) {
                    console.log(response.data.data);
                    self.productList = response.data.data.product_list;
                }).catch(function (error) {
                    console.log(error);
                });

            },
            getAccountSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                axios.get(url, {
                    params: {apikey: true}
                }).then(function (response) {
                    // console.log(response.data.data);
                    Object.getOwnPropertyNames(response.data.data.apikey).forEach(function (key) {
                        var name = response.data.data.apikey[key];
                        if (key) {
                            self.accountList.push({value: key, label: name});
                        }
                    });
                    // self.accountList.push({value: 'all', label: '全部'});
                }).catch(function (error) {
                    console.log(error);
                });

            },
            onSubmit: function (formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        var user_cookie = getCookie('PHPSESSID');
                        this.form.user_cookie = user_cookie;
                        var data = this.form;
                        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/account/product/saveLimit";
                        console.log(data)
                        axios.post(url, data).then(function (response) {
                            //console.log(response.data)
                            if (response.data.code == 0) {
                                successMsg(response.data.msg);
                            } else {
                                errorMsg(response.data.msg);
                            }
                        }).catch(function (error) {
                            console.log(error);
                            errorMsg(error);
                        });
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.pageSize = val;
                this.currentPage = 1;
                this.getTableData();
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.currentPage = val;
                this.getTableData();
            },
            searchTableData: function () {
                this.currentPage = 1;
                this.getTableData();
            },
            addTableData: function () {
                this.dialog_title = '添加';
                this.operate = false;
                this.resetForm();
                this.dialogFormVisible = true;
                this.addDataList = [];
            },
            closeDialogCallBack: function (formName) {
                this.$refs[formName].resetFields();
            },
            handleEdit(index, id) {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/account/product/getLimitInfo";
                axios.get(url, {
                    params: {id: id}
                }).then(function (response) {
                    console.log(response.data);
                    if (response.data.code == 0) {
                        //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                        self.form = response.data.data;

                        self.dialog_title = '编辑';
                        self.operate = true;
                        self.dialogFormVisible = true;
                    } else {
                        errorMsg(response.data.msg);
                    }

                }).catch(function (error) {
                    console.log(error);
                });

            },
            handleDelete(index, id) {
                var self = this;
                var user_cookie = getCookie('PHPSESSID');
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/account/product/delLimit";
                axios.get(url, {
                    params: {id: id, user_cookie: user_cookie}
                }).then(function (response) {
                    if (response.data.code == 0) {
                        //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                        self.$message({
                            showClose: true,
                            message: response.data.msg,
                            type: 'success'
                        });
                        self.getTableData();
                    } else {
                        errorMsg(response.data.msg);
                    }

                }).catch(function (error) {
                    console.log(error);
                });
            },

            resetForm: function () {
                this.form = {
                    id: '',
                    account_id: '',
                    product_id: '',
                    start_time: '',
                    end_time: '',
                    total_limit: '',
                    remark: ''
                };
            }
        }

    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
        vm.getTableData();
        vm.resetForm();
        vm.dialogFormVisible = false;
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return (arr[2]);
        else
            return null;
    }

</script>
</body>
</html>