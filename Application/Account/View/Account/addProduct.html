<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form method="post" class="form-horizontal" id="form_account">
                            <div class="form-group">
                                <label class="control-label col-sm-3">账号ID：</label>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" value="<?= $account_info['account_id']?>" readonly="readonly">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-sm-3">账号名称：</label>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" value="<?= $account_info['account_name']?>" readonly="readonly">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="product_id" class="control-label col-sm-3">选择产品：</label>
                                <div class="col-md-4">
                                    <select name="product_id" id="product_id" class="form-control">
                                        <option value="">选择产品</option>
                                        <?php foreach($product_list as $p){
                                    ?>
                                        <option value="<?= $p['product_id']?>" data='<?= $p["data"]?>'><?= $p['product_name']?></option>
                                        <?php
                                    }?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="status" class="control-label col-sm-3">产品状态：</label>
                                <div class="col-md-4">
                                    <select name="status" id="status" class="form-control">
                                        <option value="1">可用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">签约状态：</label>
                                <div class="col-sm-4">
                                    <select name="contract_status" id="contract_status" class="form-control">
                                        <option value="1">已签约已付款</option>
                                        <option value="2">已签约未付费</option>
                                        <option value="3" selected="">未签约</option>
                                        <option value="5">特殊客户</option>
                                        <option value="4">其他</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">截止日期：</label>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="end_time" id="end_time" value=""/>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">产品调用类型：</label>
                                <div class="col-md-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="use_type" value="1" checked> 外部调用
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="use_type" value="2"> 内部调用
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">日用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="daily_limit" id="daily_limit" value="0" placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">月用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="month_limit" id="month_limit" value="-1" placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">年用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="year_limit" id="year_limit" value="-1" placeholder="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">总用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="total_limit" id="total_limit" value="-1" placeholder="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">总限额开始日期：</label>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="limit_start_date" id="limit_start_date" value="" placeholder="请选择总限额开始日期">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">秒并发：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="concurrency" id="concurrency" value="1" placeholder="">
                                </div>
                            </div>

                            <div id="data_html"></div>

                            <div class="pull-right">
                                <input type="hidden" name="account_id" value="<?= $account_info['account_id']?>">
                                <input type="hidden" id="data_json" name="data_json" value="">
                                <input type="submit" class="btn btn-primary btn-sm" value="添加">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <div class="modal fade" id="success_confirm" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">请确认下一步操作</h4>
                </div>
                <div class="modal-body" id="success_body">
                    ...
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">返回列表</button>
                    <button type="button" class="btn btn-primary" id="success_confirm_true">开通产品</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--预览协议 模拟post提交使用 -->
<div style="display: none" id="preview_protocol"></div>

<script type="text/javascript">
    $('#success_confirm').on('hide.bs.modal', function () {
        if ('{$Think.get.callback_url}' == '') {
            window.location.href = "{:U('Account/Account/index')}";
        } else {
            window.location.href = "{$Think.get.callback_url|urldecode}";
        }
    });
    $("#success_confirm_true").click(function () {
        window.location.href = window.location.href;
    });
    new Vue({
        el : '#account_app'
    });

    $(function () {
        // 初始化下拉框
        iniSelect2();

        // 表单提交事件
        formSubmit();

        // 初始化产品
        iniProduct();

        //多选框绑定全选事件
        $("#data_html").on('click', '.checkbox_choose_all', function () {
            var checked = $(this).parent().find('input:checkbox').eq(0).prop('checked');
            $(this).parent().find('input:checkbox').prop('checked', !checked);
        });
    });

    // 初始化产品
    function iniProduct() {
        //选择产品
        $('#product_id').bind('change', function () {
            let html = '';
            $('#data_html').html(html);

            let product_id = $(this).val();

            if (1000 == product_id) {
                let fields = `<div class="form-group" id="productId210_extra_form2">
                                <label class="col-sm-3 control-label">选择开通的字段：</label>
                                <div class="col-sm-9">
                                    <?php foreach ($bxf_all_pid as $itemProduct) {
                                    $product_id = $itemProduct["product_id"];
                                    ?>
                                    <div class="form-group">
                                        <div class="col-sm-4">
                                            <label class="checkbox-inline">
                                                <input type="checkbox" name="data[product_ids][]" value='{$product_id}'>
                                                【{$itemProduct['product_id']}】{$itemProduct['product_name']}
                                            </label>
                                        </div>
                                        <!--
                                        <?php if (in_array($itemProduct['product_id'], $bxf_sore_pid)){ ?>
                                        <div class="col-sm-3">
                                            <select name="data[cucc_cmcc_map][{$itemProduct['product_id']}]" data-type="2" class="form-control">
                                                <option value="">不存在</option>
                                                <?php echo makeOption($cmccConfig);?>
                                            </select>
                                        </div>
                                        <?php } ?>
                                        -->
                                    </div>
                                    <?php } ?>
                                </div>
                            </div>`;
                $("#data_html").before(fields);
            } else {
                $("body").find("#productId210_extra_form1").remove();
                $("body").find("#productId210_extra_form2").remove();
            }

            if (!product_id) {
                return false;
            }

            let data_json = $('#product_id option:selected').attr('data');
            if (!data_json) {
                alert('产品配置参数格式有误！');
                return false;
            }
            $('#data_json').val(data_json);
            let content = JSON.parse(data_json);

            $.each(content, function (k, i) {
                html += '<div class="form-group"><label class="col-sm-3 control-label">' + i.cn_name + '：</label>';
                i.default = !!i.default ? i.default : '';
                //1单行文本框、2多行文本框、3单选框、4多选框、5时间控件，默认为单行文本框
                switch (i.type) {
                    case 1:
                        html += '<div class="col-sm-4">';
                        // H5邦秒爬的cid  && token是不可以更改的
                        if (parseInt(product_id) === 301 && (i.name === 'cid' || i.name === 'token')) {
                            html += '<input type="text" class="form-control" name="data[' + i.name + ']" value="' + i.default + '" placeholder="">';
                        } else {
                            html += '<input type="text" class="form-control" name="data[' + i.name + ']" value="' + i.default + '" placeholder="">';
                        }
                        html += '</div>';
                        break;
                    case 2:
                        html += '<div class="col-sm-4">';
                        html += '<textarea type="text" name="data[' + i.name + ']" class="form-control">' + i.default + '</textarea>';
                        html += '</div>';
                        break;
                    case 3:
                        // 设置单选按钮
                        html += setRadio(product_id, i);
                        break;
                    case 4:
                        html += '<div class="col-sm-4">';
                        html += '<button class="btn btn-primary btn-sm checkbox_choose_all" style="margin-right:10px;" type="button">全选/取消</button>';
                        $.each(i.option, function (ko, io) {
                            let type_change       = typeof io.opt_val === 'number' ? io.opt_val.toString() : parseInt(io.opt_val);
                            let determine_checked = (i.default.indexOf(io.opt_val) !== -1) || (i.default.indexOf(type_change) !== -1);
                            let checked_str       = determine_checked ? 'checked' : '';
                            html += '<label class="checkbox-inline">';
                            html += '<input type="checkbox" name="data[' + i.name + '][]" value="' + io.opt_val + '" ' + checked_str + '>' + io.opt_name;
                            html += '</label>';
                        });
                        html += '</div>';
                        break;
                    case 5:
                        html += '<div class="col-sm-4">';
                        html += '<input type="date" name="data[' + i.name + ']" value="' + i.default + '" class="form-control">';
                        html += '</div>';
                        break;
                    case 6:
                        // 富文本编辑框
                        html += '<div class="col-sm-9">';
                        html += '<div id="editor_' + i.name + '"></div>';
                        html += '<textarea id="' + i.name + '" name="data[' + i.name + ']" style="width:100%; height:200px; display: none"></textarea>';
                        html += '</div>';
                        break;
                    case 7:
                        // 单选下拉框
                        html += iniSelectSingle();
                        break;
                    case 8:
                        // 多选下拉框
                        html += iniSelectMultiple(product_id, i);
                        break;
                    case 9:
                        // console.log(i, '多选文本框');
                        // 多选文本框
                        html += iniInputMultiple(i);
                        break;
                }
                if (!!i.placeholder) {
                    html += '<span class="col-sm-5"><label class="alert alert-info control-label" style="height: 34px;">* ' + i.placeholder + '</label></span>';
                }
                html += '</div>';
            });

            $('#data_html').html(html);

            // 格式化下拉框
            iniSelect2();

            // 初始化夫文本框
            iniEdit(content);
        });
    }

    // 多选文本框
    function iniInputMultiple(item) {
        // 如果没有设置
        let html = '<div class="col-sm-9 form-inline" style="width: 300px;">';
        html += '<input type="text" name="data[' + item.name + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
        html += '<button type="button" onclick=addInputForH5(this) class="btn btn-default">+</button>' +
            '</div>';
        return html;
    }
    function addInputForH5(that) {
        let html = '<div class="col-sm-9 form-inline col-sm-offset-3" style="width: 300px;">';
        html += '<input type="text"   name="data[scoreGradeLimit][]"  multiple="multiple" class="form-control" style="width: 42%">';
        html += '<button type="button" onclick="removeInputForH5(this)" class="btn btn-default">-</button>' +
            '</div>';

        $(html).insertAfter(that.parentNode);
    }

    // 删掉新增的h5
    function removeInputForH5(that) {
        that.parentNode.remove();
    }

    // 单选下拉框
    function iniSelectSingle() {
        let html = '<div class="col-sm-9">';
        html += '<select  name="data[' + item.name + ']" class="form-control" style="width: 42%">';
        item.option.forEach(function (element) {
            html += '<option value="' + element.opt_val + '">' + element.opt_name + '</option>';
        });
        html += '</select></div>';
        return html;
    }

    // 初始化select2
    function iniSelect2() {
        $('select[data-type!="2"]').select2({
            allowClear : false,
            theme      : "bootstrap",
            width      : 'resolve',
        });
    }

    function iniEdit(data_config) {
        // 富文本框的处理
        $.each(data_config, function (key, item) {
            if (parseInt(item.type) !== 6) {
                return true;
            }

            // 设置富文本框的默认值 && 文本域selector
            $('#editor_' + item.name).append(item.default);
            let textarea = $('#' + item.name);

            // 授权协议(富文本框)
            let editor = new window.wangEditor('#editor_' + item.name);

            editor.customConfig.onchange            = function (html) {
                // 监控变化，同步更新到 textarea
                textarea.val(html);
            };
            editor.customConfig.pasteFilterStyle    = false; // 不对粘贴的样式过滤
            editor.customConfig.uploadImgShowBase64 = true;  // 使用 base64 保存图片
            editor.customConfig.showLinkImg         = true; // 使用网络照片
            editor.create();

            // 初始化 textarea 的值
            textarea.val(editor.txt.html());
        });
    }

    // 初始化多选下拉框
    function iniSelectMultiple(product_id, item) {
        // 如果是product_id是301
        if (parseInt(product_id) === 301) {
            return iniSelectMultipleForH5(item);
        }

        return iniSelectMultipleForCommon(item);
    }

    // h5配置的多选框
    function iniSelectMultipleForH5(item) {
        let html = '<div class="col-sm-9 form-inline">';
        html += '<select  name="data[' + item.name + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
        item.option.forEach(function (element) {
            html += '<option value="' + element.opt_val + '">' + element.opt_name + '</option>';
        });

        let item_str = JSON.stringify(item);
        html += '</select>' +
            '<button type="button" onclick=addSelectForH5(this,' + item_str + ') class="btn btn-default">+</button>' +
            '</div>';
        return html;
    }

    // 为301增加select控制
    function addSelectForH5(that, item) {
        // 增加select2
        addEmergencySelectForH5(that, item);

        // 初始化新增的select
        iniSelect2();
    }

    // h5邦秒爬配置select下拉框
    function addEmergencySelectForH5(that, item) {
        let selector_length = $('select').length;
        let html            = '<div class="col-sm-9 form-inline col-sm-offset-3">';
        html += '<select  name="data[emergency_contact_detail_limits' + selector_length + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
        item.option.forEach(function (element) {
            html += '<option value="' + element.opt_val + '">' + element.opt_name + '</option>';
        });
        html += '</select>' +
            '<button type="button" onclick="removeSelectForH5(this)" class="btn btn-default">-</button>' +
            '</div>';

        $(html).insertAfter(that.parentNode);
    }

    // 删掉新增的h5 select
    function removeSelectForH5(that) {
        that.parentNode.remove();
    }

    // 普通的多选框
    function iniSelectMultipleForCommon(item) {
        let html = '<div class="col-sm-9">';
        html += '<select  name="data[' + item.name + '][]"  multiple="multiple" class="form-control bigdrop" style="width: 42%">';
        item.option.forEach(function (element) {
            html += '<option value="' + element.opt_val + '">' + element.opt_name + '</option>';
        });
        html += '</select></div>';
        return html;
    }

    // 设置单选按钮
    function setRadio(product_id, i) {
        // 301的授权协议需要单独处理
        if (parseInt(product_id) === 301 && i.name === 'protocol_default') {
            let html = '<div class="col-sm-4">';
            $.each(i.option, function (ko, io) {
                let checkedstr = '';
                if (i.default == io.opt_val) {
                    checkedstr = 'checked=""';
                }
                html += '<label class="radio-inline">';
                html += '<input type="radio" id="' + i.name + '" name="data[' + i.name + ']" value="' + io.opt_val + '" ' + checkedstr + '>' + io.opt_name;
                html += '</label>';
            });

            // 预览
            html += '<a style="margin-left: 5px" href="javascript:void(0);" target="_blank" onclick="preview()" class="btn btn-primary btn-xs">预览</a>';
            html += '</div>';
            return html;
        }

        let html = '<div class="col-sm-4">';
        $.each(i.option, function (ko, io) {
            let checkedstr = '';
            if (i.default == io.opt_val) {
                checkedstr = 'checked=""';
            }
            html += '<label class="radio-inline">';
            html += '<input type="radio" name="data[' + i.name + ']" value="' + io.opt_val + '" ' + checkedstr + '>' + io.opt_name;
            html += '</label>';
        });
        html += '</div>';
        return html;
    }

    // 预览授权协议
    function preview() {
        // 获取预览地址
        $.get('/Api/BackendPreview/previewUrl').success(function (response) {
            if (response.status === 0) {

                // 执行预览操作
                let preview_url = response.preview_url;
                previewDo(preview_url);
            } else {
                console.log('请求预览地址失败', response);
            }
        }).error(function (response) {
            console.log('请求预览地址失败', response);
        });
    }

    // 预览
    function previewDo(preview_url) {
        // 获取预览协议
        let protocol = getProtocolForPreview();

        $('#preview_protocol').html('<form id="preview_form" target="NewWindow" action="' + preview_url + '" method="POST" style="display:none;"><textarea name="protocol_content">' + protocol + '</textarea></form>');
        window.open("", 'NewWindow');
        $('#preview_form').submit();
    }

    // 获取预览协议
    function getProtocolForPreview() {
        let protocol         = $('#protocol_content').val();
        let protocol_default = $('input[name="data[protocol_default]"]:checked', '#form_account').val();

        // 如果是默认协议的话 则API获取协议
        if (parseInt(protocol_default) === 1) {
            $.ajax({
                url      : '/Api/BackendPreview/defaultProtocol',
                async    : false,
                dataType : 'json'
            }).success(function (response) {
                if (response.status === 0) {
                    protocol = response.protocol_default;
                } else {
                    console.log('获取默认协议出错');
                }

            }).error(function (response) {
                console.log('获取默认协议出错');
            });
        }
        return protocol;
    }

    // 表单提交事件
    function formSubmit() {
        $('#form_account').submit(function () {
            // 检查参数
            if (checkParams() === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest();
        });
    }

    // 提交表单
    function formRequest() {
        let form         = $('#form_account');
        let data_request = form.serialize();
        let url_request  = '/Account/Account/addProduct';

        $.post(url_request, data_request).success(function (response) {
            console.log(response);
            if (response.status === 0) {
                //alert(response.msg);
                // window.location.href = url_redirect;
                $("#success_body").html(response.msg);
                $('#success_confirm').modal({
                    keyboard : false
                });
            } else {
                alert(response.errors.msg);
            }
        }).error(function (response) {
            alert('开通产品出错，请稍后重试');
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        let product_id = $('#product_id').val();
        let end_time   = $('#end_time').val().trim();
        if (!product_id) {
            alert('请选择产品');
            return false;
        }
        if (!end_time) {
            alert('截止日期未填写');
            return false;
        } else {
            // 选定的时间必须是大于今天的
            let today_str = (new Date()).toDateString();
            let time_diff = new Date(Date.parse(end_time)) - new Date(Date.parse(today_str));
            if (time_diff < 0) {
                alert('截止日期不能小于当前日期');
                return false;
            }
        }
        return true;
    }

</script>
</body>
</html>
