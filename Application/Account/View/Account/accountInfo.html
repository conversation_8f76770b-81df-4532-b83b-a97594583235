<style>
    div.font-info-34{line-height: 34px;}
</style>
<div class="container show_account">
    <form action="./" class="form-horizontal">
        <prompt_modal_unique_template ref="prompt_modal" unique_string='<?= $account_info["account_id"] ?>'></prompt_modal_unique_template>
        <div class="form-group">
            <label class="col-sm-2 control-label">账号ID：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['account_id']?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">账号名称：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['account_name']?></div>
        </div>

<!--        <div class="form-group">-->
<!--            <label class="col-sm-2 control-label">登录邮箱：</label>-->
<!--            <div class="col-sm-4 font-info-34"><?= $account_info['email']?></div>-->
<!--        </div>-->

        <div class="form-group">
            <label class="col-sm-2 control-label">所属客户：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['name']?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">账号类型：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['type']==1?'正式账号':'测试账号'?></div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">账号状态：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['status']==1?'可用':'禁用'?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">APIKEY：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['apikey']?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">APPSECRET：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['appsecret']?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">截止日期：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['end_time']?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">IP白名单：</label>
            <div class="col-sm-4 font-info-34"><?= implode('<br/>', explode(PHP_EOL,$account_info['access_ip'])); ?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">备注：</label>
            <div class="col-sm-4 font-info-34"><?= $account_info['mark']?></div>
        </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">产品：</label>
            <div class="col-sm-10 font-info-34 table-responsive">
                <table class="table text-nowrap">
                    <thead>
                    <tr>
                        <th>产品名称</th>
                        <th>产品状态</th>
                        <th>签约类型</th>
                        <th>截止时间</th>
                        <th>日/月/年/总限量</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <?php foreach($account_product_name_list as $product){ ?>
                    <tbody>
                    <tr <?php if(in_array($product['product_id'], [615, 210, 1000])){ ?> style="color: blue;font-weight:bold;" <?php } ?> >
                        <td><?= $product['product_name'] ?></td>
                        <td><?= $product['product_status_zh'] ?></td>
                        <td><?= $product['contract_status_zh'] ?></td>
                        <td><?= $product['end_time_zh'] ?></td>
                        <td><?= $product['limit_zh'] ?></td>
                        <td>
                            <a style="cursor:pointer" class="btn btn-small btn-default" href="{:U('editProduct',array('account_product_id'=>$product['id']))}?callback_url={$Think.get.callback_url|urlencode}" >编辑</a>

                            <?php if($product["product_id"] == 601) {?>
                            <a onclick="cpSign('<?= $account_info['account_id']?>')" class="btn btn-small btn-info" style="cursor:pointer">复制签名以及示例</a>
                            <?php } ?>
                        </td>
                    </tr>
                    </tbody>
                    <?php } ?>
                </table>
            </div>
        </div>
    </form>
    <template id="sign_maio_pei_button"></template>
    <div class="modal-footer form-inline">
        <div class="form-group">
            <button type="button" class="btn btn-primary submit_button" id="btn_edit_account" onclick="btn_edit_account()">编辑账号</button>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-primary submit_button" id="btn_add_product" onclick="btn_add_product()">开通产品</button>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        </div>
    </div>
</div>
<script type="text/javascript">
    function cpSign (account_id) {
        var vm = new Vue({
            el:".show_account"
        });
        let params = {
            params : {
                product_id : 601,
                account_id : account_id
            }
        };
        axios.get('/Api/BackendProduct/signs', params).then(function (response) {

            console.log(response, '签名');
            if (response.data.status === 0) {
                vm.$refs.prompt_modal.open({
                    title : '复制签名提示',
                    body: '签名字符串 :' + response.data.sig_str + ' 示列：' + response.data.api_example
                });
            } else {
                vm.$refs.prompt_modal.open({
                    title : '复制签名提示',
                    body: response.data.errors.msg
                });
            }
        });
    }
//    new Vue({
//        el: '.show_account',
//        mounted : function () {
//            alert(21);
//            console.log('这里是show_account');
//        },
//        methods : {
//            cpSign : function (account_id) {
//                // 获取签名字符串 && 调用实例
//                let params = {
//                    params : {
//                        product_id : 601,
//                        account_id : account_id
//                    }
//                }, vm = this;
//                axios.get('/Api/BackendProduct/signs', params).then(function (response) {
//
//                    console.log(response, '签名');
//
//                    if (response.data.status === 0) {
//                        vm.$refs.prompt_modal.open({
//                            title : '复制签名提示',
//                            body: '签名字符串 :' + response.data.sig_str + ' 示列：' + response.data.api_example
//                        });
//                    } else {
//                        vm.$refs.prompt_modal.open({
//                            title : '复制签名提示',
//                            body: response.data.errors.msg
//                        });
//                    }
//                });
//            }
//        },
//    });

    function btn_edit_account() {
        window.location.href = '{:U(\'/Account/Account/edit\',array(\'id\'=>$account_info[\'id\']))}?callback_url={$Think.get.callback_url|urlencode}';
    }
    function btn_add_product() {
        window.location.href = '{:U(\'/Account/Account/addProduct\',array(\'id\'=>$account_info[\'id\']))}?callback_url={$Think.get.callback_url|urlencode}';

    }
</script>
