<!DOCTYPE html>
<html>
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    -->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
        .index-btn {
            margin:5px;
        }
        .tags_green{
            color: white;
            margin-right: 2px;
            background: green;
            width: 1.2em;
            display: inline-block;
            text-align: center;
            margin-top: 1px;
        }
        .tags_grey{
            color: white;
            margin-right: 2px;
            text-align: center;
            display: inline-block;
            width: 1.2em;
            background: gray;
            margin-top: 1px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('Account/Account/index')}" method="get" class="form-inline">
                    <div class="form-group">
                        <label for="account_id">ID：</label>
                        <input type="text" name="account_id" id="account_id" class="form-control" value="{$Think.get.account_id}">
                    </div>
                    <div class="form-group">
                        <label for="account">账号名称：</label>
                        <?php $account_get = I('get.account'); ?>
                        <select name="account" id="account" class="form-control">
                            <notempty name="account_get">
                                <option value="{$account_get}" selected>{$account[$account_get]}</option>
                            </notempty>
                            <option value=""></option>
                            <volist name="account" id="vo">
                                <option value="{$key}">{$vo}</option>
                            </volist>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="group">主体名称：</label>
                        <?php $group_get = I('get.group'); ?>
                        <select name="group" id="group" class="form-control">
                            <notempty name="group_get">
                                <option value="{$group_get}" selected>{$group[$group_get]}</option>
                            </notempty>
                            <option value=""></option>
                            <volist name="group" id="vo">
                                <option value="{$key}">{$vo}</option>
                            </volist>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="customer">客户名称：</label>
                        <?php $customer_get = I('get.customer'); ?>
                        <select name="customer" id="customer" class="form-control">
                            <notempty name="customer_get">
                                <option value="{$customer_get}" selected>{$customer[$customer_get]}</option>
                            </notempty>
                            <option value=""></option>
                            <volist name="customer" id="vo">
                                <option value="{$key}">{$vo}</option>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="cid">cid：</label>
                        <input type="text" name="cid" id="cid" class="form-control" value="{$Think.get.cid}">
                    </div>
                    <div class="form-group">
                        <label for="apikey">APIKEY：</label>
                        <input type="text" name="apikey" id="apikey" class="form-control" value="{$Think.get.apikey}">
                    </div>
                    <div class="form-group">
                        <label for="product">产品名称：</label>
                        <select name="product" id="product" class="form-control">
                            <?= $product_get = I('get.product'); ?>
                            <notempty name="product_get">
                                <option value="{$product_get}" selected>{$product.$product_get}</option>
                            </notempty>
                            <option value=""></option>
                            <volist name="product" id="vo">
                                <option value="{$key}">{$vo}</option>
                            </volist>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="type">账号类型：</label>
                        <?php $type_get = I('get.type'); ?>
                        <select name="type" id="type" class="form-control">
                            <option value="-1">全部</option>
                            <option value="1"
                            <?= ($type_get!=='' && $type_get==1) ? 'selected' : '' ?>>正式</option>
                            <option value="0"
                            <?= ($type_get!=='' && $type_get==0) ? 'selected' : '' ?>>测试</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="status">账号状态：</label>
                        <?php $status_get = I('get.status'); ?>
                        <select name="status" id="status" class="form-control">
                            <option value="-1">全部</option>
                            <option value="1"
                            <?= ($status_get!=='' && $status_get==1) ? 'selected' : '' ?>>可用</option>
                            <option value="0"
                            <?= ($status_get!=='' && $status_get==0) ? 'selected' : '' ?>>禁用</option>
                        </select>
                    </div>

                <div class="form-group">
                    <label for="close_zhilian">直连关闭：</label>
                    <?php $close_zhilian = I('get.close_zhilian'); ?>
                    <select name="close_zhilian" id="close_zhilian" class="form-control">
                        <option value="-1">全部</option>
                        <option value="0"
                        <?= ($close_zhilian!=='' && $close_zhilian==0) ? 'selected' : '' ?>>否</option>
                        <option value="1"
                        <?= ($close_zhilian!=='' && $close_zhilian==1) ? 'selected' : '' ?>>是</option>
                    </select>
                </div>
                <div class="form-group">
                    <input type="submit" id="submit_btn" class="btn btn-primary btn-sm" value="查询">
                </div>
                <div class="form-group">
                    <a id="file_export" class="btn btn-success btn-sm">导出</a>
                </div>
                <div class="form-group">
                    <a id="add_account"  type="button" data-toggle="modal" data-target="#myModal" class="btn btn-info btn-sm">添加账号</a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th width="150">账号ID</th>
                <th>账号名称</th>
                <th>状态</th>
                <th>所属客户</th>
                <th>CID</th>
                <th width="140" style="word-wrap: break-word;">APIKEY</th>
                <th width="200" style="word-wrap: break-word;">APISECRET</th>
                <th>直连关闭</th>
                <th>已开通产品</th>
                <th width="200">换签产品</th>
                <th>账号类型</th>
                <th width="60">操作人</th>
                <th width="280">操作</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($data['data'] as $account){ ?>
            <tr>
                <td>{$account.account_id}</td>
                <td>{$account.account_name}</td>
                <td>
                    <?php
                        echo $account['status'] ? '可用' : '禁用';
                    ?>
                </td>
                <td>{$account.name}</td>
                <td>{$account.cid}</td>
                <td style="word-break: break-all">{$account.apikey}</td>
                <td style="word-break: break-all">{$account.appsecret}</td>
                <td style="word-break: break-all"><?= ($account['close_zhilian'] == 1) ? '是' : '否' ; ?></td>
                <td>{$account.product|implode="<br/>", ###}</td>
                <td style="white-space:nowrap;">{$account.change_tags|implode="<br/>", ###}</td>
                <td><?= ($account['type'] == 1) ? '正式' : '测试' ; ?></td>
                <td><?= $user_data[$account['admin']]; ?></td>
                <td>
                    <a href="{:U('Account/Account/edit', ['id' => $account['id']])}?callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn btn-info btn-sm">编辑</a>
                    <a href="{:U('Account/Account/addProduct', ['id' => $account['id']])}?callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn btn-info btn-sm">开通产品</a>
                    <a href="{:U('Account/Account/changeSource', ['account_id' => $account['account_id']])}?callback_url={$Think.server.REQUEST_URI|urlencode}" class="btn btn-warning btn-sm">换签标记</a>
                    <button onclick="deleteAccount('{$account.id}');" class="btn btn-danger btn-sm">删除</button>
                </td>
            </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
</div>
<div class="container">
    <ul class="pagination">
        {$data.page}
    </ul>
</div>
<div class="container">
    <div class="modal fade" id="myModal" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">添加账号</h4>
                </div>
                <div class="modal-body">
                    您要为那个客户添加账号？<br/><br/>
                        <div class="form-group">
                            <select name="customer_addAccount" id="customer_addAccount" class="form-control">
                                <option value=""></option>
                                <volist name="customer_addAccount" id="vo">
                                    <option value="{$key}">{$vo}</option>
                                </volist>
                            </select>
                        </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="addAccount_confirm">确定添加</button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
<script type="text/javascript">
    $(function () {
        $("#group").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择主体',
            width: '200px'
        });
        $("#customer").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择客户',
            width: '200px'
        });
        $("#account").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择账号',
            width: '200px'
        });
        $("#product").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '选择产品',
            width: '200px'
        });
        $("#customer_addAccount").select2({
            allowClear: true,
            theme: "bootstrap",
            placeholder: '为客户增加账号',
            width: '200px',
            dropdownParent: $("#myModal")
        });
        $("#addAccount_confirm").click(function () {
            var customer_id = $("#customer_addAccount").val();
            if (customer_id==='') {
                alert("请选择客户");
                return false;
            }
            location.href = "{:U('Account/Account/add')}?callback_url={$Think.server.REQUEST_URI|urlencode}&id="+customer_id;
        });
        $("#file_export").click(function () {
            var conf = confirm('文件导出将导出已查询出的全部数据（不分页），是否继续？');
            if (conf) {
                window.open("{:U('Account/Account/export')}" + location.search);
            }
        });
    });
    function deleteAccount(id) {
        let conf = confirm('确定删除此账号吗？');
        if (!conf) {
            return false;
        }
        $.post("{:U('Account/Account/delete')}", {
            id:id
        }, function (res) {
            if (res.res>0) {
                alert("删除成功");
                location.reload();
            } else {
                alert("删除失败");
            }
        })
    }
</script>