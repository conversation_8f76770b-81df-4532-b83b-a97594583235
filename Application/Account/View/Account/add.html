<!DOCTYPE html>
<html>
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    -->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            width:100%;
            height : 240px !important;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="/Account/Account/add" method="post" class="form-horizontal" id="form_account">
                            <div class="form-group">
                                <label for="account_name" class="control-label col-md-3">账号名称：</label>
                                <div class="col-md-4">
                                    <input type="text" name="account_name" id="account_name" class="form-control" value="">
                                </div>
                            </div>

<!--                            <div class="form-group">-->
<!--                                <label for="email" class="control-label col-md-3">登录邮箱：</label>-->
<!--                                <div class="col-md-4">-->
<!--                                    <input type="text" name="email" class="form-control" id="email">-->
<!--                                </div>-->
<!--                            </div>-->

                            <div class="form-group">
                                <label class="control-label col-md-3">所属客户：</label>
                                <div class="col-md-4">
                                    <select class="form-control" readonly="">
                                        <option value=""><?= $customer_info['name']?></option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="type" class="control-label col-md-3">账号类型：</label>
                                <div class="col-md-4">
                                    <select name="type" id="type" class="form-control">
                                        <?php if ($_GET['original']=='test'){ ?>
                                        <option value="2">售前测试账号</option>
                                        <?php } else { ?>
                                        <option value="1">正式账号</option>
                                        <option value="0">测试账号</option>
                                        <option value="2">售前测试账号</option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="status" class="control-label col-sm-3">账号状态：</label>
                                <div class="col-md-4">
                                    <select name="status" id="status" class="form-control">
                                        <option value="1">可用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">APIKEY：</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" name="apikey" id="input_apikey" value="" >
                                </div>
                                <div class="col-sm-1"></div>
                                <div class="col-sm-1">
                                    <button type="button" class="btn btn-info btn-sm" onclick="edit_hash('input_apikey', 32)">生 成</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">APPSECRET：</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" name="appsecret" id="input_appsecret" value="" >
                                </div>
                                <div class="col-sm-1"></div>
                                <div class="col-sm-1">
                                    <button type="button" class="btn btn-info btn-sm" onclick="edit_hash('input_appsecret', 64)">生 成</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">截止日期：</label>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="end_time" id="end_time" value="2099-12-31"/>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="cache_days" class="control-label col-md-3">缓存时长(天)：</label>
                                <div class="col-md-4">
                                    <input type="number" name="cache_days" id="cache_days" class="form-control" value="1">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="account_name" class="control-label col-md-3">账号秒并发：</label>
                                <div class="col-md-4">
                                    <input type="text" name="concurrency" id="concurrency" placeholder="默认-1不限制" class="form-control" value="-1">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">IP白名单：</label>
                                <div class="col-sm-4">
                                    <textarea type="text" class="form-control" name="access_ip" id="access_ip"></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">备注：</label>
                                <div class="col-sm-4">
                                    <textarea type="text" class="form-control" name="mark" id="mark"></textarea>
                                </div>
                            </div>

                            <div class="pull-right">
                                <input type="hidden" name="customer_id" value="<?= $customer_info['customer_id']?>">
                                <!--<input type="hidden" name="father_id" value="<?= $customer_info['account_id']?>">-->
                                <input type="submit" class="btn btn-primary btn-sm" value="添加">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    new Vue({
        el : '#account_app'
    });

    $(function () {
        // 表单提交事件
        formSubmit();

        creat_hash('input_apikey',32);
        creat_hash('input_appsecret',64);
    });

    // 表单提交事件
    function formSubmit() {

        $('#form_account').submit(function(){
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request = $(that).attr('action');
        /*var url_redirect;
        if ('{$Think.get.source}' === 'account') {
            url_redirect = '/Account/Account/index';
        } else {
            url_redirect = '/Account/Customer/index';
        }*/
        $.post(url_request, data_request).success(function(response){
            if (response.status !== 'success') {
                modalExport(response.info);
                return '';
            }
            var account_id = response.data.account_id;
            var url = '{:U(\'/Account/Account/addSuccess\',array(\'account_id\'=>0))}';
            url = url.replace('account_id/0', 'account_id/'+account_id) + '?callback_url={$Think.get.callback_url|urlencode}';
            DHB.INFO.view(url,'账号添加成功');
        }).error(function(response){
            modalExport('创建账号出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var name = $('#account_name').val().trim();
        // var email = $('#email').val().trim();
        var end_time = $('#end_time').val().trim();
        if (!name) {
            modalExport('账号名称未填写');
            return false;
        }
        if (name.length < 2) {
            modalExport('账号的名字的长度需要不可以小于2');
            return false;
        }
        var cache_days = $('#cache_days').val().trim();
        if(cache_days < 0){
            modalExport('缓存时长不可小于0');
            return false;
        }
        // if (!email) {
        //     modalExport('邮箱未填写');
        //     return false;
        // }
        if (!end_time) {
            modalExport('截止日期未填写');
            return false;
        }else {
            // 选定的时间必须是大于今天的
            var today_str = (new Date()).toDateString();
            var time_diff = new Date(Date.parse(end_time)) - new Date(Date.parse(today_str));
            if (time_diff < 0) {
                modalExport('截止日期不能小于当前日期');
                return false;
            }
        }

        return true;
    }

    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length)
    {
        // if(confirm('修改apikey,appsecret会使当前账号的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        // }
    }
</script>
</body>
</html>
