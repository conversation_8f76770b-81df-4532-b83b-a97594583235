<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            width:100%;
            height : 240px !important;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>

<!-- 更新产品的重要字段，弹窗提示的组件 -->
<include file="Common@concessions/product_edit_warning_template"/>
<div id="account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form method="post" class="form-horizontal" id="form_account">
                            <div class="form-group">
                                <label class="control-label col-sm-3">账号ID：</label>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" value="<?= $account_info['account_id']?>"
                                           readonly="readonly">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-sm-3">账号名称：</label>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" value="<?= $account_info['account_name']?>"
                                           readonly="readonly">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="product_id" class="control-label col-sm-3">选择产品：</label>
                                <div class="col-md-4">
                                    <select name="product_id" id="product_id" class="form-control" readonly="">
                                        <option value="<?= $product_info['product_id']?>" data='<?= $product_info["data"]?>'
                                                selected=""><?= $product_info['product_name']?></option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="status" class="control-label col-sm-3">产品状态：</label>
                                <div class="col-md-4">
                                    <select name="status" id="status" class="form-control">
                                        <option value="1"
                                        <?= $account_info['status'] == 1 ?'selected': '' ?> >可用</option>
                                        <option value="0"
                                        <?= $account_info['status']!=1 ? 'selected' : '' ?>>禁用</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">签约状态：</label>
                                <div class="col-sm-4">
                                    <select name="contract_status" id="contract_status" class="form-control">
                                        <option value="1"
                                        <?= $account_info['contract_status']==1?'selected=""':''?>>已签约已付款</option>
                                        <option value="2"
                                        <?= $account_info['contract_status']==2?'selected=""':''?>>已签约未付费</option>
                                        <option value="3"
                                        <?= $account_info['contract_status']==3?'selected=""':''?>>未签约</option>
                                        <option value="5"
                                        <?= $account_info['contract_status']==5?'selected=""':''?>>特殊客户</option>
                                        <option value="4"
                                        <?= $account_info['contract_status']==4?'selected=""':''?>>其他</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">截止日期：</label>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="end_time" id="end_time"
                                           value="<?= date('Y-m-d', $account_info['end_time'])?>"/>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-sm-3">产品调用类型：</label>
                                <div class="col-md-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="use_type"
                                               value="1" <?=  $account_info['use_type'] == 1 ? 'checked' : '' ?> > 外部调用
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="use_type"
                                               value="2"  <?=  $account_info['use_type'] == 2 ? 'checked' : '' ?>> 内部调用
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">日用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="daily_limit" id="daily_limit"
                                           value="<?= $account_info['daily_limit']?>" placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">月用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="month_limit" id="month_limit"
                                           value="<?= $account_info['month_limit']?>" placeholder="">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">年用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="year_limit" id="year_limit"
                                           value="<?= $account_info['year_limit']?>" placeholder="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">总用量限额：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="total_limit" id="total_limit"
                                           value="<?= $account_info['total_limit']?>" placeholder="">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">总限额开始日期：</label>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="limit_start_date" id="limit_start_date" value="{$account_info.limit_start_date}" placeholder="请选择总限额开始日期">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">秒并发：</label>
                                <div class="col-sm-4">
                                    <input type="number" class="form-control" name="concurrency" id="concurrency"
                                           value="<?= $account_info['concurrency']?>" placeholder="">
                                </div>
                            </div>

                            <?php if(1000==$product_info['product_id']){ ?>
                            <div class="form-group" id="productId210_extra_form2">
                                <label class="col-sm-3 control-label">选择开通的字段：</label>
                                <div class="col-sm-9">
                                    <?php foreach ($bxf_all_pid as $itemProduct) {
                                    $product_id = $itemProduct["product_id"];
                                    ?>
                                    <div class="form-group">
                                        <div class="col-sm-4">
                                            <label class="checkbox-inline">
                                                <input type="checkbox" name="data[product_ids][]" value='{$product_id}' <?php echo in_array($itemProduct['product_id'], $productIds)?'checked':''; ?>>
                                                【{$itemProduct['product_id']}】{$itemProduct['product_name']}
                                            </label>
                                        </div>
                                        <!--
                                        <?php if (in_array($itemProduct['product_id'], $bxf_sore_pid)){ ?>
                                        <div class="col-sm-3">
                                            <select name="data[cucc_cmcc_map][{$itemProduct['product_id']}]" data-type="2" class="form-control">
                                                <option value="">不存在</option>
                                                <?php echo makeOption($cmccConfig, $cucc_cmcc_map[$itemProduct['product_id']]);?>
                                            </select>
                                        </div>
                                        <?php } ?>
                                        -->
                                    </div>
                                    <?php } ?>
                                </div>
                            </div>
                            <?php } ?>
                            <div id="data_html"></div>

                            <div class="pull-right">
                                <input type="hidden" name="account_id" id="account_id"
                                       value="<?= $account_info['account_id']?>">
                                <input type="hidden" name="data_json" value='<?= $product_info["data"]?>'>
                                <input type="submit" class="btn btn-primary btn-sm" value="保存">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div style="display: none" id="preview_protocol"></div>

<script type="text/javascript">
    new Vue({
        el: '#account_app',
    });

    $(function () {
        formSubmit();

        // 初始化产品
        initProduct();

        // 初始化下拉框
        iniSelect2();

        //多选框绑定全选事件
        $("#data_html").on('click', '.checkbox_choose_all', function () {
            var checked = $(this).parent().find('input:checkbox').eq(0).prop('checked');
            $(this).parent().find('input:checkbox').prop('checked', !checked);
        });
    });

    // 声明全局变量
    function declareGlobalVar(product_id, account_id) {
        window.edit = {};
        window.edit.product_id = product_id;
        window.edit.account_id_id = account_id;

        // 日月年限量 && 秒并发 && 产品状态 && 截至日期
        window.edit.daily_limit = $('#daily_limit').val();
        window.edit.month_limit = $('#month_limit').val();
        window.edit.total_limit = $('#total_limit').val();
        window.edit.year_limit = $('#year_limit').val();
        window.edit.concurrency = $('#concurrency').val();
        window.edit.status = $('#status').val();
        window.edit.end_time = $('#end_time').val();
    }

    // 初始化产品
    function initProduct() {
        //编辑产品
        let html = '';
        $('#data_html').html(html);

        // 这里使用ajax获取信息 因为直接传递的话，协议有可能解析错误
        let product_id = $('#product_id').val();
        let account_id = $('#account_id').val();

        // 设置全局变量
        declareGlobalVar(product_id, account_id);

        $.post('/Api/BackendProduct/product', {
            product_id: product_id,
            account_id: account_id
        }, function (response) {
            if (response.status === 0) {
                console.log(response, '请求data信息');
                // 生成产品的单独配置
                genDataConfigHtml(response.data, product_id, html);

                // 设置全局变量
                window.edit.data = response.data;
                console.log(window.edit, '捕捉到的旧数据');
            } else {
                console.log('获取产品的配置信息出错');
            }
        });
    }

    // 生成产品的单独配置
    function genDataConfigHtml(content_account_data, product_id, html) {
        $.each(content_account_data, function (k, i) {
            //选项赋值
            let val = i.val;

            html += '<div class="form-group"><label class="col-sm-3 control-label">' + i.cn_name + '：</label>';
            i.default = !!i.default ? i.default : '';
            //[1 =>单行文本框、2=>多行文本框,3=>单选按钮, 4 =>多选按钮,5=>时间元素 ,6 =>富文本编辑框 ,7=>单选下拉框,8 =>多选下拉框]
            switch (i.type) {
                case 1:
                    // 文本框
                    html += '<div class="col-sm-4">';
                    // H5邦秒爬的cid  && token是不可以更改的
                    if (parseInt(product_id) === 301 && (i.name === 'cid' || i.name === 'token')) {
                        html += '<input type="text" class="form-control" name="data[' + i.name + ']" value="' + i.val + '" placeholder="">';
                    } else {
                        html += '<input type="text" class="form-control" name="data[' + i.name + ']" value="' + i.val + '" placeholder="">';
                    }
                    html += '</div>';
                    break;
                case 2:

                    html += '<div class="col-sm-4">';
                    html += '<textarea type="text" name="data[' + i.name + ']" class="form-control">' + val + '</textarea>';
                    html += '</div>';
                    break;
                case 3:
                    // 设置单选按钮
                    html += setRadio(product_id, i);
                    break;
                case 4:
                    // 多选按钮
                    html += '<div class="col-sm-4">';
                    html += '<button class="btn btn-primary btn-sm checkbox_choose_all" style="margin-right:10px;" type="button">全选/取消</button>';
                    $.each(i.option, function (ko, io) {
                        // 解决int string 不兼容的问题
                        let type_change = typeof io.opt_val === 'number' ? io.opt_val.toString() : parseInt(io.opt_val);
                        let determine_checked = i.val ? ((i.val.indexOf(io.opt_val) !== -1) || (i.val.indexOf(type_change) !== -1)) : false;
                        let checked_str = determine_checked ? 'checked' : '';

                        html += '<label class="checkbox-inline">';
                        html += '<input type="checkbox" name="data[' + i.name + '][]" value="' + io.opt_val + '" ' + checked_str + '>' + io.opt_name;
                        html += '</label>';
                    });
                    html += '</div>';
                    break;
                case 5:
                    html += '<div class="col-sm-4">';
                    html += '<input type="date" name="data[' + i.name + ']" value="' + val + '" class="form-control">';
                    html += '</div>';
                    break;
                case 6:
                    // 富文本编辑框
                    html += '<div class="col-sm-9">';
                    html += '<div id="editor_' + i.name + '"></div>';
                    html += '<textarea id="' + i.name + '" name="data[' + i.name + ']" style="width:100%; height:200px; display: none"></textarea>';
                    html += '</div>';
                    break;
                case 7:
                    // 单选下拉框
                    html += iniSelectSingle(product_id, i);

                    break;
                case 8:
                    console.log(i, '多选下拉框');
                    // 多选下拉框
                    html += iniSelectMultiple(product_id, i);
                    break;
                case 9:
                    // console.log(i, '多选文本框');
                    // 多选文本框
                    html += iniInputMultiple(i);
                    break;
            }
            if (!!i.placeholder) {
                html += '<span class="col-sm-5"><label class="alert alert-info control-label" style="height: 34px;">* ' + i.placeholder + '</label></span>';
            }
            html += '</div>';
        });
        $('#data_html').html(html);

        // 初始化夫文本框
        iniEdit(content_account_data);

        // 初始化下拉框
        iniSelect2();
    }

    // 单选下拉框
    function iniSelectSingle(product_id, item) {
        let html = '<div class="col-sm-9">';
        html += '<select  name="data[' + item.name + ']" class="form-control" style="width: 42%">';
        item.option.forEach(function (element) {
            let select_item = ($.inArray(element.opt_val, item.val) !== -1) ? 'selected' : '';
            html += '<option value="' + element.opt_val + '" ' + select_item + '>' + element.opt_name + '</option>';
        });
        html += '</select></div>';
        return html;
    }

    // 多选文本框
    function iniInputMultiple(item) {
        let html = '';

        // 如果没有设置
        if (item.val.length === 0) {
            let html = '<div class="col-sm-9 form-inline" style="width: 300px;">';
            html += '<input type="text" name="data[' + item.name + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
            html += '<button type="button" onclick=addInputForH5(this) class="btn btn-default">+</button>' +
                '</div>';
            return html;
        }

        item.val.forEach(function (val_item, index) {
            let max = val_item.max ? val_item.max : 0;
            // 如果不是第一个 则可以触发+;
            if (index === 0) {
                html += '<div class="col-sm-9 form-inline" style="width: 300px;">';
                html += '<input type="text" name="data[' + item.name + '][]" value="'+ max +'" multiple="multiple" class="form-control" style="width: 42%">';
                html += '</select>' +
                    '<button type="button" onclick=addInputForH5(this) class="btn btn-default">+</button>' +
                    '</div>';
            } else {
                html += '<div class="col-sm-9 form-inline col-sm-offset-3" style="width: 300px;">';
                html += '<input type="text" name="data[' + item.name + '][]" value="'+ max +'" multiple="multiple" class="form-control" style="width: 42%">';
                html += '</select>' +
                    '<button type="button" onclick=removeInputForH5(this) class="btn btn-default">-</button>' +
                    '</div>';
            }
        });
        return html;
    }
    function addInputForH5(that) {
        let html = '<div class="col-sm-9 form-inline col-sm-offset-3" style="width: 300px;">';
        html += '<input type="text"   name="data[scoreGradeLimit][]"  multiple="multiple" class="form-control" style="width: 42%">';
        html += '<button type="button" onclick="removeInputForH5(this)" class="btn btn-default">-</button>' +
            '</div>';

        $(html).insertAfter(that.parentNode);
    }

    // 删掉新增的h5
    function removeInputForH5(that) {
        that.parentNode.remove();
    }

    // 初始化多选下拉框
    function iniSelectMultiple(product_id, item) {
        // 如果是product_id是301
        if (parseInt(product_id) === 301) {
            return iniSelectMultipleForH5(item);
        }

        return iniSelectMultipleForCommon(item);
    }

    // 普通类型的多选
    function iniSelectMultipleForCommon(item) {
        let html = '<div class="col-sm-9">';
        html += '<select  name="data[' + item.name + '][]"  multiple="multiple" class="form-control bigdrop" style="width: 42%">';
        item.option.forEach(function (element) {
            // 是否已经选中
            let selected_item = ($.inArray(element.opt_val, item.val()) !== -1) ? "selected" : "";
            html += '<option value="' + element.opt_val + '"  ' + selected_item + '>' + element.opt_name + '</option>';
        });
        html += '</select></div>';
        return html;
    }

    // h5配置的多选框
    function iniSelectMultipleForH5(item) {
        let html = '';

        // 如果没有设置紧急联系人
        if (item.val.length === 0) {
            let html = '<div class="col-sm-9 form-inline">';
            html += '<select  name="data[' + item.name + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
            item.option.forEach(function (element) {
                html += '<option value="' + element.opt_val + '">' + element.opt_name + '</option>';
            });

            let item_str = JSON.stringify(item);
            html += '</select>' +
                '<button type="button" onclick=addSelectForH5(this,' + item_str + ') class="btn btn-default">+</button>' +
                '</div>';
            return html;
        }

        item.val.forEach(function (val_item, index) {
            html += '<div class="col-sm-9 form-inline col-sm-offset-3">';
            html += '<select  name="data[' + item.name + index + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
            item.option.forEach(function (element) {
                // 是否选中
                let select_item = ($.inArray(element.opt_val, val_item) !== -1) ? 'selected' : '';
                html += '<option value="' + element.opt_val + '" ' + select_item + '>' + element.opt_name + '</option>';
            });

            let item_str = JSON.stringify(item);

            // 如果不是第一个 则可以触发+;
            if (index === 0) {
                html += '</select>' +
                    '<button type="button" onclick=addSelectForH5(this,' + item_str + ') class="btn btn-default">+</button>' +
                    '</div>';
            } else {
                html += '</select>' +
                    '<button type="button" onclick=removeSelectForH5(this) class="btn btn-default">-</button>' +
                    '</div>';
            }
        });
        return html;
    }

    // 初始化select2
    function iniSelect2() {
        $('select').select2({
            allowClear: false,
            theme: "bootstrap",
            width: 'resolve',
        });
    }

    // 为301增加select控制
    function addSelectForH5(that, item) {
        // 增加select2
        addEmergencySelectForH5(that, item);

        // 初始化新增的select
        iniSelect2();
    }

    // h5邦秒爬配置select下拉框
    function addEmergencySelectForH5(that, item) {
        let selector_length = $('select').length;
        let html = '<div class="col-sm-9 form-inline col-sm-offset-3">';
        html += '<select  name="data[emergency_contact_detail_limits' + selector_length + '][]"  multiple="multiple" class="form-control" style="width: 42%">';
        item.option.forEach(function (element) {
            html += '<option value="' + element.opt_val + '">' + element.opt_name + '</option>';
        });
        html += '</select>' +
            '<button type="button" onclick="removeSelectForH5(this)" class="btn btn-default">-</button>' +
            '</div>';

        $(html).insertAfter(that.parentNode);
    }

    // 删掉新增的h5 select
    function removeSelectForH5(that) {
        that.parentNode.remove();
    }

    // 设置单选按钮
    function setRadio(product_id, i) {
        // 301的授权协议需要单独处理
        if (parseInt(product_id) === 301 && i.name === 'protocol_default') {
            let html = '<div class="col-sm-4">';
            $.each(i.option, function (ko, io) {
                let checkedstr = '';
                if (i.val === io.opt_val) {
                    checkedstr = 'checked=""';
                }
                html += '<label class="radio-inline">';
                html += '<input type="radio" id="' + i.name + '" name="data[' + i.name + ']" value="' + io.opt_val + '" ' + checkedstr + '>' + io.opt_name;
                html += '</label>';
            });

            // 预览
            html += '<a style="margin-left: 5px" href="javascript:void(0);" target="_blank" onclick="preview()" class="btn btn-primary btn-xs">预览</a>';
            html += '</div>';
            return html;
        }

        let html = '<div class="col-sm-4">';
        $.each(i.option, function (ko, io) {
            let checkedstr = '';
            if (i.val === io.opt_val) {
                checkedstr = 'checked=""';
            }
            html += '<label class="radio-inline">';
            html += '<input type="radio" name="data[' + i.name + ']" value="' + io.opt_val + '" ' + checkedstr + '>' + io.opt_name;
            html += '</label>';
        });
        html += '</div>';
        return html;
    }

    // 预览授权协议
    function preview() {
        // 获取预览地址
        $.get('/Api/BackendPreview/previewUrl').success(function (response) {
            if (response.status === 0) {

                // 执行预览操作
                let preview_url = response.preview_url;
                previewDo(preview_url);
            } else {
                console.log('请求预览地址失败', response);
            }
        }).error(function (response) {
            console.log('请求预览地址失败', response);
        });
    }

    // 预览
    function previewDo(preview_url) {
        // 获取预览协议
        let protocol = getProtocolForPreview();
        $('#preview_protocol').html('<form id="preview_form" target="NewWindow" action="' + preview_url + '" method="POST" style="display:none;"><textarea name="protocol_content">' + protocol + '</textarea></form>');
        window.open("", 'NewWindow');
        $('#preview_form').submit();
    }

    // 获取预览协议
    function getProtocolForPreview() {
        let protocol = $('#protocol_content').val();
        let protocol_default = $('input[name="data[protocol_default]"]:checked', '#form_account').val();

        // 如果是默认协议的话 则API获取协议
        if (parseInt(protocol_default) === 1) {
            $.ajax({
                url: '/Api/BackendPreview/defaultProtocol',
                async: false,
                dataType: 'json'
            }).success(function (response) {
                if (response.status === 0) {
                    protocol = response.protocol_default;
                } else {
                    console.log('获取默认协议出错');
                }

            }).error(function (response) {
                console.log('获取默认协议出错');
            });
        }
        return protocol;
    }


    // 初始化富文本框
    function iniEdit(data_config) {
        // 富文本框的处理
        $.each(data_config, function (key, item) {
            if (parseInt(item.type) !== 6) {
                return true;
            }

            // 设置富文本框的默认值 && 文本域selector
            let editor_val = item.val;
            $('#editor_' + item.name).append($(editor_val));
            let textarea = $('#' + item.name);

            // 授权协议(富文本框)
            let editor = new window.wangEditor('#editor_' + item.name);

            editor.customConfig.onchange = function (html) {
                console.log('变化', html);
                // 监控变化，同步更新到 textarea
                textarea.val(html);
            };
            editor.customConfig.pasteFilterStyle = false; // 不对粘贴的样式过滤
            editor.customConfig.uploadImgShowBase64 = true;  // 使用 base64 保存图片
            editor.customConfig.showLinkImg = true; // 使用网络照片
            editor.create();

            // 初始化 textarea 的值
            textarea.val(editor.txt.html());
        });

    }

    // 表单提交事件
    function formSubmit() {
        $('#form_account').submit(function () {
            // 检查参数
            if (checkParams() === false) {
                return false;
            }

            // 关键字段变动预警
            introWarning();

            // 发送表单
            event.preventDefault();
        });
    }

    // 关键字段变动预警
    function introWarning() {
        let payload = {
            product_new: $('#form_account').serializeArray(),
            product_old: window.edit
        };

        let url_warning = '/Api/BackendProduct/warningWhenUpdateImportField';
        axios.post(url_warning, payload).then(function (response) {
            console.log(response, '检查重要字段是否发生了变化');
            if (JSON.stringify(response.data.import_fields_changed) !== '[]') {
                return showUpdateContent(response.data.import_fields_changed);
            }

            formRequest();
        });

    }

    // 提交表单
    function formRequest() {
        let data_request = $('#form_account').serialize();
        let url_request = '/Api/BackendProduct/updateProduct';
        $.post(url_request, data_request).success(function(response){
            console.log(response);

            // 兼容提交审核提醒
            if (response.status === 'success') {
                alert(response.info);
                return '';
            }
            if (response.status !== 0) {
                alert(response.errors.msg);
                return '';
            }
            alert('编辑产品成功');
            if ('{$Think.get.callback_url}'=='') {
                window.location.href = "{:U('Account/Account/index')}";
            } else {
                window.location.href = '{$Think.get.callback_url}';
            }
        }).error(function(response){
            console.log(response);
            alert('编辑产品出错，请稍后重试');
        });
    }

    // 检查参数
    function checkParams() {
        let product_id = $('#product_id').val();
        let end_time = $('#end_time').val().trim();
        if (!product_id) {
            alert('请选择产品');
            return false;
        }
        if (!end_time) {
            alert('截止日期未填写');
            return false;
        } else {
            // 选定的时间必须是大于今天的
            let today_str = (new Date()).toDateString();
            let time_diff = new Date(Date.parse(end_time)) - new Date(Date.parse(today_str));
            if (time_diff < 0) {
                alert('截止日期不能小于当前日期');
                return false;
            }
        }
        //其他限制
        if (!checkBaseParams()) {
            return false;
        }
        
        let status = $("#status").val();
        if (status==0) {
            return confirm("当状态设置为禁用时，所属此开通产品的计费配置将会归零，规则为：按用量-固定价格且单价为0的计费配置，计费开始日期为系统当前日期+1；请谨慎操作！谢谢！");
        }
        return true;
    }
    //基础参数限制
    function checkBaseParams()
    {
        let daily_limit = $("#daily_limit").val();
        if (!checkDosage(daily_limit)) {
            alert('日限量格式不正确');
            return false;
        }
        let month_limit = $("#month_limit").val();
        if (!checkDosage(month_limit)) {
            alert('月限量格式不正确');
            return false;
        }
        let year_limit = $("#year_limit").val();
        if (!checkDosage(year_limit)) {
            alert('年限量格式不正确');
            return false;
        }
        let total_limit = $("#total_limit").val();
        console.log(daily_limit, month_limit, year_limit, total_limit);
        if (!checkDosage(total_limit)) {
            alert('总限量格式不正确');
            return false;
        }
//        if (parseInt(daily_limit)>parseInt(month_limit)) {
//            alert('日限额不可大于月限额');
//            return false;
//        }
//        if (parseInt(month_limit)>parseInt(year_limit)) {
//            alert('月限额不可大于年限额');
//            return false;
//        }
//        if (parseInt(year_limit)>parseInt(total_limit)) {
//            alert('年限额不可大于总限额');
//            return false;
//        }
        //秒并发
        let concurrency = $("#concurrency").val();
        if (!checkNumber(concurrency)) {
            alert("秒并发格式不正确");
            return false;
        }
        if (parseInt(concurrency)<=0 || parseInt(concurrency)>100) {
            alert('秒并发的值区间为0~100，不包含0');
            return false;
        }
        return true;
    }
    //用量限额
    function checkDosage(data) {
        if (checkNumber(data)) {
            if (data==-1 || data>0) {
                return true;
            }
        }
        return false;
    }
    //检查是否为数字格式
    function checkNumber(data)
    {
        let number_preg = /^\-?\d{1,}$/;
        return number_preg.test(data);
    }
</script>
</body>
</html>
