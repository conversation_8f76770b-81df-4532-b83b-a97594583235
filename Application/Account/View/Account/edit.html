<!DOCTYPE html>
<html>
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    -->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            width:100%;
            height : 240px !important;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回列表</a>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="/Account/Account/edit" method="post" class="form-horizontal" id="form_account">
                            <div class="form-group">
                                <label for="account_name" class="control-label col-md-3">账号名称：</label>
                                <div class="col-md-4">
                                    <input type="text" name="account_name" id="account_name" class="form-control" value="<?= $account_info['account_name']?>">
                                </div>
                            </div>

<!--                            <div class="form-group">-->
<!--                                <label for="email" class="control-label col-md-3">登录邮箱：</label>-->
<!--                                <div class="col-md-4">-->
<!--                                    <input type="text" name="email" class="form-control" id="email" value="<?= $account_info['email']?>">-->
<!--                                </div>-->
<!--                                <div class="col-md-1"></div>-->
<!--                                <div class="col-md-2">-->
<!--                                    <input type="button" onclick="DHB.INFO.set('{:U('resetPwd',array('id'=>$account_info['id']))}','提示')" class="btn btn-info btn-sm" value="重置密码">-->
<!--                                </div>-->
<!--                            </div>-->

                            <div class="form-group">
                                <label class="control-label col-md-3">所属客户：</label>
                                <div class="col-md-4">
                                    <select class="form-control" readonly="">
                                        <option value=""><?= $account_info['name']?></option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-md-3">账号类型：</label>
                                <div class="col-md-4">
                                    <select name="type" class="form-control">
                                        <option value="1" <?= $account_info['type'] == 1 ? 'selected' : '' ?> >正式账号</option>
                                        <option value="0" <?= $account_info['type'] != 1 ? 'selected' : '' ?> >测试账号</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="status" class="control-label col-sm-3">账号状态：</label>
                                <div class="col-md-4">
                                    <select name="status" id="status" class="form-control">
                                        <option value="1" <?= $account_info['status'] == 1? 'selected' : '' ?> >可用</option>
                                        <option value="0" <?= $account_info['status'] !=1 ? 'selected' : '' ?> >禁用</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">APIKEY：</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" name="apikey" id="input_apikey" value="<?= $account_info['apikey']?>" readonly>
                                </div>
                                <div class="col-sm-1"></div>
                                <div class="col-sm-1">
                                    <button type="button" class="btn btn-info btn-sm" name="input_apikey" onclick="edit_hash('input_apikey', 32)">生 成</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">APPSECRET：</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" id="input_appsecret" name="appsecret" value="<?= $account_info['appsecret']?>" readonly>
                                </div>
                                <div class="col-sm-1"></div>
                                <div class="col-sm-1">
                                    <button type="button" class="btn btn-info btn-sm" onclick="edit_hash('input_appsecret', 64)">生 成</button>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="close_zhilian_radio1" class="control-label col-md-3">直连关闭：</label>
                                <div class="col-sm-4">
                                    <label class="radio-inline">
                                        <input type="radio" name="close_zhilian" id="close_zhilian_radio1" value="0"  <?= $account_info['close_zhilian'] == 0 ? 'checked' : ''; ?> >否
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="close_zhilian" id="close_zhilian_radio2" value="1"  <?= $account_info['close_zhilian'] == 1 ? 'checked' : ''; ?> >是
                                    </label>
                                </div>
                                <input type="hidden" name="appsecret_bak" value="<?= $account_info['appsecret_bak']?>"/>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">截止日期：</label>
                                <div class="col-sm-4">
                                    <input type="date" class="form-control" name="end_time" id="end_time" value="<?= $account_info['end_time']?>"/>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="cache_days" class="control-label col-md-3">缓存时长(天)：</label>
                                <div class="col-sm-4">
                                    <input type="number" name="cache_days" id="cache_days" class="form-control" value="<?= $account_info['cache_days']?>">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="account_name" class="control-label col-md-3">账号秒并发：</label>
                                <div class="col-md-4">
                                    <input type="text" name="concurrency" id="concurrency" class="form-control" value="<?= $account_info['concurrency']?>">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">IP白名单：</label>
                                <div class="col-sm-4">
                                    <textarea type="text" class="form-control" name="access_ip" id="access_ip"><?= $account_info['access_ip']?></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">备注：</label>
                                <div class="col-sm-4">
                                    <textarea type="text" class="form-control" name="mark" id="mark"><?= $account_info['mark']?></textarea>
                                </div>
                            </div>

                            <div class="pull-right">
                                <input type="hidden" name="customer_id" value="<?= $account_info['customer_id']?>">
                                <!--<input type="hidden" name="father_id" value="<?= $account_info['account_id']?>">-->
                                <input type="hidden" name="id" value="<?= $account_info['id']?>">
                                <ul class="list-inline">
                                    <li><input type="submit" class="btn btn-primary btn-sm" value="保存"></li>
                                    <li><a href="http://php.net/manual/zh/function.strpos.php" onclick="return goBack()" class="btn btn-info btn-sm">返回</a></li>
                                </ul>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    new Vue({
        el : '#account_app'
    });

    $(function () {
        // 表单提交事件
        formSubmit();
    });

    // 表单提交事件
    function formSubmit() {
        $('#form_account').submit(function(){
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request = $(that).attr('action');
        if ('{$Think.get.callback_url}'=='') {
            var url_redirect = "{:U('Account/Account/index')}";
        } else {
            var url_redirect = '{$Think.get.callback_url}';
        }
        $.post(url_request, data_request).success(function(response){
            if (response.status !== 'success') {
                modalExport(response.info);
                return '';
            }
            alert( response.info??'保存账号成功');
            window.location.href = url_redirect;
        }).error(function(response){
            modalExport('创建用户出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var name = $('#account_name').val().trim();
        // var email = $('#email').val().trim();
        var end_time = $('#end_time').val().trim();
        if (!name) {
            modalExport('账号名称未填写');
            return false;
        }
        if (name.length < 2) {
            modalExport('账号的名字的长度需要不可以小于2');
            return false;
        }
        // if (!email) {
        //     modalExport('邮箱未填写');
        //     return false;
        // }
        if (!end_time) {
            modalExport('截止日期未填写');
            return false;
        } else {
            // 选定的时间必须是大于今天的
            var today_str = (new Date()).toDateString();
            var time_diff = new Date(Date.parse(end_time)) - new Date(Date.parse(today_str));
            if (time_diff < 0) {
                modalExport('截止日期不能小于当前日期');
                return false;
            }
        }
        var cache_days = $('#cache_days').val().trim();
        if(cache_days < 0){
            modalExport('缓存时长不可小于0');
            return false;
        }

        var status = $("#status").val();
        if (status==0) {
            return confirm('当账号状态设置为禁用时，此账号下所有计费配置会增加一条计费规则为：按用量-固定价格且单价为0的计费配置，计费开始日期为系统当前日期+1；请谨慎操作！谢谢！');
        }
        return true;
    }

    function creat_hash(id,length){
        DHB.ajax({
            url:"{:U('Home/Tool/hashid')}",
            type:'get',
            data:{"length":length},
            success:function(r){
                $("#"+id).val(r['data']);
            }
        });
    }

    function edit_hash(id, length)
    {
        if(confirm('修改apikey,appsecret会使当前账号的授权失效，确定这样做吗？')) {
            creat_hash(id, length);
        }
    }

    // 返回上级菜单
    function goBack() {
        event.preventDefault();
        window.history.back();
    }
</script>
</body>
</html>
