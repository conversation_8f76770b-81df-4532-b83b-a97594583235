<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>

<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
    <div class="container">
        <a href="{$Think.get.callback_url}" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回列表</a>
    </div>
</div>
<style>

    [v-cloak] {
      display: none;
    }

    .hader_explain{
        font-size: 0.8em;
        font-weight: normal;
    }
    .customer_name{
        font-size: 1.5em;
        font-weight: bolder;
    }
    .account_name{
        font-size: 1.2em;
        font-weight: bold;
    }
</style>

<!--发票明细-->
<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <div>
            <span class="customer_name">{{customer_name}}</span>
            <i class="el-icon-arrow-right"></i>
            <span class="account_name">{{account_name}}</span>
        </div>
        <template>
            <el-table :data="list" border stripe fit ref="main_table">
                <el-table-column label="父产品" prop="father_name"></el-table-column>
                <el-table-column label="产品名称" prop="product_name"></el-table-column>
                <el-table-column label="产品id" prop="product_id"></el-table-column>
                <el-table-column label="状态" prop="status_name"></el-table-column>
                <el-table-column label="截止日期" prop="end_time_fmort"></el-table-column>
                <el-table-column label="已换签产品" >
                    <template slot="header">
                        <div>换签</div>
                        <div class="hader_explain">绿色为已换签, 灰色为未换签, 点击更改换签状态</div>
                    </template>
                    <template slot-scope="scope">
                        <el-button-group>
                            <template v-for="item in scope.row.source_tags">
                                <el-button :type="item.is_change?'success':'info'" size="small" @click="changeTag(scope.row.id,scope.row.account_id,scope.row.product_id,item.source,item.is_change)">{{item.source_name}}</el-button>
                            </template>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </div>
</div>

<script type="application/javascript">
    let account_id = '{$account_id}';
    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,
            user_cookie:user_cookie,
            account_id:account_id,
            //接口地址
            urls: {
                // user_auth : url_prefix + '/options/getMap?user_auth=true&user_cookie=' + user_cookie,
                product_list  : url_prefix + '/account/product/list', //帐号开通产品列表
                change_source : url_prefix + '/account/product/change_source', //帐号开通产品列表
            },

            customer_name:'',
            account_name:'',
            list: [],

        },
        created: function () {
            this.getData();
        },
        methods: {
            //获取驳回列表数据
            getData: async function () {
                let self = this;
                self.loading = true;
                let para = {
                    user_cookie: user_cookie,
                    account_id:self.account_id,
                };
                await axios.post(self.urls.product_list, para).then(function (response) {
                    self.customer_name = response.data.data.customer_name;
                    self.account_name = response.data.data.account_name;
                    self.list = response.data.data.list;
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },

            changeTag:function(id,account_id,product_id,source,is_change){
                let self = this;

                // 如果是取消 给出弹窗提示
                if(is_change){
                    self.$confirm('该产品已经换签, 该操作将取消标记,是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        self.change_tag(id,account_id,product_id,source,is_change);
                    }).catch(() => {
                        successMsg('已取消');
                    });
                }else{
                    self.change_tag(id,account_id,product_id,source,is_change);
                }
            },
            change_tag:function(id,account_id,product_id,source,is_change) {
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    id:id,
                    account_id:account_id,
                    product_id:product_id,
                    source:source,
                    is_change:is_change,
                };

                axios.post(self.urls.change_source, para).then(function (response) {
                    let res = response.data.data;
                    if(!res){
                        errorMsg('操作失败');
                    }else{
                        successMsg('操作成功');
                        self.getData();
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },

            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>