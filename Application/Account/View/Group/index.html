<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    [v-cloak] {
      display: none;
    }

    .copy_btn_icon{
        padding-top: 0;
        padding-bottom: 0;
    }

    /*弹窗内容居中*/
    .el-dialog--center .el-dialog__body {
        margin: 5px auto;
    }

    /*transfer 高度*/
    .el-transfer-panel__body {
        height: 546px;
    }
    .el-transfer-panel__list.is-filterable {
        height: 494px;
    }
    .el-dialog__body {
        width: 721px;
    }

    .show_group_customers_dialog .el-dialog__body {
        width: 100%;
    }

</style>

<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-body">
                <el-form :inline="true" :model="search_form" label-width="100px" class="demo-form-inline" size="mini">
                    <el-form-item label="主体名:">
                        <el-select v-model="search_form.group_id" filterable clearable placeholder="主体">
                            <el-option v-for="item in group_map" :key="item.group_id" :label="item.group_name" :value="item.group_id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchTableData()">查询</el-button>
                        <el-button type="success" @click="show_add_group_dialog()">添加主体</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
    <div class="container">
        <template>
            <el-table :data="table_data" border stripe fit>
                <el-table-column label="主体ID">
                    <template slot-scope="scope">
                        <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.group_id" icon="el-icon-document-copy"></el-button>
                        <span >{{ scope.row.group_id }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="主体名称">
                    <template slot-scope="scope">
                        <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.group_name" icon="el-icon-document-copy"></el-button>
                        <span >{{ scope.row.group_name }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="客户数量" prop="customer_count">
                    <template slot-scope="scope">
                        <el-button type="text" class="" @click="show_group_customer_list(scope.row.group_id)"><span>{{ scope.row.customer_count }}</span></el-button>
                    </template>
                </el-table-column>
                <el-table-column label="备注" prop="remark"></el-table-column>
                <el-table-column label="状态" prop="status_name"></el-table-column>
                <el-table-column label="操作人" prop="admin_name"></el-table-column>
                <el-table-column label="时间" >
                    <template slot-scope="scope">
                        创建: {{scope.row.created_at}}<br />
                        更新: {{scope.row.updated_at}}
                    </template>
                </el-table-column>

                <el-table-column fixed="right" fit label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="show_edit_group_dialog(scope.row.group_id,scope.row.group_name,scope.row.status,scope.row.remark)">编辑</el-button>
                        <el-button size="mini" @click="show_edit_group_customer_dialog(scope.row.group_id,scope.row.group_name,scope.row.customer_list)">编辑客户</el-button>
                    </template>
                </el-table-column>

            </el-table>
            <div class="block" style="margin-bottom: 16px;">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="page_size" layout="total, sizes, prev, pager, next, jumper" :total="total_num"></el-pagination>
            </div>
        </template>
    </div>


    <!--弹窗-->
    <!--添加主体-->
    <el-dialog title="添加主体" :visible.sync="add_group_dialog_visible" center>
        <el-form :model="add_group_form" label-position="right" label-width="80px" @submit.native="add_group_enter()" style="width: 90%;">
            <el-form-item label="主体名称">
                <el-input v-model="add_group_form.group_name" autocomplete="off"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="add_group_dialog_visible = false">取消</el-button>
            <el-button type="primary" @click="add_group()">添加</el-button>
        </div>
    </el-dialog>
    <!--添加主体-->





    <!--编辑主体-->
    <el-dialog title="编辑主体" :visible.sync="edit_group_dialog_visible" center>
        <el-form :model="edit_group_form" label-position="right" label-width="80px" @submit.native="deit_group_enter()">
            <el-form-item label="主体名称">
                <el-input v-model="edit_group_form.group_name" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="备注">
                <el-input v-model="edit_group_form.remark" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="是否启用">
                <el-radio v-model="edit_group_form.status" :label="1">启用</el-radio>
                <el-radio v-model="edit_group_form.status" :label="2">禁用</el-radio>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="edit_group_dialog_visible = false">取消</el-button>
            <el-button type="primary" @click="edit_group()">修改</el-button>
        </div>
    </el-dialog>
    <!--编辑主体-->


    <!--编辑主体客户-->
    <el-dialog :title="'管理客户: '+edit_group_customer_group_name" :visible.sync="edit_group_customer_dialog_visible" center >
        <el-transfer
                filterable
                target-order="push"
                :filter-method="edit_group_customer_filter"
                filter-placeholder="请输入客户名"
                :titles="['未关联主体客户', '当前主体客户']"
                :button-texts="['取消关联', '关联']"
                v-model="edit_group_customer_data"
                :data="edit_group_customer_list_data">
        </el-transfer>
        <div slot="footer" class="dialog-footer">
            <el-button @click="edit_group_customer_dialog_visible = false">取消</el-button>
            <el-button type="primary" @click="edit_group_customer_list_fun()">修改</el-button>
        </div>
    </el-dialog>
    <!--编辑主体客户-->




    <!--主体客户列表-->
    <el-dialog title="客户列表" :visible.sync="show_group_customers_dialog_visible" center class="show_group_customers_dialog">
        <el-table :data="show_group_customer_list_data" stripe>
            <el-table-column label="客户ID" prop="remark">
                <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_id" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.customer_id }}</span>
                </template>
            </el-table-column>
            <el-table-column label="客户名">
                <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.name" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="公司名">
                <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.company" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.company }}</span>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button @click="show_group_customers_dialog_visible = false">取消</el-button>
        </div>
    </el-dialog>
    <!--主体客户列表-->

    <!--弹窗-->
</div>

<script type="application/javascript">
    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,

            user_cookie:user_cookie,
            //接口地址
            urls: {
                user_auth           : url_prefix + '/options/getMap?user_auth=true&user_cookie=' + user_cookie,
                customer_list       : url_prefix + '/options/getMap?customer=true&include_group_id=true&user_cookie=' + user_cookie,
                customer_group_list : url_prefix + '/customer/group/list', //获取客户主体列表
                group_map           : url_prefix + '/customer/group/group_map', //所有主体
                add                 : url_prefix + '/customer/group/add', //添加主体
                edit                : url_prefix + '/customer/group/edit', //编辑主体
                edit_group_customer_list :  url_prefix + '/customer/group/edit_group_customer_list', //编辑主体客户
                group_customer_list :  url_prefix + '/customer/group/group_customer_list', //获取主体客户
            },
            //客户列表
            table_data: [],
            group_map:[],

            //分页
            total_num: 0,
            page_size: 20,
            current_page: 1,

            ok_confirm: false,

            //搜索条件
            search_form: {
                group_id: '',
                status: 1
            },

            //添加主体
            add_group_dialog_visible:false,
            add_group_form:{
                group_name:'',
            },


            //编辑主体
            edit_group_dialog_visible:false,
            edit_group_form:{
                group_id:'',
                group_name:'',
                status:'',
                remark:'',
            },

            //编辑主体客户
            edit_group_customer_dialog_visible: false,
            edit_group_customer_group_id: '',
            edit_group_customer_group_name: '',
            edit_group_customer_data:[],
            edit_group_customer_list_data:[],
            edit_group_customer_origin_data_list:[],

            //展示主体客户
            show_group_customers_dialog_visible: false,
            show_group_customer_list_data:[],
        },
        mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);
        },
        created: function () {
            this.getTableData();
            this.get_all_customer();
        },
        methods: {
            //获取驳回列表数据
            getTableData: function () {
                let self = this;
                self.loading = true;

                let group_id = self.search_form.group_id;

                let para = {
                    limit: self.page_size,
                    page: self.current_page,
                    user_cookie: user_cookie,
                };
                if (group_id) {
                    para.group_id = group_id;
                }

                axios.post(self.urls.customer_group_list, para).then(function (response) {
                    self.table_data = response.data.data.list;
                    self.total_num = response.data.data.count;
                    self.loading = false;
                    self.getOptions();
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            get_all_customer: function () {
                let self = this;
                self.loading = true;

                axios.get(self.urls.customer_list).then(function (response) {
                    let customer_list = response.data.data.customer;
                    self.edit_group_customer_origin_data_list = [];
                    for (const customer_id in customer_list) {
                        self.edit_group_customer_origin_data_list.push(
                            {
                                name: customer_list[customer_id]['name'],
                                customer_id: customer_id,
                                group_id:customer_list[customer_id]['group_id']
                            }
                        );
                    }
                    console.log(self.edit_group_customer_origin_data_list);
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            //获取客户和产品列表
            getOptions: function () {
                let self = this;
                axios.post(self.urls.group_map, {}).then(function (response) {
                    self.group_map = response.data.data;
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            //修改每页条书
            handleSizeChange(val) {
                this.page_size = val;
                this.current_page = 1;
                this.getTableData();
            },
            //修改页数,获取数据
            handleCurrentChange(val) {
                this.current_page = val;
                this.getTableData();
            },
            //表单搜索
            searchTableData: function () {
                this.current_page = 1;
                this.getTableData();
            },


            //添加主体 展示弹窗
            show_add_group_dialog:function(){
                this.add_group_dialog_visible = true;
            },
            //添加主体
            add_group:function (){
                let self = this;

                let group_name = self.add_group_form.group_name;
                let para = {
                    group_name: group_name,
                    user_cookie: user_cookie,
                };

                axios.post(self.urls.add, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.loading = false;
                        successMsg('添加成功');
                        self.add_group_dialog_visible = false;
                        self.add_group_form.group_name = '';

                        self.current_page = 1;
                        self.getTableData();
                    } else {
                        // self.add_group_form.group_name = '';
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            add_group_enter:function(event) {
                // 阻止默认的表单提交行为
                event.preventDefault();
                this.add_group();
            },
            //管理客户
            show_edit_group_customer_dialog:function(group_id,group_name,customer_list){
                this.edit_group_customer_group_id = group_id;
                this.edit_group_customer_group_name = group_name;

                this.edit_group_customer_dialog_visible = true;

                this.edit_group_customer_list_data = [];
                this.edit_group_customer_data = [];
                for (let i = 0; i < customer_list.length; i++) {
                    this.edit_group_customer_data.push(customer_list[i]['customer_id']);
                }
                console.log(this.edit_group_customer_data);

                for (let i = 0; i < this.edit_group_customer_origin_data_list.length; i++) {
                    let origin_info = this.edit_group_customer_origin_data_list[i];
                    this.edit_group_customer_list_data.push(
                        {
                            label: origin_info['name'],
                            key: origin_info['customer_id'],
                            disabled: origin_info['group_id'] !== '' && origin_info['group_id'] !== group_id,
                        }
                    );
                }
                console.log(this.edit_group_customer_list_data);
            },

            edit_group_customer_list_fun:function(){
                this.$confirm('此操作将变更主体关系, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {



                    let self = this;
                    self.loading = true;

                    let para = {
                        group_id: self.edit_group_customer_group_id,
                        customer_list:self.edit_group_customer_data,
                        user_cookie: user_cookie,
                    };
                    console.log(para);

                    axios.post(self.urls.edit_group_customer_list,para).then(function (response) {

                        if (response.data.status === 0) {
                            self.loading = false;
                            self.edit_group_customer_dialog_visible = false;

                            successMsg('编辑成功');
                            self.get_all_customer();
                            self.current_page = 1;
                            self.getTableData();
                        } else {
                            errorMsg(response.data.msg);
                        }
                    }).catch(function (error) {
                        errorMsg(error);
                        self.loading = false;
                });

                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消'
                    });
                });
            },

            edit_group_customer_filter:function(query,item){
                if(query === ""){
                    return true;
                }
                console.log('edit_group_customer_filter:');
                console.log(query,item);

                return item.label.indexOf(query) > -1;
            },

            edit_group_customer: function(){
                console.log('编辑主体客户:');
                console.log(this.edit_group_customer_group_name);
            },

            //修改主体
            show_edit_group_dialog:function(group_id,group_name,status,remark){
                this.edit_group_form.group_id = group_id;
                this.edit_group_form.group_name = group_name;
                this.edit_group_form.status = status;
                this.edit_group_form.remark = remark;

                this.edit_group_dialog_visible = true;

            },
           //修改主体
            edit_group:function (){
                let self = this;

                let group_id = self.edit_group_form.group_id;
                let group_name = self.edit_group_form.group_name;
                let status = self.edit_group_form.status;
                let remark = self.edit_group_form.remark;
                let para = {
                    group_id: group_id,
                    group_name: group_name,
                    status: status,
                    remark: remark,
                    user_cookie: user_cookie,
                };

                axios.post(self.urls.edit, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.loading = false;
                        successMsg('编辑成功');
                        self.edit_group_dialog_visible = false;

                        self.edit_group_form.group_id = '';
                        self.edit_group_form.group_name = '';
                        self.edit_group_form.status = '';

                        self.current_page = 1;
                        self.getTableData();
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            edit_group_enter:function(event) {
                // 阻止默认的表单提交行为
                event.preventDefault();
                this.edit_group();
            },

            show_group_customer_list:function(group_id){
                let self = this;
                self.loading = true;

                self.show_group_customer_list_data = [];

                axios.get(self.urls.group_customer_list+'?group_id='+group_id).then(function (response) {
                    if (response.data.status === 0) {
                        self.loading = false;
                        console.log(response.data.data);
                        self.show_group_customer_list_data = response.data.data;
                        self.show_group_customers_dialog_visible = true;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                })
            },

            //修改每页条书
            handleSizeChangeDidd(val) {
                this.didd.page_size = val;
                this.didd.current_page = 1;
                this.get_ddid_data();
            },
            //修改页数,获取数据
            handleCurrentChangeDidd(val) {
                this.didd.current_page = val;
                this.get_ddid_data();
            },
            successFunc: function(e) {
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc:function(e) {
                console.error("Action:", e.action);
                console.error("Trigger:", e.trigger);

                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>