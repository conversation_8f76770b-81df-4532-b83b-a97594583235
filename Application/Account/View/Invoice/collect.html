<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>

</head>

<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>

    [v-cloak] {
      display: none;
    }

    /*.unc_title{*/
    /*    font-size:1.1rem;*/
    /*}*/

    /*.unc_source_name{*/
    /*    font-size:1.1rem;*/
    /*}*/

    .unc_fount{
        font-weight: bold;
        font-size:1.3rem;
        color:red;
    }
    .copy_btn_icon{
        padding-top: 0;
        padding-bottom: 0;
    }
    .copy_btn{
        padding-top: 0;
        padding-bottom: 0;
    }
    /*.el-table .el-table__cell{*/
    /*    padding:0;*/
    /*}*/
</style>

<!--发票明细-->
<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-body">
                <el-form :inline="true" :model="search_form" label-width="100px" class="demo-form-inline" size="mini">
                    <el-form-item label="来源:">
                        <el-select v-model="search_form.source" filterable clearable placeholder="请选择">
                            <el-option v-for="item in source_map" :key="item.source" :label="item.name" :value="item.source"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="客户:">
                        <el-select v-model="search_form.customer" filterable clearable placeholder="请选择">
                            <el-option v-for="(item,index) in customer_list" :key="index" :label="item" :value="index"></el-option>
                        </el-select>
                    </el-form-item>
                    <!--<el-form-item label="月份:">-->
                    <!--    <el-date-picker-->
                    <!--            v-model="search_form.month"-->
                    <!--            type="month"-->
                    <!--            value-format="timestamp"-->
                    <!--            align="center"-->
                    <!--            unlink-panels-->
                    <!--            :default-value="default_month"></el-date-picker>-->
                    <!--</el-form-item>-->
                    <el-form-item label="月份:">
                        <el-date-picker v-model="search_form.month" type="monthrange" value-format="timestamp" align="center"
                                        unlink-panels range-separator="-" start-placeholder="开始" end-placeholder="结束"
                                        :default-time="['00:00:00', '23:59:59']"></el-date-picker>
                    </el-form-item>
                    <!--<el-form-item label="开票时间:">-->
                    <!--    <el-date-picker v-model="search_form.invoice_date" type="daterange" value-format="timestamp" align="center"-->
                    <!--                    unlink-panels range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"-->
                    <!--                    :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>-->
                    <!--</el-form-item>-->
                    <!--<el-form-item label="回款时间:">-->
                    <!--    <el-date-picker v-model="search_form.remit_date" type="daterange" value-format="timestamp" align="center"-->
                    <!--                    unlink-panels range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"-->
                    <!--                    :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions"></el-date-picker>-->
                    <!--</el-form-item>-->
                    <!--<el-form-item>-->
                    <!--    <el-switch v-model="search_form.include_es_product" active-text="包含企服产品" inactive-text="不包含企服产品"></el-switch>-->
                    <!--</el-form-item>-->
                    <el-form-item>
                        <el-button type="primary" @click="getTableData()">查询</el-button>
                        <el-button type="warning" @click="getTableDataExcel()">导出</el-button>
                    </el-form-item>
                    <!--<el-form-item>-->
                    <!--    <el-button type="success">导出</el-button>-->
                    <!--</el-form-item>-->
                </el-form>
            </div>
        </div>
    </div>
    <div class="container">

                <div v-loading="unc_loading">
                    <el-row>
                        <el-col :span="6" :offset="3"><span class="unc_title">发票结余合计</span></el-col>
                    </el-row>
                    <el-row v-for="(unc,source_name) in uninvoice_count">
                        <el-col :span="4" :offset="1"><span class="unc_source_name">{{source_name}}</span></el-col>
                        <el-col :span="8"><span class="unc_fount">{{unc}}</span></el-col>
                    </el-row>
                </div>
                <br/>
                <el-table :data="table_data" border stripe fit>
                    <el-table-column label="客户名称" align="left" width="222">
                        <template slot-scope="scope">
                            <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_id" icon="el-icon-document-copy"></el-button>
                            <span>{{ scope.row.customer_id }}</span>
                            <br/>
                            <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_name" icon="el-icon-document-copy"></el-button>
                            <span>{{ scope.row.customer_name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="月份" prop="month" width="80"></el-table-column>
                    <el-table-column label="来源" prop="source_name" width="75"></el-table-column>
                    <el-table-column label="消耗" prop="consume_money" align="right"></el-table-column>
                    <el-table-column label="回款" prop="remit_money" align="right"></el-table-column>
                    <el-table-column label="余额" prop="remit_balance" align="right"></el-table-column>
                    <el-table-column label="开票金额" prop="invoice_money" align="right"></el-table-column>
                    <el-table-column label="发票余额" prop="invoice_balance" align="right"></el-table-column>
                </el-table>
                <div class="block" style="margin-bottom: 16px;">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="page_size"
                                   layout="total, sizes, prev, pager, next, jumper" :total="total_num"></el-pagination>
                </div>

    </div>
    <!--弹窗-->
    <!--弹窗-->
</div>

<script type="application/javascript">

    //来源
    let source_map = [
        {"name": "全部", "source": -1},
        {"name": "羽乐科技", "source": 0},
        {"name": "朴道", "source": 1},
        {"name": "浙数交", "source": 10},
    ];

    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,
            unc_loading: false,

            user_cookie:user_cookie,
            //接口地址
            urls: {
                customer              : url_prefix + '/options/getMap?customer=true&user_cookie=' + user_cookie,
                collect               : url_prefix + '/invoice/collect',//发票信息汇总页面
                uninvoice_money_count : url_prefix + '/invoice/uninvoice_money_count',//发票结余合计
            },

            default_month:'',
            //客户列表
            customer_list: [],

            //状态
            status_map: [],

            //来源
            source_map: source_map,


            //列表数据 发票数据列表 待开票+已开票数据
            table_data: [],

            //发票结余合计数据
            uninvoice_count:{},

            //分页
            total_num: 0,
            page_size: 20,
            current_page: 1,

            ok_confirm: false,

            //搜索条件
            search_form: {
                include_es_product:false,
                month:0,
                source: -1,
            },

        },
        mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);
        },
        created: function () {
            this.getOptions()

            // 获取上个月的时间范围
            let startDate = new Date();
            let previous_first_day = new Date(startDate.getFullYear(), startDate.getMonth() - 1, 1);
            previous_first_day.setHours(0, 0, 0, 0);

            let this_month_first_day = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
            this_month_first_day.setHours(0, 0, 0, 0);

            // 获取毫秒时间戳
            let startTimestamp = previous_first_day.getTime();
            let endTimestamp = this_month_first_day.getTime() - 1;

            this.search_form.month = [startTimestamp,endTimestamp];

            this.getTableData();
        },
        methods: {
            //获取驳回列表数据
            getTableData:async function () {
                let self = this;
                let customer = self.search_form.customer;
                let source = self.search_form.source;
                let month = self.search_form.month;
                let include_es_product = self.search_form.include_es_product;

                let para = {
                    user_cookie: user_cookie,
                    page_size: self.page_size,
                    page: self.current_page,
                    include_es_product:include_es_product,
                };
                if (customer) {
                    para.customer = customer;
                }

                if (source !== -1) {
                    para.source = source;
                }

                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (month && !isNaN(month[0]) && !isNaN(month[1])) {
                    let time = [];
                    time[0] = (month[0] - month[0] % 1000) / 1000;
                    time[1] = (month[1] - month[1] % 1000) / 1000;
                    para.month = time;
                }
                let table_data = await this.getData(para);

                this.table_data = table_data.list;
                this.total_num = table_data.total_num;
                this.page_size = table_data.page_size
                this.current_page = table_data.page;

                this.getUninvoiceMoneyCount();
            },
            //excel导出
            getTableDataExcel:async function () {
                let self = this;
                let customer = self.search_form.customer;
                let source = self.search_form.source;
                let month = self.search_form.month;
                let include_es_product = self.search_form.include_es_product;

                let para = {
                    user_cookie: user_cookie,
                    page_size:100000,
                    page:1,
                    include_es_product:include_es_product,
                };
                if (customer) {
                    para.customer = customer;
                }

                if (source === 0 || source === 1 || source === -1) {
                    para.source = source;
                }

                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (month && !isNaN(month[0]) && !isNaN(month[1])) {
                    let time = [];
                    time[0] = (month[0] - month[0] % 1000) / 1000;
                    time[1] = (month[1] - month[1] % 1000) / 1000;
                    para.month = time;
                }
                let table_data = await this.getData(para);

                this.loading = true;

                let sheetHeader = ["客户名称","月份","消耗","发票金额","发票结余","回款金额","客户余额","渠道"];
                let sheetData = [];
                let already_customer = [];
                for(let i = 0; i < table_data.list.length; i++){
                    let val = table_data.list[i];
                    if(val.customer_month_count === 0 ){
                        continue;
                    }
                    if(already_customer.includes(val.customer_name)){
                        continue;
                    }

                    already_customer.push(val.customer_name);

                    sheetData.push({
                        customer_name: val.customer_name,
                        month: val.month,
                        consume_money: val.consume_money,
                        invoice_money: val.invoice_money,
                        invoice_balance: val.invoice_balance,
                        remit_money: val.remit_money,
                        remit_balance: val.remit_balance,
                        source_name: val.source_name,
                    })
                }

                let option = {};
                option.fileName = '发票汇总导出_'+month[0]+"_"+month[1];
                option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
                if(sheetData.length>0){
                    (new ExportJsonExcel(option)).saveExcel();
                }
                this.loading = false;
            },
            getData: async function (para) {
                let self = this;
                self.loading = true;

                let data = [];
                await axios.post(self.urls.collect, para).then(function (response) {
                    if (response.data.status === 0) {
                        data = response.data.data;
                    }else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });

                return data;
            },
            getUninvoiceMoneyCount:function(){
                let self = this;
                self.unc_loading = true;
                let month = self.search_form.month;
                let customer = self.search_form.customer;
                let include_es_product = self.search_form.include_es_product;

                let para = {
                    user_cookie : user_cookie,
                };

                if (customer) {
                    para.customer = customer;
                }
                para.include_es_product = include_es_product;

                if (month && !isNaN(month[0]) && !isNaN(month[1])) {
                    let time = [];
                    time[0] = (month[0] - month[0]%1000)/1000;
                    time[1] = (month[1] - month[1]%1000)/1000;
                    para.month = time;
                }
                axios.post(self.urls.uninvoice_money_count, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.uninvoice_count = response.data.data;
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.unc_loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.unc_loading = false;
                });
            },
            getOptions: function () {
                let self = this;
                axios.get(self.urls.customer, { user_cookie: user_cookie }).then(function (response) {
                    if (response.data.status === 0) {
                        self.customer_list = response.data.data.customer;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
            },
            //修改每页条书
            handleSizeChange(val) {
                this.page_size = val;
                this.current_page = 1;
                this.getTableData();
            },
            //修改页数,获取数据
            handleCurrentChange(val) {
                this.current_page = val;
                this.getTableData();
            },
            //表单搜索
            searchTableData: function () {
                this.current_page = 1;
                this.getTableData();
            },
            merge_cells:function({ row, column, rowIndex, columnIndex }){
                // //注意 这里的row.count经过处理 否则需要处理所有行
                let month_merge_count_array = [1];//根据月份合并
                // let receipt_merge_count_array = [10,11];//根据收款单合并
                let invoice_merge_count_array = [5,6,7,10,11];//根据发票流水号合并
                let customer_source_count_array = [0,4,8,9,13,14,15];//根据客户渠道合并

                if(customer_source_count_array.indexOf(columnIndex) !== -1){
                    return [row.customer_source_count, 1];
                }else if(month_merge_count_array.indexOf(columnIndex) !== -1){
                    return [row.customer_month_count, 1];
                }else if(invoice_merge_count_array.indexOf(columnIndex) !== -1){
                    return [row.invoice_merge_count, 1];
                // }else if(receipt_merge_count_array.indexOf(columnIndex) !== -1){
                //     return [row.receipt_merge_count, 1];
                }
                return [1, 1];
            },
            successFunc: function(e) {
                // 清除选中状态
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc:function(e) {
                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>