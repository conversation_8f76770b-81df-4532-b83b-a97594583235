<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>

</head>

<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>

    [v-cloak] {
      display: none;
    }
    .el-table .el-table__cell{
        padding:0;
    }
</style>

<!--发票明细-->
<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-body">
                <el-form :inline="true" :model="search_form" label-width="100px" class="demo-form-inline" size="mini">
                    <el-form-item label="来源:">
                        <el-select v-model="search_form.source" filterable clearable placeholder="请选择">
                            <el-option v-for="item in source_map" :key="item.source" :label="item.name" :value="item.source"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="客户:">
                        <el-select v-model="search_form.customer" filterable clearable placeholder="请选择">
                            <el-option v-for="(item,index) in customer_list" :key="index" :label="item" :value="index"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发票流水号:">
                        <el-input v-model="search_form.invoice_id" autocomplete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="消耗月份:">
                        <el-date-picker v-model="search_form.month" type="monthrange" value-format="timestamp" align="center"
                                        unlink-panels range-separator="-" start-placeholder="开始月份" end-placeholder="结束月份"
                                        :default-time="['00:00:00', '23:59:59']"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="开票时间:">
                        <el-date-picker v-model="search_form.invoice_date" type="daterange" value-format="timestamp" align="center"
                                        unlink-panels range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                                        :default-time="['00:00:00', '23:59:59']"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="回款时间:">
                        <el-date-picker v-model="search_form.remit_date" type="daterange" value-format="timestamp" align="center"
                                        unlink-panels range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                                        :default-time="['00:00:00', '23:59:59']"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getTableData()">查询</el-button>
                        <el-button type="warning" @click="getTableDataExcel()">导出</el-button>
                    </el-form-item>
                    <!--<el-form-item>-->
                    <!--    <el-button type="success">导出</el-button>-->
                    <!--</el-form-item>-->
                </el-form>
            </div>
        </div>
    </div>
    <div class="container">
        <el-table :data="table_data" border fit :span-method="merge_cells" :header-cell-style='{"text-align":"center","padding":"12px 0"}'>
        <!--<el-table :data="table_data" border fit>-->
            <el-table-column label="客户id" width="185">
                <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_id" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.customer_id }}</span>
                </template>
            </el-table-column>
            <el-table-column label="客户" width="150">
                <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_name" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.customer_name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="客户公司" width="190" show-overflow-tooltip>
               <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_company_name" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.customer_company_name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="消耗月份" prop="month" width="80"></el-table-column>
            <el-table-column label="产品" prop="product_name"></el-table-column>
            <el-table-column label="消耗" prop="consume" width="190"></el-table-column>
            <el-table-column label="消耗合计" prop="consume_sum" width="190"></el-table-column>
            <el-table-column label="发票流水号" width="190" show-overflow-tooltip>
                <template slot-scope="scope">
                    <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.invoice_id" icon="el-icon-document-copy"></el-button>
                    <span >{{ scope.row.invoice_id }}</span>
                </template>
            </el-table-column>
            <el-table-column label="开票企业名称" prop="invoice_company" width="190" show-overflow-tooltip></el-table-column>
            <el-table-column label="发票金额" prop="invoice_money" align="right" width="140"></el-table-column>
            <el-table-column label="开票金额" prop="consume" align="right" width="140"></el-table-column>
            <el-table-column label="开票日期" prop="invoice_date" width="175"></el-table-column>
            <!--<el-table-column label="客户余额" prop="customer_balance"></el-table-column>-->
            <el-table-column label="回款单号" prop="receipt_serial"></el-table-column>
            <el-table-column label="回款金额"><template slot-scope="scope">{{ scope.row.receipt_serial != "" ?scope.row.consume:''  }}</template></el-table-column>
            <el-table-column label="回款日期" prop="remit_date"></el-table-column>
            <el-table-column label="渠道" prop="source_name"></el-table-column>
        </el-table>
        <div class="block" style="margin-bottom: 16px;">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="page_size" layout="total, sizes, prev, pager, next, jumper" :total="total_num"></el-pagination>
        </div>
    </div>
    <!--弹窗-->
    <!--弹窗-->
</div>

<script type="application/javascript">

    //来源
    let source_map = [
        {"name": "全部", "source": -1},
        {"name": "羽乐科技", "source": 0},
        {"name": "朴道", "source": 1},
        {"name": "浙数交", "source": 10},
    ];
    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,

            user_cookie:user_cookie,
            //接口地址
            urls: {
                customer            : url_prefix + '/options/getMap?customer=true&user_cookie=' + user_cookie,
                invoice_detail_list : url_prefix + '/invoice/invoice_detail_list', //后付费客户开票明细
            },
            //客户列表
            customer_list: [],

            //状态
            status_map: [],

            //来源
            source_map: source_map,

            //列表数据 发票数据列表 待开票+已开票数据
            table_data: [],

            //分页
            total_num: 0,
            page_size: 10,
            current_page: 1,

            ok_confirm: false,

            //搜索条件
            search_form: {
                source : -1,
                invoice_date:[0,0],
            },

        },
        mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);
        },
        created: function () {
            this.getOptions();

            // 获取当前日期并设置为00:00:00
            let startDate = new Date();
            startDate.setHours(0, 0, 0, 0);

            // 复制日期并设置为23:59:59
            let endDate = new Date(startDate);
            endDate.setHours(23, 59, 59, 999);

            // 获取毫秒时间戳
            let startTimestamp = startDate.getTime();
            let endTimestamp = endDate.getTime();

            this.search_form.invoice_date = [startTimestamp,endTimestamp];

            this.getTableData();
        },
        methods: {
            //获取驳回列表数据
            getTableData: async function () {
                let self = this;
                let customer = self.search_form.customer;
                let month = self.search_form.month;
                let source = self.search_form.source;
                let invoice_date = self.search_form.invoice_date;
                let remit_date = self.search_form.remit_date;
                let invoice_id = self.search_form.invoice_id;

                let para = {
                    user_cookie : user_cookie,
                    page_size:self.page_size,
                    page:self.current_page,
                };
                if (customer) {
                    para.customer = customer;
                }
                if (invoice_id) {
                    para.invoice_id = invoice_id;
                }

                // if (source === 0 || source === 1 || source === -1 || source === 10) {
                //     para.source = source;
                // }
                para.source = source;

                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (month && !isNaN(month[0]) && !isNaN(month[1])) {
                    let time = [];
                    time[0] = (month[0] - month[0]%1000)/1000;
                    time[1] = (month[1] - month[1]%1000)/1000;
                    para.month = time;
                }
                if (invoice_date && !isNaN(invoice_date[0]) && !isNaN(invoice_date[1])) {
                    let time = [];
                    time[0] = (invoice_date[0] - invoice_date[0]%1000)/1000;
                    time[1] = (invoice_date[1] - invoice_date[1]%1000)/1000;
                    para.invoice_date = time;
                }
                if (remit_date && !isNaN(remit_date[0]) && !isNaN(remit_date[1])) {
                    let time = [];
                    time[0] = (remit_date[0] - remit_date[0]%1000)/1000;
                    time[1] = (remit_date[1] - remit_date[1]%1000)/1000;
                    para.remit_date = time;
                }

                let response = await this.getData(para);

                this.table_data = response.list;
                this.total_num = response.total_num;
                this.page_size = response.page_size
                this.current_page = response.page;
            },
            getData: async function (para) {
                let self = this;
                self.loading = true;
                let table_data = [];

                await axios.post(self.urls.invoice_detail_list, para).then(function (response) {
                    if (response.data.status === 0) {
                        table_data = response.data.data;
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
                return table_data;
            },
            getTableDataExcel: async function () {
                let self = this;
                let customer = self.search_form.customer;
                let month = self.search_form.month;
                let source = self.search_form.source;
                let invoice_date = self.search_form.invoice_date;
                let remit_date = self.search_form.remit_date;
                let invoice_id = self.search_form.invoice_id;

                let para = {
                    user_cookie: user_cookie,
                    page_size:100000,
                    page:1,
                };

                if (customer) {
                    para.customer = customer;
                }
                if (invoice_id) {
                    para.invoice_id = invoice_id;
                }

                if (source === 0 || source === 1 || source === -1) {
                    para.source = source;
                }

                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (month && !isNaN(month[0]) && !isNaN(month[1])) {
                    let time = [];
                    time[0] = (month[0] - month[0]%1000)/1000;
                    time[1] = (month[1] - month[1]%1000)/1000;
                    para.month = time;
                }
                if (invoice_date && !isNaN(invoice_date[0]) && !isNaN(invoice_date[1])) {
                    let time = [];
                    time[0] = (invoice_date[0] - invoice_date[0]%1000)/1000;
                    time[1] = (invoice_date[1] - invoice_date[1]%1000)/1000;
                    para.invoice_date = time;
                }
                if (remit_date && !isNaN(remit_date[0]) && !isNaN(remit_date[1])) {
                    let time = [];
                    time[0] = (remit_date[0] - remit_date[0]%1000)/1000;
                    time[1] = (remit_date[1] - remit_date[1]%1000)/1000;
                    para.remit_date = time;
                }

                let table_data = await this.getData(para);
                this.loading = true;

                let sheetHeader = ["客户ID","客户名称","客户公司","消耗月份","产品","消耗","发票流水号","开票金额","开票日期","开票公司名称","发票代码","发票号码","回款单号","回款金额","回款日期","渠道"];
                let sheetData = [];
                $.each(table_data.list, function (key, val){
                    sheetData.push({
                        customer_id : val.customer_id,
                        customer_name : val.customer_name,
                        customer_company_name : val.customer_company_name,
                        month : val.month,
                        product_name : val.product_name,
                        consume : val.consume,
                        invoice_id : val.invoice_id,
                        invoice_money : val.invoice_money,
                        // invoice_consume : val.invoice_consume,
                        invoice_date : val.invoice_date,
                        invoice_company : val.invoice_company,
                        invoice_code : val.invoice_code,
                        invoice_no : val.invoice_no,
                        receipt_serial : val.receipt_serial,
                        remit_money : val.receipt_serial !== '' ? val.consume : '',
                        remit_date : val.remit_date,
                        source_name : val.source_name,
                    })
                });

                let option = {};
                let currentTime = new Date().toLocaleString().replace(/[^\d]/g, '');
                option.fileName = '发票明细导出_'+currentTime;
                option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
                if(sheetData.length>0){
                    (new ExportJsonExcel(option)).saveExcel();
                }
                this.loading = false;
            },
            getOptions: function () {
                let self = this;
                axios.get(self.urls.customer, { user_cookie: user_cookie }).then(function (response) {
                    if (response.data.status === 0) {
                        self.customer_list = response.data.data.customer;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
            },
            //修改每页条书
            handleSizeChange(val) {
                this.page_size = val;
                this.current_page = 1;
                this.getTableData();
            },
            //修改页数,获取数据
            handleCurrentChange(val) {
                this.current_page = val;
                this.getTableData();
            },
            //表单搜索
            searchTableData: function () {
                this.current_page = 1;
                this.getTableData();
            },
            merge_cells:function({ row, column, rowIndex, columnIndex }){
                // //注意 这里的row.count经过处理 否则需要处理所有行
                //合并4 5 6列
                if(columnIndex === 0 || columnIndex === 1 ){
                    return [row.customer_merge_count, 1];
                }else if(columnIndex === 2 || columnIndex === 6){
                    return [row.month_merge_count, 1];
                }

                return [1, 1];
            },
            successFunc: function(e) {
                // 清除选中状态
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc:function(e) {
                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>