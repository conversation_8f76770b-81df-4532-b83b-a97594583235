<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">

    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
    <script type="application/javascript" src="__JS__big.js"></script>
    <script type="application/javascript" src="__JS__nzh.min.js"></script>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>

    [v-cloak] {
      display: none;
    }

    .el-dialog__body .el-form .el-input__inner {
        width: 256px;
    }

    .el-date-editor .el-range-separator {
        width: 6%;
    }

    .apply-content-p {
        display: inline-block;
        overflow: hidden;
        height: 28px;
        text-overflow: ellipsis;
        cursor: pointer;
        white-space: nowrap;
        margin-bottom: 0;
        width: 100%;
    }

    .show-apply-content {
        white-space: pre-line;
    }


    /*.invoice-operation {*/
    /*    padding-left: 100px;*/
    /*}*/

    .textcenter {
        text-align: center;
    }
    .biggertext {
        font-size:1.2em;
        font-weight: 900;
    }

    .upload-excel {
        display: inline-block;
        margin-left: 10px;
    }
    .el-upload__input {
        display:none !important;
    }


    .info_header{
        font-weight: bold;
        font-size: 1.1em;
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.5s;
    }
    .fade-enter,
    .fade-leave-to {
        opacity: 0;
    }

    .el-dialog__body .el-form .el-input__inner{
        width: 100%;
    }
    .copy_btn_icon{
        padding-top: 0;
        padding-bottom: 0;
    }

    .el-table .el-table__cell{
        padding:0;
    }

    .table_fold_placeholder{
        display:inline-block;
        width:23px;
    }

    .el-tabs--border-card > .el-tabs__content {
        padding: 0px;
        margin:-1px;
    }

    .el-dialog__body .el-row{
        margin-top: 10px;
        margin-bottom: 10px;
    }

    /*.el-select-dropdown__item:has(> .information_option_even) {*/
    /*    background-color: #f2ffe9;*/
    /* }*/
    .information_option_line {
        height: 90px;
    }
    .information_option_line:not(:last-child) {
        height: 90px;
        border-bottom-width: 1px;
        margin-bottom: 9px;
        border-bottom-style: dashed;
    }
    .information_option_even > .el-row {
        line-height: 20px;
    }
    .information_option_odd > .el-row {
        line-height: 20px;
    }

</style>

<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-body">
                <el-form :inline="true" :model="search_form" label-width="100px" class="demo-form-inline" size="mini">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="来源:">
                                <el-select v-model="search_form.source" filterable clearable placeholder="请选择" @change="getOptions()">
                                    <el-option v-for="item in source_map" :key="item.source" :label="item.name" :value="item.source"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="付费类型:" v-show="tab_name == 'applyed'">
                                <el-select v-model="search_form.payment_type" filterable placeholder="请选择" @change="getOptions()">
                                    <el-option v-for="item in payment_type_map" :key="item.type" :label="item.name" :value="item.type"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="客户:">
                                <el-select v-model="search_form.customer" filterable clearable placeholder="请选择" @change="getOptions()">
                                    <el-option v-for="(item,index) in customer_list" :key="index" :label="item" :value="index"></el-option>
                                </el-select>
                            </el-form-item>
                            <!--<br />-->
                            <!--<el-form-item label="产品:">-->
                            <!--    <el-select v-model="search_form.product" filterable clearable placeholder="请选择"  @change="getOptions()">-->
                            <!--        <el-option v-for="(item,index) in product_list" :key="index" :label="item" :value="index"></el-option>-->
                            <!--    </el-select>-->
                            <!--</el-form-item>-->
                            <el-form-item label="发票流水号:" v-show="tab_name == 'applyed'"><!-- 选择未申请标签页,不能进行状态选择 -->
                                <el-input v-model="search_form.invoice_id" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="开票公司:" v-show="tab_name == 'applyed'"><!-- 选择未申请标签页,不能进行状态选择 -->
                                <el-select v-model="search_form.invoice_company" filterable placeholder="请选择" clearable style="width:400px">
                                    <el-option v-for="(item,index) in invoice_company_list" :key="index" :label="item" :value="item"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="状态:" v-show="tab_name == 'applyed'"><!-- 选择未申请标签页,不能进行状态选择 -->
                                <template v-if="is_sale === 'no'">
                                    <!--财务需要单选-->
                                    <el-select v-model="search_form.status" filterable placeholder="请选择" clearable @change="getOptions()" style="width:400px">
                                        <el-option v-for="(item,index) in status_map" :key="index" :label="item" :value="index"></el-option>
                                    </el-select>
                                </template>
                                <template v-else>
                                    <!--商务给予多选-->
                                    <el-select v-model="search_form.status" filterable placeholder="请选择" multiple clearable @change="getOptions()" style="width:400px">
                                        <el-option v-for="(item,index) in status_map" :key="index" :label="item" :value="index"></el-option>
                                    </el-select>
                                </template>
                            </el-form-item>
                            <el-form-item label="申请时间:">
                                <el-date-picker v-model="search_form.create_at" type="monthrange" value-format="timestamp" align="center" unlink-panels range-separator="至" start-placeholder="开始日期"
                                                end-placeholder="结束日期" :picker-options="picker_options" :default-time="['00:00:00', '23:59:59']" @change="getOptions()"></el-date-picker>
                            </el-form-item>

                            <!--<el-form-item label="是否邮寄:" v-show="tab_name == 'applyed'">-->
                            <!--    <el-select v-model="search_form.send_status" filterable clearable placeholder="请选择" @change="getOptions()">-->
                            <!--        <el-option v-for="item in send_status_map" :key="item.type" :label="item.name" :value="item.type"></el-option>-->
                            <!--    </el-select>-->
                            <!--</el-form-item>-->

                            <!--<el-form-item label="快递单号:" v-show="tab_name == 'applyed'">&lt;!&ndash; 选择未申请标签页,不能进行状态选择 &ndash;&gt;-->
                            <!--    <el-input v-model="search_form.express_no" clearable></el-input>-->
                            <!--</el-form-item>-->
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" :offset="1">
                            <el-form-item>
                                <el-button type="primary" @click="searchTableData()">查询</el-button>
                                <el-button type="success" size="mini" @click="getTableDataExcel()">导出</el-button>
                            </el-form-item>
                            <el-form-item>
                                <div class="invoice-operation">
                                    <template v-if="is_sale === 'no'">
                                        <el-button type="success" size="mini" @click="getInfoTableDataExcel()">消耗开票明细导出</el-button>

                                        <el-button v-if="show_pass_button" type="warning" @click="batchPass()" size="mini">审批</el-button>
                                        <!--<el-button type="warning">回款</el-button>-->
                                        <el-upload v-if="show_issue_button"
                                          class="upload-excel"
                                          :action="urls.issue_by_excel"
                                          :data="{'user_cookie':user_cookie}"
                                          :on-success="getTableData"
                                          :limit="1"
                                          :file-list="fileList" size="mini">
                                            <el-button type="primary" size="mini">开票</el-button>
                                        </el-upload>
                                    </template>
                                </div>
                            </el-form-item>
                            <el-form-item>
                                <template v-if="is_sale === 'yes'">
                                    <el-button type="primary" @click="applyInvoiceDialog()" size="mini">申请开票</el-button>
                                    <el-button @click="qiFuInvoice()" size="mini">企服开票</el-button>
                                </template>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
    </div>
    <div class="container">
        <template>
            <el-tabs v-model="tab_name" @tab-click="tab_click" type="border-card">
                <el-tab-pane label="未申请消耗" name="unapply"></el-tab-pane>
                <el-tab-pane label="未申请回款" name="unapply_receipt"></el-tab-pane>
                <el-tab-pane label="已申请发票" name="applyed"></el-tab-pane>

                <template v-if="tab_name == 'unapply'">
                    <!--未申请消耗-->
                    <el-table :data="table_unapply_consume" border fit key="ref_table_unapply_consume" :header-cell-style='{"text-align":"center","padding":"12px 0"}'>
                        <!--<el-table-column label="序号" type="index"></el-table-column>-->
                        <!--<el-table-column label="id" prop="id"></el-table-column>-->
                        <el-table-column label="客户" prop="customer_name">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_name" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.customer_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="客户id">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_id" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.customer_id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="产品" prop="product_name">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.product_name" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.product_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="月份">
                            <template slot-scope="scope">
                                <span >{{ scope.row.consume_month }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="金额" prop="consume_money"></el-table-column>
                        <el-table-column label="未开票金额" prop="consume_balance"></el-table-column>
                        <el-table-column label="来源" prop="source_name"></el-table-column>
                    </el-table>
                </template>
                <template v-else-if="tab_name == 'unapply_receipt'">
                    <!--未申请回款-->
                    <el-table :data="table_data_receipt" border fit key="ref_table_data_receipt" :header-cell-style='{"text-align":"center","padding":"12px 0"}'>
                        <el-table-column label="客户" prop="customer_name">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_name" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.customer_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="客户id">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_id" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.customer_id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="回款单号" width="300">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.receipt_serial" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.receipt_serial }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="打款公司" prop="name"  width="300"></el-table-column>
                        <el-table-column label="打款银行" prop="bank"></el-table-column>
                        <el-table-column label="金额" prop="money"></el-table-column>
                        <el-table-column label="回款日期" prop="remit_date_fmt"></el-table-column>
                        <el-table-column label="是否是企服回款">
                            <template slot-scope="scope">
                                <span >{{ scope.row.is_qf_receipt_serial?'是':'否' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="未开票金额" prop="remit_unconsumed"></el-table-column>
                        <el-table-column label="来源" prop="source_name"></el-table-column>
                    </el-table>
                </template>
                <template v-else-if="tab_name == 'applyed'">
                    <!--已申请-->
                    <el-table :data="table_data" border fit @selection-change="invoiceChange" highlight-selection-row :header-cell-style='{"text-align":"center","padding":"12px 0"}' row-key="row_key" key="ref_table_data">
                        <el-table-column type="selection" v-if="show_check_box"></el-table-column>
                        <el-table-column  fixed="left"  label="发票流水号" v-if="show_invoice_id_column" width="215" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span v-if="!scope.row.has_child" class="table_fold_placeholder"></span>
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.invoice_id" v-if="scope.row.invoice_id != ''" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.invoice_id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="客户id" width="190">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_id" v-if="scope.row.customer_id != ''" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.customer_id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="客户" width="150">
                            <template slot-scope="scope">
                                <el-button type="text" class="copy_btn" :data-clipboard-text="scope.row.customer_name" v-if="scope.row.customer_name != ''" icon="el-icon-document-copy"></el-button>
                                <span >{{ scope.row.customer_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="产品" prop="product_name" width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="开票模式" width="160">
                            <template slot-scope="scope" >
                                <el-tag v-if="scope.row.invoice_model == 5 || scope.row.invoice_model == 6"   type="warning">{{ scope.row.invoice_model_name }}</el-tag>
                                <el-tag v-else>{{ scope.row.invoice_model_name }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="商务" prop="salesman_name" width="80"></el-table-column>
                        <el-table-column label="消耗月份" prop="month" width="80" show-overflow-tooltip></el-table-column>
                        <el-table-column label="消耗金额" prop="consume" align="right" width="140"></el-table-column>
                        <el-table-column label="开票金额" prop="money" align="right" width="120"></el-table-column>
                        <!--<el-table-column v-if="show_invoice_id_column" label="购方企业名称" prop="company_name"></el-table-column>-->
                        <el-table-column label="开票企业名称" prop="invoice_company" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column label="申请时间" width="100">
                            <template slot-scope="scope" >
                                <el-tooltip class="item" effect="dark" :content="scope.row.apply_date" placement="top">
                                    <span>{{ scope.row.apply_date_short }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="开票日期" width="100">
                            <template slot-scope="scope" >
                                <el-tooltip class="item" effect="dark" :content="scope.row.invoice_date" placement="top">
                                    <span>{{ scope.row.invoice_date_short }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="发票号" prop="invoice_no" show-overflow-tooltip></el-table-column>
                        <el-table-column label="回款单号" prop="receipt_serial" width="150" show-overflow-tooltip></el-table-column>
                        <el-table-column label="到款日期" width="100">
                            <template slot-scope="scope" >
                                <el-tooltip class="item" effect="dark" :content="scope.row.remit_day" placement="top">
                                    <span>{{ scope.row.remit_day_short }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="到款金额" prop="remit_money" width="120"></el-table-column>
                        <!--<el-table-column label="快递单号" prop="express_no" width="220">-->
                        <!--    <template slot-scope="scope">-->
                        <!--        &lt;!&ndash;<span v-if="!scope.row.has_child" class="table_fold_placeholder"></span>&ndash;&gt;-->
                        <!--        <el-button v-if="!scope.row.is_child && scope.row.express_no" type="text" class="copy_btn" :data-clipboard-text="scope.row.express_no" v-if="scope.row.express_no != ''" icon="el-icon-document-copy"></el-button>-->
                        <!--        <span >{{ scope.row.express_no }}</span>-->
                        <!--    </template>-->
                        <!--</el-table-column>-->
                        <!--<el-table-column label="到款渠道" prop="___________"></el-table-column>-->
                        <el-table-column label="来源" prop="source" width="80">
                            <template slot-scope="scope" v-if="!scope.row.is_child"><span >{{ scope.row.source }}</span></template>
                        </el-table-column>
                        <el-table-column fixed="right" label="状态" align="center" width="80">
                            <template slot-scope="scope" v-if="!scope.row.is_child">
                                <el-tag :type="tag_type_map[scope.row.status]" size="small">{{ scope.row.status_text }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" fit label="操作" width="260px">
                            <template slot-scope="scope" v-if="!scope.row.is_child">
                                <template v-if="is_sale === 'yes'" v-for="(info,action) in salesman_buttons[scope.row.status]">
                                    <el-button size="mini" :type="info.type" @click="updateStatus(scope.row,action)">{{info.name}}</el-button>
                                </template>
                                <template v-if="is_sale === 'no'" v-for="(info,action) in finance_buttons[scope.row.status]">
                                    <template v-if="action === 'copy'">
                                        <el-button class="copy_btn" size="mini" :type="info.type" :data-clipboard-text="scope.row.copy_text" plain>{{info.name}}</el-button>
                                    </template>
                                    <template v-else-if="action === 'express'">
                                        <template v-if="scope.row.send_status == 0">
                                            <el-button size="mini" :type="info.add.type" @click="updateStatus(scope.row,action)">{{info.add.name}}</el-button>
                                        </template>
                                        <template v-else>
                                            <el-button size="mini" :type="info.edit.type" @click="updateStatus(scope.row,action)">{{info.edit.name}}</el-button>
                                        </template>
                                    </template>
                                    <template v-else>
                                        <el-button size="mini" :type="info.type" @click="updateStatus(scope.row,action)">{{info.name}}</el-button>
                                    </template>
                                </template>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <div class="block" style="margin-bottom: 16px;">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="page_size" layout="total, sizes, prev, pager, next, jumper" :total="total_num"></el-pagination>
                </div>
            </el-tabs>
        </template>
    </div>


    <!--弹窗-->
    <!--申请开票-->
    <el-dialog width="95%" :title="didd.dialog_title" :visible="didd.apply_invoice_visible" v-loading="didd_loading" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" >
        <el-row :gutter="20">
            <el-col :span="20" :offset="2">
                <!--居中-->
                <el-tabs v-model="didd.current_tab" @tab-click="switchTab">
                    <el-tab-pane label="后付费" name="alr">
                        <el-form :inline="true" :model="didd.s_form" :inline="true" :rules="didd.rules" ref="didd_dialog_form" size="mini">
                            <el-form-item :label-width="formLabelWidth">
                                <el-form-item label="来源:">
                                    <el-select v-model="didd.s_form.source" filterable clearable placeholder="请选择" @change="diddGetSource" autocomplete="off" style="width:100px">
                                        <el-option v-for="item in didd.source_map" :key="item.source" :label="item.name" :value="item.source"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="客户:"  prop="customer" v-if="didd.apply_data.source == 0">
                                    <el-select v-model="didd.s_form.customer" filterable clearable placeholder="请选择" @change="diddUpdateSelectedCustomerInvoiceModel">
                                        <el-option v-for="(item,index) in didd.customer_list" :key="item.customer_id" :label="item.name" :value="item.customer_id"></el-option>
                                    </el-select>
                                    <!--{{didd.s_form.invoice_model}}-->
                                </el-form-item>
                                <el-form-item label="产品:" v-if="didd.apply_data.source == 0">
                                    <el-select v-model="didd.s_form.product" filterable clearable placeholder="请选择">
                                        <el-option v-for="(item,index) in didd.product_list" :key="index" :label="item" :value="index"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="月份:">
                                    <el-date-picker v-model="didd.s_form.create_at" type="monthrange" value-format="timestamp" align="center" unlink-panels range-separator="-" start-placeholder="开始月份"
                                                    end-placeholder="结束月份" :picker-options="picker_options" :default-time="['00:00:00', '00:00:00']" style="width:250px"></el-date-picker>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="diddSearch('didd_dialog_form')" :disabled="ok_confirm" size="mini">查询未开票数据</el-button>
                                    <!--<el-button type="warning" @click="import_source_data_dialog" v-if="didd.apply_data.source > 0 && isd.show_button" size="mini">导入数据</el-button>-->
                                    <!-- auto_check -->
                                    <el-upload v-if="didd.apply_data.source > 0 && isd.show_button" size="mini"
                                               style="display:inline-block"
                                               type="warning"
                                               :show-file-list="false"
                                               class="auto_check"
                                               :action="urls.auto_check"
                                               :data="acd"
                                               :before-upload="before_auto_check_upload"
                                               :on-success="auto_check_upload_success"
                                               :show-file-list="true"
                                               :file-list="fileList">
                                        <el-button size="small" type="primary">上传对账单,匹配消耗</el-button>
                                        <!--<div slot="tip" class="el-upload__tip">只能上传excel文件</div>-->
                                    </el-upload>
                                </el-form-item>
                            </el-form-item>
                        </el-form>
                        <template>
                            <el-table
                                    style="width:100%; margin:auto;"
                                    stripe
                                    it
                                    :data="didd.for_apply_items"
                                    ref="alr_table"
                                    @row-click="alrRowClick"
                                    @selection-change="alrChange"
                                    max-height="650"
                                    :default-sort = "{prop: 'cost_money', order: 'descending'}"
                            >
                                <el-table-column type="selection"></el-table-column>
                                <el-table-column label="客户" prop="customer_name" width="200" :filters="didd.filters_customer" :filter-method="filterCustomerHandler"></el-table-column>
                                <el-table-column label="产品" prop="product_name" :filters="didd.filters_product" :filter-method="filterProductHandler"></el-table-column>
                                <el-table-column label="月份" prop="month_date" width="120" sortable></el-table-column>
                                <el-table-column label="商务" prop="salesman_name" width="80"></el-table-column>
                                <!--<el-table-column label="edit_status" prop="edit_status"></el-table-column>-->
                                <el-table-column label="金额">
                                    <template slot-scope="scope">
                                        <!-- 后付费客户可修改金额 -->
                                        <template v-if="didd.customer_list[scope.row.customer_id]['invoice_model'] == 2 || didd.customer_list[scope.row.customer_id]['invoice_model'] == 4">
                                            <!--{{ scope.row.edit_status }}-->
                                            <template v-if="!scope.row.edit_status">
                                                    <el-col :span="12">
                                                        <span>{{ scope.row.cost_money }}</span>
                                                    </el-col>
                                                    <el-col :span="12">
                                                        <el-button type="primary" icon="el-icon-edit" size="mini" @click.stop="alrShowEditMoney(scope.row)"></el-button>
                                                    </el-col>
                                            </template>
                                            <template v-else>
                                                <el-col :span="12">
                                                    <el-input v-model="scope.row.cost_money" size="mini" placeholder="修改开票金额" @click.native.stop @keyup.native.enter="alrEditMoney(scope.row)" style="margin-left: -16px;  font-size: 14px;}"></el-input>
                                                </el-col>
                                                <el-col :span="12">
                                                    <el-button type="success" icon="el-icon-check" size="mini" @click.stop="alrEditMoney(scope.row)"></el-button>
                                                </el-col>
                                            </template>
                                        </template>
                                        <template v-else>
                                            <!-- 保持格式统一 与可修改金额方式开票对齐-->
                                            <el-col :span="12">
                                                <span>{{ scope.row.cost_money }}</span>
                                            </el-col>
                                            <el-col :span="12"></el-col>
                                        </template>
                                    </template>
                                </el-table-column>
                                <el-table-column label="">
                                    <template slot-scope="scope">
                                        <template v-if="didd.customer_list[scope.row.customer_id]['invoice_model'] == 2 || didd.customer_list[scope.row.customer_id]['invoice_model'] == 4">
                                            <span v-if="scope.row.origin_cost_money != scope.row.cost_money">修改前:{{ scope.row.origin_cost_money }}</span>
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!--<div class="block" style="margin-bottom: 16px;">-->
                            <!--    <el-pagination small hide-on-single-page @size-change="handleSizeChangeDidd" @current-change="handleCurrentChangeDidd" :current-page="didd.current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="didd.page_size" layout="total, prev, pager, next" :total="didd.total_num"></el-pagination>-->
                            <!--</div>-->
                        </template>
                    </el-tab-pane>

                    <el-tab-pane label="预付费" name="pre">
                    </el-tab-pane>
                        <!--申请开票弹窗表单-->

                        <el-form ref="form" :model="didd.apply_data" label-width="120px" label-position="right" style="width: 50%;" size="mini">

                            <template v-if="didd.current_tab == 'alr'">
                                <el-form-item label="共选择">
                                    <span class="biggertext">{{this.didd.selection_data.count}}</span> 条
                                </el-form-item>
                                <el-form-item label="金额">
                                    <span class="biggertext">{{this.didd.selection_data.sum}}</span>
                                </el-form-item>
                                <el-form-item label="大写">
                                    <span class="zh_big">{{sum_zh_big}}</span>
                                </el-form-item>
                            </template>

                            <template v-if="didd.current_tab == 'pre'">
                                <el-form-item label="来源:">
                                    <el-select v-model="didd.apply_data.source" filterable clearable placeholder="请选择" @change="get_pre_customers" >
                                        <el-option v-for="item in didd.source_map" :key="item.source" :label="item.name" :value="item.source"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="开票模式">
                                    <el-radio-group v-model="didd.apply_data.invoice_model" @input="get_pre_customers">
                                        <!--渠道预付费都是先票后款-->
                                        <el-radio-button label="5" :disabled="didd.apply_data.source > 0">按收款单开票</el-radio-button>
                                        <el-radio-button label="6">先票后款</el-radio-button>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="客户">
                                    <template v-if="didd.apply_data.source > 0">
                                        <!--渠道客户-->
                                        <el-select v-model="didd.apply_data.customer_ids" filterable clearable placeholder="请选择" @change="changePreCustomers" multiple>
                                            <el-option v-for="(item,index) in didd.customer_list" :key="item.customer_id" :label="item.name" :value="item.customer_id"></el-option>
                                        </el-select>
                                    </template>
                                    <template v-else>
                                        <!--直客-->
                                        <el-select v-model="didd.apply_data.customer_id" filterable clearable placeholder="请选择" @change="changePreCustomers">
                                            <el-option v-for="(item,index) in didd.customer_list" :key="item.customer_id" :label="item.name" :value="item.customer_id"></el-option>
                                        </el-select>
                                    </template>
                                </el-form-item>
                                <el-form-item label="合同:" v-if="didd.apply_data.source == 0 && didd.apply_data.invoice_model == 6">
                                    <!--先票后款 先择合同-->

                                    <el-select v-model="didd.apply_data.contract_no" filterable clearable placeholder="请选择">
                                        <el-option v-for="item in didd.contract_list" :key="item.contract_no" :label="item.contract_no" :value="item.contract_no"></el-option>
                                    </el-select>

                                    <el-button @click="view_pdf()" size="mini">合同预览</el-button>

                                    <!--{{didd.apply_data.contract_no}}-->
                                </el-form-item>
                                <!--羽乐科技-按收款开票-->
                                <el-form-item label="收款单" v-if="didd.apply_data.source == 0 && didd.apply_data.invoice_model == 5">
                                    <!--选择 预付费 后 在这里展示收款单列表-->
                                    <el-table
                                            style="width:100%; margin:auto;"
                                            highlight-current-row
                                            it
                                            :data="didd.receipt_serial_list"
                                            ref="pre_table"
                                            @current-change="preChange"
                                            max-height="650"
                                            :default-sort = "{prop: 'remit_date_fmt', order: 'descending'}"
                                     >
                                        <el-table-column label="收款单号" prop="receipt_serial"></el-table-column>
                                        <el-table-column label="金额" prop="money" sortable></el-table-column>
                                        <el-table-column label="交易日期" prop="remit_date_fmt"></el-table-column>
                                        <el-table-column label="公司名" prop="name" sortable></el-table-column>
                                    </el-table>
                                </el-form-item>
                                <!--选择收款单后的总金额-->
                                <el-form-item label="金额" v-if="didd.apply_data.source == 0 && didd.apply_data.invoice_model == 5">
                                    <span class="biggertext">{{this.didd.apply_data.money}}</span>
                                </el-form-item>
                                <!--羽乐科技-按收款开票-->


                                <!--羽乐科技-先票后款-->
                                <el-form-item label="金额" v-if="didd.apply_data.source == 0 && didd.apply_data.invoice_model == 6">
                                    <el-input v-model="didd.apply_data.money" placeholder="开票金额" style="width:225px" v-if="didd.apply_data.invoice_model == 6"></el-input>
                                </el-form-item>
                                <!--羽乐科技-先票后款-->


                                <!--渠道-先票后款-->
                                <el-form-item label="金额" v-if="didd.apply_data.source > 0 && didd.apply_data.invoice_model == 6">
                                    <!--渠道-先票后款-->
                                    <!--{{didd.apply_data.customer_id}}-->
                                    <!--{{didd.apply_data.preset_receipts}}-->
                                    <el-table
                                        style="width:100%; margin:auto;"
                                        :data="didd.apply_data.preset_receipts"
                                        ref="channel_customer_money"
                                        max-height="650"
                                     >
                                        <el-table-column label="客户" prop="customer_name"></el-table-column>
                                        <el-table-column label="金额" >
                                            <template slot-scope="scope">
                                                <el-input placeholder="金额" style="width:225px" v-model="scope.row.money" @input="sum_money"></el-input>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-form-item>
                                <el-form-item label="总金额" v-if="didd.apply_data.source > 0 && didd.apply_data.invoice_model == 6">
                                    <span class="biggertext">{{this.didd.apply_data.money}}</span>
                                </el-form-item>
                                <!--渠道-先票后款-->


                                <el-form-item label="大写">
                                    <span class="zh_big">{{money_zh_big}}</span>
                                </el-form-item>
                            </template>


                            <el-form-item label="选择开票信息" v-if="didd.informations.length > 0">
                                <el-select placeholder="选择开票信息" v-model="didd.informations_idx" @change="change_informations(didd.informations_idx)" style="width:510px">
                                    <el-option v-for="(item,index) in didd.informations"
                                               :value="index"
                                               :label="(index + 1) +'. '+ (item.invoice_company?item.invoice_company:'')+', '+ (item.tax_number?item.tax_number:'')+', '+(item.email?item.email:'')"
                                               class="information_option_line">

                                        <span :class="'information_option_'+(index % 2 == 0 ? 'even' : 'odd')">
                                            <el-row :gutter="2">
                                                <el-col :span="2">{{index + 1}}</el-col>
                                                <el-col :span="6"><span>公司</span></el-col>
                                                <el-col :span="16"><span>{{item.invoice_company}}</span></el-col>
                                            </el-row>
                                            <el-row :gutter="2">
                                                <el-col :span="6" :offset="2"><span>税号</span></el-col>
                                                <el-col :span="16"><span>{{item.tax_number}}</span></el-col>
                                            </el-row>
                                            <el-row :gutter="2">
                                                <el-col :span="6" :offset="2"><span>邮箱</span></el-col>
                                                <el-col :span="16"><span>{{item.email}}</span></el-col>
                                            </el-row>
                                            <el-row :gutter="2">
                                                <el-col :span="6" :offset="2"><span>开户行</span></el-col>
                                                <el-col :span="16"><span>{{item.bank}}</span></el-col>
                                            </el-row>
                                        </span>
                                    </el-option>
                                </el-select>
                                <el-button type="warning" plain @click="clear_informations_with_confirm">清空开票信息</el-button>
                            </el-form-item>

                            <!--<el-form-item label="是否需要盖章">-->
                            <!--    <el-radio-group v-model="didd.apply_data.need_bill">-->
                            <!--      <el-radio-button label="yes">需要</el-radio-button>-->
                            <!--      <el-radio-button label="no">不需要</el-radio-button>-->
                            <!--    </el-radio-group>-->
                            <!--</el-form-item>-->
                            <el-form-item label="发票类型" required>
                                <el-radio-group v-model="didd.apply_data.invoice_type">
                                  <!--<el-radio-button label="normal">普票</el-radio-button>-->
                                  <!--<el-radio-button label="special">专票</el-radio-button>-->
                                  <el-radio-button label="e-normal">数电票（普通发票）</el-radio-button>
                                  <el-radio-button label="e-special">数电票（增值税专用发票）</el-radio-button>
                                </el-radio-group>
                            </el-form-item>


                            <el-form-item label="开票企业名称" required>
                                <el-input v-model="didd.apply_data.invoice_company" placeholder="开票企业名称"></el-input>
                            </el-form-item>
                            <!--<el-form-item label="购方企业名称">-->
                            <!--    <el-input v-model="didd.apply_data.company_name" placeholder="购方企业名称"></el-input>-->
                            <!--</el-form-item>-->
                            <el-form-item label="购方税号" required>
                                <el-input v-model="didd.apply_data.tax_number" placeholder="购方税号"></el-input>
                            </el-form-item>
                            <el-form-item label="开户银行">
                                <el-input v-model="didd.apply_data.bank" placeholder="开户银行"></el-input>
                            </el-form-item>
                            <el-form-item label="银行账号">
                                <el-input v-model="didd.apply_data.bank_account" placeholder="银行账号"></el-input>
                            </el-form-item>
                            <el-form-item label="地址">
                                <el-input v-model="didd.apply_data.address" placeholder="地址"></el-input>
                            </el-form-item>
                            <el-form-item label="电话">
                                <el-input v-model="didd.apply_data.phone" placeholder="电话"></el-input>
                            </el-form-item>
                            <el-form-item label="邮箱地址" required>
                                <el-input v-model="didd.apply_data.email" placeholder="电子发票收件人邮箱"></el-input>
                            </el-form-item>
                            <el-form-item label="邮寄收件人">
                                <el-input v-model="didd.apply_data.post_receiver" placeholder="邮寄收件人"></el-input>
                            </el-form-item>
                            <el-form-item label="收件人电话">
                                <el-input v-model="didd.apply_data.post_phone" placeholder="收件人电话"></el-input>
                            </el-form-item>
                            <el-form-item label="邮寄地址">
                                <el-input v-model="didd.apply_data.post_address" placeholder="邮寄地址"></el-input>
                            </el-form-item>

                            <el-form-item label="开票内容" required>
                                <el-radio-group v-model="didd.apply_data.invoice_content" style="width:900px">
                                  <el-radio-button label="信息技术服务*技术服务费">信息技术服务*技术服务费</el-radio-button>
                                  <el-radio-button label="信息技术服务*信息服务费">信息技术服务*信息服务费</el-radio-button>
                                  <el-radio-button label="研发和技术服务*技术服务费">研发和技术服务*技术服务费</el-radio-button>
                                </el-radio-group>
                            </el-form-item>

                            <el-form-item label="发票备注">
                                <!--写在发票上的备注信息-->
                                <el-input type="textarea" :rows="2" placeholder="请填写需放在发票备注栏的信息，其他无关信息请勿描述。" v-model="didd.apply_data.invoice_remark" maxlength="200" show-word-limit></el-input>
                            </el-form-item>

                            <el-form-item label="业务备注" :required="didd.apply_data.source == 0 && didd.apply_data.invoice_model == 6">
                                <el-input type="textarea" :rows="2" placeholder="如需邮寄对账单,请备注盖章类型!" v-model="didd.apply_data.remark" maxlength="200" show-word-limit></el-input>
                            </el-form-item>
                        </el-form>
                </el-tabs>
            </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancle_apply_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="applyInvoice()" :disabled="ok_confirm" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--申请开票-->




    <!--发票详情弹窗-->
    <el-dialog width="50%" title="发票详情" :visible="invoice_info_visible" v-loading="info_loading" :close-on-click-modal="true" :close-on-press-escape="true" :show-close="false">
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">发票流水号</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.invoice_id" icon="el-icon-document-copy"></el-button>
                {{invoice_info.invoice_id}}
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">商务</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.salesman_name}}</el-col>
        </el-row>
        <!--<el-row :gutter="20">-->
        <!--    <el-col :span="6" :offset="2"><span class="info_header">是否发送账单</span></el-col>-->
        <!--    <el-col :span="15" :offset="1">{{invoice_info.is_with_bill}}</el-col>-->
        <!--</el-row>-->
        <!--<el-row :gutter="20">-->
        <!--    <el-col :span="6" :offset="2"><span class="info_header">购方企业名称</span></el-col>-->
        <!--    <el-col :span="15" :offset="1">{{invoice_info.company_name}}</el-col>-->
        <!--</el-row>-->
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">开票企业名称</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.invoice_company" icon="el-icon-document-copy"></el-button>
                {{invoice_info.invoice_company}}
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">购方税号</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.tax_number" icon="el-icon-document-copy"></el-button>
                {{invoice_info.tax_number}}
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">地址 电话</span></el-col>
            <el-col :span="15" :offset="1">
                <template v-if="invoice_info.address != '' || invoice_info.phone != ''">
                    <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.address+' '+invoice_info.phone" icon="el-icon-document-copy"></el-button>
                    {{invoice_info.address}} {{invoice_info.phone}}
                </template>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">开户银行 银行账号</span></el-col>
            <el-col :span="15" :offset="1">
                <template v-if="invoice_info.bank != '' || invoice_info.bank_account != ''">
                    <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.bank+' '+invoice_info.bank_account" icon="el-icon-document-copy"></el-button>
                    {{invoice_info.bank}} {{invoice_info.bank_account}}
                </template>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">收件信息</span></el-col>
            <el-col :span="15" :offset="1">
                <template v-if="invoice_info.post_address != '' || invoice_info.post_receiver != '' || invoice_info.post_phone != ''">
                    <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.post_address+' '+invoice_info.post_receiver+' '+invoice_info.post_phone" icon="el-icon-document-copy"></el-button>
                    {{invoice_info.post_address}} {{invoice_info.post_receiver}} {{invoice_info.post_phone}}
                </template>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">电子发票收件人邮箱</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.email}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">开票日期</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.date}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">金额</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.money}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">发票类型</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.invoice_type_name}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">开票内容</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.invoice_content}}</el-col>
        </el-row>


        <el-row ygutter="20" v-if="invoice_info.flush_to != ''">
            <el-col :span="6" :offset="2"><span class="info_header">红冲发票 原发票</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.flush_to" icon="el-icon-document-copy"></el-button>
                {{invoice_info.flush_to}}
            </el-col>
        </el-row>
        <el-row ygutter="20" v-if="invoice_info.flush_to != ''">
            <el-col :span="6" :offset="2"><span class="info_header">红冲发票 原发票开票時间</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.flush_to_date}}</el-col>
        </el-row>
        <el-row ygutter="20" v-if="invoice_info.flush_to != ''">
            <el-col :span="6" :offset="2"><span class="info_header">红冲发票 原发票号</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.flush_to_no" icon="el-icon-document-copy"></el-button>
                {{invoice_info.flush_to_no}}
            </el-col>
        </el-row>


        <el-row ygutter="20" v-if="invoice_info.flush_by != ''">
            <el-col :span="6" :offset="2"><span class="info_header">红冲发票</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.flush_by" icon="el-icon-document-copy"></el-button>
                {{invoice_info.flush_by}}
            </el-col>
        </el-row>
        <el-row ygutter="20" v-if="invoice_info.flush_by != ''">
            <el-col :span="6" :offset="2"><span class="info_header">红冲发票 红冲发票开票時间</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.flush_by_date}}</el-col>
        </el-row>
        <el-row ygutter="20" v-if="invoice_info.flush_by != ''">
            <el-col :span="6" :offset="2"><span class="info_header">红冲发票 红冲发票号</span></el-col>
            <el-col :span="15" :offset="1">
                <el-button v-if="invoice_info.flush_by_no" type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.flush_by_no" icon="el-icon-document-copy"></el-button>
                {{invoice_info.flush_by_no}}
            </el-col>
        </el-row>


        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">回款单号</span></el-col>
            <el-col :span="15" :offset="1">
                <template v-for="receipt_serial in invoice_info.receipt_serial_list">
                    <a :href="'/Receipt/index.html?receipt_serial='+receipt_serial" target="_blank">{{receipt_serial}}</a>
                </template>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">回款时间</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.remit_day}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">业务备注</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.remark}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">发票备注</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.invoice_remark}}</el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">状态</span></el-col>
            <el-col :span="15" :offset="1">
                <el-tag :type="tag_type_map[invoice_info.status]" size="small">{{invoice_info.status_text}}</el-tag>
            </el-col>
        </el-row>
        <el-row :gutter="20">
            <el-col :span="6" :offset="2"><span class="info_header">来源</span></el-col>
            <el-col :span="15" :offset="1">{{invoice_info.source}}</el-col>
        </el-row>

        <el-row ygutter="20" v-if="invoice_info.contract_no != ''">
            <el-col :span="6" :offset="2"><span class="info_header">合同</span></el-col>
            <el-col :span="15" :offset="1">
                {{invoice_info.contract_no}}
                <el-button @click="view_pdf_use_contract(invoice_info.contract_no,invoice_info.file_url)" size="mini">合同预览</el-button>
            </el-col>
        </el-row>

        <!--<el-row :gutter="20">-->
        <!--    <el-col :span="6" :offset="2"><span class="info_header">快递单号</span></el-col>-->
        <!--    <el-col :span="15" :offset="1">-->
        <!--        <template v-if="invoice_info.send_status == 0">-->
        <!--            <el-tag type="info" size="small">未邮寄</el-tag>-->
        <!--        </template>-->
        <!--        <template v-else>-->
        <!--            <el-tag type="success" size="small">已邮寄</el-tag>-->
        <!--            <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="invoice_info.express_no" icon="el-icon-document-copy"></el-button>-->
        <!--            {{invoice_info.express_no}}-->
        <!--        </template>-->
        <!--    </el-col>-->
        <!--</el-row>-->


        <div slot="footer" class="dialog-footer">

            <template v-if="is_sale === 'yes'" v-for="(info,action) in salesman_buttons[invoice_info.status]">
                <template v-if="action !== 'view'">
                    <el-button :type="info.type" @click="updateStatus(invoice_info,action)" size="mini">{{info.name}}</el-button>
                </template>
            </template>
            <template v-if="is_sale === 'no'" v-for="(info,action) in finance_buttons[invoice_info.status]">
                <template v-if="action !== 'view'">
                    <!--<template v-if="action === 'copy'">-->
                    <!--    <el-button class="copy_btn" :type="info.type" :data-clipboard-text="invoice_info.copy_text" plain size="mini">{{info.name}}</el-button>-->
                    <!--</template>-->
                    <template v-if="action === 'express'">
                        <template v-if="invoice_info.send_status == 0">
                            <el-button size="mini" :type="info.add.type" @click="updateStatus(invoice_info,action)">{{info.add.name}}</el-button>
                        </template>
                        <template v-else>
                            <el-button size="mini" :type="info.edit.type" @click="updateStatus(invoice_info,action)">{{info.edit.name}}</el-button>
                        </template>
                    </template>
                    <template v-else>
                        <el-button :type="info.type" @click="updateStatus(invoice_info,action)" size="mini">{{info.name}}</el-button>
                    </template>
                </template>
            </template>

            <el-button @click="cancle_info_dialog()" size="mini">取 消</el-button>
            <!--<el-button type="primary" @click="applyInvoice()" :disabled="ok_confirm">确 定</el-button>-->
        </div>
    </el-dialog>
    <!--发票详情弹窗-->


    <!--填写快递单号-->
    <el-dialog width="30%" :title="ded.dialog_title" :visible="ded.dialog_visible" v-loading="ded.loading" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" >
        <el-form ref="form" label-width="100px" label-position="right">
            <el-form-item label="快递单号" required>
                <el-input v-model="ded.express_no" placeholder="快递单号"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancle_add_express_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="add_express_no()" :disabled="ok_confirm" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--填写快递单号-->


    <!--红冲-->
    <el-dialog width="30%" :title="ifd.dialog_title" :visible="ifd.dialog_visible" v-loading="ifd.loading" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" >
        <el-form ref="form" label-width="100px" label-position="right">
            <template v-if="ifd.flush_type == 'all'">
                <span style="font-size: 1.2em;font-weight: bolder;color:#f54242;">该发票不可部分退款,仅可重新开票!</span>
            </template>
            <template v-else>
                <el-form-item label="红冲金额" required>
                    <el-input v-model="ifd.flush_money" placeholder="红冲金额"></el-input>
                </el-form-item>
            </template>
            <p> </p>
            <el-form-item label="业务备注" required>
                <el-input v-model="ifd.remark" placeholder="业务备注"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="ifd.dialog_visible = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="invoice_flush()" :disabled="ok_confirm" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--红冲-->

    <!--导入勾选数据-->
    <el-dialog width="30%" :title="isd.dialog_title" :visible="isd.dialog_visible" v-loading="isd.loading" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" >
        <el-form ref="form" label-width="100px" label-position="right">
            <el-form-item label="开票数据" required>
                <el-input type="textarea" v-model="isd.string_data" placeholder="放入需要勾选的金额,每行一个"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancle_import_source_data_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="import_source_data()" :disabled="ok_confirm" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--导入勾选数据-->

    <!--导入勾选数据 2 不匹配数据提示-->
    <el-dialog width="50%" :title="acd.dialog_title" :visible="acd.dialog_visible" v-loading="acd.loading" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" >

        <el-table :data="acd.none_match_data" border fit>
            <el-table-column label="客户" prop="customer_name"></el-table-column>
            <el-table-column label="客户id" prop="customer_id"></el-table-column>
            <el-table-column label="产品" prop="product_name"></el-table-column>
            <el-table-column label="产品id" prop="product_id"></el-table-column>
            <el-table-column label="月分" prop="month"></el-table-column>
            <el-table-column label="表格金额" prop="table_money"></el-table-column>
            <el-table-column label="消耗金额" prop="consume_money"></el-table-column>
            <el-table-column label="差额" prop="diff_money"></el-table-column>
            <el-table-column label="不匹配原因" prop="not_match_note"></el-table-column>
        </el-table>

        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="acd.dialog_visible = false" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--导入勾选数据 2 不匹配数据提示-->

    <!--弹窗-->
</div>

<script type="application/javascript">
    //来源
    let source_map = [
        {"name": "全部", "source": -1},
        {"name": "羽乐科技", "source": 0},
        {"name": "朴道", "source": 1},
        {"name": "浙数交", "source": 10},
        {"name": "郑数交", "source": 2},
    ];

    //有效的渠道
    let effective_source = [0,1,10,2];

    let payment_type_map = [
        {"name": "全部", "type": -1},
        {"name": "预付费", "type": 1},
        {"name": "后付费", "type": 2}
    ];
    let send_status_map = [
        {"name": "全部",   "type": -1},
        {"name": "未邮寄", "type": 0},
        {"name": "已邮寄", "type": 1}
    ];

    let tag_type_map = {
        0:'info',
        10:'',
        15:'danger',
        16:'danger',
        20:'warning',
        30:'success',
        90:'success',
        100:'success',
    };

    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,
            didd_loading: false,
            info_loading: false,
            user_cookie:user_cookie,
            tab_name:'applyed',
            //接口地址
            urls: {
                user_auth             : url_prefix + '/options/getMap?user_auth=true&user_cookie=' + user_cookie,
                unapplied_list        : url_prefix + '/invoice/unapplied_list', //获取商务未申请列表
                unapplied_consume_list: url_prefix + '/invoice/unapplied_consume_list', //获取商务未申请列表
                unapplied_receipt_list: url_prefix + '/invoice/unapplied_receipt_list', //获取商务未申请列表
                invoice_list          : url_prefix + '/invoice/invoice_list',   //获取发票列表
                consume_info_list     : url_prefix + '/invoice/consume_info_list',//获取消耗明细列表 导出用
                invoice_info          : url_prefix + '/invoice/invoice_info',   //获取发票详情
                options               : url_prefix + '/invoice/options',        //获取客户和产品列表 如果 登录用户非商务获取所有
                customer_list         : url_prefix + '/invoice/customer_list',  //获取客户和产品列表 如果 登录用户非商务获取所有
                informations          : url_prefix + '/invoice/informations',   //获取客户开票信息
                invoice_company_list  : url_prefix + '/invoice/invoice_company_list', //获取客户的开票公司列表
                receipt_list          : url_prefix + '/invoice/receipt_list',   //获取客户收款单列表
                contract_list         : url_prefix + '/invoice/contract_list',  //获取客户先票后款合同列表
                apply                 : url_prefix + '/invoice/apply',          //申请开票
                reject                : url_prefix + '/invoice/reject',         //驳回
                cancel                : url_prefix + '/invoice/cancel',         //取消
                pass                  : url_prefix + '/invoice/pass',           //审核通过
                add_express_no        : url_prefix + '/invoice/add_express_no', //添加快递单号
                issue_by_excel        : url_prefix + '/invoice/issue_by_excel', //申请开票
                batch_pass            : url_prefix + '/invoice/batch_pass',     //批量审批
                void                  : url_prefix + '/invoice/void',           //作废
                flush                 : url_prefix + '/invoice/flush',          //红冲
                flush_cancel          : url_prefix + '/invoice/flush_cancel',   //红冲撤销

                auto_check            : url_prefix + '/invoice/auto_check',     //开票时辅助勾选金额
            },
            //快捷时间选择配置
            picker_options: {
                shortcuts: [{
                    text: '本月',
                    onClick(picker) {
                        picker.$emit('pick', [new Date(), new Date()]);
                    }
                }, {
                    text: '今年至今',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date(new Date().getFullYear(), 0);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick(picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setMonth(start.getMonth() - 3);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近六个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setMonth(start.getMonth() - 6);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            //客户列表
            customer_list: [],
            //产品列表
            product_list: [],

            //状态
            status_map: [],

            //邮寄状态
            send_status_map: send_status_map,

            //是否登录用户是商务
            is_sale: 'none',

            //是否展示发票流水号列
            show_invoice_id_column: true,

            //是否展示开票按钮
            show_issue_button:false,

            //是否展示审核按钮
            show_pass_button:true,

            //是否展示表格的复选框
            show_check_box:true,

            //来源
            source_map: source_map,
            //付费类型
            payment_type_map: payment_type_map,
            //状态tag类型
            tag_type_map: tag_type_map,

            //发票数据列表
            table_data: [],
            //待开票消耗
            table_unapply_consume: [],
            //待开票收款
            table_data_receipt: [],

            //上传文件列表
            fileList:[],

            //操作按钮
            salesman_buttons : {
                //已申请
                10 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    'cancel': {
                        'name': '撤销',
                        'type': 'warning',
                    },
                },
                //已驳回
                15 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                },
                //已撤销
                16 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                },
                //已审核
                20 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                },
                //已开票
                30 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    'flush': {
                        'name': '红冲',
                        'type': 'danger',
                    },
                },
                //部分回款
                90 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    'flush': {
                        'name': '红冲',
                        'type': 'danger',
                    },
                },
                //已回款
                100 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    'flush': {
                        'name': '红冲',
                        'type': 'danger',
                    },
                },
            },

            finance_buttons : {
                //已申请
                10 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    'reject': {
                        'name': '驳回',
                        'type': 'danger',
                    },
                    'pass': {
                        'name': '审核通过',
                        'type': 'success',
                    },
                },
                //已驳回
                15 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                },
                //已撤销
                16 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                },
                //已审核
                20 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                },
                //已开票
                30 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    // 'express': {
                    //     'add': {
                    //         'name': '填写快递单号',
                    //         'type': 'primary',
                    //     },
                    //     'edit': {
                    //         'name': '修改快递单号',
                    //         'type': 'warning',
                    //     }
                    // },
                    // 'void': {
                    //     'name': '作废',
                    //     'type': 'danger',
                    // },
                    // 'written_back': {
                    //     'name': '冲红',
                    //     'type': 'danger',
                    // },
                },
                //部分回款
                90 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    // 'express': {
                    //     'add': {
                    //         'name': '填写快递单号',
                    //         'type': 'primary',
                    //     },
                    //     'edit': {
                    //         'name': '修改快递单号',
                    //         'type': 'warning',
                    //     }
                    // },
                },
                //已回款
                100 : {
                    'view': {
                        'name': '查看',
                        'type': 'info',
                    },
                    // 'express': {
                    //     'add': {
                    //         'name': '填写快递单号',
                    //         'type': 'primary',
                    //     },
                    //     'edit': {
                    //         'name': '修改快递单号',
                    //         'type': 'warning',
                    //     }
                    // },
                },
            },

            //分页
            total_num: 0,
            page_size: 20,
            current_page: 1,

            ok_confirm: false,

            payment_type_name_map:{
                'pre': 1,
                'alr': 2,
            },

            invoice_info_visible:false,
            invoice_info:[],//发票详情信息

            batch_pass_invoice_ids:[],//批量审评发票流水号

            invoice_company_list:[],//客户的开票公司列表
            //弹窗
            formLabelWidth: '100px',
            //申请开票弹窗 apply_invoice_dialog_data
            didd: {
                loading:false,
                current_tab: 'alr',//标签名  pre 预付费  alr 后付费
                dialog_title: '申请开票',//申请快票弹窗标题
                apply_invoice_visible: false,//是否展示
                //查询条件校验
                rules: {
                    // customer: [
                    //     { required: true, message: '请选择客户', trigger: 'change' },
                    // ],
                    source:[
                        { required: true, message: '请选择来源!', trigger: 'change' },
                    ],
                },
                source_map: [
                    {"name": "羽乐科技", "source": 0},
                    {"name": "朴道", "source": 1},
                    {"name": "浙数交", "source": 10},
                    {"name": "郑数交", "source": 2},
                ],
                payment_type_map: [
                    {"name": "预付费", "type": 1},
                    {"name": "后付费", "type": 2}
                ],
                customer_list: {},//登录用户的客户列表,不与发票列表的共享数据
                product_list: {},//登录用户的客户的账户所使用的产品父id列表
                customer_invoice_information: [],//开票信息列表
                for_apply_items: [],//后付费 待开票消耗 列表
                receipt_serial_list:[],//预付费客户 收款单 列表
                filters_customer:[],//筛选客户
                filters_product:[],//筛选产品
                total_num: 0,//总数
                page_size: 50,
                current_page: 1,
                //搜索条件
                s_form: {
                    payment_type: 2,
                    source: 0,
                    customer: '',
                    product: '',
                    invoice_model: '',//选中客户的开票模式
                },
                //在申请开票弹窗的待开票数据列表中 选中一个待开票项目时更新选中条数与选中的总金额
                selection_data:{
                    count:0,
                    sum:'0.00',
                },
                //申请开票数据
                apply_data: {
                    need_bill:false,//是否需要对账单(盖章)
                    customer_id: '',//开票客户
                    customer_ids: [],//开票客户 多选
                    customer_list:[],//开票客户列表
                    invoice_type:'',//发票类型 专票 普票
                    remark:'',//备注
                    invoice_remark:'',//写在发票中的备注
                    money:'0.00',//开票金额
                    receipt_serial:'',//开票收款单 预付费必填
                    customer_cost_list: [],//开票产品 月份 {father_id: father_id, month_date: month_date}
                    preset_receipts:[],//渠道预付费 开票 虚拟收款单 客户->金额
                    customer_invoice_information_id:'',//开票信息id
                    source:0,
                    //开票信息
                    company_name:'',
                    invoice_company:'',
                    tax_number:'',
                    bank:'',
                    bank_account:'',
                    address:'',
                    phone:'',
                    post_receiver:'',
                    post_phone:'',
                    post_address:'',
                    email:'',
                    invoice_model:0,//开票模式 羽乐科技 按客户开票模式 渠道后付费按子产品可拆金额模式 渠道预付费 为先票后款
                    contract_no:'',
                },
                informations:[],
                informations_idx:0,
                // information_edit_status:{
                //     company_name:false,
                //     invoice_company:false,
                //     tax_number:false,
                //     bank:false,
                //     bank_account:false,
                //     address:false,
                //     phone:false,
                //     post_receiver:false,
                //     post_phone:false,
                //     post_address:false,
                // },
                contract_list:[],
                contract_no_url_map:{}, //合同编号 -> url
            },
            ded:{
                loading:false,
                dialog_title:'填写快递单号',
                dialog_visible:false,
                express_no:'',
                invoice_id:'',
            },

            //发票红冲
            ifd:{
                loading: false,
                dialog_title: '红冲',
                dialog_visible: false,
                flush_money: '',//红冲金额
                invoice_model: 0,//开票模式
                flush_type: '',//all part
                invoice_id: '',//红冲发票
                remark:'',//业务备注
            },

            //渠道开票导入数据 import_source_data
            isd:{
                show_button:false,
                loading:false,
                dialog_title:'开票导入数据',
                dialog_visible:false,
                string_data:'',
                array_data:[],
                duplicate_data:[], //重复的金额 不匹配
            },


            // 开票时辅助勾选金额 auto_check_data 代替 渠道开票导入数据isd
            acd : {
                source:0, // 不同渠道excel格式不同
                user_cookie:user_cookie, // 不同渠道excel格式不同
                month_ragne:[], //用于校验消耗月份
                dialog_title:'以下是未匹配数据,请核对',
                dialog_visible:false,
                loading:false,
                none_match_data:[],
            },

            empty_arr:['',undefined,null], // 空值

            //搜索条件
            search_form: {
                create_at: [],
                customer: '',
                account: '',
                product: '',
                op_type: '',
                status: ['10','20','30','90','100'],//默认展示已申请状态
                source: -1,
                payment_type: -1,
                invoice_company:'',//客户的开票公司
            },
        },
        computed: {
            //预付费金额展示大写
            money_zh_big() {
                let nzhcn = Nzh.cn;
                return nzhcn.toMoney(this.didd.apply_data.money,{outSymbol:false});
            },
            //后付费金额展示大写
            sum_zh_big() {
                let nzhcn = Nzh.cn;
                return nzhcn.toMoney(this.didd.selection_data.sum,{outSymbol:false});
            },
        },
        watch: {
            didd: {
                handler(newVal) {
                    //过滤输入数字中的','(千分位符号)
                    if (typeof this.didd.apply_data.money === "string") {
                        this.didd.apply_data.money = this.didd.apply_data.money.replace(/[^0-9.-]/g, '');
                    }
                },
                deep: true,
            },
        },
        mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);
        },
        created: async function () {
            // await this.getOptions();//这个函数会更新表格数据
            await this.get_options(this.search_form.payment_type);
            if(this.is_sale === 'yes') {
                this.search_form.status = ['10', '20', '30', '90', '100'];
            }else{
                this.search_form.status = '10';
                this.search_form.invoice_company = '';
            }
            this.searchTableData();
            this.get_invoice_company_list();
        },
        methods: {
            tab_click:async function(){
                if(this.tab_name == 'unapply' || this.tab_name == 'unapply_receipt') {
                    this.search_form.status = ['0'];
                    this.search_form.create_at = [];
                }else{
                    if(this.is_sale === 'yes') {
                        this.search_form.status = ['10', '20', '30', '90', '100'];
                    }else{
                        this.search_form.status = '10';
                        this.search_form.invoice_company = '';
                    }
                }
                this.searchTableData();
            },
            //获取未开票消耗数据
            get_unapplied_consume_list: async function () {
                let self = this;
                self.loading = true;
                let product = self.search_form.product;
                let customer = self.search_form.customer;
                let source = self.search_form.source;
                let create_at = self.search_form.create_at;

                let para = {
                    limit: self.page_size,
                    page: self.current_page,
                    user_cookie: user_cookie,
                };
                if (customer) {
                    para.customer = customer;
                }
                // if (source === 0 || source === 1 || source === -1 || source === 10) {
                if(effective_source.indexOf(source) !== -1){
                    para.source = source;
                }
                if (product) {
                    para.product = product;
                }

                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (create_at && !isNaN(create_at[0]) && !isNaN(create_at[1])) {
                    let time = [];
                    time[0] = (create_at[0] - create_at[0] % 1000) / 1000;
                    time[1] = (create_at[1] - create_at[1] % 1000) / 1000;
                    para.create_at = time;
                }

                await axios.post(self.urls.unapplied_consume_list, para).then(function (response) {
                    if(response.data.status == 0) {
                        self.total_num             = response.data.data.count;
                        self.table_unapply_consume = response.data.data.list;
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            //获取未开票消耗数据
            get_unapplied_receipt_list: async function () {
                let self = this;
                self.loading = true;
                let customer = self.search_form.customer;
                let source = self.search_form.source;

                let para = {
                    limit: self.page_size,
                    page: self.current_page,
                    user_cookie: user_cookie,
                };
                if (customer) {
                    para.customer = customer;
                }
                // if (source === 0 || source === 1 || source === -1 || source === 10) {
                if(effective_source.indexOf(source) !== -1){
                    para.source = source;
                }

                await axios.post(self.urls.unapplied_receipt_list, para).then(function (response) {
                    if(response.data.status == 0) {
                        self.total_num          = response.data.data.count;
                        self.table_data_receipt = response.data.data.list;
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            //查询 导出 参数
            get_para:function(){
                let customer = this.search_form.customer;
                let payment_type = this.search_form.payment_type;
                let create_at = this.search_form.create_at;
                let source = this.search_form.source;
                let product = this.search_form.product;
                let status = this.search_form.status;
                let send_status = this.search_form.send_status;
                let invoice_id = this.search_form.invoice_id;
                let express_no = this.search_form.express_no;
                let invoice_company = this.search_form.invoice_company;

                let para = {
                    limit: this.page_size,
                    page: this.current_page,
                    user_cookie: user_cookie,
                };
                if (customer) {
                    para.customer = customer;
                }
                if (payment_type) {
                    para.payment_type = payment_type;
                }
                if(effective_source.indexOf(source) !== -1){
                    para.source = source;
                }
                if (product) {
                    para.product = product;
                }
                if (status) {
                    para.status = status;
                }
                if (send_status === 0 || send_status === 1 || send_status === 2) {
                    para.send_status = send_status;
                }
                if (invoice_id) {
                    para.invoice_id = invoice_id;
                }
                if (express_no) {
                    para.express_no = express_no;
                }
                if (invoice_company) {
                    para.invoice_company = invoice_company;
                }

                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (create_at && !isNaN(create_at[0]) && !isNaN(create_at[1])) {
                    let time = [];
                    time[0] = (create_at[0] - create_at[0] % 1000) / 1000;
                    time[1] = (create_at[1] - create_at[1] % 1000) / 1000;
                    para.create_at = time;
                }

                return para;
            },
            //获取驳回列表数据
            getTableData: async function () {
                let self = this;
                self.loading = true;

                let para = this.get_para();

                let t_data = await this.getData(para);
                self.table_data = t_data.table_data;
                self.total_num = t_data.total_num;
                for (let i = 0; i < self.table_data.length; i++) {
                    self.table_data[i]['copy_text'] =
                        '金融数据' + "\t" +
                        self.table_data[i]['invoice_id'] + "\t" +
                        self.table_data[i]['salesman_name'] + "\t" +
                        self.table_data[i]['email'] + "\t" +
                        "" + "\t" +
                        self.table_data[i]['invoice_type_name'] + "\t" +
                        self.table_data[i]['customer_name'] + "\t" +
                        "" + "\t" +
                        // self.table_data[i]['company_name'] + "\t" +
                        self.table_data[i]['invoice_company'] + '\t' +
                        self.table_data[i]['tax_number'] + '\t' +
                        self.table_data[i]['bank'] + ' ' + self.table_data[i]['bank_account'] + '\t' +
                        self.table_data[i]['address'] + ' ' + self.table_data[i]['phone'] + '\t' +
                        self.table_data[i]['invoice_content'] + '\t' +
                        self.table_data[i]['money'] + '\t' +
                        self.table_data[i]['remark'] + '\t' +
                        self.table_data[i]['status_text'] + '\t' +
                        "" + "\t" +
                        "" + "\t" +
                        "" + "\t" +
                        self.table_data[i]['created_at'] + '\t' +
                        "" + "\t" +
                        self.table_data[i]['updated_at'] + '\t' +
                        self.table_data[i]['salesman_name'];
                }
            },
            getData: async function (para) {
                let self = this;
                self.loading = true;
                let table_data = [];

                await axios.post(self.urls.invoice_list, para).then(function (response) {
                    if(response.data.status == 0) {
                        table_data = response.data.data.list;
                        total_num  = response.data.data.count;

                        for (let i = 0; i < table_data.length; i++) {
                            if(table_data[i]['apply_date']){
                                table_data[i]['apply_date_short'] = table_data[i]['apply_date'].substring(0, 10);
                            }
                            if(table_data[i]['invoice_date']){
                                table_data[i]['invoice_date_short'] = table_data[i]['invoice_date'].substring(0, 10);
                            }
                            if(table_data[i]['remit_day']){
                                table_data[i]['remit_day_short'] = table_data[i]['remit_day'].substring(0, 10);
                            }
                        }
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
                return {
                    table_data:table_data,
                    total_num:total_num
                };
            },
            //获取客户和产品列表
            getOptions: async function () {
                await this.get_options(this.search_form.payment_type);
                this.searchTableData();
                // this.search_form.customer = '';
            },
            //获取 申请开票弹窗 的 客户和产品列表
            getOptionsDialog: function () {
                this.get_options_didd(this.didd.s_form.payment_type);
                this.didd.s_form.customer = '';
            },
            get_options: async function (payment_type) {
                let self = this;
                self.loading = true;
                self.ok_confirm = true;
                await axios.post(self.urls.options, {user_cookie: user_cookie, payment_type: payment_type}).then(function (response) {
                    if (response.data.status === 0) {
                        self.product_list    = response.data.data.product_list;
                        self.customer_list   = response.data.data.customer_list;
                        self.status_map      = response.data.data.invoice_status;
                        // self.send_status_map = response.data.data.send_status;
                        self.is_sale         = response.data.data.is_sale?'yes':'no';

                        if (self.is_sale === 'yes') {
                            self.didd.customer_list = response.data.data.customer_list;
                            self.didd.product_list = response.data.data.product_list;
                        }
                        self.ok_confirm = false;
                        self.loading = false;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    self.loading = false;
                    self.ok_confirm = false;
                    errorMsg(error);
                });
            },
            //获取申请开票弹窗会用到的数据
            get_options_didd: function (payment_type) {
                let self = this;
                self.ok_confirm = true;
                self.didd_loading = true;
                axios.post(self.urls.options, {user_cookie: user_cookie, payment_type: payment_type}).then(function (response) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    if (response.data.status === 0) {
                        // self.didd.customer_list = response.data.data.customer_list;
                        self.didd.product_list = response.data.data.product_list;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    errorMsg(error);
                });
            },
            //计算朴道预付费开票金额
            sum_money:function(){
                let sum = 0;
                for (let i = 0; i < this.didd.apply_data.preset_receipts.length; i++) {
                    // console.log('计算朴道预付费开票金额');
                    let tmp_money = this.didd.apply_data.preset_receipts[i].money.replace(/[^0-9.-]/g, '');
                    sum = sum + parseFloat(tmp_money);
                }
                this.didd.apply_data.money = "" + sum + "";//这里转为字符串 为了处理this.didd.apply_data.money的watch方法
            },
            //申请预付费开票 选择客户后更新开票信息 地址信息
            changePreCustomers:function(customer_id){
                console.log(customer_id);
                this.didd.receipt_serial_list = [];//清空收款单列表

                if (Array.isArray(customer_id)) {
                    // console.log('这是一个数组');
                    // console.log(this.didd.customer_list);
                    this.didd.apply_data.preset_receipts = [];//多选时填写金额的列表
                    // 如果时数组 则为渠道预付费开票 先票后款
                    for (let i = 0; i < customer_id.length; i++) {
                        this.didd.apply_data.preset_receipts[i] = {
                            'customer_id':customer_id[i],
                            'customer_name':this.didd.customer_list[customer_id[i]]['name'],
                            'money':'0',//这里给个字符串默认值,处理计算总金额时对金额中的千分位符号进行过滤
                        };
                    }

                    this.get_customer_invoice_informations(this.didd.apply_data.customer_id,this.didd.apply_data.source);//获取开票信息
                } else {
                    // console.log('这不是一个数组');
                    this.get_customer_invoice_informations(this.didd.apply_data.customer_id,0);//获取开票信息
                    if(this.didd.apply_data.invoice_model == 5) {
                        this.get_pre_customer_receipt_list(this.didd.apply_data.customer_id);//获取预付费客户收款单列表
                    }
                    if(this.didd.apply_data.invoice_model == 6) {
                        console.log('获取先票后款客户合同列表！！！');
                        this.get_pre_customer_contract_list(this.didd.apply_data.customer_id);//获取预付费客户收款单列表
                    }
                }

            },
            get_pre_customer_contract_list:function(customer_id) {
                let self = this;
                self.ok_confirm = true;
                self.didd_loading = true;
                self.didd.contract_list = [];
                self.didd.contract_no_url_map = {};
                axios.post(self.urls.contract_list, {user_cookie: user_cookie, customer_id: customer_id}).then(function (response) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    if (response.data.status === 0) {
                        self.didd.contract_list = response.data.data;
                        if(self.didd.contract_list) {
                            for (let i = 0; i < self.didd.contract_list.length; i++) {
                                self.didd.contract_no_url_map[self.didd.contract_list[i]['contract_no']] = self.didd.contract_list[i]['file_url'];
                            }
                        }
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    errorMsg(error);
                });
            },
            //获取预付费客户收款单列表
            get_pre_customer_receipt_list:function(customer_id){
                let self = this;
                self.ok_confirm = true;
                self.didd_loading = true;
                axios.post(self.urls.receipt_list, {user_cookie: user_cookie, customer_id: customer_id}).then(function (response) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    if (response.data.status === 0) {
                        // console.log("get_pre_customer_receipt_list");
                        // console.log(response.data.data);
                        // receipt_serial 流水号
                        // money 金额
                        // remit_date_fmt 交易日期
                        // name 公司名
                        self.didd.receipt_serial_list = response.data.data.list;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    errorMsg(error);
                });
            },
            //获取选择客户的开票信息
            get_customer_invoice_informations: function (customer_id,source) {
                let self = this;
                self.ok_confirm = true;
                self.didd_loading = true;
                self.didd.informations = [];
                self.didd.informations_idx = undefined; // 清空开票信息

                self.clear_informations();

                axios.post(self.urls.informations, {user_cookie: user_cookie, customer_id: customer_id,source:source}).then(function (response) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    if (response.data.status === 0) {
                        for (let i = 0; i < response.data.data.length; i++) {
                            self.$set(self.didd.informations,i,{});
                            // self.didd.customer_invoice_information = response.data.data[i];
                            // console.log(response.data.data[i]['tax_number']);
                            self.$set(self.didd.informations[i],'company_name',response.data.data[i]['company_name']);
                            self.$set(self.didd.informations[i],'invoice_company',response.data.data[i]['invoice_company']);
                            self.$set(self.didd.informations[i],'tax_number',response.data.data[i]['tax_number']);
                            self.$set(self.didd.informations[i],'bank',response.data.data[i]['bank']);
                            self.$set(self.didd.informations[i],'bank_account',response.data.data[i]['bank_account']);
                            self.$set(self.didd.informations[i],'address',response.data.data[i]['address']);
                            self.$set(self.didd.informations[i],'phone',response.data.data[i]['phone']);
                            self.$set(self.didd.informations[i],'post_receiver',response.data.data[i]['post_receiver']);
                            self.$set(self.didd.informations[i],'post_phone',response.data.data[i]['post_phone']);
                            self.$set(self.didd.informations[i],'post_address',response.data.data[i]['post_address']);
                            self.$set(self.didd.informations[i],'email',response.data.data[i]['email']);
                            self.$set(self.didd.informations[i],'invoice_type',response.data.data[i]['invoice_type']);
                            self.$set(self.didd.informations[i],'invoice_content',response.data.data[i]['invoice_content']);
                        }
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    errorMsg(error);
                });
            },
            change_informations:function(information_idx){
                // console.log('fuction change_informations:',information_idx);
                // 调用clear_informations时可能会触发这个方法，information_idx为空，此时不执行后续代码
                if(this.empty_arr.indexOf(information_idx) !== -1){
                    return;
                }

                this.$set(this.didd.apply_data,'company_name',this.didd.informations[information_idx]['company_name']);
                this.$set(this.didd.apply_data,'invoice_company',this.didd.informations[information_idx]['invoice_company']);
                this.$set(this.didd.apply_data,'tax_number',this.didd.informations[information_idx]['tax_number']);
                this.$set(this.didd.apply_data,'bank',this.didd.informations[information_idx]['bank']);
                this.$set(this.didd.apply_data,'bank_account',this.didd.informations[information_idx]['bank_account']);
                this.$set(this.didd.apply_data,'address',this.didd.informations[information_idx]['address']);
                this.$set(this.didd.apply_data,'phone',this.didd.informations[information_idx]['phone']);
                this.$set(this.didd.apply_data,'post_receiver',this.didd.informations[information_idx]['post_receiver']);
                this.$set(this.didd.apply_data,'post_phone',this.didd.informations[information_idx]['post_phone']);
                this.$set(this.didd.apply_data,'post_address',this.didd.informations[information_idx]['post_address']);
                this.$set(this.didd.apply_data,'email',this.didd.informations[information_idx]['email']);
                this.$set(this.didd.apply_data,'invoice_type',this.didd.informations[information_idx]['invoice_type']);
                this.$set(this.didd.apply_data,'invoice_content',this.didd.informations[information_idx]['invoice_content']);
            },
            clear_informations:function(){
                // console.log('fuction clear_informations');
                this.didd.informations_idx = undefined; // 清空开票信息
                this.$set(this.didd.apply_data,'company_name','');
                this.$set(this.didd.apply_data,'invoice_company','');
                this.$set(this.didd.apply_data,'tax_number','');
                this.$set(this.didd.apply_data,'bank','');
                this.$set(this.didd.apply_data,'bank_account','');
                this.$set(this.didd.apply_data,'address','');
                this.$set(this.didd.apply_data,'phone','');
                this.$set(this.didd.apply_data,'post_receiver','');
                this.$set(this.didd.apply_data,'post_phone','');
                this.$set(this.didd.apply_data,'post_address','');
                this.$set(this.didd.apply_data,'email','');
                this.$set(this.didd.apply_data,'invoice_type','');
                this.$set(this.didd.apply_data,'invoice_content','');
            },
            clear_informations_with_confirm:function(){
                if(this.empty_arr.indexOf(this.didd.informations_idx) !== -1){
                    return;
                }

                this.$confirm('此操作会清空已经填写的开票信息, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.clear_informations();
                    this.$message({
                        type: 'success',
                        message: '已清空!'
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消清空开票信息操作'
                    });
                });
            },
            view_pdf: function () {
                let contract_no = this.didd.apply_data.contract_no;
                let file_url = this.didd.contract_no_url_map[contract_no];
                let url = "/Account/Contract/pdfview.html?pdfurl="+file_url+"&contract_no="+contract_no;
                window.open(url, '_blank');
            },
            view_pdf_use_contract: function (contract_no,file_url) {
                let url = "/Account/Contract/pdfview.html?pdfurl="+file_url+"&contract_no="+contract_no;
                window.open(url, '_blank');
            },
            //修改每页条书
            handleSizeChange(val) {
                this.page_size = val;
                this.current_page = 1;
                this.getTableData();
            },
            //修改页数,获取数据
            handleCurrentChange(val) {
                this.current_page = val;
                this.getTableData();
            },
            //表单搜索
            searchTableData: function () {
                this.current_page = 1;
                //只有在未申请状态下不展示发票流水号
                if(this.search_form.status === '0') {
                    this.show_invoice_id_column = false;
                }else{
                    this.show_invoice_id_column = true;
                }
                this.show_pass_button  = (this.tab_name == 'applyed' && this.search_form.status == 10);
                this.show_issue_button = (this.tab_name == 'applyed' && this.search_form.status == 20);

                this.show_check_box = (this.tab_name == 'applyed' && this.search_form.status == 10);



                if(this.tab_name == 'unapply') {
                    this.get_unapplied_consume_list();
                    // this.$nextTick(() => {
                    //     this.$refs.ref_table_unapply_consume.doLayout()
                    // })
                }else if(this.tab_name == 'unapply_receipt'){
                    this.get_unapplied_receipt_list();
                    // this.$nextTick(() => {
                    //     this.$refs.ref_table_data_receipt.doLayout()
                    // })
                }else{
                    this.getTableData();
                    // this.$nextTick(() => {
                    //     this.$refs.ref_table_data.doLayout()
                    // })
                }
            },

            filterCustomerHandler:function(value, row, column){
                return row['customer_id'] === value;
            },
            filterProductHandler:function(value, row, column){
                return row['product_id'] === value;
            },

            //修改每页条书
            handleSizeChangeDidd(val) {
                this.didd.page_size = val;
                this.didd.current_page = 1;
                this.get_ddid_data();
            },
            //修改页数,获取数据
            handleCurrentChangeDidd(val) {
                this.didd.current_page = val;
                this.get_ddid_data();
            },

            //获取当前商务的客户列表和对应的父产品列表
            diddSearch: function (didd_dialog) {
                this.didd.current_page = 1;
                this.$refs[didd_dialog].validate((valid) => {
                  if (valid) {
                    this.get_ddid_data();
                    this.get_customer_invoice_informations(this.didd.s_form.customer,this.didd.s_form.source);//获取开票信息
                  } else {
                    console.log('error submit!!');
                    return false;
                  }
                });
                this.isd.show_button = true;
            },
            qiFuInvoice:function(){
                window.location.href = 'http://manage.donotcall.com.cn/Site/Autologin';
            },
            //提交开票申请弹窗
            applyInvoiceDialog: function () {
                this.getOptionsDialog();
                let payment_type = this.payment_type_name_map[this.didd.current_tab];
                this.get_salesman_customers(payment_type);
                this.didd.apply_invoice_visible = true;
            },
            cancle_apply_dialog:function(){
                this.clearDialogData();
                this.didd.apply_invoice_visible = false;
                this.didd.informations = [];
                this.didd.informations_idx = [];
                this.didd.s_form.source = 0; //还原选中羽乐科技 后付费
                this.didd.apply_data.source = 0; //还原选中羽乐科技 预付费
                this.isd.show_button = false; //隐藏导入勾选数据按钮
            },
            //导出 明细数据
            getInfoTableDataExcel: async function () {
                let self = this;
                self.loading = true;

                let para = this.get_para();
                para.limit = -1;
                para.page = 1;

                let t_data = await this.get_consume_info_data(para);
                console.log(t_data);
                console.log(t_data);

                let sheetHeader = [
                    "项目","客户id","客户名","客户公司","销售","消耗id","来源","父产品","子产品",
                    "消耗月份","消耗金额",
                    "发票金额","开票金额","发票流水号","发票号码","申请时间","开票日期","开票公司","发票状态",
                    "回款日期","回款单号","回款单金额","回款金额","回额状态"
                ];
                let sheetData = [];
                $.each(t_data.table_data, function (key, val){
                    sheetData.push(val)
                });

                let option = {};
                const currentTime = new Date().toLocaleString().replace(/[^\d]/g, '');
                option.fileName = '消耗开票明细导出_'+currentTime;
                option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
                if(sheetData.length>0){
                    (new ExportJsonExcel(option)).saveExcel();
                }
                self.loading = false;
            },
            get_consume_info_data: async function(para) {
                let self = this;
                self.loading = true;
                let table_data = [];

                await axios.post(self.urls.consume_info_list, para).then(function (response) {
                    if(response.data.status == 0) {
                        table_data = response.data.data;
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
                return {
                    table_data:table_data,
                    // total_num:total_num
                };
            },
            getTableDataExcel: async function () {
                // 财务导出已申请的不需要消耗数据,单独导出
                if(this.tab_name == 'applyed' && this.is_sale === 'no' && this.search_form.status == 10) {
                    // console.log('apply excel')
                    await this.getApplyTableDataExcel();
                }else{
                    // console.log('other excel')
                    await this.getOtherTableDataExcel();
                }

            },
            //导出 数据
            getOtherTableDataExcel: async function () {
                let self = this;
                self.loading = true;

                let para = this.get_para();
                para.limit = 1000000;
                para.page = 1;

                let t_data = await this.getData(para);

                let sheetHeader = [
                    "项目","发票唯一流水号","客户id","客户名","客户公司",
                    "销售人员","电子发票收件人邮箱","发票维度",
                    "发票类型","商户名称","充值金额/交易金额","开票公司名称",
                    "税号","地址 电话","开票行 开票账号","开票内容","开票金额",
                    "业务备注", "发票备注", "发票状态",
                    "发票号码","发票代码","开票日期","申请时间","驳回原因","最后操作时间","操作人员",
                    "产品","消耗月份","消耗金额"
                ];
                let sheetData = [];
                $.each(t_data.table_data, function (key, item){
                    let item_arr = [];
                    if(item.has_child == true){
                        // item_arr.push(item);
                        $.each(item.children, function (key, child) {
                            child.address = item.address;
                            child.phone = item.phone;
                            child.bank = item.bank;
                            child.bank_account = item.bank_account;
                            child.invoice_content = item.invoice_content;
                            child.invoice_id = item.invoice_id;
                            child.money = item.money;
                            child.customer_company_name= item.customer_company_name;
                            child.salesman_name= item.salesman_name;
                            child.email = item.email;
                            child.invoice_type_name = item.invoice_type_name;//发票类型
                            child.invoice_company = item.invoice_company;
                            child.tax_number = item.tax_number;//税号
                            child.status_text = item.status_text;//发票状态
                            child.invoice_code= item.invoice_code;//发票号码
                            child.invoice_no= item.invoice_no;//发票代码
                            child.date= item.date,//开票日期

                            item_arr.push(child);
                        })
                    }else {
                        item_arr.push(item);
                    }
                    console.log(item_arr);

                    $.each(item_arr, function (key, val) {
                        sheetData.push({
                            item: '金融数据',
                            invoice_id: val.invoice_id,
                            customer_id: val.customer_id,
                            customer_name_0: val.customer_name,
                            customer_company_name: val.customer_company_name,

                            salesman_name: val.salesman_name,
                            email: val.email,//val.email,
                            dime: '',//发票维度


                            invoice_type: val.invoice_type_name,//发票类型
                            customer_name: val.customer_name,
                            deal_num: '',//充值金额/交易金额,
                            invoice_company: val.invoice_company,


                            tax_number: val.tax_number,//税号
                            address_and_phone: val.address + " " + val.phone,//地址 电话
                            bank_and_bank_account: val.bank + " " + val.bank_account,//开票行 开票账号
                            invoice_content: val.invoice_content,//开票内容
                            money: val.money,//开票金额

                            remark : val.remark,//业务备注
                            invoice_remark : val.invoice_remark,//发票备注
                            status_text: val.status_text,//发票状态


                            invoice_code: val.invoice_code,//发票号码
                            invoice_no: val.invoice_no,//发票代码
                            date: val.date,//开票日期
                            created_at: val.created_at,//申请时间
                            reject_content: '',//驳回原因
                            updated_at: val.updated_at,//最后操作时间
                            // express_no: val.express_no,//最后操作时间
                            op_name: val.salesman_name,//操作人员
                            product_name: val.product_name,//产品
                            month: val.month,//消耗月份
                            consume: val.consume,//消耗金额

                            // invoice_date : val.invoice_date,
                            // receipt_serial : val.receipt_serial,
                            // remit_money : val.receipt_serial !== '' ? val.consume : '',
                            // remit_date : val.remit_date,
                            // source_name : val.source_name,
                        });
                    });
                });

                let option = {};
                const currentTime = new Date().toLocaleString().replace(/[^\d]/g, '');
                option.fileName = '发票管理导出数据_'+currentTime;
                option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
                if(sheetData.length>0){
                    (new ExportJsonExcel(option)).saveExcel();
                }
                self.loading = false;
            },




            //导出 数据 已申请导出
            getApplyTableDataExcel: async function () {
                let self = this;
                self.loading = true;

                let para = this.get_para();
                para.limit = 1000000;
                para.page = 1;

                let t_data = await this.getData(para);

                let sheetHeader = [
                    "客户id","客户名","客户公司",
                    "项目","发票唯一流水号","销售人员","电子发票收件人邮箱","发票维度",
                    "发票类型","商户名称","充值金额/交易金额","开票公司名称",
                    "税号","地址 电话","开票行 开票账号","开票内容","开票金额",
                    "业务备注", "发票备注", "发票状态",
                    "发票号码","发票代码","开票日期","申请时间","驳回原因","最后操作时间","快递单号","操作人员"
                ];
                let sheetData = [];
                $.each(t_data.table_data, function (key, val){
                    sheetData.push({
                        customer_id : val.customer_id,
                        customer_name_0 : val.customer_name,
                        customer_company_name : val.customer_company_name,

                        item : '金融数据',
                        invoice_id : val.invoice_id,
                        salesman_name : val.salesman_name,
                        email : val.email,//val.email,
                        dime : '',//发票维度


                        invoice_type : val.invoice_type_name,//发票类型
                        customer_name : val.customer_name,
                        deal_num : '',//充值金额/交易金额,
                        invoice_company:val.invoice_company,


                        tax_number : val.tax_number,//税号
                        address_and_phone : val.address + " " + val.phone,//地址 电话
                        bank_and_bank_account : val.bank + " " + val.bank_account,//开票行 开票账号
                        invoice_content : val.invoice_content,//开票内容
                        money : val.money,//开票金额

                        remark : val.remark,//业务备注
                        invoice_remark : val.invoice_remark,//发票备注
                        status_text : val.status_text,//发票状态


                        invoice_code : val.invoice_code,//发票号码
                        invoice_no : val.invoice_no,//发票代码
                        date : val.date,//开票日期
                        created_at : val.created_at,//申请时间
                        reject_content : '',//驳回原因
                        updated_at : val.updated_at,//最后操作时间
                        express_no : val.express_no,//最后操作时间
                        op_name : val.salesman_name,//操作人员

                        // invoice_date : val.invoice_date,
                        // receipt_serial : val.receipt_serial,
                        // remit_money : val.receipt_serial !== '' ? val.consume : '',
                        // remit_date : val.remit_date,
                        // source_name : val.source_name,
                    })
                });

                let option = {};
                const currentTime = new Date().toLocaleString().replace(/[^\d]/g, '');
                option.fileName = '发票管理导出数据_'+currentTime;
                option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
                if(sheetData.length>0){
                    (new ExportJsonExcel(option)).saveExcel();
                }
                self.loading = false;
            },
            //提交开票申请
            applyInvoice:function(){
                let self = this;
                self.didd_loading = true;
                //页面校验

                //获取数据
                let paras = self.didd.apply_data;
                paras.payment_type = self.didd.current_tab;
                paras.user_cookie = user_cookie;
                if(paras.payment_type == 'alr' && paras.source > 0){
                    //后付费使用混合开票模式
                    paras.invoice_model = 10;
                }
                axios.post(self.urls.apply, paras).then(function (response) {
                    if (response.data.status === 0) {
                        // 提示
                        successMsg("申请成功");
                        //清除弹窗数据

                        //取消弹窗
                        self.cancle_apply_dialog();
                        //刷新页面数据
                        self.getTableData();
                        self.didd.s_form.source = 0;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.ok_confirm = false;
                    self.didd_loading = false;
                }).catch(function (error) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    errorMsg(error);
                });
            },
            //获取待申请列表数据
            get_ddid_data: function () {
                let self = this;
                self.didd_loading = true;

                let customer = self.didd.s_form.customer;
                let payment_type = self.didd.s_form.payment_type;
                let create_at = self.didd.s_form.create_at;
                let source = self.didd.s_form.source;
                let product = self.didd.s_form.product;

                let paras = {
                    limit: self.didd.page_size,
                    page: self.didd.current_page,
                    user_cookie: user_cookie,
                };
                if (customer) {
                    paras.customer = customer;
                }
                if (payment_type) {
                    paras.payment_type = payment_type;
                }
                if (product) {
                    paras.product = product;
                }
                if(effective_source.indexOf(source) !== -1){
                    paras.source = source;
                }
                //Element UI 时间组件返回的是毫秒时间戳,需要处理为Unix时间戳
                if (create_at && !isNaN(create_at[0]) && !isNaN(create_at[1])) {
                    let time = [];
                    time[0] = (create_at[0] - create_at[0]%1000)/1000;
                    time[1] = (create_at[1] - create_at[1]%1000)/1000;
                    paras.create_at = time;
                }

                axios.post(self.urls.unapplied_list, paras).then(function (response) {
                    if (response.data.status === 0) {
                        self.didd.total_num = response.data.data.count;

                        let data_list = response.data.data.list;
                        for (let i = 0; i < data_list.length; i++) {
                            data_list[i]['edit_status'] = false;//金额编辑状态
                            data_list[i]['origin_cost_money'] = data_list[i]['cost_money'];//原始金额
                        }
                        //如果需要edit_status是响应式的 需要在赋值给vue之前添加到data_list中
                        self.didd.for_apply_items = data_list;

                        self.didd.filters_customer = [];
                        self.didd.filters_product = [];
                        //表格筛选
                        let customer_hash = {};
                        let product_hash = {};
                        for (let i = 0; i < self.didd.for_apply_items.length; i++){
                            if(!(self.didd.for_apply_items[i]['customer_id'] in customer_hash)){
                                let customer_item = {text: self.didd.for_apply_items[i]['customer_name'], value:  self.didd.for_apply_items[i]['customer_id']};
                                customer_hash[self.didd.for_apply_items[i]['customer_id']] = self.didd.for_apply_items[i]['customer_name'];
                                self.didd.filters_customer.push(customer_item);
                            }

                            if(!(self.didd.for_apply_items[i]['product_id'] in product_hash)){
                                let product_item = {text: self.didd.for_apply_items[i]['product_name'], value:  self.didd.for_apply_items[i]['product_id']};
                                product_hash[self.didd.for_apply_items[i]['product_id']] = self.didd.for_apply_items[i]['product_name'];
                                self.didd.filters_product.push(product_item);
                            }
                        }

                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.ok_confirm = false;
                    self.didd_loading = false;
                }).catch(function (error) {
                    self.didd_loading = false;
                    self.ok_confirm = false;
                    this.didd.for_apply_items = [];
                    errorMsg(error);
                });
            },
            //申请弹窗切换标签
            switchTab: function () {
                this.didd_loading = true;
                //切换标签 清空地址信息 开票信息
                this.clearDialogData();
                let payment_type = this.payment_type_name_map[this.didd.current_tab];
                this.didd.s_form.source = 0;
                if(this.didd.current_tab == 'alr'){
                    this.didd.s_form.create_at = [];
                }
                // console.log('switchTab: ',payment_type);
                this.get_salesman_customers(payment_type);
            },
            // 预付费根据来源获取客户列表
            get_pre_customers:function(){
                let payment_type = this.payment_type_name_map[this.didd.current_tab];
                this.didd.s_form.source = this.didd.apply_data.source;
                this.didd.apply_data.customer_id = '';
                this.didd.receipt_serial_list = [];
                this.didd.apply_data.money = 0;
                this.didd.contract_no_url_map = {};
                this.didd.contract_list = [];

                if(this.didd.apply_data.source > 0){
                    this.didd.apply_data.invoice_model = 6;
                }

                this.get_salesman_customers(payment_type);
            },
            get_salesman_customers:function(payment_type){
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    payment_type: payment_type,
                    source: self.didd.s_form.source,
                    //后付费不需要这个 默认为0,预付费必传
                    model: self.didd.apply_data.invoice_model
                };
                //更新客户列表为相应付费类型
                axios.post(self.urls.customer_list, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.didd.customer_list = response.data.data;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.didd_loading = false;
                }).catch(function (error) {
                    self.didd_loading = false;
                    errorMsg(error);
                });
            },

            //后付费 点击行选中该行
            alrRowClick: function (row, column, event) {
                let refsElTable = this.$refs.alr_table; // 获取表格对象
                if(row.edit_status){
                    return false;
                }
                refsElTable.toggleRowSelection(row); // 调用选中行方法
            },
            //后付费 更改选中项时调用
            alrChange:function(selection){
                //选中条数
                this.didd.selection_data.count = selection.length;
                //选中金额
                // this.didd.selection_data.sum = 0;
                this.didd.selection_data.sum = Big(0);
                this.didd.apply_data.customer_cost_list = [];
                for (let i = 0; i < selection.length; i++) {
                    // console.log(selection[i]);
                    // this.didd.selection_data.sum += parseFloat(selection[i]['cost_money']);
                    this.didd.selection_data.sum = this.didd.selection_data.sum.add(Big(selection[i]['cost_money']));
                    let item = {
                        customer_consume_id: selection[i]['id'],//消耗表id
                        customer_id        : selection[i]['customer_id'],
                        product_id         : selection[i]['product_id'],
                        month_date         : selection[i]['month_date'],
                        cost_money         : selection[i]['cost_money'],
                        origin_cost_money  : selection[i]['origin_cost_money'],
                        model              : this.didd.customer_list[selection[i]['customer_id']]['invoice_model'],
                    };
                    this.didd.apply_data.customer_cost_list.push(item);
                }
                // this.didd.selection_data.sum = this.didd.selection_data.sum.toFixed(2);
                this.didd.selection_data.sum = Big(this.didd.selection_data.sum).toFixed(2);
                this.didd.apply_data.money = this.didd.selection_data.sum;
            },

            //预付费 更改选中项时调用
            preChange:function(currentRow){
                console.log(typeof currentRow);
                console.log(currentRow);
                //选中条数

                //选中金额
                this.didd.apply_data.money = Big(0);
                this.didd.apply_data.money = Big(currentRow.money).toFixed(2);
                //选中收款单
                this.didd.apply_data.receipt_serial = currentRow.receipt_serial;
            },


            //获取选择的来源
            diddGetSource:function(source){
                this.didd.apply_data.source = source;
                this.didd.s_form.customer = '';
                this.didd.for_apply_items = [];

                //刷新客户列表
                //非羽乐科技的需要全部对应渠道的
                let payment_type = this.payment_type_name_map[this.didd.current_tab];//获取付费模式
                this.get_salesman_customers(payment_type);
            },

            //获取或付费选中客户的开票模式 羽乐科技
            diddUpdateSelectedCustomerInvoiceModel:function(slected_customer){
                this.didd.apply_data.invoice_model = this.didd.customer_list[slected_customer]['invoice_model'];
                //切换客户时清空待开票消耗列表
                this.didd.for_apply_items = [];
            },


            //展示编辑金额输入框
            alrShowEditMoney:function(item){
                item.edit_status = true;
            },

            //编辑金额
            alrEditMoney:function(item){
                if(!(!isNaN(parseFloat(item.cost_money)) && isFinite(item.cost_money))){
                    errorMsg("请输入一个数字!");
                    item.cost_money = item.origin_cost_money;
                    return false;
                }

                if(0 == item.cost_money){
                    errorMsg("修改金额不可为0!");
                    item.cost_money = item.origin_cost_money;
                    return false;
                }

                if(item.origin_cost_money > 0 && item.cost_money > item.origin_cost_money){
                    errorMsg("修改金额不可超过原始金额!");
                    item.cost_money = item.origin_cost_money;
                    return false;
                }

                if(item.origin_cost_money < 0 && item.cost_money < item.origin_cost_money){
                    errorMsg("修改金额不可小于原始金额!");
                    item.cost_money = item.origin_cost_money;
                    return false;
                }

                item.edit_status = false;

                // 获取表格对象选中
                let selection = this.$refs.alr_table.selection;
                //更新开票金额
                this.alrChange(selection);
                return true;
            },

            //点击行展示详情
            // showDetailDialog:function(row, column, event){
            //     let refsElTable = this.$refs.main_table;
            //     if(row.status < 10){
            //         return;
            //     }
            //     //打开详情弹窗
            //     console.log(row, column);
            //     this.invoice_info_visible = true;
            //
            //
            //
            //
            // },
            cancle_info_dialog:function(){
                this.invoice_info = [];
                this.invoice_info_visible = false;
            },
            invoiceChange:function(selection){
                this.batch_pass_invoice_ids = [];
                for (let i = 0; i < selection.length; i++) {
                    let invoice_id = selection[i]['invoice_id'];
                    this.batch_pass_invoice_ids[i] = invoice_id;
                }
            },
            updateStatus: function (invoice_info,action) {
                // this.showDetailDialog();
                //打开详情弹窗
                let self = this;
                console.log(invoice_info,action);
                switch (action) {
                    case 'view': //查看发票详情
                        self.info_loading = true;
                        self.invoice_info_visible = true;
                        self.invoice_info = [];
                        axios.post(self.urls.invoice_info, {user_cookie: user_cookie,invoice_id: invoice_info.invoice_id}).then(function (response) {
                            if (response.data.status === 0) {
                                self.invoice_info = response.data.data;
                                self.invoice_info['copy_text'] =
                                    '金融数据' + "\t" +
                                    self.invoice_info.invoice_id + "\t" +
                                    self.invoice_info.salesman_name + "\t" +
                                    self.invoice_info.email + "\t" +
                                    "" + "\t" +
                                    self.invoice_info.invoice_type_name + "\t" +
                                    self.invoice_info.customer_name + "\t" +
                                    "" + "\t" +
                                    // self.invoice_info.company_name + "\t" +
                                    self.invoice_info.invoice_company + '\t' +
                                    self.invoice_info.tax_number + '\t' +
                                    self.invoice_info.bank + ' ' + self.invoice_info.bank_account + '\t' +
                                    self.invoice_info.address + ' ' + self.invoice_info.phone + '\t' +
                                    self.invoice_info.invoice_content + '\t' +
                                    self.invoice_info.money + '\t' +
                                    self.invoice_info.remark + '\t' +
                                    self.invoice_info.status_text + '\t' +
                                    "" + "\t" +
                                    "" + "\t" +
                                    "" + "\t" +
                                    self.invoice_info.created_at + '\t' +
                                    "" + "\t" +
                                    self.invoice_info.updated_at + '\t' +
                                    self.invoice_info.salesman_name;
                            } else {
                                errorMsg(response.data.msg);
                            }
                            self.info_loading = false;
                        }).catch(function (error) {
                            self.info_loading = false;
                            errorMsg(error);
                        });
                        break;
                    case 'reject': //驳回
                        this.invoice_reject(invoice_info.invoice_id);
                        break;
                    case 'pass': //审核通过
                        this.invoice_pass(invoice_info.invoice_id);
                        break;
                    case 'cancel': //撤销
                        this.invoice_cancel(invoice_info.invoice_id);
                        break;
                    case 'copy': //复制信息
                        break;
                    // case 'express':
                    //     this.show_add_express_dialog(invoice_info.invoice_id,invoice_info.customer_name,invoice_info.express_no);
                    //     break;
                    // case 'void': //作废
                    //     this.invoice_void(invoice_info.invoice_id);
                    //     break;
                    case 'flush': //作废
                        this.invoice_flush_dialog(invoice_info);
                        break;
                    default:
                        console.log('updateStatus:default');
                        break;
                }
            },
            invoice_pass:function(invoice_id){
                let self = this;
                if( self.invoice_info_visible === true ){
                    self.info_loading = true;
                }else{
                    self.loading = true;
                }
                axios.post(self.urls.pass, {user_cookie: user_cookie,invoice_id: invoice_id}).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("操作成功");
                    } else {
                        errorMsg(response.data.msg);
                    }
                    if( self.invoice_info_visible === true ){
                        self.info_loading = false;
                        self.invoice_info_visible = false; //关闭详情弹窗
                    }else{
                        self.loading = false;
                    }
                    //刷新列表
                    self.getTableData();
                }).catch(function (error) {
                    self.info_loading = false;
                    errorMsg(error);
                });
            },
            //驳回
            invoice_reject:function(invoice_id){
                this.$confirm('确认要进行驳回么!!!', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let self = this;
                    if (self.invoice_info_visible === true) {
                        self.info_loading = true;
                    } else {
                        self.loading = true;
                    }
                    axios.post(self.urls.reject, {user_cookie: user_cookie, invoice_id: invoice_id}).then(function (response) {
                        if (response.data.status === 0) {
                            successMsg("操作成功");
                        } else {
                            errorMsg(response.data.msg);
                        }
                        if (self.invoice_info_visible === true) {
                            self.info_loading = false;
                            self.invoice_info_visible = false; //关闭详情弹窗
                        } else {
                            self.loading = false;
                        }
                        //刷新列表
                        self.getTableData();
                    }).catch(function (error) {
                        self.info_loading = false;
                        errorMsg(error);
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消'
                    });
                });
            },
            invoice_cancel:function(invoice_id){
                let self = this;
                if( self.invoice_info_visible === true ){
                    self.info_loading = true;
                }else{
                    self.loading = true;
                }
                axios.post(self.urls.cancel, {user_cookie: user_cookie,invoice_id: invoice_id}).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("操作成功");
                    } else {
                        errorMsg(response.data.msg);
                    }
                    if( self.invoice_info_visible === true ){
                        self.info_loading = false;
                        self.invoice_info_visible = false; //关闭详情弹窗
                    }else{
                        self.loading = false;
                    }
                    //刷新列表
                    self.getTableData();
                }).catch(function (error) {
                    self.info_loading = false;
                    errorMsg(error);
                });
            },
            invoice_flush_dialog:function(invoice_info){
                // console.log('invoice_flush_dialog');
                this.ifd.dialog_visible = true;
                this.ifd.invoice_model = invoice_info.invoice_model;
                this.ifd.invoice_id = invoice_info.invoice_id;
                this.ifd.flush_type = (invoice_info.invoice_model == 5 || invoice_info.invoice_model == 6) ? 'part' : 'all';
                this.ifd.remark = '';
                // console.log(this.ifd);
            },
            //invoice_flush 红冲
            invoice_flush:function(){
                let self = this;
                self.loading = true;
                if(self.ifd.remark == '') {
                    errorMsg('请填写业务备注！');
                    self.loading = false;
                    return;
                }

                this.$confirm('确认要进行红冲么!!!', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let para = {
                        user_cookie: user_cookie,
                        invoice_id: self.ifd.invoice_id,
                        flush_type: self.ifd.flush_type,
                        flush_money: self.ifd.flush_money,
                        remark: self.ifd.remark,
                    };
                    axios.post(self.urls.flush,para).then(function (response) {
                        if (response.data.status === 0) {
                            successMsg("操作成功");
                        } else {
                            errorMsg(response.data.msg);
                        }

                        self.loading = false;
                        self.ifd.dialog_visible = false;
                        self.invoice_info_visible = false; //关闭详情弹窗

                        //刷新列表
                        self.getTableData();
                    }).catch(function (error) {
                        self.info_loading = false;
                        errorMsg(error);
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            //作废
            invoice_void:function(invoice_id){
                return;


                this.$confirm('确认要进行作废么!!!', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let self = this;
                    if (self.invoice_info_visible === true) {
                        self.info_loading = true;
                    } else {
                        self.loading = true;
                    }
                    axios.post(self.urls.void, {user_cookie: user_cookie, invoice_id: invoice_id}).then(function (response) {
                        if (response.data.status === 0) {
                            successMsg("操作成功");
                        } else {
                            errorMsg(response.data.msg);
                        }
                        if (self.invoice_info_visible === true) {
                            self.info_loading = false;
                            self.invoice_info_visible = false; //关闭详情弹窗
                        } else {
                            self.loading = false;
                        }
                        //刷新列表
                        self.getTableData();
                    }).catch(function (error) {
                        self.info_loading = false;
                        errorMsg(error);
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            show_add_express_dialog:function(invoice_id, customer_name, express_no){
                this.ded.dialog_title   = '填写快递单号';
                this.ded.dialog_title   = customer_name + ": " + invoice_id + " " + this.ded.dialog_title;
                this.ded.dialog_visible = true;
                this.ded.invoice_id     = invoice_id;
                this.ded.express_no     = express_no;
            },
            cancle_add_express_dialog:function(invoice_id){
                this.ded.dialog_visible = false;
            },
            add_express_no:function(){
                let self = this;
                let express_no = self.ded.express_no;
                let invoice_id = self.ded.invoice_id;
                self.ded.loading = true;

                axios.post(self.urls.add_express_no, {user_cookie: user_cookie,invoice_id: invoice_id,express_no: express_no}).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("操作成功");
                        self.ded.dialog_visible = false; //关闭详情弹窗
                        self.getTableData();
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.ded.loading = false;
                }).catch(function (error) {
                    self.ded.loading = false;
                    errorMsg(error);
                });
            },



            //展示开票导入数据弹窗
            import_source_data_dialog:function(){
                this.isd.dialog_visible = true;
            },

            //关闭开票导入数据弹窗
            cancle_import_source_data_dialog:function(){
                this.isd.dialog_visible = false;
                this.isd.string_data = '';
                this.isd.array_data = [];
                this.isd.duplicate_data = [];
            },

            //处理开票导入数据逻辑
            import_source_data:function(){
                this.isd.array_data = [];
                this.isd.duplicate_data = [];

                this.isd.array_data = this.isd.string_data.split('\n');
                this.isd.array_data  = this.isd.array_data.filter(item => item !== null && item !== undefined && item !== ''); //过滤空值

                // console.log("array_data:",this.isd.array_data);
                this.isd.duplicate_data = findDuplicates(this.isd.array_data);
                // console.log("duplicate_data:",this.isd.duplicate_data);


                let refsElTable = this.$refs.alr_table; // 获取表格对象

                let none_selection_nums = [];//未匹配金额数字

                // console.log('循环开始');
                for (let i = 0; i < this.isd.array_data.length; i++) {
                    let match = false;
                    let num = this.isd.array_data[i];
                    if (this.isd.duplicate_data.indexOf(num) !== -1) {
                        continue; // 跳过重复值
                    }

                    for (let j = 0; j < this.didd.for_apply_items.length; j++) {
                        let consume_money = this.didd.for_apply_items[j]['consume_money'];
                        //根据金额查询行 进行选中

                        //因为保留两位有效数字,这里乘100后取整在比较
                        let num_int = Math.floor(num * 100);
                        let consume_money_int = Math.floor(consume_money * 100);
                        if(consume_money_int == num_int){
                            match = true; //标记已经匹配
                            // 操作表格行进行选中
                            refsElTable.toggleRowSelection(this.didd.for_apply_items[j],true);
                            continue;
                        }
                    }
                    if(!match) {
                        none_selection_nums.push(num);
                    }
                }

                let duplicate_data = this.isd.duplicate_data;
                // console.log('重复数字:',duplicate_data);

                this.isd.dialog_visible = false;
                this.isd.string_data = '';
                this.isd.array_data = [];
                this.isd.duplicate_data = [];

                // 提示重复金额和未匹配金额
                if(none_selection_nums.length > 0 || duplicate_data.length > 0) {
                    if(duplicate_data.length == 0){
                        if(none_selection_nums.length > 0) {
                            this.$alert("下面的数字没有匹配到相同金额: " + none_selection_nums.join(","), '未匹配金额', {
                                confirmButtonText: '确定'
                            });
                        }
                    }else {
                        this.$alert("下面的金额在数据中重复,请手动处理: " + duplicate_data.join(","), '金额重复', {
                            confirmButtonText: '确定',
                            callback: action => {
                                if (none_selection_nums.length > 0) {
                                    this.$alert("下面的数字没有使用: " + none_selection_nums.join(","), '标题', {
                                        confirmButtonText: '确定'
                                    });
                                }
                            }
                        });
                    }
                }
            },


            batchPass:function(){
                let self = this;
                self.loading = true;

                if(self.batch_pass_invoice_ids.length === 0){
                    errorMsg('请勾选需要审批的发票!');
                    let t = setTimeout(() => {
                        self.loading = false;
                    }, 1000);

                    return;
                }

                axios.post(self.urls.batch_pass, {user_cookie: user_cookie,invoice_id: self.batch_pass_invoice_ids}).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("操作成功");
                    } else {
                        errorMsg(response.data.msg);
                    }

                    self.loading = false;
                    //刷新列表
                    self.getTableData();
                }).catch(function (error) {
                    self.info_loading = false;
                    errorMsg(error);
                });
            },

            //清空申请弹窗中的数据
            clearDialogData:function(){
                this.didd.for_apply_items = [];
                this.didd.receipt_serial_list = [];
                this.didd.customer_invoice_information = {};
                this.didd.apply_data = {
                    need_bill:false, //是否需要对账单(盖章)
                    customer_id: '', //开票客户
                    invoice_type:'', //发票类型 专票 普票
                    remark:'', //备注
                    money:'0.00', //开票金额
                    customer_cost_list: [], //开票产品 月份 {father_id: father_id, month_date: month_date}
                    customer_invoice_information_id:'', //开票信息id
                    source:0,
                    invoice_model:0
                };
            },

            //获取客户的开票公司列表
            get_invoice_company_list: async function () {
                let self = this;
                self.loading = true;

                await axios.post(self.urls.invoice_company_list,{user_cookie: user_cookie}).then(function (response) {
                    if(response.data.status == 0) {
                        self.invoice_company_list = response.data.data;
                    }else{
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },



            //开票时辅助勾选金额
            before_auto_check_upload:function() {
                console.log('before_auto_check_upload');
                this.acd.source = this.didd.apply_data.source
                this.acd.month_range = this.didd.s_form.create_at;
                this.didd_loading = true;
                console.log(this.acd);
            },

            //开票时辅助勾选金额
            auto_check_upload_success: function (response) {
                // console.log('auto_check_upload_success ------');
                if(response.status != 0) {
                    errorMsg(response.msg);
                }
                // console.log(response);

                let check_data = response.data;

                let refsElTable = this.$refs.alr_table; // 获取表格对象
                refsElTable.clearSelection();

                let none_selection_nums = [];//未匹配金额数字

                let mot_match_not_map = [];
                mot_match_not_map[1] = '没有找到客户';
                mot_match_not_map[2] = '没有找到产品';
                mot_match_not_map[3] = '没有找到对应月份';
                mot_match_not_map[4] = '金额不匹配';

                for (let i = 0; i < check_data.length; i++) {
                    let match = false;
                    // console.log(check_data[i]);
                    let _customer_id = check_data[i].customer_id;
                    let _product_id = check_data[i].product_id;
                    let _month = parseInt(check_data[i].month);
                    let _money = check_data[i].money;
                    check_data[i]['not_match_note'] = '';
                    check_data[i]['not_match_level'] = 0;
                    check_data[i]['table_money'] = check_data[i]['money'];
                    check_data[i]['consume_money'] = check_data[i]['money'];
                    check_data[i]['diff_money'] = 0;
                    // console.log(_customer_id, _product_id, _month, _money);

                    //因为保留两位有效数字,这里乘100后取整在比较
                    let num_int = Math.floor(_money * 100);
                    for (let j = 0; j < this.didd.for_apply_items.length; j++) {
                        // console.log(this.didd.for_apply_items[j]);
                        let consume_money = this.didd.for_apply_items[j]['consume_balance'];
                        let j_customer_id = this.didd.for_apply_items[j]['customer_id'];
                        let j_product_id = this.didd.for_apply_items[j]['product_id'];
                        let j_month_date = this.didd.for_apply_items[j]['consume_month'];
                        let consume_money_int = Math.floor(consume_money * 100);
                        //根据金额查询行 进行选中


                        if (j_customer_id == _customer_id) {
                            if (j_product_id == _product_id) {
                                if (j_month_date == _month) {
                                    if (consume_money_int == num_int) {
                                        match = true; //标记已经匹配
                                        // 操作表格行进行选中
                                        refsElTable.toggleRowSelection(this.didd.for_apply_items[j], true);
                                        continue;
                                    } else {
                                        //如果金额不同, 判断是否可以修改金额
                                        // this.didd.for_apply_items[j].cost_money = _money;
                                        // console.log('original_money:',this.didd.for_apply_items[j].origin_cost_money);
                                        // let select_edit_money = this.alrEditMoney(this.didd.for_apply_items[j]);
                                        // console.log('edit_status:',this.didd.for_apply_items[j].edit_status);

                                        if(this.didd.for_apply_items[j].edit_status) {
                                            this.didd.for_apply_items[j].cost_money = _money;
                                            this.alrEditMoney(this.didd.for_apply_items[j]);
                                            refsElTable.toggleRowSelection(this.didd.for_apply_items[j], true);
                                            match = true; //标记已经匹配
                                            continue;
                                        }else{
                                            this.didd.for_apply_items[j].cost_money = this.didd.for_apply_items[j].origin_cost_money;

                                            if (check_data[i]['not_match_level'] < 4) {
                                                check_data[i]['not_match_level'] = 4;
                                                check_data[i]['consume_money'] = consume_money;
                                                check_data[i]['diff_money'] = (num_int - consume_money_int) / 100;
                                            }
                                        }
                                    }
                                } else {
                                    if (check_data[i]['not_match_level'] < 3) {
                                        check_data[i]['not_match_level'] = 3;
                                    }
                                }
                            } else {
                                if(check_data[i]['not_match_level'] < 2) {
                                    check_data[i]['not_match_level'] = 2;
                                }
                            }
                        } else {
                            if (check_data[i]['not_match_level'] < 1) {
                                check_data[i]['not_match_level'] = 1;
                            }
                        }
                    }
                    if (!match) {
                        check_data[i]['not_match_note'] = mot_match_not_map[check_data[i]['not_match_level']];
                        none_selection_nums.push(check_data[i]);
                    }
                }


                this.didd_loading = false;
                this.acd.dialog_visible = false;

                // 提示重复金额和未匹配金额
                if(none_selection_nums.length > 0) {
                    // console.log('下面的数字没有匹配到相同金额');
                    // console.log(none_selection_nums);
                    this.acd.none_match_data = none_selection_nums;
                    this.acd.dialog_visible = true;
                }
            },


            successFunc: function(e) {
                // console.info("Action:", e.action);
                // console.info("Text:", e.text);
                // console.info("Trigger:", e.trigger);
                // 可以取到目标元素上的自定义属性（可以据此再做一些处理）
                // e.trigger.dataset.test && console.log(e.trigger.dataset.test)
                // 清除选中状态
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc:function(e) {
                // console.error("Action:", e.action);
                // console.error("Trigger:", e.trigger);

                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    //查询数组中的重复值 返回一个数组包含重复值
    function findDuplicates(arr) {
        return [...new Set(arr.filter(item => arr.indexOf(item) !== arr.lastIndexOf(item)))];
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>