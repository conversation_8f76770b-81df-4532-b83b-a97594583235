<!DOCTYPE html>
<html>
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    -->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
        .index-btn {
            margin:5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container">
    <?php $father_id = I('get.father_id'); ?>
    <div class="panel panel-default">
        <div class="panel-body">
            <form action="{:U('index', ['father_id' => $father_id])}" method="get" class="form-inline">
                <div class="form-group">
                    <label for="product_name">产品名称：</label>
                    <input type="text" name="product_name" id="product_name" class="form-control"
                           value="<?= (isset($request['product_name']) && $request['product_name']) ? $request['product_name'] : '';?>">
                </div>

                <div class="form-group">
                    <label for="submit_btn"></label>
                    <input type="submit" id="submit_btn" class="btn btn-primary btn-sm" value="查询">
                </div>

                <div class="form-group">
                    <a href="/Account/Product/add" class="btn btn-info btn-sm">添加产品</a>
                </div>

                <notempty name="father_id">
                    <div class="form-group">
                        <a href="{:U('index')}" class="btn btn-primary btn-sm">返回上级</a>
                    </div>
                </notempty>
            </form>
        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered">
            <thead>
            <tr>
                <th>产品编号</th>
                <th>产品名称</th>
                <th width="300">产品key</th>
                <th>备注</th>
                <th width="240">Time</th>
                <th>操作人</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <volist name="list_product" id="product">
                <tr>
                    <td>{$product.product_id}</td>
                    <td>
                        <if condition="empty($father_id) && ($product['children_count']!=0)">
                            <a href="{:U('index', ['father_id' => $product['product_id']])}">{$product.product_name}</a>
                            <else />
                            {$product.product_name}
                        </if>
                    </td>
                    <td style="word-break: break-all">{$product.product_key}</td>
                    <td><?= $product['mark']; ?></td>
                    <td class="time_span">
                        <span>创建: <?= date('Y-m-d H:i:s', $product['create_at']) ?></span><br>
                        <span>更新: <?= date('Y-m-d H:i:s', $product['update_at']) ?></span>
                    </td>
                    <td style="word-break: break-all"><?= $product['admin']; ?></td>
                    <td>
                        <a href="{:U('edit')}?product_id={$product.product_id}" class="btn btn-info btn-sm">编辑</a>
                    </td>
                </tr>
            </volist>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
