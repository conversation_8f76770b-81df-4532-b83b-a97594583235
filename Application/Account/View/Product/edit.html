<!DOCTYPE html>
<html>
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>
    -->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <style>
        textarea {
            width:100%;
            height : 240px !important;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="update_account_app">
    <dialog_template></dialog_template>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <div class="container">
        <a href="/Account/Product/index" class="btn btn-primary btn-sm" style="float: right;margin:5px 0;">返回产品列表</a>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <form action="{:U('edit')}" method="post" class="form-horizontal" id="form_product">
                            <div class="form-group">
                                <label for="product_id" class="control-label col-md-3">产品编号：</label>
                                <div class="col-md-4">
                                    <input type="number" name="product_id" id="product_id" class="form-control" value="{$product.product_id}" readonly>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="product_name" class="control-label col-md-3">产品名称：</label>
                                <div class="col-md-4">
                                    <input type="text" name="product_name" id="product_name" class="form-control" value="{$product.product_name}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="product_key" class="control-label col-md-3">产品key：</label>
                                <div class="col-md-4">
                                    <input type="text" name="product_key" id="product_key" class="form-control" value="{$product.product_key}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="father_id" class="control-label col-md-3">所属产品：</label>
                                <div class="col-md-4">
                                    <select name="father_id" id="father_id" class="form-control">
                                        <if condition="$product['father_id']!=0">
                                            <option value="{$product.father_id}">{$productData[$product['father_id']].product_name}</option>
                                        </if>
                                        <option value="0">一级产品</option>
                                        <volist name="productData" id="vo">
                                            <option value="{$vo.product_id}">{$vo.product_name}</option>
                                        </volist>
                                    </select>
                                    <notempty name="$vo['children_count']">
                                        <span style="color:red;line-height:18px;">*不可建立与其他产品的所属关系</span>
                                    </notempty>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">后台展示状态：</label>
                                <div class="col-md-4">
                                    <label class="checkbox-inline">
                                        <input type="radio" name="back_status" value="1" {$product['back_status']?'checked':''} />
                                        展示
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="radio" name="back_status" value="0" {$product['back_status']?'':'checked'} />
                                        不展示
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">在表单查询是否展示：</label>
                                <div class="col-md-4">
                                    <label class="checkbox-inline">
                                        <input type="radio" name="search_show" value="1" <if condition="$product['search_show'] eq 1">checked<else /> </if> />
                                        展示
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="radio" name="search_show" value="-1" <if condition="$product['search_show'] eq -1">checked<else /> </if> />
                                        不展示
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="data" class="control-label col-md-3">产品配置参数：</label>
                                <div class="col-md-4">
                                    <textarea id="data" name="data" class="form-control">{$product.data}</textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="stat_config" class="control-label col-md-3">客户统计配置：</label>
                                <div class="col-md-4">
                                    <textarea id="stat_config" name="stat_config" class="form-control">{$product.stat_config}</textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="fee_config" class="control-label col-md-3">客户计费配置：</label>
                                <div class="col-md-4">
                                    <textarea id="fee_config" name="fee_config" class="form-control">{$product.fee_config}</textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="channel_stat" class="control-label col-md-3">渠道统计配置：</label>
                                <div class="col-md-4">
                                    <textarea id="channel_stat" name="channel_stat" class="form-control">{$product.channel_stat}</textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="channel_fee" class="control-label col-md-3">渠道计费配置：</label>
                                <div class="col-md-4">
                                    <textarea id="channel_fee" name="channel_fee" class="form-control">{$product.channel_fee}</textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="mark" class="control-label col-md-3">备注：</label>
                                <div class="col-md-4">
                                    <textarea id="mark" name="mark" class="form-control">{$product.mark}</textarea>
                                </div>
                            </div>
                            <div class="pull-right">
                                <input type="submit" class="btn btn-primary btn-sm" value="更新">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<script type="text/javascript">

    new Vue({
        el : '#update_account_app'
    });
    $("#father_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '所属产品',
        width: '200px'
    });
    $(function () {
        // 表单提交事件
        formSubmit();

    });

    // 返回上级菜单
    function goBack() {
        event.preventDefault();
        window.history.back();
    }

    // 表单提交事件
    function formSubmit() {
        $('#form_product').submit(function () {
            // 检查参数
            var result_check = checkParams();
            if (result_check === false) {
                return false;
            }

            // 发送表单
            event.preventDefault();
            formRequest($(this));
        });
    }

    // 提交表单
    function formRequest(that) {
        var data_request = $(that).serialize();
        var url_request = $(that).attr('action');
        var url_redirect = '/Account/Product/index';

        $.post(url_request, data_request).success(function (response) {
            if (response.status !== 'success') {
                modalExport(response.info);
                return '';
            }
            alert( '编辑产品成功');
            window.location.href = url_redirect;
        }).error(function (response) {
            modalExport('更新产品出错，请稍后重试');
            // 方便debug
            console.log(response.info);
            return '';
        });
    }

    // 检查参数
    function checkParams() {
        var product_id = $('#product_id').val().trim();
        var product_name = $('#product_name').val().trim();
        var data = $('#data').val().trim();
        if (!product_id) {
            modalExport('产品编号未填写');
            return false;
        }
        if (!product_name) {
            modalExport('产品名称未填写');
            return false;
        }
        if (!data) {
            modalExport('配置参数未填写');
            return false;
        }
        return true;
    }

</script>
</body>
</html>
