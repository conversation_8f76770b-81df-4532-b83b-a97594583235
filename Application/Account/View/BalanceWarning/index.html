<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客户余额预警列表</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm4 layui-col-md2 layui-col-lg2">
            <label class="layui-form-label">选择客户</label>
            <div class="layui-input-block">
                <select name="customer_id" lay-search id="customer_id"></select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm4 layui-col-md2 layui-col-lg2">
            <label class="layui-form-label">付款类型</label>
            <div class="layui-input-block">
                <select name="payment_type" id="payment_type">
                    <option value="">请选择</option>
                    <option value="1">预付款</option>
                    <option value="2">后付款</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>
        <div class="layui-form-item layui-col-md1 layui-col-lg1"></div>
    </form>
</div>


<div class="container">
    <div id="list_table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/dateFormat.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/jquery.dateFormat.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    let form = layui.form, layer = layui.layer;
    (function () {
        Request.get("{$Think.config.BACK_API_DOMAIN}/options/customer", {"status":1}).then(function (data) {
            $("#customer_id").html(`<option value="">--全部--</option>${data.data}`);
            layui.form.render('select');
        });
        // select下拉框选中触发事件
        form.on("select", function(data){
            window.cache.list_where['customer_id'] = $("#customer_id").val();
            window.cache.list_where['payment_type'] = $("#payment_type").val();
        });
    })();
    window.cache = {
        list_url         : "{$Think.config.BACK_API_DOMAIN}/balanceWarning/basicsList",
        list_order_field : "days",
        list_order_type  : "asc",
        list_where       : {},
        list_title       : `客户余额预警列表`,
        list_form        : false,
        list_fields      : [[{
            field : 'customer_name',
            title : '客户名',
            align : 'center',
        },{
            field : 'payment_type',
            title : '付款类型',
            align : 'center',
            sort: true,
            templet : function(d) {
                return d.payment_type === 1 ? '预付款' : '后付款';
            }
        },{
            field : 'days',
            title : '可用天数',
            align : 'center',
            sort: true
        },{
            field : 'today_income',
            title : '当日消费',
            align : 'center',
            sort: true
        },{
            field : 'this_month_income',
            title : '当月消费',
            align : 'center',
            sort: true
        },{
            field : 'balance',
            title : '余额',
            align : 'center',
            sort: true
        },{
            field : 'credit',
            title : '授信额度',
            align : 'center',
            sort: true
        },{
            field : 'credit_balance',
            title : '授信余额',
            align : 'center',
            sort: true
        },{
            field : 'surplus_credit',
            title : '剩余授信额度',
            align : 'center',
            width : '10%',
            sort: true
        },{
            field : 'update_time',
            title : '更新时间',
            align : 'center',
            width : '12%',
            sort: true
        },{
            field : 'ignore',
            title : '忽略报警',
            align : 'center',
            sort: true,
            templet : function(d) {
                if(d.ignore) {
                    return '<button type="button" class="layui-btn layui-btn-xs layui-btn-disabled" data-id="'+d['customer_id']+'">已忽略</button>'
                } else {
                    return '<button type="button" class="layui-btn layui-btn-xs ignore_today" data-id="'+d['customer_id']+'">今日忽略</button>'
                }
            }
        }]]
    };

    $(document).on("click", ".ignore_today", function() {
        let customer_id = $(this).data('id');
        Popup.confirm('确定要忽略今日该客户的报警吗？', function(){
            Request.post("{$Think.config.BACK_API_DOMAIN}/balanceWarning/warningIgnore", {"customer_id":customer_id})
            Popup.success('操作成功');
            getData();
        });
    });
    /**
     * 获取主数据
     */
    function getData(){
        //加载表格
        Table.reloadTable({
            where          : window.cache.list_where,
            page           : false,
            cellMinWidth   : undefined,
            parseData      : function (response) {
                return {
                    "code"  : response.status,
                    "msg"   : response.msg,
                    "count" : response.data.count,
                    "data"  : response.data.data
                };
            },
            size           : 'default',
            autoSort : true
        });
    }
    /**
     * 搜索按钮
     */
    $("#query").click(function () {getData();});
    getData();  //加载完页面后获取表格数据
</script>