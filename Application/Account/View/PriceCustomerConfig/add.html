<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>计费配置增加页</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__CSS__bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .container {
            margin     : 20px auto;
            width      : 96%;
        }

        #form_container {
            margin     : 20px auto;
            border     : 1px solid #EEEEEE;
            padding    : 10px;
            width      : 96%;
            min-height : 240px;
        }

        .children_product_title {
            width       : 100%;
            height      : 30px;
            line-height : 30px;
            font-size   : 14px;
            color       : #337AB7;
            font-weight : bolder;
        }

        .children_product_hr {
            margin-top    : 20px;
            margin-bottom : 10px;
            border        : 2px solid #999999;
        }

        .operator_left_offset {
            padding-left : 40px;
            width        : 100%;
            box-sizing   : border-box;
            margin-top   : 10px;
        }

        .operator_title {
            width       : 100%;
            height      : 30px;
            line-height : 30px;
            font-size   : 14px;
            color       : #337AB7;
            margin-top  : 20px;
        }

        .package_number_input {
            width      : 30px;
            height     : 24px;
            margin     : 0 5px;
            display    : inline-block;
            text-align : center;
            padding    : 0;
        }

        .package_input {
            padding : 6px 0;
            color   : #F15A34;
        }

        .package_item {
            wdith : 100%;
        }

        .price_input {
            max-width : 300px;
        }

        #saveButtonGrid {
            text-align : center;
            width      : 100%;
        }
    </style>
</head>
<body>

<div class="container" id="form_container">
    <a href="./index.html?customer_id={$Think.get.customer_id}" class="layui-btn">计费配置列表</a>
    <form class="form-horizontal" method="post" id="form">
        <div class="form-group">
            <label class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label" for="customer_name">
                客户名称：
            </label>
            <div class="col-xs-6 col-sm-4 col-md-4 col-log-3">
                <input type="text" class="form-control" readonly="" disabled="" value="{$data.customer_name}" id="customer_name"/>
                <input type="hidden" name="customer_id" id="customer_id"/>
                <input type="hidden" name="request_type" value="RUN_INSERT"/>
            </div>
        </div>

        <!--产品选择框容器-->
        <div id="productGrid"></div>

        <!--选择账号-->
        <div id="accountGrid"></div>

        <!--计费依据-->
        <div id="accordGrid"></div>

        <!--选择账号框的容器-->
        <div class="accountGrid"></div>

        <!--计费类型选择框容器-->
        <div id="methodsGrid"></div>

        <!--阶梯周期选择框容器-->
        <div id="periodGrid"></div>

        <!--运营商选择容器-->
        <div id="operatorGrid"></div>

        <!--多级产品计费模式（只在多级产品中出现）-->
        <div id="modeGrid"></div>

        <!-- 复制配置id -->
        <div class="form-group">
            <label class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label" for="copy_id">
                复制配置ID：
            </label>
            <div class="col-xs-6 col-sm-4 col-md-4 col-log-3">
                <input style="width:200px;display:inline-block;" type="input" name="copy_id" class="form-control" value="" id="copy_id" placeholder="默认为空,无需填写">
                <button style="height:34px;line-height:34px;width:70px" type="button" class="layui-btn copy_price_btn">复制</button>
            </div>
        </div>

        <!--价格-->
        <div id="priceGrid"></div>

        <!--其他参数-->
        <div id="paramGrid">
            <input type="hidden" name="is_multiple" value="0" class="is_multiple_input"/>
        </div>

        <div class="form-group">
            <label class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label" for="start_date">
                计费开始时间：
            </label>
            <div class="col-xs-6 col-sm-4 col-md-4 col-log-3">
                <input type="date" name="start_date" class="form-control" value="" id="start_date" required>
            </div>
        </div>

        <div class="form-group">
            <label class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label" for="remark">
                备注信息：
            </label>
            <div class="col-xs-6 col-sm-4 col-md-4 col-log-3">
                <textarea name="remark" id="remark" cols="30" rows="10" class="form-control"></textarea>
            </div>
        </div>

        <!--保存按钮-->
        <div id="saveButtonGrid"></div>

    </form>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script src="__JS__select2.full.min.js"></script>
<script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.4/layui/layui.all.js"></script>
<script type="application/javascript" src="__STATICS__js/common.js"></script>
<script type="application/javascript">
    //设置产品选择框
    function setProductInput() {
        let display = `<div class="form-group">
                <label for="product_id" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                    选择产品：
                </label>
                <div class="col-xs-6 col-sm-4 col-md-4 col-log-3">
                    <select name="product_id" id="product_id" class="form-control">
                        <option value=""></option>`;
        $.each(window.config, function (i, n) {
            display += `<option value="${n.product_id}">${n.product_name}</option>`;
        });
        display += `</select></div></div>`;

        $("#productGrid").html(display);
        $("#product_id").select2({
            allowClear  : true,
            theme       : "bootstrap",
            placeholder : '选择产品',
            width       : '200px'
        });

        //绑定产品选择框的事件
        $("#product_id").change(function () {
            let productId = $(this).val();
            //设置计费方式的选择框
            if ("" === String(productId)) {
                setMethodsInput();
                setAccountInput();
                setOperatorInput();
                setAccordInput();
                setModeInput();
            } else {
                setMethodsInput(productId);//设置计费方式选择框
                setAccountInput(productId);//选择账号
                setOperatorInput(productId);//设置选择是否区分运营商的选择框
                setAccordInput(productId);//设置计费依据选择框
                setModeInput(productId);//设置二级产品计费模式
                setParamInput(productId);//设置其他参数隐藏域
            }
        });
    }

    //设置其他参数隐藏域
    function setParamInput(productId = null) {
        let productConfig = window.config[productId].config;

        //是否为多级产品
        $(".is_multiple_input").val(productConfig.is_multiple);
    }

    //选择账号
    function setAccountInput(productId = null) {
        let display = ``;
        if (null !== productId) {
            let accountConfig = window.config[productId].config.account;
            display           = `<div class="form-group">
            <label for="type" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                选择账号：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">`;

            //如果是多级产品，则不支持多选账号
            if (1 !== Number(window.config[productId].config.is_multiple)) {
                //支持批量设置账号
                display += `<label class="checkbox-inline" style="padding-left: 0;"><input type="button" id="batch_account" class="btn btn-primary btn-sm" value="全选"></label>`;
                Object.keys(accountConfig).forEach(function (item) {
                    display += `<label class="checkbox-inline"><input type="checkbox" class="account_input" value="${item}" name="account_id[]">${accountConfig[item]}</label>`;
                });
            } else {
                Object.keys(accountConfig).forEach(function (item) {
                    display += `<label class="radio-inline"><input type="radio" class="account_input" value="${item}" name="account_id">${accountConfig[item]}</label>`;
                });
            }


            display += `</div></div>`;
        }
        $("#accountGrid").html(display);

        //设置点击全选按钮的事件
        $("#batch_account").click(function () {
            if ($('.account_input').eq(0).prop('checked')) {
                $('.account_input').prop('checked', false);
            } else {
                $('.account_input').prop('checked', true);
            }
            setPriceInput();
        });

        //绑定选择账号
        $(".account_input").click(function () {
            setPriceInput();
        });
    }


    //设置计费方式选择框
    function setMethodsInput(productId = null) {
        let display = ``;
        if (null !== productId) {
            let methodsConfig = window.config[productId].config.methods;
            display           = `<div class="form-group">
            <label for="type" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                计费方式：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">`;

            $.each(methodsConfig, function (i, n) {
                display += `<label class="radio-inline"><input type="radio" class="methods_radio" name="methods" value="${i}">${n}</label>`;
            });

            display += `</div></div>`;
        }

        $("#methodsGrid").html(display);

        //绑定计费方式选择框
        $('input[name="methods"]').change(function () {
            let type = $(this).val();
            setPeriodInput(productId, type);
            setPriceInput();
        });
    }

    //设置阶梯周期选择框
    function setPeriodInput(productId = null, methods = null) {
        let display = ``;
        if (null !== productId) {
            switch (Number(methods)) {
                case 1:
                    display += `<div class="form-group">
            <label for="type" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                包年周期：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">
                <input type="number" class="form-control" name="years" value="1" required placeholder="请输入年数" />
            </div>
        </div>`;
                    break;
                case 3:
                    display += `<div class="form-group">
            <label for="type" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                阶梯周期：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">
                <label class="radio-inline"><input type="radio" class="period_radio" name="period" value="1">日</label>
                <label class="radio-inline"><input type="radio" class="period_radio" name="period" value="2">月</label>
                <label class="radio-inline"><input type="radio" class="period_radio" name="period" value="3">年</label>
                <label class="radio-inline"><input type="radio" class="period_radio" name="period" value="4">无周期</label>
            </div>
        </div>`;
                    break;
                case 4:
                    display += `<div class="form-group">
            <label for="type" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                阶梯周期：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">
                <label class="radio-inline"><input type="radio" class="period_radio" name="period" value="1">日</label>
                <label class="radio-inline"><input type="radio" class="period_radio" name="period" value="2">月</label>
            </div>
        </div>`;
                    break;
            }
        }

        $("#periodGrid").html(display);

        $('input[name="period"]').change(function () {
            setPriceInput();
        });
    }

    //设置选择是否区分运营商的选择框
    function setOperatorInput(productId = null) {
        let display = ``;
        if (null !== productId) {
            let operatorConfig = config[productId].config.operator;
            if (0 !== Number(Object.keys(operatorConfig).length)) {
                display = `<div class="form-group">
            <label for="type" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                区分运营商：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">
                <label class="radio-inline"><input type="radio" class="operator_radio" name="diff_operator" value="0" checked>不区分</label>
                <label class="radio-inline"><input type="radio" class="operator_radio" name="diff_operator" value="1">区分</label>
            </div>
        </div>`;
            }
        }
        $("#operatorGrid").html(display);

        $(".operator_radio").change(function () {
            setPriceInput();
        });
    }

    //设置计费依据选择框
    function setAccordInput(productId) {
        let display = ``;
        if (null !== productId) {
            display = `<div class="form-group">
            <label for="accord" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                计费依据：
            </label>
            <div class="col-xs-6 col-sm-4 col-md-3 col-log-2">
                <select name="accord" id="accord" class="form-control">
                    <option value="1">成功调用量</option>
                    <option value="2">查得量</option>
                    <option value="3">计费量</option>
                </select>
            </div>
        </div>`;
        }

        $("#accordGrid").html(display);
    }

    //设置打包选择框
    function setModeInput(productId = null) {
        let display = ``;
        if (null !== productId) {
            if (1 === Number(window.config[productId].config.is_multiple)) {
                display = `<div class="form-group">
            <label for="mode" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                二级产品计费模式：
            </label>
            <div class="col-xs-6 col-sm-4 col-md-3 col-log-2">
                <label class="radio-inline"><input type="radio" class="mode_radio" name="mode" value="1" checked>独立子产品</label>
                <label class="radio-inline"><input type="radio" class="mode_radio" name="mode" value="2">打包计费</label>
                <label class="radio-inline"><input type="radio" class="mode_radio" name="mode" value="3">汇总子产品</label>
            </div>
        </div>`;
            }
        }

        $("#modeGrid").html(display);

        $(".mode_radio").change(function () {
            setPriceInput();
        });
    }

    function setPriceInput() {
        let priceHtml = getPriceInput();
        $("#priceGrid").html(priceHtml);

        if ('' !== String(priceHtml)) {
            $("#saveButtonGrid").html(`<input type="button" id="submit" class="btn btn-primary btn-sm" value="增加计费配置">`);
        }
    }

    //设置单价文本框
    function getPriceInput() {
        //是否存在产品元素
        if (0 === $("#product_id").length) {
            return '';
        }
        //选择的产品
        let productId = $("#product_id").val();
        if ('' === productId) {
            return '';
        }
        //产品配置
        let productConfig = window.config[productId].config;
        //是否已经选择了计费方式
        if (0 === $(".methods_radio:checked").length) {
            return '';
        }
        //计费方式
        let methods = $(".methods_radio:checked").val();

        //是否选择了账号
        if (0 === $(".account_input:checked").length) {
            return '';
        }

        //是否存在阶梯周期
        switch (Number(methods)) {
            case 1:
                if ('' === $('input[name="years"]').val()) {
                    return '';
                }
                break;
            case 3:
                if (0 === $(".period_radio:checked").length) {
                    return '';
                }
                break;
            case 4:
                if (0 === $(".period_radio:checked").length) {
                    return '';
                }
                break;
        }

        //是否为多级产品
        let isMultiple = productConfig.is_multiple;
        //多级产品计费模式
        let mode       = $(".mode_radio:checked").length > 0 ? $(".mode_radio:checked").val() : 0;


        let display = `<div class="form-group">
            <label for="price" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                单价：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">`;
        if (0 === Number(isMultiple)) {
            //当前产品计费
            display += getItemOperatorPriceInput(productId, methods, `[${productId}]`);
        } else if (1 === Number(mode)) {
            //独立子产品计费
            let accountId   = $(".account_input:checked").eq(0).val();
            let productList = productConfig.children_product[accountId];

            Object.keys(productList).forEach(function (item) {
                display += `<div class="children_product_title">${productList[item]}</div>`;
                display += getItemOperatorPriceInput(productId, methods, `[${item}]`);
                display += `<hr class="children_product_hr">`;
            });
        } else if (2 === Number(mode)) {
            //父产品-打包计费
            let accountId   = $(".account_input:checked").eq(0).val();
            let productList = productConfig.children_product[accountId];
            display += `<div class="package_item">
<div class="package_input">
当同时调用子产品个数>=<input type="text" class="form-control package_number_input" name="package_number[]" value="1" required />时的价格：
</div>`;
            display += getItemOperatorPriceInput(productId, methods, `[package_0]`);
            display += `<hr class="children_product_hr"></div>
<div class="package_operation">
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-primary btn-sm add_package" value="增加打包计费"></label>
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-warning btn-sm del_package" value="删除打包计费"></label>
</div>`;
        } else if (3 === Number(mode)) {
            //汇总子产品计费
            display += getItemOperatorPriceInput(productId, methods, `[${productId}]`);
        } else {
            display += getItemOperatorPriceInput(productId, methods, `[${productId}]`);
        }

        display += `</div></div>`;
        return display;
    }

    //获取每个运营商的单价模块
    function getItemOperatorPriceInput(productId, methods, postfixName = '') {
        //是否区分运营商
        let operator = $(".operator_radio:checked").length > 0 ? $(".operator_radio:checked").val() : 0;
        console.log(operator);
        if (1 === Number(operator)) {
            let operatorConfig = window.config[productId].config.operator;
            let display        = ``;
            console.log(operatorConfig);
            Object.keys(operatorConfig).forEach(function (item) {
                display += `<div class="operator_left_offset"><div class="operator_title">${operatorConfig[item]}</div>`;
                display += getItemPriceInput(methods, postfixName + `[${item}]`);
                display += `</div>`;
            });
            return display;
        } else {
            return getItemPriceInput(methods, postfixName);
        }
    }

    //获取单元单价模块
    function getItemPriceInput(methods, name) {
        let display = ``;
        switch (Number(methods)) {
            case 0:
                return `<input type="text" value="0" name="price${name}" readonly class="form-control" />`;
                break;
            case 1:
                return `<input type="text" class="form-control price_input" name="price${name}" required placeholder="请输入价格" required />`;
                break;
            case 2:
                return `<input type="text" class="form-control price_input" name="price${name}" required placeholder="请输入单价" required />`;
                break;
            case 3:
                return `<div class="table-responsive">
<table class="table table-hover table-bordered" border="0" cellpadding="0" cellspacing="0">
    <thead>
        <tr><th>阶梯阈值</th><th>阶梯单价</th></tr>
    </thead>
    <tbody>
        <tr>
            <td><input type="number" class="form-control ladder_input" name="ladder${name}[]" required placeholder="请输入当前阶梯阈值" value="1" /></td>
            <td><input type="text" class="form-control price_input" name="price${name}[]" required placeholder="请输入当前阶梯单价" /></td>
        </tr>
    </tbody>
</table>
</div>
<div class="ladder_operation">
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-primary btn-sm add_ladder" value="增加阶梯"></label>
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-warning btn-sm del_ladder" value="删除阶梯"></label>
</div>
`;
                break;
            case 4:
                return `<div class="table-responsive">
<table class="table table-hover table-bordered" border="0" cellpadding="0" cellspacing="0">
    <thead>
        <tr><th>阶梯阈值</th><th>阶梯单价</th></tr>
    </thead>
    <tbody>
        <tr>
            <td><input type="text" class="form-control ladder_input" name="ladder${name}[]" required placeholder="请输入当前阶梯阈值" value="1"></td>
            <td><input type="text" class="form-control price_input" name="price${name}[]" required placeholder="请输入当前阶梯单价" /></td>
        </tr>
    </tbody>
</table>
</div>
<div class="ladder_operation">
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-primary btn-sm add_ladder" value="增加阶梯"></label>
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-warning btn-sm del_ladder" value="删除阶梯"></label>
</div>
`;
                break;
        }
    }

    //绑定增加阶梯
    function bindAddLadderEvent() {
        $("#priceGrid").on('click', '.add_ladder', function () {
            let tbody = $(this).parent().parent().prev().find('.table').find('tbody');
            let tr    = tbody.find('tr').last().clone(false);
            tbody.append(tr);
            tbody.find('tr').last().find('input').val('');
        });
    }

    //绑定删除阶梯事件
    function bindDelLadderEvent() {
        $("#priceGrid").on('click', '.del_ladder', function () {
            let tbody = $(this).parent().parent().prev().find('.table').find('tbody');
            console.log(tbody);
            if (tbody.find('tr').length > 1) {
                tbody.find('tr').last().remove();
            }
        });
    }

    //绑定增加打包计费事件
    function bindAddPackageEvent() {
        $("#priceGrid").on('click', '.add_package', function () {
            $(this).parent().parent().before($(this).parent().parent().parent().find('.package_item').last().clone());
            $(this).parent().parent().parent().find('.package_item').last().find('.price_input').val('');
            $(this).parent().parent().parent().find('.package_item').last().find('.package_number_input').val('');

            //替换价格表单域的name值
            let priceInput = $(this).parent().parent().parent().find('.package_item').last().find('.price_input');
            let index      = $(this).parent().parent().parent().find('.package_item').length - 1;
            priceInput.each(function () {
                let name = $(this).attr('name');
                name     = name.replace(/package_\d+/, 'package_' + index);
                $(this).attr('name', name);
            });

//            let price_input_name = $(this).parent().parent().parent().find('.package_item').last().find('.price_input').attr('name');
//            price_input_name = price_input_name.replace(/package_\d+/, 'package_' + ($(this).parent().parent().parent().find('.package_item').length - 1));
//            $(this).parent().parent().parent().find('.package_item').last().find('.price_input').attr('name', price_input_name);

            //替换阶梯表单域的name值
            let ladderInput = $(this).parent().parent().parent().find('.package_item').last().find('.ladder_input');
            ladderInput.each(function () {
                let name = $(this).attr('name');
                name     = name.replace(/package_\d+/, 'package_' + index);
                $(this).attr('name', name);
            });
//            let ladder_input_name = $(this).parent().parent().parent().find('.package_item').last().find('.ladder_input').attr('name');
//            ladder_input_name = ladder_input_name.replace(/package_\d+/, 'package_' + ($(this).parent().parent().parent().find('.package_item').length - 1));
//            $(this).parent().parent().parent().find('.package_item').last().find('.ladder_input').attr('name', ladder_input_name);
        });
    }

    //绑定删除打包计费事件
    function bindDelPackageEvent() {
        $("#priceGrid").on('click', '.del_package', function () {
            if ($(this).parent().parent().parent().find('.package_item').length > 1) {
                $(this).parent().parent().parent().find('.package_item').last().remove();
            }
        });
    }

    //绑定提交表单事件
    function bindSubmitEvent() {
        $("#saveButtonGrid").on('click', '#submit', function () {
            let start_date = $("#start_date").val();
            if (compareDate(getCurrentDate(), start_date)) {
                let confirmIndex = layui.layer.confirm('系统检测到您校验增加历史的计费配置，增加后系统将会自动的重跑账单，重跑时间将根据历史跨度决定，是否继续添加？', {
                    icon  : 3,
                    title : '警告信息'
                }, function () {
                    layui.layer.close(confirmIndex);
                    submitEvent();
                });
            } else {
                submitEvent();
            }
        });
    }

    //表单提交
    function submitEvent() {
        let data      = $("#form").serialize();
        let loadIndex = layui.layer.load(3);
        $.ajax({
            url     : location.href,
            type    : 'post',
            data    : data,
            success : function (response) {
                if (0 === Number(response.code)) {
                    location.href = './index.html?customer_id=' + getRequestParam('customer_id');
                } else {
                    layui.layer.close(loadIndex);
                    layui.layer.alert(response.message, {icon : 2});
                }
            },
            error   : function () {
                layui.layer.close(loadIndex);
                layui.layer.alert('服务端请求失败，请及时处理', {icon : 2});
            }
        });
    }


    //页面初始化
    function init() {
        $.ajax({
            url     : location.href,
            type    : 'post',
            data    : {
                customer_id  : getRequestParam('customer_id'),
                request_type : 'GET_ADD_CONFIG'
            },
            success : function (response) {
                if (0 === Number(response.code)) {
                    let customer_id = getRequestParam('customer_id');
                    $("#customer_id").val(customer_id);
                    $("#start_date").val(getCurrentDate());
                    window.config = response.data.config;
                    $("#customer_name").val(response.data.customer_name);
                    setProductInput();
                    bindAddLadderEvent();
                    bindDelLadderEvent();
                    bindAddPackageEvent();
                    bindDelPackageEvent();
                    bindSubmitEvent();
                } else {
                    layui.layer.alert(response.message, {icon : 2});
                }
            },
            error   : function () {
                layui.layer.alert('服务端请求失败，请及时处理', {icon : 2});
            }
        });
    }
    init();

    //$("#copy_id").blur(function(){
    $(".copy_price_btn").click(function(){
        let copy_config_id = $("#copy_id").val();
        if(Number(copy_config_id) === 0){
            layui.layer.alert('请输入有效的配置id', {icon : 2});
            return false;
        }

        //多级产品计费模式
        let mode = $(".mode_radio:checked").length > 0 ? $(".mode_radio:checked").val() : 0;
        console.log('mode:'+mode);
        if(Number(mode) !== 1){
            layui.layer.alert('计费模式必须为独立子产品', {icon : 2});
            return false;
        }

        let methods = $(".methods_radio:checked").length > 0 ? $(".methods_radio:checked").val() : 0;
        console.log('methods:'+methods);
        if(Number(methods) < 2 ||  Number(methods) > 4){
            layui.layer.alert('计费方式必须为固定价格、累进阶梯、到达阶梯中的一种', {icon : 2});
            return false;
        }
        if(Number(methods) === 3 || Number(methods) === 4){
            let period = $(".period_radio:checked").length > 0 ? $(".period_radio:checked").val() : 0;
            if(Number(period) === 0){
                layui.layer.alert('请选择阶梯周期', {icon : 2});
                return false;
            }
        }

        let operator = $(".operator_radio:checked").length > 0 ? $(".operator_radio:checked").val() : 0;
        console.log('operator:'+operator);

        let account_id = $(".account_input:checked").length > 0 ? $(".account_input:checked").val() : 0;
        console.log('account_id:'+account_id);
        if(account_id === 0){
            layui.layer.alert('请选择账号', {icon : 2});
            return false;
        }

        let url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/customer/price/info";
        console.log('=====================');
        Request.get(url, {id:copy_config_id}).then(function (response) {
            console.log(response.data.account_id);
            console.log(response.data.methods);
            let config = response.data;
            if(config.account_id != account_id){
                layui.layer.alert('当前选择账号和要复制配置的账号不一致', {icon : 2});
                return false;
            }
            if(config.mode != mode){
                layui.layer.alert('当前设置计费模式和要复制配置的计费模式不一致', {icon : 2});
                return false;
            }
            if(config.methods != methods){
                layui.layer.alert('当前设置计费方式和要复制配置的计费方式不一致', {icon : 2});
                return false;
            }
            if(config.diff_operator != operator){
                layui.layer.alert('当前设置区分运营商和要复制配置的区分运营商不一致', {icon : 2});
                return false;
            }

            setCopyPrice(config);

        });

    });

    //设置复制记录价格
    function setCopyPrice(config){
        let productId = $("#product_id").val();//开通的父产品的id
        let methods = config.methods;
        console.log('productId:'+productId);
        if (Number(productId) === 0) {
            layui.layer.alert('请选择产品', {icon : 2});
            return false;
        }
        //产品配置
        let productConfig = window.config[productId].config;
        let accountId   = config.account_id;
        //当前要设置的productList
        let productList = productConfig.children_product[accountId];

        let copyPriceList = config.price;//复制配置的价格列表

        let display = `<div class="form-group">
            <label for="price" class="col-xs-4 col-sm-4 col-md-2 col-log-1 control-label">
                单价：
            </label>
            <div class="col-xs-6 col-sm-8 col-md-6 col-log-6">`;
        Object.keys(productList).forEach(function (item) {
            //console.log('item:'+item);//开通的子产品的id
            //console.log('pname:'+productList[item]);//开通的子产品名
            //判断要开通的子产品在要复制的产品配置中是否存在
            if(copyPriceList.hasOwnProperty(item)){
                console.log('yes');
                console.log('get-copy-pinfo:'+copyPriceList[item]);//copy的子产品
                display += `<div class="children_product_title">${productList[item]}</div>`;
                display += copyItemOperatorPriceInput(productId, methods, `[${item}]`, copyPriceList[item]);
                display += `<hr class="children_product_hr">`;
            }else{
                console.log('no');
                display += `<div class="children_product_title" style="color:red;">${productList[item]}</div>`;
                display += copyItemOperatorPriceInput(productId, methods, `[${item}]`);
                display += `<hr style="border-block-color:red;" class="children_product_hr">`;
            }


        });
        display += `</div></div>`;

        $("#priceGrid").html(display);

        if ('' !== String(display)) {
            $("#saveButtonGrid").html(`<input type="button" id="submit" class="btn btn-primary btn-sm" value="增加计费配置">`);
        }

    }


    //复制每个运营商的单价模块
    function copyItemOperatorPriceInput(productId, methods, postfixName = '', copyPriceList = '') {
        //是否区分运营商
        let operator = $(".operator_radio:checked").length > 0 ? $(".operator_radio:checked").val() : 0;
        //console.log(operator);
        if (1 === Number(operator)) {
            let operatorConfig = window.config[productId].config.operator;
            let display        = ``;
            console.log(operatorConfig);
            Object.keys(operatorConfig).forEach(function (item) {
                display += `<div class="operator_left_offset"><div class="operator_title">${operatorConfig[item]}</div>`;

                if(copyPriceList == ''){
                    display += copyItemPriceInput(methods, postfixName + `[${item}]`);
                }else{
                    //判断要开通的子产品运营商在要复制的产品运营商中是否存在
                    if(copyPriceList.hasOwnProperty(item)){
                        console.log('operator-price-yes:'+copyPriceList[item]);
                        display += copyItemPriceInput(methods, postfixName + `[${item}]`, copyPriceList[item]);
                    }else{
                        console.log('operator-price-no:'+item);
                        display += copyItemPriceInput(methods, postfixName + `[${item}]`);
                    }
                }

                display += `</div>`;
            });
            return display;
        } else {
            return copyItemPriceInput(methods, postfixName, copyPriceList);
        }
    }

    //复制单元单价模块
    function copyItemPriceInput(methods, name, copyPriceList = '') {
        let display = ``;
        switch (Number(methods)) {
            case 0:
                return `<input type="text" value="0" name="price${name}" readonly class="form-control" />`;
                break;
            case 1:
                return `<input type="text" class="form-control price_input" name="price${name}" required placeholder="请输入价格" required />`;
                break;
            case 2:
                if(copyPriceList == ''){
                    display += `<input type="text" class="form-control price_input" name="price${name}" required placeholder="请输入单价" required />`;
                }else{
                    display += `<input type="text" class="form-control price_input" name="price${name}" required placeholder="请输入单价" required value="${copyPriceList}" />`;
                }

                return display;
                break;
            case 3:
                display += `<div class="table-responsive">
<table class="table table-hover table-bordered" border="0" cellpadding="0" cellspacing="0">
    <thead>
        <tr><th>阶梯阈值</th><th>阶梯单价</th></tr>
    </thead>
    <tbody>`;
                if(copyPriceList === ''){
                    display += ` <tr>
            <td><input type="number" class="form-control ladder_input" name="ladder${name}[]" required placeholder="请输入当前阶梯阈值" value="1" /></td>
    <td><input type="text" class="form-control price_input" name="price${name}[]" required placeholder="请输入当前阶梯单价" /></td>
</tr>`;
                }else{
                    Object.keys(copyPriceList).forEach(function (k) {
                        display += ` <tr>
            <td><input type="number" class="form-control ladder_input" name="ladder${name}[]" required placeholder="请输入当前阶梯阈值" value="${k}" /></td>
    <td><input type="text" class="form-control price_input" name="price${name}[]" required placeholder="请输入当前阶梯单价" value="${copyPriceList[k]}"/></td>
</tr>`;
                        console.log("===3===");
                        console.log('copy-k:'+k);//copy的阶梯阈值
                        console.log('copy-v:'+copyPriceList[k]);//copy的阶梯单价

                    })

                }

                display += `</tbody>
</table>
</div>
<div class="ladder_operation">
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-primary btn-sm add_ladder" value="增加阶梯"></label>
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-warning btn-sm del_ladder" value="删除阶梯"></label>
</div>
`;
                return display;
            break;
            case 4:
                display += `<div class="table-responsive">
<table class="table table-hover table-bordered" border="0" cellpadding="0" cellspacing="0">
    <thead>
        <tr><th>阶梯阈值</th><th>阶梯单价</th></tr>
    </thead>
    <tbody>`;

                if(copyPriceList === ''){
                    display += `<tr>
    <td><input type="text" class="form-control ladder_input" name="ladder${name}[]" required placeholder="请输入当前阶梯阈值" value="1"></td>
    <td><input type="text" class="form-control price_input" name="price${name}[]" required placeholder="请输入当前阶梯单价" /></td>
</tr>`;
                }else{
                    Object.keys(copyPriceList).forEach(function (k) {
                        display += `<tr>
    <td><input type="text" class="form-control ladder_input" name="ladder${name}[]" required placeholder="请输入当前阶梯阈值" value="${k}"></td>
    <td><input type="text" class="form-control price_input" name="price${name}[]" required placeholder="请输入当前阶梯单价" value="${copyPriceList[k]}" /></td>
</tr>`;
                        console.log("===4===");
                        console.log('copy-k:'+k);//copy的阶梯阈值
                        console.log('copy-v:'+copyPriceList[k]);//copy的阶梯单价

                    })

                }

                display += `</tbody>
</table>
</div>
<div class="ladder_operation">
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-primary btn-sm add_ladder" value="增加阶梯"></label>
    <label class="checkbox-inline" style="padding-left: 0;"><input type="button" class="btn btn-warning btn-sm del_ladder" value="删除阶梯"></label>
</div>
`;
                return display;
                break;
        }
    }



</script>