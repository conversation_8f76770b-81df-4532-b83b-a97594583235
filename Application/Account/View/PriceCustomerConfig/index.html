<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>计费配置增加页</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .container {
            margin  : 20px auto;
            padding : 10px;
            width   : 96%;
        }

        pre {
            white-space : pre-wrap; /* css-3 */
            white-space : -moz-pre-wrap; /* Mozilla, since 1999 */
            white-space : -o-pre-wrap; /* Opera 7 */
            word-wrap   : break-word;
        }

        .price_details {
            font-family : monospace, monospace;
            font-size   : 1em;
        }

        .layui-layer-dialog .layui-layer-content {
            position    : relative;
            padding     : 20px;
            line-height : 24px;
            word-break  : break-all;
            overflow    : hidden;
            font-size   : 14px;
            overflow-x  : hidden;
            overflow-y  : auto;
        }
    </style>
</head>
<body>
<div class="container">
    <div id="operation">
        <a href="./add.html?customer_id={$customer_id}" class="layui-btn">增加配置</a>
    </div>
    <div id="table" lay-filter="show"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.4/layui/layui.all.js"></script>
<script type="application/javascript">
    layui.use('table', function () {
        let table = layui.table;
        let layer = layui.layer;

        //第一个实例
        table.render({
            elem   : '#table',
            height : 'auto',
            url    : '{:U("index")}', //数据接口
            page   : false, //开启分页
            method : 'POST',
            where  : {customer_id : "{$customer_id}"},
            cols   : [[ //表头
                {field : 'id', title : 'ID', width : 60, sort : true, fixed : 'left'},
                {field : 'product_name', title : '产品'},
                {field : 'account_name', title : '账号'},
                {field : 'methods', title : '计费方式', width : 90},
                {field : 'accord', title : '计费依据', width : 90},
                {field : 'diff_operator', title : '运营商'},
                {field : 'mode', title : '多级模式', width : 90},
                {
                    field : 'operation1', title : '价格', width : 90, event: 'showPrice',templet:function () {
                        return `<div><button class="layui-btn layui-btn-xs ">查看详情</button></div>`;
                    }
                },
                {field : 'period', title : '计费周期', width : 90},
                {
                    field : 'remark', title : '备注', width : 90, event: 'showRemark',templet:function () {
                        return `<div><button class="layui-btn layui-btn-xs ">查看详情</button></div>`;
                    }
                },
                {field : 'start_date', title : '开始时间', width : 110},
                {field : 'create_time', title : '创建时间', width : 146},
                {field : 'update_time', title : '更新时间', width : 146},
                {
                    field : 'operation', title : '操作', templet : function (data) {
                    return `<div><button onclick="del(${data.id})" onclick="del(${data.id})" class="layui-btn layui-btn-xs layui-btn-danger">删除</button></div>`;
                }
                }
            ]]
        });

        table.on('tool(show)', function(obj){
            var data = obj.data;
            if(obj.event === 'showPrice'){
                layer.open({
                    type: 1,
                    title: false,
                    skin: 'layui-layer-demo', //样式类名
                    closeBtn: 0, //不显示关闭按钮
                    anim: 2,
                    shadeClose: true, //开启遮罩关闭
                    content: data.price,
                    area: '700px',
                });
            }

            if(obj.event === 'showRemark'){
                layer.open({
                    type: 1,
                    title: false,
                    skin: 'layui-layer-demo', //样式类名
                    closeBtn: 0, //不显示关闭按钮
                    anim: 2,
                    shadeClose: true, //开启遮罩关闭
                    content: '<div style="margin: 8px">'+data.remark+'</div>',
                    area: '400px',
                });
            }

        });
    });

    function del(id) {

        layui.layer.confirm('是否删除该条计费配置？', {
            icon  : 3,
            title : '警告信息'
        }, function () {
            $.ajax({
                url     : './delete.html',
                data    : {
                    id : id
                },
                type    : 'post',
                success : function (response) {
                    if (0 === Number(response.code)) {
                        location.href = location.href;
                    } else {
                        layui.layer.alert(response.message ? response.message : response.info, {icon : 2});
                    }
                },
                error   : function () {
                    layui.layer.alert('删除失败', {icon : 2});
                }
            })
        });

    }
</script>