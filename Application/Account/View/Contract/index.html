<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>

    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .demo-form-inline .el-input {
        --el-input-width: 220px;
    }

    .search-form {
        :deep(.el-form-item) {
            margin-bottom: 12px;
        }
    }

    .el-table {
        :deep(.el-table__cell) {
            padding: 4px 0;
        }

        /*:deep(thead) {*/
        /*  font-weight: 700;*/
        /*  color: var(--el-text-color-regular);*/
        /*}*/

        /*:deep(td .cell) {*/
        /*  color: var(--el-text-color-primary);*/
        /*}*/
    }

    /*提交按钮颜色设置*/
    .el-button--primary {
        --el-button-bg-color: #409eff;
        --el-button-hover-bg-color: #409eff;
    }

    .el-button--primary:hover {
        --el-button-hover-bg-color: #79bbff;
    }

    .el-tag--small {
        height: 16px;
        padding: 0 4px;
    }

    /* 悬浮按钮样式*/
    .mainaffix {
        position: absolute;
        top: 50%;
        right: 0;
        z-index: 999999;
        cursor: pointer;
    }

    .copy_btn_icon{
        padding-top: 0;
        padding-bottom: 0;
    }
    /*.copy_temp {*/
    /*    font-weight: bold;*/
    /*    color: #000;*/
    /*    cursor: pointer;*/
    /*}*/

    /*.product_name {*/
    /*    display: block;*/
    /*}*/
</style>

<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <div class="panel panel-default">
            <div class="panel-body">
                <el-form :inline="true" :model="formInline" label-position="left" label-width="100px" class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]" size="mini">
                    <el-form-item label="合同编号">
                        <el-input v-model="formInline.contract_no" placeholder="输入合同编号" clearable></el-input>
                    </el-form-item>


                    <el-form-item label="渠道类型" v-if="showChannelCol">
                        <el-select v-model="formInline.channel_type" placeholder="选择类型简称" @change="typeOptionsChange" filterable clearable>
                            <el-option v-for="item in channelTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="渠道简称" v-if="showChannelCol">
                        <el-select v-model="formInline.channel_name" placeholder="选择渠道简称" @change="typeOptionsChange" filterable clearable>
                            <el-option v-for="item in channelNameOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="渠道全称" v-if="showChannelCol">
                        <el-select v-model="formInline.channel_full_name" placeholder="选择渠道全称" @change="typeOptionsChange" filterable clearable>
                            <el-option v-for="item in channelFullNameOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="商务跟进人" v-if="showChannelCol">
                        <el-select v-model="formInline.channel_salesman" placeholder="选择商务跟进人" @change="typeOptionsChange" filterable clearable>
                            <el-option v-for="item in channelSalesmanOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="签约年份" v-if="showChannelCol">
                        <el-date-picker
                                v-model="formInline.sign_year"
                                type="year"
                                value-format="yyyy"
                                placeholder="签约年份">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="合同类型">
                        <el-select v-model="formInline.contract_type" placeholder="选择合同类型" @change="typeOptionsChange" filterable clearable>
                            <el-option v-for="item in typeOptions[contractCategoryTab]" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="合同开始时间">
                        <!--<el-date-picker v-model="formInline.contract_start" type="monthrange" value-format="timestamp" align="center"-->
                        <!--                unlink-panels range-separator="-" start-placeholder="开始月份" end-placeholder="结束月份"-->
                        <!--                :default-time="['00:00:00', '23:59:59']"></el-date-picker>-->
                        <el-date-picker v-model="formInline.contract_start_start" type="month" value-format="timestamp" placeholder="合同开始时间" style="width:110px"></el-date-picker>
                        -
                        <el-date-picker v-model="formInline.contract_start_end" type="month" value-format="timestamp" placeholder="合同开始时间" style="width:110px"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="合同结束时间">
                        <el-date-picker v-model="formInline.contract_end_start" type="month" value-format="timestamp" placeholder="合同结束时间" style="width:110px"></el-date-picker>
                        -
                        <el-date-picker v-model="formInline.contract_end_end" type="month" value-format="timestamp" placeholder="合同结束时间" style="width:110px"></el-date-picker>
                    </el-form-item>
                    <!--<el-form-item label="商务">-->
                    <!--    <el-select v-model="formInline.salesman_name" filterable remote reserve-keyword placeholder="输入商务名" remote-show-suffix :remote-method="remoteSalesmanMethod" :loading="remoteSalesmanData.loading" clearable>-->
                    <!--        <el-option v-for="item in remoteSalesmanData.options" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
                    <!--    </el-select>-->
                    <!--</el-form-item>-->
                    <el-form-item label="部门/人员" v-show="!disableUser && showCustomerCol">
                        <el-cascader
                                v-model="formInline.dept_id"
                                :options="deptInfo"
                                :props="{ checkStrictly: true,expandTrigger: 'hover',value: 'key', }"
                                clearable
                                popper-class="popper"
                                :show-all-levels="false"
                                filterable
                                fit
                        >
                        </el-cascader>
                    </el-form-item>

                    <el-form-item label="客户名称" v-if="showCustomerCol">
                    <el-autocomplete
                            class="inline-input"
                            v-model="formInline.customer_name"
                            :fetch-suggestions="customer_name_search"
                            placeholder="请输入客户名称"
                            :trigger-on-focus="false"
                            @select="handleSelect"
                    ></el-autocomplete>
                    </el-form-item>
                    <el-form-item label="公司名称" v-if="showCustomerCol">
                        <el-select v-model="formInline.company_name" filterable remote reserve-keyword placeholder="输入公司名称" remote-show-suffix :remote-method="remoteCompanyMethod" :loading="remoteCompanyData.loading" clearable>
                            <el-option v-for="item in remoteCompanyData.options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">查询</el-button>
                        <el-button type="warning" @click="showUploadDialog()">上传</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
    <div class="container">
        <template>
            <div class="w-[99/100] mt-2 bg-bg_color">
                <el-tabs v-model="contractCategoryTab" class="demo-tabs" type="card" @tab-click="handleTabChange">
                    <el-tab-pane label="客户类别" name="1"></el-tab-pane>
                    <el-tab-pane label="渠道类别" name="2" v-if="!['shuyu.jia','tianyi.li'].includes(user_name)"></el-tab-pane>
                    <!--<el-tab-pane label="三方类别" name="3" v-if="!['shuyu.jia'].includes(user_name)"></el-tab-pane>-->

                    <el-table :data="tableData" border ref="mainTable" style="width: 100%" fit stripe>
                        <!-- 所有类型都存在的字段 -->
                        <el-table-column label="合同编号" width="180">
                            <template #default="scope">
                                <el-button  type="text" class="copy_btn copy_btn_icon" :data-clipboard-text="scope.row.contract_no" icon="el-icon-document-copy"></el-button>
                                {{scope.row.contract_no}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="contract_type_text" label="合同类型" width="80"></el-table-column>
                        <el-table-column prop="contract_start" label="合同开始日期" width="110"></el-table-column>
                        <el-table-column prop="contract_end" label="合同结束日期" width="110"></el-table-column>
                        <el-table-column prop="salesman_name" label="商务跟进人" width="100"></el-table-column>

                        <el-table-column prop="sign_type_text" v-if="showCustomerCol" label="签约类型" width="80"></el-table-column>

                        <el-table-column prop="channel_type_text" v-if="showChannelCol" label="渠道类型" width="80"></el-table-column>
                        <el-table-column prop="channel_name" v-if="showChannelCol" label="渠道简称" width="120"></el-table-column>
                        <el-table-column prop="channel_full_name" v-if="showChannelCol" label="渠道全称" width="250"></el-table-column>
                        <el-table-column prop="sign_group_text" v-if="showChannelCol" label="签约主体" width="120"></el-table-column>
                        <el-table-column prop="sign_year" v-if="showChannelCol" label="签约年份" width="80"></el-table-column>


                        <el-table-column prop="customer_name" v-if="showCustomerCol" label="客户名称" width="250"></el-table-column>
                        <el-table-column v-if="showCustomerCol || showThirdCol" label="公司名称" width="250">
                            <template #default="scope">
                                <span v-if="scope.row.company_name" class="copy_temp">{{ scope.row.company_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="showCustomerCol" label="签约主产品" width="150">
                            <template #default="scope">
                                <template v-for="p_name in scope.row.main_product_name">
                                    <span class="product_name" >{{ p_name }}</span><br />
                                </template>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="showCustomerCol" label="签约子产品" width="200">
                            <template #default="scope">
                                <template v-for="p_name in scope.row.product_name">
                                    <span class="product_name" >{{ p_name }}</span><br />
                                </template>
                            </template>
                        </el-table-column>

                        <!--<el-table-column prop="channel_product_name" v-if="showChanneChanneChanneChannel品" width="80"></el-table-column>-->

                        <!--<el-table-column prop="third_party_company_name" v-if="showThirdCol" label="三方公司" width="80"></el-table-column>-->
                        <!--<el-table-column prop="third_product_name" v-if="showThirdCol" label="产品" width="80"></el-table-column>-->

                        <el-table-column prop="created_at" v-if="showCustomerCol" label="归档时间" width="110"></el-table-column>
                        <el-table-column prop="archive_num" v-if="showCustomerCol" label="归档份数" width="80"></el-table-column>
                        <el-table-column prop="remark" label="备注"></el-table-column>
                        <el-table-column prop="" label="操作" width="360" fixed="right">
                            <template #default="scope">
                                <el-button size="mini" @click="viewPdf(scope.row.file_url, scope.row.contract_no)">pdf预览</el-button>
                                <el-button v-if="!disableUser && !disableDept" size="mini" type="warning" @click="showEditDialog(scope.row)">编辑</el-button>
                                <el-button v-if="!disableUser && !disableDept" size="mini" type="danger" @click="showDelDialog(scope.row.contract_no)">删除</el-button>

                                <template v-if="['yuan.sun','wei.xiu','chuanchuan.yu','xiaolan.xie','ningning.zhang'].includes(user_name)">
                                    <el-button v-if="scope.row.permission_status == 10"  size="mini" type="success" @click="downloadPdf(scope.row.contract_no)">下载</el-button>
                                    <el-button v-else-if="scope.row.permission_status == 1" size="mini">申请中</el-button>
                                    <el-tooltip v-else-if="scope.row.permission_status == 20" class="item" effect="light" :content="'驳回原因:'+(scope.row.reject_comments === '' ? '无':scope.row.reject_comments)" placement="bottom">
                                        <el-button @click="approvalDownload(scope.row.contract_no)" type="info" size="mini">重新申请</el-button>
                                    </el-tooltip>
                                    <el-button v-else-if="scope.row.permission_status == 30" @click="approvalDownload(scope.row.contract_no)" size="mini">重新申请</el-button>
                                    <el-button v-else-if="scope.row.permission_status == 40" @click="approvalDownload(scope.row.contract_no)" size="mini">重新申请</el-button>
                                    <el-button v-else size="mini" @click="approvalDownload(scope.row.contract_no)">申请下载</el-button>
                                </template>
                                <template v-if="['shuyu.jia','ren.zhang','tianyi.li'].includes(user_name)">
                                    <el-button  size="mini" type="success" @click="downloadPdf(scope.row.contract_no)">下载</el-button>
                                </template>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tabs>
            </div>
            <div>
                <el-pagination background layout="prev, pager, next" :page-size="formInline.page_size" :total="tableDataTotalNum" @current-change="handleCurrentChange"></el-pagination>
            </div>
        </template>
    </div>


    <!--弹窗-->
    <el-dialog :visible="uploadDialogData.dialogVisible" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" title="上传合同" width="400px">
        <el-form>
            <el-upload
                    :headers="headersToken"
                    :file-list="fileList"
                    class="upload-demo"
                    drag
                    :action="urls.contract_upload_api_upload"
                    multiple
                    accept=".pdf,.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf"
                    ref="uploadRef"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :on-error="handleError"
                    :on-success="handleSuccess"
                    :before-upload="beforeUpload"
                    :limit="301"
            >
                <!--<el-icon class="el-icon&#45;&#45;upload">-->
                <!--  <IconifyIconOffline :icon="CloudUpload" />-->
                <!--</el-icon>-->
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">
                    <p style="font-size:1.5em;font-weight: bold">请上传一个Excel和若干个PDF文件</p>
                    <p style="font-size:1.5em;font-weight: bold">Excel中合同行数应和PDF文件个数匹配</p>
                    <p style="font-size:1.5em;font-weight: bold">PDF文件名称应为合同编号</p>
                    <p style="font-size:1.5em;font-weight: bold">点击下载模板</p>
                    <p><a href="/statics/template/客户类别excel模板.xlsx" style="font-size:1em;font-weight: bold">客户类别模板</a></p>
                    <!--<p><a href="/statics/template/三方类别excel模板.xlsx" style="font-size:1em;font-weight: bold">三方类别模板</a></p>-->
                    <p><a href="/statics/template/渠道类别excel模板.xlsx" style="font-size:1em;font-weight: bold">渠道类别模板</a></p>
                    <br/>
                </div>
            </el-upload>

            <el-form-item label="合同类别" required>
                <el-radio-group v-model="addFileFormData.category" size="large">
                    <el-radio-button v-for="item in filteredCategoryOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancelUploadContracts">取消</el-button>
                <el-button type="primary" @click="submitContracts">提交合同</el-button>
            </div>
        </template>
    </el-dialog>

    <el-dialog :visible="deleteDialogData.dialogVisible" title="提示" width="500">
        <span>确认删除合同么</span>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancleDelDialog">取消</el-button>
                <el-button type="primary" @click="execDel"> 确认</el-button>
            </div>
        </template>
    </el-dialog>


    <!--编辑合同-->
    <el-dialog :visible="editDialogData.dialogVisible" title="编辑合同">
        <el-form ref="editDialogForm" :model="editDialogData.contract_info" label-width="150px" size="mini">
            <el-form-item label="合同编号">
                {{editDialogData.contract_info.contract_no}}
            </el-form-item>

            <el-form-item label="合同类型">
                <el-select v-model="editDialogData.contract_info.contract_type" placeholder="选择合同类型" filterable clearable style="width: 300px;">
                    <el-option v-for="item in typeOptions[contractCategoryTab]" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="渠道类型" v-if="showChannelCol">
                <el-select v-model="editDialogData.contract_info.channel_type" placeholder="选择类型简称" filterable clearable style="width: 300px;">
                    <el-option v-for="item in channelTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="签约主体" v-if="showChannelCol">
                <el-select v-model="editDialogData.contract_info.sign_group" placeholder="选择签约主体" filterable clearable style="width: 300px;">
                    <el-option v-for="item in signGroupOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="签约年份" v-if="showChannelCol">
                <el-date-picker
                        v-model="editDialogData.contract_info.sign_year_timestamp"
                        type="year"
                        format="yyyy"
                        value-format="timestamp"
                        placeholder="签约年份"
                        style="width: 300px;"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item label="合同开始时间">
                <el-date-picker v-model="editDialogData.contract_info.contract_start_timestamp" type="date" value-format="timestamp" align="center" placeholder="合同开始日期" :picker-options="pickerOptions" style="width: 300px;"></el-date-picker>
            </el-form-item>

            <el-form-item label="合同结束时间">
                <el-date-picker v-model="editDialogData.contract_info.contract_end_timestamp" type="date" value-format="timestamp" align="center" placeholder="合同结束日期" :picker-options="pickerOptions" style="width: 300px;"></el-date-picker>
            </el-form-item>

            <el-form-item label="签约类型" v-if="showCustomerCol">
                <el-select v-model="editDialogData.contract_info.sign_type" placeholder="选择签约类型" filterable clearable style="width: 300px;">
                    <el-option v-for="item in signOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="商务">
                <el-select v-model="editDialogData.contract_info.salesman_name" filterable remote reserve-keyword placeholder="输入商务名" remote-show-suffix :remote-method="remoteSalesmanMethod" :loading="remoteSalesmanData.loading" clearable style="width: 300px;">
                    <el-option v-for="item in remoteSalesmanData.options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="公司名称" v-if="showCustomerCol || showThirdCol">
                <el-select v-model="editDialogData.contract_info.company_name" allow-create filterable remote reserve-keyword placeholder="输入公司名称" remote-show-suffix :remote-method="remoteCompanyMethod" :loading="remoteCompanyData.loading" clearable style="width: 300px;">
                    <el-option v-for="item in remoteCompanyData.options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="客户名称" v-if="showCustomerCol || showThirdCol">
                <el-select v-model="editDialogData.contract_info.customer_id" allow-create filterable reserve-keyword placeholder="输入公司名称" clearable style="width: 300px;">
                    <el-option v-for="(item,index) in customerOptions" :key="index" :label="item" :value="index"></el-option>
                </el-select>
            </el-form-item>

            <!--<el-form-item label="主产品" v-if="showCustomerCol">-->
            <!--    <el-select v-model="editDialogData.contract_info.product_id" filterable reserve-keyword placeholder="选择主产品" clearable>-->
            <!--        <el-option v-for="(item,index) in mainProductOptions" :key="index" :label="item.product_name" :value="item.product_id"></el-option>-->
            <!--    </el-select>-->
            <!--</el-form-item>-->

            <el-form-item label="主产品" v-if="showCustomerCol">
                <el-select v-model="editDialogData.contract_info.father_id_arr" allow-create filterable multiple placeholder="选择子产品" clearable style="width: 300px;">
                    <el-option v-for="(item,index) in editDialogData.fatherProductMap" :key="index" :label="item" :value="index"></el-option>
                </el-select>

                <el-popover
                    placement="right"
                    width="400"
                    trigger="hover">
                    <div>
                        添加子产品时会附带添加主产品,<br >但因为可以单独添加主产品,<br >所以在删除子产品的时候并不会同时删除对应的主产品,<br >请手动删除的对应的主产品.
                    </div>
                    <i class="el-icon-warning" slot="reference"></i>
                </el-popover>

            </el-form-item>

            <el-form-item label="子产品" v-if="showCustomerCol">
                <el-select v-model="editDialogData.contract_info.product_id_arr" allow-create filterable multiple placeholder="选择子产品" clearable style="width: 300px;">
                    <el-option v-for="(item,index) in editDialogData.allProductOptions" :key="index" :label="item" :value="index"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="渠道简称" v-if="showChannelCol">
                <el-input v-model="editDialogData.contract_info.channel_name" filterable placeholder="渠道简称" style="width: 300px;"></el-input>
            </el-form-item>

            <el-form-item label="渠道全称" v-if="showChannelCol">
                <el-input v-model="editDialogData.contract_info.channel_full_name" filterable placeholder="渠道全称" style="width: 300px;"></el-input>
            </el-form-item>

            <!--<el-form-item label="渠道产品" v-if="showChannelCol">-->
            <!--    <el-input v-model="editDialogData.contract_info.channel_product_name" filterable placeholder="渠道产品" style="width: 300px;"></el-input>-->
            <!--</el-form-item>-->

            <!--<el-form-item label="三方公司" v-if="showThirdCol">-->
            <!--    <el-input v-model="editDialogData.contract_info.third_party_company_name" filterable placeholder="三方公司" style="width: 300px;"></el-input>-->
            <!--</el-form-item>-->

            <!--<el-form-item label="产品" v-if="showThirdCol">-->
            <!--    <el-input v-model="editDialogData.contract_info.third_product_name" filterable placeholder="产品" style="width: 300px;"></el-input>-->
            <!--</el-form-item>-->

            <el-form-item label="归档份数" v-if="showCustomerCol">
                <el-input-number v-model="editDialogData.contract_info.archive_num" :min="1" :max="100" label="归档份数" style="width: 300px;"></el-input-number>
            </el-form-item>

            <el-form-item label="备注">
                <el-input type="textarea" v-model="editDialogData.contract_info.remark" filterable placeholder="备注" style="width: 300px;"></el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="cancleEditDialog">取消</el-button>
                <el-button type="primary" @click="execEdit"> 确认</el-button>
            </div>
        </template>
    </el-dialog>
<!--弹窗-->
</div>

<script type="application/javascript">
    let finance_api_prefix  = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let contract_api_prefix = '{$Think.config.CONTRACT_API_DOMAIN}';
    let pdf_url_prefix      = "<?php echo $pdf_url_prefix; ?>";
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            categoryOptions: [], //合同类别
            typeOptions: [], //合同类型
            signOptions: [], //签约类别
            channelNameOptions:[],//渠道简称
            channelFullNameOptions:[],//渠道全称
            channelSalesmanOptions:[],//渠道商务
            channelTypeOptions:[],//渠道类型
            signGroupOptions:[],//签约主体
            customerOptions: [], //客户名称
            customerNameOptions: [], //客户名称
            mainProductOptions: [], //主产品
            allProductOptions: [], //主产品
            tableData: [],
            tableDataTotalNum: 0,
            formInline: {
                page: 1,
                page_size: 20,
                contract_no: "",
                contract_type: -1, //合同类型
                contract_category: "1", //合同种类
                contract_start_start: "",
                contract_start_end: "",
                contract_end_start: "",
                contract_end_end: "",
                created_at_start: "",
                created_at_end: "",
                company_name: "",
                customer_name: "",
                salesman_name:"",
                channel_name:"",
                channel_full_name:"",
                channel_salesman:"",
                channel_type:"",
                sign_year:"",
                dept_id:[],
                user_cookie:user_cookie,
            },
            user_name:"",
            contractCategoryTab: "1",
            // uft:"",//上传参数,唯一字符串

            //加载遮罩
            loading: false,
            //接口地址
            urls: {
                options_get_map             : finance_api_prefix + '/options/getMap?user_auth=true&customer=true&finance_auth=true&main_product_list=true&product=true&source=true&user_cookie='+user_cookie,
                // options_get_map           : finance_api_prefix + '/options/getMap?customer=true&main_product_list=true&product=true',
                contract_options            : finance_api_prefix + '/contract/options', //获取客户产品列表
                contract_list               : finance_api_prefix + '/contract/list', //获取合同列表
                contract_del                : finance_api_prefix + '/contract/del', //删除合同
                contract_add                : finance_api_prefix + '/contract/add', //添加合同
                contract_edit               : finance_api_prefix + '/contract/edit', //编辑合同
                contract_company_list       : finance_api_prefix + '/contract/company_list',
                salesman_list               : finance_api_prefix + '/contract/salesman_list',
                apply_download              : finance_api_prefix + '/contract/apply_download',
                abandon_download_permission : finance_api_prefix + '/contract/abandon_download_permission',

                contract_upload_api_upload: contract_api_prefix + '/upload',
            },

            // 删除合同操作相关数据
            deleteDialogData: {
                dialogVisible: false,
                dialogLoading: false,
                contract_no: ""
            },

            // 编辑合同操作相关数据
            editDialogData: {
                dialogVisible: false,
                dialogLoading: false,
                contract_no: "",
                contract_info:{},
                productMap:{},
                fatherProductMap:{},
                allProductOptions:{},
            },

            headersToken: {
                Authorization: "",
                Uft: ""
            },

            fileList: [],
            // 上传
            uploadDialogData: {
                dialogVisible: false,
                dialogLoading: false
            },

            // 上传合同表单数据
            addFileFormData: {
                category: "", //合同类别
                excel: "",
                pdf: []
            },

            // 获取公司名称
            remoteCompanyData: {
                loading: false,
                options: []
            },

            // 获取商务名字
            remoteSalesmanData: {
                loading: false,
                options: []
            },

            pickerOptions: {
              // disabledDate(time) {
              //   return time.getTime() > Date.now();
              // },
              shortcuts: [{
                text: '今天',
                onClick(picker) {
                  picker.$emit('pick', new Date());
                }
              }, {
                text: '一年后',
                onClick(picker) {
                  const date = new Date();
                  date.setTime(date.getTime() + 3600 * 1000 * 24 * 364);
                  picker.$emit('pick', date);
                }
              }, {
                text: '一年半后',
                onClick(picker) {
                  const date = new Date();
                  date.setTime(date.getTime() + 3600 * 1000 * 24 * 547);
                  picker.$emit('pick', date);
                }
              }, {
                text: '两年后',
                onClick(picker) {
                  const date = new Date();
                  date.setTime(date.getTime() + 3600 * 1000 * 24 * 729);
                  picker.$emit('pick', date);
                }
              }
              ]
            },
            //区域 人员
            area_map:[],
            user_auth:{},
            area_person:{},
            finance_auth:{},
            customer_list:{},
            deptInfo:[],
            disableDept:true,
            disableUser:true,
        },
        computed: {
            filteredCategoryOptions() {
                // return this.categoryOptions;
                return this.categoryOptions.filter(item => item.value !== -1);
            },
            showCustomerCol() {
                return this.contractCategoryTab === "1";
            },
            showChannelCol() {
                return this.contractCategoryTab === "2";
            },
            showThirdCol() {
                return this.contractCategoryTab === "3";
            },
        },
        watch: {},
        async mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);

            await this.getCustomerOptions();
            await this.getContractOptions();
            await this.getContracttList();

            // this.headersToken.Authorization = "Bearer " + user_cookie;
        },
        created: function () {
        },
        methods: {
            getContracttList: async function () {
                // console.log('contract_list');
                let self = this;
                this.loading = true;
                // contract/list
                await axios.post(self.urls.contract_list, self.formInline).then(function (response) {
                    // console.log(response.data);
                    if (response.data.status === 0) {
                        self.tableData = response.data.data.list;
                        self.tableDataTotalNum = response.data.data.count;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                });
            },
            // 查询
            onSubmit: async function () {
                // console.log("查询提交触发了");
                await this.getContracttList();
            },

            typeOptionsChange: async function () {
                // console.log("typeOptionsChange");
                await this.getContracttList();
            },

            // categoryOptionsChange: async function () {
                // console.log("查询提交触发了");
                // await this.getContracttList();
            // },

            // 获取客户产品列表
            getContractOptions: async function () {
                // console.log("contract options:");
                let self = this;
                // contract/options
                await axios.post(self.urls.contract_options).then(function (response) {;
                    if (response.data.status === 0) {
                        self.typeOptions = response.data.data.type;
                        self.categoryOptions = response.data.data.category;
                        self.signOptions = response.data.data.sign;
                        self.channelNameOptions = response.data.data.channel_name;
                        self.channelFullNameOptions = response.data.data.channel_full_name;
                        self.channelSalesmanOptions = response.data.data.channel_salesman;
                        self.channelTypeOptions = response.data.data.channel_type;
                        self.signGroupOptions = response.data.data.sign_group;
                        // self.uft = response.data.upload_file_token;
                        // self.headersToken.Uft = response.data.data.upload_file_token;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
            },

            customer_name_search(queryString, cb) {
                let restaurants = this.customerNameOptions;
                console.log(restaurants);
                let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                // 调用 callback 返回建议列表的数据
                cb(results);
            },
            createFilter(queryString) {
                return (restaurant) => {
                    return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                };
            },

            handleSelect(item) {
                console.log(item);
            },

            handleTabChange: async function () {
                // console.log("change tabs to ",this.contractCategoryTab);
                this.formInline.contract_category = this.contractCategoryTab;
                if(this.contractCategoryTab === "2") {
                    this.formInline.dept_id = [];
                    this.formInline.dept_id.push('dept_no-dept-id');
                }
                if(this.contractCategoryTab === "1") {
                    await this.getCustomerOptions();
                }
                await this.getContracttList();
            },


            handleCurrentChange: async function (currentPage) {
                // console.log("当前页码:", currentPage);
                this.formInline.page = currentPage;
                await this.getContracttList();
            },

            //pdf预览
            viewPdf: function (file_url, contract_no) {
                // console.log("pdf预览 触发了", file_url, contract_no);
                let url = "/Account/Contract/pdfview.html?pdfurl="+file_url+"&contract_no="+contract_no;
                window.open(url, '_blank');
                // router.push({
                //     path: "/contractManage/pdfview",
                //     query: {
                //         file_url: file_url,
                //         contract_no: contract_no
                //     }
                // });
            },
            //pdf预览
            downloadPdf: function (contract_no) {
                let self = this;
                let para = {
                    contract_no: contract_no,
                    user_cookie: user_cookie,
                };

                self.loading = true;
                axios.post(self.urls.abandon_download_permission, para).then(function (response) {
                    if (response.data.status === 0) {
                        let file_url = response.data.data;
                        self.getContracttList();
                        self.loading = false;
                        window.open(pdf_url_prefix + "/" + file_url, '_blank');
                    } else {
                        errorMsg(response.data.msg);
                        self.loading = false;D
                    }
                }).catch(function (error) {
                    self.loading = false;D
                    errorMsg(error);
                });
            },
            //申请下载合同文件
            approvalDownload: async function (contract_no) {
                let self = this;

                this.$prompt('请输入申请理由', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                }).then(async ({value}) => {
                    let para = {
                        contract_no: contract_no,
                        user_cookie: user_cookie,
                        apply_remark: value, //填写到弹窗中的值
                    };
                    await axios.post(self.urls.apply_download, para).then(function (response) {
                        if (response.data.status === 0) {
                            self.getContracttList();
                        } else {
                            errorMsg(response.data.msg);
                        }
                    }).catch(function (error) {
                        errorMsg(error);
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '取消申请'
                    });
                });

            },

            showDelDialog: function (contract_no) {
                // console.log("展示确认弹窗:", contract_no);
                this.deleteDialogData.dialogVisible = true;
                this.deleteDialogData.contract_no = contract_no;
            },

            cancleDelDialog: function () {
                this.deleteDialogData.dialogVisible = false;
                this.deleteDialogData.contract_no = "";
            },

            execDel: async function () {
                let self = this;
                let para = {contract_no: self.deleteDialogData.contract_no};
                await axios.post(self.urls.contract_del, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.deleteDialogData.contract_no = "";
                        self.deleteDialogData.dialogVisible = false;
                        self.getContracttList();
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
            },

            // 获取客户列表
            getCustomerOptions: async function () {
                // console.log("getOptions:");
                let self = this;
                await axios.get(self.urls.options_get_map ).then(function (response) {
                    self.customerNameOptions = [];
                    // console.log(response.data.status, response.data.data.customer);
                    if (response.data.status === 0) {
                        let customer_options = response.data.data.customer;
                        self.customerOptions = customer_options;
                        self.mainProductOptions = response.data.data.main_product_list;
                        self.allProductOptions = response.data.data.product;

                        self.customerNameOptions = Object.entries(customer_options).map(([key, value]) => ({
                            key: key,
                            value: value
                        }));

                        // console.log('customerNameOptions');
                        // console.log(self.customerNameOptions);

                        //权限
                        let area_person = response.data.data.user_auth.area_person;
                        for (let i in area_person) {
                            let item = area_person[i];
                            self.area_map.push(item.area);
                            self.area_person[item.area.dept_id] = [];
                            for (let ik in item.person) {
                                self.area_person[item.area.dept_id].push(item.person[ik]);
                            }
                        }

                        let user_auth = response.data.data.user_auth;

                        self.user_name = user_auth.user_name;

                        //是否展示确认按钮
                        //是否展示区域,人员

                        //判断是销售人员且是领导 可查看本区域销售
                        if(user_auth.is_sale === true && user_auth.is_leader === 1 && user_auth.data_auth !== 0){
                            self.disableDept = true;
                            self.disableUser = false;
                            self.area_person[user_auth.dept_id] = self.area_person[user_auth.dept_id];
                            // self.formInline.dept_id = user_auth.dept_id;
                        }
                        if(response.data.data.user_auth.is_leader && response.data.data.user_auth.dept_id) {
                            self.def_dept_id = ["dept_" + response.data.data.user_auth.dept_id];//确认默认值
                            self.formInline.dept_id = ["dept_"+response.data.data.user_auth.dept_id];

                        }
                        //判断是销售人员且不是领导 只能查看自己
                        if(user_auth.is_sale === true && user_auth.is_leader === 0 && user_auth.data_auth !== 0){
                            self.disableDept = true;
                            self.disableUser = true;
                            self.formInline.only_sale = 'yes';
                        }

                        // 上面3个都是商务的配置,如果不是商务而且是领导则会被下面判断覆盖

                        //非商务 默认全选
                        if(user_auth.is_sale === false){
                            self.def_dept_id = [user_auth.dept_info[0].key];//确认默认值
                            self.formInline.dept_id = [user_auth.dept_info[0].key];//确认默认值
                        }

                        //判断非销售人员的展示
                        if(user_auth.is_sale === false || user_auth.data_auth === 0){
                            self.disableDept = false;
                            self.disableUser = false;
                        }

                        self.customer_list = response.data.data.customer;//客户

                        self.deptInfo = response.data.data.user_auth.dept_info;
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
            },

            showEditDialog: function (_contract_info) {
                // console.log(_contract_info);
                let contract_info = {};
                for (let key in _contract_info) {
                    contract_info[key] = _contract_info[key];
                }

                this.editDialogData.contract_no = contract_info.contract_no;
                this.editDialogData.contract_info = contract_info;

                this.editDialogData.allProductOptions = { ...this.allProductOptions };
                this.editDialogData.fatherProductMap  = { ...this.allProductOptions };

                // console.log("showEditDialog contract_info.main_product_name:");
                // console.log(this.editDialogData.contract_info.father_id_arr);

                if(contract_info.main_product_name.length > 0) {
                    for (let idx in contract_info.father_name_arr) {
                        let man_product_name = contract_info.father_name_arr[idx];
                        this.editDialogData.fatherProductMap[man_product_name] = man_product_name;
                    }
                }

                if(contract_info.product_name_arr.length > 0) {
                    for (let idx in contract_info.$product_name_arr) {
                        let product_name = contract_info.product_id_arr[idx];
                        this.editDialogData.allProductOptions[product_name] = product_name;
                    }
                }

                // console.log(this.editDialogData.fatherProductMap);

                this.editDialogData.dialogVisible = true;
            },

            cancleEditDialog: function () {
                this.editDialogData.dialogVisible = false;
                this.editDialogData.contract_no = "";
                this.editDialogData.contract_info = {};
                this.productMap = {};
                this.fatherProductMap = {};
            },

            execEdit: async function () {
                let self = this;
                self.editDialogData.contract_info.user_cookie = user_cookie;
                await axios.post(self.urls.contract_edit, self.editDialogData.contract_info).then(function (response) {
                    if (response.data.status === 0) {
                        self.editDialogData.dialogVisible = false;
                        self.editDialogData.contract_no   = "";
                        self.editDialogData.contract_info = {};
                        self.getContracttList();
                    } else {
                        errorMsg(response.data.msg);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
            },

            showUploadDialog: function () {
                this.uploadDialogData.dialogVisible = true;
                //添加支持上传文件夹的属性
                setTimeout(function () {
                    document.querySelector(".el-upload__input").webkitdirectory = true;
                    document.querySelector(".el-upload__input").style.display = "none";

                }, 100);
            },

            handleRemove: function (file, uploadFiles) {
                // console.log(file, uploadFiles);
                this.fileList = uploadFiles;
                // this.fileList.splice(self.fileList.indexOf(file), 1);
            },

            handleError: function (uploadFile, uploadFiles) {
                // console.log(uploadFile, uploadFiles);
            },

            handleSuccess: function (response, uploadFile, uploadFiles) {
                let self = this;
                // console.log("handleSuccess:", response, uploadFile, uploadFiles);
                // console.log("handleSuccess:", uploadFiles);
                if (response.code === 200) {
                    // console.log(uploadFile.name + " 上传成功!");
                    self.fileList = uploadFiles;
                } else {
                    errorMsg(uploadFile.name + " 上传失败: " + response.msg);
                    //在上传列表中清除该文件
                    self.fileList.splice(self.fileList.indexOf(uploadFile), 1);
                }
                // console.log(this.fileList);
            },


            handlePreview: function (uploadFile) {
                // console.log(uploadFile);
            },

            beforeUpload: function (UploadRawFile) {
                if (UploadRawFile.name === '.DS_Store') {
                    return false; // 如果是.DS_Store文件，则不进行上传
                }

                // excel临时文件 则不进行上传
                //类似这种 .~合同管理系统-表单模版.xlsx
                let tmp_xlsx_file_name_rege = /\.~.*\.xlsx?/;
                if (tmp_xlsx_file_name_rege.test(UploadRawFile.name)) {
                    return false;
                }

                if (
                    UploadRawFile.type !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" &&
                    UploadRawFile.type !== "application/pdf"
                ) {
                    errorMsg(
                        UploadRawFile.name + " 文件类型错误,仅支持pdf和xlsx类型文件进行上传!"
                    );
                    return false;
                }
            },


            /**
             * 提交表单
             * 获取文件
             * 获取类别
             */
            submitContracts: async function () {
                let self = this;
                // console.log("submitContracts 方法 开始");
                this.uploadDialogData.dialogLoading = true;

                // console.log("fileList: ",this.fileList);

                for (let i = 0; i < this.fileList.length; i++) {
                    const element = this.fileList[i];
                    const type = element.raw.type;
                    const url = (element.response).url;
                    if (type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
                        this.addFileFormData.excel = url;
                    }
                    if (type === "application/pdf") {
                        this.addFileFormData.pdf.push(url);
                    }
                }
                // console.log(self.addFileFormData);
                self.addFileFormData.user_cookie = user_cookie;
                await axios.post(self.urls.contract_add, self.addFileFormData).then(function (response) {
                    if (response.data.status === 0) {
                        self.uploadDialogData.dialogLoading = false;
                        self.uploadDialogData.dialogVisible = false;
                        self.addFileFormData.category = "";
                        self.addFileFormData.excel = "";
                        self.addFileFormData.pdf = [];
                        self.fileList = [];
                        successMsg("添加成功");
                        self.getContracttList();
                    } else {
                        const message = response.data.msg;
                        errorMsg(message);
                    }
                }).catch(function (error) {
                    errorMsg(error);
                });
                // console.log("submitContracts 方法 结束");
            },


            /**
             * 取消提交
             * 清空文件数据
             * 清空选择的类别
             * 隐藏弹窗
             */
            cancelUploadContracts: function () {
                // console.log("cancelUploadContracts 方法 开始");
                this.uploadDialogData.dialogLoading = true;

                this.fileList = [];
                this.addFileFormData.category = "";
                this.fileList = [];
                this.addFileFormData.excel = "";
                this.addFileFormData.pdf = [];
                this.uploadDialogData.dialogVisible = false;
                this.uploadDialogData.dialogLoading = false;
                // console.log("cancelUploadContracts 方法 结束");
            },


            remoteCompanyMethod: async function (company_name) {
                // console.log("remoteCompanyMethod company_name:",company_name);
                if (company_name) {
                    let self = this;
                    self.remoteCompanyData.loading = true;
                    self.remoteCompanyData.options = [];
                    await axios.post(self.urls.contract_company_list, {company_name: company_name}).then(function (response) {
                        // console.log("remoteCompanyMethod status:",response.data.status);
                        // console.log("remoteCompanyMethod data:",response.data.data);
                        if (response.data.status === 0) {
                            const company_list = response.data.data;
                            // console.log("remoteCompanyMethod company_list:",company_list);
                            for (let i = 0; i < company_list.length; i++) {
                                const company_name = company_list[i];
                                console.log(company_name);
                                const info = {
                                    key: company_name,
                                    label: company_name,
                                    value: company_name
                                };
                                console.log(info);
                                self.remoteCompanyData.options.push(info);
                            }

                            self.remoteCompanyData.loading = false;
                        } else {
                            errorMsg(response.msg);
                        }
                    }).catch(function (error) {
                        errorMsg(error);
                    });
                } else {
                    this.remoteCompanyData.options = [];
                }
            },

            remoteSalesmanMethod: async function (salesman_name) {
                console.log("remoteSalesmanMethod salesman_name:",salesman_name);
                if (salesman_name) {
                    let self = this;
                    self.remoteSalesmanData.loading = true;
                    self.remoteSalesmanData.options = [];
                    await axios.post(self.urls.salesman_list, {salesman_name: salesman_name}).then(function (response) {
                        console.log("remoteSalesmanMethod status:",response.data.status);
                        console.log("remoteSalesmanMethod data:",response.data.data);
                        if (response.data.status === 0) {
                            const company_list = response.data.data;
                            console.log("remoteSalesmanMethod company_list:",company_list);
                            for (let i = 0; i < company_list.length; i++) {
                                const salesman_name = company_list[i];
                                console.log(salesman_name);
                                const info = {
                                    key: salesman_name,
                                    label: salesman_name,
                                    value: salesman_name
                                };
                                console.log(info);
                                self.remoteSalesmanData.options.push(info);
                            }

                            self.remoteSalesmanData.loading = false;
                        } else {
                            errorMsg(response.msg);
                        }
                    }).catch(function (error) {
                        errorMsg(error);
                    });
                } else {
                    this.remoteCompanyData.options = [];
                }
            },

            successFunc: function(e) {
                // console.info("Action:", e.action);
                // console.info("Text:", e.text);
                // console.info("Trigger:", e.trigger);
                // 可以取到目标元素上的自定义属性（可以据此再做一些处理）
                // e.trigger.dataset.test && console.log(e.trigger.dataset.test)
                // 清除选中状态
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc:function(e) {
                // console.error("Action:", e.action);
                // console.error("Trigger:", e.trigger);

                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
        }
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>