<?php

namespace Account\Controller;

use Account\Model\CustomerExpendModel;
use Account\Model\CustomerModel;
use Account\Model\CustomerSalesmanHistoryModel;
use Account\Model\ProductModel;
use Account\Model\CustomerGroupModel;
use Account\Repositories\CompanyTypeRepository;
use Account\Repositories\CustomerFeeRepository;
use Account\Repositories\FeeConfigRepository;
use Api\Controller\BackendCustomerController;
use Api\Repositories\BackendCustomerRepository;
use Common\Common\HandlerLog;
use Common\Common\ResponseTrait;
use Common\Controller\AdminController;
use Common\Controller\DataAuthController;
use Common\Model\FeeConfigModel;
use Common\Model\SystemDeptModel;
use Common\Model\SystemUserModel;
use PreSales\Repositories\PreSalesCustomerRepository;
use Common\Model\CommonEnumModel;

class CustomerController extends AdminController
{
    use ResponseTrait;

    /*
     * 客户Repository
     * */
    private $_customer_model;
    private $customerFeeRepository;
    private $product_model;     //产品的Model
    private $group_model;     //主体的Model
    private $contract_status = [1 => '已签约已付款', 2 => '已签约未付款', 3 => '未签约', 5 => '特殊客户', 4 => '其他'];

    public function __construct()
    {
        parent::__construct();
        $this->_customer_model = (new CustomerModel());
        $this->customerFeeRepository = new CustomerFeeRepository();
        $this->product_model = new ProductModel();
        $this->group_model = new CustomerGroupModel();
    }

    //默认入口
    public function index()
    {
        $companyTypeRepository = new CompanyTypeRepository();
        //监听切换公司类型时间
        $companyTypeRepository->listenCutFirstType();

        $list = $this->_customer_model->getCustomerByGetForPage();
        // GET方式获取用户列表客户(select2使用)
        $list_customer_for_select2 = $this->_customer_model->customerListForGet();
        $list_group_for_select2    = $this->group_model->groupListForGet();

        // 获取产品列表
        $productList = $this->getProductList();
        //客户类型option
        $customerTypeOption = "<option value='' >全部</option>";
        $customerTypeOption .= makeOption(array_flip(CustomerModel::$customerType), I('customerType'));
        //付费类型option
        $paymentTypeOption = "<option value='' >全部</option>";
        $paymentTypeOption .= makeOption(array_flip(CustomerModel::$paymentType), I('paymentType'));
        //征信客户分类option
        $signTypeMap = array_flip(CustomerModel::$signType);
        $signTypeOption = "<option value='-100' >全部</option>";
        $signType = I('signType');
        if($signType === ''){
            $signType = -100;
        }
        $signTypeOption .= makeOption($signTypeMap,$signType);
        $signTypeMap[-1] = '';

        //获取用户数据
        $user_data = (new SystemUserModel())->field('username,realname,dept_id')
            ->where(DataAuthController::instance()
                ->getUserWhere())
            ->select();

        $dept_list = SystemDeptModel::getDeptsInfo();
        $user_dept_map = [];
        foreach($user_data as $user_info) {
            if (!empty($user_info['dept_id'])) {
                $user_dept_map[$user_info['username']] = $dept_list[$user_info['dept_id']]['dept_name'];
            }
        }

        $user_data = array_column($user_data, 'realname', 'username');

        // GET 方法的参数
        $request = I('get.');

        $group_id = I('group_id');
        if(!empty($group_id)){
            $group_name = $this->group_model->getGroupInfoByGroupId($group_id);
            $request['group_name'] = $group_name['group_name'];
        }

        $level_scale = [
            '' => '全部',
            'A' => 'A',
            'B' => 'B',
            'C' => 'C',
            'S' => 'S',
        ];
        $level_income = [
            '' => '全部',
            '1' => '1',
            '2' => '2',
            '3' => '3',
            '4' => '4',
            '5' => '5',
            '6' => '6',
            '7' => '7',
            '8' => '8',
        ];

        //获取主体数据
        $groupOption = $this->getGroupList();

        $this->assign('list_customer', $list['list_customer']);
        $this->assign('productList', $productList);
        $this->assign('list_customer_for_select2', $list_customer_for_select2);
        $this->assign('list_group_for_select2', $list_group_for_select2);
        $this->assign('obj_page', $list['obj_page']);
        $this->assign('request', $request);
        $this->assign('option', $companyTypeRepository->getCompanyTypeOptionForIndex(I('get.first_type'), I('get.type')));
        $this->assign('company_type_data', $companyTypeRepository->getTwiceTypeData());
        $this->assign('user_data', $user_data);
        $this->assign('user_dept_map', $user_dept_map);
        $this->assign('operator_option', makeOption($user_data, I('get.operator', '')));
        $this->assign('salesman_option', makeOption($user_data, I('get.salesman', '')));
        $this->assign('contract_status_option', $this->getContractStatusOption());
        $this->assign('contract_status', $this->contract_status);
        $this->assign('sign_type_map', $signTypeMap);
        $this->assign('customerTypeOption', $customerTypeOption);
        $this->assign('signTypeOption', $signTypeOption);
        $this->assign('paymentTypeOption', $paymentTypeOption);
        $this->assign('GroupOption', $groupOption);
        $this->assign('LevelScaleOption', makeOption($level_scale, I('get.level_scale', '')));
        $this->assign('LevelIncomeOption', makeOption($level_income, I('get.level_income', '')));

        #公司名称
        $company_data = (new CustomerModel())->field('company')
            ->where(DataAuthController::instance()
                ->getCustomerWhere())
            ->where([
                'is_delete' => 0,
            ])
            ->select();
        $this->assign('company_option', makeOption(array_column($company_data, 'company', 'company'), I('get.company')));
        $this->display();
    }

    /**
     * 获取签约状态的选择项Option
     *
     * @access protected
     *
     * @return string
     **/
    protected function getContractStatusOption()
    {
        $contract_status_option = '';
        $contract_status = $this->contract_status;
        $get_contract_status = I('get.contract_status', [1, 2, 3, 4, 5]);
        if (empty($get_contract_status)) {
            $get_contract_status = [1, 2, 3, 4, 5];
        }
        foreach ($contract_status as $key => $val) {
            if (in_array($key, $get_contract_status)) {
                $contract_status_option .= "<option value='{$key}' selected>{$val}</option>";
            } else {
                $contract_status_option .= "<option value='{$key}'>{$val}</option>";
            }
        }

        return $contract_status_option;
    }

    /**
     * 客户列表（新）列表文件的导出
     * <AUTHOR>
     * @datetime 14:05 2018/12/6
     **/
    public function export()
    {
        //在列表页中是以客户为主题进行查询，每行有一条客户数据
        //在列表文件导出中，是以账号为主题进行查询，每行有一条账号数据
        $list = $this->_customer_model->getCustomerByGetForFile();
        //excel导出
        $this->downloadFile('客户管理（新）_' . date('Ymd'), $list);
    }

    /**
     * 创建客户
     */
    public function add()
    {
        // 创建客户页面
        if (IS_GET) {
            //客户类型option
            $customerTypeOption = makeOption(array_flip(CustomerModel::$customerType));
            $reconciliationCycleOption = makeOption(array_flip(CustomerModel::$reconciliationCycle));
            // $signTypeOption = makeOption(array_flip(CustomerModel::$signType));
            $signTypeOption = array_flip(CustomerModel::$signType);

            //获取所有未注册的测试客户
            $preSalesCustomerRepository = new PreSalesCustomerRepository();
            $pre_sales_customer_data = $preSalesCustomerRepository->getNotSignPreCustomer();
            $this->assign('pre_sales_customer_data', $pre_sales_customer_data);
            $this->assign((new CompanyTypeRepository())->getCompanyTypeOption());
            $this->assign('user_option', $this->customerFeeRepository->getUserOption($this->loginuser['username']));
            $this->assign('channel_follower_option', $this->customerFeeRepository->getUserOption('jing.zhou'));//渠道跟进人 默认为周静
            $this->assign('customerTypeOption', $customerTypeOption);
            $this->assign('reconciliationCycleOption', $reconciliationCycleOption);
            $this->assign('signTypeOption', $signTypeOption);
            $this->assign('source_list', (new CommonEnumModel())->getEnumPairs('1'));
            $this->assign('source_id', [1,2,10]);//渠道跟进人可见数据 默认为朴道 浙数交 郑数交

            //获取主体数据
            $_groupOption = $this->getGroupList();
            $groupOption = [];
            $groupOption[''] = '请选择';
            foreach ($_groupOption as $g_id => $g_name){
                $groupOption[$g_id] = $g_name;
            }
            $groupOption = makeOption($groupOption);
            $this->assign('group_option', $groupOption);

            $this->display();
            exit();
        }
        //监听是否为切换一级公司类型
        (new CompanyTypeRepository())->listenCutFirstType();
        // 创建客户
        try {
            $id = $this->_customer_model->createCustomer();
            //修改操作人
            $this->_customer_model->updateAdminInfo($id, $this->loginuser['username']);
            $this->__Return('添加客户成功', ['customer_id' => $id], 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 编辑页面
     */
    public function edit()
    {
        if (IS_GET) {
            try {
                $customer_id = I('get.customer_id', '', 'trim');
                //数据权限校验
                DataAuthController::instance()
                    ->validAllowDoCustomer($customer_id);
                $source_list = (new CommonEnumModel())->getEnumPairs('1');

                // 通过ID,GET的方式获取客户的信息
                $customer_info = $this->_customer_model->getCustomerData($customer_id);
                //对公司名称合并处理
                if(empty($customer_info['agent_company'])){
                    $agent_company = [];
                }else{
                    $agent_company = json_decode($customer_info['agent_company'], true);
                }

                $companys = [];
                foreach ($source_list as $s_id => $source_name){
                    $companys[$s_id]['source_name'] = $source_name;
                    if($s_id == 0){
                        $companys[$s_id]['company_name'] =  $customer_info['company'];
                    }else{
                        $companys[$s_id]['company_name'] = isset($agent_company[$s_id]) ? $agent_company[$s_id] : '';
                    }

                }

                $this->assign('companys', $companys);
                $this->assign('customer_info', $customer_info);
                //客户类型option
                $customerTypeOption = makeOption(array_flip(CustomerModel::$customerType),$customer_info['customer_type']);
                $this->assign('customerTypeOption', $customerTypeOption);
                $preSalesCustomerRepository = new PreSalesCustomerRepository();
                $pre_sales_customer_data = $preSalesCustomerRepository->getPreCustomerBySignCustomerId($customer_info['customer_id']);
                $this->assign('pre_sales_customer_data', $pre_sales_customer_data);
                $response = new FeeConfigRepository();
                $this->assign('fee_config', $response->list_html($customer_id));
                $this->assign('option', (new CompanyTypeRepository())->getCompanyTypeOption($customer_info['type']));

                $this->assign('user_option_operator', $this->customerFeeRepository->getUserOption($customer_info['operator']));
                $this->assign('user_option_salesman', $this->customerFeeRepository->getUserOption($customer_info['salesman']));
                $this->assign('user_option_introduce_salesman', $this->customerFeeRepository->getUserOption($customer_info['introduce_salesman']));
                $default_channel_follower = empty($customer_info['channel_follower'])?'jing.zhou':$customer_info['channel_follower'];
                $this->assign('user_option_channel_follower', $this->customerFeeRepository->getUserOption($default_channel_follower));
                $this->assign('source_list', $source_list);
                $source_id = $customer_info['source_id'] == '-1'? [1,10] : explode(',',$customer_info['source_id']);
                $this->assign('source_id', $source_id);

                $sign_type = explode(',',$customer_info['sign_type']);
                $this->assign('sign_type', $sign_type);

                //对账方式 对账周期
                $reconciliationCycleOption = makeOption(array_flip(CustomerModel::$reconciliationCycle),$customer_info['reconciliation_cycle']);
                $this->assign('reconciliationCycleOption', $reconciliationCycleOption);

                //过去这个客户的所有开通的产品
                $product_list = (new ProductModel())->field(['product_id', 'product_name'])
                    ->select();

                $this->assign('product_options', makeOption(array_column($product_list, 'product_name', 'product_id'), ''));

                $expend_data = (new CustomerExpendModel())->where(compact('customer_id'))
                    ->select();
                $this->assign('expend', $expend_data);

                //当前商务的开始时间
                $salesman_change_month = (new CustomerSalesmanHistoryModel())->getLastSalesman($customer_info['customer_id']);
                if(!is_null($salesman_change_month)){
                    $salesman_change_month = $salesman_change_month[0]['start_day'];
                    $salesman_change_month = date('Y-m', strtotime($salesman_change_month));
                }else{
                    $salesman_change_month = date('Y-m', strtotime('-1 months'));
                }
                $this->assign('salesman_change_month', $salesman_change_month);

                $signTypeOption = array_flip(CustomerModel::$signType);
                $this->assign('signTypeOption', $signTypeOption);

                //获取主体数据
                $_groupOption = $this->getGroupList();
                $groupOption = [];
                $groupOption[''] = '请选择';
                foreach ($_groupOption as $g_id => $g_name){
                    $groupOption[$g_id] = $g_name;
                }
                $groupOption = makeOption($groupOption,$customer_info['group_id']);
                $this->assign('group_option', $groupOption);

                $this->display();
                exit();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), I('get.callback_url', U('index'), 'urldecode'));
            }
        }
        try {
            //监听是否为切换一级公司类型
            (new CompanyTypeRepository())->listenCutFirstType();

            //数据权限校验
            DataAuthController::instance()
                ->validAllowDoCustomer(I('post.customer_id', '', 'trim'));

            // 更新
            $this->_customer_model->updateCustomerByIdForPost();


            //修改操作人
            $id = I('post.id', '', 'trim');
            $this->_customer_model->updateAdminInfo($id, $this->loginuser['username']);

            $this->__Return('更新客户成功', '', 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

//    /**
//     * 客户管理编辑页面重置密码功能
//     */
//    public function resetPwd()
//    {
//        if (!IS_POST) {
//            $id = I('get.id', 0, 'intval');
//
//            $this->assign('id', $id);
//            $this->display();
//        } else {
//            try {
//                $id = I('post.id', 0, 'intval');
//                $this->_customer_model->updatePwdById($id);
//            } catch (\Exception $e) {
//                $this->__Return($e->getMessage());
//            }
//            $this->__Return('操作成功', '', 'tip_success');
//        }
//    }

    /**
     * 客户添加成功显示页面
     */
    public function addSuccess()
    {
        $id = I('get.customer_id', 0, 'intval');

        $this->assign('id', $id);
        $this->display();
    }

//    /**
//     * 增加计费配置
//     **/
//    protected function addFeeConfig()
//    {
//        $repository = new FeeConfigRepository();
//        if (IS_POST) {
//            try {
//                //数据权限校验
//                DataAuthController::instance()
//                    ->validAllowDoCustomer(I('post.customer_id', '', 'trim'));
//                //增加计费配置
//                $repository->add();
//                $data = [
//                    'code' => 0,
//                    'msg' => '',
//                    'data' => [],
//                ];
//            } catch (\Exception $e) {
//                $data = [
//                    'code' => 100,
//                    'msg' => $e->getMessage(),
//                    'data' => [],
//                ];
//            }
//            $this->ajaxReturn($data);
//
//            return;
//        }
//        try {
//            //数据权限校验
//            DataAuthController::instance()
//                ->validAllowDoCustomer(I('get.customer_id', '', 'trim'));
//            $assign = $repository->add_html();
//        } catch (\Exception $e) {
//            $this->error($e->getMessage(), urldecode(I('get.callback_url')));
//        }
//        $this->assign($assign);
//        $this->display('add_fee_config');
//    }

//    /**
//     * 编辑计费配置
//     **/
//    protected function editFeeConfig()
//    {
//        $repository = new FeeConfigRepository();
//        if (IS_POST) {
//            try {
//                $repository->edit();
//                $data = [
//                    'code' => 0,
//                    'msg' => '',
//                    'data' => [],
//                ];
//            } catch (\Exception $e) {
//                $data = [
//                    'code' => 100,
//                    'msg' => $e->getMessage(),
//                    'data' => [],
//                ];
//            }
//            $this->ajaxReturn($data);
//
//            return;
//        }
//        try {
//            $assign = $repository->edit_html();
//        } catch (\Exception $e) {
//            $this->error($e->getMessage(), urldecode(I('get.callback_url')));
//        }
//        $this->assign($assign);
//        $this->display('edit_fee_config');
//    }

//    /**
//     * 删除计费配置
//     **/
//    protected function deleteFeeConfig()
//    {
//        $repository = new FeeConfigRepository();
//        if (IS_POST) {
//            try {
//                $repository->del();
//                $data = [
//                    'status' => 'success',
//                    'info' => '删除成功',
//                    'data' => [],
//                ];
//            } catch (\Exception $e) {
//                $data = [
//                    'status' => 'error',
//                    'info' => $e->getMessage(),
//                    'data' => [],
//                ];
//            }
//            $this->ajaxReturn($data);
//
//            return;
//        }
//        $this->redirect('index');
//    }

//    /**
//     * 计费配置
//     */
//    public function feeConfig()
//    {
//        $type = I('get.type', '', 'trim');
//        if ($type == 'add') {
//            return $this->addFeeConfig();
//        } else if ($type == 'edit') {
//            return $this->editFeeConfig();
//        } else if ($type == 'delete') {
//            return $this->deleteFeeConfig();
//        }
//        $this->redirect('index');
        /*
                $id = I('get.id', 0, 'trim');
                if (IS_POST) {
                    try {
                        $res = $this->customerFeeRepository->saveFeeConfig($id);
                        if ($res) {
                            $this->__Return('计费配置保存成功', '', 'success');
                        }
                        $this->__Return('计费配置保存失败', '', 'error');
                    } catch (\Exception $e) {
                        $this->__Return($e->getMessage(), '', 'error');
                    }
                }

                try {
                    $edit = I('get.edit', '', 'trim');
                    if ($edit) {
                        if (I('get.product_id') == 210) {
                            //获取所有的配置数据
                            $feeData = (new FeeConfigModel())->where(['id' => ['in', $id]])->select();
                            if (empty($feeData)) {
                                throw new \Exception('计费数据不存在');
                            }
                            //已存在的数据设置
                            $fee_info = [
                                'product_id'    => '210',
                                'fee_basis'     => $feeData[0]['fee_basis'],
                                'fee_method'    => $feeData[0]['fee_method'],
                                'fee_time_rule' => $feeData[0]['fee_time_rule'],
                                'fee_amount_rule' => $feeData[0]['fee_amount_rule'],
                                'fee_step_rule' => $feeData[0]['fee_step_rule'],
                                'fee_price_rule'=> $feeData[0]['fee_price_rule'],
                                'fee_price'     => $feeData[0]['fee_price'],
                                'start_date'    => $feeData[0]['start_date'],
                                'remarks'       => $feeData[0]['remarks'],
                                'id'            => $id
                            ];
                            $customer_id = $feeData[0]['customer_id'];
                            $account_id = $feeData[0]['account_id'];
                            $product_list =
                            $customer_info = $this->_customer_model->where(compact('customer_id'))->field('customer_id, name')->find();
                            $product_json = json_encode($product_list);
                            // 计费方式
                            $fee_method = D('FeeConfig')->getFeeMethod();
                            // 时间计费规则
                            $fee_time_rule = D('FeeConfig')->getFeeTimeRule();
                            // 用量计费规则
                            $fee_amount_rule = D('FeeConfig')->getFeeAmountRule();
                            // 阶梯计费规则
                            $fee_step_rule = D('FeeConfig')->getFeeStepRule();
                            //价格规则
                            $fee_price_rule = D('FeeConfig')->getFeePriceRule();
                            //运营商规则
                            $fee_flow_rule = D('FeeConfig')->getFeeFlowRule();
                            $fee_flow_rule_json = json_encode($fee_flow_rule);
                            $this->assign(compact('customer_id', 'account_id', 'product_list', 'product_json', 'customer_info',
                                'fee_method', 'fee_time_rule', 'fee_amount_rule', 'fee_step_rule', 'fee_price_rule', 'fee_flow_rule', 'fee_flow_rule_json', 'fee_info'));
                            $this->display();
                            die;

                            $account_id = I('get.account_id', '', 'trim');
                            $product_id = I('get.product_id', '', 'trim');
                            $product_info = I('get.product_list', '', 'trim');
                            halt($id);
                            $customer_info = $this->_customer_model->getCustomerById($id, 'customer_id, name');
                            if (!$customer_info) {
                                throw new \Exception('不存在该客户');
                            }
                            $product_list = $this->customerFeeRepository->getProductList($customer_info);
                            $fee_info = [];
                            if ($account_id && $product_id) {
                                $fee_info = $this->customerFeeRepository->getFeeConfigInfo($customer_info);
                                $this->assign('f_id', implode(',', $fee_info['fee_id']));
                            }
                            // 邦秒验产品
                            $data_check_list = $this->customerFeeRepository->getSubProductList(200);
                            // 计费方式
                            $fee_method = D('FeeConfig')->getFeeMethod();
                            // 时间计费规则
                            $fee_time_rule = D('FeeConfig')->getFeeTimeRule();
                            // 用量计费规则
                            $fee_amount_rule = D('FeeConfig')->getFeeAmountRule();
                            // 阶梯计费规则
                            $fee_step_rule = D('FeeConfig')->getFeeStepRule();
                            //价格规则
                            $fee_price_rule = D('FeeConfig')->getFeePriceRule();
                            //运营商规则
                            $fee_flow_rule = D('FeeConfig')->getFeeFlowRule();

                            $this->assign('id', $id);
                            $this->assign('product_id', $product_id);
                            $this->assign('account_id', $account_id);
                            $this->assign('fee_info', $fee_info);
                            $this->assign('customer_info', $customer_info);
                            $this->assign('product_list', $product_list);
                            $this->assign('product_json', json_encode($product_list));
                            $this->assign('fee_method', $fee_method);
                            $this->assign('fee_time_rule', $fee_time_rule);
                            $this->assign('fee_amount_rule', $fee_amount_rule);
                            $this->assign('fee_step_rule', $fee_step_rule);
                            $this->assign('fee_price_rule', $fee_price_rule);
                            $this->assign('fee_flow_rule', $fee_flow_rule);
                            $this->assign('fee_flow_rule_json', json_encode($fee_flow_rule));
                            $this->assign('data_check_list', $data_check_list);
                            $this->assign('data_check_list_json', json_encode($data_check_list));



                            return $this->display();
                        } else {
                            $f_id = $id;
                            $data = (new FeeConfigModel())->find($f_id);
                            if (empty($data)) {
                                throw new \Exception('不存在该条数据');
                            }
                            /*if (strtotime($data['start_date'])<strtotime(date('Y-m'))) {
                                throw new \Exception('该条数据不可编辑');
                            }
                            $account_id = [$data['account_id']];
                            $product_id = $data['product_id'];
                            $customer_id = $data['customer_id'];
                            $customer_info = $this->_customer_model->where(compact('customer_id'))->field('name, customer_id')->find();
                            if (!$customer_info) {
                                throw new \Exception('不存在该客户');
                            }
                            $product_list = $this->customerFeeRepository->getProductList($customer_info);

                            $data['fee_price'] = @json_decode($data['fee_price'], true)?:$data['fee_price'];
                            $fee_info = $data;
                            //$fee_info = $this->customerFeeRepository->getFeeConfigInfo($customer_info);

                            $this->assign('f_id', $f_id);
                        }
                    } else {
                        $account_id = I('get.account_id', '', 'trim');
                        $product_id = I('get.product_id', '', 'trim');
                        $customer_info = $this->_customer_model->getCustomerById($id, 'customer_id, name');
                        if (!$customer_info) {
                            throw new \Exception('不存在该客户');
                        }
                        $product_list = $this->customerFeeRepository->getProductList($customer_info);
                        $fee_info = [];
                        if ($account_id && $product_id) {
                            $fee_info = $this->customerFeeRepository->getFeeConfigInfo($customer_info);
                        }
                    }
                    // 邦秒验产品
                    $data_check_list = $this->customerFeeRepository->getSubProductList(200);
                    // 计费方式
                    $fee_method = D('FeeConfig')->getFeeMethod();
                    // 时间计费规则
                    $fee_time_rule = D('FeeConfig')->getFeeTimeRule();
                    // 用量计费规则
                    $fee_amount_rule = D('FeeConfig')->getFeeAmountRule();
                    // 阶梯计费规则
                    $fee_step_rule = D('FeeConfig')->getFeeStepRule();
                    //价格规则
                    $fee_price_rule = D('FeeConfig')->getFeePriceRule();
                    //运营商规则
                    $fee_flow_rule = D('FeeConfig')->getFeeFlowRule();

                    $this->assign('id', $id);
                    $this->assign('product_id', $product_id);
                    $this->assign('account_id', json_encode($account_id));
                    $this->assign('fee_info', $fee_info);
                    $this->assign('customer_info', $customer_info);
                    $this->assign('product_list', $product_list);
                    $this->assign('product_json', json_encode($product_list));
                    $this->assign('fee_method', $fee_method);
                    $this->assign('fee_time_rule', $fee_time_rule);
                    $this->assign('fee_amount_rule', $fee_amount_rule);
                    $this->assign('fee_step_rule', $fee_step_rule);
                    $this->assign('fee_price_rule', $fee_price_rule);
                    $this->assign('fee_flow_rule', $fee_flow_rule);
                    $this->assign('fee_flow_rule_json', json_encode($fee_flow_rule));
                    $this->assign('data_check_list', $data_check_list);
                    $this->assign('data_check_list_json', json_encode($data_check_list));
                    $this->display();
                } catch (\Exception $e) {
                    $this->__Return($e->getMessage());
                }*/
//    }

//    public function delFeeConfig()
//    {
        //        try {
        //            $id = I('post.id', 0, 'trim');
        //            $res = (new FeeConfigModel())->where(compact('id'))->save(['is_delete' => 1]);
        //            if ($res) {
        //                $this->__Return('删除成功', '', 'success');
        //            }
        //            $this->__Return('删除失败', '', 'error');
        //        } catch (\Exception $e) {
        //            $this->__Return($e->getMessage(), '', 'error');
        //        }
//    }

//    /**
//     * 计费配置导出
//     * @return
//     */
//    public function feeConfigDownload()
//    {
//        $this->customerFeeRepository->getCustomerFeeConfigDownload();
//        exit;
//    }

    /*
     * 获取所有产品的列表(以产品编号为键名)
     *
     * @access private
     * @return array
     */
    private function getProductList()
    {
        $productData = $this->product_model->getProductListByWhere([], 'product_id,product_name');

        return array_column($productData, 'product_name', 'product_id');
    }


    private function getGroupList() {
        $productData = $this->group_model->getGroupListByWhere([]);
        return array_column($productData, 'group_name', 'group_id');
    }


    /**
     * excel文件生成直接下载
     * @param $filename string 文件名称
     * @param $data     array 数据
     *
     * @return void
     **<AUTHOR>
     * @datetime 15:57 2018/12/7
     *
     * @access   private
     *
     */
    function downloadFile($filename, $data)
    {
        include LIB_PATH . 'Org/PHPExcel/PHPExcel.php';
        $companyTypeRepository = new CompanyTypeRepository();
        $companyTypeData = $companyTypeRepository->getTwiceTypeData();
        $objPHPExcel = new \PHPExcel();
        $cellKey = [
            'A',
            'B',
            'C',
            'D',
            'E',
            'F',
            'G',
            'H',
            'I',
            'J',
            'K',
            'L',
            'M',
            'N',
            'O',
            'P',
            'Q',
            'R',
            'S',
            'T',
            'U',
            'V',
            'W',
            'X',
            'Y',
            'Z',
            'AA',
            'AB',
            'AC',
            'AD',
            'AE',
            'AF',
            'AG',
            'AH',
            'AI',
            'AJ',
            'AK',
            'AL',
            'AM',
            'AN',
            'AO',
            'AP',
            'AQ',
            'AR',
            'AS',
            'AT',
            'AU',
            'AV',
            'AW',
            'AX',
            'AY',
            'AZ',
        ];
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('A')
            ->setWidth(14);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('B')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('C')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('D')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('E')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('F')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('G')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('H')
            ->setWidth(20);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('I')
            ->setWidth(14);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('J')
            ->setWidth(16);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('K')
            ->setWidth(16);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('L')
            ->setWidth(14);
        $objPHPExcel->getActiveSheet()
            ->getColumnDimension('M')
            ->setWidth(14);
        $objPHPExcel->getActiveSheet()
            ->getStyle('A1:M1')
            ->getFont()
            ->setBold(true);
        //处理表头
        $titleName = [
            '客户ID',
            '客户名称',
            '公司名称-羽乐科技',
            '公司名称-朴道',
            '公司名称-浙数交',
            '公司名称-郑数交',
            '公司类型',
            '付款类型',
            '状态',
            '已开通账号',
            '已开通产品',
            '运营跟进人',
            '商务跟进人',
            '商务推荐人',
            '创建时间',
            '操作人',
            '客户类型',
            '是否为渠道客户',
            '对账周期',
            '主体',
            '区域',
            '是否羽乐科技签约',
            '是否朴道签约',
            '是否渐数交签约',
            '是否郑数交签约',
            '客户级别1(规模)',
            '客户级别2(收入)',
            '客户级别3(规模+收入)',
        ];
        $row = 1;
        $col = 0;
        foreach ($titleName as $item) {
            $objPHPExcel->getActiveSheet()
                ->setCellValue($cellKey[$col] . $row, $item);
            $col++;
        }
        $row++;
        //获取用户数据
        $user_data = (new SystemUserModel())->field('username,realname,dept_id')
            ->where(DataAuthController::instance()
                ->getUserWhere())
            ->select();

        $dept_list = SystemDeptModel::getDeptsInfo();
        $user_dept_map = [];
        foreach($user_data as $user_info) {
            if (!empty($user_info['dept_id'])) {
                $user_dept_map[$user_info['username']] = $dept_list[$user_info['dept_id']]['dept_name'];
            }
        }

        $user_data = array_column($user_data, 'realname', 'username');

        $customer_type_map = array_flip(CustomerModel::$customerType);
        $reconciliation_cycle_map = array_flip(CustomerModel::$reconciliationCycle);
        $sign_type_map = array_flip(CustomerModel::$signType);

        $group_list = $this->getGroupList();

        $sheet = $objPHPExcel->getActiveSheet();
        foreach ($data as $item) {
            $sign_type_arr = array_flip(explode(',',$item['sign_type']));

            $sign_type_is_dhb = isset($sign_type_arr[0]) ? '是' : '否';
            $sign_type_is_pd = isset($sign_type_arr[1]) ? '是' : '否';
            $sign_type_is_veuj = isset($sign_type_arr[10]) ? '是' : '否';
            $sign_type_is_vguj = isset($sign_type_arr[2]) ? '是' : '否';

            if(empty($item['agent_company'])){
                $agent_company = [];
            }else{
                $agent_company = json_decode($item['agent_company'], true);
            }

            $sheet->setCellValue('A' . $row, $item['customer_id']);
            $sheet->setCellValue('B' . $row, $item['name']);
            $sheet->setCellValue('C' . $row, $item['company']);
            $sheet->setCellValue('D' . $row, isset($agent_company[1]) ? $agent_company[1] : '');
            $sheet->setCellValue('E' . $row, isset($agent_company[10]) ? $agent_company[10] : '');
            $sheet->setCellValue('F' . $row, isset($agent_company[2]) ? $agent_company[2] : '');

            $sheet->setCellValue('G' . $row, $companyTypeData[$item['type']]);
            $sheet->setCellValue('H' . $row, $item['payment_type'] == 1 ? '预付费' : '后付费');
            $sheet->setCellValue('I' . $row, $item['status'] ? '可用' : '禁用');
            $sheet->setCellValue('J' . $row, implode(' | ', array_keys(array_flip($item['account']))));
            $sheet->setCellValue('K' . $row, implode(' | ', array_keys(array_flip($item['product']))));
            $sheet->setCellValue('L' . $row, $user_data[$item['operator']]);
            $sheet->setCellValue('M' . $row, $user_data[$item['salesman']]);
            $sheet->setCellValue('N' . $row, $user_data[$item['introduce_salesman']]);
            $sheet->setCellValue('O' . $row, date('Y-m-d H:i', $item['create_at']));
            $sheet->setCellValue('P' . $row, $user_data[$item['admin']]);
            $sheet->setCellValue('Q' . $row, $customer_type_map[$item['customer_type']]);
            $sheet->setCellValue('R' . $row, $item['channel_mode'] == 1 ? '渠道客户' : '非渠道客户');
            $sheet->setCellValue('S' . $row, $reconciliation_cycle_map[$item['reconciliation_cycle']]);
            $sheet->setCellValue('T' . $row, $group_list[$item['group_id']]);
            $sheet->setCellValue('U' . $row, $user_dept_map[$item['salesman']]);
            $sheet->setCellValue('V' . $row, $sign_type_is_dhb);
            $sheet->setCellValue('W' . $row, $sign_type_is_pd);
            $sheet->setCellValue('X' . $row, $sign_type_is_veuj);
            $sheet->setCellValue('Y' . $row, $sign_type_is_vguj);
            $sheet->setCellValue('Z' . $row, $item['level_scale']);
            $sheet->setCellValue('AA' . $row, $item['level_income'] ?: '');
            $sheet->setCellValue('AB' . $row, $item['level_scale_income']);
            $row++;
        }
        $objPHPExcel->createSheet();
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        header('Content-Type:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition:attachment;filename="' . $filename . '.xls"');
        header('Cache-Control:max-age=0');
        $objWriter->save('php://output');
        die;
    }

    /**
     * 删除客户
     * <AUTHOR>
     * @datetime 14:56 2018/12/21
     **/
    public function delete()
    {
        try {
            $customer_id = I('post.customer_id');

            //数据权限校验
            DataAuthController::instance()
                ->validAllowDoCustomer($customer_id);

            $this->_customer_model->deleteCustomerAndAccountByWhere([compact('customer_id')]);
            $log_info = [
                'handle_type' => 'delete',
                'description' => '删除客户',
                'content' => compact('customer_id'),
                'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: "system",
                'handle_result' => 1,
            ];
            HandlerLog::log($log_info);
            $this->ajaxReturn(['status' => 0]);
        } catch (\Exception $exception) {
            $this->ajaxReturn([
                'status' => 100,
                'message' => $exception->getMessage(),
            ]);
        }

    }

    /**
     * 一键变更
     *
     * @access public
     *
     * @return void
     **/
    public function onceButtonUpdate()
    {
        try {
            $repository = new BackendCustomerRepository();
            $response = $repository->upgradeAtOnce();
            $this->response(compact('response'));
        } catch (\Exception $e) {
            $this->setStatus(1478)
                ->responseError($e->getMessage());
        }
    }

    //编辑特殊消耗
//    public function editExpend()
//    {
//        $product_list = (new ProductModel())->field(['product_id', 'product_name'])
//            ->select();
//        $productInfo = array_column($product_list, 'product_name', 'product_id');
//        if (IS_GET) {
//            $id = I('get.id');
//            if (empty($id)) {
//                $this->redirect('Home/Index/index');
//            }
//            $data = (new CustomerExpendModel())->find($id);
//            if (empty($data)) {
//                $this->redirect('Home/Index/index');
//            }
//
//            $this->assign('data', $data);
//            $this->assign('product_options', makeOption($productInfo, $data['product_id']));
//
//            $this->display('');
//        } else {
//            try {
//                $data = [
//                    'start_date' => str_replace('-', '', I('post.start_date')),
//                    'name' => I('post.name'),
//                    'money' => I('post.money'),
//                    'type' => I('post.type'),
//                    'profile_show_date' => I('post.profile_show_date'),
//                    'product_id' => I('post.product_id'),
//                    'remark' => $productInfo[I('post.product_id')],
//                    'fee_number' => empty(I('post.fee_number')) ? 0 : I('post.fee_number'),
//                    'update_time' => time(),
//                ];
//                $data['id'] = I('post.id');
//
//                (new CustomerExpendModel())->save($data);
//                $this->response(['status' => 'success', 'info' => '保存成功']);
//            } catch (\Exception $e) {
//                $this->setStatus(1478)
//                    ->responseError($e->getMessage());
//            }
//
//        }
//    }

    //删除特殊费用
//    public function delExpend()
//    {
//        $id = I('get.id');
//        (new CustomerExpendModel())->where(compact('id'))
//            ->delete();
//
//        $this->redirect(I('get.callback_url'));
//    }
}
