<?php

namespace Account\Controller;
use Common\Controller\AdminController;

/**
 * 合同管理
 */
class ContractController extends AdminController
{

    /**
     * 合同管理
     */
    public function index(){
        $pdf_url_prefix = C('CONTRACT_API_DOMAIN');
        $this->assign('pdf_url_prefix',  $pdf_url_prefix);
        $this->display();
    }


    public function pdfview(){
        $pdfurl = I('get.pdfurl', '', 'trim');
        $contract_no = I('get.contract_no', '', 'trim');

        $pdfurl = C('CONTRACT_API_DOMAIN')."/".$pdfurl;

        $this->assign('pdfurl',  $pdfurl);
        $this->assign('contract_no', $contract_no);
        $this->display();
    }
}
