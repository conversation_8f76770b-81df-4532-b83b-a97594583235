<?php

namespace Account\Controller;

use Account\Model\AccountModel;
use Account\Model\CustomerGroupModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Account\Model\AccountProductModel;
use Account\Repositories\AccountRepository;
use Common\Common\HandlerLog;
use Common\Common\ResponseTrait;
use Common\Controller\AdminController;
use Common\Controller\DataAuthController;
use Common\Model\SystemUserModel;

class AccountController extends AdminController
{
    use ResponseTrait;
    /*
     * 客户Repository
     * */
    private $_account_model;
    protected $repository_account;

    public function __construct()
    {
        parent::__construct();
        $this->_account_model = (new AccountModel());
        $this->repository_account = new AccountRepository();
    }

    /**
     * 账号列表
     * <AUTHOR>
     * @datetime 9:54 2018/12/19
     **/
    public function index()
    {
        //获取列表数据
        $data = $this->repository_account->getDataForList();

        $this->assign('data', $data);

        //拉去所有用户
        $user_data = (new SystemUserModel())->field('username,realname')->select();
        $this->assign('user_data', array_column($user_data, 'realname', 'username'));


        $group_model = new CustomerGroupModel();
        $this->assign('group', array_column($group_model->groupListForGet(), 'group_name', 'group_id'));

        //拉取所有的客户
        $customer_model = new CustomerModel();
        $this->assign('customer', array_column($customer_model->customerListForGet(), 'name', 'customer_id'));
        $this->assign('customer_addAccount', array_column($customer_model->customerListForGet(), 'name', 'id'));
        //拉取所有账号
        $this->assign('account', array_column($this->_account_model->accountListForGet(), 'account_name', 'account_id'));

        //拉取所有产品
        $product_model = new ProductModel();
        $product_data = $product_model->getProductListByWhere([], 'product_id,product_name');
        $this->assign('product', array_column($product_data, 'product_name', 'product_id'));
        $this->display();
    }

    /**
     * 账号列表导出功能
     * <AUTHOR>
     * @datetime 15:22 2018/12/19
     **/
    public function export()
    {
        ini_set('max_execution_time', 0);

        //获取导出数据
        $data = $this->repository_account->getDataForExport();
        //获取一个Excel对象
        $excel = $this->repository_account->getExcelObj();
        //设置导出文件的头
        $this->repository_account->setExcelHeader($excel);
        //增加数据
        $this->repository_account->setExcelData($excel, $data['data']);
        //下载文件
        $this->repository_account->downloadExcel($excel);
    }

    /**
     * 创建账号
     */
    public function add()
    {
        // 创建账号页面
        if (IS_GET) {
            try {
                $id = I('get.id', '', 'trim');
                if (empty($id)) {
                    $this->redirect('Account/Customer/index');
                }
                $customer_model = new CustomerModel();
                $customer_info = $customer_model->getCustomerForEdit();

                //数据权限校验
                DataAuthController::instance()->validAllowDoCustomer($customer_info['customer_id']);

                $this->assign('customer_info', $customer_info);
                $this->display();
                exit();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), I('get.callback_url', U('index'), 'urldecode'));
            }
        }
        // 创建账号
        try {
            //数据权限校验
            DataAuthController::instance()->validAllowDoCustomer(I('post.customer_id', '', 'trim'));

            $id = $this->_account_model->createAccountByForm();
            //修改操作人
            $this->_account_model->updateAdminInfo($id, $this->loginuser['username']);
            $this->__Return('添加客户成功', ['account_id' => $id], 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 编辑页面
     */
    public function edit()
    {
        if (IS_GET) {
            try {
                $id = I('get.id', 0, 'intval');
                // 通过ID,GET的方式获取客户的信息
                $account_info = $this->_account_model->getAccountInfoById($id);

                $this->assign('account_info', $account_info);
                $this->display();
                exit();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), I('get.callback_url', U('index'), 'urldecode'));
            }
        }

        // 更新
        try {
            //数据权限校验
            DataAuthController::instance()->validAllowDoCustomer(I('post.customer_id', '', 'trim'));

            $this->_account_model->updateAccountByIdForPost();

            //修改操作人
            $id = I('post.id', '', 'trim');
            $this->_account_model->updateAdminInfo($id, $this->loginuser['username']);

            $this->__Return('更新客户成功', '', 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 客户管理编辑页面重置密码功能
     */
    public function accountInfo()
    {
        $id = I('get.id', 0, 'intval');
        $account_info = $this->_account_model->getAccountInfoById($id);

        // 开通的产品
        $account_product_model = new AccountProductModel();
        $account_product_name_list = $account_product_model->getProductNameListByAccountId($account_info['account_id']);

        // 格式化的操作
        $account_product_name_list = $this->formatProductForAccount($account_product_name_list);

        $this->assign('account_info', $account_info);
        $this->assign('account_product_name_list', $account_product_name_list);
        $this->display();
    }

    /**
     * 格式化的操作
     * @param array $list_products
     * @return array
     */
    private function formatProductForAccount(array $list_products)
    {
        $list_product_status = [
            0 => '禁用',
            1 => '可用',
        ];

        $list_contract_status = [
            1 => '已签约已付款',
            2 => '已签约未付费',
            3 => '未签约',
            4 => '其他',
            5 => '特殊客户'
        ];

        return array_map(function ($product) use ($list_product_status, $list_contract_status) {
            $product['product_status_zh'] = $list_product_status[$product['status']];
            $product['contract_status_zh'] = $list_contract_status[$product['contract_status']];
            $product['end_time_zh'] = date('Y-m-d', $product['end_time']);
            $product['limit_zh'] = $product['daily_limit'] . '/' . $product['month_limit'] . '/' . $product['year_limit'] . '/' . $product['total_limit'];

            return $product;
        }, $list_products);

    }

//    /**
//     * 账号管理编辑页面重置密码功能
//     */
//    public function resetPwd()
//    {
//        if (!IS_POST) {
//            $id = I('get.id', 0, 'intval');
//
//            $this->assign('id', $id);
//            $this->display();
//        } else {
//            try {
//                $id = I('post.id', 0, 'intval');
//                $this->_account_model->updatePwdById($id);
//            } catch (\Exception $e) {
//                $this->__Return($e->getMessage());
//            }
//            $this->__Return('操作成功', '', 'tip_success');
//        }
//    }

    /**
     * 账号绑定产品
     */
    public function addProduct()
    {
        // View
        if (IS_GET) {
            try {
                $data_ini = $this->repository_account->addProduct();
                $this->assign($data_ini);
                $this->display();
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }
            exit();
        }

        // action
        try {
            $this->repository_account->storeProduct();
            $msg = '产品开通成功，是否继续开通产品？';
            $this->response(compact('msg'));
        } catch (\Exception $e) {
            $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 编辑账号绑定产品(view)
     */
    public function editProduct()
    {
        try {
            $edit_info = $this->repository_account->editProduct();
            $this->assign($edit_info);
            $this->display();
        } catch (\Exception $e) {
            $this->__Return($e->getMessage());
        }
    }

    /**
     * 账号添加成功显示页面
     */
    public function addSuccess()
    {
        $id = I('get.account_id', 0, 'intval');

        $this->assign('id', $id);
        $this->display();
    }

    /**
     * 删除账号
     * <AUTHOR>
     * @datetime 16:41 2018/12/21
     **/
    public function delete()
    {
        try {
            $id = I('post.id');

            $account_data = $this->_account_model->find($id);
            //数据权限校验
            DataAuthController::instance()->validAllowDoCustomer($account_data['customer_id']);

            $res = $this->_account_model->deleteAccountByWhere([
                compact('id')
            ]);
            $log_info = [
                'handle_type' => 'delete',
                'description' => '删除账号',
                'content' => compact('id'),
                'handle_user' => session(C('LOGIN_SESSION_NAME')) ?: "system",
                'handle_result' => 1
            ];
            HandlerLog::log($log_info);
            $this->ajaxReturn(compact('res'));
        } catch (\Exception $exception) {
            $this->ajaxReturn(['res' => 0]);
        }
    }

    public function limit()
    {
        $this->display();
    }

    public function changeSource()
    {
        $account_id = I('get.account_id');
        $this->assign('account_id', $account_id);
        $this->display();
    }
}
