<?php
namespace Account\Controller;
use Account\Repositories\ProductRepository;
use Common\Controller\AdminController;
use Account\Model\ProductModel;

class ProductController extends AdminController {
    private $_model_product;
    private $repository;

    public function __construct()
    {
        parent::__construct();
        $this->_model_product = (new ProductModel());
        $this->repository = new ProductRepository();
    }

	//默认入口
	public function index() {
        // GET方式获取客户信息 && 产品信息
        $list_product = $this->repository->getDataForList();
        // GET 方法的参数
        $request = I('get.');
        $this->assign('list_product', $list_product);
        $this->assign('request', $request);
        $this->display();
	}

    /**
     * 添加产品
     */
	public function add(){
        // 创建客户页面
        if (IS_GET) {
            $father_id = 0;
            $this->assign('productData', $this->repository->getDataForSelect(compact('father_id')));
            $this->display();
            exit();
        }
        try {
            $this->repository->add($this->loginuser['username']);
            $this->__Return('添加产品成功', '', 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }

    /**
     * 产品编辑页面
     */
    public function edit()
    {
        // 编辑界面
        if (IS_GET) {
            // 通过ID,GET的方式获取客户的信息 && 一对多的关系
            $product = $this->repository->getDataForEdit();
            $this->assign('product', $product);
            if ($product['children_count']==0) {
                $this->assign('productData', $this->repository->getDataForSelect([
                    'father_id' => 0,
                    'product_id'    => ['neq', I('get.product_id', '', 'trim')]
                ]));
            }
            $this->display();
            exit();
        }
        // 更新
        try {
            $this->repository->edit($this->loginuser['username']);
            $this->__Return('更新客户成功','' , 'success');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
            $this->__Return($msg, '', 'error');
        }
    }
}
