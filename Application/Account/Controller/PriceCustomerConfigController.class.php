<?php

namespace Account\Controller;

use Account\Repositories\PriceCustomerConfigRepository;
use Common\Controller\AdminController;

/**
 * Class PriceCustomerConfigController 客户单价配置
 * @package Account\Controller
 */
class PriceCustomerConfigController extends AdminController
{
	/**
	 * @var PriceCustomerConfigRepository 代码仓库
	 */
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new PriceCustomerConfigRepository();
	}
	
	/**
	 * 列表页
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/4 11:33
	 *
	 * @return void
	 */
	public function index()
	{
		//ajax获取列表数据
		$this->repository->getListData();
		
		$customer_id = I('get.customer_id');
		$this->assign('customer_id', $customer_id);
		$this->display('');
		
	}

	//计费配置导出下载
    public function feeConfigDown()
    {
        $input = file_get_contents('php://input');
        $params = json_decode($input, true, JSON_UNESCAPED_UNICODE);
        //ajax获取列表数据
        $this->repository->exportListData($params);

        exit;

    }
	
	/**
	 * 增肌页
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/4 14:19
	 *
	 * @return void
	 */
	public function add()
	{
		//执行添加计费配置功能
		$this->repository->runInsert();
		
		//获取增加页面展示的所需数据
		$this->repository->getInfoForAddHtml();
		
		$this->display('');
	}
	
	/**
	 * 编辑页面
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/17 18:25
	 *
	 * @return void
	 */
	public function edit()
	{
        $id = I('GET.copy_config_id');
        $this->assign('copy_config_id', $id);
        $this->display('');
	}
	
	/**
	 * 删除数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/18 15:38
	 *
	 * @param name string this is params
	 *
	 * @throws \Exception
	 *
	 * @return
	 */
	public function delete()
	{
		$this->repository->runDelete();
	}
}