<?php

namespace Account\Controller;

use Account\Model\AccountModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Account\Repositories\AccountRepository;
use Common\Common\ResponseTrait;
use Common\Controller\AdminController;
use Common\Model\SystemUserModel;

//售前测试账号管理
class TestAccountController extends AdminController
{
	use ResponseTrait;
	/*
	 * 客户Repository
	 * */
	private   $_account_model;
	protected $repository_account;
	
	public function __construct()
	{
		parent::__construct();
		$this->_account_model     = (new AccountModel());
		$this->repository_account = new AccountRepository();
	}
	
	/**
	 * 账号列表
	 * <AUTHOR>
	 * @datetime 9:54 2018/12/19
	 **/
	public function index()
	{
		//获取列表数据
		$data = $this->repository_account->getDataForList(true);
		
		$this->assign('data', $data);
		
		//拉取所有用户
		$user_data = (new SystemUserModel())->field('username,realname')
											->select();
		$this->assign('user_data', array_column($user_data, 'realname', 'username'));
		
		//拉取所有的客户
		$customer_model = new CustomerModel();
		$this->assign('customer', array_column($customer_model->customerListForGet(), 'name', 'customer_id'));
		$this->assign('customer_addAccount', array_column($customer_model->customerListForGet(), 'name', 'id'));
		//拉取所有账号
		$this->assign('account', array_column($this->_account_model->accountListForGet(), 'account_name', 'account_id'));
		
		//拉取所有产品
		$product_model = new ProductModel();
		$product_data  = $product_model->getProductListByWhere([], 'product_id,product_name');
		$this->assign('product', array_column($product_data, 'product_name', 'product_id'));
		$this->display();
	}
}