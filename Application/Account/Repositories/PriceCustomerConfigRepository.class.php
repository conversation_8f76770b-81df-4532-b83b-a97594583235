<?php

namespace Account\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\ConfigPriceCustomerModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Account\Model\ConfigOperatorModel;

/**
 * Class PriceCustomerConfigRepository 客户计费配置
 * @package Account\Repositories
 */
class PriceCustomerConfigRepository
{
	/**
	 * @var AccountModel 账号模型
	 */
	protected $accountModel;
	/**
	 * @var CustomerModel 客户模型
	 */
	protected $customerModel;
	/**
	 * @var AccountProductModel 账号产品中间表模型
	 */
	protected $accountProductModel;
	/**
	 * @var ProductModel 产品表模型
	 */
	protected $productModel;
	
	/**
	 * @var ConfigPriceCustomerModel
	 */
	protected $configPriceCustomerModel;
	
	/**
	 * @var array 计费配置通用配置项
	 */
	protected $price_config = [
		//计费类型配置
		'methods' => [
			0 => '终止计费',
			1 => '包年',
			2 => '固定价格',
			3 => '累进阶梯',
			4 => '到达阶梯',
		],
		//计费依据
		'accord'  => [
			1 => '成功调用量',
			2 => '查得量',
			3 => '计费量',
		],
		//多级产品计费模式
		'mode'    => [
			0 => '无',
			1 => '独立子产品',
			2 => '打包',
			3 => '汇总子产品',
		],
	];
	
	/**
	 * @var array 每个产品的计费配置项（临时的，后续重构配置模块时候在修改）
	 */
	protected $price_product_config = [
		//邦信分详单版V1
		101 => [
			//产品ID
			'product_id'     => 101,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//邦秒配详单版
		104 => [
			//产品ID
			'product_id'           => 104,
			//是否为多级产品
			'is_multiple'          => 0,
			//子产品IDs
			'children_product_ids' => [],
			//支持的计费配置类型
			'allow_methods'        => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator'       => [],
		],
		//邦信分详单版V2
		105 => [
			//产品ID
			'product_id'     => 105,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//邦秒验200
		200 => [
			//产品ID
			'product_id'     => 200,
			//是否为多级产品
			'is_multiple'    => 1,
			//支持的计费配置类型
			'allow_methods'  => [0, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [
				'CMCC' => '移动',
				'CUCC' => '联通',
				'CTCC' => '电信',
				'CBN' => '广电',
			],
		],
		//邦信分-通信字段
		210 => [
			//产品ID
			'product_id'     => 210,
			//是否为多级产品
			'is_multiple'    => 1,
			//支持的计费配置类型
			'allow_methods'  => [0, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [
				'PROCMCC' => '省移动',
				'CUCC'    => '联通',
				'CTCC'    => '电信',
			],
		],
		//邦企查
		401 => [
			//产品ID
			'product_id'     => 401,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//私有云
		501 => [
			//产品ID
			'product_id'     => 401,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//邦秒配单号版
		601 => [
			//产品ID
			'product_id'     => 401,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//批量版邦秒配
		604 => [
			//产品ID
			'product_id'     => 604,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//金盾贷前
		612 => [
			//产品ID
			'product_id'     => 612,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//金盾
		615 => [
			//产品ID
			'product_id'     => 615,
			//是否为多级产品
			'is_multiple'    => 1,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//风险符号
		616 => [
			//产品ID
			'product_id'     => 616,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//风险符号
		664 => [
			//产品ID
			'product_id'     => 664,
			//是否为多级产品
			'is_multiple'    => 0,
			//支持的计费配置类型
			'allow_methods'  => [0, 1, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [],
		],
		//邦信分-通信评分
		1000 => [
			//产品ID
			'product_id'     => 1000,
			//是否为多级产品
			'is_multiple'    => 1,
			//支持的计费配置类型
			'allow_methods'  => [0, 2, 3, 4],
			//支持的运营商
			'allow_operator' => [
				'ALLCMCC' => '全国移动',
				'PROCMCC' => '省移动',
				'CUCC'    => '联通',
				'CTCC'    => '电信',
			],
		],
        //号码分(废弃)
        1100 => [
            //产品ID
            'product_id'     => 1100,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
        //号码分
        10000 => [
            //产品ID
            'product_id'     => 10000,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
        //贷后风险指数
        20000 => [
            //产品ID
            'product_id'     => 20000,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
        //事件分
        30000 => [
            //产品ID
            'product_id'     => 30000,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
        //企服产品3100
        3100 => [
            //产品ID
            'product_id'     => 3100,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [
                'CMCC' => '移动',
                'CUCC' => '联通',
                'CTCC' => '电信',
            ],
        ],
        3000 => [
            //产品ID
            'product_id'     => 3000,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
        //金盾
        50000 => [
            //产品ID
            'product_id'     => 50000,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
        //号码融
        70000 => [
            //产品ID
            'product_id'     => 70000,
            //是否为多级产品
            'is_multiple'    => 1,
            //支持的计费配置类型
            'allow_methods'  => [0, 1, 2, 3, 4],
            //支持的运营商
            'allow_operator' => [],
        ],
	];

	protected $allow_operator = [
        'ALLCMCC' => '全国移动',
        'PROCMCC' => '省移动',
        'CMCC'    => '移动',
        'CUCC'    => '联通',
        'CTCC'    => '电信',
        'CBN'    => '广电',
    ];
	
	
	/**
	 * 获取增加页面的中的所需数据
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/4 14:06
	 *
	 * @return array
	 */
	public function getInfoForAddHtml()
	{
		if (!IS_POST || 'GET_ADD_CONFIG' != I('POST.request_type')) {
			return;
		}
		
		//获取为那个客户增加计费配置
		$customer_id = I('POST.customer_id');
		$config      = $this->checkCustomerStoreProduct($customer_id);
		
		//将这个配置项转为JSON格式并发送给JS脚本
		
		//获取客户名称
		$customer      = $this->getCustomerModel()
							  ->where(compact('customer_id'))
							  ->field(['name'])
							  ->find();
		$customer_name = $customer['name'];
		
		createJsonResponse(0, compact('config', 'customer_name', 'customer_id'));
	}
	
	/**
	 * 校验并获取该客户的所有开通的产品及关联账号，如果校验失败，返回字符串类型，如果成功，则返回数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/4 14:10
	 *
	 * @param $customer_id string 客户ID
	 *
	 * @return array
	 */
	protected function checkCustomerStoreProduct($customer_id)
	{
		$is_delete = 0;
		if (!$customer_id) {
			createJsonResponse(10002);
		}
		
		//查看该客户的状态
		$customerCount = $this->getCustomerModel()
							  ->where(compact('customer_id', 'is_delete'))
							  ->count();
		if (!$customerCount) {
			createJsonResponse(10003);
		}
		
		//查看该客户是否存在大于1个的账号(1个初始账号)
		$account = $this->getAccountModel()
						->field(['account_id', 'account_name'])
						->where(compact('customer_id', 'is_delete'))
						->select();

		if (count($account) < 1) {
			createJsonResponse(10004);
		}
		$account = array_column($account, 'account_name', 'account_id');
		
		//查看该客户开通的产品
		$accountProduct = $this->getAccountProductModel()
							   ->field(['product_id', 'account_id'])
							   ->where(['account_id' => ['in', array_keys($account)]])
							   ->select();
		if (0 == count($accountProduct)) {
			createJsonResponse(10005);
		}
		
		//获取这些产品的数据
		$product = $this->getProductModel()
						->where(['product_id' => ['in', array_column($accountProduct, 'product_id')]])
						->field(['product_id', 'product_name', 'father_id'])
						->select();
		
		//组合产品配置项
		$result = [];
		array_walk($product, function ($productItem) use (&$result) {
			$product_id   = $productItem['product_id'];
			$product_name = $productItem['product_name'];
			$father_id    = $productItem['father_id'];
			//子产品不可单独设置计费配置
            if ((0 != $father_id) && ($father_id != 401)) {
				return;
			}
			
			//查看价格产品配置中是否支持
			if (!array_key_exists($product_id, $this->price_product_config)) {
				return;
			}
			//整理计费配置的JSON数据
			$is_multiple    = $this->price_product_config[$product_id]['is_multiple'];
			$allow_methods  = $this->price_product_config[$product_id]['allow_methods'];
			$allow_operator = $this->price_product_config[$product_id]['allow_operator'];
			$config         = [
				'product_id'       => $product_id,
				'product_name'     => $product_name,
				//开通此产品的账号数据
				'account'          => [],
				//计费方式
				'methods'          => array_intersect_key($this->price_config['methods'], array_flip($allow_methods)),
				//区分运营商
				'operator'         => $allow_operator,
				//是否为多级产品
				'is_multiple'      => $is_multiple,
				//每个账号都有属于自己的子产品数据
				'children_product' => [],
			];
			
			$result[$product_id] = compact('product_id', 'product_name', 'config');
		});
		if (0 == count($result)) {
			return createJsonResponse(10006);
		}
		
		//获取所有产品与父产品对应的关系，并过滤掉父产品ID=0的数据
		$product2FatherIdMapping = $this->getProductModel()
										->field(['product_id', 'father_id'])
										->select();
		$product2FatherIdMapping = array_column($product2FatherIdMapping, 'father_id', 'product_id');
		$product2FatherIdMapping = array_filter($product2FatherIdMapping, function ($item) {
			return $item != 0;
		});
		
		
		//遍历账号_产品数据，将账号信息、子产品信息融合进结果表中
		$product = array_column($product, 'product_name', 'product_id');
		array_walk($accountProduct, function ($item) use (&$result, $account, $product2FatherIdMapping, $product) {
			$product_id   = $item['product_id'];
			$account_id   = $item['account_id'];
			$product_name = $product[$product_id];
			$account_name = $account[$account_id];
			$father_id    = array_key_exists($product_id, $product2FatherIdMapping) ? $product2FatherIdMapping[$product_id] : 0;
			
			
			//如果该产品ID既不在结果数据中，父产品也不在结果数据中，则直接丢弃
			if (!array_key_exists($product_id, $result) && !array_key_exists($father_id, $result)) {
				return;
			} else if (!array_key_exists($product_id, $result)) {
				//子产品
				$result[$father_id]['config']['children_product'][$account_id][$product_id] = $product_name;
				$result[$father_id]['config']['account'][$account_id]                       = $account_name;
			} else if (!array_key_exists($father_id, $result) || ($father_id == 401)) {
				//如果是父产品
				$result[$product_id]['config']['account'][$account_id] = $account_name;
				
			}
		});
		
		return $result;
	}
	
	
	/**
	 * 展示错误模板
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/4 14:15
	 *
	 * @param $message string 错误信息
	 *
	 * @return void
	 */
	protected function errorView($message)
	{
		header('Content-Type:text/html; charset=utf-8');
		echo <<<VIEW
<h1 style='width:100%;height:100px;line-height: 100px;color: #333333;text-align: center;'>{$message}</h1>
VIEW;
		die;
		
	}
	
	/**
	 * @return AccountModel
	 */
	protected function getAccountModel()
	{
		if (!$this->accountModel) {
			$this->accountModel = new AccountModel();
		}
		
		return $this->accountModel;
	}
	
	/**
	 * @return CustomerModel
	 */
	protected function getCustomerModel()
	{
		if (!$this->customerModel) {
			$this->customerModel = new CustomerModel();
		}
		
		return $this->customerModel;
	}
	
	/**
	 * @return AccountProductModel
	 */
	protected function getAccountProductModel()
	{
		if (!$this->accountProductModel) {
			$this->accountProductModel = new AccountProductModel();
		}
		
		return $this->accountProductModel;
	}
	
	/**
	 * @return ProductModel
	 */
	protected function getProductModel()
	{
		if (!$this->productModel) {
			$this->productModel = new ProductModel();
		}
		
		return $this->productModel;
	}
	
	/**
	 * @return ConfigPriceCustomerModel
	 */
	protected function getConfigPriceCustomerModel()
	{
		if (!$this->configPriceCustomerModel) {
			$this->configPriceCustomerModel = new ConfigPriceCustomerModel();
		}
		
		return $this->configPriceCustomerModel;
	}
	
	/**
	 * 执行插入计费配置
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/10 10:45
	 *
	 * @return void
	 */
	public function runInsert()
	{
		if (!IS_POST || 'RUN_INSERT' != I('POST.request_type')) {
			return;
		}
		
		//过滤一些特定的组合方式不支持
		$this->filterMethods();
		
		//是否为多级产品
		$is_multiple = I('post.is_multiple');
		if (1 == $is_multiple) {
			//多级产品计费
			$this->addMultipleProductPriceConfig();
		} else {
			//单独产品计费
			$this->addAloneProductPriceConfig();
		}
		
		createJsonResponse(0);
	}
	
	/**
	 *过滤一些计费组合的方式，不予支持
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/15 13:54
	 *
	 * @return void
	 */
	protected function filterMethods()
	{

	}
	
	/**
	 * 增加多级产品的计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/14 11:28
	 *
	 * @return void
	 */
	protected function addMultipleProductPriceConfig()
	{
		//APIKEY
		$account_id = I('post.account_id');
		$apikeys    = $this->getApikeysByAccountIds([$account_id]);
		$apikey     = array_shift($apikeys);
		
		//父产品ID
		$father_id = I('post.product_id');
		
		//当前账号开通的子产品
		$allSubProductIds = $this->getProductModel()
								 ->where(['father_id' => $father_id])
								 ->field(['product_id'])
								 ->select();
		$allSubProductIds = array_column($allSubProductIds, 'product_id');
		$product_ids      = $this->getAccountProductModel()
								 ->where(['product_id' => ['in', $allSubProductIds], 'account_id' => $account_id])
								 ->field(['product_id'])
								 ->select();
		$product_ids      = implode(',', array_column($product_ids, 'product_id'));
		
		//价格
		$price = $this->getCustomerPriceField()->getPrice();
        //去除空格处理
        $pattern = '/\s+/';//去除一个或多个空格
        $price = preg_replace($pattern, '', $price);
		
		$this->create(compact('product_ids', 'father_id', 'apikey', 'price'));
		
		//包年计费设置终止计费
		$this->autoCreateStopBillingConfig($apikey, $product_ids, $father_id);
	}
	
	/**
	 * 如果包年，则自动创建停止计费的配置项
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/11 17:18
	 *
	 * @apikey   string 账号唯一标识
	 *
	 * @param $product_ids string 产品ID
	 * @param $father_id   integer 父产品ID
	 *
	 * @return void
	 */
	protected function autoCreateStopBillingConfig($apikey, $product_ids, $father_id)
	{
		$methods = I('post.methods');
		if (1 != $methods) {
			return;
		}
		
		$is_package    = I('post.is_package', 0);
		$period        = 0;
		$diff_operator = 0;
		$price         = 0;
		$start_date    = I('post.start_date');
		$years         = I('post.years', 1);
		$start_date    = date('Ymd', strtotime("+{$years} years", strtotime($start_date)));
		$methods       = 0;
		$accord        = 1;
		$remark        = '系统自动生成的终止计费配置';
		$create_time   = $update_time = time();
		$data          = compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'is_package', 'price', 'start_date', 'create_time', 'update_time', 'remark');
		
		$this->getConfigPriceCustomerModel()
			 ->add($data);
	}
	
	/**
	 * 单级产品计费
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/10 14:54
	 *
	 * @return void
	 */
	protected function addAloneProductPriceConfig()
	{
		$account_ids = I('post.account_id');
		$apikeys     = $this->getApikeysByAccountIds($account_ids);
		
		$father_id = $product_ids = I('product_id');
		
		$price = $this->getCustomerPriceField()
					  ->getPrice();
        //去除空格处理
        $pattern = '/\s+/';//去除一个或多个空格
        $price = preg_replace($pattern, '', $price);
		
		array_walk($apikeys, function ($apikey) use ($price, $father_id, $product_ids) {
			$this->create(compact('apikey', 'price', 'father_id', 'product_ids'));
			
			$this->autoCreateStopBillingConfig($apikey, $product_ids, $father_id);
		});
	}
	
	/**
	 * 通过账号ID获取apikey数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/10 15:08
	 *
	 * @param $account_ids array 账号ID
	 *
	 * @return array
	 */
	protected function getApikeysByAccountIds($account_ids)
	{
		$data = $this->getAccountModel()
					 ->where(['account_id' => ['in', $account_ids]])
					 ->field(['apikey', 'account_id'])
					 ->select();
		
		return array_column($data, 'apikey', 'account_id');
	}
	
	/**
	 * 增加一条计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/10 15:52
	 *
	 * @param $data array 计费配置信息
	 *
	 * @return integer
	 */
	protected function create($data)
	{
		//补充一些数据
		
		//计费方式
		$data['methods'] = I('post.methods');
		
		//是否区分运营商
		$data['diff_operator'] = I('post.diff_operator', 0);
		
		//多级产品计费模式
		$data['mode'] = I('post.mode', 0);
		
		//计费开始时间
		$data['start_date'] = date('Ymd', strtotime(I('post.start_date')));
		
		//计费周期
		$data['period'] = I('post.period', 0);
		
		//备注信息
		$data['remark'] = I('post.remark', '');
		
		//操作人
		$data['admin'] = $_SESSION[C('LOGIN_SESSION_NAME')];
		
		//计费依据
		$data['accord'] = I('post.accord');
		
		//创建时间、更新时间
		$data['create_time'] = $data['update_time'] = time();
		
		return $this->getConfigPriceCustomerModel()
					->add($data);
	}
	
	/**
	 * 获取价格字段的整理对象
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/10 17:09
	 *
	 * @return CustomerPriceField
	 */
	protected function getCustomerPriceField()
	{
		return new CustomerPriceField();
	}
	
	
	/**
	 * 获取列表数据
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/14 11:38
	 *
	 * @return void
	 */
	public function getListData()
	{
		if (!IS_POST) {
			return;
		}
		//获取排序条件
		
		
		$customer_id = I('post.customer_id');
		
		
		$data = $this->getConfigPriceCustomerModel()
					 ->field([
						 'product_name',
						 'account_name',
						 'config_price_customer.*'
//						 'config_price_customer.id',
//						 'config_price_customer.diff_operator',
//						 'config_price_customer.mode',
//						 'config_price_customer.methods',
//						 'config_price_customer.accord',
//						 'config_price_customer.start_date',
//						 'config_price_customer.price',
//						 'config_price_customer.create_time',
//						 'config_price_customer.update_time',
//						 'config_price_customer.remark',
					 ])
					 ->join('product ON product.product_id = config_price_customer.father_id')
					 ->join('account ON account.apikey=config_price_customer.apikey')
					 ->join('customer ON customer.customer_id = account.customer_id')
					 ->where(['customer.customer_id' => $customer_id, 'delete_time' => ['exp', 'IS NULL']])
					 ->order('start_date desc, create_time desc')
					 ->select();

        $productMap = ProductModel::getListProduct();
        $productMap = array_column($productMap, 'product_name', 'product_id');
        $periodMap = [
            0 => '无意义',
            1 => '日',
            2 => '月',
            3 => '年',
            4 => '无周期'
        ];

		$data = array_map(function ($item) use($productMap, $periodMap) {
            $item['price'] = $item['price'] ? $this->formatePriceHtml($item,$productMap) : '';
			$item['methods']       = $this->price_config['methods'][$item['methods']];
			$item['diff_operator'] = $item['diff_operator'] ? '区分' : '不区分';
			$item['mode']          = $this->price_config['mode'][$item['mode']];
			$item['start_date']    = date('Y-m-d', strtotime($item['start_date']));
			$item['create_time']   = date('Y-m-d H:i', $item['create_time']);
			$item['update_time']   = date('Y-m-d H:i', $item['update_time']);
			$item['accord']        = $this->price_config['accord'][$item['accord']];
			$item['period']        = $periodMap[$item['period']] ? $periodMap[$item['period']] : '未知';

			return $item;
		}, $data);
		
		$code  = 0;
		$msg   = '';
		$count = count($data);
		
		header('content-type:application/json');
		print_r(json_encode(compact('code', 'msg', 'count', 'data'), JSON_UNESCAPED_UNICODE));
		die;
	}

	public function exportListData($where){
        $customer_ids = [];
	    if(isset($where['export']) && $where['export'] == 'feeConfig'){
            $customer_list = CustomerModel::getListCustomer(['status'=>1], 'customer_id');
            $customer_ids = array_column($customer_list, 'customer_id');
        }

        $data = $this->getConfigPriceCustomerModel()
            ->field([
                'customer.customer_id',
                'customer.name as customer_name',
                //'account.account_id',
                'product_name',
                //'product.product_id',
                'account_name',
                'config_price_customer.id',
                'config_price_customer.diff_operator',
                'config_price_customer.mode',
                'config_price_customer.methods',
                'config_price_customer.accord',
                'config_price_customer.start_date',
                'config_price_customer.price',
                'config_price_customer.create_time',
                'config_price_customer.update_time',
                'config_price_customer.remark',
            ])
            ->join('product ON product.product_id = config_price_customer.father_id')
            ->join('account ON account.apikey=config_price_customer.apikey')
            ->join('customer ON customer.customer_id = account.customer_id')
            ->where(['customer.customer_id' => ['in', $customer_ids], 'delete_time' => ['exp', 'IS NULL']])
            ->order('start_date desc, create_time desc')
            ->select();

        $pList = ProductModel::getListProduct();
        $pList = array_column($pList, 'product_name', 'product_id');
        //var_dump($pList);exit;

        $opList = ConfigOperatorModel::getListConfig();
        $opList = array_column($opList, 'name', 'operator');

        //$data = array_map(function ($item) {
            foreach($data as $key => &$item) {
                $item['methods'] = $this->price_config['methods'][$item['methods']];
                $item['diff_operator'] = $item['diff_operator'] ? '区分' : '不区分';
                $item['mode'] = $this->price_config['mode'][$item['mode']];
                $item['start_date'] = date('Y-m-d', strtotime($item['start_date']));
                $item['create_time'] = date('Y-m-d H:i', $item['create_time']);
                $item['update_time'] = date('Y-m-d H:i', $item['update_time']);
                $item['accord'] = $this->price_config['accord'][$item['accord']];
                if($item['price'] && $item['mode'] !='打包' ){
                    $item['price_cn'] = $this->priceConversion($item['price'], $item['diff_operator'], $pList, $opList);
                }else{
                    $item['price_cn'] = $item['price'];
                }

            }
           // return $item;
        //}, $data);

        $customer_id_col = array_column($data,'customer_id');
        array_multisort($customer_id_col,SORT_DESC, $data);

        $status  = 0;
        $msg   = '';
        $count = count($data);

        /*header('content-type:application/json');
        print_r(json_encode(compact('code', 'msg', 'count', 'data'), JSON_UNESCAPED_UNICODE));
        die;*/
        header('content-type:application/json;charset=utf-8');
        echo json_encode(compact('status', 'msg', 'count', 'data'), JSON_UNESCAPED_UNICODE);exit;


    }
	
	/**
	 * 获取增加页面的中的所需数据
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/4 14:06
	 *
	 * @return void
	 */
	public function getInfoForEditHtml()
	{
		//获取需要编辑的数据
		$id          = I('GET.id');
		$delete_time = ['exp', 'is NULL'];
		$data        = $this->getConfigPriceCustomerModel()
							->where(compact('id', 'delete_time'))
							->find();
		if (empty($data)) {
			createJsonResponse(10001);
		}
		$data['price'] = json_decode($data['price'], true);
		
		//获取为那个客户标记的计费配置
		$apikey        = $data['apikey'];
		$customer_info = $this->getAccountModel()
							  ->where(compact('apikey'))
							  ->find();
		$customer_id   = $customer_info['customer_id'];
		$result        = $this->checkCustomerStoreProduct($customer_id);
		
		//获取客户名称
		$customer      = $this->getCustomerModel()
							  ->where(compact('customer_id'))
							  ->field(['name'])
							  ->find();
		$customer_name = $customer['name'];
		
		createJsonResponse(0, compact('config', 'customer_name', 'customer_id', 'data'));
	}
	
	/**
	 * 执行删除数据
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/18 15:41
	 *
	 * @return void
	 */
	public function runDelete()
	{
		if (!IS_POST) {
			return;
		}
		$id = I('post.id');
		$this->getConfigPriceCustomerModel()
			 ->delete($id);
		
		createJsonResponse(0);
	}

	public function priceConversion($jsonData, $diff_operator, $pList, $opList){
        $arr = json_decode($jsonData, true, JSON_UNESCAPED_UNICODE);
        $tmp = [];
        foreach($arr as $key => $val){
            $pinfo = isset($pList[$key]) ? $pList[$key] : '';
            if($diff_operator == '区分'){
                $op_tmp = [];
                foreach($val as $opkey => $opval){
                    $op_info = isset($opList[$opkey]) ? $opList[$opkey] : '';
                    $op_tmp[$op_info] = $opval;
                }
                $tmp[$pinfo] = $op_tmp;
            }else{
                $tmp[$pinfo] = $val;
            }

        }

        unset($arr);
        return json_encode($tmp, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 以表格形式格式化计费配置
     *
     * @param $priceConfig 价格配置
     * @param $productMap 产品map
     * @param $opMap 运营商map
     */
    protected function formatePriceHtml($config,$productMap)
    {
        $html = '';
        $params = [];
        $price = $this->parsePrice($config);
        $isPack = false;
        switch ($config['mode']) {
            case 0://无计费模式
                $params[$config['father_id']] = $this->getHtmlParams($config, $price);
                break;
            case 1://独立子产品计费
                foreach ($price as $product_id => $item) {
                    $params[$product_id] = $this->getHtmlParams($config, $item);
                }
                break;
            case 2://打包计费
                $isPack = true;
                foreach ($price as $item) {
                    $params[$config['father_id']][$item['min']] = $this->getHtmlParams($config, $item['price']);
                }
                break;
            case 3://汇总子产品计费
                $params[$config['father_id']] = $this->getHtmlParams($config, $price);
                break;
        }

        foreach ($params as $productId => $arr) {
            $tmp = [];
            foreach ($arr as $key => $value) {
                if (!$isPack) {
                    $tmpPrice = is_array($value['priceData']) ? implode('；', $value['priceData']) : $value['priceData'];
                    if ($value['yys'] != 'none') {
                        $tmp[] = "<td>{$this->allow_operator[$value['yys']]}</td><td colspan='2'>{$tmpPrice}</td>";
                    } else {
                        $tmp[] = "<td colspan='3'>{$tmpPrice}</td>";
                    }
                } else {
                    foreach ($value as $item) {
                        $tmpPrice = is_array($item['priceData']) ? implode('；', $item['priceData']) : $item['priceData'];
                        if ($value['yys'] != 'none') {
                            $tmp[] = "<td>打包量大于{$key}</td><td>{$this->allow_operator[$item['yys']]}</td><td>{$tmpPrice}</td>";
                        } else {
                            $tmp[] = "<td colspan='2'>打包量大于{$key}</td><td>{$tmpPrice}</td>";
                        }
                    }
                }
            }
            $rowspan = count($tmp);
            $i = 0;
            foreach ($tmp as $t) {
                if (!$i) {
                    $html .= "<tr><td rowspan='{$rowspan}'>{$productMap[$productId]}</td>{$t}</tr>";
                } else {
                    $html .= "<tr>{$t}</tr>";
                }
                $i++;
            }
        }
        $return ="<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }
    </style>
</head>
<body>
    <div style='width: 100%;margin-bottom: 10px'>
        <h1>价格配置 <span style='font-size:14px;color: #aaaaaa'>计费方式：{$this->price_config['methods'][$config['methods']]}</span></h1>
        <table>
            <tr><th> 产品名称 </th><th colspan='3'> 价格配置 </th></tr>
            {$html}
        </table>
    </div>
</body>";
        return $return;
    }

    /**
     * 获取html参数
     *
     * @param $config
     * @param $price
     * @param int $productId
     */
    protected function getHtmlParams($config, $price)
    {
        $price = is_array($price) ? $price : json_decode($price, true);
        $return = [];
        $isLadder = in_array($config['methods'], [3,4]) ? 1 : 0;
        //是否区分运营商
        if ($config['diff_operator']) {
            foreach ($price as $key => $item) {
                $tmp = [
                    'yys' => $key,
                    'isLadder' => $isLadder,
                    'priceData' => $item
                ];
                if (!$isLadder) {
                    $return[] = $tmp;
                    continue;
                }
                $tmp['priceData'] = $this->getLadderParams($item);
                $return[] = $tmp;
            }
            return $return;
        }

        //是否阶梯
        $priceData = isset($price['price']) ? $price['price'] : $price;
        if ($isLadder) {
            $priceData = $this->getLadderParams($price);
        }
        $return[] = [
            'yys' => 'none',
            'isLadder' => $isLadder,
            'priceData' => $priceData
        ];

        return $return;
    }

    protected function getLadderParams($price)
    {
        $return = [];
        if (!$price) {
            return [];
        }

        $price = is_array($price) ? $price : json_decode($price, true);
        foreach ($price as $k => $v) {
            $return[] = $k . '：' . $v;
        }
        return $return;
    }
    /**
     * 解析原始单价
     *
     * @return mixed
     */
    private function parsePrice($config)
    {
        switch ($config['mode']) {
            case 0:
                //无计费模式
                return $this->getAloneProductPrice($config);
            case 1:
                //独立子产品计费
                return $this->getSubProductPrice($config);
            case 2:
                //打包计费
                return $this->getPackagePrice($config);
            case 3:
                //汇总子产品计费
                return $this->getSupProductPrice($config);
        }

        return false;
    }

    /**
     * 获取独立产品的单价
     *
     *
     * @return string
     */
    private function getAloneProductPrice($config)
    {
        $product_id = $config['father_id'];
        $price      = json_decode($config['price'], true);
        if (JSON_ERROR_NONE != json_last_error()) {
            throw new \Exception('产品单价解析错误, 计费配置ID:' . $config['id']);
        }

        $price = array_map(function ($item) {
            return is_array($item) ? json_encode($item, JSON_UNESCAPED_UNICODE) : $item;
        }, $price);
        if (!array_key_exists($product_id, $price)) {
            throw new \Exception('产品单价数据(未发现产品单价), 计费配置ID:' . $config['id']);
        }

        return $price[$product_id];
    }

    /**
     * 获取独立子产品计费模式中每个子产品的单价
     *
     * @return array
     */
    private function getSubProductPrice($config)
    {
        $price = json_decode($config['price'], true);
        if (JSON_ERROR_NONE != json_last_error()) {
            throw new \Exception('子产品单价解析错误');
        }

        return array_map(function ($item) {
            return is_array($item) ? json_encode($item, JSON_UNESCAPED_UNICODE) : $item;
        }, $price);
    }

    /**
     * 获取打包计费中每个区间的单价
     *
     * @return array
     */
    private function getPackagePrice($config)
    {
        $price = json_decode($config['price'], true);
        if (JSON_ERROR_NONE != json_last_error()) {
            throw new \Exception('打包单价解析错误');
        }

        $result = [];
        array_walk($price, function ($price, $together_call_number) use (&$result) {
            $price = is_array($price) ? json_encode($price, JSON_UNESCAPED_UNICODE) : $price;
            $min   = $together_call_number;
            if (0 != count($result)) {
                $last        = array_pop($result);
                $last['max'] = bcsub($together_call_number, 1);
                array_push($result, $last);
            }
            array_push($result, compact('min', 'price'));
        });

        return $result;
    }

    /**
     * 获取汇总子产品计费模式下的统一单价
     *
     * @return string
     */
    private function getSupProductPrice($config)
    {
        $product_id = $config['father_id'];
        $price      = json_decode($config['price'], true);
        if (JSON_ERROR_NONE != json_last_error()) {
            throw new \Exception('产品单价解析错误, 计费配置ID:' . $config['id']);
        }

        $price = array_map(function ($item) {
            return is_array($item) ? json_encode($item, JSON_UNESCAPED_UNICODE) : $item;
        }, $price);
        if (!array_key_exists($product_id, $price)) {
            throw new \Exception('产品单价数据(未发现产品单价), 计费配置ID:' . $config['id']);
        }

        return $price[$product_id];
    }


}