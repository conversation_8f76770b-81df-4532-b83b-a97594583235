<?php

namespace Account\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\BxfshortAllCmccConfigModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Common\CurlTrait;
use Common\Common\HandlerLog;
use Common\Common\ResponseTrait;
use Common\Common\WechatBackendExceptionTrait;
use Common\Controller\DataAuthController;
use Common\Model\CommonEnumModel;
use Common\Model\ProductTypeModel;
use Common\Model\SystemUserModel;
use PreSales\Repositories\PreSalesCustomerProductRepository;
use Think\Page;

class AccountRepository extends BaseRepository
{
	use ResponseTrait, CurlTrait, WechatBackendExceptionTrait;
	
	/*
	 * 不可以共存的产品ID
	 * */
	protected $list_only_one = [
		301,
		302,
	];
	
	/*
	 * 运营商报告的产品ID
	 * */
	protected $product_report_id = 102;
	
	/*
	 * 运营商报告的配置信息
	 * */
	private $report_config;
	
	/**
	 * @return mixed
	 */
	private function getReportConfig()
	{
		if ($this->report_config) {
			return $this->report_config;
		}
		
		$product_id     = $this->product_report_id;
		$product_report = $this->getOneProductByCondition(compact('product_id'));
		
		return $this->report_config = json_decode($product_report['data'], true);
	}
	
	/**
	 * 编辑产品View
	 * @throws \Exception
	 */
	public function editProduct()
	{
		// 限定访问类型
		$this->validateParamsForEdit();
		
		// 选定的产品
		$account_info = $this->getChooseProductForEdit();
		
		// 产品信息处理
		$account_info = $this->tidyProductDataForEdit($account_info);
		
		// 选定的产品基本信息
		$product_info = $this->getBaseProductForEdit($account_info);
		
		//邦信分快捷版全部字段
		$bxf_all_pid = (new ProductModel())->where(['father_id' => 1000])
										   ->field(['product_id', 'product_name', 'mark'])
										   ->order('id')
										   ->select();

        /*
		//邦信分快捷版评分类字段
		$bxf_sore_pid = array_column((new ProductTypeModel())->where(['type' => 1])
															 ->field(['product_id'])
															 ->select(), 'product_id');
		$cmccConfig   = (new BxfshortAllCmccConfigModel())->order('sort desc')
														  ->select();
		$cmccConfig   = array_column($cmccConfig, 'name', 'flag');
        */

		$data = (isset($account_info['data']) && !empty($account_info['data'])) ? json_decode($account_info['data'], true) : [];
		
		
		$productIds = isset($data['product_ids']) ? $data['product_ids'] : [];
		
		
		//$cucc_cmcc_map = isset($data['cucc_cmcc_map']) ? $data['cucc_cmcc_map'] : [];
		
		//return compact('account_info', 'product_info', 'bxf_sore_pid', 'bxf_all_pid', 'cmccConfig', 'cucc_cmcc_map', 'productIds');
		return compact('account_info', 'product_info', 'bxf_all_pid', 'productIds');
	}
	
	/**
	 * 校验编辑产品的参数
	 * @throws \Exception
	 */
	protected function validateParamsForEdit()
	{
		// 限定访问方式
		$this->validateMethodForEdit();
		
		// 限定其他的参数
		$this->validateOtherParamsForEdit();
	}
	
	/**
	 * 限定其他的参数
	 * @throws \Exception
	 */
	protected function validateOtherParamsForEdit()
	{
		$account_product_id = I('get.account_product_id', '', 'trim');
		if (!$account_product_id) {
			throw new \Exception('请传递合法的account_product_id');
		}
	}
	
	/**
	 * 限定访问方式
	 * @throws \Exception
	 */
	protected function validateMethodForEdit()
	{
		if (!IS_GET) {
			throw new \Exception('本方法仅限于GET方式访问');
		}
	}
	
	/**
	 * 追加账号信息
	 *
	 * @param array $product_info
	 *
	 * @return array
	 */
	protected function appendAccountNameToProductForEdit(array $product_info)
	{
		$account_id   = $product_info['account_id'];
		$account_info = $this->getOneAccountByCondition(compact('account_id'));
		$account_name = $account_info['account_name'];
		
		return array_merge($product_info, compact('account_name'));
	}
	
	/**
	 * 产品data信息处理
	 *
	 * @param array $product_info 产品信息
	 *
	 * @return array
	 */
	protected function tidyProductDataForEdit(array $product_info)
	{
		// 追加账号信息
		return $this->appendAccountNameToProductForEdit($product_info);
	}
	
	/**
	 * 获取要编辑产品类型的信息
	 *
	 * @param array $product_info
	 *
	 * @return array
	 */
	private function getBaseProductForEdit(array $product_info)
	{
		$product_id = $product_info['product_id'];
		
		return $this->getOneProductByCondition(compact('product_id'));
	}
	
	/**
	 * 获取产品类型信息
	 *
	 * @param array $where
	 *
	 * @return array
	 */
	protected function getOneProductByCondition(array $where)
	{
		return (new ProductModel())->where($where)
								   ->find();
	}
	
	/**
	 * 选定的产品基本信息
	 * @return array
	 */
	private function getChooseProductForEdit()
	{
		$id = I('get.account_product_id', '', 'trim');
		
		return $this->getOneAccountProductByCondition(compact('id'));
	}
	
	/**
	 * 根据条件获取一个产品
	 *
	 * @param array $where
	 *
	 * @return array
	 */
	protected function getOneAccountProductByCondition(array $where)
	{
		return (new AccountProductModel())->where($where)
										  ->find();
	}
	
	/**
	 * @throws \Exception
	 */
	public function storeProduct()
	{
		
		// 限制访问类型
		$this->limitMethodPost();
		
		// 检查参数
		$this->verifyParamsForStore();
		// 存储数据
		$this->beforeStore();
		
		
		//测试客户产品监听开通产品事件
		(new PreSalesCustomerProductRepository())->listenStoreProduct();
		
		//监听开通产品的状态
		$this->listenAccountProductStatus();
		
	}
	
	/**
	 * 监听开通产品状态，如果开通产品状态小于所属客户的当前状态值，则修改客户状态值为开通产品状态值
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function listenAccountProductStatus()
	{
		$account_id = I('post.account_id', '');
		if (empty($account_id)) {
			return;
		}
		$account_model = new AccountModel();
		$account_data  = $account_model->where(compact('account_id'))
									   ->find();
		$customer_id   = $account_data['customer_id'];
		if (empty($customer_id)) {
			return;
		}
		$customer_model = new CustomerModel();
		$customer_data  = $customer_model->where(compact('customer_id'))
										 ->find();
		if (empty($customer_data)) {
			return;
		}
		$customer_contract_status = $customer_data['contract_status'];  //当前的客户签约状态
		$contract_status          = I('post.contract_status', 5);                //将要修改为的开通的产品签约状态
		//状态的权重顺序为 4 5 3 2 1
		if ($contract_status == $customer_contract_status || $contract_status == 4) {
			return;
		}
		if ($customer_contract_status == 4 || $contract_status < $customer_contract_status) {
			$customer_model->where(compact('customer_id'))
						   ->save(compact('contract_status'));
		}
	}
	
	/**
	 * 一键变更时，监听产品状态，使的客户状态值跟开通产品状态值一致
	 *
	 * @access public
	 *
	 * @param $customer_id     string 客户ID
	 * @param $contract_status integer 产品的签约状态
	 *
	 * @return void
	 **/
	public function listenAccountProductStatusForUpgradeAtOnce($customer_id, $contract_status)
	{
		if (empty($customer_id)) {
			return;
		}
		if (!in_array($contract_status, [1, 2, 3, 4, 5])) {
			return;
		}
		$customer_model = new CustomerModel();
		$customer_data  = $customer_model->where(compact('customer_id'))
										 ->find();
		if (empty($customer_data)) {
			return;
		}
		$customer_contract_status = $customer_data['contract_status'];      //当前的客户签约状态
		//状态的权重顺序为 4 5 3 2 1
		if ($contract_status == $customer_contract_status || $contract_status == 4) {
			return;
		}
		if ($customer_contract_status == 4 || $contract_status < $customer_contract_status) {
			$customer_model->where(compact('customer_id'))
						   ->save(compact('contract_status'));
		}
	}
	
	/**
	 * 处理新增产品
	 * @throws \Exception
	 */
	private function beforeStore()
	{
		// 如果是301 302 则特殊处理
		$product_id = I('post.product_id', '', 'trim');
		if (in_array($product_id, [301, 302])) {
			$this->storeProductForSpecial();
			
			return;
		} else if ($product_id == 210) {
			//需要与子产品一起开通
			CuiShouFenRepository::build()
								->storeProduct($this);
			
			return;
		} else if ($product_id == 1000) {
			CuiShouFenRepository1000::build()
									->storeProduct($this);
			
			return;
		} else if ($product_id == 615) {
			GoldShieldRepository::build()
								->storeProduct($this);
			
			return;
		}
		
		
		//如果是邦秒验子产品、并且为开通父级产品，则需要开通一下（临时处理）
		$product_id = I('post.product_id');
		$father_id  = (new ProductModel())->where(compact('product_id'))
										  ->field(['father_id'])
										  ->select();
		if (200 == $father_id[0]['father_id']) {
			//是否开通邦秒验
			$data = [
				'account_id'      => I('post.account_id'),
				'product_id'      => 200,
				'status'          => 1,
				'contract_status' => 1,
				'end_time'        => **********,
				'daily_limit'     => -1,
				'month_limit'     => -1,
				'year_limit'      => -1,
				'total_limit'     => -1,
				'concurrency'     => 1,
				'data'            => json_encode([], JSON_UNESCAPED_UNICODE),
			];
			
			$fatherInfo = (new AccountProductModel())->where([
				'account_id' => $data['account_id'],
				'product_id' => 200,
			])->find();
			
			//子产品截止时间是当天晚上 23.59.59 因此加86399
			$endtime = strtotime(I('post.end_time'))+86399;
			$status = I('post.status');

			if (0 == count($fatherInfo)) {
				if($status=='1'){
					//新增的父产品的截止时间应该跟子产品一致
					$data['end_time'] = $endtime;
				}else{
					//产品若禁用 讨论默认值写当天晚上时间
					$data['end_time'] = strtotime(date('Y-m-d 23:59:59'));
				}
				(new AccountProductModel())->add($data);
			}else{
				if($status=='1'){
					//新增子产品的截止日期晚于父产品截止日期，更新父产品截止时间
					if($endtime>$fatherInfo['end_time']){
						(new AccountProductModel())->where([
							'account_id' => $data['account_id'],
							'product_id' => 200,
						])->save(['end_time'=>$endtime]);
					}
				}
			}
		}
		
		// 普通的产品的处理
		$this->storeProductForCommon();
	}
	
	/**
	 * 针对普通产品的处理
	 * @throws \Exception
	 */
	public function storeProductForCommon()
	{
		// 生成参数
		$params = $this->genParamsForStore();
		
		// store
		$insert_id = $this->storeDo($params);
		
		// 记录日志
		$this->logCreate($insert_id);
	}
	
	/**
	 * @throws \Exception
	 */
	private function storeProductForSpecial()
	{
		// 生成报告产品
		$this->createReportProductForSpecial();
		
		// 生成301或者302产品
		$this->createCrawlerProductForSpecial();
	}
	
	/**
	 * 生成报告产品
	 * @throws \Exception
	 */
	private function createCrawlerProductForSpecial()
	{
		// 生成参数
		$params               = $this->getCrawlerProductParamsForSpecial();
		$params['product_id'] = I('post.product_id', '', 'trim');
		
		// 新建产品
		$this->createCrawlerProductDo($params);
	}
	
	/**
	 * 新建产品
	 *
	 * @param array $params
	 *
	 * @throws \Exception
	 */
	private function createCrawlerProductDo(array $params)
	{
		$crawler_result = $this->storeDo($params);
		if ($crawler_result === false) {
			$this->wehcatException('开通邦秒爬API或者邦秒爬H5的时候, 开通失败');
		}
		
		// 日志
		$this->logCreate($crawler_result);
	}
	
	/**
	 * 为301 or 302 生成参数
	 * @return array
	 * @throws \Exception
	 */
	private function getCrawlerProductParamsForSpecial()
	{
		// 通用参数
		$params_common = $this->getCommonParamsForSpecial();
		
		// data 参数
		$data_crawler = $this->getCrawlerDataForSpecial();
		
		return array_merge($params_common, $data_crawler);
	}
	
	/**
	 * 为301 302生成data参数
	 * @throws \Exception
	 */
	private function getCrawlerDataForSpecial()
	{
		$data_post    = I('post.data');
		$data_crawler = $this->getCrawlerConfig();
		
		//如果授权状态回调地址、报告状态回调地址两个地址相同时
		if (trim($data_post['authorize_notify_url']) == trim($data_post['report_notify_url']) && !empty(trim($data_post['authorize_notify_url']))) {
			$authorize_notify_url              = trim($data_post['authorize_notify_url']);
			$data_post['authorize_notify_url'] = strstr($authorize_notify_url, '?') ? $authorize_notify_url . '&yulore_flag=report' : $authorize_notify_url . '?yulore_flag=auth';
		}
		
		// data数据可能补全, eg:  H5配置-必填字段在不选择的时候是这样的
		$data = array_reduce($data_crawler, function ($carry, $item) use ($data_post) {
			$name         = $item['name'];
			$carry[$name] = array_key_exists($name, $data_post) ? $data_post[$name] : ($item['type'] == 4 ? [] : '');
			
			return $carry;
		}, []);
		
		// 不同产品,加工一波
		$data = $this->tidyDataForSpecialProduct($data);
		
		$data = json_encode($data, JSON_UNESCAPED_UNICODE);
		
		return compact('data');
	}
	
	/**
	 * 获取爬虫的配置
	 * @throws \Exception
	 */
	private function getCrawlerConfig()
	{
		$product_id      = I('post.product_id', '', 'trim');
		$product_crawler = $this->getOneProductByCondition(compact('product_id'));
		
		return json_decode($product_crawler['data'], true);
	}
	
	/**
	 * 生成报告产品
	 * @throws \Exception
	 */
	private function createReportProductForSpecial()
	{
		// 生成参数
		$params               = $this->getReportProductParamsForSpecial();
		$params['product_id'] = $this->product_report_id;
		
		// 更新或者新建产品
		$this->updateOrCreateReportProduct($params);
	}
	
	/**
	 * 更新产品
	 *
	 * @param array $params
	 *
	 * @throws \Exception
	 */
	private function updateOrCreateReportProduct(array $params)
	{
		// 条件
		$account_id = I('post.account_id', '', 'trim');
		$product_id = $this->product_report_id;
		$where      = compact('product_id', 'account_id');
		
		// 更新
		$determine_exists = $this->getOneAccountProductByCondition($where);
		
		if ($determine_exists) {
			// 只需要更新data
			$data_config = arrOnly($params, ['data']);
			$this->updateReportProduct($data_config, $where);
			
			return;
		}
		// 新建
		$this->createReportProduct($params);
	}
	
	/**
	 * 更新报告
	 *
	 * @param array $params
	 * @param array $where
	 *
	 * @throws \Exception
	 */
	private function updateReportProduct(array $params, array $where)
	{
		// 旧版的数据
		$product_old = $this->getOneAccountProductByCondition($where);
		
		$update_result = (new AccountProductModel())->where($where)
													->save($params);
		
		if ($update_result === false) {
			$msg = '更新爬虫产品的时候，伴生运营商报告产品更新时候';
			$this->wehcatException($msg . ' msg :' . json_encode(compact('account_id', 'product_id', 'params')));
			throw new \Exception($msg);
		}
		
		// 日志
		$product_new = $this->getOneAccountProductByCondition($where);
		$this->logUpdate(compact('product_new', 'product_old'));
	}
	
	/**
	 * 记录create log
	 *
	 * @param array $content
	 *
	 * @throws \Exception
	 */
	private function logUpdate(array $content)
	{
		$log_info = [
			'handle_type'   => 'update',
			'description'   => '更新产品日志',
			'content'       => $content,
			'handle_user'   => session(C('LOGIN_SESSION_NAME')) ?: 'system',
			'handle_result' => 1,
		];
		HandlerLog::log($log_info);
	}
	
	/**
	 * @param array $params
	 *
	 * @throws \Exception
	 */
	private function createReportProduct(array $params)
	{
		$report_result = $this->storeDo($params);
		if ($report_result === false) {
			$this->wehcatException('开通邦秒爬API或者邦秒爬H5的时候,绑定运营商报告开通失败');
		}
		
		// 日志
		$this->logCreate($report_result);
	}
	
	/**
	 * 生成参数
	 * @return array
	 */
	private function getReportProductParamsForSpecial()
	{
		// 通用参数
		$data_common = $this->getCommonParamsForSpecial();
		
		// data 参数
		$data_report = $this->getReportParamsForSpecial();
		
		return array_merge($data_common, $data_report);
	}
	
	/**
	 * 通用参数
	 * @return array
	 */
	private function getReportParamsForSpecial()
	{
		// 获取report data的配置
		$data_json = $this->getReportConfig();
		$data_post = I('post.data');
		
		//如果授权状态回调地址、报告状态回调地址两个地址相同时
		if (trim($data_post['authorize_notify_url']) == trim($data_post['report_notify_url']) && !empty(trim($data_post['authorize_notify_url']))) {
			$report_notify_url              = trim($data_post['report_notify_url']);
			$data_post['report_notify_url'] = strstr($report_notify_url, '?') ? $report_notify_url . '&yulore_flag=report' : $report_notify_url . '?yulore_flag=report';
		}
		// data数据可能补全, eg:  H5配置-必填字段在不选择的时候是这样的
		$data = array_reduce($data_json, function ($carry, $item) use ($data_post) {
			$name         = $item['name'];
			$carry[$name] = array_key_exists($name, $data_post) ? $data_post[$name] : ($item['type'] == 4 ? [] : '');
			
			return $carry;
		}, []);
		
		$data = json_encode($data, JSON_UNESCAPED_UNICODE);
		
		return compact('data');
	}
	
	/**
	 * 通用参数
	 * @return array
	 */
	private function getCommonParamsForSpecial()
	{
		// 开通产品基本参数
		$limit_base = $this->genParamsBaseForSpecial();
		
		// 基础参数
		$limit_time = $this->genTimeParamsForSpecial();
		
		return array_merge($limit_base, $limit_time);
	}
	
	/**
	 * 开通产品生成其他基础限制的参数
	 * @return array
	 */
	protected function genTimeParamsForSpecial()
	{
		$end_time  = I('post.end_time', '', 'strtotime');
		$end_time  += 86399;
		$create_at = time();
		
		return compact('end_time', 'create_at');
	}
	
	/**
	 * 开通产品生成限制的参数
	 * @return array
	 */
	protected function genParamsBaseForSpecial()
	{
		$params_post                     = I('post.');
		$params_post['limit_start_date'] = I('post.limit_start_date') ? I('post.limit_start_date') : null;
		$list_params                     = [
			'total_limit',
			'daily_limit',
			'month_limit',
			'year_limit',
			'concurrency',
			'account_id',
			'status',
			'contract_status',
			'use_type',
			'limit_start_date',
		];
		
		return arrOnly($params_post, $list_params);
	}
	
	/**
	 * 记录create log
	 *
	 * @param integer $id
	 *
	 * @throws \Exception
	 */
	public function logCreate($id)
	{
		$product_new_create = $this->getOneAccountProductByCondition(compact('id'));
		
		$log_info = [
			'handle_type'   => 'create',
			'description'   => '新增产品日志',
			'content'       => compact('product_new_create'),
			'handle_user'   => session(C('LOGIN_SESSION_NAME')) ?: "system",
			'handle_result' => 1,
		];
		HandlerLog::log($log_info);
	}
	
	/**
	 * store
	 *
	 * @param array $params
	 *
	 * @return int
	 */
	public function storeDo(array $params)
	{
		return (new AccountProductModel())->add($params);
	}
	
	
	/**
	 * 为新建产品生成参数
	 * @return array
	 */
	protected function genParamsForStore()
	{
		// 生成data参数
		$params_data = $this->genDataParamsForStore();
		
		// 生成基本参数
		$params_base = $this->genBaseParamsForStore();
		
		return array_merge($params_base, $params_data);
	}
	
	/**
	 * 生成公用的参数
	 * @return array
	 */
	public function genBaseParamsForStore()
	{
		// 开通产品基本参数
		$limit_base = $this->genParamsBaseForStore();
		
		// 基础参数
		$limit_time = $this->genTimeParamsForStore();
		
		return array_merge($limit_base, $limit_time);
	}
	
	/**
	 * 开通产品生成其他基础限制的参数
	 * @return array
	 */
	protected function genTimeParamsForStore()
	{
		$end_time  = I('post.end_time', '', 'strtotime');
		$end_time  += 86399;
		$create_at = time();
		
		return compact('end_time', 'create_at');
	}
	
	/**
	 * 开通产品生成限制的参数
	 * @return array
	 */
	protected function genParamsBaseForStore()
	{
		$params_post                     = I('post.');
		$params_post['limit_start_date'] = I('post.limit_start_date') ? I('post.limit_start_date') : null;
		$list_params                     = [
			'total_limit',
			'daily_limit',
			'month_limit',
			'year_limit',
			'concurrency',
			'account_id',
			'product_id',
			'status',
			'contract_status',
			'use_type',
			'limit_start_date',
		];
		
		return arrOnly($params_post, $list_params);
	}
	
	/**
	 * 生成data参数
	 * @return array
	 */
	protected function genDataParamsForStore()
	{
		// 生成通用的data
		$data = $this->genCommonDataForStore();
		
		$data = json_encode($data, JSON_UNESCAPED_UNICODE);
		
		return compact('data');
	}
	
	/**
	 * 生成通用的data
	 * @return array
	 */
	protected function genCommonDataForStore()
	{
		$data      = I('post.data');
		$data_json = json_decode(I('post.data_json', '', 'trim'), true);

        //如果是号码分等级区间配置，需要单独逻辑整理数据
        $data = $this->checkScoreGradeLimit($data);
		// data数据可能补全, eg:  H5配置-必填字段在不选择的时候是这样的
		return array_reduce($data_json, function ($carry, $item) use ($data) {
				$name         = $item['name'];
				$carry[$name] = array_key_exists($name, $data) ? $data[$name] : ($item['type'] == 4 ? [] : '');
				
				return $carry;
			}, []) + $data;
	}

    /**
     * 处理号码分等级字段逻辑
     * @param $data
     * @return mixed
     */
    protected function checkScoreGradeLimit($data){
        if (!isset($data['scoreGradeLimit'])) {return $data;}
        //如果是号码分值等级，需要修改数据格式
        $sourceLimit = $data['scoreGradeLimit'];
        $gradeLimit = [];
        sort($sourceLimit);
        foreach ($sourceLimit as $key => $limit) {
            $gradeLimit[] = [
                'grade' => $key + 1,
                'min' => $key ? $sourceLimit[$key-1] + 1 : 0,
                'max' => intval($limit)
            ];
        }
        $data['scoreGradeLimit'] = $gradeLimit;
        return $data;
    }
	/**
	 * 各个产品的特殊处理
	 *
	 * @param array $data
	 *
	 * @return array
	 */
	protected function tidyDataForSpecialProduct(array $data)
	{
		$product_id = I('post.product_id');
		switch ($product_id) {
			case 301:
				return $this->tidyDataForH5($data);
				break;
			default:
				return $data;
				break;
		}
	}
	
	/**
	 * H5邦秒爬需要特殊处理的地方
	 *
	 * @param array $data
	 *
	 * @return array
	 */
	protected function tidyDataForH5(array $data)
	{
		// 数值型转int类型
		$data = $this->tidyTypeDataForH5($data);
		
		// H5必填紧急联系人限制
		$data = $this->tidyEmergencyDataForH5($data);
		
		// 协议处理
		return $this->tidyProtocolDataForH5($data);
	}
	
	/**
	 * H5必填紧急联系人限制
	 *
	 * @param array $data
	 *
	 * @return array
	 */
	private function tidyEmergencyDataForH5(array $data)
	{
		// 如果没有介入联系人页面，则重置为[]
		$data_post = I('post.data');
		if ($data_post['contactor_page'] === 'N') {
			$data['emergency_contact_detail_limits'] = [];
			
			return $data;
		}
		
		// 整合各个限制
		$emergency_contact_detail_limits = [];
		array_walk($data_post, function ($item, $key) use (&$emergency_contact_detail_limits) {
			if (strpos($key, 'emergency_contact_detail_limits') !== false) {
				array_push($emergency_contact_detail_limits, $item);
			}
		});
		
		$data['emergency_contact_detail_limits'] = $emergency_contact_detail_limits;
		
		return $data;
	}
	
	/**
	 * 301产品协议处理
	 *
	 * @param array $data
	 *
	 * @return array
	 */
	protected function tidyProtocolDataForH5(array $data)
	{
		// 如果是默认协议 则协议内容一律为空
		$data_post = I('post.data');
		if ((int)$data_post['protocol_default'] === 1) {
			$data['protocol_content'] = '';
		}
		
		return $data;
	}
	
	/**
	 * 301产品数值型转int类型
	 *
	 * @param array $data
	 *
	 * @return array
	 */
	protected function tidyTypeDataForH5(array $data)
	{
		$list_sections = [
			'emergency_contact_max_number',
			'effective_authorization_time',
		];
		
		array_walk($list_sections, function ($section) use (&$data) {
			$data[$section] = $data[$section] === '' ? '' : (int)$data[$section];
		});
		
		return $data;
	}
	
	/**
	 * @throws \Exception
	 */
	private function verifyParamsForStore()
	{
		// 检测必选条件是否存在
		$this->verifyParamsExistsForStore();

        // 检测产品是否下架
        $this->verifyProductStatus();
		
		// 检测该类型产品是否已经创建
		$this->verifyUniqueTypeForStore();
		
		// 检测基本的参数
		$this->verifyBaseParamsForStore();
		
		// 检测是否必选
		$this->verifyRequiredParamsForStore();
		
		// 特定产品的校验
		$this->verifyOptionForSpecialProduct();
	}
	
	/**
	 * 检测基本的参数
	 * @throws \Exception
	 */
	protected function verifyBaseParamsForStore()
	{
		// 截至时间
		$this->verifyEndTimeForStore();
		
		// 限额
		$this->verifyLimitNumberForStore();
		
		// 秒并发
		$this->verifyConcurrencyForStore();
	}
	
	/**
	 * 校验秒并发
	 * @throws \Exception
	 */
	protected function verifyConcurrencyForStore()
	{
		$concurrency = I('post.concurrency', '', 'trim');
		if (!$concurrency) {
			throw new \Exception('请输入秒并发');
		}
		
		if ($concurrency > 100 || $concurrency <= 0 || !is_numeric($concurrency)) {
			throw new \Exception('请输入合法的秒并发(0--100)');
		}
	}
	
	/**
	 * 开通产品检查结束时间
	 * @throws \Exception
	 */
	protected function verifyEndTimeForStore()
	{
		// 如果没有输入截至时间
		$end_time = I('post.end_time', '', 'trim');
		if (!$end_time) {
			throw new \Exception('请输入合法的截至时间');
		}
		
		// 如果截至时间
		$end_time   = strtotime($end_time);
		$today_time = strtotime(date('Y-m-d'));
		if ($end_time <= $today_time) {
			throw new \Exception('请输入合法的截至时间');
		}
	}
	
	/**
	 * 开通产品检测限额
	 * @throws \Exception
	 */
	protected function verifyLimitNumberForStore()
	{
		$daily_limit = I('post.daily_limit', '', 'trim');
		$month_limit = I('post.month_limit', '', 'trim');
		$year_limit  = I('post.year_limit', '', 'trim');
		$total_limit = I('post.total_limit', '', 'trim');
		
		// 限额是否存在
		if (!$total_limit || $total_limit < -1 || !is_numeric($total_limit)) {
			throw new \Exception('请填写合法的限额总量');
		}
		if (!$daily_limit || $daily_limit < -1 || !is_numeric($daily_limit)) {
			throw new \Exception('请填写合法的日限额');
		}
		if (!$month_limit || $month_limit < -1 || !is_numeric($month_limit)) {
			throw new \Exception('请填写合法的月限额');
		}
		if (!$year_limit || $year_limit < -1 || !is_numeric($year_limit)) {
			throw new \Exception('请填写合法的年限额');
		}
		
		// 限额之间的
		if ($month_limit != -1 && $daily_limit > $month_limit) {
			throw new \Exception('日限额必须不大于月限额');
		}
		
		if ($year_limit != -1 && $month_limit > $year_limit) {
			throw new \Exception('月限额必须不大于年限额');
		}
		
		if ($total_limit != -1 && $year_limit > $total_limit) {
			throw new \Exception('年限额必须不大于总限额');
		}
	}
	
	/**
	 * 检测必须项目
	 * @throws \Exception
	 */
	protected function verifyRequiredParamsForStore()
	{
		$data_json = json_decode(I('post.data_json', '', 'trim'), true);
		$data      = I('post.data', '');
        $product_id = I('post.product_id', '');
        //210产品开通子产品时，需要data_json中product_ids和data[product_ids]两者结合，1000产品只在data[product_ids]中
        if($product_id == 1000){
            if(!isset($data['product_ids'])){
                throw new \Exception('请选择开通的子产品');
            }else{
                //检验产品是否下架
                $this->verifyMultipleProductStatus($data['product_ids']);
            }
        }

		array_walk($data_json, function ($item) use ($data) {
			// 校验必须填写
			$this->verifyRequireForCommon($item, $data);

            // 如果是product_id、out_fields字段，必须选择一个产品开通且产品不是下架产品
            // 开通的子产品的字段在邦信分中字段为product_ids,在号码风险等级产品则叫out_fields
            if(in_array($item['name'], ['product_ids', 'out_fields'])){
                if(!isset($data[$item['name']])){
                    throw new \Exception($item['cn_name'] . '未填写');
                }else{
                    //检验产品是否下架
                    $this->verifyMultipleProductStatus($data[$item['name']]);
                }
            }

		});
	}
	
	/**
	 * 特定产品的校验
	 * @throws \Exception
	 */
	protected function verifyOptionForSpecialProduct()
	{
		$product_id = I('post.product_id', '', 'trim');
		switch ($product_id) {
			case 301:
				// H5邦秒爬
				$this->verifyItemForH5Crawler();
				break;
			case 302:
				$this->validateItemForApiCrawler();
				break;
			//催收分快捷版（17:13 2018/12/28 xiaogang.cui增加）
			case 210:
				CuiShouFenRepository::build()
									->storeProductValid();
				break;
			case 615:
				GoldShieldRepository::build()
									->storeProductValid();
				break;
		}
        $father_id  = (new ProductModel())->where(compact('product_id'))
            ->field(['father_id'])
            ->select();
        if ($father_id[0]['father_id'] == 1100) {
            $this->verifyItemForHmf();
        }
	}

    /**
     * 号码分检测特定条件
     * @throws \Exception
     */
    protected function verifyItemForHmf()
    {
        $data = I('post.data');
        //如果开启了分支等级展示，但没有配置等级区间
        if (!array_key_exists('showScoreGrade', $data)) {
            throw new \Exception('请选择是否展示分值等级');
        }
        if ($data['showScoreGrade'] == 1) {
            if ((count($data['scoreGradeLimit']) == 1) && ($data['scoreGradeLimit'][0] <= 0)) {
                throw new \Exception('请填写分值等级区间');
            }
        }
    }
	
	/**
	 * 为邦秒爬API校验参数
	 * @throws \Exception
	 */
	private function validateItemForApiCrawler()
	{
		// 检查授权状态推送地址
		$this->validateAuthorizeUrlForApiCrawler();
		
		// 检查报告属性
		$this->validateReportParams();
	}
	
	/**
	 * 检索输入的apikey appsecret是否合法
	 *
	 * @param string $apikey
	 * @param string $appsecret
	 *
	 * @throws \Exception
	 */
	private function determineHasOneCuishouProduct($apikey, $appsecret)
	{
		// 是否存在账号
		$account = $this->getOneAccountByCondition(compact('apikey', 'appsecret'));
		if (!$account) {
			throw new \Exception('催收分析APPKEY && 催收分析APPsercert不存在对应的账号');
		}
		
		// 账号下是否存在邦信分详单版V1的产品
		$account_id = $account['account_id'];
		$product_id = 101;
		$product    = $this->getOneAccountProductByCondition(compact('account_id', 'product_id'));
		if (!$product) {
			throw new \Exception('催收分析APPKEY && 催收分析APPsercert不存在对应的产品');
		}
	}
	
	/**
	 * 授权状态推送地址
	 * @throws \Exception
	 */
	private function validateAuthorizeUrlForApiCrawler()
	{
		$data                 = I('post.data');
		$authorize_notify_url = array_key_exists('authorize_notify_url', $data) ? trim($data['authorize_notify_url']) : '';
		
		// 有填充且填充不是合法的URL则抛出异常
		if ($authorize_notify_url !== '' && validateUrl($authorize_notify_url) === false) {
			throw new \Exception('授权状态推送地址不合法');
		}
	}
	
	/**
	 * h5邦秒爬检测特定条件
	 * @throws \Exception
	 */
	protected function verifyItemForH5Crawler()
	{
		// 检查爬虫属性
		$this->validateItemForH5();
		
		// 检查报告的属性
		$this->validateReportParams();
	}
	
	/**
	 * 检查爬虫属性
	 * @throws \Exception
	 */
	private function validateItemForH5()
	{
		// 限定必须传入是否输入了联系人页面
		$this->limitContactorPageForH5();
		
		// 校验H5授权链接失效限制时间
		$this->verifyTimeEffectiveForH5();
		
		// 校验H5紧急联系人
		$this->verifyEmergencyContactForH5();
		
		// 地址系列的校验
		$this->verifyUrlForH5();
		
		// 重定向地址域名白名单
		$this->verifyDomainForH5();
		
		// H5配置-显示字段和必选字段
		$this->verifyShowAndRequiredFieldForH5();
		
		// 校验授权协议
		$this->verifyProtocolContentForH5();
	}
	
	/**
	 * 检查报告的属性
	 * @throws \Exception
	 */
	private function validateReportParams()
	{
		// 检查加密推送详单
		$this->validatePushDetailForReport();
		
		// 检查返回邦信分详单版V1
		$this->validateNeedDunningForReport();
		
		// 生成报告时检查报告状态推送地址
		$this->validateReportForReport();
	}
	
	/**
	 * 生成报告时检查报告状态推送地址
	 * @throws \Exception
	 */
	private function validateReportForReport()
	{
		$data              = I('post.data');
		$report_notify_url = array_key_exists('report_notify_url', $data) ? $data['report_notify_url'] : '';
		
		// 检查报告状态推送地址
		if ($report_notify_url && validateUrl($report_notify_url) === false) {
			throw new \Exception('报告状态推送地址不合法');
		}
	}
	
	/**
	 * 检查返回邦信分详单版V1
	 * @throws \Exception
	 */
	private function validateNeedDunningForReport()
	{
		$data         = I('post.data');
		$need_dunning = $data['need_dunning'];
		$apikey       = array_key_exists('cuishou_apikey', $data) ? trim($data['cuishou_apikey']) : '';
		$appsecret    = array_key_exists('cuishou_appsecret', $data) ? trim($data['cuishou_appsecret']) : '';
		
		// 如果没有返回详单版
		if ($need_dunning != 1) {
			return;
		}
		
		// 检查选中的apikey api secret是否有对应的邦信分详单版V1产品
		$this->determineHasOneCuishouProduct($apikey, $appsecret);
	}
	
	/**
	 * 选中加密推送详单 则检查详单推送地址
	 * @throws \Exception
	 */
	private function validatePushDetailForReport()
	{
		$data           = I('post.data');
		$is_push_detail = $data['is_push_detail'];
		$notify_url     = array_key_exists('notify_url', $data) ? trim($data['notify_url']) : '';
		
		// 如果没有选中推送
		if ($is_push_detail != 1) {
			return;
		}
		if (validateUrl($notify_url) === false) {
			throw new \Exception('详单推送地址不合法');
		}
	}
	
	/**
	 * 限定必须传入是否输入了联系人页面
	 * @throws \Exception
	 */
	private function limitContactorPageForH5()
	{
		$data = I('post.data');
		// 如果没有介入紧急联系人 则不检测最多紧急联系人数量
		if (!array_key_exists('contactor_page', $data)) {
			throw new \Exception('请选定是否需要接入联系人页面contactor_page');
		}
	}
	
	/**
	 * 校验授权协议
	 * @throws \Exception
	 */
	protected function verifyProtocolContentForH5()
	{
		$data = I('post.data');
		if (!array_key_exists('protocol_default', $data) || !array_key_exists('protocol_content', $data)) {
			throw new \Exception('缺少协议的相关配置');
		}
		$protocol_default = $data['protocol_default'];
		$protocol_content = trim($data['protocol_content']);
		if ($protocol_default == 1 && $protocol_content && $protocol_content !== '<p><br></p>' && $protocol_content !== htmlentities('<p><br></p>')) {
			throw new \Exception('选择默认协议的时候, 不可以填写协议内容');
		}
		
		// wangEditor 编辑框默认带了一些符号
		if ($protocol_default != 1 && $protocol_content === htmlentities('<p><br></p>')) {
			throw new \Exception('自定义协议的时候， 协议内容不可以为空');
		}
	}
	
	/**
	 * H5配置-显示字段和必选字段
	 * @throws \Exception
	 */
	protected function verifyShowAndRequiredFieldForH5()
	{
		$data = I('post.data');
		
		if ($data['contactor_page'] === 'N') {
			return true;
		}
		
		if (!array_key_exists('ui_proposer_show_fields', $data)) {
			throw new \Exception('显示字段不可以为空');
		}
		
		if (!array_key_exists('ui_proposer_required_fields', $data)) {
			throw new \Exception('必选字段不可以为空');
		}
		
		$ui_proposer_show_fields     = $data['ui_proposer_show_fields'];
		$ui_proposer_required_fields = $data['ui_proposer_required_fields'];
		if (array_diff($ui_proposer_required_fields, $ui_proposer_show_fields)) {
			throw new \Exception('必选字段必须是显示字段');
		}
	}
	
	/**
	 * 重定向地址域名白名单
	 * @throws \Exception
	 */
	protected function verifyDomainForH5()
	{
		$data = I('post.data');
		
		if (!array_key_exists('redirect_url_domain', $data) || !trim($data['redirect_url_domain'])) {
			return true;
		}
		$redirect_url_domain = trim($data['redirect_url_domain']);
		
		// 如果没有限定 则pass
		if (!$redirect_url_domain) {
			return true;
		}
		
		$list_domain = explode(',', $redirect_url_domain);
		array_walk($list_domain, function ($domain) {
			$domain = trim($domain);
			// 检索纯数字
			if (is_numeric(str_replace('.', '', $domain))) {
				throw new \Exception('重定向地址域名白名单不合法 : ' . $domain);
			}
			// 正常检索主机IP
			if (filter_var(gethostbyname($domain), FILTER_VALIDATE_IP) === false) {
				throw new \Exception('重定向地址域名白名单不合法 : ' . $domain);
			}
		});
	}
	
	/**
	 * 地址系列的校验
	 * @throws \Exception
	 */
	protected function verifyUrlForH5()
	{
		$list_section = [
			'notify_url'           => '详单推送地址',
			'authorize_notify_url' => '授权状态回调地址',
			'close_redirect_url'   => '重定向地址',
		];
		
		$data = I('post.data');
		
		array_walk($list_section, function ($tip, $key_url) use ($data) {
			// 如果不存在key pass
			if (!array_key_exists($key_url, $data) || trim($data[$key_url]) === '') {
				return true;
			}
			
			$url = trim($data[$key_url]);
			if (validateUrl($url) === false) {
				throw new \Exception($tip . ' 不是一个合法的url');
			}
		});
	}
	
	/**
	 * 校验H5紧急联系人
	 * @throws \Exception
	 */
	protected function verifyEmergencyContactForH5()
	{
		// 最多紧急联系人数量
		$this->verifyLimitMaxForH5();
		
		// 紧急联系人总量限制
		$this->verifyEmergencyLimitForH5();
	}
	
	/**
	 * 紧急联系人总量限制
	 * @throws \Exception
	 */
	protected function verifyEmergencyLimitForH5()
	{
		$data = I('post.data');
		if ($data['contactor_page'] === 'N') {
			return;
		}
		
		// 紧急联系人限制的条数
		$count_emergency_contact_detail_limits = 0;
		array_walk($data, function ($item, $key) use (&$count_emergency_contact_detail_limits) {
			if (strpos($key, 'emergency_contact_detail_limits') !== false) {
				$count_emergency_contact_detail_limits++;
			}
		});
		
		// 限制最大
		$emergency_contact_max_number = $data['emergency_contact_max_number'];
		if ($emergency_contact_max_number < $count_emergency_contact_detail_limits) {
			throw new \Exception('H5必填紧急联系人限制限制的条数必须不大于H5最多紧急联系人数量');
		}
	}
	
	/**
	 * 最多紧急联系人数量
	 * @throws \Exception
	 */
	protected function verifyLimitMaxForH5()
	{
		$data = I('post.data');
		if ($data['contactor_page'] === 'N') {
			return true;
		}
		
		if (!array_key_exists('emergency_contact_max_number', $data)) {
			throw new \Exception('请填写最多紧急联系人数量');
		}
		
		$emergency_contact_max_number = $data['emergency_contact_max_number'];
		if (!is_numeric($emergency_contact_max_number)) {
			throw new \Exception('最多紧急联系人数量必须是0--15的正整数');
		}
		
		$emergency_contact_max_number = (int)$emergency_contact_max_number;
		if ($emergency_contact_max_number < 0 || $emergency_contact_max_number > 15) {
			throw new \Exception('最多紧急联系人数量必须是0--15的正整数');
		}
	}
	
	/**
	 * 校验H5授权链接失效限制时间
	 * @throws \Exception
	 */
	protected function verifyTimeEffectiveForH5()
	{
		$data = I('post.data');
		// 如果这个字段是必须的有填充 则必须是1-36之间的正整数
		if (array_key_exists('effective_authorization_time', $data) && $data['effective_authorization_time']) {
			
			$effective_authorization_time = $data['effective_authorization_time'];
			if (!is_numeric($effective_authorization_time)) {
				throw new \Exception('H5授权链接失效限制时间必须是个整数');
			}
			
			$effective_authorization_time = (int)$effective_authorization_time;
			if ($effective_authorization_time != -1 && ($effective_authorization_time < 1 || $effective_authorization_time > 36)) {
				throw new \Exception('H5授权链接失效限制时间允许范围是1--36的正整数(不限制-1)');
			}
		}
	}
	
	/**
	 * 检测公用的部分
	 *
	 * @param array $item 单个的单元
	 * @param array $data 结果
	 *
	 * @throws \Exception
	 */
	protected function verifyRequireForCommon(array $item, array $data)
	{
		// 检查是否必须
		if ($item['is_need'] && (!array_key_exists($item['name'], $data) || !isset($data[$item['name']]))) {
			throw new \Exception($item['cn_name'] . '未填写');
		}
	}

    /**
     * 检测产品是否下架
     * @throws \Exception
     */
    private function verifyProductStatus()
    {
        $product_id = I('post.product_id', '', 'trim');
        $where['product_id'] = ['EQ', $product_id];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $exists = $this->getOneProductByCondition($where);
        if (!$exists) {
            throw new \Exception('抱歉,该产品已经下架哦');
        }
    }

    /**
     * 检测多个产品中是否有下架产品
     * @throws \Exception
     */
    private function verifyMultipleProductStatus($product_ids = []){
        if(empty($product_ids)){
            throw new \Exception('请选择开通的子产品');
        }

        $where['product_id'] = ['IN', $product_ids];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $usableList = (new ProductModel())->where($where)
            ->field('product_id')
            ->select();

        $usableList = array_column($usableList, 'product_id');
        $diff = [];
        foreach ($product_ids as $pid){
            if(!in_array($pid, $usableList)){
                $diff[] = $pid;
            }
        }

        if(!empty($diff)){
            throw new \Exception('产品'.implode(',', $diff).'已下架哦');
        }

    }

	/**
	 * 检测该类型产品是否已经创建
	 * @throws \Exception
	 */
	private function verifyUniqueTypeForStore()
	{
		$product_id = I('post.product_id', '', 'trim');
		$account_id = I('post.account_id', '', 'trim');
		
		$exists = $this->getAccountProductByCondition(compact('product_id', 'account_id'));
		if ($exists) {
			throw new \Exception('抱歉,同一类型的产品只可以创建一次哦');
		}
	}
	
	/**
	 * 检测必选条件是否存在
	 * @throws \Exception
	 */
	private function verifyParamsExistsForStore()
	{
		$product_id = I('post.product_id', '', 'trim');
		$account_id = I('post.account_id', '', 'trim');
		$data_json  = I('post.data_json', '', 'trim');
		$data       = I('post.data', '');
		
		if (!$product_id) {
			throw new \Exception('请选择产品');
		}
		
		if (!$account_id || !$data_json) {
			throw new \Exception('请传递account_id && data_json');
		}
		
		if (!$data) {
			throw new \Exception('产品的特殊配置至少要选择一项');
		}
	}
	
	/**
	 * 新增产品 view
	 * @throws \Exception
	 * @return array
	 */
	public function addProduct()
	{
		// 检测条件
		$this->verifyParamsForCreate();
		
		// 账号信息
		$account_info = $this->getAccountForCreateProduct();
		
		//数据权限校验
		DataAuthController::instance()
						  ->validAllowDoCustomer($account_info['customer_id']);
		
		// 编辑账号下未开通的产品
		$product_list = $this->getProductListForCreate();
		
		// 格式化特定产品
		$product_list = $this->formatProductForCreate($product_list);
		
		//邦信分快捷版全部字段
		$bxf_all_pid = (new ProductModel())->where(['father_id' => 1000])
										   ->field(['product_id', 'product_name', 'mark'])
										   ->order('id')
										   ->select();

        /*
		//邦信分快捷版评分类字段
		$bxf_sore_pid = array_column((new ProductTypeModel())->where(['type' => 1])
															 ->field(['product_id'])
															 ->select(), 'product_id');
		$cmccConfig   = (new BxfshortAllCmccConfigModel())->order('sort desc')
														  ->select();
		$cmccConfig   = array_column($cmccConfig, 'name', 'flag');
        */
		
		//return compact('account_info', 'product_list', 'bxf_all_pid', 'bxf_sore_pid', 'cmccConfig');
		return compact('account_info', 'product_list', 'bxf_all_pid');
	}
	
	/**
	 * 格式化产品列表
	 *
	 * @param array $product_list
	 *
	 * @return array
	 */
	protected function formatProductForCreate(array $product_list)
	{
		return array_map(function ($item) {
			switch ($item['product_id']) {
				case 301:
					// 301 h5邦秒爬格式化一波
					return $this->formatProductForH5($item);
					break;
				case 302:
					return $this->formatProductForApi($item);
					break;
				default :
					return $item;
			}
		}, $product_list);
	}
	
	/**
	 * 格式化crawler api
	 *
	 * @param array $product
	 *
	 * @return array
	 */
	private function formatProductForApi(array $product)
	{
		$data = json_decode($product['data'], true);
		// 格式化data
		$data = array_map(function ($item) {
			switch ($item['name']) {
				case 'cid':
					return $this->formatCidForApi($item);
					break;
				default :
					return $item;
			}
		}, $data);
		
		// 追加运行商报告的配置
		$data = $this->appendReportToApiProduct($data);
		
		$product['data'] = json_encode($data, JSON_UNESCAPED_UNICODE);
		
		return $product;
	}
	
	/**
	 * 追加运行商报告的配置
	 *
	 * @param array $data
	 *
	 * @return array
	 */
	private function appendReportToApiProduct(array $data)
	{
		// 运营商报告的配置
		$data_report = $this->getReportProductConfig();
		
		return array_merge($data, $data_report);
	}
	
	/**
	 * 运营商报告的配置
	 * @return array
	 */
	private function getReportProductConfig()
	{
		$product_id     = $this->product_report_id;
		$product_report = $this->getOneProductByCondition(compact('product_id'));
		
		// 如果当前账号已经开通过了运营商报告 则覆盖掉默认
		return $this->determineOpenedReportProduct($product_report);
	}
	
	/**
	 * 生成报告配置
	 *
	 * @param array $product_report
	 *
	 * @return array
	 */
	private function determineOpenedReportProduct(array $product_report)
	{
		$id         = I('get.id', '', 'trim');
		$account    = $this->getOneAccountByCondition(compact('id'));
		$account_id = $account['account_id'];
		$product_id = $this->product_report_id;
		
		// 如果没有开通报告
		$product_opened = $this->getOneAccountProductByCondition(compact('account_id', 'product_id'));
		if (!$product_opened) {
			return json_decode($product_report['data'], true);
		}
		
		// 如果已经开通了报告
		$data      = json_decode($product_report['data'], true);
		$data_open = json_decode($product_opened['data'], true);
		
		return array_map(function ($item) use ($data_open) {
			$item['default'] = array_key_exists($item['name'], $data_open) ? $data_open[$item['name']] : '';
			
			return $item;
		}, $data);
	}
	
	/**
	 * h5邦秒爬格式化CID
	 *
	 * @param array $item 格式化cid
	 *
	 * @return array
	 */
	protected function formatCidForApi(array $item)
	{
		$id              = I('get.id');
		$account         = $this->getOneAccountByCondition(compact('id'));
		$item['default'] = $account['account_id'];
		
		return $item;
	}
	
	/**
	 * h5邦秒爬格式化一波
	 *
	 * @param array $product
	 *
	 * @return array
	 */
	protected function formatProductForH5(array $product)
	{
		$data = json_decode($product['data'], true);
		
		// 格式化data
		$data = array_map(function ($item) {
			switch ($item['name']) {
				case 'cid':
					$item = $this->formatCidForH5($item);
					break;
				case 'token':
					$item = $this->formatTokenForH5($item);
					break;
				
			}
			
			return $item;
		}, $data);
		
		// 追加运行商报告的配置
		$data = $this->appendReportToApiProduct($data);
		
		$product['data'] = json_encode($data, JSON_UNESCAPED_UNICODE);
		
		return $product;
	}
	
	/**
	 * 格式化token
	 *
	 * @param array $item
	 *
	 * @return array
	 */
	protected function formatTokenForH5(array $item)
	{
		$item['default'] = genCrawlerToken();
		
		return $item;
	}
	
	/**
	 * h5邦秒爬格式化CID
	 *
	 * @param array $item 格式化cid
	 *
	 * @return array
	 */
	protected function formatCidForH5(array $item)
	{
		$id              = I('get.id');
		$account         = $this->getOneAccountByCondition(compact('id'));
		$item['default'] = $account['account_id'];
		
		return $item;
	}
	
	/**
	 * 检测条件
	 * @throws \Exception
	 */
	protected function verifyParamsForCreate()
	{
		$id = I('get.id');
		if (!$id) {
			throw new \Exception('缺少必要参数id');
		}
		
		$account = $this->getOneAccountByCondition(compact('id'));
		if (!$account) {
			throw new \Exception('传入的是错误的参数ID :' . $id);
		}
	}
	
	
	/**
	 * 编辑账号下未开通的产品
	 * @return array
	 */
	protected function getProductListForCreate()
	{
		// 全量的产品
//		$back_status  = 1;
//		$list_product = $this->getProductListByCondition(compact('back_status'));
        $where['back_status'] = ['EQ', 1];
        $where['status'] = ['NEQ', ProductModel::STATUS_DISABLE];
        $list_product = $this->getProductListByCondition($where);
		// 已经开通的产品
		$list_opened_ones = $this->getOpenedProductForAccount();
		
		// 获取没有没有开通的部分
		return $this->getNotOpenedProductListForAccount($list_product, $list_opened_ones);
	}
	
	/**
	 * 获取没有没有开通的部分
	 *
	 * @param array $list_product
	 * @param array $list_opened_ones 已经开通的产品
	 *
	 * @return array
	 */
	protected function getNotOpenedProductListForAccount(array $list_product, array $list_opened_ones)
	{
		// 已经开通过的产品ID
		$list_opened_ones = $this->getOpenIdsForCreating($list_opened_ones);
		
		// 容器
		$list_container = [];
		array_walk($list_product, function ($item) use (&$list_container, $list_opened_ones) {
			$product_id = $item['product_id'];
			if (!in_array($product_id, $list_opened_ones)) {
				array_push($list_container, $item);
			}
		});
		
		return $list_container;
	}
	
	/**
	 * 获取已经开通过的产品ID
	 *
	 * @param array $list_opened_ones 已经开通的产品
	 *
	 * @return array
	 */
	private function getOpenIdsForCreating(array $list_opened_ones)
	{
		$list_opened_ones = array_column($list_opened_ones, 'product_id');
		
		// 如果一个都没有开通, 则不限制
		if (array_intersect($this->list_only_one, $list_opened_ones) === []) {
			return $list_opened_ones;
		}
		
		// 下面的两个不能共存
		return array_merge($list_opened_ones, $this->list_only_one);
	}
	
	
	/**
	 * 获取一个账号已经开通的产品列表
	 * @return array
	 */
	protected function getOpenedProductForAccount()
	{
		$id         = I('get.id');
		$account    = $this->getOneAccountByCondition(compact('id'));
		$account_id = $account['account_id'];
		
		return $this->getAccountProductByCondition(compact('account_id'));
	}

	/**
	 * 获取账号下开通的产品
	 *
	 * @param array $where
	 *
	 * @return array
	 */
	protected function getAccountProductByCondition(array $where)
	{
		$data = (new AccountProductModel())->where($where)
										   ->select();
		
		return $data;
	}
	
	/**
	 * 获取产品列表(通过条件)
	 *
	 * @param array $where 条件
	 *
	 * @return array
	 */
	protected function getProductListByCondition(array $where)
	{
		$productRepository = new ProductRepository();
		$data              = $productRepository->data($where);
		$data              = array_column($data, null, 'product_id');
		
		return $data;
	}
	
	/**
	 * 获取要创建产品
	 * @return array
	 */
	protected function getAccountForCreateProduct()
	{
		$id = I('get.id');
		
		return $this->getOneAccountByCondition(compact('id'));
	}
	
	/**
	 * 根据条件获取一个账号
	 *
	 * @param array $where 条件
	 *
	 * @return array
	 */
	protected function getOneAccountByCondition(array $where)
	{
		return (new AccountModel())->where($where)
								   ->find();
	}
	
	/**
	 * 根据查询条件获取账号列表数据
	 *  直接使用join关联查询
	 * <AUTHOR>
	 * @datetime 9:59 2018/12/19
	 *
	 * @access   public
	 *
	 * @param $isTest boolean 是否为售前测试客户列表
	 *
	 * @return array
	 **/
	public function getDataForList($isTest = false)
	{
		//获取查询条件
		$where = $this->getWhere();
		if ($isTest) {
			$where['account.type'] = 2;
		} else {
			if (!array_key_exists('account.type', $where)) {
				$where['account.type'] = ['in', [0, 1]];
			}
		}
		//获取账号数据
		$accountData = $this->getAccountDataForList($where);
		$data        = $accountData['data'];
		$page        = $accountData['page'];
		//获取所有已开通的产品数据
		$accountProductData = $this->getAccountProductWithProduct(array_column($data, 'account_id'));
		//整合账号、产品数据
		$data = $this->imposeAccountAndProductData($data, $accountProductData);
		
		return compact('data', 'page');
	}
	
	/**
	 * 根据查询条件获取账号的导出数据
	 * <AUTHOR>
	 * @datetime 17:27 2018/12/19
	 *
	 * @access   public
	 *
	 * @return array
	 **/
	public function getDataForExport()
	{
		//获取查询条件
		$where = $this->getWhere();
		//获取账号数据
		$accountData = $this->getAccountDataForList($where, false);
		$data        = $accountData['data'];
		//获取所有已开通的产品数据
		$accountProductData = $this->getAccountProductWithProduct(array_column($data, 'account_id'));
		//整合账号、产品数据
		$data = $this->imposeAccountAndProductData($data, $accountProductData, false);
		
		return compact('data');
	}
	
	/**
	 * 账号列表数据
	 * <AUTHOR>
	 * @datetime 15:17 2018/12/19
	 *
	 * @access   public
	 *
	 * @param $where array 查询条件
	 * @param $page  boolean 是否分页（不分页应用于导出功能）
	 *
	 * @return array
	 **/
	public function getAccountDataForList($where, $page = true)
	{
		$result = [];
		$model  = new AccountModel();
		if ($page) {
			//获取数据分页
			$count          = $model->join('customer ON account.customer_id = customer.customer_id')
									->where(DataAuthController::instance()
															  ->getAccountWhere())
									->where($where)
									->count();
			$page           = new \Common\ORG\Page($count, C('LIST_ROWS'));
			$model          = $model->limit($page->firstRow, $page->listRows);
			$result['page'] = $page->show();
		}
		//根据条件获取数据
		$result['data'] = $model->field('account.id,account_id,account_name,customer.name,apikey,appsecret,account.type,account.status,account.end_time,account.admin,account.cid,account.close_zhilian')
								->join('customer ON account.customer_id = customer.customer_id')
								->where($where)
								->where(DataAuthController::instance()
														  ->getAccountWhere())
								->order('status DESC, id DESC')
								->select();
		
		return $result;
	}
	
	/**
	 * 获取账号列表查询条件
	 * <AUTHOR>
	 * @datetime 10:04 2018/12/19
	 *
	 * @access   public
	 *
	 * @return array
	 **/
	public function getWhere()
	{
		$where      = [];
		$account_id = trim(I('get.account_id'));
		if (!empty($account_id)) {
			$where[] = compact('account_id');
		}
		$account = I('get.account');
		if (!empty($account)) {
			$where[] = ['account_id' => $account];
		}
		//产品查询
		$product = I('get.product');
		if (!empty($product)) {
			$account_product_model = new AccountProductModel();
			$data                  = $account_product_model->where([
				['product_id' => $product],
			])
														   ->field('account_id')
														   ->select();
			if (!empty($data)) {
				$account_id = array_column($data, 'account_id');
				$where[]    = [
					'account_id' => [
						'in',
						$account_id,
					],
				];
			} else {
				//如果没有账号开通了此产品
				$where[] = [
					'account_id' => '000',
				];
			}
		}
        $customer = I('get.customer');

        if (!empty($customer)) {
            $where[] = ['account.customer_id' => $customer];
        }else {
            $group = I('get.group');
            if (!empty($group)) {
                $customer_arr = (new CustomerModel())->where([['group_id' => $group]])->field('customer_id')->select();
                $customer_arr = array_column($customer_arr, 'customer_id');
                $where[] = ['account.customer_id' => ['in' , $customer_arr]];
            }
        }

		$cid = trim(I('get.cid'));
		if (!empty($cid)) {
			$where[] = compact('cid');
		}
		$apikey = trim(I('get.apikey'));
		if (!empty($apikey)) {
			$where[] = compact('apikey');
		}
		$type = I('get.type');
		if ($type !== '' && $type != -1) {
			$where[] = ['account.type' => $type];
		}
		$status = I('get.status');
		if ($status !== '' && $status != -1) {
			$where[] = ['account.status' => $status];
		}
        $close_zhilian = I('get.close_zhilian');
        if ($close_zhilian !== '' && $close_zhilian != -1) {
            $where[] = ['account.close_zhilian' => $close_zhilian];
        }
		$where[] = [
			'account.father_id' => ['neq', '0'],
		];
		$where[] = [
			'account.is_delete' => ['eq', 0],
		];
		$where[] = [
			'customer.is_delete' => ['eq', 0],
		];
		
		return $where;
	}
	
	/**
	 * 获取每个账号的产品（先获取所有涉及到的产品，然后在便利组合数据）
	 * <AUTHOR>
	 * @datetime 15:10 2018/12/19
	 *
	 * @access   protected
	 *
	 * @param $account_id array 需要查询的账号数据
	 *
	 * @return array
	 **/
	protected function getAccountProductWithProduct($account_id)
	{
		if (empty($account_id)) {
			return [];
		}
		$accountProductModel = new AccountProductModel();
		
		return $accountProductModel->join('product ON account_product.product_id=product.product_id')
								   ->where([
									   ['account_id' => ['in', $account_id]],
									   // ['back_status' => 1],
								   ])
								   ->field('account_id,product.product_id,product_name,account_product.status,account_product.id,account_product.end_time,account_product.source_tags,back_status')
								   ->order('status DESC, id DESC')
								   ->select();
	}
	
	/**
	 * 整合账号、产品数据
	 * <AUTHOR>
	 * @datetime 15:14 2018/12/19
	 *
	 * @access   protected
	 *
	 * @param $accountData    array 账号数据
	 * @param $accountProduct array 账号产品数据
	 * @param $forList        boolean 是否为列表处理
	 *
	 *
	 * @return array
	 **/
	public function imposeAccountAndProductData($accountData, $accountProduct, $forList = true) {
        // $tags_map = [1 => '朴道',10 => '浙数交'];
        $tags_map = (new CommonEnumModel())->getEnumPairs(1);
        unset($tags_map[0]);//去掉羽乐科技

		$time         = time();
		$callback_url = urlencode($_SERVER['REQUEST_URI']);

        $father_without_sub_ids =(new ProductModel())->getFatherProductsWithoutGeneral();

		return array_map(function ($item) use ($accountProduct, $forList, $time, $callback_url,$tags_map,$father_without_sub_ids) {
			$item['product'] = [];
			foreach ($accountProduct as $product) {
				if ($product['account_id'] == $item['account_id']) {
                    $tags = [];
                    $tag_arr = explode(',',trim($product['source_tags'],','));
                    foreach($tags_map as $k => $n){
                        if(in_array($k, $tag_arr)){
                            $tags[$n] = '已换签';
                        }else{
                            $tags[$n] = '未换签';
                        }
                    }

					if ($forList) { //页面列表用数据
						if (!$product['status']) {
							$style = 'style="color:#666;"';
						} else if ($product['end_time'] < $time) {
							$style = 'style="color:#ff0000;"';
						} else {
							$style = '';
						}
                        if($product['back_status'] == 1) {
                            $product_str = '<a href="' . U('Account/Account/editProduct', ['account_product_id' => $product['id']]) . '?callback_url=' . $callback_url . '" ' . $style . '>' . $product['product_name'] . '</a>';
                            //页面中仅展示库中配置可展示的
                            $item['product'][] = $product_str;
                        }
                        if(!in_array($product['product_id'], $father_without_sub_ids)){
                            $label = '';
                            if(!empty($tags)){
                                foreach($tags as $n => $tag){
                                    if($tag == '已换签') {
                                        $label .= '<span class="tags_green" aria-hidden="true">'.mb_substr($n,0,1,"utf-8").'</span>';
                                    }else{
                                        $label .= '<span class="tags_grey" aria-hidden="true">'.mb_substr($n,0,1,"utf-8").'</span>';
                                    }
                                }
                                $label .= $product['product_name'];
                            }else{
                                $label = '<span style="display: inline-block;  width: 1em;"></span>' . $product['product_name'];
                            }
                            $item['change_tags'][] = $label;
                        }
					} else { //excel导出用数据
						$product_str = $product['product_name'];
                        $item['product'][$product['product_id']]     = $product_str;
                        $item['change_tags_pd'][$product['product_id']] = $tags['朴道'];
                        $item['change_tags_vuj'][$product['product_id']] = $tags['浙数交'];
                        $item['ap_end_time'][$product['product_id']] = $product['end_time'];//account_product.ent_time
					}
				}
			}
			return $item;
		}, $accountData);
	}
	
	/**
	 * 获取一个Excel对象
	 * <AUTHOR>
	 * @datetime 18:17 2018/12/19
	 *
	 * @access   public
	 *
	 * @return \PHPExcel
	 **/
	public function getExcelObj()
	{
		include LIB_PATH . 'Org/PHPExcel/PHPExcel.php';
		
		return new \PHPExcel();
	}
	
	/**
	 * 设置Excel资源的头部信息
	 * <AUTHOR>
	 * @datetime 17:50 2018/12/19
	 *
	 * @access   public
	 *
	 * @param $excel \PHPExcel excel资源
	 *
	 * @return void
	 **/
	public function setExcelHeader(\PHPExcel &$excel)
	{
		$excel->setActiveSheetIndex(0);
		$excel->getActiveSheet()
			  ->setTitle('账号列表');
		$col   = 1;
		$start = $row = 65;
		$title = [
			[
				'title' => '账号ID',
				'width' => 18,
			],
			[
				'title' => '账号名称',
				'width' => 30,
			],
			[
				'title' => '所属客户',
				'width' => 25,
			],
			[
				'title' => 'APIKEY',
				'width' => 33,
			],
			[
				'title' => 'APISECRET',
				'width' => 25,
			],
			[
				'title' => '已开通产品',
				'width' => 40,
			],
			[
				'title' => '是否换签朴道',
				'width' => 20,
			],			[
				'title' => '是否换签浙数交',
				'width' => 20,
			],
			[
				'title' => '截止日期',
				'width' => 14,
			],
			[
				'title' => '账号类型',
				'width' => 10,
			],
			[
				'title' => '操作人',
				'width' => 20,
			],
		];
		foreach ($title as $item) {
			$excel->getActiveSheet()
				  ->setCellValue(chr($row) . $col, $item['title']);
			$excel->getActiveSheet()
				  ->getColumnDimension(chr($row))
				  ->setWidth($item['width']);
			$row++;
		}
		$excel->getActiveSheet()
			  ->getStyle(chr($start) . $col . ':' . chr($row - 1) . $col)
			  ->getFont()
			  ->setBold(true);
		$excel->getActiveSheet()
			  ->getStyle(chr($start) . $col . ':' . chr($row - 1) . $col)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_JUSTIFY);
		$excel->getActiveSheet()
			  ->getStyle(chr($start) . $col . ':' . chr($row - 1) . $col)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$excel->getActiveSheet()
			  ->getRowDimension($col)
			  ->setRowHeight(18);
	}
	
	/**
	 * 设置Excel的数据
	 * <AUTHOR>
	 * @datetime 18:14 2018/12/19
	 *
	 * @access   public
	 *
	 * @param $excel \PHPExcel excel资源
	 * @param $data  array 数据
	 *
	 * @return void
	 **/
	public function setExcelData(\PHPExcel &$excel, $data)
	{
		$user_data = (new SystemUserModel())->field('realname, username')->select();
		$user_data = array_column($user_data, 'realname', 'username');

        $style_1 = [
            'alignment' => [
                'horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                'vertical'   => \PHPExcel_Style_Alignment::VERTICAL_CENTER,
            ],
        ];

        $style_2 = [
            'alignment' => [
                'horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical'   => \PHPExcel_Style_Alignment::VERTICAL_CENTER,
            ],
        ];

        $father_without_sub_ids =(new ProductModel())->getFatherProductsWithoutGeneral();
		$startCol  = $col = 2;
        try {
            $sheet = $excel->getActiveSheet();
            foreach ($data as $item) {
                $start_range = $col;
                $range = 0;
                $idx = 0;
                foreach($item['product'] as $k => $p){
                    if(in_array($k,$father_without_sub_ids)){
                        continue;
                    }
                    if($idx == 0) {
                        $sheet->setCellValue('A' . $col, $item['account_id']);
                        $sheet->setCellValue('B' . $col, $item['account_name']);
                        $sheet->setCellValue('C' . $col, $item['name']);
                        $sheet->setCellValue('D' . $col, $item['apikey']);
                        $sheet->setCellValue('E' . $col, $item['appsecret']);
                        //F
                        //G
                        //H
                        $sheet->setCellValue('J' . $col, ($item['type'] == 1 ? '正式' : '测试'));
                        $sheet->setCellValue('K' . $col, $user_data[$item['admin']]);
                    }
                    $sheet->setCellValue('F' . $col, $p);//子产品
                    $sheet->setCellValue('G' . $col, $item['change_tags_pd'][$k]);//子产品
                    $sheet->setCellValue('H' . $col, $item['change_tags_vuj'][$k]);//子产品
                    $sheet->setCellValue('I' . $col, date('Y-m-d', $item['ap_end_time'][$k]));//产品截止时间

                    $range ++;
                    $col ++;
                    $idx ++;
                }
                $end_range = $start_range + $range - 1;
                if($range > 1) {
                    $sheet->mergeCells('A' . $start_range . ':' . 'A' . $end_range)->getStyle('A' . $start_range)->applyFromArray($style_1);
                    $sheet->mergeCells('B' . $start_range . ':' . 'B' . $end_range)->getStyle('B' . $start_range)->applyFromArray($style_1);
                    $sheet->mergeCells('C' . $start_range . ':' . 'C' . $end_range)->getStyle('C' . $start_range)->applyFromArray($style_1);
                    $sheet->mergeCells('D' . $start_range . ':' . 'D' . $end_range)->getStyle('D' . $start_range)->applyFromArray($style_1);
                    $sheet->mergeCells('E' . $start_range . ':' . 'E' . $end_range)->getStyle('E' . $start_range)->applyFromArray($style_1);
                    // $sheet->mergeCells('H' . $start_range . ':' . 'H' . $end_range)->getStyle('H' . $start_range)->applyFromArray($style_2);
                    $sheet->mergeCells('J' . $start_range . ':' . 'J' . $end_range)->getStyle('J' . $start_range)->applyFromArray($style_2);
                    $sheet->mergeCells('K' . $start_range . ':' . 'K' . $end_range)->getStyle('K' . $start_range)->applyFromArray($style_2);
                }else{
                    // $sheet->getStyle('H' . $start_range)->applyFromArray($style_2);
                    $sheet->getStyle('J' . $start_range)->applyFromArray($style_2);
                    $sheet->getStyle('K' . $start_range)->applyFromArray($style_2);
                }
            }
            $sheet->getRowDimension($startCol . ':' . $col)->setRowHeight(18);
        } catch (\PHPExcel_Exception $e) {
            throw new \PHPExcel_Exception($e->getMessage());
        }
	}
	
	/**
	 * 下载大包好的excel数据
	 * <AUTHOR>
	 * @datetime 18:25 2018/12/19
	 *
	 * @access   public
	 *
	 * @param $excel \PHPExcel excel资源
	 *
	 * @return void
	 **/
	public function downloadExcel(\PHPExcel &$excel)
	{
		include LIB_PATH . 'Org/PHPExcel/PHPExcel/Writer/Excel2007.php';
		$objWriter = new \PHPExcel_Writer_Excel5($excel);
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
		header("Content-Type:application/force-download");
		header("Content-Type:application/vnd.ms-execl");
		header("Content-Type:application/octet-stream");
		header("Content-Type:application/download");;
		header('Content-Disposition:attachment;filename="账号列表_' . date('Y-m-d') . '.xls"');
		header("Content-Transfer-Encoding:binary");
		$objWriter->save('php://output');
	}
}