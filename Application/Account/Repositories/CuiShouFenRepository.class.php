<?php

namespace Account\Repositories;

use Account\Model\AccountProductModel;
use Account\Model\ProductModel;

/**
 * 催收分快捷版【编号210】特殊定制类
 * <AUTHOR>
 * @date   10:09 2018/12/29
 */
class CuiShouFenRepository
{
	protected static $repository;
	public           $product_id = 210;
	
	public static function build()
	{
		if (empty(self::$repository)) {
			self::$repository = new self();
		}
		
		return self::$repository;
	}
	
	protected function __construct()
	{
	
	}
	
	/**
	 * 催收分快捷版开通时候使用的功能
	 * 催收分快捷版在开通时，不仅开通本身产品，而是根据其选择的子产品进行依次开通
	 * <AUTHOR>
	 * @datetime 10:14 2018/12/29
	 *
	 * @access   public
	 *
	 * @return void
	 **/
	public function storeProduct(AccountRepository $repository)
	{
		$childrenProduct = I('post.data')['product_ids'];
		$childrenConfig  = json_encode([], JSON_UNESCAPED_UNICODE);
		$params          = $repository->genBaseParamsForStore();
		//优先开通子产品
		$Model = M();
		$Model->startTrans();
		foreach ($childrenProduct as $item) {
			$params['product_id'] = $item;
			$params['data']       = $childrenConfig;
			$insert_id            = $repository->storeDo($params);
			if (!$insert_id) {
				$Model->rollback();
				throw new \Exception('store children product failed，product_id is ' . $item);
			}
			// 记录日志
			$repository->logCreate($insert_id);
		}
		//最后开通210产品
		try {
			$repository->storeProductForCommon();
			$Model->commit();
		} catch (\Exception $exception) {
			$Model->rollback();
			throw new \Exception('store father product failed，exception is ' . $exception->getMessage());
		}
	}
	
	/**
	 * 催收分快捷版开通时候的验证，
	 * 不进行验证data配置，因为对于催收分快捷版而言，data配置失效【10:19 2018/12/29】
	 * 只需要验证是否选择了子产品
	 * <AUTHOR>
	 * @datetime 10:18 2018/12/29
	 *
	 * @access   public
	 *
	 * @return void
	 **/
	public function storeProductValid()
	{
		$data        = I('post.data');
		$product_ids = $data['product_ids'];
		if (empty($product_ids)) {
			throw new \Exception('请选择至少一项需要开通的接口');
		}
	}
	
	/**
	 * 获取催收分快捷版的所有子产品
	 * <AUTHOR>
	 * @datetime 13:33 2018/12/29
	 *
	 * @access   protected
	 *
	 * @param $where array 查询条件
	 *
	 * @return array
	 **/
	protected function getChildrenProduct($where = [], $field = 'product_id, product_name')
	{
		$productRepository = new ProductRepository();
		$father_id         = $this->product_id;
		$where             = array_merge(compact('father_id'), $where);
		
		return $productRepository->data($where, $field);
	}
	
	/**
	 * 催收分快捷版修改开通产品
	 * <AUTHOR>
	 * @datetime 16:20 2018/12/29
	 *
	 * @access   public
	 *
	 * @param $params array 开通产品的基本数据
	 * @param $data   array 开通产品的data数据
	 *
	 * @return void
	 **/
	public function updateProduct($params, $data)
	{
		$accountProductModel = new AccountProductModel();
		$data                = json_decode($data['data'], true);
		//获取本次开通的子产品
		$newProduct = $data['product_ids'];
		
		//获取之前开通的子产品
		$account_id = $params['account_id'];
		$oldProduct = $accountProductModel->where([
			'account_id' => $account_id,
			'product_id' => ['in', array_column($this->getChildrenProduct(), 'product_id')],
		])
										  ->select();
		$oldProduct = array_column($oldProduct, 'product_id');
		//求交集，作为需要进行修改的开通产品
		$updateProductId = array_intersect($newProduct, $oldProduct);
		//以oldProduct为依据做差集，作为需要删除的开通产品
		$deleteProductId = array_diff($oldProduct, $newProduct);
		//以newProduct为依据做差集，作为需要增加的开通产品
		$addProductId   = array_diff($newProduct, $oldProduct);
		$childrenConfig = json_encode([]);
		//修改子产品
		$model = M();
		$model->startTrans();
		try {
			if (!empty($updateProductId)) {
				$params_update         = $params;
				$params_update['data'] = $childrenConfig;
				unset($params_update['product_id']);
				$accountProductModel->where([
					'product_id' => ['in', $updateProductId],
					'account_id' => $account_id,
				])
									->save($params_update);
			}
			//增加子产品
			foreach ($addProductId as $item) {
				$params_add               = $params;
				$params_add['data']       = $childrenConfig;
				$params_add['product_id'] = $item;
				$params_add['create_at']  = time();
				$res                      = $accountProductModel->add($params_add);
			}
			//删除子产品
			if (!empty($deleteProductId)) {
				$accountProductModel->where([
					'product_id' => ['in', $deleteProductId],
					'account_id' => $account_id,
				])
									->delete();
			}
			//修改父级产品
			$params_update = $params;
			unset($params_update['product_id']);
			$accountProductModel->where([
				'product_id' => $this->product_id,
				'account_id' => $account_id,
			])
								->save($params_update);
		} catch (\Exception $exception) {
			$model->rollback();
			throw new \Exception('修改失败, error :' . $exception->getMessage());
		}
		$model->commit();
	}
	
	/**
	 * 获取当前产品的data配置
	 * <AUTHOR>
	 * @datetime 16:56 2019/1/8
	 *
	 * @access   protected
	 *
	 * @return array
	 **/
	protected function getProductData()
	{
		$productRepository = new ProductRepository();
		$product_id        = $this->product_id;
		$data              = $productRepository->one(compact('product_id'));
		
		return json_decode($data['data'], true);
	}
	
	/**
	 * 根据用户选择的210配置项，获取需要开通子产品的产品ID
	 * <AUTHOR>
	 * @datetime 11:06 2019/1/9
	 *
	 * @access   protected
	 *
	 * @param $fields array 选择的配置项
	 *
	 * @return array
	 **/
	protected function getStoreChildrenProductId($fields)
	{
		return array_column((new ProductModel())->where([
			'father_id' => 210,
			'mark'      => ['in', $fields],
		])
												->field(['product_id'])
												->select(), 'product_id');
	}
}