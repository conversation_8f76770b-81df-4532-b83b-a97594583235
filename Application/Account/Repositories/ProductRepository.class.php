<?php

namespace Account\Repositories;

use Account\Model\ProductModel;
use Common\Common\CurlTrait;
use Common\Common\WechatBackendExceptionTrait;

class ProductRepository
{
    use CurlTrait, WechatBackendExceptionTrait;
    protected $model;

    public function __construct()
    {
        $this->model = new ProductModel();
    }

    /**
     * 多条产品数据批量查询（快捷查询）
     * <AUTHOR>
     * @datetime 16:46 2018/12/26
     *
     * @access public
     * @param $where array 查询的条件
     * @param $field string|array 需要查询的字段
     * @param $order string 排序方式
     *
     * @return array
     **/
    public function data($where = [], $field = '*', $order = null)
    {
        return $this->buildModelForQuick($where, $field, $order)->select();
    }

    /**
     * 单条产品数据查询（快捷查询）
     * <AUTHOR>
     * @datetime 16:50 2018/12/26
     *
     * @access public
     * @param $where array 查询的条件
     * @param $field string|array 需要查询的字段
     * @param $order string 排序方式
     *
     * @return array
     **/
    public function one($where = [], $field = '*', $order = null)
    {
        return $this->buildModelForQuick($where, $field, $order)->find();
    }

    /**
     * 产品数据数量查询（快捷查询）
     * <AUTHOR>
     * @datetime 18:12 2018/12/26
     *
     * @access public
     * @param $where array 查询的条件
     *
     * @return integer
     **/
    public function count($where = [])
    {
        return $this->buildModelForQuick($where, '*', null)->count();
    }

    /**
     * 为快捷查询model构造器
     * <AUTHOR>
     * @datetime 16:51 2018/12/26
     *
     * @access protected
     * @param $where array 查询的条件
     * @param $field string|array 需要查询的字段
     * @param $order string 排序方式
     *
     * @return ProductModel
     **/
    protected function buildModelForQuick($where, $field, $order)
    {
        if (is_array($field)) {
            $field = import(',', $field);
        }
        $model = $this->model->field($field)->where($where);
        if (!empty($order)) {
            $model = $model->order($order);
        }
        return $model;
    }

    /**
     * 生成产品的select专用数组
     * <AUTHOR>
     * @datetime 17:50 2018/12/26
     *
     * @access public
     * @param $where array 查询条件
     *
     * @return array
     **/
    public function getDataForSelect($where = [])
    {
        $data = $this->data($where, 'product_id,product_name');
        return array_column($data, null, 'product_id');
    }

    /**
     * 在账号管理、客户管理中存在产品的筛选条件
     * 该方法是为了便于筛选条件快速查询出select内部的数据
     * <AUTHOR>
     * @datetime 16:02 2018/12/28
     *
     * @access public
     *
     * @return array
     **/
    public function getDataForSelectInAccount()
    {
        return $this->getDataForSelect([
            'back_status' => 1
        ]);
    }

    /**
     * 增加产品
     * <AUTHOR>
     * @datetime 18:04 2018/12/26
     *
     * @access public
     * @param $admin string 当前登陆用户名称（算作产品的操作人）
     * @throws \Exception
     * @return void
     **/
    public function add($admin)
    {
        $data = $this->validateForAdd();
        $data['admin'] = $admin;
        $this->model->add($data);

        // 刷新鉴权的缓存
        $this->clearAuthCache();
    }

    /**
     * 编辑产品
     * <AUTHOR>
     * @datetime 18:05 2018/12/26
     *
     * @access public
     * @param $admin string 当前登陆用户名称（算作产品的操作人）
     * @throws \Exception
     * @return void
     **/
    public function edit($admin)
    {
        $data = $this->validateForEdit();
        $data['admin'] = $admin;
        $product_id = I('post.product_id', '', 'trim');
        $this->model->where(compact('product_id'))->save($data);

        // 清理鉴权缓存
        $this->clearAuthCache();
    }

    /**
     * 清理鉴权缓存
     * @throws \Exception
     */
    private function clearAuthCache()
    {
        $url = C('LIST_API_URL')['backend_api_clear_auth'];
        $i = 0;
        while (true) {
            $response = $this->get($url);
            if ($response['status'] == 0 || $i > 2) {
                break;
            }
            $i++;
        }

        if ($response['status'] != 0) {
            $msg = '调用清理鉴权缓存API连续三次失败';
            $exception = json_encode(compact('msg', 'response', 'url'), JSON_UNESCAPED_UNICODE);
            $this->wehcatException($exception);
            throw new \Exception('调用清理鉴权缓存API连续三次失败');
        }
    }

    /**
     * 校验并获取需要保存的数据
     *  应用于增加的情况下，下面有个雷同的，应用于编辑的情况下
     * <AUTHOR>
     * @datetime 18:06 2018/12/26
     *
     * @access protected
     *
     * @return array
     **/
    protected function validateForAdd()
    {
        //验证product_id
        $product_id = I('post.product_id', '', 'trim');
        $product_id = intval($product_id);
        if (empty($product_id)) {
            $this->buildErrorResponse('产品编号未填写');
        } elseif ($this->count([compact('product_id')]) != 0) {
            $this->buildErrorResponse("【{$product_id}】产品编号已经被占用");
        }
        //验证product_name
        $product_name = I('post.product_name', '', 'trim');
        if (empty($product_name)) {
            $this->buildErrorResponse('产品名称未填写');
        } elseif ($this->count(compact('product_name')) != 0) {
            $this->buildErrorResponse("【{$product_name}】产品名称已经被占用");
        }
        $product_key = I('post.product_key');
        $father_id = intval(I('post.father_id'));
        $back_status = intval(I('post.back_status'));
        $search_show = intval(I('post.search_show'));
        //验证各种JSON配置
        $data = I('post.data', '', 'trim');
        if (empty($data)) {
            $this->buildErrorResponse('产品配置参数为必填项，请为其填写JSON配置');
        }
        if (!$this->isJson($data)) {
            $this->buildErrorResponse('产品配置参数不是标准的JSON格式');
        }
        $stat_config = I('post.stat_config', '', 'trim');
        if (!empty($stat_config) && !$this->isJson($stat_config)) {
            $this->buildErrorResponse('客户统计配置不是标准的JSON格式');
        }
        $fee_config = I('post.fee_config', '', 'trim');
        if (!empty($fee_config) && !$this->isJson($fee_config)) {
            $this->buildErrorResponse('客户计费配置不是标准的JSON格式');
        }
        $channel_stat = I('post.channel_stat', '', 'trim');
        if (!empty($channel_stat) && !$this->isJson($channel_stat)) {
            $this->buildErrorResponse('渠道统计配置不是标准的JSON格式');
        }
        $channel_fee = I('post.channel_fee', '', 'trim');
        if (!empty($channel_fee) && !$this->isJson($channel_fee)) {
            $this->buildErrorResponse('渠道计费配置格式不是标准的JSON格式');
        }
        $mark = I('post.mark');
        $create_at = $update_at = time();
        return compact('product_id', 'product_name', 'product_key',
            'father_id', 'back_status', 'search_show', 'data', 'stat_config',
            'fee_config', 'channel_fee', 'channel_stat', 'mark',
            'create_at', 'update_at');
    }

    /**
     * 校验并获取需要保存的数据
     *  应用于编辑的情况下
     * <AUTHOR>
     * @datetime 18:06 2018/12/26
     *
     * @access protected
     *
     * @return array
     **/
    public function validateForEdit()
    {
        //验证product_id
        $product_id = I('post.product_id', '', 'trim');
        $product_id = intval($product_id);
        if (empty($product_id)) {
            $this->buildErrorResponse('产品编号已丢失');
        } elseif ($this->count([compact('product_id')]) == 0) {
            $this->buildErrorResponse("【{$product_id}】产品编号不存在");
        }
        //验证product_name
        $product_name = I('post.product_name', '', 'trim');
        if (empty($product_name)) {
            $this->buildErrorResponse('产品名称未填写');
        } elseif ($this->count([
                'product_name' => $product_name,
                'product_id' => ['neq', $product_id]
            ]) != 0) {
            $this->buildErrorResponse("【{$product_name}】产品名称已经被占用");
        }
        $product_key = I('post.product_key');
        $father_id = intval(I('post.father_id'));
        $back_status = intval(I('post.back_status'));
        $search_show = intval(I('post.search_show'));

        //验证各种JSON配置
        $data = I('post.data', '', 'trim');
        if (empty($data)) {
            $this->buildErrorResponse('产品配置参数为必填项，请为其填写JSON配置');
        }
        if (!$this->isJson($data)) {
            $this->buildErrorResponse('产品配置参数不是标准的JSON格式');
        }
        $stat_config = I('post.stat_config', '', 'trim');
        if (!empty($stat_config) && !$this->isJson($stat_config)) {
            $this->buildErrorResponse('客户统计配置不是标准的JSON格式');
        }
        $fee_config = I('post.fee_config', '', 'trim');
        if (!empty($fee_config) && !$this->isJson($fee_config)) {
            $this->buildErrorResponse('客户计费配置不是标准的JSON格式');
        }
        $channel_stat = I('post.channel_stat', '', 'trim');
        if (!empty($channel_stat) && !$this->isJson($channel_stat)) {
            $this->buildErrorResponse('渠道统计配置不是标准的JSON格式');
        }
        $channel_fee = I('post.channel_fee', '', 'trim');
        if (!empty($channel_fee) && !$this->isJson($channel_fee)) {
            $this->buildErrorResponse('渠道计费配置格式不是标准的JSON格式');
        }
        $mark = I('post.mark');
        $update_at = time();
        return compact('product_name', 'product_key', 'father_id', 'back_status','search_show',
            'data', 'stat_config', 'fee_config', 'channel_fee', 'channel_stat', 'mark', 'update_at');
    }

    /**
     * 校验某个数据是否为JSON数据格式
     * <AUTHOR>
     * @datetime 18:24 2018/12/26
     *
     * @access protected
     * @param $data mixed 数据
     *
     * @return boolean
     **/
    protected function isJson($data)
    {
        $res = @json_decode($data, true);
        if (is_null($res)) {
            return false;
        }
        return true;
    }

    /**
     * 创建一个错误响应
     * <AUTHOR>
     * @datetime 18:29 2018/12/26
     *
     * @access protected
     * @param $msg string 响应信息
     *
     * @return void
     **/
    protected function buildErrorResponse($msg)
    {
        throw new \Exception($msg);
    }

    /**
     * 为列表获取数据
     * <AUTHOR>
     * @datetime 10:21 2018/12/27
     *
     * @access public
     *
     * @return array
     **/
    public function getDataForList()
    {
        $childrenSql = $this->model->fetchSql()->alias('children')->where('product.product_id = children.father_id')->count();
        return $this->data($this->where(), 'product_id,product_name,product_key,create_at,update_at,admin,mark,(' . $childrenSql . ') as children_count', 'product_id asc');
    }

    /**
     * 获取列表页筛选条件
     * <AUTHOR>
     * @datetime 11:01 2018/12/27
     *
     * @access protected
     *
     * @return array
     **/
    protected function where()
    {
        $father_id = I('get.father_id');
        $father_id = intval($father_id);
        $where = compact('father_id');
        $product_name = I('get.product_name', '', 'trim');
        if (!empty($product_name)) {
            $where['product_name'] = ['like', "%{$product_name}%"];
        }
        $where['product_id'] = ['not in', [301, 302]];
        return $where;
    }

    /**
     * 为产品编辑页面获取产品数据
     * <AUTHOR>
     * @datetime 10:41 2018/12/27
     *
     * @access public
     *
     * @return array
     **/
    public function getDataForEdit()
    {
        $product_id = I('get.product_id');
        if (empty($product_id)) {
            throw new  \Exception('该产品不存在');
        }
        $childrenSql = $this->model->alias('children')->fetchSql()->where('product.product_id = children.father_id')->count();
        return $this->one(compact('product_id'), '*, (' . $childrenSql . ') children_count');
    }

}