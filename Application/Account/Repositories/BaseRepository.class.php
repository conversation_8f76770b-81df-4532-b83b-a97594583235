<?php

namespace Account\Repositories;

use Common\Controller\DataAuthController;

class BaseRepository
{
    /**
     * 格式化数字
     * @param  integer $number
     * @return string
     */
    public function formatNum($number)
    {
        if (!is_numeric($number)) {
            return $number;
        }
        return number_format($number, 0, '.', ',');
    }

    /**
     * 百分比格式化
     * @param $item
     * @return string
     */
    public function formatPercentage($item)
    {
        if ($item == 'NA') {
            return $item;
        }

        return round($item * 100, 2) . '%';
    }

    /**
     * 获取从一个数组中获取一个特定索引的数值， 支持默认值
     * @param $list_source
     * @param $key_search
     * @param string $default_value
     * @throws \Exception
     * @return mixed
     */
    public function getValueWithDefault($list_source, $key_search, $default_value = '')
    {
        if (!is_array($list_source) || !is_string($key_search)) {
            throw new \Exception(__METHOD__ . ' 函数需要第一个参数必须是array, 第二参数必选是string 第三个参数可选参数');
        }

        return isset($list_source[$key_search]) ? $list_source[$key_search] : $default_value;
    }

    /**
     * 日志
     * @param $response
     * @return string
     */
    protected function log($response)
    {
        // 組裝要寫日志
        $info_log = json_encode($response, JSON_UNESCAPED_UNICODE);

        // 檢查日志文件是否存在
        $dir = RUNTIME_PATH . date('Ymd') . '/';
        if (!file_exists($dir) || !is_dir($dir)) {
            @mkdir($dir, 0755, true);
        }

        // 日志文件名
        $log_name = $this->genLogName();
        $destination = $dir . $log_name . '.log';

        // 寫入
        file_put_contents(
            $destination,
            '[' . date('Y-m-d H:i:s') . ']  ' . $info_log . PHP_EOL,
            FILE_APPEND
        );
    }

    /**
     * 生成日志名字
     * @return mixed
     */
    protected function genLogName()
    {
        // 回溯调用者的文件的名字
        $file_name_invoking = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 2)[1]['file'];
        $file_name_invoking = trim($file_name_invoking, '.class.php');
        $list_name = explode('/', $file_name_invoking);

        return end($list_name);
    }

    /**
     * 发送post请求
     * @param $url
     * @param $post_data
     * @return array|mixed
     */
    protected function curlRequestForPost($url, $post_data)
    {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $list_stat = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'msg' => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($list_stat)) {
            return json_decode($list_stat, true);
        }

        return $list_stat;
    }

    /**
     * 发送get请求
     * @param string $url 请求的Url
     * @param array $url_data 请求参数
     * @return array|mixed
     */
    protected function curlRequestForGet($url, $url_data = [])
    {
        // 追加参数
        if ($url_data && is_array($url_data)) {
            $url_data = http_build_query($url_data);
            $url .= '?' . $url_data;
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        $list_stat = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return [
                'success' => false,
                'status' => 1748, // 设置出错的标识
                'msg' => curl_error($curl)
            ];
        }
        curl_close($curl);
        if (is_string($list_stat)) {
            return json_decode($list_stat, true);
        }

        return $list_stat;
    }

    /**
     * curl请求
     * @param  string $method 请求方式
     * @param  string $url    请求地址
     * @param  array  $vars   传入参数
     * @return array 返回结果
     */
    public function getCurl($method, $url, $vars = array(), $build = 'form')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        if ($vars && 'form' == strtolower($build)) {
            $vars = http_build_query($vars, '', '&');
        }
        curl_setopt($curl, CURLOPT_HTTPHEADER, ['Expect:']);
        if ($vars && 'form' != strtolower($build)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, ['Content-Type' => 'application/json', 'Content-Length' => strlen($vars)]);
        }
        if ($vars) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);
        }
        curl_setopt($curl, CURLOPT_TIMEOUT, 60);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

        switch (strtoupper($method)) {
            case 'HEAD':
                curl_setopt($curl, CURLOPT_NOBODY, true);
                break;
            case 'GET':
                curl_setopt($curl, CURLOPT_HTTPGET, true);
                break;
            case 'POST':
                curl_setopt($curl, CURLOPT_POST, true);
                break;
            default:
                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
                break;
        }

        $response = curl_exec($curl);
        //释放curl
        curl_close($curl);
        return $response;
    }

    /**
     * 添加url参数（get）
     * @param $url 原始url
     * @param $params_arr 添加的参数数组
     * @return string 添加后的url
     */
    public static function  addUrlTail($url, $params_arr) {
        if (is_array($params_arr) && count($params_arr) > 0) {
            $url_tail = http_build_query($params_arr);
            if (strpos($url, '?') !== false) {
                $url .= '&' . $url_tail;
            } else {
                $url .= '?' . $url_tail;
            }
        }
        return $url;
    }
    /**
     * 获取后台用户的数据
     *
     * @access public
     * @param $default string 默认选择的用户
     * @param $value string option的值
     * @param $show string 限制的字段
     *
     * @return string
     **/
    public function getUserOption($default = '', $value = 'username', $show = 'realname')
    {
        $data = (new \Common\Model\SystemUserModel())->field([$value, $show])->where(DataAuthController::instance()->getUserWhere())->select();
        $display = '';
        foreach ($data as $item) {
            $selected = ($default==$item[$value])?'selected':'';
            $display .= "<option value='{$item[$value]}' {$selected}>{$item[$show]}</option>";
        }
        return $display;
    }
}
