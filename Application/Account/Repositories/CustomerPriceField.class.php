<?php

namespace Account\Repositories;

/**
 * Class CustomerPriceField 客户单价字段的处理对象
 *   计费配置单价数据通过POST传递到后端，后端需要对不同格式的数据进行整合处理
 *
 * @package Account\Repositories
 */
class CustomerPriceField
{
	/**
	 * 根据不同的计费模式获取单价数据
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/9/10 17:49
	 *
	 * @return mixed
	 */
	public function getPrice()
	{
		$methodName = 'getPrice';
		//是否为多级产品: A-不是 B-是
		$methodName .= I('post.is_multiple', 0) ? 'B' : 'A';
		
		//计费方式 A-停止计费 B-包年 C-固定价格 D-累进阶梯 E-到达阶梯
		switch (I('post.methods', 0)) {
			case 0:
				$methodName .= 'A';
				break;
			case 1:
				$methodName .= 'B';
				break;
			case 2:
				$methodName .= 'C';
				break;
			case 3:
				$methodName .= 'D';
				break;
			case 4:
				$methodName .= 'E';
				break;
			
		}
		
		//计费模式 A-无 B-独立子产品计费 C-打包计费 D-汇总子产品计费
		if (1 == I('post.is_multiple', 0)) {
			switch (I('post.mode', 0)) {
				case 1:
					$methodName .= 'B';
					break;
				case 2:
					$methodName .= 'C';
					break;
				case 3:
					$methodName .= 'D';
					break;
			}
		} else {
			$methodName .= 'A';
		}
		
		//是否区分运营商 A - 不区分运营商 B-区分运营商
		$methodName .= I('post.diff_operator', 0) ? 'B' : 'A';
		
		return $this->$methodName();
	}
	
	/**
	 * 单级产品-停止计费-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAAAA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-停止计费-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAAAB()
	{
		createJsonResponse(10019);
		
		return false;
	}
	
	/**
	 * 单级产品-停止计费-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAABA()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-停止计费-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAABB()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-停止计费-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAACA()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-停止计费-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAACB()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-停止计费-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAADA()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-停止计费-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAADB()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABAA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-包年-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABAB()
	{
		createJsonResponse(10020);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABBA()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABBB()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABCA()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABCB()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABDA()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-包年-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceABDB()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-固定价格-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACAA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-固定价格-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACAB()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-固定价格-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACBA()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-固定价格-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACBB()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-固定价格-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACCA()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-固定价格-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACCB()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-固定价格-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACDA()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-固定价格-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceACDB()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-累进阶梯-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADAA()
	{
		$price  = I('post.price');
		$ladder = I('post.ladder');
		
		$result = [];
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			$item = [];
			foreach ($ladder as $key => $number) {
				$item[$number] = $price[$product_id][$key];
			}
			ksort($item, SORT_NUMERIC);
			
			$result[$product_id] = $item;
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-累进阶梯-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADAB()
	{
		$ladder = I('post.ladder');
		$price  = I('post.price');
		
		$result = [];
		
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			array_walk($ladder, function ($ladder, $operator) use (&$result, $price, $product_id) {
				foreach ($ladder as $key => $number) {
					$result[$product_id][$operator][$number] = $price[$product_id][$operator][$key];
				}
				
				//排一下阶梯的顺序
				ksort($result[$product_id][$operator], SORT_NUMERIC);
			});
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-累进阶梯-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADBA()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-累进阶梯-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADBB()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-累进阶梯-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADCA()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-累进阶梯-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADCB()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-累进阶梯-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADDA()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-累进阶梯-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceADDB()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-到达阶梯-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAEAA()
	{
		$price  = I('post.price');
		$ladder = I('post.ladder');
		
		$result = [];
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			$item = [];
			foreach ($ladder as $key => $number) {
				$item[$number] = $price[$product_id][$key];
			}
			ksort($item, SORT_NUMERIC);
			
			$result[$product_id] = $item;
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-到达阶梯-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAEAB()
	{
		$ladder = I('post.ladder');
		$price  = I('post.price');
		
		$result = [];
		
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			array_walk($ladder, function ($ladder, $operator) use (&$result, $price, $product_id) {
				foreach ($ladder as $key => $number) {
					$result[$product_id][$operator][$number] = $price[$product_id][$operator][$key];
				}
				
				//排一下阶梯的顺序
				ksort($result[$product_id][$operator], SORT_NUMERIC);
			});
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 单级产品-到达阶梯-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAEBA()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-到达阶梯-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAEBB()
	{
		createJsonResponse(10011);
		
		return false;
	}
	
	/**
	 * 单级产品-到达阶梯-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAECA()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-到达阶梯-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAECB()
	{
		createJsonResponse(10012);
		
		return false;
	}
	
	/**
	 * 单级产品-到达阶梯-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAEDA()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 单级产品-到达阶梯-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceAEDB()
	{
		createJsonResponse(10013);
		
		return false;
	}
	
	/**
	 * 多级产品-停止计费-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBAAA()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-停止计费-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBAAB()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-停止计费-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBABA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-停止计费-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBABB()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-停止计费-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBACA()
	{
		createJsonResponse(10015);
		
		return false;
	}
	
	/**
	 * 多级产品-停止计费-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBACB()
	{
		createJsonResponse(10015);
		
		return false;
	}
	
	/**
	 * 多级产品-停止计费-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBADA()
	{
		createJsonResponse(10017);
		
		return false;
	}
	
	/**
	 * 多级产品-停止计费-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBADB()
	{
		createJsonResponse(10017);
		
		return false;
	}
	
	/**
	 * 多级产品-包年-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBAA()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-包年-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBAB()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-包年-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBBA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-包年-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBBB()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-包年-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBCA()
	{
		createJsonResponse(10016);
		
		return false;
	}
	
	/**
	 * 多级产品-包年-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBCB()
	{
		createJsonResponse(10016);
		
		return false;
	}
	
	/**
	 * 多级产品-包年-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBDA()
	{
		createJsonResponse(10018);
		
		return false;
	}
	
	/**
	 * 多级产品-包年-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBBDB()
	{
		createJsonResponse(10018);
		
		return false;
	}
	
	/**
	 * 多级产品-固定价格-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCAA()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-固定价格-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCAB()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-固定价格-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCBA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-固定价格-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCBB()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-固定价格-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCCA()
	{
		$price = I('post.price');
		
		$package_number = I('post.package_number');
		
		$result = [];
		
		array_walk($package_number, function ($number, $key) use (&$result, $price) {
			$result[$number] = $price['package_' . $key];
		});
		
		ksort($result, SORT_NUMERIC);
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-固定价格-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCCB()
	{
		$price          = I('post.price');
		$package_number = I('post.package_number');
		
		$result = [];
		
		array_walk($package_number, function ($number, $key) use (&$result, $price) {
			$result[$number] = $price['package_' . $key];
		});
		
		ksort($result, SORT_NUMERIC);
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-固定价格-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCDA()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-固定价格-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBCDB()
	{
		return json_encode(I('post.price'), JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-累进阶梯-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDAA()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-累进阶梯-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDAB()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-累进阶梯-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDBA()
	{
		$price  = I('post.price');
		$ladder = I('post.ladder');
		
		$result = [];
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			$item = [];
			foreach ($ladder as $key => $number) {
				$item[$number] = $price[$product_id][$key];
			}
			ksort($item, SORT_NUMERIC);
			
			$result[$product_id] = $item;
		});
		
		
		//对阶梯排序
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-累进阶梯-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDBB()
	{
		$ladder = I('post.ladder');
		$price  = I('post.price');
		
		$result = [];
		
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			array_walk($ladder, function ($ladder, $operator) use (&$result, $price, $product_id) {
				foreach ($ladder as $key => $number) {
					$result[$product_id][$operator][$number] = $price[$product_id][$operator][$key];
				}
				
				//排一下阶梯的顺序
				ksort($result[$product_id][$operator], SORT_NUMERIC);
			});
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-累进阶梯-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDCA()
	{
		$price          = I('post.price');
		$ladder         = I('post.ladder');
		$package_number = I('post.package_number');
		
		$result = [];
		array_walk($package_number, function ($packageNumber, $packageKey) use (&$result, $ladder, $price) {
			$packageLadder = $ladder['package_' . $packageKey];
			$packagePrice  = $price['package_' . $packageKey];
			$resultItem    = [];
			array_walk($packageLadder, function ($ladderNumber, $ladderKey) use (&$resultItem, $packagePrice) {
				$resultItem[$ladderNumber] = $packagePrice[$ladderKey];
			});
			
			ksort($resultItem, SORT_NUMERIC);
			
			$result[$packageNumber] = $resultItem;
		});
		
		ksort($result, SORT_NUMERIC);
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-累进阶梯-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDCB()
	{
		$ladder         = I('post.ladder');
		$price          = I('post.price');
		$package_number = I('post.package_number');
		
		$result = [];
		array_walk($package_number, function ($packageNumber, $packageKey) use (&$result, $price, $ladder) {
			$packagePrice  = $price['package_' . $packageKey];
			$packageLadder = $ladder['package_' . $packageKey];
			
			$operatorItem = [];
			array_walk($packagePrice, function ($operatorPrice, $operator) use (&$operatorItem, $packageLadder) {
				$operatorLadder = $packageLadder[$operator];
				$item           = [];
				array_walk($operatorLadder, function ($ladderNumber, $ladderKey) use (&$item, $operatorPrice) {
					$item[$ladderNumber] = $operatorPrice[$ladderKey];
				});
				ksort($item, SORT_NUMERIC);
				
				$operatorItem[$operator] = $item;
			});
			
			$result[$packageNumber] = $operatorItem;
			ksort($result, SORT_NUMERIC);
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-累进阶梯-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDDA()
	{
		$price  = I('post.price');
		$ladder = I('post.ladder');
		
		$result = [];
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			$item = [];
			foreach ($ladder as $key => $number) {
				$item[$number] = $price[$product_id][$key];
			}
			ksort($item, SORT_NUMERIC);
			
			$result[$product_id] = $item;
		});
		
		
		//对阶梯排序
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-累进阶梯-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBDDB()
	{
		$ladder = I('post.ladder');
		$price  = I('post.price');
		
		$result = [];
		
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			array_walk($ladder, function ($ladder, $operator) use (&$result, $price, $product_id) {
				foreach ($ladder as $key => $number) {
					$result[$product_id][$operator][$number] = $price[$product_id][$operator][$key];
				}
				
				//排一下阶梯的顺序
				ksort($result[$product_id][$operator], SORT_NUMERIC);
			});
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-到达阶梯-无计费模式-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBEAA()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-到达阶梯-无计费模式-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBEAB()
	{
		createJsonResponse(10014);
		
		return false;
	}
	
	/**
	 * 多级产品-到达阶梯-独立子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBEBA()
	{
		$price  = I('post.price');
		$ladder = I('post.ladder');
		
		$result = [];
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			$item = [];
			foreach ($ladder as $key => $number) {
				$item[$number] = $price[$product_id][$key];
			}
			ksort($item, SORT_NUMERIC);
			
			$result[$product_id] = $item;
		});
		
		//对阶梯排序
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-到达阶梯-独立子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBEBB()
	{
		$ladder = I('post.ladder');
		$price  = I('post.price');
		
		$result = [];
		
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			array_walk($ladder, function ($ladder, $operator) use (&$result, $price, $product_id) {
				foreach ($ladder as $key => $number) {
					$result[$product_id][$operator][$number] = $price[$product_id][$operator][$key];
				}
				
				//排一下阶梯的顺序
				ksort($result[$product_id][$operator], SORT_NUMERIC);
			});
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-到达阶梯-打包计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBECA()
	{
		$price          = I('post.price');
		$ladder         = I('post.ladder');
		$package_number = I('post.package_number');
		
		$result = [];
		array_walk($package_number, function ($packageNumber, $packageKey) use (&$result, $ladder, $price) {
			$packageLadder = $ladder['package_' . $packageKey];
			$packagePrice  = $price['package_' . $packageKey];
			$resultItem    = [];
			array_walk($packageLadder, function ($ladderNumber, $ladderKey) use (&$resultItem, $packagePrice) {
				$resultItem[$ladderNumber] = $packagePrice[$ladderKey];
			});
			
			ksort($resultItem, SORT_NUMERIC);
			
			$result[$packageNumber] = $resultItem;
		});
		
		ksort($result, SORT_NUMERIC);
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-到达阶梯-打包计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBECB()
	{
		$ladder         = I('post.ladder');
		$price          = I('post.price');
		$package_number = I('post.package_number');
		
		$result = [];
		array_walk($package_number, function ($packageNumber, $packageKey) use (&$result, $price, $ladder) {
			$packagePrice  = $price['package_' . $packageKey];
			$packageLadder = $ladder['package_' . $packageKey];
			
			$operatorItem = [];
			array_walk($packagePrice, function ($operatorPrice, $operator) use (&$operatorItem, $packageLadder) {
				$operatorLadder = $packageLadder[$operator];
				$item           = [];
				array_walk($operatorLadder, function ($ladderNumber, $ladderKey) use (&$item, $operatorPrice) {
					$item[$ladderNumber] = $operatorPrice[$ladderKey];
				});
				ksort($item, SORT_NUMERIC);
				
				$operatorItem[$operator] = $item;
			});
			
			$result[$packageNumber] = $operatorItem;
			ksort($result, SORT_NUMERIC);
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-到达阶梯-汇总子产品计费-不区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBEDA()
	{
		$price  = I('post.price');
		$ladder = I('post.ladder');
		
		$result = [];
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			$item = [];
			foreach ($ladder as $key => $number) {
				$item[$number] = $price[$product_id][$key];
			}
			ksort($item, SORT_NUMERIC);
			
			$result[$product_id] = $item;
		});
		
		
		//对阶梯排序
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	/**
	 * 多级产品-到达阶梯-汇总子产品计费-区分运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/13 13:48
	 *
	 * @return mixed
	 */
	protected function getPriceBEDB()
	{
		$ladder = I('post.ladder');
		$price  = I('post.price');
		
		$result = [];
		
		array_walk($ladder, function ($ladder, $product_id) use (&$result, $price) {
			array_walk($ladder, function ($ladder, $operator) use (&$result, $price, $product_id) {
				foreach ($ladder as $key => $number) {
					$result[$product_id][$operator][$number] = $price[$product_id][$operator][$key];
				}
				
				//排一下阶梯的顺序
				ksort($result[$product_id][$operator], SORT_NUMERIC);
			});
		});
		
		return json_encode($result, JSON_UNESCAPED_UNICODE);
	}
	
	
}