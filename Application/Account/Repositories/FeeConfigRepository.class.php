<?php

namespace Account\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Common\Approval;
use Common\Common\HandlerLog;
use Common\Controller\DataAuthController;
use Common\Model\FeeConfigModel;
use Think\Model;

class FeeConfigRepository
{
	protected $customer_model;
	protected $feeConfig_model;
	protected $account_product_model;
	protected $product_model;
	protected $account_model;
	protected $children_product_210  = [];
	protected $children_product_1000 = [];
	protected $children_product_615  = [];
	//不可增加计费配置的产品ID
	protected $filter_product_id = [
		301,
		302,
		651,
		652,
		681,
		682,
		683,
		684,
	];
	
	/**
	 * 获取210的全量子产品，
	 *
	 * @access protected
	 *
	 * @param $with_product_name boolean 是否携带产品名称
	 *
	 * @return array
	 **/
	protected function get210AllChildrenProductId($with_product_name = false)
	{
		if (empty($this->children_product_210)) {
			$father_id                  = 210;
			$product_id                 = ['not in', $this->filter_product_id];
			$data                       = $this->product_model->field('product_id, product_name')
															  ->where(compact('father_id', 'product_id'))
															  ->select();
			$this->children_product_210 = $data;
		}
		if ($with_product_name) {
			return $this->children_product_210;
		} else {
			return array_column($this->children_product_210, 'product_id');
		}
	}
	
	/**
	 * 获取1000的全量子产品，
	 *
	 * @access protected
	 *
	 * @param $with_product_name boolean 是否携带产品名称
	 *
	 * @return array
	 **/
	protected function get1000AllChildrenProductId($with_product_name = false)
	{
		if (empty($this->children_product_1000)) {
			$father_id                   = 1000;
			$product_id                  = ['not in', $this->filter_product_id];
			$data                        = $this->product_model->field('product_id, product_name')
															   ->where(compact('father_id', 'product_id'))
															   ->select();
			$this->children_product_1000 = $data;
		}
		if ($with_product_name) {
			return $this->children_product_1000;
		} else {
			return array_column($this->children_product_1000, 'product_id');
		}
	}
	
	/**
	 * 获取615的全量子产品，
	 *
	 * @access protected
	 *
	 * @param $with_product_name boolean 是否携带产品名称
	 *
	 * @return array
	 **/
	protected function get615AllChildrenProductId($with_product_name = false)
	{
		if (empty($this->children_product_615)) {
			$father_id                  = 615;
			$product_id                 = ['not in', $this->filter_product_id];
			$data                       = $this->product_model->field('product_id, product_name')
															  ->where(compact('father_id', 'product_id'))
															  ->select();
			$this->children_product_615 = $data;
		}
		if ($with_product_name) {
			return $this->children_product_615;
		} else {
			return array_column($this->children_product_615, 'product_id');
		}
	}
	
	public function __construct()
	{
		$this->customer_model        = new CustomerModel();
		$this->feeConfig_model       = new FeeConfigModel();
		$this->account_model         = new AccountModel();
		$this->account_product_model = new AccountProductModel();
		$this->product_model         = new ProductModel();
	}
	
	/**
	 * 为增加计费配置页面准备数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	public function add_html()
	{
		$customer_id = I('get.customer_id', '', 'trim');
		if (!$customer_id) {
			throw new \Exception('缺少客户ID');
		}
		$is_delete     = 0;
		$customer_data = $this->customer_model->field('customer_id,name as customer_name, status')
											  ->where(DataAuthController::instance()
																		->getCustomerWhere())
											  ->where(compact('customer_id', 'is_delete'))
											  ->find();
		if (empty($customer_data)) {
			throw new \Exception('该客户不存在');
		}
		if ($customer_data['status'] == 0) {
			throw new \Exception('该客户为禁用状态，不可进行计费配置设置');
		}
		$account_list = $this->getAccountListByCustomerId($customer_id);
		if (empty($account_list)) {
			throw new \Exception('该客户未开通账号！');
		}
		$store_product_list = $this->getStoreProductListForAccountList($account_list);
		if (empty($store_product_list)) {
			throw new \Exception('该客户暂无开通的产品');
		}
		$result = $this->mergeAccountAndProduct($account_list, $store_product_list);
		$result = json_encode($result, JSON_UNESCAPED_UNICODE);
		
		return compact('result', 'customer_data');
	}
	
	/**
	 * 为计费配置的JS生成JSON数据（增加）
	 *
	 * @access protected
	 *
	 * @param $account_list       array 账号数据
	 * @param $store_product_list array 账号开通的产品数据
	 *
	 * @return array
	 **/
	protected function mergeAccountAndProduct($account_list, $store_product_list)
	{
		//单个产品配置项
		$itemProductConfig = [
			'product_id'        => '',
			'product_name'      => '',
			'price_rule'        => false,    //价格规则（区分运营商）
			'diff_inter'        => true,     //是否区分接口
			'unique_account'    => true,     //唯一选择账号限制
			'price_input_model' => 'common',    //价格表单域模板
			'account_list'      => [],       //账号列表，
			'basis_list'        => []        //计费依据
		];
		
		//生成一个可以校验某个账号是否开通了产品的数组
		$validate_store_product_array = array_map(function ($item) {
			return $item['product_id'] . '_' . $item['account_id'];
		}, $store_product_list);
		//获取所有开通的产品数据
		$store_product_id = array_unique(array_column($store_product_list, 'product_id'));
		$product_data     = $this->getProductDataByProductId($store_product_id);
		//开始组合数据（注意先将210、615、1000产品排除在外，进行特殊处理）
		$result = [];
		array_walk($product_data, function ($item, $product_id) use (&$result, $account_list, $itemProductConfig, $validate_store_product_array) {
			//后面会单独处理210产品的配置内容
			if ($product_id == 210) {
				return true;
			}
			//后面单独处理615的配置内容
			if ($product_id == 615) {
				return true;
			}
			//后面单独处理1000的配置内容
			if ($product_id == 1000) {
				return true;
			}
			//不展示的产品过滤掉
			if ($item['back_status'] == 0) {
				return true;
			}
			//是否存在区分运营商计费（目前仅邦信分快捷版与邦秒验两款产品会进行运营商的区分）
			$price_rule = false;
			if ($item['father_id'] == 200 || $item['father_id'] == 1000) {
				$price_rule = true;
			}
			//设置产品的计费配置
			$result[$product_id]                   = $itemProductConfig;
			$result[$product_id]['product_id']     = $product_id;
			$result[$product_id]['product_name']   = $item['product_name'];
			$result[$product_id]['price_rule']     = $price_rule;
			$result[$product_id]['diff_inter']     = false;
			$result[$product_id]['unique_account'] = false;
			$result[$product_id]['account_list']   = [];
			$result[$product_id]['basis_list']     = json_decode($item['fee_config'], true);
			
			//将账号数据整合到数组中
			array_walk($account_list, function ($item, $account_id) use (&$result, $product_id, $validate_store_product_array) {
				$validate_value = $product_id . '_' . $account_id;
				if (in_array($validate_value, $validate_store_product_array)) {
					$result[$product_id]['account_list'][$item['account_id']] = $item;
				}
			});
			
			return true;
		});
		//如果开通了210产品需要对其进行特殊处理
		if (in_array(210, $store_product_id)) {
			$result[210]                      = $itemProductConfig;
			$result[210]['product_id']        = 210;
			$result[210]['product_name']      = $product_data[210]['product_name'];
			$result[210]['price_rule']        = true;
			$result[210]['diff_inter']        = true;
			$result[210]['unique_account']    = true;
			$result[210]['price_input_model'] = 'children_field';
			$result[210]['account_list']      = [];
			$result[210]['basis_list']        = json_decode($product_data[210]['fee_config'], true);
			//获取所有催收分析快捷版的子产品
			$children_product_data = $this->get210AllChildrenProductId(true);
			$children_product_data = array_column($children_product_data, null, 'product_id');
			array_walk($account_list, function ($item, $account_id) use (&$result, $children_product_data, $validate_store_product_array) {
				$validate_value = '210_' . $account_id;
				if (in_array($validate_value, $validate_store_product_array)) {
					$account_list = [
						'account_id'       => $account_id,
						'account_name'     => $item['account_name'],
						'children_product' => [],
					];
					array_walk($children_product_data, function ($item) use (&$account_list, $validate_store_product_array, $account_id) {
						$product_id     = $item['product_id'];
						$validate_value = $product_id . '_' . $account_id;
						if (in_array($validate_value, $validate_store_product_array)) {
							$account_list['children_product'][$product_id] = $item['product_name'];
						}
					});
					$result[210]['account_list'][$account_list['account_id']] = $account_list;
				}
			});
		}
		
		//如果开通了210产品需要对其进行特殊处理
		if (in_array(1000, $store_product_id)) {
			$result[1000]                      = $itemProductConfig;
			$result[1000]['product_id']        = 1000;
			$result[1000]['product_name']      = $product_data[1000]['product_name'];
			$result[1000]['price_rule']        = true;
			$result[1000]['diff_inter']        = true;
			$result[1000]['unique_account']    = true;
			$result[1000]['price_input_model'] = 'children_field';
			$result[1000]['account_list']      = [];
			$result[1000]['basis_list']        = json_decode($product_data[1000]['fee_config'], true);
			//获取所有邦信分-通信评分的子产品
			$children_product_data = $this->get1000AllChildrenProductId(true);
			$children_product_data = array_column($children_product_data, null, 'product_id');
			array_walk($account_list, function ($item, $account_id) use (&$result, $children_product_data, $validate_store_product_array) {
				$validate_value = '1000_' . $account_id;
				if (in_array($validate_value, $validate_store_product_array)) {
					$account_list = [
						'account_id'       => $account_id,
						'account_name'     => $item['account_name'],
						'children_product' => [],
					];
					array_walk($children_product_data, function ($item) use (&$account_list, $validate_store_product_array, $account_id) {
						$product_id     = $item['product_id'];
						$validate_value = $product_id . '_' . $account_id;
						if (in_array($validate_value, $validate_store_product_array)) {
							$account_list['children_product'][$product_id] = $item['product_name'];
						}
					});
					$result[1000]['account_list'][$account_list['account_id']] = $account_list;
				}
			});
		}
		
		//如果开通了615产品需要对其进行特殊处理
		if (in_array(615, $store_product_id)) {
			
			$result[615]                      = $itemProductConfig;
			$result[615]['product_id']        = 615;
			$result[615]['product_name']      = $product_data[615]['product_name'];
			$result[615]['price_rule']        = false;
			$result[615]['diff_inter']        = true;
			$result[615]['unique_account']    = true;
			$result[615]['price_input_model'] = 'children_field';
			$result[615]['account_list']      = [];
			$result[615]['basis_list']        = json_decode($product_data[615]['fee_config'], true);
			
			//获取所有催收分析快捷版的子产品
			$children_product_data = $this->get615AllChildrenProductId(true);
			$children_product_data = array_column($children_product_data, null, 'product_id');
			array_walk($account_list, function ($item, $account_id) use (&$result, $children_product_data, $validate_store_product_array) {
				$validate_value = '615_' . $account_id;
				if (in_array($validate_value, $validate_store_product_array)) {
					$account_list = [
						'account_id'       => $account_id,
						'account_name'     => $item['account_name'],
						'children_product' => [],
					];
					array_walk($children_product_data, function ($item) use (&$account_list, $validate_store_product_array, $account_id) {
						$product_id     = $item['product_id'];
						$validate_value = $product_id . '_' . $account_id;
						if (in_array($validate_value, $validate_store_product_array)) {
							$account_list['children_product'][$product_id] = $item['product_name'];
						}
					});
					$result[615]['account_list'][$account_list['account_id']] = $account_list;
				}
			});
		}
		
		return $result;
	}
	
	/**
	 * 根据产品ID获取产品数据
	 *
	 * @access protected
	 *
	 * @param $product_id array 产品ID
	 *
	 * @return array
	 **/
	protected function getProductDataByProductId($product_id)
	{
		$product_id   = ['in', $product_id];
		$product_data = $this->product_model->field('product_id,product_name,back_status,father_id,fee_config')
											->where(compact('product_id'))
											->select();
		
		return array_column($product_data, null, 'product_id');
	}
	
	/**
	 * 通过账号ID获取开通的产品数据
	 *
	 * @access protected
	 *
	 * @param $account_list array 账号数据
	 *
	 * @return array
	 **/
	protected function getStoreProductListForAccountList($account_list)
	{
		$account_id         = ['in', array_column($account_list, 'account_id')];
		$product_id         = ['not in', $this->filter_product_id];
		$store_product_data = $this->account_product_model->field('product_id,account_id')
														  ->where(compact('account_id', 'product_id'))
														  ->select();
		
		return $store_product_data;
	}
	
	/**
	 * 通过客户ID查询所有未被删除的账号列表
	 *
	 * @access protected
	 *
	 * @param $customer_id string 客户ID
	 *
	 * @return array
	 **/
	protected function getAccountListByCustomerId($customer_id)
	{
		$is_delete = 0;
		$status    = 1;
		$data      = $this->account_model->field('account_id,account_name')
										 ->where(compact('customer_id', 'is_delete', 'status'))
										 ->select();
		
		return array_column($data, null, 'account_id');
	}
	
	/**
	 * 增加计费配置
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function add()
	{
		//获取POST数据
		$data = $this->getAddData();
		//增加计费配置
		$res = $this->feeConfig_model->addAll($data);
		if (!$res) {
			throw new \Exception('增加失败');
		} else {
			//监听210产品的增加计费配置
			$this->listenDiffInter();
		}
	}
	
	/**
	 * 监听是否区分接口
	 *  如果区分接口，则需要判断历史计费配置是否存在非区分接口的配置
	 *      1.如果计费开始日期-1日存在不同的区分接口的配置，则需要直接删除昨日的区分接口的配置
	 *      2.如果计费开始日期-1日之前存在不同的区分接口的配置，则需要增加昨日的归零的配置
	 *
	 *  目前适用于210|615产品
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function listenDiffInter()
	{
		$product_id = I('post.product_id');
		if (in_array($product_id, [210, 615])) {
			//计费开始时间（新增）
			$start_date = I('post.start_date');
			//是否区分接口（新增）
			$diff_inter = I('post.diff_inter');
			//账号
			$account_id  = I('post.account_id');
			$account_id  = $account_id[0];
			$customer_id = I('post.customer_id');
			
			$children_product_id = [];
			if ($product_id == 210) {
				$children_product_id = $this->get210AllChildrenProductId();
			} else if ($product_id == 615) {
				$children_product_id = $this->get615AllChildrenProductId();
			}
			
			//获取计费开始时间-1日的计费配置
			$yesterday_date = date('Y-m-d', strtotime($start_date) - 1);
			$where          = [
				'diff_inter' => abs($diff_inter - 1),       //查询不同与当前的区分接口字段
				'start_date' => $yesterday_date,
				'product_id' => ['in', array_merge($children_product_id, [$product_id])],
				'account_id' => $account_id,
			];
			//            $yesterdayFeeConfig = $this->feeConfig_model->where($where)
			//                ->select();
			//            if (!empty($yesterdayFeeConfig)) {
			//                $id = ['in', array_column($yesterdayFeeConfig, 'id')];
			//                //删除昨日的不同的计费配置
			//                $this->feeConfig_model->where(compact('id'))
			//                    ->save([
			//                        'is_delete' => 1
			//                    ]);
			//            }
			
			
			//处理历史的计费配置
			$where['start_date'] = ['elt', $where['start_date']];
			$historyFeeConfig    = $this->feeConfig_model->where($where)
														 ->select();
			if (!empty($historyFeeConfig)) {
				$reset_data = array_map(function ($item) use ($customer_id, $account_id, $yesterday_date, $where) {
					$product_id      = $item['product_id'];
					$fee_basis       = 1;
					$fee_method      = 2;
					$fee_time_rule   = 0;
					$fee_amount_rule = 1;
					$fee_step_rule   = 0;
					$fee_price_rule  = 0;
					$fee_price       = 0;
					$start_date      = $yesterday_date;
					$remarks         = '系统归零设置';
					$create_time     = time();
					$is_delete       = 0;
					$admin           = $this->getAdmin();
					$diff_inter      = $where['diff_inter'];
					$is_reset        = 1;
					
					return compact('product_id', 'customer_id', 'account_id', 'fee_basis', 'fee_method', 'fee_time_rule', 'fee_amount_rule', 'fee_step_rule', 'fee_price', 'fee_price_rule', 'start_date', 'remarks', 'create_time', 'is_reset', 'is_delete', 'admin', 'diff_inter');
				}, $historyFeeConfig);
				$this->feeConfig_model->addAll($reset_data);
			}
		}
	}
	
	
	/**
	 * 获取增加配置的POST数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getAddData()
	{
		$data = [];
		//获取POST数据
		$data['customer_id'] = $this->simpleGetPostParam('customer_id');
		$data['product_id']  = $this->simpleGetPostParam('product_id');
		$data['account_id']  = $this->simpleGetPostParam('account_id');
		$data['basis']       = $this->simpleGetPostParam('basis');
		$data['method']      = $this->simpleGetPostParam('method');
		$data['rule']        = $this->simpleGetPostParam('rule');
		//区分接口
		$data['diff_inter'] = I('post.diff_inter', 0, 'trim');
		
		//临时不支持不区分接口 10:14 2019/9/26 0026
		//        if ($data['diff_inter'] == 0 && $data['product_id'] == 210) {
		//            throw new \Exception('暂不支持不区分接口计费');
		//        }
		
		//阶梯周期
		$data['period'] = I('post.period', 0, 'trim');
		//是否区分运营商
		$data['price_rule'] = I('post.price_rule', 0, 'trim');
		$data['price']      = $this->simpleGetPostParam('price');
		$data['start_date'] = $this->simpleGetPostParam('start_date');
		if ($this->checkMonthBeforeDate($data['start_date'])) {
			throw new \Exception('不可增加本月之前的计费配置');
		}
		$data['remark'] = I('post.remark', '');
		
		//对POST数据进行拆分整理,使之成为一个可以直接增加的二维数组
		return $this->imposeAddData($data);
	}
	
	/**
	 * 增加数据的处理
	 *
	 * @access protected
	 *
	 * @param $data array POST数据
	 *
	 * @return array
	 **/
	protected function imposeAddData($data)
	{
		if ($data['product_id'] == '210') {
			return $this->imposeAddData210($data);
		}
		
		if ($data['product_id'] == '1000') {
			return $this->imposeAddData1000($data);
		}
		
		if ($data['product_id'] == 615) {
			return $this->imposeAddData615($data);
		}
		
		//按账号切割
		$account_id = $data['account_id'];
		$time       = time();
		if (is_array($data['price'])) {
			$price = json_encode(array_values($data['price']), JSON_UNESCAPED_UNICODE);
		} else {
			$price = $data['price'];
		}
		
		return array_map(function ($item) use ($data, $time, $price) {
			$this->checkAccountInCustomer($data['customer_id'], $item);
			$this->checkAccountStoreProduct($item, $data['product_id']);
			$this->checkUniqueFeeConfig($item, $data['product_id'], $data['start_date']);
			
			return [
				'customer_id'     => $data['customer_id'],
				'account_id'      => $item,
				'product_id'      => $data['product_id'],
				'fee_basis'       => $data['basis'],
				'fee_method'      => $data['method'],
				'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
				'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
				'fee_step_rule'   => $data['period'],
				'fee_price_rule'  => $data['price_rule'],
				'fee_price'       => $price,
				'start_date'      => $data['start_date'],
				'remarks'         => $data['remark'],
				'create_time'     => $time,
				'is_delete'       => 0,
				'update_time'     => $time,
				'admin'           => $this->getAdmin(),
			];
		}, $account_id);
	}
	
	/**
	 * 210产品的增加数据组合
	 *
	 * @access protected
	 *
	 * @param $data array POST数据
	 *
	 * @return array
	 **/
	protected function imposeAddData210($data)
	{
		//查询所有接口
		$product_id = ['in', $this->get210AllChildrenProductId()];
		//查询当前账号开通的接口
		$account_id = $data['account_id'][0];
		if (empty($account_id)) {
			throw new \Exception('找不到account_id字段');
		}
		$product_id = $this->account_product_model->field('product_id')
												  ->where(compact('product_id', 'account_id'))
												  ->select();
		$product_id = array_column($product_id, 'product_id');
		//校验客户与账号
		$this->checkAccountInCustomer($data['customer_id'], $account_id);
		//唯一性的校验
		$this->checkUniqueFeeConfig($account_id, 210, $data['start_date']);
		$time = time();
		//区分接口
		if ($data['diff_inter'] == '1') {
			//按产品进行分割
			return array_map(function ($item) use ($data, $time, $account_id) {
				if (is_array($data['price'][$item])) {
					$price = json_encode(array_values($data['price'][$item]), JSON_UNESCAPED_UNICODE);
				} else {
					$price = $data['price'][$item];
				}
				
				return [
					'customer_id'     => $data['customer_id'],
					'account_id'      => $account_id,
					'product_id'      => $item,
					'fee_basis'       => $data['basis'],
					'fee_method'      => $data['method'],
					'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
					'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
					'fee_step_rule'   => $data['period'],
					'fee_price_rule'  => $data['price_rule'],
					'fee_price'       => $price,
					'diff_inter'      => 1,
					'start_date'      => $data['start_date'],
					'remarks'         => $data['remark'],
					'create_time'     => $time,
					'is_delete'       => 0,
					'update_time'     => $time,
					'admin'           => $this->getAdmin(),
				];
			}, $product_id);
		} else {
			if (is_array($data['price'])) {
				$price = json_encode(array_values($data['price']), JSON_UNESCAPED_UNICODE);
			} else {
				$price = $data['price'];
			}
			
			//不区分接口
			return [
				[
					'customer_id'     => $data['customer_id'],
					'account_id'      => $account_id,
					'product_id'      => 210,
					'fee_basis'       => $data['basis'],
					'fee_method'      => $data['method'],
					'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
					'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
					'fee_step_rule'   => $data['period'],
					'fee_price_rule'  => $data['price_rule'],
					'fee_price'       => $price,
					'diff_inter'      => 0,
					'start_date'      => $data['start_date'],
					'remarks'         => $data['remark'],
					'create_time'     => $time,
					'is_delete'       => 0,
					'update_time'     => $time,
					'admin'           => $this->getAdmin(),
				],
			];
		}
	}
	
	/**
	 * 1000产品的增加数据组合
	 *
	 * @access protected
	 *
	 * @param $data array POST数据
	 *
	 * @return array
	 **/
	protected function imposeAddData1000($data)
	{
		//查询所有接口
		$product_id = ['in', $this->get1000AllChildrenProductId()];
		//查询当前账号开通的接口
		$account_id = $data['account_id'][0];
		if (empty($account_id)) {
			throw new \Exception('找不到account_id字段');
		}
		$product_id = $this->account_product_model->field('product_id')
												  ->where(compact('product_id', 'account_id'))
												  ->select();
		$product_id = array_column($product_id, 'product_id');
		//校验客户与账号
		$this->checkAccountInCustomer($data['customer_id'], $account_id);
		//唯一性的校验
		$this->checkUniqueFeeConfig($account_id, 1000, $data['start_date']);
		$time = time();
		//区分接口
		if ($data['diff_inter'] == '1') {
			//按产品进行分割
			return array_map(function ($item) use ($data, $time, $account_id) {
				if (is_array($data['price'][$item])) {
					$price = json_encode(array_values($data['price'][$item]), JSON_UNESCAPED_UNICODE);
				} else {
					$price = $data['price'][$item];
				}
				
				return [
					'customer_id'     => $data['customer_id'],
					'account_id'      => $account_id,
					'product_id'      => $item,
					'fee_basis'       => $data['basis'],
					'fee_method'      => $data['method'],
					'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
					'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
					'fee_step_rule'   => $data['period'],
					'fee_price_rule'  => $data['price_rule'],
					'fee_price'       => $price,
					'diff_inter'      => 1,
					'start_date'      => $data['start_date'],
					'remarks'         => $data['remark'],
					'create_time'     => $time,
					'is_delete'       => 0,
					'update_time'     => $time,
					'admin'           => $this->getAdmin(),
				];
			}, $product_id);
		} else {
			if (is_array($data['price'])) {
				$price = json_encode(array_values($data['price']), JSON_UNESCAPED_UNICODE);
			} else {
				$price = $data['price'];
			}
			
			//不区分接口
			return [
				[
					'customer_id'     => $data['customer_id'],
					'account_id'      => $account_id,
					'product_id'      => 1000,
					'fee_basis'       => $data['basis'],
					'fee_method'      => $data['method'],
					'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
					'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
					'fee_step_rule'   => $data['period'],
					'fee_price_rule'  => $data['price_rule'],
					'fee_price'       => $price,
					'diff_inter'      => 0,
					'start_date'      => $data['start_date'],
					'remarks'         => $data['remark'],
					'create_time'     => $time,
					'is_delete'       => 0,
					'update_time'     => $time,
					'admin'           => $this->getAdmin(),
				],
			];
		}
	}
	
	/**
	 * 615产品的增加数据组合
	 *
	 * @access protected
	 *
	 * @param $data array POST数据
	 *
	 * @return array
	 **/
	protected function imposeAddData615($data)
	{
		//查询所有接口
		$product_id = ['in', $this->get615AllChildrenProductId()];
		//查询当前账号开通的接口
		$account_id = $data['account_id'][0];
		if (empty($account_id)) {
			throw new \Exception('找不到account_id字段');
		}
		$product_id = $this->account_product_model->field('product_id')
												  ->where(compact('product_id', 'account_id'))
												  ->select();
		$product_id = array_column($product_id, 'product_id');
		//校验客户与账号
		$this->checkAccountInCustomer($data['customer_id'], $account_id);
		//唯一性的校验
		$this->checkUniqueFeeConfig($account_id, 615, $data['start_date']);
		$time = time();
		//区分接口
		if ($data['diff_inter'] == '1') {
			//按产品进行分割
			return array_map(function ($item) use ($data, $time, $account_id) {
				if (is_array($data['price'][$item])) {
					$price = json_encode(array_values($data['price'][$item]), JSON_UNESCAPED_UNICODE);
				} else {
					$price = $data['price'][$item];
				}
				
				return [
					'customer_id'     => $data['customer_id'],
					'account_id'      => $account_id,
					'product_id'      => $item,
					'fee_basis'       => $data['basis'],
					'fee_method'      => $data['method'],
					'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
					'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
					'fee_step_rule'   => $data['period'],
					'fee_price_rule'  => 0,
					'fee_price'       => $price,
					'diff_inter'      => 1,
					'start_date'      => $data['start_date'],
					'remarks'         => $data['remark'],
					'create_time'     => $time,
					'is_delete'       => 0,
					'update_time'     => $time,
					'admin'           => $this->getAdmin(),
				];
			}, $product_id);
		} else {
			if (is_array($data['price'])) {
				$price = json_encode(array_values($data['price']), JSON_UNESCAPED_UNICODE);
			} else {
				$price = $data['price'];
			}
			
			//不区分接口
			return [
				[
					'customer_id'     => $data['customer_id'],
					'account_id'      => $account_id,
					'product_id'      => 615,
					'fee_basis'       => $data['basis'],
					'fee_method'      => $data['method'],
					'fee_time_rule'   => ($data['method'] == 1) ? $data['rule'] : 0,
					'fee_amount_rule' => ($data['method'] == 2) ? $data['rule'] : 0,
					'fee_step_rule'   => $data['period'],
					'fee_price_rule'  => 0,
					'fee_price'       => $price,
					'diff_inter'      => 0,
					'start_date'      => $data['start_date'],
					'remarks'         => $data['remark'],
					'create_time'     => $time,
					'is_delete'       => 0,
					'update_time'     => $time,
					'admin'           => $this->getAdmin(),
				],
			];
		}
	}
	
	/**
	 * 校验某个客户是否存在某个账号
	 *
	 * @access protected
	 *
	 * @param $customer_id string 客户ID
	 * @param $account_id  string 账号ID
	 *
	 * @return void
	 **/
	protected function checkAccountInCustomer($customer_id, $account_id)
	{
		$is_delete = 0;
		$count     = $this->account_model->where(compact('customer_id', 'account_id', 'is_delete'))
										 ->count();
		if ($count == 0) {
			throw new \Exception("校验{$customer_id}不存在{$account_id}账号");
		}
	}
	
	/**
	 * 校验某个账号是否开通了某个产品
	 *
	 * @access protected
	 *
	 * @param $account_id string 账号ID
	 * @param $product_id string 产品ID
	 *
	 * @return void
	 **/
	protected function checkAccountStoreProduct($account_id, $product_id)
	{
		$count = $this->account_product_model->where(compact('account_id', 'product_id'))
											 ->count();
		if ($count == 0) {
			throw new \Exception("校验{$account_id}未开通{$product_id}产品");
		}
	}
	
	/**
	 * 校验某个账号、产品、日期下是否存在计费配置
	 *
	 * @access protected
	 *
	 * @param $account_id string 账号ID
	 * @param $product_id string 产品ID
	 * @param $start_date string 日期
	 * @param $where      array 额外的条件
	 *
	 * @return void
	 **/
	protected function checkUniqueFeeConfig($account_id, $product_id, $start_date, $where = [])
	{
		$is_delete = 0;
		$is_reset  = 0;
		//210产品判断的依据是所有子产品及父产品的唯一
		if ($product_id == 210) {
			$children_product_id = $this->get210AllChildrenProductId();
			$where               = array_merge($where, compact('account_id', 'start_date', 'is_delete', 'is_reset'), [
				'product_id' => ['in', array_merge($children_product_id, [210])],
			]);
			$count               = $this->feeConfig_model->where($where)
														 ->count();
		} else if ($product_id == 615) {
			$children_product_id = $this->get615AllChildrenProductId();
			$where               = array_merge($where, compact('account_id', 'start_date', 'is_delete', 'is_reset'), [
				'product_id' => ['in', array_merge($children_product_id, [615])],
			]);
			$count               = $this->feeConfig_model->where($where)
														 ->count();
		} else {
			$count = $this->feeConfig_model->where(array_merge(compact('account_id', 'is_reset', 'product_id', 'start_date', 'is_delete'), $where))
										   ->count();
		}
		//当天是否存在
		if ($count > 0) {
			$accountName = $this->account_model->where(compact('account_id', 'is_delete'))
											   ->getField('account_name');
			$productName = $this->product_model->where(compact('product_id', 'is_delete'))
											   ->getField('product_name');
			throw new \Exception("【{$accountName}】账号在【{$start_date}】时间已存在【{$productName}】产品的计费配置");
		}
	}
	
	/**
	 * 快速获取非空的POST数据
	 *
	 * @access protected
	 *
	 * @param $name string 参数名称
	 *
	 * @return mixed
	 **/
	protected function simpleGetPostParam($name)
	{
		$value = I("post.{$name}", '');
		if (empty($value) && $value != 0) {
			throw new \Exception("缺少{$name}参数");
		}
		
		return $value;
	}
	
	/**
	 * 为客户编辑页生成计费配置列表数据
	 *
	 * @access public
	 *
	 * @param $customer_id string 客户ID
	 *
	 * @return array
	 **/
	public function list_html($customer_id)
	{
		//获取全量计费数据
		$is_delete = 0;
		$is_reset  = 0;
		$data      = $this->feeConfig_model->where(compact('customer_id', 'is_delete', 'is_reset'))
										   ->order('start_date desc')
										   ->select();
		//获取210产品的子ID
		$children_product_id_210 = $this->get210AllChildrenProductId();
		//获取615产品的子ID
		$children_product_id_615 = $this->get615AllChildrenProductId();
		//获取1000产品的子ID
		$children_product_id_1000 = $this->get1000AllChildrenProductId();
		//获取全量的账号数据
		$account_data = $this->account_model->field('account_id,account_name')
											->where(compact('customer_id', 'is_delete'))
											->select();
		$account_data = array_column($account_data, 'account_name', 'account_id');
		//获取全量的展示产品数据
		$back_status  = 1;
		$product_data = $this->product_model->field('product_id,product_name,fee_config,father_id')
											->where(compact('back_status'))
											->select();
		$product_data = array_column($product_data, null, 'product_id');
		
		//遍历数据，过滤掉210产品的数据
		$validate_children_product_fee_config = [];      //用于校验210是否已经存放在结果组中(数据格式 accountId_startDate)
		$result                               = [];
		array_walk($data, function ($item) use (&$result, &$validate_children_product_fee_config, $children_product_id_210, $children_product_id_615, $children_product_id_1000, $account_data, $product_data) {
			$product_id = $item['product_id'];
			$account_id = $item['account_id'];
			$start_date = $item['start_date'];
			if (!array_key_exists($product_id, $product_data)) {
				//表示可能是210/615/1000的产品
				$father_id = in_array($product_id, $children_product_id_210) ? 210 : 0;
				$father_id = in_array($product_id, $children_product_id_615) ? 615 : $father_id;
				$father_id = in_array($product_id, $children_product_id_1000) ? 1000 : $father_id;
				if ($father_id == 210 || $father_id == 615 || $father_id = 1000) {
					$validate_label = "{$account_id}_{$start_date}_{$father_id}";
					if (in_array($validate_label, $validate_children_product_fee_config)) {
						return true;
					} else {
						$product_id = $father_id;
						//特殊产品的价格
						$item['fee_price']                      = $this->computeFeePriceForSpecialProduct($start_date, $account_id, $product_id);
						$validate_children_product_fee_config[] = $validate_label;
					}
				}
			}
			$fee_config   = array_column(json_decode($product_data[$product_id]['fee_config'], true), 'cn_name', 'val');
			$product_name = $product_data[$product_id]['product_name'];
			$period_name  = '';  //阶梯周期
			if ($item['fee_method'] == 1) {
				$method = '按时间';
				switch ($item['fee_time_rule']) {
					case '1':
						$rule = '包日';
						break;
					case '2':
						$rule = '包月';
						break;
					case '3':
						$rule = '包年';
						break;
					default:
						$rule = '';
						break;
				}
			} else {
				$method = '按用量';
				switch ($item['fee_amount_rule']) {
					case '1':
						$rule = '固定价格';
						break;
					case '2':
						$rule = '累进阶梯';
						switch ($item['fee_step_rule']) {
							case 1:
								$period_name = '日';
								break;
							case 2:
								$period_name = '月';
								break;
							case 3:
								$period_name = '年';
								break;
							case 4:
								$period_name = '无周期';
								break;
							default :
								$period_name = '未知周期';
								break;
						}
						break;
					case '3':
						$rule = '到达阶梯';
						switch ($item['fee_step_rule']) {
							case 1:
								$period_name = '日';
								break;
							case 2:
								$period_name = '月';
								break;
							case 3:
								$period_name = '年';
								break;
							case 4:
								$period_name = '无周期';
								break;
							default :
								$period_name = '未知周期';
								break;
						}
						break;
					default:
						$rule = '';
						break;
				}
			}
			$is_delete = $this->checkMonthBeforeDate($item['start_date']) ? '0' : '1';
			$result[]  = [
				'id'           => $item['id'],
				'start_date'   => $start_date,
				'product_name' => $product_name,
				'account_name' => $account_data[$account_id],
				'basis'        => $fee_config[$item['fee_basis']],
				'method'       => $method,
				'rule'         => $rule,
				'price_rule'   => ($item['fee_price_rule'] == 2) ? '区分' : '不区分',
				'price'        => str_replace(['],[', '[[', ']]'], [
					'],<br/>[',
					'[<br/>[',
					']<br/>]',
				], $item['fee_price']),
				'update_time'  => $item['update_time'],
				'create_time'  => $item['create_time'],
				'is_delete'    => $is_delete,       //是否允许编辑、删除
				'remark'       => $item['remarks'],
				'period'       => $period_name,
			];
			
			return true;
		});
		
		return $result;
	}
	
	/**
	 * 组合特殊产品（210 615）的列表展示价格
	 *
	 * @access protected
	 *
	 * @param $start_date string 计费开始时间
	 * @param $account_id string 账号ID
	 * @param $product_id string 产品ID
	 *
	 * @return string
	 **/
	protected function computeFeePriceForSpecialProduct($start_date, $account_id, $product_id)
	{
		$functionName = "get{$product_id}AllChildrenProductId";
		$product_data = $this->$functionName(true);
		$product_id   = ['in', array_column($product_data, 'product_id')];
		$product_data = array_column($product_data, 'product_name', 'product_id');
		$is_reset     = 0;
		$is_delete    = 0;
		$data         = $this->feeConfig_model->field('fee_price, product_id')
											  ->where(compact('product_id', 'start_date', 'account_id', 'is_delete', 'is_reset'))
											  ->select();
		if (count(array_unique(array_column($data, 'fee_price'))) > 1) {
			$result = array_map(function ($item) use ($product_data) {
				return [
					$product_data[$item['product_id']],
					$item['fee_price'],
				];
			}, $data);
			
			return json_encode($result, JSON_UNESCAPED_UNICODE);
		} else {
			return array_unique(array_column($data, 'fee_price'))[0];
		}
		
	}
	
	/**
	 * 验证某个时间是否为当月之前的时间
	 *
	 * @access protected
	 *
	 * @param $date string 校验日期
	 *
	 * @return boolean
	 **/
	protected function checkMonthBeforeDate($date)
	{
		$this_month_time = strtotime(date('Y-m'));
		$time            = strtotime($date);
		
		return $time < $this_month_time && !C('ALLOW_HISTORY_FEE_CONFIG');
	}
	
	/**
	 * 为编辑计费配置也生成数据
	 *
	 * @access public
	 *
	 * @return array
	 **/
	public function edit_html()
	{
		$data = $this->getEditHtmlData();
		list($customer_data, $result) = $this->getEditHtmlJsConfig($data);
		//数据权限校验
		$customer_id = $customer_data['customer_id'];
		DataAuthController::instance()
						  ->validAllowDoCustomer($customer_id);
		
		$result = json_encode($result, JSON_UNESCAPED_UNICODE);
		$data   = json_encode($data, JSON_UNESCAPED_UNICODE);
		
		return compact('result', 'customer_data', 'data');
	}
	
	/**
	 * 生成编辑数据的页面配置
	 *
	 * @access protected
	 *
	 * @param $data array 编辑的数据
	 *
	 * @return array
	 **/
	protected function getEditHtmlJsConfig($data)
	{
		//默认产品配置项
		$defaultProductConfig = [
			'product_id'        => '',
			'product_name'      => '',
			'price_rule'        => false,    //价格规则（区分运营商）
			'diff_inter'        => false,     //是否区分接口
			'unique_account'    => false,     //唯一选择账号限制
			'price_input_model' => 'common',    //价格表单域模板
			'account_list'      => [],       //账号列表，
			'basis_list'        => []        //计费依据
		];
		
		
		//客户数据
		$customer_id   = $data['customer_id'];
		$is_delete     = 0;
		$customer_data = $this->customer_model->field('customer_id,name as customer_name')
											  ->where(compact('customer_id', 'is_delete'))
											  ->find();
		if (empty($customer_data)) {
			throw new \Exception('该客户不存在');
		}
		//账号数据
		$account_id   = $data['account_id'];
		$account_data = $this->account_model->field('account_id,account_name')
											->where(compact('account_id'))
											->select();
		$account_data = array_column($account_data, null, 'account_id');
		//产品数据
		$product_id                          = $data['product_id'];
		$product_data                        = $this->product_model->field('product_id,product_name,fee_config')
																   ->where(compact('product_id'))
																   ->find();
		$result                              = [
			$product_id => $defaultProductConfig,
		];
		$result[$product_id]['product_name'] = $product_data['product_name'];
		$result[$product_id]['product_id']   = $product_id;
		$result[$product_id]['basis_list']   = $product_data['fee_config'] ? json_decode($product_data['fee_config'], true) : [];
		
		if ($product_id == 210) {
			$result[210]['price_rule']                     = true;
			$result[210]['diff_inter']                     = true;
			$result[210]['unique_account']                 = true;
			$result[210]['price_input_model']              = 'children_field';
			$product_id                                    = ['in', $data['children_product_list']];
			$children_product                              = $this->product_model->field('product_id,product_name')
																				 ->where(compact('product_id'))
																				 ->select();
			$children_product                              = array_column($children_product, 'product_name', 'product_id');
			$account_data[$account_id]['children_product'] = $children_product;
			$result[210]['account_list']                   = $account_data;
		} else if ($product_id == 1000) {
			$result[1000]['price_rule']                    = true;
			$result[1000]['diff_inter']                    = true;
			$result[1000]['unique_account']                = true;
			$result[1000]['price_input_model']             = 'children_field';
			$product_id                                    = ['in', $data['children_product_list']];
			$children_product                              = $this->product_model->field('product_id,product_name')
																				 ->where(compact('product_id'))
																				 ->select();
			$children_product                              = array_column($children_product, 'product_name', 'product_id');
			$account_data[$account_id]['children_product'] = $children_product;
			$result[1000]['account_list']                  = $account_data;
		} else if ($product_id == 615) {
			$result[615]['price_rule']                     = false;
			$result[615]['diff_inter']                     = true;
			$result[615]['unique_account']                 = true;
			$result[615]['price_input_model']              = 'children_field';
			$product_id                                    = ['in', $data['children_product_list']];
			$children_product                              = $this->product_model->field('product_id,product_name')
																				 ->where(compact('product_id'))
																				 ->select();
			$children_product                              = array_column($children_product, 'product_name', 'product_id');
			$account_data[$account_id]['children_product'] = $children_product;
			$result[615]['account_list']                   = $account_data;
		} else {
			$result[$product_id]['account_list'] = $account_data;
			//邦秒验所有的子产品会区分运营商
			$father_id               = 200;
			$children_product_id_200 = $this->product_model->field('product_id')
														   ->where(compact('father_id'))
														   ->select();
			$children_product_id_200 = array_column($children_product_id_200, 'product_id');
			if (in_array($product_id, $children_product_id_200)) {
				$result[$product_id]['price_rule'] = true;
			}
		}
		
		return [$customer_data, $result];
	}
	
	/**
	 * 获取编辑数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getEditHtmlData()
	{
		//获取编辑的数据
		$id        = I('get.id', '0', 'trim');
		$is_delete = 0;
		$is_reset  = 0;
		$data      = $this->feeConfig_model->where(compact('id', 'is_delete', 'is_reset'))
										   ->find();
		
		if (empty($data)) {
			throw new \Exception('未找到需要编辑的数据');
		}
		
		//校验时间
		if ($this->checkMonthBeforeDate($data['start_date'])) {
			throw new \Exception('此条计费配置已被使用，不可修改');
		}
		
		//获取其中存在变化的数据（价格、产品ID、数据ID）
		$trends = $this->getTrendsEditData($data);
		$data   = array_merge($data, $trends);
		//整理字段
		$data['basis']  = $data['fee_basis'];
		$data['method'] = $data['fee_method'];
		if ($data['fee_method'] == 1) {
			$data['rule'] = $data['fee_time_rule'];
		} else {
			$data['rule'] = $data['fee_amount_rule'];
		}
		$data['period']     = $data['fee_step_rule'];
		$data['price_rule'] = $data['fee_price_rule'];
		$data['remark']     = $data['remarks'];
		
		return $data;
	}
	
	/**
	 * 获取210产品的价格配置
	 *
	 * @access protected
	 *
	 * @param $data array 计费数据
	 *
	 * @return array
	 **/
	protected function getTrendsEditData($data)
	{
		$result            = [];
		$product_data_210  = $this->get210AllChildrenProductId();
		$product_data_615  = $this->get615AllChildrenProductId();
		$product_data_1000 = $this->get1000AllChildrenProductId();
		$product_id        = $data['product_id'];
		if ($product_id == 210) {
			$account_id                      = $data['account_id'];
			$product_id                      = ['in', $product_data_210];
			$storeProductId                  = $this->account_product_model->field('product_id')
																		   ->where(compact('product_id', 'account_id'))
																		   ->select();
			$result['children_product_list'] = array_column($storeProductId, 'product_id');
			$price                           = @json_decode($data['fee_price'], true);
			if (!is_null($price)) {
				$result['price'] = $price;
			} else {
				$result['price'] = $data['fee_config'];
			}
			$result['diff_inter'] = 0;
		} else if ($product_id == 1000) {
			$account_id                      = $data['account_id'];
			$product_id                      = ['in', $product_data_1000];
			$storeProductId                  = $this->account_product_model->field('product_id')
																		   ->where(compact('product_id', 'account_id'))
																		   ->select();
			$result['children_product_list'] = array_column($storeProductId, 'product_id');
			$price                           = @json_decode($data['fee_price'], true);
			if (!is_null($price)) {
				$result['price'] = $price;
			} else {
				$result['price'] = $data['fee_config'];
			}
			$result['diff_inter'] = 0;
		} else if ($product_id == 615) {
			$account_id                      = $data['account_id'];
			$product_id                      = ['in', $product_data_615];
			$storeProductId                  = $this->account_product_model->field('product_id')
																		   ->where(compact('product_id', 'account_id'))
																		   ->select();
			$result['children_product_list'] = array_column($storeProductId, 'product_id');
			$price                           = @json_decode($data['fee_price'], true);
			if (!is_null($price)) {
				$result['price'] = $price;
			} else {
				$result['price'] = $data['fee_config'];
			}
			$result['diff_inter'] = 0;
		} else if (in_array($product_id, $product_data_210)) {
			$result['product_id'] = 210;
			$account_id           = $data['account_id'];
			//获取当前账号开通的所有邦信分快捷版的子产品
			$product_id = ['in', $product_data_210];
			//$storeProductId = $this->account_product_model->field('product_id')->where(compact('product_id', 'account_id'))->select();
			//$storeProductId = array_column($storeProductId, 'product_id');
			//获取当前的计费配置的所有数据
			$start_date      = $data['start_date'];
			$is_reset        = 0;
			$fee_config_data = $this->feeConfig_model->field('fee_price,product_id,id')
													 ->where(compact('product_id', 'start_date', 'account_id', 'is_reset'))
													 ->select();
			//获取当前计费配置中的所有子产品
			$fee_product_id = array_column($fee_config_data, 'product_id');
			//已开通但不存在的计费配置的产品（原来设置的计费配置，但是又新增的子产品）
			//$add_fee_product_id = array_diff($storeProductId, $fee_product_id);
			//存在计费配置但未开通的产品（原来设置的计费配置，但是又删除的子产品）
			//$delete_fee_product_id = array_diff($fee_product_id, $storeProductId);
			//存在计费配置也存在开通的产品（原来设置的计费配置，既不是新增，也没有被删除的子产品）
			//$old_fee_product_id = array_intersect($storeProductId, $fee_product_id);
			
			//设置原有的计费配置
			$price                 = [];
			$id                    = [];
			$children_product_list = [];
			$fee_config_data       = array_column($fee_config_data, null, 'product_id');
			array_walk($fee_product_id, function ($product_id) use (&$price, &$id, &$children_product_list, $fee_config_data) {
				$json = @json_decode($fee_config_data[$product_id]['fee_price']);
				if (is_null(($json))) {
					$price[$product_id] = $fee_config_data[$product_id]['fee_price'];
				} else {
					$price[$product_id] = $json;
				}
				$id[]                    = $fee_config_data[$product_id]['id'];
				$children_product_list[] = $product_id;
			});
			$result['children_product_list'] = $children_product_list;
			$result['price']                 = $price;
			$result['id']                    = implode(',', $id);
			$result['diff_inter']            = 1;
		} else if (in_array($product_id, $product_data_1000)) {
			$result['product_id'] = 1000;
			$account_id           = $data['account_id'];
			//获取当前账号开通的所有邦信分快捷版的子产品
			$product_id = ['in', $product_data_1000];
			//$storeProductId = $this->account_product_model->field('product_id')->where(compact('product_id', 'account_id'))->select();
			//$storeProductId = array_column($storeProductId, 'product_id');
			//获取当前的计费配置的所有数据
			$start_date      = $data['start_date'];
			$is_reset        = 0;
			$fee_config_data = $this->feeConfig_model->field('fee_price,product_id,id')
													 ->where(compact('product_id', 'start_date', 'account_id', 'is_reset'))
													 ->select();
			//获取当前计费配置中的所有子产品
			$fee_product_id = array_column($fee_config_data, 'product_id');
			//已开通但不存在的计费配置的产品（原来设置的计费配置，但是又新增的子产品）
			//$add_fee_product_id = array_diff($storeProductId, $fee_product_id);
			//存在计费配置但未开通的产品（原来设置的计费配置，但是又删除的子产品）
			//$delete_fee_product_id = array_diff($fee_product_id, $storeProductId);
			//存在计费配置也存在开通的产品（原来设置的计费配置，既不是新增，也没有被删除的子产品）
			//$old_fee_product_id = array_intersect($storeProductId, $fee_product_id);
			
			//设置原有的计费配置
			$price                 = [];
			$id                    = [];
			$children_product_list = [];
			$fee_config_data       = array_column($fee_config_data, null, 'product_id');
			array_walk($fee_product_id, function ($product_id) use (&$price, &$id, &$children_product_list, $fee_config_data) {
				$json = @json_decode($fee_config_data[$product_id]['fee_price']);
				if (is_null(($json))) {
					$price[$product_id] = $fee_config_data[$product_id]['fee_price'];
				} else {
					$price[$product_id] = $json;
				}
				$id[]                    = $fee_config_data[$product_id]['id'];
				$children_product_list[] = $product_id;
			});
			$result['children_product_list'] = $children_product_list;
			$result['price']                 = $price;
			$result['id']                    = implode(',', $id);
			$result['diff_inter']            = 1;
		} else if (in_array($product_id, $product_data_615)) {
			$result['product_id'] = 615;
			$account_id           = $data['account_id'];
			//获取当前账号开通的所有邦信分快捷版的子产品
			$product_id = ['in', $product_data_615];
			//$storeProductId = $this->account_product_model->field('product_id')->where(compact('product_id', 'account_id'))->select();
			//$storeProductId = array_column($storeProductId, 'product_id');
			//获取当前的计费配置的所有数据
			$start_date      = $data['start_date'];
			$is_reset        = 0;
			$fee_config_data = $this->feeConfig_model->field('fee_price,product_id,id')
													 ->where(compact('product_id', 'start_date', 'account_id', 'is_reset'))
													 ->select();
			//获取当前计费配置中的所有子产品
			$fee_product_id = array_column($fee_config_data, 'product_id');
			//已开通但不存在的计费配置的产品（原来设置的计费配置，但是又新增的子产品）
			//$add_fee_product_id = array_diff($storeProductId, $fee_product_id);
			//存在计费配置但未开通的产品（原来设置的计费配置，但是又删除的子产品）
			//$delete_fee_product_id = array_diff($fee_product_id, $storeProductId);
			//存在计费配置也存在开通的产品（原来设置的计费配置，既不是新增，也没有被删除的子产品）
			//$old_fee_product_id = array_intersect($storeProductId, $fee_product_id);
			
			//设置原有的计费配置
			$price                 = [];
			$id                    = [];
			$children_product_list = [];
			$fee_config_data       = array_column($fee_config_data, null, 'product_id');
			array_walk($fee_product_id, function ($product_id) use (&$price, &$id, &$children_product_list, $fee_config_data) {
				$json = @json_decode($fee_config_data[$product_id]['fee_price']);
				if (is_null(($json))) {
					$price[$product_id] = $fee_config_data[$product_id]['fee_price'];
				} else {
					$price[$product_id] = $json;
				}
				$id[]                    = $fee_config_data[$product_id]['id'];
				$children_product_list[] = $product_id;
			});
			$result['children_product_list'] = $children_product_list;
			$result['price']                 = $price;
			$result['id']                    = implode(',', $id);
			$result['diff_inter']            = 1;
		} else {
			$price = @json_decode($data['fee_price'], true);
			if (!is_null($price)) {
				$result['price'] = $price;
			} else {
				$result['price'] = $data['fee_config'];
			}
			$result['diff_inter'] = 0;
		}
		
		return $result;
		
		
		/*if ($data['product_id'] == 210) {
			$account_id                      = $data['account_id'];
			$product_id                      = ['in', $product_data_210];
			$storeProductId                  = $this->account_product_model->field('product_id')
				->where(compact('product_id', 'account_id'))
				->select();
			$result['children_product_list'] = array_column($storeProductId, 'product_id');
			$price                           = @json_decode($data['fee_price'], true);
			if (!is_null($price)) {
				$result['price'] = $price;
			} else {
				$result['price'] = $data['fee_config'];
			}
			$result['diff_inter'] = 0;
		} elseif (!in_array($data['product_id'], $product_data_210) && !in_array($data['product_id'],
				$product_data_615)
		) {

		} else {
			$result['product_id'] = 210;
			$account_id           = $data['account_id'];
			//获取当前账号开通的所有邦信分快捷版的子产品
			$product_id = ['in', $product_data_210];
			//$storeProductId = $this->account_product_model->field('product_id')->where(compact('product_id', 'account_id'))->select();
			//$storeProductId = array_column($storeProductId, 'product_id');
			//获取当前的计费配置的所有数据
			$start_date      = $data['start_date'];
			$is_reset        = 0;
			$fee_config_data = $this->feeConfig_model->field('fee_price,product_id,id')
				->where(compact('product_id', 'start_date', 'account_id', 'is_reset'))
				->select();
			//获取当前计费配置中的所有子产品
			$fee_product_id = array_column($fee_config_data, 'product_id');
			//已开通但不存在的计费配置的产品（原来设置的计费配置，但是又新增的子产品）
			//$add_fee_product_id = array_diff($storeProductId, $fee_product_id);
			//存在计费配置但未开通的产品（原来设置的计费配置，但是又删除的子产品）
			//$delete_fee_product_id = array_diff($fee_product_id, $storeProductId);
			//存在计费配置也存在开通的产品（原来设置的计费配置，既不是新增，也没有被删除的子产品）
			//$old_fee_product_id = array_intersect($storeProductId, $fee_product_id);

			//设置原有的计费配置
			$price                 = [];
			$id                    = [];
			$children_product_list = [];
			$fee_config_data       = array_column($fee_config_data, null, 'product_id');
			array_walk($fee_product_id,
				function ($product_id) use (&$price, &$id, &$children_product_list, $fee_config_data) {
					$json = @json_decode($fee_config_data[$product_id]['fee_price']);
					if (is_null(($json))) {
						$price[$product_id] = $fee_config_data[$product_id]['fee_price'];
					} else {
						$price[$product_id] = $json;
					}
					$id[]                    = $fee_config_data[$product_id]['id'];
					$children_product_list[] = $product_id;
				});
			$result['children_product_list'] = $children_product_list;
			$result['price']                 = $price;
			$result['id']                    = implode(',', $id);
			$result['diff_inter']            = 1;
		}
		return $result;*/
	}
	
	/**
	 * 保存修改的计费配置
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function edit()
	{
		$customer_id = I('post.customer_id', '', 'trim');
		if (empty($customer_id)) {
			throw new \Exception('客户ID不存在');
		}
		//数据权限校验
		DataAuthController::instance()
						  ->validAllowDoCustomer($customer_id);
		
		//获取POST数据
		$data = $this->getEditData();
		//编辑数据
		if (in_array(I('post.product_id', ''), [210, 615])) {
			$this->editProduct210($data);
		} else {
			//其他产品
			$id = $data['id'];
			unset($data['id']);
			$this->feeConfig_model->where(compact('id'))
								  ->save($data);
		}
	}
	
	/**
	 * 修改210产品的计费配置
	 *      1.区分接口字段是否发生变化
	 *      2.修改的数据是否区分接口
	 *
	 * @access protected
	 *
	 * @param $data array 处理后的编辑数据
	 *
	 * @return void
	 **/
	protected function editProduct210($data)
	{
		//是否区分接口
		$diff_inter = $data['diff_inter'];
		$id         = ['in', $data['id']];
		//查询修改之前的计费配置
		$updateBeforeData      = $this->feeConfig_model->where(compact('id'))
													   ->find();
		$updateBeforeDiffInter = $updateBeforeData['diff_inter'];
		
		//如果diff_inter发生未发生变化，只需要进行修改即可
		if ($updateBeforeDiffInter == $diff_inter) {
			if ($diff_inter == 1) {
				if (is_string($data['fee_price'])) {
					$data['fee_price'] = json_decode($data['fee_price'], true);
				}
				foreach ($data['fee_price'] as $product_id => $price) {
					$update_data              = $data;
					$update_data['fee_price'] = $price;
					unset($update_data['id']);
					$this->feeConfig_model->where(compact('id', 'product_id'))
										  ->save($update_data);
				}
			} else {
				$id = $data['id'];
				unset($data['id']);
				$this->feeConfig_model->where(compact('id'))
									  ->save($data);
			}
		} else {
			throw new \Exception('暂不支持修改区分接口字段');
		}
	}
	
	/**
	 * 获取编辑数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getEditData()
	{
		$data = [];
		//获取POST数据
		$data['id']     = $this->simpleGetPostParam('id');
		$data['basis']  = $this->simpleGetPostParam('basis');
		$data['method'] = $this->simpleGetPostParam('method');
		$data['rule']   = $this->simpleGetPostParam('rule');
		//阶梯周期
		$data['period'] = I('post.period', 0, 'trim');
		//是否区分运营商
		$data['price_rule'] = I('post.price_rule', 0, 'trim');
		$data['price']      = $this->simpleGetPostParam('price');
		$data['start_date'] = $this->simpleGetPostParam('start_date');
		if ($this->checkMonthBeforeDate($data['start_date'])) {
			throw new \Exception('不可修改为本月之前的计费配置');
		}
		$data['remark']     = I('post.remark', '');
		$data['diff_inter'] = I('post.diff_inter', 0);
		
		//对POST数据进行拆分整理,使之成为一个可以直接增加的二维数组
		return $this->imposeEditData($data);
	}
	
	/**
	 * 处理编辑数据
	 *
	 * @access protected
	 *
	 * @param $data array 编辑的数据
	 *
	 * @return array
	 **/
	protected function imposeEditData($data)
	{
		$result = [];
		if (strstr($data['id'], ',')) {
			$result['id'] = explode(',', $data['id']);
			$price        = [];
			array_walk($data['price'], function ($item, $product_id) use (&$price) {
				if (is_array($item)) {
					$price[$product_id] = json_encode($item, JSON_UNESCAPED_UNICODE);
				} else {
					$price[$product_id] = $item;
				}
			});
			$result['fee_price'] = $price;
		} else {
			$result['id'] = $data['id'];
			if (is_array($data['price'])) {
				$result['fee_price'] = json_encode($data['price'], JSON_UNESCAPED_UNICODE);
			} else {
				$result['fee_price'] = $data['price'];
			}
		}
		$result['fee_basis']  = $data['basis'];
		$result['fee_method'] = $data['method'];
		if ($data['method'] == 1) {
			$result['fee_time_rule']   = $data['rule'];
			$result['fee_amount_rule'] = 0;
		} else {
			$result['fee_amount_rule'] = $data['rule'];
			$result['fee_time_rule']   = 0;
		}
		$result['fee_step_rule']  = $data['period'];
		$result['fee_price_rule'] = $data['price_rule'];
		$result['start_date']     = $data['start_date'];
		$result['remarks']        = $data['remark'];
		$result['diff_inter']     = $data['diff_inter'];
		$result['update_time']    = time();
		$result['admin']          = $this->getAdmin();
		
		return $result;
	}
	
	/**
	 * 删除指定的计费配置
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function del()
	{
		$id = $this->simpleGetPostParam('id');
		//查询需要删除的计费配置数据
		$data = $this->feeConfig_model->where(compact('id'))
									  ->find();
		if (empty($data)) {
			throw new \Exception('要删除的数据不存在');
		}
		//数据权限校验
		$customer_id = $data['customer_id'];
		DataAuthController::instance()
						  ->validAllowDoCustomer($customer_id);
		$product_id = $data['product_id'];
		//校验是否为210子产品
		$children_product_id_210 = $this->get210AllChildrenProductId();
		if (in_array($product_id, $children_product_id_210) || $product_id == 210) {
			$id = ['in', $this->getDel210FeeConfigId($data)];
		}
		$children_product_id_1000 = $this->get1000AllChildrenProductId();
		if (in_array($product_id, $children_product_id_1000) || $product_id == 1000) {
			$id = ['in', $this->getDel1000FeeConfigId($data)];
		}
		//删除数据
		$is_delete = 1;
		$admin     = $this->getAdmin();
		$this->feeConfig_model->where(compact('id'))
							  ->save(compact('is_delete', 'admin'));
		//保存日志
		$log_info = [
			'handle_type'   => 'delete',
			'description'   => '删除计费配置',
			'content'       => json_encode($data, JSON_UNESCAPED_UNICODE),
			'handle_user'   => session(C('LOGIN_SESSION_NAME')) ?: "system",
			'handle_result' => 1,
		];
		HandlerLog::log($log_info);
	}
	
	/**
	 * 如果删除的是210产品的计费配置，则需要特殊处理，获取需要删除的计费配置ID
	 *      删除210产品的计费配置时，需要连同当天的重置计费配置一起删除，同时也要删除所有同时配置的子产品的计费配置
	 *
	 * @access protected
	 *
	 * @param $data array 查询到的需要删除的计费配置数据（一条）
	 *
	 * @return array
	 **/
	protected function getDel210FeeConfigId($data)
	{
		
		//全量子产品
		$children_product_id_210 = $this->get210AllChildrenProductId();
		$product_id              = $data['product_id'];
		$start_date              = $data['start_date'];
		$is_reset                = 1;
		if ($product_id == 210) {
			$product_id = ['in', $children_product_id_210];
			//归零的计费配置ID
			$reset_id = $this->feeConfig_model->field('id')
											  ->where(compact('is_reset', 'product_id', 'start_date'))
											  ->select();
			
			return array_values(array_unique(array_merge(array_column($reset_id, 'id'), [$data['id']])));
		} else {
			$product_id = 210;
			//归零的计费配置ID
			$reset_id = $this->feeConfig_model->field('id')
											  ->where(compact('is_reset', 'product_id', 'start_date'))
											  ->select();
			//所有子产品的计费配置
			$product_id = ['in', $children_product_id_210];
			$is_reset   = 0;
			$id         = $this->feeConfig_model->field('id')
												->where(compact('is_reset', 'product_id', 'start_date'))
												->select();
			
			return array_values(array_unique(array_merge(array_column($reset_id, 'id'), array_column($id, 'id'))));
			
		}
	}
	
	/**
	 * 如果删除的是1000产品的计费配置，则需要特殊处理，获取需要删除的计费配置ID
	 *      删除1000产品的计费配置时，需要连同当天的重置计费配置一起删除，同时也要删除所有同时配置的子产品的计费配置
	 *
	 * @access protected
	 *
	 * @param $data array 查询到的需要删除的计费配置数据（一条）
	 *
	 * @return array
	 **/
	protected function getDel1000FeeConfigId($data)
	{
		//全量子产品
		$children_product_id_1000 = $this->get1000AllChildrenProductId();
		$product_id               = $data['product_id'];
		$start_date               = $data['start_date'];
		$is_reset                 = 1;
		if ($product_id == 1000) {
			$product_id = ['in', $children_product_id_1000];
			//归零的计费配置ID
			$reset_id = $this->feeConfig_model->field('id')
											  ->where(compact('is_reset', 'product_id', 'start_date'))
											  ->select();
			
			return array_values(array_unique(array_merge(array_column($reset_id, 'id'), [$data['id']])));
		} else {
			$product_id = 1000;
			//归零的计费配置ID
			$reset_id = $this->feeConfig_model->field('id')
											  ->where(compact('is_reset', 'product_id', 'start_date'))
											  ->select();
			//所有子产品的计费配置
			$product_id = ['in', $children_product_id_1000];
			$is_reset   = 0;
			$id         = $this->feeConfig_model->field('id')
												->where(compact('is_reset', 'product_id', 'start_date'))
												->select();
			
			return array_values(array_unique(array_merge(array_column($reset_id, 'id'), array_column($id, 'id'))));
			
		}
	}
	
	/**
	 * 监听编辑客户，如果将客户的状态从可用修改为禁用，需要对计费配置进行处理
	 *
	 * @access protected
	 *
	 * @param $data        array 需要编辑的数据
	 * @param $customer_id string 客户ID
	 *
	 * @return void
	 **/
	public function listenUpdateCustomer($data, $customer_id)
	{
		if ($data['status'] == 0) {
			//获取更新前的状态
			$oldData = $this->customer_model->where(compact('customer_id'))
											->find();
			if ($oldData['status'] == 1) {
				$this->resetFeeConfigByCustomerId($customer_id);
			}
			
			// 禁用这个客户下面所有的账号
			AccountModel::updateData(compact('customer_id'), ['status' => 0]);
		} else {
			$father_id = '0';
			AccountModel::updateData(compact('customer_id', 'father_id'), ['status' => 1]);
		}
	}
	
	/**
	 * 监听编辑账号，如果将账号的状态从可用修改为禁用，需要对计费配置进行处理
	 *
	 * @access public
	 *
	 * @param $data array 需要编辑的数据
	 * @param $id   integer 数据ID
	 *
	 * @return void
	 **/
	public function listenUpdateAccount($data, $id)
	{
		if ($data['status'] == 0) {
			//获取更新前的数据
			$oldData = $this->account_model->find($id);
			if ($oldData['status'] == 1) {
				$customer_id = $oldData['customer_id'];
				$account_id  = $oldData['account_id'];
				$this->resetFeeConfigByAccountId($customer_id, $account_id);
			}
		}
	}
	
	/**
	 * 监听编辑开通的产品事件、会触发两种不同的事件
	 *      1.当状态由可用->禁用， 删除当日之后的所有相关产品、账号的计费配置，并归零计费配置
	 *      2.如果为执行1事件，如果产品ID为210，并且存在删除的子产品，则对删除的子产品删除当日之后的所有计费配置，并归零计费配置
	 *
	 * @access public
	 *
	 * @return void
	 **/
	public function listenUpdateStoreProduct()
	{
		$product_id  = I('post.product_id', '', 'trim');
		$account_id  = I('post.account_id', '', 'trim');
		$customer_id = $this->account_model->where(compact('account_id'))
										   ->getField('customer_id');
		//获取原来的数据
		$old_data   = $this->account_product_model->where(compact('product_id', 'account_id'))
												  ->find();
		$status     = I('post.status', '', 'trim');
		$start_date = ['gt', date('Y-m-d')];
		$is_delete  = 0;
		//如果前后状态不一致 并且修改的数据中存在状态为禁用，则证明此次修改，是将可以变为禁用
		if ($old_data['status'] != $status && $status == 0) {
			if ($product_id == 210) {
				//获取210所有子产品
				$product_id = ['in', $this->get210AllChildrenProductId()];
			}
			
			//删除明日之后的该客户的所有相关产品的计费配置
			$this->feeConfig_model->where(compact('account_id', 'product_id', 'start_date'))
								  ->save([
									  'is_delete' => 1,
									  'admin'     => $this->getAdmin(),
								  ]);
			//查询需要进行归零的计费配置
			$data = $this->feeConfig_model->field('product_id')
										  ->where(compact('product_id', 'account_id', 'is_delete'))
										  ->select();
			//生成一个可以校验是否已经归零了的账号、产品数组 [product_id]
			$checkIsResetArray = [];
			$data              = array_column($data, 'product_id');
			array_walk($data, function ($product_id) use (&$checkIsResetArray, $customer_id, $account_id) {
				if (in_array($product_id, $checkIsResetArray)) {
					return true;
				}
				$checkIsResetArray[] = $product_id;
				$this->resetFeeConfigForEveryData($customer_id, $account_id, $product_id);
				
				return true;
			});
		} else if ($status == 1) {
			//如果状态未发生变化，那么需要确认是否为210产品，如果为210产品&&减少了开通的子产品，需要对减少的子产品进行归零计费配置
			if ($product_id == 210) {
				//获取210所有子产品
				$product_id = ['in', $this->get210AllChildrenProductId()];
				//获取用户已经开通的210子产品
				$old_store_product_id = $this->account_product_model->field('product_id')
																	->where(compact('account_id', 'product_id'))
																	->select();
				$old_store_product_id = array_column($old_store_product_id, 'product_id');
				//获取该用户编辑后开通的210子产品
				$field                = array_flip(I('post.data', [], 'trim')['fields']);
				$data_json            = I('post.data_json', '', 'trim');
				$data_json            = array_column(json_decode($data_json, true), null, 'name');
				$field_total          = array_column($data_json['fields']['option'], 'product_id', 'opt_val');
				$new_store_product_id = array_values(array_intersect_key($field_total, $field));
				//求差集（新的编辑数据中，别删除的子产品）
				$delete_store_product_id = array_diff($old_store_product_id, $new_store_product_id);
				//生成一个可以校验是否已经归零了的账号、产品数组 [product_id]
				$checkIsResetArray = [];
				array_walk($delete_store_product_id, function ($product_id) use (&$checkIsResetArray, $customer_id, $account_id) {
					if (in_array($product_id, $checkIsResetArray)) {
						return true;
					}
					$checkIsResetArray[] = $product_id;
					$this->resetFeeConfigForEveryData($customer_id, $account_id, $product_id);
					
					return true;
				});
			}
		}
	}
	
	/**
	 * 归零计费配置（针对某客户下的所有账号）
	 *
	 * @access protected
	 *
	 * @param $customer_id string 客户ID
	 *
	 * @return void
	 **/
	protected function resetFeeConfigByCustomerId($customer_id)
	{
		$start_date = ['gt', date('Y-m-d')];
		$is_delete  = 0;
		//删除明天及明天之后的该客户的所有计费配置
		$this->feeConfig_model->where(compact('customer_id', 'start_date'))
							  ->save([
								  'is_delete' => 1,
								  'admin'     => $this->getAdmin(),
							  ]);
		//查询所有存在计费配置的账号、产品数据
		$data = $this->feeConfig_model->field('account_id,product_id')
									  ->where(compact('customer_id', 'is_delete'))
									  ->select();
		//生成一个可以校验是否已经归零了的账号、产品数组 [account_id]_[product_id]
		$checkIsResetArray = [];
		array_walk($data, function ($item) use (&$checkIsResetArray, $customer_id) {
			$checkValue = $item['account_id'] . '_' . $item['product_id'];
			if (in_array($checkValue, $checkIsResetArray)) {
				return true;
			}
			$checkIsResetArray[] = $checkValue;
			$this->resetFeeConfigForEveryData($customer_id, $item['account_id'], $item['product_id']);
			
			return true;
		});
	}
	
	/**
	 * 归零计费配置（针对某账号下的所有开通的产品）
	 *
	 * @access protected
	 *
	 * @param $customer_id string 客户ID
	 * @param $account_id  string 账号ID
	 *
	 * @return void
	 **/
	protected function resetFeeConfigByAccountId($customer_id, $account_id)
	{
		$start_date = ['gt', date('Y-m-d')];
		$is_delete  = 0;
		//删除明天及明天之后的该客户的所有计费配置
		$this->feeConfig_model->where(compact('account_id', 'start_date'))
							  ->save([
								  'is_delete' => 1,
								  'admin'     => $this->getAdmin(),
							  ]);
		//查询所有存在计费配置的账号、产品数据
		$data = $this->feeConfig_model->field('product_id')
									  ->where(compact('account_id', 'is_delete'))
									  ->select();
		//生成一个可以校验是否已经归零了的账号、产品数组 [product_id]
		$checkIsResetArray = [];
		array_walk($data, function ($item) use (&$checkIsResetArray, $customer_id, $account_id) {
			$checkValue = $item['product_id'];
			if (in_array($checkValue, $checkIsResetArray)) {
				return true;
			}
			$checkIsResetArray[] = $checkValue;
			$this->resetFeeConfigForEveryData($customer_id, $account_id, $item['product_id']);
			
			return true;
		});
	}
	
	/**
	 * 归零某条计费配置
	 *
	 * @access protected
	 *
	 * @param $customer_id string 客户ID
	 * @param $account_id  string 账号ID
	 * @param $product_id  string 产品ID
	 * @param $start_date  string 归零配置的计费开始时间
	 *
	 * @return void
	 **/
	protected function resetFeeConfigForEveryData($customer_id, $account_id, $product_id, $start_date = null)
	{
		$start_date = empty($start_date) ? date('Y-m-d', strtotime('+1 days')) : $start_date;
		$data       = [
			'customer_id'     => $customer_id,
			'account_id'      => $account_id,
			'product_id'      => $product_id,
			'fee_basis'       => 1,
			'fee_method'      => 2,
			'fee_time_rule'   => 0,
			'fee_amount_rule' => 1,
			'fee_step_rule'   => 0,
			'fee_price_rule'  => 0,
			'fee_price'       => 0,
			'start_date'      => $start_date,
			'remarks'         => '系统归零设置',
			'create_time'     => time(),
			'admin'           => $this->getAdmin(),
			'is_reset'        => 1,
		];
		$this->feeConfig_model->add($data);
	}
	
	/**
	 * 是否为归零计费配置
	 *
	 * @access protected
	 *
	 * @param $data array 计费配置数据
	 *
	 * @return boolean
	 **/
	protected function isResetConfig($data)
	{
		return $data['is_reset'] == 1;
	}
	
	/**
	 * 获取操作人
	 *
	 * @access protected
	 *
	 * @return string
	 **/
	protected function getAdmin()
	{
        //审批通过使用的用户名称
        $approval_token = I('post.approval_token');
        if($approval_token){
            $mod = new Approval();
            return $mod->getApplicantById($approval_token);
        }
		return $_SESSION[C('LOGIN_SESSION_NAME')];
	}
}