<?php
namespace Account\Repositories;

use Account\Model\AccountModel;
use Account\Model\ProductModel;
use Common\Controller\DataAuthController;
use Common\Model\FeeConfigModel;

class CustomerFeeRepository extends BaseRepository
{
    /**
     * 获取该客户计费配置列表
     * @param  array $customer 客户信息
     * @return list
     */
    public function getFeeConfig($customer)
    {
        $fee = $this->getFeeConfigByCustomerId($customer);
        $list = $this->getArrayDiff($fee);
        $account_list = $this->getAccountList($customer);
        $product_list = $this->getProductListAll();
        $fee_method = D('FeeConfig')->getFeeMethod();
        $fee_time_rule = D('FeeConfig')->getFeeTimeRule();
        $fee_amount_rule = D('FeeConfig')->getFeeAmountRule();
        $fee_step_rule = D('FeeConfig')->getFeeStepRule();
        $result = [];
        $cuishou_short = [];
        array_walk($fee, function ($item) use (&$result, $account_list, $product_list, $fee_method, $fee_time_rule, $fee_amount_rule, $fee_step_rule, &$cuishou_short) {
            $account_id = $item['account_id'];
            $product_id = $item['product_id'];
            //如果是邦信分快捷版
            if ($product_list[$product_id]['father_id']==210)
            {
                $cuishou_short[] = $item;
                return true;
            }
            //账号名称
            $item['account_name'] = $account_list[$account_id]['account_name'];
            //产品名称
            $item['product_name'] = $product_list[$product_id]['product_name'];
            //计费依据
            $item['fee_basis_name'] = $product_list[$product_id]['fee_config'][$item['fee_basis']]['cn_name'];
            //计费方式
            $item['fee_method_name'] = $fee_method[$item['fee_method']];
            //时间计费规则
            $item['fee_time_rule_name'] = $fee_time_rule[$item['fee_time_rule']];
            //用量计费规则
            $item['fee_amount_rule_name'] = $fee_amount_rule[$item['fee_amount_rule']];
            //计费规则
            $item['fee_rule'] = $item['fee_time_rule']?$item['fee_time_rule_name']:$item['fee_amount_rule_name'];
            //产品
            $item['product_json'] = '';
            //账号
            $item['account_id'] = [$account_id];
            $item['account_json'] = json_encode($item['account_id']);
            $result[] = $item;
            return true;
        });
        //组合催收分析分析快捷版的数据
        $product_data = (new ProductModel())->where(['product_id' => 210])->find();
        $cuishou_short_result = [];
        array_walk($cuishou_short, function ($item) use (&$cuishou_short_result, $account_list, $product_data, $fee_method, $fee_time_rule, $fee_amount_rule, $fee_step_rule) {
            $account_id = $item['account_id'];
            $key = $item['account_id'] . '_' . $item['start_date'];
            if (!isset($cuishou_short_result[$key])) {
                //账号名称
                $item['account_name'] = $account_list[$account_id]['account_name'];
                //产品名称
                $item['product_name'] = $product_data['product_name'];
                //计费依据
                $json=array_column(json_decode($product_data['fee_config'], true), null, 'val');
                $item['fee_basis_name'] = $json[$item['fee_basis']]['cn_name'];
                //计费方式
                $item['fee_method_name'] = $fee_method[$item['fee_method']];
                //时间计费规则
                $item['fee_time_rule_name'] = $fee_time_rule[$item['fee_time_rule']];
                //用量计费规则
                $item['fee_amount_rule_name'] = $fee_amount_rule[$item['fee_amount_rule']];
                //计费规则
                $item['fee_rule'] = $item['fee_time_rule']?$item['fee_time_rule_name']:$item['fee_amount_rule_name'];
                //特出的东西
                $item['product_list'] = [];
                $item['account_id'] = [$account_id];
                $item['product_list'][] = $item['product_id'];
                $item['product_id'] = 210;
                $item['id'] = [$item['id']];
                $cuishou_short_result[$key] = $item;
            } else {
                $cuishou_short_result[$key]['id'][] = $item['id'];
                $cuishou_short_result[$key]['product_list'][] = $item['product_id'];
            }
        });
        return array_merge($cuishou_short_result, $result);
        /*if ($list && is_array($list)) {
            foreach ($list as $key => &$value) {
                $value['product_name'] = $product_list[$value['product_id']]['product_name'];
                $value['fee_basis_cn'] = $product_list[$value['product_id']]['fee_config'][$value['fee_basis']]['cn_name'];
                $value['fee_method_cn'] = $fee_method[$value['fee_method']];
                $rule = $value['fee_step_rule'] ? '/'.$fee_step_rule[$value['fee_step_rule']] : '';
                $value['fee_rule'] = $value['fee_time_rule'] ? $fee_time_rule[$value['fee_time_rule']] : $fee_amount_rule[$value['fee_amount_rule']].$rule;
                if (is_array($value['account_id'])) {
                    array_walk($value['account_id'], function(&$v, $k, $p) {
                        $m['account_id'] = $v;
                        $m['account_name'] = $p[$v]['account_name'];
                        $v = $m;
                    }, $account_list);
                }

                $value['product_json'] = isset($value['product_list']) ? json_encode($value['product_list']) : '';
                $value['account_name'] = implode(' | ', array_column($value['account_id'], 'account_name'));
                $value['account_id'] = array_column($value['account_id'], 'account_id');
                $value['account_json'] = json_encode($value['account_id']);
            }
        }
        return $list;*/
    }

    /**
     * 根据客户ID获取计费配置信息
     * @param  array $customer 客户信息
     * @return array
     */
    public function getFeeConfigByCustomerId($customer)
    {
        $where['customer_id'] = $customer['customer_id'];
        $where['is_delete'] = 0;
        $field = 'id, account_id, product_id, fee_basis, fee_method, fee_time_rule, fee_amount_rule, fee_step_rule, fee_price, start_date,update_time,create_time';
        $fee = D('FeeConfig')->where($where)->field($field)->order('start_date desc')->select();
        return $fee;
    }

    /**
     * 获取产品列表 全部
     * @return list
     */
    public function getProductListAll()
    {
        $list = D('Product')->field('product_id, product_name, fee_config, father_id')->select();
        $list = $list ? array_column($list, null, 'product_id') : [];
        array_walk($list, function(&$v, $k) {
            $fee_config = $v['fee_config'] ? json_decode($v['fee_config'], true) : [];
            $fee_config = $fee_config ? array_column($fee_config, null, 'val') : [];
            $v['fee_config'] = $fee_config;
        });
        return $list;
    }

    /**
     * 获取邦秒验产品
     * @return array
     */
    public function getSubProductList($father_id)
    {
        $list = D('Account/Product')->field('product_id')->where(compact('father_id'))->select();
        return array_column($list, 'product_id');
    }

    /**
     * 将相同的配置合并到一起
     * @param  array $data 计费配置列表
     * @return list
     */
    public function getArrayDiff($data)
    {
        $list = [];
        $info = $data;
        $cuishou_short_list = $this->getSubProductList(210);
        foreach ($data as $key => &$value) {
            $id = $value['account_id'];
            $pro_id = $value['product_id'];
            $fee_price = $value['fee_price'];
            if (in_array($pro_id, $cuishou_short_list)) {
                unset($value['fee_price']);
                $value['product_id'] = 210;
            } else {
                unset($value['account_id']);
            }
            $mm = md5(json_encode($value));
            if (isset($list[$mm]['account_id'])) {
                if (in_array($pro_id, $cuishou_short_list)) {
                    array_push($list[$mm]['product_list'], $pro_id);
                } else {
                    array_push($list[$mm]['account_id'], $id);
                }
            } else {
                $value['account_id'] = [$id];
                if (in_array($pro_id, $cuishou_short_list)) {
                    $value['fee_price'] = $fee_price;
                    $value['product_list'] = [$pro_id];
                }
                $list[$mm] = $value;
            }
        }
        return $list;
    }

    /**
     * 获取账号列表
     * @param  array $customer 客户信息
     * @return list
     */
    public function getAccountList($customer)
    {
        $where['customer_id'] = $customer['customer_id'];
        $where['father_id'] = ['neq', '0'];
        $list = D('Account')->where($where)->field('account_id, account_name')->select();
        $list = $list ? array_column($list, null, 'account_id') : [];
        return $list;
    }

    /**
     * 获取计费配置信息
     * @param  array $customer 客户信息
     * @return list
     */
    public function getFeeConfigList($customer)
    {
        $where['customer_id'] = $customer['customer_id'];
        $account_id = I('get.account_id', '', 'trim');
        if ($account_id) {
            $account_id = json_decode($account_id, true);
            $where['account_id'] = ['not in', $account_id];
        }
        $fee = D('FeeConfig')->where($where)->field('account_id, product_id')->select();
        array_map(function($val) use (&$list) {
            $list[$val['product_id']][] = $val['account_id'];
        }, $fee);
        return $list;
    }

    /**
     * 获取账号产品关联信息
     * @param  array $customer 产品信息
     * @return array
     */
    public function getAccountProduct($customer)
    {
        $account_list = $this->getAccountList($customer);
        if (!$account_list) {
            throw new \Exception('请先创建账号，在进行计费配置');
        }
        $where['account_id'] = ['in', array_keys($account_list)];
        $acc_pro = D('AccountProduct')->where($where)->field('account_id, product_id')->select();
        if (!$acc_pro) {
            throw new \Exception('请先开通产品，在进行计费配置');
        }
        $cuishou_short_list = $this->getSubProductList(210);
        array_map(function($val) use (&$list, $cuishou_short_list) {
            if (in_array($val['product_id'], $cuishou_short_list)) {
                $list[210][$val['account_id']][] = $val['product_id'];
            } else {
                $list[$val['product_id']][] = $val['account_id'];
            }
        }, $acc_pro);
        return compact('account_list', 'list');
    }

    /**
     * 获取未配置的产品账号
     * @param  array $customer 客户信息
     * @return list
     */
    public function getFeeConfigListNo($customer)
    {
        $account = $this->getAccountProduct($customer);
        // $fee = $this->getFeeConfigList($customer);
        $list = [];
        // 获取催收分快捷版子产品
        foreach ($account['list'] as $key => $value) {
            // $account_id = isset($fee[$key]) ? array_values(array_diff($value, $fee[$key])) : $value;
            $account_id = $value;
            if ($key == 210) {
                array_walk($account_id, function($v, $k, $p) use (&$account_list) {
                    if (isset($p[$k])) {
                        $m = $p[$k];
                        $m['product_list'] = isset($p[$k]) ? $v : [];
                        $account_list[$k] = $m;
                    }
                }, $account['account_list']);
                $account_id = $account_list;
            } else {
                array_walk($account_id, function(&$v, $k, $p) {
                    $v = $p[$v];
                }, $account['account_list']);
            }
            if ($account_id) {
                $list[$key] = $account_id;
            }
        }
        return $list;
    }

    /**
     * 获取未配置的产品账号配置信息
     * @param  array $customer 客户信息
     * @return list
     */
    public function getProductList($customer)
    {
        $list = $this->getFeeConfigListNo($customer);
        $product_id = array_keys($list);
        if (!$product_id) {
            throw new \Exception('请先开通产品，在进行计费配置');
        }
        $product_list = $this->getProductListAll();
        array_walk($list, function(&$v, $k, $p) {
            array_walk($v, function(&$vv, $kk, $pp) {
                if ($vv['product_list']) {
                    array_walk($vv['product_list'], function (&$vvv, $kkk, $ppp) {
                        $mm = $ppp[$vvv];
                        $vvv = $mm;
                    }, $pp);
                }
            }, $p);
            $m = $p[$k];
            $m['account_list'] = $v;
            $v = $m;
        }, $product_list);
        $list = array_column($list, null, 'product_id');
        return $list;
    }

    /**
     * 根据客户ID获取配置信息
     * @param  array $customer 客户信息
     * @return info
     */
    public function getFeeConfigInfo($customer)
    {
        $field = 'product_id, fee_basis, fee_method, fee_time_rule, fee_amount_rule, fee_step_rule, fee_price_rule, fee_price, start_date, remarks, id';
        $where['customer_id'] = $customer['customer_id'];
        $where['is_delete'] = 0;
        $product_id = I('get.product_id', '', 'trim');
        $account_id = I('get.account_id', '', 'trim');
        $product_list = I('get.product_list', '', 'trim');
        if ($product_id) {
            $where['product_id'] = $product_id;
        }
        if ($account_id) {
            $account_id = json_decode($account_id, true);
            $where['account_id'] = $account_id[0];
        }
        if ($product_list) {
            $product_list = json_decode($product_list, true);
            $where['product_id'] = ['in', $product_list];
            $list = D('FeeConfig')->where($where)->field($field)->select();
            $fee = [];
            if ($list) {
                array_map(function ($v) use (&$fee) {
                    $product_id = $v['product_id'];
                    $fee_price = json_decode($v['fee_price'], true);
                    if (!isset($fee['product_list'])) {
                        unset($v['product_id']);
                        unset($v['fee_price']);
                        $fee = $v;
                    }
                    $fee['product_list'][$product_id] = $fee_price;
                    $fee['fee_id'][] = $v['id'];
                }, $list);
            }
        } else {
            $fee = D('FeeConfig')->where($where)->field($field)->find();
            if ($fee) {
                unset($fee['product_id']);
                $fee['fee_price'] = json_decode($fee['fee_price'], true);
            }
        }
        return $fee;
    }

    /**
     * 保存计费配置信息
     * @return true/false
     */
    public function saveFeeConfig()
    {
        $customer_id = I('post.customer_id', '', 'trim');
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '');
        $edit = I('post.edit', 0, 'trim'); // 1 编辑 0 添加
        if (!$customer_id) {
            throw new \Exception('该客户信息不正确');
        }
        if (!$product_id) {
            throw new \Exception('请选择产品');
        }
        if (!$account_id || !is_array($account_id)) {
            throw new \Exception('至少选择一个账号');
        }
        $product_type = I('post.product_type', '');
        $param = $this->getFeeConfigParam();
        if ($product_type == 'cuishou_short') {
            unset($param['fee_price']);
            $product_id = I('post.fee_price', '');
            if (!$product_id || !is_array($product_id)) {
                throw new \Exception('邦信分快捷版计费配置参数错误');
            }
            $param['account_id'] = $account_id[0];
            $param['customer_id'] = $customer_id;
            $data = $product_id;
            array_walk($data, function(&$v, $k, $p) {
                $m['fee_price'] = is_array($v)?json_encode($v):$v;
                $m['product_id'] = $k;
                $m['create_time'] = time();
                $v = array_merge($m, $p);
            }, $param);
            $data = array_values($data);
            $product_id = ['in', array_keys($product_id)];
        } else {
            $param = array_merge($param, compact('product_id', 'customer_id'));
            $data = $account_id;
            array_walk($data, function(&$v, $k, $p) {
                $account_id = $v;
                $v = array_merge($p, compact('account_id'));
                $v['create_time'] = time();
            }, $param);
        }
        $account_id = ['in', $account_id];
        if ($edit) {
            $fee_model = new FeeConfigModel();
            $f_id = I('post.f_id', '', 'trim');
            $f_id = explode(',', $f_id);
            $i = 0;
            foreach ($f_id as $fee_id) {
                $data_i = $data[$i];
                $old_data = $fee_model->find($fee_id);
                /*if (strtotime($old_data['start_date'])<strtotime(date('Y-m'))) {
                    throw new \Exception('该条数据不可编辑');
                }*/

                /*if (strtotime($data_i['start_date'])<strtotime(date('Y-m'))) {
                    throw new \Exception('不可修改为本月之前的计费配置');
                }*/
                $new_data['fee_price'] = $data_i['fee_price'];
                $new_data['remarks'] = $data_i['remarks'];
                $new_data['start_date'] = $data_i['start_date'];
                $new_data['update_time'] = time();
                $new_data['id'] = $fee_id;
                $res = $fee_model->save($new_data);
                if (!$res) {
                    throw new \Exception('数据保存中出现错误');
                }
                $i++;
            }
            return true;
        } else {
            /*if (I('post.start_date', 0, 'strtotime') < strtotime(date('Y-m'))) {
                throw new \Exception('不可增加本月之前的计费配置');
            }*/
            //获取参与此次交互的账号数据
            $account_data = (new AccountModel())->where(compact('account_id'))->select();
            $account_data = array_column($account_data, 'account_name', 'account_id');
            //校验唯一性与每月最多三条计费限制
            array_walk($data, function ($items) use ($account_data) {
                //增加计费配置的校验
                $this->addValid($items, $account_data);
            });
            $res = (new FeeConfigModel())->addAll($data);
            return $res;
        }



        halt($data);

        $list = $this->getFeeConfigByTime(compact('customer_id', 'product_id', 'account_id'));

        if (!$list) { // 全部添加
            // 三次保存机会
            $num = D('FeeConfig')->field('account_id, product_id, count(*) as num')->where(compact('customer_id', 'product_id', 'account_id'))->group('customer_id,product_id,account_id')->select();
            $num = $num ? array_multisort(array_column($num, 'num'), SORT_DESC, $num) : '';
            if (isset($num[0]) && $num[0]['num'] > 3) {
                throw new \Exception($num[0]['account_id'].'账号'.$num[0]['product_id'].'产品本月计费配置已达上限，无法保存');
            }
            $is_delete = 0;
            D('FeeConfig')->where(compact('customer_id', 'product_id', 'account_id', 'is_delete'))->save(['is_delete' => 1]);
            return D('FeeConfig')->addAll($data);
        }
        // 对于当天和未计费开始前进行更新
        return $this->updateFeeConfig($data, $list);
    }
    /**
     * 增加计费配置的校验
     *
     * @access protected
     * @param $data array 增加的数据
     * @param $account array 账号的数据
     *
     * @return void
     **/
    protected function addValid($data, $account)
    {
        $start_date = $data['start_date'];
        $account_id = $data['account_id'];
        $product_id = $data['product_id'];
        $is_delete = 0;
        $model = new FeeConfigModel();
        //唯一性的校验
        $count = $model->where(compact('start_date', 'account_id', 'product_id', 'is_delete'))->count();
        if ($count) {
            throw new \Exception('账号【' . $account[$account_id] . '】在此产品中已存在【' . $start_date . '】的计费配置！');
        }
        //当月不得超过3条计费依据
        $start_month = date('Y-m', strtotime($start_date));
        $start_date = ['like', $start_month . '-%'];
        $count = $model->where(compact('start_date', 'account_id', 'product_id', 'is_delete'))->count();
        if ($count>=3) {
            throw new \Exception('账号【' . $account[$account_id] . '】在此产品中已存在3条【' .$start_month. '内】的计费配置！');
        }
    }
    /**
     * 获取今天的数据
     * @return info
     */
    public function getFeeConfigByTime($param)
    {
        $start_time = strtotime(date('Y-m-d'));
        $end_time = strtotime(date('Y-m-d 23:59:59'));
        $complex['create_time'] = ['between', [$start_time, $end_time]];
        $complex['start_date'] = ['egt' => date('Y-m-d')];
        $complex['_logic'] = 'or';
        $where = array_merge($param, ['is_delete' => 0, '_complex' => $complex]);
        $list = D('FeeConfig')->where($where)->field('id, account_id, product_id')->select();
        $product_list = $list ? array_column($list, null, 'product_id') : [];
        $account_list = $list ? array_column($list, null, 'account_id') : [];
        $product_type = I('post.product_type', '');
        $list = $product_type == 'cuishou_short' ? $product_list : $account_list;
        return $list;
    }

    /**
     * 更新数据
     * @param  array $data 更新数据
     * @param  array $list 需要更新的ID
     * @return true
     */
    public function updateFeeConfig($data, $list)
    {

        array_walk($data, function(&$v, $k, $p) {
            $product_type = I('post.product_type', '');
            $key = $product_type == 'cuishou_short' ? $v['product_id'] : $v['account_id'];
            if (isset($p[$key])) {
                $v['id'] = $p[$key]['id'];
                D('FeeConfig')->save($v);
            } else { // 不存在
                $customer_id = $v['customer_id'];
                $product_id = $v['product_id'];
                $account_id = $v['account_id'];
                $is_delete = 0;
                D('FeeConfig')->where(compact('customer_id', 'product_id', 'account_id', 'is_delete'))->save(['is_delete' => 1]);
                D('FeeConfig')->add($v);
            }
        }, $list);
        return true;
    }

    /**
     * 设置计费配置基本信息
     * @return param
     */
    public function getFeeConfigParam()
    {
        $fee_basis = I('post.fee_basis', 0, 'intval');
        $fee_method = I('post.fee_method', 0, 'intval');
        $start_date = I('post.start_date', '', 'trim');
        $remarks = I('post.remarks', '', 'trim,strip_tags,stripslashes');
        if (!$fee_basis) {
            throw new \Exception('请选择计费依据');
        }
        if (!$fee_method) {
            throw new \Exception('请选择计费方式');
        }
        if (!$start_date) {
            throw new \Exception('请选择正式计费开始时间');
        }
        if ($remarks && strlen($remarks) > 1000) {
            throw new \Exception('备注不可超过1000个字符');
        }
        if ($fee_method == 1) { //按时间
            $fee_rule = $this->getFeeTimeParam();
        } elseif ($fee_method == 2) { //按用量
            $fee_rule = $this->getFeeAmountParam();
        }
        $base = compact('customer_id', 'product_id', 'fee_basis', 'fee_method', 'start_date', 'remarks');
        return array_merge($base, $fee_rule);
    }

    /**
     * 获取时间计费配置信息
     * @return array
     */
    public function getFeeTimeParam()
    {
        $fee_time_rule = I('post.fee_time_rule', 0, 'trim');
        $fee_price = I('post.fee_price', '');
        if (!$fee_time_rule) {
            throw new \Exception('请选择时间计费规则');
        }
        if ($fee_price < 0) {
            throw new \Exception('请选择时间计费价格');
        }
        $fee_price_rule = $this->getFeePriceRuleParam();
        $fee_amount_rule = 0;
        $fee_step_rule = 0;
        if ($fee_price_rule == 2) {
            $fee_price = json_encode($fee_price, JSON_UNESCAPED_SLASHES);
        }
        return compact('fee_time_rule', 'fee_price', 'fee_amount_rule', 'fee_step_rule', 'fee_price_rule');
    }

    /**
     * 价格规则
     * @return array
     */
    public function getFeePriceRuleParam()
    {
        $fee_price_rule = I('post.fee_price_rule', 0, 'trim');
        $product_type = I('post.product_type', '', 'trim');
        if (!$fee_price_rule && in_array($product_type, ['data_check', 'cuishou_short'])) {
            throw new \Exception('请选择价格规则');
        }
        return $fee_price_rule;
    }

    /**
     * 获取用量计费信息
     * @return array
     */
    public function getFeeAmountParam()
    {
        $fee_amount_rule = I('post.fee_amount_rule', 0, 'trim');
        if (!$fee_amount_rule) {
            throw new \Exception('请选择用量计费规则');
        }

        if ($fee_amount_rule == 1) {
            $fee_rule = $this->getFeeFixedParam();
        } elseif (in_array($fee_amount_rule, [2, 3])) {
            $fee_rule = $this->getFeeStepParam();
        }
        $fee_time_rule = 0;
        return array_merge(compact('fee_amount_rule', 'fee_time_rule'), $fee_rule);
    }

    /**
     * 固定价格
     * @return array
     */
    public function getFeeFixedParam()
    {
        $fee_price = I('post.fee_price', '');
        if ($fee_price < 0) {
            throw new \Exception('请输入正确的固定价格');
        }
        $fee_step_rule = 0;
        $fee_price_rule = $this->getFeePriceRuleParam();
        if ($fee_price_rule == 2) {
            $fee_price = json_encode($fee_price, JSON_UNESCAPED_SLASHES);
        }
        return compact('fee_price', 'fee_step_rule', 'fee_price_rule');
    }

    /**
     * 阶梯周期
     * @return array
     */
    public function getFeeStepParam()
    {
        $fee_step_rule = I('post.fee_step_rule', 0, 'intval');
        if (!$fee_step_rule) {
            throw new \Exception('请选择阶梯周期');
        }
        $fee_price = I('post.fee_price', '');
        if (!$fee_price) {
            throw new \Exception('请选择区间价格');
        }
        $fee_price_rule = $this->getFeePriceRuleParam();
        $fee_price = json_encode($fee_price, JSON_UNESCAPED_SLASHES);
        return compact('fee_step_rule', 'fee_price', 'fee_price_rule');
    }

    /**
     * 删除计费配置
     * @return array
     */
    public function delFeeConfig()
    {
        $customer_id = I('post.customer_id', '', 'trim');
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '');
        $product_list = I('post.product_list', '');

        if (!$customer_id || !$product_id || !$account_id) {
            throw new \Exception('参数错误');
        }

        $account_id = ['in', $account_id];
        if ($product_list) {
            $product_id = ['in', $product_list];
        }
        return D('FeeConfig')->where(compact('customer_id', 'product_id', 'account_id'))->save(['is_delete' => 1]);
    }

    public function getAccountAll()
    {
        $list = D('Account')->field('account_id, account_name')->select();
        return array_column($list, null, 'account_id');
    }

    public function getCustomerFeeConfigList()
    {
        $product_id = I('get.product_name', '', 'trim');
        $customer_id = I('get.customer_id', '', 'trim');
        $field = 'customer_id, account_id, product_id, fee_basis, fee_method, fee_time_rule, fee_amount_rule, fee_step_rule, fee_price, start_date,remarks';
        $where = array_filter(compact('product_id', 'customer_id'));
        $where['is_delete'] = 0;
        $fee_config = D('FeeConfig')->field($field)->where($where)->order('customer_id desc')->select();
        return $fee_config;
    }

    public function getCustomerList()
    {
        $where = $this->getCustomerListParam();
        $customer = D('Account/Customer')
            ->where($where)
            ->where(DataAuthController::instance()->getCustomerWhere())
            ->field('customer_id, name, type')
            ->select();
        if (!$customer) {
            return [];
        }
        return array_column($customer, null, 'customer_id');
    }

    public function getCustomerListParam()
    {
        $param = I('get.');
        if ($param['company']) {
            $param['company'] = ['like', '%'.$param['company'].'%'];
        }
        if ($param['status'] == -1) {
            unset($param['status']);
        }
        unset($param['product_name']);

        if ($param['contract_status']) {
            $param['contract_status'] = ['in', $param['contract_status']];
        }

        return array_filter($param);
    }

    public function getCustomerFeeConfigData()
    {
        $account_list = $this->getAccountAll();
        $product_list = $this->getProductListAll();
        $customer_list = $this->getCustomerList();
        $fee_method = D('FeeConfig')->getFeeMethod();
        $fee_time_rule = D('FeeConfig')->getFeeTimeRule();
        $fee_amount_rule = D('FeeConfig')->getFeeAmountRule();
        $fee_config = $this->getCustomerFeeConfigList();
        $fee_config = $this->getArrayDiff($fee_config);
        $list = [];
        if ($fee_config && is_array($fee_config)) {
            foreach ($fee_config as $key => $value) {
                if (isset($customer_list[$value['customer_id']])) {
                    $temp = $customer_list[$value['customer_id']];
                    $temp['product_name'] = $product_list[$value['product_id']]['product_name'];
                    if (is_array($value['account_id'])) {
                        array_walk($value['account_id'], function (&$v, $k, $p) {
                            $v = $p[$v]['account_name'];
                        }, $account_list);
                    }
                    $temp['account_name'] = implode(' | ', $value['account_id']);
                    $temp['fee_basis'] = $product_list[$value['product_id']]['fee_config'][$value['fee_basis']]['cn_name'];
                    $temp['fee_method'] = $fee_method[$value['fee_method']];
                    $temp['fee_rule'] = $value['fee_time_rule'] ? $fee_time_rule[$value['fee_time_rule']] : $fee_amount_rule[$value['fee_amount_rule']];
                    $temp['start_date'] = $value['start_date'];
                    $temp['fee_price'] = $this->getCustomerFeeConfigPrice($value, $product_list);
                    $temp['remarks'] = $value['remarks'];
                    $list[] = $temp;
                }
            }
        }
        return $list;
    }

    public function getCustomerFeeConfigPrice($fee_config, $product_list)
    {
        $fee_price = $fee_config['fee_price'];
        $fee_amount_rule = $fee_config['fee_amount_rule'];
        if (in_array($fee_amount_rule, [2, 3])) {
            $fee_price = json_decode($fee_price);
            foreach ($fee_price as $key => $value) {
                $kk = $fee_amount_rule == 2 ? '('.$value[0].' ~ '.$value[1].')' : '('.$value[0].')';
                unset($value[0]);
                if ($fee_amount_rule == 2) {
                    unset($value[1]);
                }
                $list[] = '['.$kk.'：'.json_encode(array_values($value)).']';
            }
            $fee_price = '{'.implode('；', $list).'}';
        }
        $fee_price = str_replace(',', '，', $fee_price);
        if (isset($fee_config['product_list'])) {
            $list = [];
            array_walk($fee_config['product_list'], function ($v, $k) use (&$list, $product_list, $fee_price) {
                $list[] = $product_list[$v]['product_name'].'：'.$fee_price;
            }, $product_list);
            $fee_price = implode(" | ", $list);
            return $fee_price;
        }
        return $fee_price;
    }

    public function getCustomerFeeConfigDownload()
    {
        $list = $this->getCustomerFeeConfigData();
        $file_name = RUNTIME_PATH . 'Cache/'.date('Ymd').'.csv';
        // gen file
        $title_list = '客户ID,客户名称,公司类型,产品名称,计费账号,计费依据,计费方式,计费规则,价格（元）,计费开始时间,备注';
        $title_list = mb_convert_encoding($title_list,'GBK','UTF-8');
        file_put_contents($file_name, $title_list);
        $companyTypeData = (new CompanyTypeRepository())->getTwiceTypeData();
        foreach ($list as $value) {
            // 数据补全
            $file_str = $value['customer_id'].','.$value['name'].','. $companyTypeData[$value['type']] .','.$value['product_name'].','.$value['account_name'].','.$value['fee_basis'].','.$value['fee_method'].','.$value['fee_rule'].','.$value['fee_price'].','.$value['start_date'] . ',' . $value['remarks'];

            $file_str = mb_convert_encoding($file_str,'GBK','UTF-8');
            file_put_contents($file_name, PHP_EOL . $file_str, FILE_APPEND);
        }
        $this->downloadTempFile($file_name);
    }


    /**
     * 为客户列表导出生成一个临时文件（为fileDownLoad插件铺垫）
     * <AUTHOR>
     * @datetime 17:27 2018/12/6
     *
     * @access public
     * @param $data array 数据
     * @param $filePath string 文件地址
     *
     * @return boolean 保证导出数据唯一性（文件不能同时操作）
     **/
    public function makeTempFile($data, $filePath)
    {
        $lock = $filePath . '.lock';
        if (file_exists($filePath . '.lock')) {
            return false;
        }
        file_put_contents($lock, time());
        $title_list = '客户ID,客户名称,公司名称,邮箱,状态,已开通账号,运营跟进人,商务跟进人,创建时间,操作人';
        $title_list = mb_convert_encoding($title_list, 'GBK', 'UTF-8');
        file_put_contents($filePath, $title_list);
        foreach ($data as $item) {
            // 数据补全
            $file_str = '"' . $item['customer_id'] . '","' . $item['name'] . '","' . $item['company'] . '","' . $item['email'] . '","'
                . ($item['status']?'可用':'禁用') . '","' . implode(' ', array_column($item['account_list'], 'account_name')) . '","' .
                $item['operator'] . '","' . $item['salesman'] . '","' . date('Y-m-d', $item['create_at']) . '","' . $item['admin'] . '"';
            $file_str = mb_convert_encoding($file_str, 'GBK', 'UTF-8');
            file_put_contents($filePath, PHP_EOL . $file_str, FILE_APPEND);
        }
        @unlink($lock);
        return true;
    }
    /**
     * 为fileDownload提供下载的文件
     * <AUTHOR>
     * @datetime 19:17 2018/12/6
     *
     * @access public
     * @param $filePath string 文件地址
     *
     * @return string
     **/
    public function downloadTempFile($filePath)
    {
        if (!file_exists($filePath)) {
            return false;
        }
        // file download
        $fileSize = filesize($filePath);
        //ob_end_clean();
        // set headers
        header('Content-Description: File Transfer');
        header("Content-type: application/octet-stream");
        header('Content-Transfer-Encoding: binary');
        header("Accept-Ranges: bytes");
        header("Accept-Length:" . $fileSize);
        header("Content-Disposition: attachment; filename=" . basename($filePath));
        header('Set-Cookie: fileDownload=true; path=/');
        // read file
        $file = new \SplFileObject($filePath, 'r');
        echo $file->fread($fileSize);
        @unlink($filePath);
    }
}