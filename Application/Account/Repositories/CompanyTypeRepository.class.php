<?php

namespace Account\Repositories;

use Account\Model\CompanytypeModel;
use Common\Common\ResponseTrait;

class CompanyTypeRepository
{
    use ResponseTrait;
    protected $model;
    public function __construct()
    {
        $this->model = new CompanytypeModel();
    }
    /**
     * 添加、编辑客户时监听是否为切换一级公司类型，进行二级联动
     *
     * @access public
     *
     * @return void
     **/
    public function listenCutFirstType()
    {
        $type = I('post.require_type', '', 'trim');
        if ($type=='cut_company_type') {
            $first_type_id = I('post.first_type_id', '', 'trim');
            if (!empty($first_type_id)) {
                $data = $this->model->where([
                    'parent_id' => $first_type_id,
                    'is_delete' => 0
                ])->field('id,name')->select();
                $option = makeOption(array_column($data, 'name', 'id'));
                $this->response([
                    'status'    => 0,
                    'data'      => [
                        'option'    => $option
                    ]
                ]);
            }
        }
    }
    /**
     * 添加、编译客户时获取一级类型、二级类型的option
     *
     * @access public
     * @param $default string 默认的二级类型
     *
     * @return array
     **/
    public function getCompanyTypeOption($default = null)
    {
        //获取所有的公司类型数据
        $data = $this->model->field('id,name,parent_id')->where(['is_delete' => 0])->select();
        //遍历所有数据、进行一级、二级的公司类型分组
        $first_type_data = [];
        $twice_type_data = [];
        $first_default = null;
        array_walk($data, function ($item) use (&$first_type_data, &$twice_type_data, &$first_default, $default) {
            $parent_id = $item['parent_id'];
            if ($default && $item['id']==$default) {
                $first_default = $parent_id;
            }
            if ($parent_id==0) {
                $first_type_data[$item['id']] = $item['name'];
            } else {
                $twice_type_data[$parent_id][$item['id']] = $item['name'];
            }
        });
        $first_default = $first_default?$first_default:array_shift(array_keys($first_type_data));
        //一级公司类型
        $first_type_option = makeOption($first_type_data, $first_default);
        //二级公司类型
        $twice_type_option = makeOption($twice_type_data[$first_default], $default);
        return compact('first_type_option', 'twice_type_option');
    }
    /**
     * 首页获取一级类型、二级类型的option
     *
     * @access public
     * @param $first_default string 默认的一级类型
     * @param $twice_default string 默认的二级类型
     *
     * @return array
     **/
    public function getCompanyTypeOptionForIndex($first_default = null, $twice_default = null)
    {
        //获取所有的公司类型数据
        $data = $this->model->field('id,name,parent_id')->where(['is_delete' => 0])->select();
        //遍历所有数据、进行一级、二级的公司类型分组
        $first_type_data = [];
        $twice_type_data = [];
        array_walk($data, function ($item) use (&$first_type_data, &$twice_type_data) {
            $parent_id = $item['parent_id'];
            if ($parent_id==0) {
                $first_type_data[$item['id']] = $item['name'];
            } else {
                $twice_type_data[$parent_id][$item['id']] = $item['name'];
            }
        });
        //一级公司类型
        $first_type_option = makeOption($first_type_data, $first_default);
        //二级公司类型
        $twice_type_option = makeOption($twice_type_data[$first_default], $twice_default);
        return compact('first_type_option', 'twice_type_option');
    }
    /**
     * 获取所有的二级公司类型的数据，并以id与name组合为键值对
     *
     * @access public
     *
     * @return array
     **/
    public function getTwiceTypeData()
    {
        //查询所有一级公司类型
        $first_type_data = $this->model->field('id,name')->where([
            'is_delete' => 0,
            'parent_id' => 0
        ])->select();
        $first_type_data = array_column($first_type_data, 'name', 'id');
        //查询所有二级公司类型
        $twice_type_data = $this->model->field('id,name,parent_id')->where([
            'is_delete' => 0,
            'parent_id' => ['neq', 0]
        ])->select();
        $result = [];
        array_walk($twice_type_data, function ($item) use (&$result, $first_type_data) {
            $id = $item['id'];
            $name = $item['name'];
            $parent_id = $item['parent_id'];
            $result[$id] = $first_type_data[$parent_id] . '--' . $name;
        });
        return $result;
    }
    /**
     * 获取某个一级类型下的所有二级类型
     *
     * @access public
     * @param $first_type_id integer 一级类型ID
     *
     * @return array
     **/
    public function getTwiceDataByFirstId($first_type_id)
    {
        return $this->model->field('id,name')->where([
            'is_delete' => 0,
            'parent_id' => $first_type_id
        ])->select();
    }
}