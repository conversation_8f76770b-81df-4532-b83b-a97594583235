<?php

namespace Account\Repositories;

use Account\Model\CustomerSalesmanHistoryModel;
use DateInterval;
use DatePeriod;
use DateTime;

class CustomerSalesmanHistoryRepository
{
    protected $model;

    public function __construct()
    {
        $this->model = new CustomerSalesmanHistoryModel();
    }


    /**
     * 按月份获取客户商务数据
     *
     *
     * @param        $customer_ids
     * @param        $start_month
     * @param string $end_month
     * @param string $fmort
     *
     * @return array
     */
    public function getListMonthly($customer_ids,$start_month,$end_month = '',$fmort = 'Y-m'){
        $data = $this->model->getList(['customer_id' => ['in', $customer_ids]]);
        $today = date("Ym28");
        $res  = [];
        foreach($data as $info){
            $start_day = date("Ym01",strtotime($info['start_day']));
            $end_day   = empty($info['end_day']) ? $today : date("Ym28",strtotime($info['end_day']));
            $months = $this->getMonthList($start_day,$end_day,$fmort);
            foreach($months as $month){
                if($month < $start_month || ($end_month != '' && $month > $end_month)) {
                    continue;
                }
                $res[$info['customer_id']][$month] = $info['salesman'];
            }
        }
        return $res;
    }


    /**
     * 获取两个日期之间的所有月份
     * @param $start_day
     * @param $end_day
     * @param $fmort
     * @return array
     */
    private function getMonthList($start_day,$end_day,$fmort){
        //获取间隔时间相隔的月数
        $months = [];
        try {
            $start = new DateTime($start_day);
            $end   = new DateTime($end_day);
            // 时间间距 这里设置的是一个月
            $interval = DateInterval::createFromDateString('1 month');
            $period   = new DatePeriod($start, $interval, $end);
            $months = [];
            foreach ($period as $dt) {
                $months[] = $dt->format($fmort);
            }
        } catch (\Exception $e) {
        }
        return $months;
    }
}