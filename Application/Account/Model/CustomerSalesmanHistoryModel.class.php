<?php

namespace Account\Model;

use Think\Model;

class CustomerSalesmanHistoryModel extends Model
{

    protected $connection  = 'DB_FINANCE';
    protected $tableName   = 'customer_salesman_history';
    protected $tablePrefix = '';


    /**
     * 获取列表
     *
     * @param array $where
     * @param string $field
     *
     * @return mixed
     */
    public static function getList(array $where = [], $field = '*') {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }


    public static function getLastSalesman($customer_id, $field = '*') {
        return (new static())->where(['customer_id' => $customer_id])
            ->where(['end_day' => ''])
            ->field($field)
            ->select();
    }


    public static function getOldCustomerIds($salesman) {
        return (new static())->where(['salesman' => $salesman])
            ->where(['end_day' => ['neq','']])
            ->select();
    }
}
