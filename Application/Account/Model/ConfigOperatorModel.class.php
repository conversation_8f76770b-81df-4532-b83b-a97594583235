<?php

namespace Account\Model;

use Account\Repositories\FeeConfigRepository;
use Common\Controller\DataAuthController;
use Think\Model;
use Common\ORG\Page;
use Account\Model\CustomerModel;
use Common\Controller\ChangeNoticeController;

class ConfigOperatorModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'config_operator';
    protected $tablePrefix = '';


    public static function getOneItem(array $where, $field='*')
    {
        return (new static())->where($where)
            ->field($field)
            ->find();
    }

    public static function getListConfig(array $where = [], $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }




}