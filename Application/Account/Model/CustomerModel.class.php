<?php

namespace Account\Model;

use Account\Repositories\CompanyTypeRepository;
use Account\Repositories\FeeConfigRepository;
use Common\Common\ResponseTrait;
use Common\Controller\DataAuthController;
use Common\Model\CommonEnumModel;
use PreSales\Repositories\PreSalesCustomerRepository;
use Think\Model;
use Common\ORG\Page;
use Account\Model\AccountModel;

class CustomerModel extends Model
{
    use ResponseTrait;

    protected $connection = 'DB_FINANCE';
    protected $tableName = 'customer';
    protected $tablePrefix = '';

    /**
     * 用户类型
     *
     * @var int[]
     */
    public static $customerType = [
        '金融用户' => '1',
        '企服用户' => '2',
    ];

    /**
     * @var string[] 付款方式
     */
    public static $paymentType = [
        '预付款客户' => '1',
        '后付款客户' => '2',
    ];

    /**
     * @var string[] 对账周期
     */
    public static $reconciliationCycle = [
        '月度' => '1',
        '季度' => '2',
        '年度' => '3',
    ];

    /**
     * @var string[] 征信客户分类
     */
    public static $signType = [
        '羽乐科技签约' => '0',
        '朴道签约'  => '1',
        '浙数交签约'  => '10',
        '郑数交签约'  => '2',
        '钱塘签约'  => '3',
        '中电签约'  => '4',
    ];

    /**
     * 获取列表
     *
     * @param array $where
     * @param string $field
     *
     * @return mixed
     */
    public static function getListCustomer(array $where = [], $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * 创建客户
     */
    public function createCustomer()
    {
        // 检查参数
        $this->checkParamsByCreateForPost();

        // 获取参数
        $params = $this->getParamForCreate();
        $id = $this->add($params);

//        if ($id) {
//            //默认开通用户账户
//            $account_model = new AccountModel();
//            $account = [];
//            $account['customer_id'] = $params['customer_id'];
//            $account['account_name'] = $params['name'] . '客户账号';
//            $account['account_id'] = $this->createUUid('FA');
//            $account['cid'] = substr(md5($account['account_id']), 0, 8);
//            $account['father_id'] = 0;
////            $account['email'] = $params['email'];
////            $account['password'] = $this->genPasswordForCreate();
//            $account['apikey'] = $params['apikey'];
//            $account['appsecret'] = $params['appsecret'];
//            $account['end_time'] = 0; //$params['end_time'];
//            $account['type'] = 1;
//            $account['create_at'] = time();
//            $account['update_at'] = time();
//            $account_model->createAccount($account);
//        }

        //添加账单特殊消耗
        //$this->addExpend($params['customer_id']);


        //关联线下测试客户
        $pre_sale_customer_id = I('post.pre_sale_customer_id', '', 'trim');
        (new PreSalesCustomerRepository())->setSignCustomerId($params['customer_id'], $pre_sale_customer_id);

        if ($id) {
            $now = date("Y-m-d H:i:s");

            //创建客户-商务记录
            $data = [
                'customer_id' => $params['customer_id'],
                'salesman'    => $params['salesman'],
                'start_day'   => date("Ym01"),
                'created_at'  => $now,
                'updated_at'  => $now,
            ];
            (new CustomerSalesmanHistoryModel())->add($data);

            //给予默认开票模式 后付费:1 预付费:5
            $source_map = (new CommonEnumModel())->getEnumPairs(1);
            foreach($source_map as $source => $source_name) {
                $payment_type = $params['payment_type'];
                $data         = [
                    'customer_id' => $params['customer_id'],
                    'source'      => $source,
                    'model'       => $payment_type == 2 ? 1 : 5,//payment_type == 1 为预付费 2为后付费
                    'created_at'  => $now,
                    'updated_at'  => $now,
                ];
                (new CustomerInvoiceConfigModel())->add($data);
            }
        }

        return $id;
    }

    /**
     * 添加账单特殊消耗
     *
     * @access protected
     *
     * @param $customer_id string 客户ID
     *
     * @return void
     **/
    protected function addExpend($customer_id)
    {
        $expend = I('post.expend');
        if (empty($expend)) {
            return;
        }
        $productInfo = (new ProductModel())->field(['product_id', 'product_name'])
            ->select();
        $productInfo = array_column($productInfo, 'product_name', 'product_id');
        $expend = array_map(function ($item) use ($customer_id, $productInfo) {
            return [
                'start_date' => str_replace('-', '', $item['start_date']),
                'name' => $item['name'],
                'money' => $item['money'],
                'type' => $item['type'],
                'profile_show_date' => $item['profile_show_date'],
                'product_id' => $item['product_id'],
                'remark' => $productInfo[$item['product_id']],
                'customer_id' => $customer_id,
                'fee_number' => empty($item['fee_number']) ? 0 : $item['fee_number'],
                'create_time' => time(),
                'update_time' => time(),
            ];
        }, array_values($expend));

        $customer_expend_model = new CustomerExpendModel();
        $customer_expend_model->addAll($expend);
    }

    /**
     * 为新建的客户生成密码
     * @return string
     */
    public function genPasswordForCreate()
    {
        return md5(C('USER_PWD_SALT') . md5(C('USER_DEFAULT_PASSWORD')));
    }

    /**
     * 检查新建客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByCreateForPost()
    {
//        $email = I('post.email', '', 'trim');
        $name = I('post.name', '', 'trim');
//        $apikey = I('post.apikey', '', 'trim');
//        $end_time = I('post.end_time', '', 'trim');

//        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
//            throw new \Exception('请检查邮箱的格式');
//        }
        if (!$name) {
            throw new \Exception('客户名称未填写');
        }
        // name email的唯一性
        $name_unique = $this->where(compact('name'))
            ->count();
        if ($name_unique) {
            throw new \Exception('客户名称已经被占用，请更换客户名称');
        }
        // email 的唯一性
//        $account_model = new AccountModel();
//        $email_unique = $account_model->where(compact('email'))
//            ->count();
//        if ($email_unique) {
//            throw new \Exception('邮箱已经被占用,请更换邮箱');
//        }
        //主体
        $group_id = I('post.group_id', '', 'trim');
        // if (empty($group_id)) {
        //     throw new \Exception('请选择主体');
        // }
        //运营跟进人
        $operator = I('post.operator', '', 'trim');
        if (empty($operator)) {
            throw new \Exception('请选择运营跟进人');
        }
        //商务跟进人
        $salesman = I('post.salesman', '', 'trim');
        if (empty($salesman)) {
            throw new \Exception('请选择商务跟进人');
        }
        // apikey的唯一性
        //        $apikey_unique = $account_model->where(compact('apikey'))->count();
        //        if ($apikey_unique){
        //            throw  new \Exception('apikey已经被占用，请更换apikey');
        //        }

//        $expend = I('post.expend');
//        if (!empty($expend)) {
//            $expend = array_values($expend);
//            $current_month_time = strtotime(date('Y-m-') . '01');
//            foreach ($expend as $item) {
//                $datetime = strtotime($item['date'] . '-01');
//                if ($datetime < $current_month_time && !C('ALLOW_HISTORY_FEE_CONFIG')) {
//                    throw new \Exception('不可增加本月之前的账单特殊消耗数据【' . $item['date'] . '】');
//                }
//            }
//        }

        //end time
//        if (empty($end_time)) {
//            throw  new \Exception('截止日期未填写');
//        }

    }

    /**
     * 为创建用户过滤参数
     */
    protected function getParamForCreate()
    {
        $name = I('post.name', '', 'trim');
        $companys = I('post.company', '', 'trim');
        $status = I('post.status', '', 'trim');
        $type = I('post.type', '', 'trim');
        $c_type = I('post.c_type', '1', 'trim');
        $customer_type = I('post.customer_type', '1', 'trim');
        $operator = I('post.operator', '', 'trim');
        $salesman = I('post.salesman', '', 'trim');
        $introduce_salesman = I('post.introduce_salesman', '', 'trim');
        $channel_follower = I('post.channel_follower', '', 'trim');// 渠道跟进人
        $source_id =implode(',',I('post.source_id', '', 'trim'));// 渠道跟进人可见征信机构
        $channel_mode = I('post.channel_mode', '', 'trim');//客户类型 是否为渠道客户
        $sign_type =implode(',',I('post.sign_type', '', 'trim'));//征信客户分类： 0: 羽乐科技签约, 10: 朴道签约 逗号分隔
        $group_id = I('post.group_id', '', 'trim');//主体id
        $reconciliation_cycle = I('post.reconciliation_cycle', '', 'trim');//对账方式 对账周期
        $level = I('post.level', '', 'trim');//主体id

        //对公司名称进行拆分:
        //1、一个为company字段 兼容历史数据以及已经在其他地方使用该字段内容的情况，它表示和羽乐科技签约的名称
        //2、一个为agent_company字段 新增该字段为代理机构对应的签约公司名称，为json格式，可存放多个代理机构对应的公司名称
        $company = $companys[0];
        unset($companys[0]);
        $agent_company = json_encode($companys, JSON_UNESCAPED_UNICODE);

//        $apikey = I('post.apikey', '', 'trim');
        $payment_type = I('post.payment_type', '', 'trim');
//        $appsecret = I('post.appsecret', '', 'trim');
//        $end_time = I('post.end_time', '2099-12-31', 'trim');
//        $end_time = intval(strtotime($end_time)) + 86399;

        //新增加的字段 账单收件邮箱&&预警余额&&计算充值日期
        $bill_email = I('post.bill_email', '', 'trim');
        if (!empty($bill_email)) {
            $bill_email_arr = explode(PHP_EOL, $bill_email);
            if (count($bill_email_arr) > 8) {
                throw new \Exception("账单收件人最多只允许设置8个邮箱");
            }
            $bill_email_arr = array_map(function ($bill_email) {
                $bill_email = trim($bill_email);
                if (!filter_var($bill_email, FILTER_VALIDATE_EMAIL)) {
                    throw new \Exception("{$bill_email}不是标准的邮箱格式");
                }

                return $bill_email;
            }, $bill_email_arr);
            $bill_email = implode(';', $bill_email_arr);
        }
        //账单抄送人
        $fm_email = '<EMAIL>';
        $bill_cc_email = I('post.bill_cc_email', '', 'trim');
        if (!empty($bill_cc_email)) {
            $bill_cc_email_arr = explode(PHP_EOL, $bill_cc_email);
            if (count($bill_cc_email_arr) > 20) {
                throw new \Exception("账单抄送人最多只允许设置8个邮箱");
            }
            $bill_cc_email_arr = array_map(function ($bill_cc_email) {
                $bill_cc_email = trim($bill_cc_email);
                if (!filter_var($bill_cc_email, FILTER_VALIDATE_EMAIL)) {
                    throw new \Exception("{$bill_cc_email}不是标准的邮箱格式");
                }

                return $bill_cc_email;
            }, $bill_cc_email_arr);
            $bill_cc_email_arr[] = $fm_email;
            $bill_cc_email_arr = array_unique($bill_cc_email_arr);

            $bill_cc_email = implode(';', $bill_cc_email_arr);
        }else {
            $bill_cc_email = $fm_email;
        }

        $balance = I('post.balance', null, 'trim');
        $balance = $balance ? $balance : null;
        $balance_percent = I('post.balance_percent', null, 'trim');
        $balance_percent = $balance_percent ? ($balance_percent / 100) : null;
        $available_days = I('post.available_days', null, 'trim');
        $available_days = $available_days ? $available_days : null;

//        $frequency = I('post.frequency', 1);

        $create_at = $update_at = time();
        $customer_id = $this->createUUid('C');

        $email_type = I('post.email_type', 1);

        return compact('customer_id', 'name', 'company', 'agent_company', 'status', 'update_at', 'create_at', 'type', 'c_type', 'operator', 'salesman','introduce_salesman', 'channel_follower', 'bill_email', 'bill_cc_email', 'balance', 'balance_percent', 'available_days', 'payment_type', 'email_type','customer_type', 'source_id','channel_mode','reconciliation_cycle','sign_type','group_id','level');
    }

    /**
     * 修改产品操作人
     *
     * @param $id
     * @param $admin
     *
     * @return bool|null
     */
    public function updateAdminInfo($id, $admin)
    {
        if (empty($id) || empty($admin)) {
            return null;
        }
        $data['admin'] = $admin;

        return $this->where('id=' . $id)
            ->save($data);
    }

    /**
     * 客户管理（新）列表文件导出
     * @return array
     **<AUTHOR>
     * @datetime 14:29 2018/12/6
     *
     * @access   public
     *
     */
    function getCustomerByGetForFile()
    {
        // 条件
        $where = $this->getCustomerConditionByGet();
        $list_customer = $this->field('*') //, (select email from account where account.customer_id=customer.customer_id and account.father_id=0 limit 0,1) as email
            ->where($where)
            ->where(DataAuthController::instance()
                ->getCustomerWhere())
            ->order('id DESC')
            ->select();

        if (empty($list_customer)) {
            return $list_customer;
        }
        $customer_id = ['in', array_column($list_customer, 'customer_id')];
        $list_customer = array_column($list_customer, null, 'customer_id');

        $accountProductModel = new AccountProductModel();
        $account_list = $accountProductModel->field('customer_id,account_name,product_name')
            ->join('account ON account.account_id=account_product.account_id')
            ->join('product ON product.product_id=account_product.product_id')
            ->where(['account.customer_id' => $customer_id])
            ->select();
        foreach ($account_list as $item) {
            if (isset($list_customer[$item['customer_id']])) {
                $list_customer[$item['customer_id']]['account'][] = $item['account_name'];
                $list_customer[$item['customer_id']]['product'][] = $item['product_name'];
            }
        }

        return $list_customer;
    }

    /**
     * GET方式获取客户列表
     * @return array
     */
    public function getCustomerByGetForPage()
    {
        // 条件
        $where = $this->getCustomerConditionByGet();

        // 分页
        $count_total = $this->where($where)
            ->where(DataAuthController::instance()
                ->getCustomerWhere())
            ->count();
        $obj_page = new Page($count_total, C('LIST_ROWS'));

        // 账户列表
        $list_customer = $this->where($where)
            ->where(DataAuthController::instance()
                ->getCustomerWhere())
            ->limit($obj_page->firstRow, $obj_page->listRows)
            ->order('status DESC, id DESC')
            ->select();
        $list_customer = $this->formatCustomerAccountList($list_customer);
        //获取所有记录列表
        $source_list = (new CommonEnumModel())->getEnumPairs('1');
        foreach ($list_customer as &$item){
            if(empty($item['agent_company'])){
                $agent_company = [];
            }else{
                $agent_company = json_decode($item['agent_company'], true);
            }

            $companys = [];
            foreach ($source_list as $s_id => $source_name){
                if($s_id > 0){
                    $companys[$source_name] =  isset($agent_company[$s_id]) ? $agent_company[$s_id] : '';
                }
            }

            $item['agent_company'] = json_encode($companys, JSON_UNESCAPED_UNICODE);
        }

        return compact('obj_page', 'list_customer');
    }

    /**
     * 查询用户&账号关联信息
     *
     * @param $list_customer
     *
     * @return array
     */
    public function formatCustomerAccountList($list_customer)
    {
        if (empty($list_customer)) {
            return [];
        }
        $list_customer = array_column($list_customer, null, 'customer_id');
        $customer_ids = array_column($list_customer, 'customer_id');
        $where['customer_id'] = ['in', $customer_ids];
        $where['is_delete'] = ['eq', 0];
        $where['type'] = ['in', [0, 1]];

        $account_model = new AccountModel();
        $account_list = $account_model->field('id,status, account_id, account_name, customer_id, email, father_id, apikey')
            ->where($where)
            ->order('STATUS DESC, id asc')
            ->select();
        foreach ($account_list as $item) {
            if (isset($list_customer[$item['customer_id']])) {
                if ($item['father_id'] == '0') {
                    $list_customer[$item['customer_id']]['email'] = $item['email'];
                } else {
                    $list_customer[$item['customer_id']]['account_list'][] = $item;
                }
            }
        }

        return $list_customer;
    }

    /**
     * GET方式获取用户列表客户
     */
    public function customerListForGet()
    {
        return $this->where([
            ['is_delete' => ['neq', 1]],
        ])
            ->field('id,customer_id,name')
            ->where(DataAuthController::instance()
                ->getCustomerWhere())
            ->select();
    }

    /**
     * 列表的基础信息限制
     * @return array
     */
    private function limitBaseParamForList()
    {
        $status = I('get.status', '', 'trim');
        $where['is_delete'] = 0;
        if ($status != -1 && $status !== '') {
            $where['status'] = $status;
        }
        //公司类型
        $first_type = I('get.first_type', '', 'trim');
        $type = I('get.type', '', 'trim');
        if (!empty($type)) {
            $where['type'] = $type;
        } else if (!empty($first_type)) {
            $data = (new CompanyTypeRepository())->getTwiceDataByFirstId($first_type);
            $where['type'] = ['in', array_column($data, 'id')];
        }

        //签约状态
        $contract_status = I('get.contract_status', [1, 2, 3, 4, 5]);
        if (empty($contract_status)) {
            $contract_status = [1, 2, 3, 4, 5];
        }
        $where['contract_status'] = ['in', $contract_status];

        //客户类型
        $customerType = I('customerType', '');
        if ($customerType) {
            $where['customer_type'] = $customerType;
        }
        //付费类型
        $paymentType = I('paymentType', '');
        if ($paymentType) {
            $where['payment_type'] = $paymentType;
        }

        //征信客户分类
        $signType = I('signType', '','intval');
        if (in_array($signType,array_values(self::$signType))) {
            $where['sign_type'] = ['like', '%' . $signType . '%'];
        }

        $group_id = I('group_id', '');
        if ($group_id) {
            $where['group_id'] = $group_id;
        }
        $level_scale_income = trim(I('level_scale_income', ''));
        if ($level_scale_income) {
            if (strlen($level_scale_income) > 1) {
                $where['level_scale_income'] = strtoupper($level_scale_income);
            } else if (is_numeric($level_scale_income)) {
                $where['level_income'] = $level_scale_income;
            } else {
                $where['level_scale'] = strtoupper($level_scale_income);
            }
        }
        return $where;
    }

    /**
     * 限制产品 && 客户选择
     * @return array
     */
    private function limitProductAndCustomerForList()
    {
        $where = [];
        $name = I('get.name', '', 'trim');

        $name && $where['name'] = $name;

        // product_id && customer_id做的限制
        return $this->limitCustomerBetweendProductAndCustomerIdApikeyForList($where);
    }

    private function limitCustomerBetweendProductAndCustomerIdApikeyForList(array $where)
    {
        $product_id = I('get.product_name', '', 'trim');
        $customer_id = I('get.customer_id', '', 'trim');
        $apikey = I('get.apikey', '', 'trim');

        // product_id && 与customer 限制的纠缠的情况
        if (!$product_id && !$customer_id && !$apikey) {
            return $where;
        }

        // 如果只有apikey限制的时候
        if (!$product_id && !$customer_id) {
            $where['customer_id'] = $this->getCustomerByApikey($apikey);

            return $where;
        }

        // 只有product_id && customer_id限制的时候
        if (!$apikey) {
            $where['customer_id'] = [
                'in',
                $this->getCustomerIdLimitByProductIdAndCustomerId($product_id, $customer_id),
            ];

            return $where;
        }

        // product_id && customer_id && apikey 形成的限制
        $list_limit_product = $this->getCustomerIdLimitByProductIdAndCustomerId($product_id, $customer_id);
        $list_limit_apikey = $this->getCustomerByApikey($apikey);
        if (in_array($list_limit_apikey, $list_limit_product)) {
            $where['customer_id'] = $list_limit_apikey;
        } else {
            $where['customer_id'] = ['apikey 和product_id customer_id冲突'];
        }

        return $where;
    }

    private function getCustomerIdLimitByProductIdAndCustomerId($product_id, $customer_id)
    {
        if (!$product_id && $customer_id) {
            return [$customer_id];
        }

        if ($product_id && !$customer_id) {
            return $this->getLimitCustomerByProductId($product_id);
        }

        // 当两个都存在的时候
        $list_need_customers = $this->getLimitCustomerByProductId($product_id);
        if (in_array($customer_id, $list_need_customers)) {
            $list_container_other = [$customer_id];
        } else {
            $list_container_other = ['customer_id和product_id限定的customer冲突'];
        }

        return $list_container_other;
    }

    /**
     * 获取apikey限制获得客户id列表
     *
     * @param string $apikey
     *
     * @return string
     */
    private function getCustomerByApikey($apikey)
    {
        // 账号列表
        $account = $this->getOneAccountByCondition(compact('apikey'), ['customer_id']);
        if (!$account) {
            return '选中的产品没有追踪到实体的账号 apikey:' . $apikey;
        }

        // 客户id
        return $account['customer_id'];
    }

    /**
     * @param array $where
     * @param string $field
     *
     * @return mixed
     */
    private function getOneAccountByCondition(array $where, $field = '*')
    {
        return (new AccountModel())->where($where)
            ->field($field)
            ->find();
    }

    /**
     * 获取product_id对应的客户列表
     *
     * @param array $product_id
     *
     * @return array
     */
    private function getLimitCustomerByProductId($product_id)
    {
        // 账号产品列表
        $list_account_products = $this->getAccountProductListByCondition(compact('product_id'), ['account_id']);
        if (!$list_account_products) {
            return ['选中的产品还没有有开通'];
        }
        $account_id = ['in', array_column($list_account_products, 'account_id')];

        // 账号列表
        $list_accounts = $this->getAccountListByCondition(compact('account_id'), ['customer_id']);
        if (!$list_accounts) {
            return ['选中的产品没有追踪到实体的账号'];
        }

        // 客户id
        return array_unique(array_column($list_accounts, 'customer_id'));
    }

    /**
     * @param array $where
     * @param string $field
     *
     * @return mixed
     */
    private function getAccountListByCondition(array $where, $field = '*')
    {
        return (new AccountModel())->where($where)
            ->field($field)
            ->select();
    }

    /**
     * @param array $where
     * @param string $field
     *
     * @return mixed
     */
    private function getAccountProductListByCondition(array $where, $field = '*')
    {
        return (new AccountProductModel())->where($where)
            ->field($field)
            ->select();
    }

    private function limitParamsBlurForList()
    {
        $where = [];
        $operator = I('get.operator', '', 'trim');
        $salesman = I('get.salesman', '', 'trim');
        $company = I('get.company', '', 'trim');
        $introduce_salesman = I('get.introduce_salesman', '', 'trim');
        $company && $where['company'] = ['like', '%' . $company . '%'];
        $operator && $where['operator'] = ['like', '%' . $operator . '%'];
        $salesman && $where['salesman'] = ['like', '%' . $salesman . '%'];
        $introduce_salesman && $where['introduce_salesman'] = ['like', '%' . $introduce_salesman . '%'];

        return $where;
    }

    /**
     * GET方法获取客户列表的条件
     * @return array
     */
    protected function getCustomerConditionByGet()
    {
        // 常规的限制
        $limit_base = $this->limitBaseParamForList();

        // 客户ID限制
        $limit_customer = $this->limitProductAndCustomerForList();

        // 公司名称 商务人 运营人名称模糊限制
        $limit_blur = $this->limitParamsBlurForList();

        return array_merge($limit_base, $limit_customer, $limit_blur);
    }

    /**
     * 客户编号规范为：C20180816A1B2C3
     * 账号编号规范为：FA/TA20180816A1B2C3（FA为正式账号/TA为测试账号）
     * 说明（客户、账号）
     * 20180816：创建的当前年月日
     * A1B2C3：后6位做0-9A-Z6位随机数
     */
    public function createUUid($type, $random_len = 6)
    {
        if ($type == 'C') {
            $prex = 'C';
        } else if ($type == 'FA') {
            $prex = 'FA';
        } else if ($type == 'TA') {
            $prex = 'TA';
        }
        $date = date('Ymd');

        $b = range('A', 'Z');
        $c = range('0', '9');
        $chars = array_merge($b, $c);
        $charslen = count($chars) - 1;
        shuffle($chars);

        $output = $prex . $date;
        for ($i = 0; $i < $random_len; $i++) {
            $output .= $chars[mt_rand(0, $charslen)];
        }

        return $output;
    }

    /**
     * 获取客户的数据
     *
     * @access public
     *
     * @param $customer_id string 客户ID
     *
     * @return array
     **/
    public function getCustomerData($customer_id)
    {
        //获取客户数据
        $is_delete = 0;
        $customer_data = $this->where(compact('customer_id', 'is_delete'))
            ->find();
        if (empty($customer_data)) {
            throw new \Exception('获取客户数据失败');
        }
//        //获取父级账号数据
//        $father_id = 0;
//        $account_data = (new AccountModel())->field('email, account_id, apikey, appsecret, end_time, father_id')
//            ->where(compact('father_id', 'customer_id'))
//            ->find();
//        if (empty($account_data)) {
//            throw new \Exception('此客户的默认账号已丢失，请及时联系管理员');
//        }
//        if ($account_data['end_time']) {
//            $account_data['end_time'] = date('Y-m-d', $account_data['end_time']);
//        }

        return $customer_data; //array_merge($customer_data, $account_data);
    }

    /**
     * 编辑客户 (已废弃)
     * @return array
     */
    public function getCustomerForEdit($id = '')
    {
        $id = empty($id) ? I('get.id', '', 'trim') : $id;
        if (empty($id)) {
            return [];
        }
        $customer_info = $this->where('id=' . $id)
            ->where(DataAuthController::instance()
                ->getCustomerWhere())
            ->find();
        if (empty($customer_info)) {
            throw new \Exception('您不存在该数据的编辑权限');
        }
        $customer_id = $customer_info['customer_id'];
        //查找父级账号
        /*
        $account_model = new AccountModel();
        $where['customer_id'] = $customer_id;
        $where['father_id'] = 0;
        $account_info = $account_model->where($where)
            ->field('email,account_id,apikey,appsecret,end_time,father_id')
            ->find();
        if ($account_info['end_time']) {
            $account_info['end_time'] = (strlen($account_info['end_time']) == 10) ? date('Y-m-d', $account_info['end_time']) : date('Y-m-d', strtotime($account_info['end_time']));
        }
        */
        $account_info = [];
        return array_merge($customer_info, $account_info);
    }

    /**
     * 查询客户信息，返回指定的字段
     *
     * @param string $field
     * @param string $id
     *
     * @return array|mixed
     */
    public function getCustomerById($id = '', $field = '*')
    {
        $id = empty($id) ? I('get.id', '', 'trim') : $id;
        if (empty($id)) {
            return [];
        }

        return $this->where('id=' . $id)
            ->field($field)
            ->find();
    }

    /**
     * 查询客户信息，返回指定的字段
     *
     * @param string $field
     * @param string $customer_id
     *
     * @return array|mixed
     */
    public function getCustomerByCustomerId($customer_id, $field = '*')
    {
        if (empty($customer_id)) {
            return [];
        }
        $where['customer_id'] = $customer_id;

        return $this->where($where)
            ->field($field)
            ->find();
    }

    /**
     * 客户管理编辑页面重置密码功能
     */
    public function updatePwdById($id)
    {
        $customer_info = $this->getCustomerForEdit($id);
        $account_id = $customer_info['account_id'];
        if (empty($account_id)) {
            throw new \Exception('请求失败');
        }

        $account_model = new AccountModel();
        $where['account_id'] = $account_id;
        $where['father_id'] = 0;
        // 生成password
        $data['password'] = $this->genPasswordForCreate();
        $data['update_at'] = time();

        return $account_model->where($where)
            ->save($data);
    }

    /**
     * post方式通过id修改客户 && relationship
     * @throws \Exception
     */
    public function updateCustomerByIdForPost()
    {
        // 检查参数
        $this->checkParamsByUpdateForPost();

        // 更新客户
        $params = $this->getParamForUpdate();
        $customer_id = I('post.customer_id', '', 'trim');
        $group_id = I('post.group_id', '', 'trim');

        //记录商务流转
        $csh = new CustomerSalesmanHistoryModel();
        $change_month = I('post.change_month', '', 'trim');//更改月份
        $change_time = strtotime($change_month."-01");
        $where['customer_id'] = $customer_id;
        $csh_list = $csh->where($where)->select();

        $current_salesman = array_pop($csh_list);//当前的商务
        $current_min_end = strtotime("+1 month",strtotime($current_salesman['start_day'])) - 1;//起始时间往后一个月
        $last_salesman = array_pop($csh_list);//当前商务的上一个商务 可能是空

        if($current_salesman['salesman'] != $params['salesman']){//更新为新商务
            if ($current_min_end > $change_time) {//校验切换时间 添加新商务需要给当前商务最少一个月
                throw new \Exception('商务跟进人切换间隔应大于一个月');
            }
        }else{//更改当前商务的开始时间
            if(!is_null($last_salesman)) {//当前商务之前没有商务,随意更改不校验
                //当前商务的前一个商务需要最少一个月
                $last_min_end = strtotime("+1 month", strtotime($last_salesman['start_day'])) - 1;//起始时间往后一个月
                if ($last_min_end > $change_time) {//校验切换时间
                    throw new \Exception('商务跟进人切换间隔应大于一个月');
                }
            }
        }


        $customer_info = $this->getCustomerData($customer_id);
        //计费配置监听客户修改
        (new FeeConfigRepository())->listenUpdateCustomer($params, $customer_id);

        $result = $this->where(compact('customer_id'))
            ->save($params);

        if ($result) {
            $account_model = new AccountModel();
            $acc_where = [];
            $acc_where['customer_id'] = $customer_info['customer_id'];
            $acc_data  = [];
            $acc_data['update_at'] = time();
            $acc_data['group_id'] = $group_id;
            $account_model->where($acc_where)->save($acc_data);
        }

        //新增账单特殊消耗
        //$this->addExpend($customer_id);

        //更新历史商务
        $now = date("Y-m-d H:i:s");
        if($current_salesman['salesman'] != $params['salesman']){//更改商务
            //更新之前的商务 添加结束时间至前一天
            $change_month = strtotime($change_month."-01");
            //更新之前商务的条件
            $where = [];
            $where['customer_id'] = $customer_info['customer_id'];
            $where['salesman']    = $customer_info['salesman'];
            $where['end_day']     = '';//默认值为空字符串
            $update_data = [
                'end_day'    => date("Ymd",strtotime("-1 day",$change_month)),
                'updated_at' => $now,
            ];
            $csh->where($where)->save($update_data);

            //添加新商务
            $data = [
                'customer_id' => $customer_info['customer_id'],
                'salesman'    => $params['salesman'],
                'start_day'   => date("Ymd",$change_month),
                'created_at'  => $now,
                'updated_at'  => $now,
            ];
            $csh->add($data);
        }else{//如果修改的商务与当前一致,时间不一致 更新当前的开始时间,上一个商务的结束时间
            //更新上一个结束时间
            $change_month = strtotime($change_month . "-01");
            $change_day = date("Ymd", $change_month);
            if($change_day != $current_salesman['start_day']) {//商务没有更改,时间更改了
                if (!is_null($last_salesman)) {//有多条历史商务数据
                    $where       = ['id' => $last_salesman['id']];
                    $update_data = [
                        'end_day'    => date("Ymd", strtotime("-1 day", $change_month)),
                        'updated_at' => $now,
                    ];
                    $csh->where($where)->save($update_data);
                }
                //更新当前开始时间
                $where       = ['id' => $current_salesman['id']];
                $update_data = [
                    'start_day'  => $change_day,
                    'updated_at' => $now,
                ];
                $csh->where($where)->save($update_data);
            }
        }

        return $result;
    }

    /**
     * 编辑账单特殊消耗数据
     *
     * @access protected
     *
     * @return void
     **/
    protected function updateExpend($customer_id)
    {
        $customer_expend_model = new CustomerExpendModel();

        //查询本月之后的所有账号特殊消耗数据
        $data = $customer_expend_model->where([
            'customer_id' => $customer_id,
            'start_date' => ['egt', date('Ym')],
        ])
            ->select();
        $data = array_column($data, null, 'id');

        //修改之后的数据
        $expend = I('post.expend');
        $expend = array_map(function ($item) {
            $item['start_date'] = str_replace('-', '', $item['start_date']);

            return $item;
        }, $expend);

        $update_expend = [];                                //修改的数据 = data中存在 expend中也存在的
        $add_expend = [];                                //data中不存在，expend中存在的
        $del_expend = array_column($data, 'id');           //data中存在，expend中不存在的
        foreach ($expend as $item) {
            $id = $item['id'];
            if (!empty($id)) {
                //存在的话则不需要删除
                $key = array_search($id, $del_expend);
                if ($key !== false) {
                    unset($del_expend[$key]);
                }
                //如果数据不一致则需要修改
                if ($data[$id]['start_date'] != $item['start_date'] || $item['name'] != $data[$id]['name'] || $item['money'] != $data[$id]['money'] || $data[$id]['type'] != $item['type'] || $data[$id]['remark'] != $item['remark']) {
                    $update_expend[] = $item;
                    continue;
                }
                continue;
            } else {
                //如果ID不存在，则代表需要增加的数据
                $item['customer_id'] = $customer_id;
                $add_expend[] = $item;
            }
        }

        //halt(compact('update_expend', 'add_expend', 'del_expend'));

        //修改数据
        foreach ($update_expend as $item) {
            $id = $item['id'];
            (new CustomerExpendModel())->save($item, compact('id'));
        }

        //删除数据
        if (!empty($del_expend)) {
            (new CustomerExpendModel())->where([
                'id' => ['in', $del_expend],
            ])
                ->delete();
        }

        //增加数据
        if (!empty($add_expend)) {
            (new CustomerExpendModel())->addAll($add_expend);
        }
    }

    /**
     * 为创建用户过滤参数
     */
    protected function getParamForUpdate()
    {
        $name = I('post.name', '', 'trim');
        $companys = I('post.company', '', 'trim');
        $status = I('post.status', '', 'trim');
        $type = I('post.type', '', 'trim');
        $c_type = I('post.c_type', '', 'trim');
        $customer_type = I('post.customer_type', '', 'trim');
        $operator = I('post.operator', '', 'trim');
        $salesman = I('post.salesman', '', 'trim');
        $introduce_salesman = I('post.introduce_salesman', '', 'trim');
        $channel_follower = I('post.channel_follower', '', 'trim');// 渠道跟进人
        $source_id = implode(',',I('post.source_id', '', 'trim'));// 渠道跟进人可见征信机构
        $channel_mode = I('post.channel_mode', '', 'trim');//客户类型 是否为渠道客户
        $sign_type =implode(',',I('post.sign_type', '', 'trim'));//征信客户分类： 0: 羽乐科技签约, 10: 朴道签约 逗号分隔
        $group_id = I('post.group_id', '', 'trim');//主体id
        $reconciliation_cycle = I('post.reconciliation_cycle', '', 'trim');//对账方式 对账周期
        $level = I('post.level', '', 'trim');//级别

        //对公司名称进行拆分:
        //1、一个为company字段 兼容历史数据以及已经在其他地方使用该字段内容的情况，它表示和羽乐科技签约的名称
        //2、一个为agent_company字段 新增该字段为代理机构对应的签约公司名称，为json格式，可存放多个代理机构对应的公司名称
        $company = $companys[0];
        unset($companys[0]);
        $agent_company = json_encode($companys, JSON_UNESCAPED_UNICODE);
        
        $payment_type = I('post.payment_type', '', 'trim');
//        $end_time = I('post.end_time', '', 'trim');
//        $end_time = intval(strtotime($end_time)) + 86399;
        $update_at = time();

        //新增加的字段 账单收件邮箱&&预警余额&&计算充值日期
        $bill_email = I('post.bill_email', '', 'trim');
        if (!empty($bill_email)) {
            $bill_email_arr = explode(PHP_EOL, $bill_email);
            if (count($bill_email_arr) > 8) {
                throw new \Exception("账单收件邮箱最多只允许设置8个邮箱");
            }
            $bill_email_arr = array_map(function ($bill_email) {
                $bill_email = trim($bill_email);
                if (!filter_var($bill_email, FILTER_VALIDATE_EMAIL)) {
                    throw new \Exception("{$bill_email}不是标准的邮箱格式");
                }

                return $bill_email;
            }, $bill_email_arr);
            $bill_email = implode(';', $bill_email_arr);
        }

        //账单抄送人
        $bill_cc_email = I('post.bill_cc_email', '', 'trim');
        if (!empty($bill_cc_email)) {
            $bill_cc_email_arr = explode(PHP_EOL, $bill_cc_email);
            if (count($bill_cc_email_arr) > 20) {
                throw new \Exception("账单抄送人最多只允许设置8个邮箱");
            }
            $bill_cc_email_arr = array_map(function ($bill_cc_email) {
                $bill_cc_email = trim($bill_cc_email);
                if (!filter_var($bill_cc_email, FILTER_VALIDATE_EMAIL)) {
                    throw new \Exception("{$bill_cc_email}不是标准的邮箱格式");
                }

                return $bill_cc_email;
            }, $bill_cc_email_arr);
            $bill_cc_email = implode(';', $bill_cc_email_arr);
        }

        $balance = I('post.balance', null, 'trim');
        $balance = $balance ? $balance : null;
        $balance_percent = I('post.balance_percent', null, 'trim');
        $balance_percent = $balance_percent ? ($balance_percent / 100) : null;
        $available_days = I('post.available_days', null, 'trim');
        $available_days = $available_days ? $available_days : null;

//        $frequency = I('post.frequency', 1);

        $email_type = I('post.email_type', 1);

        // 生成password
        return compact('name', 'company', 'agent_company', 'status', 'update_at', 'type', 'c_type', 'operator', 'salesman', 'introduce_salesman', 'channel_follower', 'balance', 'bill_email', 'bill_cc_email', 'balance_percent', 'available_days', 'payment_type', 'email_type', 'customer_type','source_id','channel_mode','reconciliation_cycle','sign_type','group_id','level');
    }

    /**
     * 检查更新客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByUpdateForPost()
    {
        // 检查邮箱是否唯一
        //$this->checkUniqueEmailForUpdate();

        // 检查Name是否唯一
        $this->checkUniqueNameForUpdate();

        //校验账单特殊消耗
        $expend = I('post.expend');
        if (!empty($expend)) {
            $expend = array_values($expend);
            $current_month_time = strtotime(date('Y-m-') . '01');
            foreach ($expend as $item) {
                $datetime = strtotime($item['start_date'] . '-01');
                if ($datetime < $current_month_time && !C('ALLOW_HISTORY_FEE_CONFIG')) {
                    throw new \Exception('不可增加本月之前的账单特殊消耗数据【' . $item['start_date'] . '】');
                }
            }
        }
    }

    /**
     * 检查更新传递的邮件是否唯一
     * @throws \Exception
     */
    protected function checkUniqueEmailForUpdate()
    {
        $account_model = new AccountModel();
        $customer_id = I('post.customer_id', '', 'trim');
        $customer_info = $this->getCustomerData($customer_id);

        $email = I('post.email', '', 'trim');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('请检查邮箱的格式');
        }
        $email_unique = $account_model->where(compact('email'))
            ->find();
        if ($email_unique && $email_unique['account_id'] != $customer_info['account_id']) {
            throw new \Exception('客户邮箱已经被占用,请更换邮箱');
        }
    }

    /**
     * 检查更新传递的name(id)是否是唯一的
     * @throws \Exception
     */
    protected function checkUniqueNameForUpdate()
    {
        $customer_id = I('post.customer_id', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');

        // name email的唯一性
        $name = I('post.name', '', 'trim');
        if (!$name) {
            throw new \Exception('客户名称未填写');
        }
        $name_unique = $this->where(compact('name'))
            ->find();
        if ($name_unique && $name_unique['customer_id'] != $customer_id) {
            throw new \Exception('客户名称已经被占用，请切更换客户名称');
        }
        //end time
//        if (empty($end_time)) {
//            throw  new \Exception('截止日期未填写');
//        }
        $group_id = I('post.group_id', '', 'trim');
        // if (!$group_id) {
        //     throw new \Exception('请选择主体');
        // }
    }

    /**
     * 根据条件删除某个客户及其子账号
     * @param $where array 条件值
     *
     * @return void
     **<AUTHOR>
     * @datetime 15:04 2018/12/21
     *
     * @access   public
     *
     */
    public function deleteCustomerAndAccountByWhere($where)
    {
        $customer_id = $this->where($where)
            ->getField('customer_id');
        $accountModel = new AccountModel();
        $accountModel->deleteAccountByWhere([compact('customer_id')]);
        $res = $this->where($where)
            ->save([
                'is_delete' => 1,
            ]);
        if (!$res) {
            throw new \Exception('删除失败');
        }
    }
}
