<?php

namespace Account\Model;

use Account\Model\ProductModel;
use Account\Model\AccountModel;
use Think\Model;
use Common\Controller\ChangeNoticeController;

class AccountProductModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'account_product';
    protected $tablePrefix = '';

    /**
     * 创建账号产品
     * @return mixed
     * @throws \Exception
     */
    public function createAccountProduct(){
        // 检查参数
        $this->checkParamsByCreateForPost();

        // 获取参数
        $params = $this->getParamForCreate();
        //修改入库字段data的组成形式
        $data1 = json_decode($params['data'],true);
        $data = [];
        foreach ($data1 as $key=>$val){
            $data[$val['name']] = $val['val'];
        }
        $params['data'] = json_encode($data);
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $resum = new ChangeNoticeController();
        $resum->Notice($product_id,$account_id);
        return $this->add($params);
    }

    /**
     * 编辑账号产品
     * @return mixed
     * @throws \Exception
     */
    public function updateAccountProduct(){
        // 检查参数
        $this->checkParamsByUpdateForPost();

        // 获取参数
        $params = $this->getParamForUpdate();
        //修改入库字段data的组成形式
        $data1 = json_decode($params['data'],true);
        $data = [];
        foreach ($data1 as $key=>$val){
            $data[$val['name']] = $val['val'];
        }
        $params['data'] = json_encode($data);
        $account_product_id = I('post.account_product_id', '', 'trim');
        $where['id'] = $account_product_id;
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $resum = new ChangeNoticeController();
        $resum->Notice($product_id,$account_id);
        return $this->where($where)->save($params);
    }

    /**
     * 为创建产品过滤参数
     */
    protected function getParamForCreate()
    {
        $product_id = I('post.product_id', '', 'trim');
        $status = I('post.status', '', 'trim');
        $contract_status = I('post.contract_status', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');
        $end_time = intval(strtotime($end_time))+86399;
        $daily_limit = I('post.daily_limit', '', 'trim');
        $month_limit = I('post.month_limit', '', 'trim');
        $year_limit = I('post.year_limit', '', 'trim');
        $total_limit = I('post.total_limit', '', 'trim');
        $concurrency = I('post.concurrency', '', 'trim');
        $data = I('post.data', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $data_json = I('post.data_json', '', 'trim');
        $data_json = json_decode($data_json, true);
        foreach ($data_json as $key=>$item){
            if (isset($data[$item['name']])){
                $data_json[$key]['val'] = $data[$item['name']];
            }
        }
        $data = json_encode($data_json);
        $create_at = $update_at = time();

        return compact('account_id', 'product_id', 'status', 'contract_status', 'end_time', 'daily_limit', 'month_limit', 'year_limit', 'total_limit', 'concurrency', 'data', 'create_at', 'update_at');
    }

    /**
     * 为修改产品过滤参数
     */
    protected function getParamForUpdate()
    {
        $product_id = I('post.product_id', '', 'trim');
        $status = I('post.status', '', 'trim');
        $contract_status = I('post.contract_status', '', 'trim');
        $end_time = I('post.end_time', '', 'trim');
        $end_time = intval(strtotime($end_time))+86399;
        $daily_limit = I('post.daily_limit', '', 'trim');
        $month_limit = I('post.month_limit', '', 'trim');
        $year_limit = I('post.year_limit', '', 'trim');
        $total_limit = I('post.total_limit', '', 'trim');
        $concurrency = I('post.concurrency', '', 'trim');
        $data = I('post.data', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $data_json = I('post.data_json', '', 'trim');
        $data_json = json_decode($data_json, true);
        foreach ($data_json as $key=>$item){
            if (isset($data[$item['name']])){
                $data_json[$key]['val'] = $data[$item['name']];
            }
        }
        $data = json_encode($data_json);
        $update_at = time();

        return compact('account_id', 'product_id', 'status', 'contract_status', 'end_time', 'daily_limit', 'month_limit', 'year_limit', 'total_limit', 'concurrency', 'data', 'create_at', 'update_at');
    }

    /**
     * 检查更新传递的name(id)是否是唯一的
     * @throws \Exception
     */
    protected function checkParamsByUpdateForPost()
    {
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $data_json = I('post.data_json', '', 'trim');
        $data = I('post.data', '', 'trim');
        $account_product_id = I('post.account_product_id', '', 'trim');

        if (empty($product_id)) {
            throw new \Exception('请选择产品');
        }
        if (empty($account_id) || empty($account_product_id)) {
            throw new \Exception('请求错误');
        }

        $data_json = json_decode($data_json, true);
        foreach ($data_json as $item){
            // 检查是否必须
            if ($item['is_need'] && (!array_key_exists($item['name'], $data) || !isset($data[$item['name']]))) {
                throw new \Exception($item['cn_name'].'未填写');
            }
        }
    }

    /**
     * 检查新建客户的参数(初步的检查已经在html做了)
     * @throws \Exception
     */
    protected function checkParamsByCreateForPost()
    {
        $product_id = I('post.product_id', '', 'trim');
        $account_id = I('post.account_id', '', 'trim');
        $data_json = I('post.data_json', '', 'trim');
        $data = I('post.data', '', 'trim');

        if (!$product_id) {
            throw new \Exception('请选择产品');
        }
        if (!$account_id) {
            throw new \Exception('请求错误');
        }
        // account_id 的唯一性
        $account_id_unique = $this->where(compact('account_id', 'product_id'))->count();
        if ($account_id_unique) {
            throw new \Exception('不能再次选择已经开通的产品');
        }

//        $data_json = json_decode($data_json, true);
//        foreach ($data_json as $item){
//            if (!isset($data[$item['name']]) || $data[$item['name']] == ''){
//                throw new \Exception($item['cn_name'].'未填写');
//            }
//        }
    }

    /**
     * 根据account_id 返回开通的产品列表
     * @param $account_id
     * @return mixed
     */
    public function getProductListByAccountId($account_id){
        $where['account_id'] = $account_id;
        return $this->where($where)->field('id,account_id,product_id')->select();
    }

    /**
     * 查询账号开通产品名称列表
     * @param $account_id
     * @return array|mixed
     */
    public function getProductNameListByAccountId($account_id){

        //查询开通的产品列表
        $product_model = new ProductModel();

        $where = [
            'account_product.account_id' => $account_id,
            'product.back_status' => 1
        ];

        $field = [
            'account_product.id',
            'product.product_id',
            'product.product_name',
            'account_product.status',
            'account_product.end_time',
            'account_product.contract_status',
            'account_product.daily_limit',
            'account_product.month_limit',
            'account_product.year_limit',
            'account_product.total_limit',
        ];

        $product_list = $product_model->join('account_product on account_product.product_id=product.product_id', 'INNER')
            ->where($where)->field($field)
            ->order('status DESC, id DESC')
            //->order('product.product_name DESC, status DESC')
            ->select();

        // 格式化
        return $product_list;
    }

    /**
     * 根据条件查询账号产品关联信息
     * @param $where
     * @param string $field
     * @return mixed
     */
    public function getAccountProductInfoByWhere($where, $field='id, account_id, product_id'){
        return $this->where($where)->field($field)->find();
    }
    /**
     * 根据查询条件查询账号-产品关联表中的数据
     * <AUTHOR>
     * @datetime 13:19 2018/12/6
     *
     * @access public
     * @param $where array 查询的条件
     * @param $field string|array 查询的字段(默认*)
     *
     * @return array
     **/
    function getDataByWhere($where, $field='*')
    {
        return $this->where($where)->field($field)->select();
    }
}