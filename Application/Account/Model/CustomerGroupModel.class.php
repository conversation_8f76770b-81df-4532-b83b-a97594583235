<?php

namespace Account\Model;

use Account\Repositories\CompanyTypeRepository;
use Account\Repositories\FeeConfigRepository;
use Common\Common\ResponseTrait;
use Common\Controller\DataAuthController;
use PreSales\Repositories\PreSalesCustomerRepository;
use Think\Model;
use Common\ORG\Page;
use Account\Model\AccountModel;

class CustomerGroupModel extends Model
{
    use ResponseTrait;

    protected $connection = 'DB_FINANCE';
    protected $tableName = 'customer_group';
    protected $tablePrefix = '';

    /**
     * 用户类型
     *
     * @var int[]
     */
    public static $statusType = [
        '启用' => '1',
        '禁用' => '2',
    ];


    /**
     * 根据条件返回列表
     * @param $where
     * @param string $field
     * @return mixed
     */
    public function getGroupListByWhere($where) {
        $where = array_merge(['status' => 1], $where);
        return $this->where($where)->field('*')->select();
    }


    /**
     * 根据group_id获取主体信息
     * @param $group_id
     *
     * @return array|false|mixed|string|null
     */
    public function getGroupInfoByGroupId($group_id) {
        $where = ['group_id' => $group_id];
        return $this->where($where)->field('*')->find();
    }


    /**
     * GET方式获取用户列表客户
     */
    public function groupListForGet() {
        return $this->where([['status' => ['neq', 2]],])
                    ->field('id,group_id,group_name')
                    ->select();
    }
}
