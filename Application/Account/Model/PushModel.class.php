<?php

namespace Account\Model;

use Think\Model;

class PushModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'account_push';
    protected $tablePrefix = '';

    const PUSH_STATUS_FAIL = 1;//推送失败
    const PUSH_STATUS_SUCCESS = 2;//推送成功
    const PUSH_TIMES = 3;//推送次数上限
    const IS_ERROR_NO = 0;//未发送预警邮件
    const IS_ERROR_YES = 1;//已发送预警邮件

    public function createPush($params)
    {
        return $this->add($params);
    }

    public function updatePushInfo($id,$data)
    {

        if(is_array($id)){
            $where['id'] = ['in',$id];
            return $this->where($where)->save($data);
        }else{
            return $this->where('id ='.$id)->save($data);
        }

    }

    public function getErrorList()
    {
        $where['status'] = self::PUSH_STATUS_FAIL;
        return $this->where($where)->select();
    }
}