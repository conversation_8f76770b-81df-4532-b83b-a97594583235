<?php
header('Content-Type: application/json; charset=utf-8');
date_default_timezone_set("asia/shanghai");

// set mysql config
define('ENV', 'prod');
$config_mysql = [
    'dev' => [
        'DB_USER' => 'crs',
        'DB_PWD' => 'crs@2017qwe',
        'DB_HOST' => '*************',
        'DB_PORT' => '3306',
        'DB_NAME' => 'crs-api',
    ],
    'prod' => [
        'DB_USER' => 'finance_backend',
        'DB_PWD' => 'Yulore@2018Qaz',
        'DB_HOST' => '************',
        'DB_PORT' => '6021',
        'DB_NAME' => 'itag_apis',
    ]
];
$config_mysql = $config_mysql[ENV];

$info = isset($_POST) ? $_POST : '';

if (empty($info)) {
    die('{"status":"104","msg":"empty info"}');
}

if (!isset($_GET['apikey']) || $_GET['apikey'] != 'yuloreeiauXtRkjH88RHit5kpvwK12np') {
    die('{"status":"104","msg":"Invalid apikey"}');
}

if (!isset($_GET['sig']) || $_GET['sig'] != 'yulore_inner_query') {
    die('{"status":"104","msg":"Invalid sig"}');
}

if (!is_array($info) || count($info) < 4) {
    die('{"status":"105","msg":"Invalid info"}');
}

//定义接口变量
$update_data = [];

//解析apikey与project
foreach ($info as $key => $value) {
    //解析apikey
    if (false === ($pos = strpos($key, '?apikey=')))
        continue;
    $apikey = trim(substr($key, $pos + strlen('?apikey=')));

    if (!$apikey) {
        continue;
    }

    //解析project
    $p_str = trim(substr($key, 0, $pos), '/');
    if ($p_str && ($sub_project = explode('/', $p_str))) {
        if (!empty($sub_project)) {
            $project = end($sub_project);
        } else {
            continue;
        }
    } else {
        continue;
    }

    //解析调用次数
    $used_arr = json_decode(str_replace('=>', ':', $value), true);

    if (!$used_arr || !isset($used_arr['count'])) {
        continue;
    }
    $daily_used = intval($used_arr['count']);
    $update_data[md5($apikey . $project)] = [
        'apikey' => $apikey,
        'project' => $project,
        'daily_used' => $daily_used
    ];
}

//检验解析结果
if (empty($update_data)) {
    die('{"status":"109","msg":"empty update data"}');
}

//解析统计日期
if (!isset($info['@timestamp'])) {
    die('{"status":"106","msg":"missing paramter @timestamp"}');
}

$mysqli_itag_apis = new mysqli($config_mysql['DB_HOST'], $config_mysql['DB_USER'], $config_mysql['DB_PWD'], $config_mysql['DB_NAME'], $config_mysql['DB_PORT']);

// query操作，更新统计值
queryStat($info, $mysqli_itag_apis, $update_data);

$mysqli_itag_apis->close();
echo('{"status":"0","msg":"success"}');

/**
 * query操作，更新统计值
 * @param array $info 参数
 * @param object $mysqli_itag_apis mysql实例
 * @param array $update_data 待更新的参数
 */
function queryStat($info, $mysqli_itag_apis, $update_data)
{
    $daily_time = date('Ymd', strtotime($info['@timestamp']));

    //更新统计数据
    foreach ($update_data as $value) {
        if ($apikey_id = checkApikey($value['apikey'])) {
            try {
                $data_stat = [
                    'apikey' => $apikey_id,
                    'project' => $value['project'],
                    'daily_time' => $daily_time,
                    'daily_used' => $value['daily_used'],
                    'created_at' => time()
                ];

                // 执行query操作，如果是连续三次失败的话 则记录日志
                queryAgainIfFail($mysqli_itag_apis, $data_stat);
            } catch (Exception $e) {
                continue;
            }
        }
    }
}

/**
 * 执行query，若失败 则再次执行
 * @param object $mysqli_itag_apis MySQL对象
 * @param array $data_stat query单元
 * @return bool
 * @throws \Exception
 */
function queryAgainIfFail($mysqli_itag_apis, $data_stat)
{
    try {
        $i = 0;
        $query_result = false;
        while (true) {
            // 最多执行三次
            $i++;
            if ($i > 3) {
                break;
            }

            // 执行query
            $query_result = queryOneStat($mysqli_itag_apis, $data_stat);
            if (false !== $query_result) {
                break;
            }
        }

        // 日志
        if ($query_result === false) {
            $info_log = [
                'msg' => '邦秒配统计的API连续三次query失败',
                'data' => $data_stat,
                '$i' => $i
            ];
            logFail($info_log);
        }

        return $query_result;
    } catch (\Exception $e) {
        $info_log = [
            'msg' => '意外执行出错',
            'reason' => $e->getMessage(),
            'data' => $data_stat
        ];
        logFail($info_log);
        throw new \Exception($e->getMessage());
    }
}

/**
 * 日志
 * @param array|string $info_log
 */
function logFail($info_log)
{
    // 組裝要寫日志
    if (is_array($info_log)) {
        $info_log = json_encode($info_log, JSON_UNESCAPED_UNICODE);
    }

    // 檢查日志文件是否存在
    $dir = dirname(__FILE__) . '/../../cache/' . date('Ymd') . '/';
    if (!file_exists($dir) || !is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
    $destination = $dir . 'matching_stat_api' . '.log';

    // 寫入
    file_put_contents(
        $destination,
        '[' . date('Y-m-d H:i:s') . ']  ' . $info_log . PHP_EOL,
        FILE_APPEND
    );
}


/**
 * 执行一个query操作
 * @param object $mysqli_itag_apis mysql对象
 * @param array $data_stat 统计单元
 * @return bool
 */
function queryOneStat($mysqli_itag_apis, $data_stat)
{
    $apikey_id = $data_stat['apikey'];
    $project = $data_stat['project'];
    $daily_time = $data_stat['daily_time'];
    $daily_used = $data_stat['daily_used'];


    //查询当前天的记录
    $find_now_sql = 'select `id`,`daily_used` from `admin_apistat` where `apikey`="' . $apikey_id . '" and `project`="' . $mysqli_itag_apis->real_escape_string($project) . '" and `daily_time`="' . $daily_time . '"';

    $result = dhbGetOne($find_now_sql);

    if (empty($result)) {
        //新增一条统计记录
        $daily_sql = 'insert into `admin_apistat`(`' . implode('`,`', array_keys($data_stat)) . '`)values("' . implode('","', $data_stat) . '")';
    } else {
        $daily_sql = 'update `admin_apistat` set `daily_used`=`daily_used`+' . $daily_used . ',`updated_at`=' . time() . ' where `id`=' . $result['id'];
    }

    return $mysqli_itag_apis->query($daily_sql);
}


function dhbSelect($sql, $key = '')
{
    global $mysqli_itag_apis;

    $result = $mysqli_itag_apis->query($sql);
    $lists = array();
    while ($row = $result->fetch_array(MYSQLI_ASSOC)) {
        if (!empty($key)) {
            $lists[$row[$key]] = $row;
        } else {
            $lists[] = $row;
        }
    }
    $result->free();
    return $lists;
}

function dhbGetOne($sql)
{
    $lists = dhbSelect($sql);
    return isset($lists[0]) ? $lists[0] : null;
}

function checkApikey($apikey)
{
    global $mysqli_itag_apis;

    //验证参数apikey
    $check_apikey_sql = 'select `id` from `admin_apikey` where `apikey` = "' . $mysqli_itag_apis->real_escape_string($apikey) . '"';
    $apikey_info = dhbGetOne($check_apikey_sql);

    if (empty($apikey_info)) {
        return false;
    }
    return $apikey_id = $apikey_info['id'];
}
