<?php
header('Content-Type: text/html; charset=utf-8');
date_default_timezone_set("asia/shanghai");

//引入数据库配置
require_once dirname(__FILE__) . '/config/mysqli_itag_apis.php';

//查询当前日期的使用数量
$nowDate = date('Ymd');
$firstDayOfMonth = date('Ym').'01';
$firstDayOfYear = date('Y').'0101';

//当天调用
$daily_stat_sql = 'select `daily_used`,`apikey` from `admin_apistat` where `daily_time`="'.$nowDate.'" and `project`="itag"';
$daily_info = dhbSelect($daily_stat_sql, 'apikey');
$apikeyids_str = '('.implode(',',array_keys($daily_info)).')';

//月度调用
$month_stat_sql = 'select sum(daily_used) as monthly_used,`apikey` from `admin_apistat` where daily_time>="'.$firstDayOfMonth.'" and daily_time<="'.$nowDate.'" and `project`="itag" and `apikey` in '.$apikeyids_str.' group by `apikey`';
$month_info = dhbSelect($month_stat_sql, 'apikey');

//年度使用
$yearly_stat_sql = 'select sum(daily_used) as yearly_used,`apikey` from `admin_apistat` where daily_time>="'.$firstDayOfYear.'" and daily_time<="'.$nowDate.'" and `project`="itag"  and `apikey` in '.$apikeyids_str.' group by `apikey`';
$year_info =  dhbSelect($yearly_stat_sql, 'apikey');


//更新统计信息
if (!empty($daily_info)) {
    foreach ($daily_info as $apiid => $info) {
        $update_stat_sql = 'update `admin_apikey` set `daily_used`='.$info['daily_used'].',`monthly_used`='.$month_info[$apiid]['monthly_used'].',`yearly_used`='.$year_info[$apiid]['yearly_used'].' where `id`='.$info['apikey'];
            if (false === $mysqli_itag_apis->query($update_stat_sql)) {
                echo '['.date('Y-m-d H:i:s').']更新统计信息失败,apikey='.$apiid;
            }
    }
}

function dhbSelect($sql, $key = '')
{
    global $mysqli_itag_apis;
    $lists = array();
    $result = $mysqli_itag_apis->query($sql);

    if ($result) {
        while ($row = $result->fetch_array(MYSQLI_ASSOC)) {
            if (!empty($key)) {
                $lists[$row[$key]] = $row;
            } else {
                $lists[] = $row;
            }
        }
        $result->free();
    }
    return $lists;
}

function dhbGetOne($sql)
{
    $lists = dhbSelect($sql);
    return isset($lists[0]) ? $lists[0] : null;
}
