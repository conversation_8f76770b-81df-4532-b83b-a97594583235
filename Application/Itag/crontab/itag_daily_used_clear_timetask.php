<?php
header('Content-Type: text/html; charset=utf-8');
date_default_timezone_set("asia/shanghai");

//引入数据库配置
require_once dirname(__FILE__) . '/config/mysqli_itag_apis.php';

//变量声明
$nowtime = time();
$hour = date('G', $nowtime);
$minute = date('i', $nowtime);
$minute = 0 === strpos($minute, '0') ? intval(substr($minute, 1)) : intval($minute);
$day = intval(date('j', $nowtime));
$month = intval(date('n', $nowtime));

//echo $month.'-'.$day.'-'.$hour.'-'.$minute;die;
//零点1分至5分之间清零所有apikey前一天的日调用次数
if (0 == $hour && $minute <= 5 && $minute >= 0) {
    $clear_daily_sql = 'update `admin_apikey` set `daily_used`=0';
    sqlExec($clear_daily_sql, 'day');

    //月初零点时间段，上月调用量清零
    if (1 == $day) {
        $clear_monthly_sql = 'update `admin_apikey` set `monthly_used`=0';
        sqlExec($clear_monthly_sql, 'month');

        //1月1日清零上一年调用次数
        if (1 == $month) {
            $clear_yearly_sql = 'update `admin_apikey` set `yearly_used`=0';
            sqlExec($clear_yearly_sql, 'year');
        }
    }
}

function sqlExec($sql, $type='day')
{
    global $mysqli_itag_apis;
    if (false === $mysqli_itag_apis->query($sql)) {
        echo '['.date('Y-m-d H:i:s').']'.'清除itag-'.$type.'调用次数失败!'.PHP_EOL;
    } else {
        echo '['.date('Y-m-d H:i:s').']'.'清除itag-'.$type.'成功!'.PHP_EOL;
    }
}
