<?php
try {
	//dev
	//$mysqli_itag_apis = new mysqli('172.18.19.173', 'crs', 'crs@2017qwe', 'crs-api', '3306');
	//online
	$mysqli_itag_apis = new mysqli('172.18.32.23', 'finance_backend', 'Yulore@2018Qaz', 'itag_apis', '6021');
	if ($mysqli_itag_apis->connect_error){
		throw new Exception($mysqli_itag_apis->connect_error);
	}
	$mysqli_itag_apis->query("SET NAMES UTF8");
} catch (Exception $e) {
	error_log('数据库连接错误：'.$e->getMessage().PHP_EOL, 3 , '../log/mysql.log');
	exit($e->getMessage());
}
