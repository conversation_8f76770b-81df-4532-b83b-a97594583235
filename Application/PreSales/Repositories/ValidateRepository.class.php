<?php

namespace PreSales\Repositories;


class ValidateRepository
{
    /**
     * 验证是否为空
     *
     * @access public
     * @param $value mixed 验证的参数
     *
     * @return boolean
     **/
    public function is_empty($value)
    {
        if ($value===0) {
            return false;
        }
        if ($value==='0') {
            return false;
        }
        if (empty($value)) {
            return true;
        }
        return false;
    }
    /**
     * 验证是否为日期格式
     *
     * @access public
     * @param $value mixed 验证的参数
     *
     * @return boolean
     **/
    public function is_date($value)
    {
        $regex = '/^[1-2][0-9]{3}\-(0[1-9]|1[0-2])\-(0[1-9]|[12][0-9]|3[0-1])$/';
        if (preg_match($regex, $value)) {
            return true;
        }
        return false;
    }
    /**
     * 验证是否为数字格式
     *
     * @access public
     * @param $value mixed 验证的参数
     *
     * @return boolean
     **/
    public function is_number($value)
    {
        return filter_var($value, FILTER_VALIDATE_INT);
    }
    /**
     * 是否为百分比小数
     *
     * @access public
     * @param $value mixed 验证的参数
     *
     * @return boolean
     **/
    public function is_rate($value)
    {
        $regex = '/^[0-9]{1,3}(\.[0-9]{1,2})?$/';
        if (preg_match($regex, $value)) {
            if ($value>100) {
                return false;
            }
            return true;
        }
        return false;
    }
    /**
     * 校验是否为百分比小数，如果是，则返回值，否则返回一个响应
     *
     * @access public
     * @param $value mixed 验证参数
     * @param $msg string 错误的响应内容
     * @param $allow_empty boolean 是否允许为空 允许的话并且如果为''，则返回null
     *
     * @return mixed
     **/
    public function valid_rate($value, $msg = '', $allow_empty = true)
    {
        if (!$this->is_empty($value)) {
            if ($this->is_rate($value)) {
                return $value;
            } else {
                $this->errorResponse($msg);
            }
        }
        if ($allow_empty) {
            return null;
        } else {
            $this->errorResponse($msg);
        }
        return null;
    }
    /**
     * 校验是否为日期
     *
     * @access public
     * @param $value mixed 验证参数
     * @param $msg string 错误的响应内容
     * @param $allow_empty boolean 是否允许为空 允许的话并且如果为''，则返回null
     *
     * @return mixed
     **/
    public function valid_date($value, $msg = '', $allow_empty = true)
    {
        if (!$this->is_empty($value)) {
            if ($this->is_date($value)) {
                return $value;
            } else {
                $this->errorResponse($msg);
            }
        }
        if ($allow_empty) {
            return null;
        } else {
            $this->errorResponse($msg);
        }
        return null;
    }
    /**
     * 校验是否为空
     *
     * @access public
     * @param $value mixed 验证参数
     * @param $msg string 错误的响应内容
     *
     * @return mixed
     **/
    public function valid_empty($value, $msg = '')
    {
        if (!$this->is_empty($value)) {
            return $value;
        }
        $this->errorResponse($msg);
        return null;
    }
    /**
     * 校验是否存在于某个数组中
     *
     * @access public
     * @param $value mixed 验证参数
     * @param $data array 数据
     * @param $msg string 错误的响应内容
     * @param $allow_empty boolean 是否允许为空 允许的话并且如果为''，则返回null
     *
     * @return mixed
     **/
    public function valid_in_data($value, $data, $msg = '', $allow_empty)
    {
        if (!$this->is_empty($value)) {
            if (in_array($value, $data)) {
                return $value;
            } else {
                $this->errorResponse($msg);
            }
        }
        if ($allow_empty) {
            return null;
        } else {
            $this->errorResponse($msg);
        }
        return null;
    }
    /**
     * 校验是否为数字
     *
     * @access public
     * @param $value mixed 验证参数
     * @param $msg string 错误的响应内容
     * @param $allow_empty boolean 是否允许为空 允许的话并且如果为''，则返回null
     *
     * @return mixed
     **/
    public function valid_number($value, $msg = '', $allow_empty = true)
    {
        if (!$this->is_empty($value)) {
            if ($this->is_number($value)) {
                return $value;
            } else {
                $this->errorResponse($msg);
            }
        }
        if ($allow_empty) {
            return null;
        } else {
            $this->errorResponse($msg);
        }
        return null;
    }
    /**
     * 构建错误响应
     *
     * @access public
     * @param $msg string 错误信息
     * @param $status integer 错误代码
     *
     * @return void
     **/
    public function errorResponse($msg = '', $status = 1, $data = [])
    {
        $data = compact('msg', 'status', 'data');
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        ob_end_clean();
        header('Content-Type:application/json; charset = UTF8');
        exit($data);
    }
    /**
     * 构建正确的AJAX响应
     *
     * @access public
     * @param $msg string 信息
     * @param $status integer 代码
     *
     * @return void
     **/
    public function successResponse($msg = '', $status = 0, $data = [])
    {
        $data = compact('msg', 'status', 'data');
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        ob_end_clean();
        header('Content-Type:application/json; charset = UTF8');
        exit($data);
    }
}