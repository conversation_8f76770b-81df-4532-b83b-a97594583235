<?php

namespace PreSales\Repositories;

use Common\ORG\Page;
use PreSales\Model\PreSalesCustomerModel;
use PreSales\Model\PreSalesPubResultModel;
use PreSales\Model\PreSalesResult104Model;

class PreSalesResult104Repository implements PreSalesResultRepository
{
    protected $model;
    protected $table_name = 'pre_sales_result104';
    protected $product_id = 104;
    protected $list_rows = 15;
    public function __construct()
    {
        $this->model = new PreSalesResult104Model();
    }
    public function getModel()
    {
        return $this->model;
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        列表页及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function index()
    {
        //获取查询参数
        $params = $this->getSearchParams();
        //获取页面的input查询域
        $input = $this->getInput($params);
        //获取查询条件
        $where = $this->getWhere($params);
        //排序方式
        $order = $this->getOrder();
        //获取数据并进行分页
        $data = $this->getData($where, $order);
        $data['input'] = $input;
        //获取Table的html
        $table = new TableRepository();
        $data['table'] = $table->setData($data['data'])->setConfig([
            ['field' => 'ps_customer_name', 'title' => '客户名称', 'order' => false, 'callback' => null],
            ['field' => 'company', 'title' => '公司名称', 'order' => false, 'callback' => null],
            ['field' => 'region', 'title' => '区域', 'order' => false, 'callback' => null],
            ['field' => 'company_type', 'title' => '公司类型', 'order' => false, 'callback' => null],
            ['field' => 'submit_time', 'title' => '提交测试日期', 'order' => 'pre_sales_pub_result.submit_time', 'callback' => null],
            ['field' => 'return_time', 'title' => '返回结果日期', 'order' => 'pre_sales_pub_result.return_time', 'callback' => null],
            ['field' => 'test_phase', 'title' => '测试期数', 'order' => 'pre_sales_pub_result.test_phase', 'callback' => null],
            ['field' => 'test_days', 'title' => '测试天数', 'order' => 'pre_sales_pub_result.test_days', 'callback' => null],
            ['field' => 'test_num', 'title' => '测试总数量', 'order' => 'pre_sales_pub_result.test_num', 'callback' => null],
            ['field' => 'rate_score', 'title' => '测试命中占比', 'order' => $this->table_name . '.rate_score', 'callback' => null],
            ['field' => 'result', 'title' => '测试结果', 'order' => false, 'callback' => null],
            ['field' => 'test_progress', 'title' => '测试进度', 'order' => false, 'callback' => null],
            ['field' => 'test_result', 'title' => '客户反馈', 'order' => false, 'callback' => null],
            ['field' => 'remark', 'title' => '备注', 'order' => false, 'callback' => null],
            ['field' => 'auth', 'title' => '操作', 'order' => false, 'callback' => null],
        ])->show();
        //获取JS
        $data['script'] = $table->setDefaultOrder('pre_sales_pub_result.submit_time')->getScript();
        return $data;
    }
    /**
     * 获取数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function getData($where, $order)
    {
        //获取总数
        $total = $this->model
            ->join('pre_sales_pub_result ON pre_sales_pub_result.id = ' . $this->table_name . '.pub_result_id')
            ->join('pre_sales_customer ON ' . $this->table_name . '.ps_customer_id = pre_sales_customer.ps_customer_id', 'left')
            ->where($where)->count();
        $page = new Page($total, $this->list_rows);
        $data = DataRepository::getFullPreSalesProductResultData($this->product_id, $where, '*,' . $this->table_name . '.id AS product_result_id', $order, [$page->firstRow, $page->listRows]);
        $render = $page->show();
        if (empty($data)) {
            return compact('data', 'render', 'total');
        }
        //获取必要的数据
        #企业类型
        $company_type = DataRepository::getCompanyTypeData([], 'id, name');
        $company_type = array_column($company_type, 'name', 'id');
        #区域
        $region_arr = PreSalesCustomerModel::$region;
        #测试进度
        $progress_arr = PreSalesPubResultModel::$progress;
        $data = array_map(function ($item) use($company_type, $region_arr, $progress_arr) {
            $result = [];
            $result['ps_customer_name'] = $item['ps_customer_name'];
            $result['company'] = $item['company'];
            $result['region'] = $region_arr[$item['region']];
            $result['company_type'] = $company_type[$item['first_type']] . '--' . $company_type[$item['twice_type']];
            $result['submit_time'] = date('Y-m-d', $item['submit_time']);
            $result['return_time'] = $item['return_time']?date('Y-m-d', $item['return_time']):'--';
            $result['test_phase'] = $item['test_phase'];
            $result['test_days'] = $item['test_days']?$item['test_days']:'--';
            $result['test_num'] = $item['test_num']?$item['test_num']:'--';
            #测试命中占比
            $result['rate_score'] = $item['rate_score']?$item['rate_score'] . '%':'--';
            #测试结果
            if (mb_strlen($item['result'], 'utf8')>5) {
                $result['result'] = mb_substr($item['result'], 0, 5, 'utf8') . <<<A
<a tabindex="0" role="button" data-toggle="popover" data-placement="left" data-trigger="focus" title="客户反馈详情" data-content="{$item['result']}">...</a>
A;
            } else {
                $result['result'] = $item['result'];
            }
            $result['test_progress'] = $progress_arr[$item['test_progress']];
            if (mb_strlen($item['test_result'], 'utf8')>5) {
                $result['test_result'] = mb_substr($item['test_result'], 0, 5, 'utf8') . <<<A
<a tabindex="0" role="button" data-toggle="popover" data-placement="left" data-trigger="focus" title="客户反馈详情" data-content="{$item['test_result']}">...</a>
A;
            } else {
                $result['test_result'] = $item['test_result'];
            }
            if (mb_strlen($item['remark'],'utf8')>5) {
                $result['remark'] = mb_substr($item['remark'], 0, 5, 'utf8') . <<<A
<a tabindex="0" role="button" data-toggle="popover" data-placement="left" data-trigger="focus" title="备注详情" data-content="{$item['remark']}">...</a>
A;
            } else {
                $result['remark'] = $item['remark'];
            }
            $result['auth'] = '<a href="' . U('edit', [
                    'product_id'    => $this->product_id,
                    'id'            => $item['product_result_id']
                ]) . '?callback=' . urlencode($_SERVER['REQUEST_URI']) . '"><nobr>编辑</nobr></a>';
            return $result;
        }, $data);
        return compact('data', 'render', 'total');
    }
    /**
     * 获取排序方式
     *
     * @access protected
     *
     * @return string
     **/
    protected function getOrder()
    {
        $order = 'pre_sales_pub_result.submit_time desc';
        $order_field = I('get.order_field', '', 'trim');
        $order_type = I('get.order_type', '', 'trim');
        if ($order_field && $order_type) {
            $order = $order_field . ' ' . $order_type;
        } elseif ($order_field) {
            $order = $order_field;
        }
        return $order;
    }
    /**
     * 查询条件
     *
     * @access protected
     * @param $params array 查询参数
     *
     * @return array
     **/
    protected function getWhere($params)
    {
        $where = [];
        //客户ID
        if (!empty($params['ps_customer_id'])) {
            $where['pre_sales_customer.ps_customer_id'] = $params['ps_customer_id'];
        }

        //客户名称
        if (!empty($params['ps_customer_name'])) {
            $where['pre_sales_customer.ps_customer_name'] = ['like', "%{$params['ps_customer_name']}%"];
        }
        //公司名称
        if (!empty($params['company'])) {
            $where['pre_sales_customer.company'] = ['like', "%{$params['company']}%"];
        }
        //区域
        if (!empty($params['region'])) {
            $where['pre_sales_customer.region'] = $params['region'];
        }
        //公司类型
        if (!empty($params['twice_type'])) {
            $where['pre_sales_customer.twice_type'] = $params['twice_type'];
        }
        //提交测试日期
        if ($params['start_submit_time'] && $params['end_submit_time']) {
            $where['pre_sales_pub_result.submit_time'] = ['between', [strtotime($params['start_submit_time']), strtotime($params['end_submit_time'] . ' 23:59:59')]];
        } elseif ($params['start_submit_time']) {
            $where['pre_sales_pub_result.submit_time'] = ['egt', strtotime($params['start_submit_time'])];
        } elseif ($params['end_submit_time']) {
            $where['pre_sales_pub_result.submit_time'] = ['elt', strtotime($params['end_submit_time'])];
        }
        //返回结果日期
        if ($params['start_return_time'] && $params['end_return_time']) {
            $where['pre_sales_pub_result.return_time'] = ['between', [strtotime($params['start_return_time']), strtotime($params['end_return_time'] . ' 23:59:59')]];
        } elseif ($params['start_return_time']) {
            $where['pre_sales_pub_result.return_time'] = ['egt', strtotime($params['start_return_time'])];
        } elseif ($params['end_return_time']) {
            $where['pre_sales_pub_result.return_time'] = ['elt', strtotime($params['end_return_time'])];
        }
        //测试进度
        if (!empty($params['test_progress'])) {
            $where['pre_sales_pub_result.test_progress'] = $params['test_progress'];
        }
        return $where;
    }
    /**
     * 获取页面查询的input
     *
     * @access protected
     * @param $params array 查询参数
     *
     * @return string
     **/
    protected function getInput($params)
    {
        $display = '';
        //客户ID
        $display .= <<<HTML
<div class="form-group">
    <label for="ps_customer_id">客户ID</label>
    <input type="text" value="{$params['ps_customer_id']}" class="form-control" id="ps_customer_id" name="ps_customer_id" placeholder="请填写客户ID" maxlength="13" />
</div>
HTML;
        //客户名称
        $display .= <<<HTML
<div class="form-group">
    <label for="ps_customer_name">客户名称</label>
    <input type="text" value="{$params['ps_customer_name']}" class="form-control" id="ps_customer_name" name="ps_customer_name" placeholder="请填写客户名称" maxlength="30" />
</div>
HTML;
        //公司名称
        $display .= <<<HTML
<div class="form-group">
    <label for="company">公司名称</label>
    <input type="text" value="{$params['company']}" class="form-control" id="company" name="company" placeholder="请填写公司名称" maxlength="50" />
</div>
HTML;
        //区域
        $regionOption = DataRepository::getOption(PreSalesCustomerModel::$region, $params['region'], ['' => '--选择区域--']);
        $display .= <<<HTML
<div class="form-group">
    <label for="region">区域</label>
    <select class="form-control" id="region" name="region">
        {$regionOption}
    </select>
</div>
HTML;
        //公司类型
        $companyTypeOption = DataRepository::getCompanyTypeOption($params['twice_type']);
        $display .= <<<HTML
<div class="form-group">
    <label for="twice_type">公司类型</label>
    <select class="form-control" id="twice_type" name="twice_type">
        {$companyTypeOption}
    </select>
</div>
HTML;
        //提交测试日期
        $display .= <<<HTML
<div class="form-group">
    <label for="start_trace_time">提交测试日期</label>
    <input class="form-control" value="{$params['start_submit_time']}" type="date" id="start_submit_time" name="start_submit_time" />
    -
    <input class="form-control" value="{$params['end_submit_time']}" type="date" id="end_submit_time" name="end_submit_time" />
</div>
HTML;
        //返回结果日期
        $display .= <<<HTML
<div class="form-group">
    <label for="start_return_time">返回结果日期</label>
    <input class="form-control" value="{$params['start_return_time']}" type="date" id="start_return_time" name="start_return_time" />
    -
    <input class="form-control" value="{$params['end_return_time']}" type="date" id="end_return_time" name="end_return_time" />
</div>
HTML;
        //测试进度
        $progressOption = DataRepository::getOption(PreSalesPubResultModel::$progress, $params['test_progress'], ['' => '--选择测试进度--']);
        $display .= <<<HTML
<div class="form-group">
    <label for="test_progress">测试进度</label>
    <select class="form-control" id="test_progress" name="test_progress">
        {$progressOption}
    </select>
</div>
HTML;
        return $display;
    }
    /**
     * 获取查询参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getSearchParams()
    {
        //客户ID
        $ps_customer_id = I('get.ps_customer_id', '', 'trim');
        //客户名称
        $ps_customer_name = I('get.ps_customer_name', '', 'trim');
        //公司名称
        $company = I('get.company', '', 'trim');
        //区域
        $region = I('get.region', '', 'trim');
        //二级类型
        $twice_type = I('get.twice_type', '', 'trim');
        //提交测试日期
        $start_submit_time = I('get.start_submit_time', null, 'trim');
        $end_submit_time = I('get.end_submit_time', null, 'trim');
        if ($start_submit_time && $end_submit_time) {
            $submit_time = [strtotime($start_submit_time), strtotime($end_submit_time)];
            $start_submit_time = date('Y-m-d', min($submit_time));
            $end_submit_time = date('Y-m-d', max($submit_time));
        }
        //返回结果日期
        $start_return_time = I('get.start_return_time', null, 'trim');
        $end_return_time = I('get.end_return_time', null, 'trim');
        if ($start_return_time && $end_return_time) {
            $return_time = [strtotime($start_return_time), strtotime($end_return_time)];
            $start_return_time = date('Y-m-d', min($return_time));
            $end_return_time = date('Y-m-d', max($return_time));
        }
        //测试进度
        $test_progress = I('get.test_progress', '', 'trim');
        return compact('ps_customer_id', 'ps_customer_name', 'company', 'region', 'operator', 'is_pay', 'is_important', 'start_return_time',
            'start_submit_time', 'end_return_time', 'end_submit_time', 'twice_type', 'test_progress');
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        增加功能及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function add_html()
    {
        $input = <<<INPUT
<div class="form-group">
    <label for="rate_score" class="col-sm-2 control-label">
        测试命中占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入测试命中占比" id="rate_score" name="rate_score" aria-describedby="basic-addon3" maxlength="5">
            <span class="input-group-addon" id="basic-addon3">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="result" class="col-sm-2 control-label">
        测试结果：
    </label>
    <div class="col-sm-10">
        <input type="text" name="result" class="form-control" id="result" placeholder="请填写测试结果" maxlength="10">
    </div>
</div>
INPUT;
        $validate = new ValidateRepository();
        $validate->successResponse('', 0, compact('input'));
    }
    public function getAddData()
    {
        $validate=new ValidateRepository();
        //测试命中占比
        $rate_score = I('post.rate_score', '', 'trim');
        $rate_score = $validate->valid_rate($rate_score, '测试命中占比格式不正确');
        //测试结果
        $result = I('post.result', '', 'trim');
        return compact('rate_score', 'result');
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        编辑功能及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function edit_html($data)
    {
        $input = <<<INPUT
<div class="form-group">
    <label for="rate_score" class="col-sm-2 control-label">
        测试命中占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_score']}" class="form-control" placeholder="请输入测试命中占比" id="rate_score" name="rate_score" aria-describedby="basic-addon3" maxlength="5">
            <span class="input-group-addon" id="basic-addon3">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="result" class="col-sm-2 control-label">
        测试结果：
    </label>
    <div class="col-sm-10">
        <input type="text" value="{$data['result']}" name="result" class="form-control" id="result" placeholder="请填写测试结果" maxlength="10">
    </div>
</div>
INPUT;
        return $input;
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        导出功能及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function file_data()
    {
        //获取查询参数
        $params = $this->getSearchParams();
        //获取查询条件
        $where = $this->getWhere($params);
        //排序方式
        $order = $this->getOrder();
        //获取数据
        return $this->getFileData($where, $order);
    }
    /**
     * 获取fileData
     *
     * @access protected
     * @param $where array 查询条件
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function getFileData($where, $order)
    {
        $title = [
            '客户名称', '公司名称', ' 区域', '公司类型', '提交测试日期', '返回结果日期', '测试期数', '测试天数', '测试总数量',
            '测试命中占比', '测试结果',
            '测试进度', '客户反馈', '备注'
        ];
        $width = [
            26, 26, 18, 26, 20, 20, 18, 18, 18,
            18, 26,
            18, 26, 40
        ];
        $filename = '邦秒配产品测试信息';
        $data = DataRepository::getFullPreSalesProductResultData($this->product_id, $where, '*,' . $this->table_name . '.id AS product_result_id', $order);
        if (empty($data)) {
            return compact('title', 'data', 'filename', 'width');
        }
        //获取必要的数据
        #企业类型
        $company_type = DataRepository::getCompanyTypeData([], 'id, name');
        $company_type = array_column($company_type, 'name', 'id');
        #区域
        $region_arr = PreSalesCustomerModel::$region;
        #测试进度
        $progress_arr = PreSalesPubResultModel::$progress;
        #子产品数据
        $children_product_data = DataRepository::getProductData([
            'father_id' => $this->product_id
        ], 'product_id, product_name');
        $children_product_data = array_column($children_product_data, 'product_name', 'product_id');
        $data = array_map(function ($item) use($company_type, $region_arr, $progress_arr, $children_product_data) {
            $result = [];
            $result[] = $item['ps_customer_name'];
            $result[] = $item['company'];
            $result[] = $region_arr[$item['region']];
            $result[] = $company_type[$item['first_type']] . '--' . $company_type[$item['twice_type']];
            $result[] = date('Y-m-d', $item['submit_time']);
            $result[] = $item['return_time']?date('Y-m-d', $item['return_time']):'--';
            $result[] = $item['test_phase'];
            $result[] = $item['test_days']?$item['test_days']:'--';
            $result[] = $item['test_num']?$item['test_num']:'--';
            #测试命中占比
            $result[] = $item['rate_score']?$item['rate_score'] . '%':'--';
            #测试结果
            $result[] .= $item['result'];
            $result[] = $progress_arr[$item['test_progress']];
            $result[] = $item['test_result'];
            $result[] = $item['remark'];
            return $result;
        }, $data);
        return compact('title', 'data', 'filename', 'width');
    }
}