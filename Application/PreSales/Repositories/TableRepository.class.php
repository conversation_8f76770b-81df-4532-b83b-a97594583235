<?php

namespace PreSales\Repositories;

use Common\ORG\Page;
use Think\Model;

//列表组装类
class TableRepository
{
    /**
     * @var $model Model
     **/
    protected $model;   //操作的model      [field => '', 'title' => '', 'order' => boolean, 'callback' => function]
    protected $config;  //配置项
    protected $select = [
        'where'     => [],      //查询的条件
        'field'     => '*',     //查询的字段
        'order'     => null,    //排序方式
        'page'      => 15,      //每页数据量
    ];
    protected $data = null;    //数据
    protected $page_render = '';    //分页内容
    protected $default_order_field = '';  //默认排序的字段
    protected $default_order_type = 'desc';  //默认排序的方式
    /**
     * 设置Select的限制
     *
     * @access public
     * @param $where array 条件
     * @param $field string 字段
     * @param $order string 排序方式(可为null)
     * @param $page integer 分页数量（可为null）
     *
     * @return $this
     **/
    public function setSelect($where, $field = '*', $order = null, $page = 15)
    {
        $this->select = compact('where','field', 'order', 'page');
        return $this;
    }
    /**
     * 设置table配置项
     *
     * @access public
     * @param $config array 配置项
     *
     * @return $this
     **/
    public function setConfig($config)
    {
        $this->config = $config;
        return $this;
    }
    /**
     * 设置Model
     *
     * @access public
     * @param $model Model 操作的Model对象
     *
     * @return $this
     **/
    public function setModel(Model $model)
    {
        $this->model = $model;
        return $this;
    }
    /**
     * 设置数据
     *
     * @access public
     * @param $data array 数据
     *
     * @return $this
     **/
    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }
    /**
     * 设置默认排序的字段
     *
     * @access public
     * @param $field string 默认排序的字段
     * @param $type string 默认排序的方式
     *
     * @return $this
     **/
    public function setDefaultOrder($field, $type = 'desc')
    {
        $this->default_order_field = $field;
        $this->default_order_type = $type;
        return $this;
    }
    /**
     * 根据条件查询数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getData()
    {
        if (!is_null($this->data)) {
            return $this->data;
        }
        if (empty($this->model)) {
            throw new \Exception('未设置Model');
        }
        $model = $this->model->field($this->select['field'])->where($this->select['where']);
        //排序
        if ($this->select['order']) {
            $model = $model->order($this->select['order']);
        }
        //分页
        if ($this->select['page']) {
            $count = $model->count();
            $page = new Page($count, $this->select['page']);
            $this->page_render = $page->show();
            $model = $this->model->limit($page->firstRow, $page->listRows);
        }
        //查询数据
        return $model->select();
    }
    /**
     * 展示页面
     *
     * @access protected
     *
     * @return string
     **/
    public function show()
    {
        //获取数据
        $data = $this->getData();
        //获取配置项
        $config = $this->config;
        if (empty($config)) {
            throw new \Exception('未设置config');
        }
        //设置table
        return $this->getTable($data, $config);
    }
    /**
     * 获取table内容
     *
     * @access protected
     * @param $data array 数据
     * @param $config array 配置项
     *
     * @return string
     **/
    protected function getTable($data, $config)
    {
        $table = <<<TABLE
<div class="table-responsive"><table class="table table-hover table-bordered"><thead><tr>
TABLE;
    //设置table头
    array_walk($config, function ($item) use (&$table) {
        $order_html = '';
        if ($item['order']) {
            $order_html = ' <span class="v-table-sort-icon order_icon" field="' . $item['order'] . '"><i class="v-icon-up-dir"></i> <i class="v-icon-down-dir"></i></span>';
        }
        $table .= "<th field=\"{$item['field']}\">{$item['title']}{$order_html}</th>";
    });
    $table .= '</tr></thead><tbody>';
    //设置table内容
    array_walk($data, function ($item) use ($config, &$table) {
        $table .= '<tr>';
        array_walk($config, function ($config_item) use ($item, &$table) {
            $value = $item[$config_item['field']];
            $callback = $config_item['callback'];
            if (!is_null($callback)) {
                $value = $callback($value, $item);
            }
            $table .= "<td>{$value}</td>";
        });
        $table .= '</tr>';
    });
        $table .= <<<TABLE
</tbody></table></div>
TABLE;
    return $table;
    }
    /**
     * 获取页面的assign
     *
     * @access public
     *
     * @return array
     **/
    public function getAssign()
    {
        $table = $this->show();
        $page = $this->page_render;
        $script = $this->getScript();
        return compact('table', 'page', 'script');
    }
    /**
     * 为页面设置JS
     *
     * @access public
     *
     * @return string
     **/
    public function getScript()
    {
        if ($this->isOrder()) {
            $url = $this->deleteOrderParams();
            $def = $this->default_order_field;
            $field = I('get.order_field', '', 'trim');
            $type = I('get.order_type', $this->default_order_type, 'trim');
            return "tableOrder('{$def}', '{$url}', '{$field}', '{$type}');";
        }
        return '';
    }
    /**
     * 删除排序参数
     *
     * @access protected
     *
     * @return string
     **/
    protected function deleteOrderParams()
    {
        $url = parse_url($_SERVER['REQUEST_URI']);
        $path = $url['path'];
        $query = $url['query'];
        if (!empty($query)) {
            parse_str($query, $query_arr);
            if($query_arr['order_type']) {
                unset($query_arr['order_type']);
            }
            if ($query_arr['order_field']) {
                unset($query_arr['order_field']);
            }
        } else {
            $query_arr = [];
        }
        if (empty($query_arr)) {
            return $path;
        }
        return $path . '?' . http_build_query($query_arr);
    }
    /**
     * 是否存在排序规则
     *
     * @access protected
     *
     * @return boolean
     **/
    protected function isOrder()
    {
        $isOrder = false;
        array_walk($this->config, function ($item) use (&$isOrder) {
            if ($item['order']) {
                $isOrder = true;
            }
        });
        return $isOrder;
    }
}