<?php

namespace PreSales\Repositories;

use Common\Common\PhpExcelTrait;
use Common\Controller\DataAuthController;
use Common\Model\SystemUserModel;
use Common\ORG\Page;
use PreSales\Model\PreSalesCustomerModel;

class PreSalesCustomerRepository
{
    use PhpExcelTrait;
    protected $model;
    protected $list_rows = 15;
    public function __construct()
    {
        $this->model = new PreSalesCustomerModel();
    }
    /**
     * 列表页
     *
     * @access public
     *
     * @return array
     **/
    public function index()
    {
        //获取查询参数
        $searchParam = $this->getSearchParams();
        //查询条件
        $where = $this->getWhere($searchParam);
        //排序方式
        $order = $this->getOrder();
        //获取数据并进行分页
        $data = $this->getData($where, $order);
        //生成table页面
        $data = array_merge($data, $this->getTableHtml($data['data']));
        //获取页面的input
        $data['input'] = $this->getInput($searchParam);
        //获取统计数据
        $data['statistics'] = $this->getStatistics($where, $data['total']);
        //生成tab的字符串
        $data['tab'] = DataRepository::getTabHtml();
        return $data;
    }
    /**
     * 获取统计数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $total integer 统计的数据条数
     *
     * @return string
     **/
    protected function getStatistics($where, $total)
    {
        //签约量
        $where['customer_id'] = ['neq', ''];
        $sign_count = $this->model->where($where)->where(['delete_time' => 0])->where(DataAuthController::instance()->getPreSalesCustomerWhere())->count();
        //签约率
        $rate_sign = bcmul(bcdiv($sign_count, $total, 4), 100, 2) . '%';
        return "当前筛选出{$total}位客户，签约{$sign_count}位，签约率{$rate_sign}";
    }
    /**
     * 获取页面展示的Table
     *
     * @access protected
     * @param $data array 展示的数据
     *
     * @return array
     **/
    protected function getTableHtml($data)
    {
        $config = [
            ['field' => 0, 'title' => '客户ID', 'order' => false, 'callback' => null],
            ['field' => 1, 'title' => '客户名称', 'order' => false, 'callback' => null],
            ['field' => 2, 'title' => '公司名称', 'order' => false, 'callback' => null],
            ['field' => 3, 'title' => '区域', 'order' => false, 'callback' => null],
            ['field' => 4, 'title' => '商务跟进人', 'order' => false, 'callback' => null],
            ['field' => 5, 'title' => '公司类型', 'order' => false, 'callback' => null],
            ['field' => 6, 'title' => '首次跟进日期', 'order' => 'trace_time', 'callback' => null],
            ['field' => 7, 'title' => '签约日期', 'order' => 'sign_time', 'callback' => null],
            ['field' => 8, 'title' => '测试产品', 'order' => false, 'callback' => null],
            ['field' => 9, 'title' => '签约产品', 'order' => false, 'callback' => null],
            ['field' => 10, 'title' => '操作', 'order' => false, 'callback' => null],
        ];
        $tableObj = new TableRepository();
        $table = $tableObj->setData($data)->setConfig($config)->show();
        $script = $tableObj->setDefaultOrder('trace_time')->getScript();
        return compact('table', 'script');
    }
    /**
     * 获取数据并进行分页
     *
     * @access protected
     * @param $where array 查询条件
     * @param $order string 排序方式
     *
     * @return array ['render', 'data']
     **/
    protected function getData($where, $order)
    {
        $total = $this->model->where($where)->where(['delete_time' => '0'])->where(DataAuthController::instance()->getPreSalesCustomerWhere())->count();
        $page = new Page($total, $this->list_rows);
        $data = DataRepository::getPreSalesCustomerData($where, '*', $order, [$page->firstRow, $page->listRows]);
        $render = $page->show();
        if (empty($data)) {
            return compact('data', 'render', 'total');
        }
        //获取必要的数据
        #区域
        $region_arr = PreSalesCustomerModel::$region;
        #企业类型
        $company_type = DataRepository::getCompanyTypeData([], 'id, name');
        $company_type = array_column($company_type, 'name', 'id');
        #产品数据
        $product_data = DataRepository::getProductData([], 'product_id, product_name');
        $product_data = array_column($product_data, 'product_name', 'product_id');
        #签约产品
        $ps_customer_id = ['in', array_column($data, 'ps_customer_id')];
        $sign_product = DataRepository::getPreSalesCustomerProductData(compact('ps_customer_id'), 'ps_customer_id, product_id');
        $store_product = [];
        array_walk($sign_product, function ($item) use (&$store_product, $product_data) {
            $ps_customer_id = $item['ps_customer_id'];
            $store_product[$ps_customer_id][] = $product_data[$item['product_id']];
        });
        //获取用户数据
        $user_data = DataRepository::getManagerData([], 'username,realname');
        $user_data = array_column($user_data, 'realname', 'username');
        //数据进行遍历处理
        $data = array_map(function ($item) use ($company_type, $region_arr, $product_data, $store_product, $user_data) {
            $ps_customer_id = $item['ps_customer_id'];
            $ps_customer_name = $item['ps_customer_name'];
            $company = $item['company'];
            $region = $region_arr[$item['region']];
            $business_admin = $user_data[$item['business_admin']];
            $company_type_string = $company_type[$item['first_type']] . '--' . $company_type[$item['twice_type']];
            $trace_time = date('Y-m-d', $item['trace_time']);
            if ($item['sign_time']) {
                $sign_time = date('Y-m-d', $item['sign_time']);
            } else {
                $sign_time = '--';
            }
            //测试的产品
            if (empty($item['test_product'])) {
                $test_product = '--';
            } else {
                $test_product = array_map(function ($product_id) use ($product_data, $ps_customer_id) {
                    return '<a href="' . U('PreSalesProduct/index', ['product_id' => $product_id]) . '?ps_customer_id=' . $ps_customer_id . '">' . $product_data[$product_id] . '</a>';
                }, explode(',', $item['test_product']));
                $test_product = implode('<br/>', $test_product);
            }
            //开通的产品
            if (empty($store_product[$ps_customer_id])) {
                $sign_product = '--';
            } else {
                $sign_product = implode('<br/>', array_unique($store_product[$ps_customer_id]));
            }
            $operation = '';
            $operation .= '<a href="' . U('edit', ['ps_customer_id' => $ps_customer_id]) . '" class="btn btn-link">编辑</a>';
            //验证是否允许删除
            if ($this->allowDelete($item)) {
                $operation .= '<a href="' . U('del', ['ps_customer_id' => $ps_customer_id]) . '" onclick="return confirm(\'删除后将不可恢复，确定要删除本条数据？\')" class="btn btn-link">删除</a>';
            }
            $operation .= '<a href="' . U('PreSalesProduct/add', ['ps_customer_id' => $ps_customer_id]) . '?callback=' . urlencode($_SERVER['REQUEST_URI']) . '" class="btn btn-link">新增测试</a>';
            return [$ps_customer_id, $ps_customer_name, $company, $region, $business_admin, $company_type_string, $trace_time, $sign_time, $test_product, $sign_product, $operation];
        }, $data);
        return compact('data', 'render', 'total');
    }
    /**
     * 获取排序方式
     *
     * @access protected
     *
     * @return string
     **/
    protected function getOrder()
    {
        $order = 'trace_time desc';
        $order_field = I('get.order_field', '', 'trim');
        $order_type = I('get.order_type', '', 'trim');
        if ($order_field && $order_type) {
            $order = $order_field . ' ' . $order_type;
        } elseif ($order_field) {
            $order = $order_field;
        }
        return $order;
    }
    /**
     * 获取数据的查询条件
     *
     * @access $params array 查询参数
     *
     * @return array
     **/
    protected function getWhere($params)
    {
        $where = [];
        $ps_customer_id = [];
        //客户ID
        if (!empty($params['ps_customer_id'])) {
            $ps_customer_id[] = [$params['ps_customer_id']];
        }
        //客户名称
        if (!empty($params['ps_customer_name'])) {
            $where['ps_customer_name'] = ['like', "%{$params['ps_customer_name']}%"];
        }
        //公司名称
        if (!empty($params['company'])) {
            $where['company'] = ['like', "%{$params['company']}%"];
        }
        //测试产品
        if (!empty($params['test_product'])) {
            $product_id = $params['test_product'];
            //确定查询的已测试的产品是否存在
            if (in_array($product_id, PreSalesCustomerModel::$product_id)) {
                //根据查询的产品，实例化不同的Model
                $customer_id = DataRepository::getPreSalesProductResultData($product_id, [], 'ps_customer_id');
                //查询已经测试此产品的客户ID
                $ps_customer_id[] = array_values(array_unique(array_column($customer_id, 'ps_customer_id')));
            } else {
                //不可能的条件，必回返回空数据
                $where['id'] = 0;
            }
        }
        //区域
        if (!empty($params['region'])) {
            $where['region'] = $params['region'];
        }
        //商务跟进人
        if (!empty($params['business_admin'])) {
            $where['business_admin'] = $params['business_admin'];
        }
        //首次跟进日期
        if ($params['start_trace_time'] && $params['end_trace_time']) {
            $where['trace_time'] = ['between', [strtotime($params['start_trace_time']), strtotime($params['end_trace_time'] . ' 23:59:59')]];
        } elseif ($params['start_trace_time']) {
            $where['trace_time'] = ['egt', strtotime($params['start_trace_time'])];
        } elseif ($params['end_trace_time']) {
            $where['trace_time'] = ['elt', strtotime($params['end_trace_time'])];
        }
        //签约日期
        if ($params['start_sign_time'] && $params['end_sign_time']) {
            $where['sign_time'] = ['between', [strtotime($params['start_sign_time']), strtotime($params['end_sign_time'] . ' 23:59:59')]];
        } elseif ($params['start_sign_time']) {
            $where['sign_time'] = ['egt', strtotime($params['start_sign_time'])];
        } elseif ($params['end_sign_time']) {
            $where['sign_time'] = ['elt', strtotime($params['end_sign_time'])];
        }
        //签约产品
        if ($params['sign_product']) {
            $product_id = $params['sign_product'];
            $customer_id = DataRepository::getPreSalesCustomerProductData(compact('product_id'), 'ps_customer_id');
            $ps_customer_id[] = array_column($customer_id, 'ps_customer_id');
        }
        if (empty($ps_customer_id)) {
            return $where;
        }
        //ps_customer_id进行单独处理
        $ps_customer_id_where = [];
        array_walk($ps_customer_id, function ($item) use (&$ps_customer_id_where) {
            if (!empty($ps_customer_id_where)) {
                $ps_customer_id_where = array_intersect($ps_customer_id_where, $item);
            } else {
                $ps_customer_id_where = $item;
            }
        });
        if (count($ps_customer_id_where)==1) {
            $where['ps_customer_id'] = $ps_customer_id_where[0];
        } elseif (!empty($ps_customer_id_where)) {
            $where['ps_customer_id'] = ['in', $ps_customer_id_where];
        } else {
            $where['id'] = 0;
        }
        return $where;
    }
    /**
     * 获取页面查询的input
     *
     * @access protected
     * @param $params array 当前查询的参数
     *
     * @return array
     **/
    protected function getInput($params)
    {
        //测试产品选择框
        $testProductData = DataRepository::getProductData([
            'product_id'    => ['in', PreSalesCustomerModel::$product_id]
        ], 'product_id, product_name');
        $testProductOption = DataRepository::getOption(array_column($testProductData, 'product_name', 'product_id'), $params['test_product'], [
            ''  => '--全部--'
        ]);
        //签约产品
        $signProductData = DataRepository::getProductData(['back_status'   => 1], 'product_id, product_name');
        $signProductOption = DataRepository::getOption(array_column($signProductData, 'product_name', 'product_id'), $params['sign_product'], [], true);
        //区域
        $regionOption = DataRepository::getOption(PreSalesCustomerModel::$region, $params['region'], [
            ''  => '--全部--'
        ]);
        //客户ID
        $ps_customer_id = $params['ps_customer_id'];
        //客户名称
        $ps_customer_name = $params['ps_customer_name'];
        //公司名称
        $company = $params['company'];
        //商务跟进人
        $userData = DataRepository::getManagerData([], 'realname,username');
        $businessAdminOption = DataRepository::getOption(array_column($userData, 'realname', 'username'), $params['business_admin'], [], true);
        //首次跟进日期
        $start_trace_time = $params['start_trace_time'];
        $end_trace_time = $params['end_trace_time'];
        //签约日期
        $start_sign_time = $params['start_sign_time'];
        $end_sign_time = $params['end_sign_time'];
        return compact('testProductOption', 'signProductOption', 'regionOption', 'ps_customer_id',
            'ps_customer_name', 'company', 'businessAdminOption', 'start_trace_time', 'end_trace_time', 'start_sign_time', 'end_sign_time');
    }
    /**
     * 获取查询参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getSearchParams()
    {
        //客户ID
        $ps_customer_id = I('get.ps_customer_id', '', 'trim');
        //客户名称
        $ps_customer_name = I('get.ps_customer_name', '', 'trim');
        //公司名称
        $company = I('get.company', '', 'trim');
        //测试产品
        $test_product = I('get.test_product', '', 'trim');
        //签约产品
        $sign_product = I('get.sign_product', '', 'trim');
        //区域
        $region = I('get.region', '', 'trim');
        //商务跟进人
        $business_admin = I('get.business_admin', '', 'trim');
        //首次跟进日期
        $start_trace_time = I('get.start_trace_time', null, 'trim');
        $end_trace_time = I('get.end_trace_time', null, 'trim');
        if ($start_trace_time && $end_trace_time) {
            $trace_time = [strtotime($start_trace_time), strtotime($end_trace_time)];
            $start_trace_time = date('Y-m-d', min($trace_time));
            $end_trace_time = date('Y-m-d', max($trace_time));
        }
        //签约日期
        $start_sign_time = I('get.start_sign_time', null, 'trim');
        $end_sign_time = I('get.end_sign_time', null, 'trim');
        if ($start_sign_time && $end_sign_time) {
            $sign_time = [strtotime($start_sign_time), strtotime($end_sign_time)];
            $start_sign_time = date('Y-m-d', min($sign_time));
            $end_sign_time = date('Y-m-d', max($sign_time));
        }
        return compact('ps_customer_id', 'ps_customer_name', 'company', 'test_product', 'sign_product', 'region', 'business_admin', 'start_trace_time', 'end_trace_time', 'start_sign_time', 'end_sign_time');
    }

    /**
     * 增加页面
     *
     * @access public
     * @param $admin string 当前用户的账号
     *
     * @return array
     **/
    public function add_html()
    {
        //设计区域的option
        $regionOption = DataRepository::getOption(PreSalesCustomerModel::$region);
        //商务跟进人
        $userData = DataRepository::getManagerData([], 'username,realname');
        $userOption = DataRepository::getOption(array_column($userData, 'realname', 'username'), $_SESSION['site_login_name']);
        //公司类型
        $firstTypeData = DataRepository::getCompanyTypeData([
            'parent_id' => 0
        ], 'id, name');
        $firstTypeOption = DataRepository::getOption(array_column($firstTypeData, 'name', 'id'));
        $twiceTypeData = DataRepository::getCompanyTypeData([
            'parent_id' => $firstTypeData[0]['id']
        ], 'id, name');
        $twiceTypeOption = DataRepository::getOption(array_column($twiceTypeData, 'name', 'id'));
        return compact('regionOption', 'userOption', 'firstTypeOption', 'twiceTypeOption');
    }
    /**
     * 增加数据
     *
     * @access protected
     *
     * @return void
     **/
    public function add()
    {
        //监听是否为查询二级菜单的请求
        $this->listen();
        //获取增加的数据
        $data = $this->getPostData();
        //补充缺失的数据
        $data = array_merge($data, $this->fillPostData());
        //增加数据
        $res = $this->model->add($data);
        $validate = new ValidateRepository();
        if ($res) {
            $validate->successResponse('', 0, ['ps_customer_id' => $data['ps_customer_id']]);
        }
        $validate->errorResponse('增加数据时出现错误' . $this->model->getError());
    }
    /**
     * 补充需要增加的数据
     *
     * @access protected
     * @param $type string 补充数据的方式
     *
     * @return array
     **/
    protected function fillPostData($type = 'add')
    {
        //售前客户ID
        $date = date('Ymd');
        $ps_customer_id = 'OC' . $date;
        $create_time = strtotime($date);
        $count = $this->model->where([
            'create_time'   => ['egt', $create_time]
        ])->count();
        $ps_customer_id .= str_pad($count+1, '3', '0', STR_PAD_LEFT);
        //创建时间
        $update_time = $create_time = time();
        //创建人
        $update_admin = $create_admin = $_SESSION['site_login_name'];
        if ($type=='add') {
            return compact('ps_customer_id', 'update_admin', 'create_admin', 'create_time', 'update_time');
        }
        return compact('update_admin', 'update_time');
    }
    /**
     * 获取POST数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getPostData()
    {
        $validate = new ValidateRepository();
        $delete_time = 0;
        $ps_customer_id = I('post.ps_customer_id', '', 'trim');
        $where = [];
        if (!empty($ps_customer_id)) {
            $where['ps_customer_id'] = ['neq', $ps_customer_id];
        }
        //客户名称
        $ps_customer_name = I('post.ps_customer_name', '', 'trim');
        if ($validate->is_empty($ps_customer_name)) {
            $validate->errorResponse('客户名称为必填项');
        }
        //唯一性验证
        $count = $this->model->where(array_merge($where, compact('ps_customer_name', 'delete_time')))->count();
        if ($count) {
            $validate->errorResponse('该客户已存在，请重新核对后提交');
        }
        //公司名称
        $company = I('post.company', '', 'trim');
        if ($validate->is_empty($company)) {
            $validate->errorResponse('公司名称为必填项');
        }
        //唯一性验证
        $count = $this->model->where(array_merge($where, compact('company', 'delete_time')))->count();
        if($count) {
            $validate->errorResponse('该公司名称已存在，请重新核对后提交');
        }
        //区域分布
        $region = I('post.region', '', 'trim');
        if ($validate->is_empty($region)) {
            $validate->errorResponse('区域分布为必填项');
        }
        if (!PreSalesCustomerModel::$region[$region]) {
            $validate->errorResponse('区域分布的值为未知枚举类型');
        }
        //商务跟进人
        $business_admin = I('post.business_admin', '', 'trim');
        if ($validate->is_empty($business_admin)) {
            $validate->errorResponse('商务跟进人为必填项');
        }
        $userData = DataRepository::getManagerData([], 'username');
        if (!in_array($business_admin, array_column($userData, 'username'))) {
            $validate->errorResponse('商务跟进人的值为未知枚举类型');
        }
        //公司类型
        $first_type = I('post.first_type', '', 'trim');
        if ($validate->is_empty($first_type)) {
            $validate->errorResponse('公司类型（一级）为必填项');
        }
        $twice_type = I('post.twice_type', '', 'trim');
        if ($validate->is_empty($twice_type)) {
            $validate->errorResponse('公司类型（二级）为必填项');
        }
        $twiceData = DataRepository::getCompanyTypeData([
            'parent_id' => $first_type
        ], 'id');
        if (!in_array($twice_type, array_column($twiceData, 'id'))) {
            $validate->errorResponse('公司类型中一级与二级不匹配');
        }
        //首次跟进日期
        $trace_time = I('post.trace_time', '', 'trim');
        if ($validate->is_empty($trace_time)) {
            $validate->errorResponse('首次跟进日期为必填项');
        }
        if (!$validate->is_date($trace_time)) {
            $validate->errorResponse('首次跟进日期必须为日期格式');
        }
        $trace_time = strtotime($trace_time);
        return compact('ps_customer_name', 'company', 'region', 'business_admin', 'first_type', 'twice_type', 'trace_time');
    }
    /**
     * 监听请求是否为请求二级菜单
     *
     * @access protected
     *
     * @return void
     **/
    protected function listen()
    {
        $type = I('get.request_type', '', 'trim');
        if ($type == 'company_type') {
            $this->getTwiceOptionForHtml();
        }
    }
    /**
     * 为页面获取二级类型的Option
     *
     * @access protected
     *
     * @return void
     **/
    protected function getTwiceOptionForHtml()
    {
        $validate = new ValidateRepository();
        $first_type = I('post.first_type', '', 'trim');
        if (empty($first_type)) {
            $validate->errorResponse('一级类型为NULL');
        }
        $data = DataRepository::getCompanyTypeData([
            'parent_id' => $first_type
        ], 'id,name');
        $data = DataRepository::getOption(array_column($data, 'name', 'id'));
        $validate->successResponse('', 0, $data);
    }
    /**
     * 获取编辑的页面
     *
     * @access public
     *
     * @return array
     **/
    public function edit_html()
    {
        $ps_customer_id = I('get.ps_customer_id', '', 'trim');
        //数据权限校验
        DataAuthController::instance()->validAllowDoPreSalesCustomer($ps_customer_id);
        //获取数据
        $data = DataRepository::getPreSalesCustomerData(compact('ps_customer_id'));
        if (empty($data)) {
            throw new \Exception('你所查询的数据不存在，或已被删除');
        }
        $data = $data[0];
        //设计区域的option
        $regionOption = DataRepository::getOption(PreSalesCustomerModel::$region, $data['region']);
        //商务跟进人
        $userData = DataRepository::getManagerData([], 'username,realname');
        $userOption = DataRepository::getOption(array_column($userData, 'realname', 'username'), $data['business_admin']);
        //公司类型
        $firstTypeData = DataRepository::getCompanyTypeData([
            'parent_id' => 0
        ], 'id, name');
        $firstTypeOption = DataRepository::getOption(array_column($firstTypeData, 'name', 'id'), $data['first_type']);
        $twiceTypeData = DataRepository::getCompanyTypeData([
            'parent_id' => $firstTypeData[0]['id']
        ], 'id, name');
        $twiceTypeOption = DataRepository::getOption(array_column($twiceTypeData, 'name', 'id'), $data['twice_type']);
        return compact('regionOption', 'userOption', 'firstTypeOption', 'twiceTypeOption', 'ps_customer_id', 'data');
    }
    /**
     * 编辑数据
     *
     * @access protected
     *
     * @return void
     **/
    public function edit()
    {
        //监听是否为查询二级菜单的请求
        $this->listen();
        //获取修改的依据
        $ps_customer_id = I('post.ps_customer_id', '', 'trim');

        //数据权限校验
        DataAuthController::instance()->validAllowDoPreSalesCustomer($ps_customer_id);

        //获取增加的数据
        $data = $this->getPostData();
        //补充缺失的数据
        $data = array_merge($data, $this->fillPostData('edit'));
        //编辑数据
        $res = $this->model->where(compact('ps_customer_id'))->save($data);
        $validate = new ValidateRepository();
        if ($res) {
            $validate->successResponse();
        }
        $validate->errorResponse('修改数据时出现错误' . $this->model->getError());
    }
    /**
     * excel导出
     *
     * @access public
     *
     * @return void
     **/
    public function file()
    {
        //获取查询参数
        $params = $this->getSearchParams();
        //获取查询条件
        $where = $this->getWhere($params);
        //排序方式
        $order = $this->getOrder();
        //获取数据
        $data = $this->getDataForFile($where, $order);
        //设置Excel
        $this->excel($data);
    }
    /**
     * 获取数据并组装（excel导出）
     *
     * @access protected
     * @param $where array 查询条件
     * @param $order string 排序方式
     * @param $is_page boolean 是否排序
     *
     * @return array ['render', 'data']
     **/
    protected function getDataForFile($where, $order)
    {
        //获取数据
        $data = DataRepository::getPreSalesCustomerData($where, '*', $order);
        if (empty($data)) {
            return [];
        }
        //获取必要的数据
        #区域
        $region_arr = PreSalesCustomerModel::$region;
        #企业类型
        $company_type = DataRepository::getCompanyTypeData([], 'id, name');
        $company_type = array_column($company_type, 'name', 'id');
        #产品数据
        $product_data = DataRepository::getProductData([], 'product_id, product_name');
        $product_data = array_column($product_data, 'product_name', 'product_id');
        #签约产品
        $ps_customer_id = ['in', array_column($data, 'ps_customer_id')];
        $sign_product = DataRepository::getPreSalesCustomerProductData(compact('ps_customer_id'), 'ps_customer_id, product_id');
        $store_product = [];
        array_walk($sign_product, function ($item) use (&$store_product, $product_data) {
            $ps_customer_id = $item['ps_customer_id'];
            $store_product[$ps_customer_id][] = $product_data[$item['product_id']];
        });
        //数据进行遍历处理
        $data = array_map(function ($item) use ($company_type, $region_arr, $product_data, $store_product) {
            $ps_customer_id = $item['ps_customer_id'];
            $ps_customer_name = $item['ps_customer_name'];
            $company = $item['company'];
            $region = $region_arr[$item['region']];
            $business_admin = $item['business_admin'];
            $company_type_string = $company_type[$item['first_type']] . '--' . $company_type[$item['twice_type']];
            $trace_time = date('Y-m-d', $item['trace_time']);
            if ($item['sign_time']) {
                $sign_time = date('Y-m-d', $item['sign_time']);
            } else {
                $sign_time = '--';
            }
            //测试的产品
            if (empty($item['test_product'])) {
                $test_product = '--';
            } else {
                $test_product = array_map(function ($product_id) use ($product_data) {
                    return $product_data[$product_id];
                }, explode(',', $item['test_product']));
                $test_product = implode(' | ', $test_product);
            }
            //开通的产品
            if (empty($store_product[$ps_customer_id])) {
                $sign_product = '--';
            } else {
                $sign_product = implode(' | ', $store_product[$ps_customer_id]);
            }
            return [$ps_customer_id, $ps_customer_name, $company, $region, $business_admin, $company_type_string, $trace_time, $sign_time, $test_product, $sign_product];
        }, $data);
        return $data;
    }
    /**
     * 设置Excel导出的文件
     *
     * @access protected
     * @param $data array 数据
     *
     * @return void
     **/
    protected function excel($data)
    {
        //初始化Excel
        $this->file_out_init();
        //设置标题
        $title = ['客户ID', '客户名称', '公司名称', '区域', '商务跟进人', '公司类型', '首次跟进日期', '签约日期', '测试产品', '签约产品'];
        $this->addRowContent($title, 18, true)->setWidth([18, 18, 18, 18, 20, 26, 24, 18, 18, 18]);
        //设置内容
        array_walk($data, function ($item) {
            $this->addRowContent($item, 16);
        });
        //下载文件
        $filename = '客户测试产品';
        $this->download($filename);
    }
    /**
     * 删除数据
     *
     * @access protected
     *
     * @return void
     **/
    public function del()
    {
        $ps_customer_id = I('get.ps_customer_id');
        //数据权限校验
        DataAuthController::instance()->validAllowDoPreSalesCustomer($ps_customer_id);

        //验证该条数据是否允许编辑删除
        if (!$this->allowDelete(null, $ps_customer_id)) {
            throw new \Exception('该条数据不可删除');
        }
        $res = $this->model->where(compact('ps_customer_id'))->save([
            'delete_time'   => time()
        ]);
        if (!$res) {
            throw new \Exception('删除失败');
        }
    }
    /**
     * 验证某条数据是否可以编辑、删除
     *
     * @access protected
     * @param $data array 数据
     * @param $id string 数据ID
     *
     * @return boolean
     **/
    protected function allowDelete($data, $ps_customer_id = '')
    {
        if (empty($data)) {
            $data = DataRepository::getPreSalesCustomerData(compact('ps_customer_id'), 'customer_id,test_product');
            if (empty($data)) {
                return false;
            }
            $data = $data[0];
        }
        if (empty($data['customer_id']) && empty($data['test_product'])) {
            return true;
        }
        return false;
    }
    /**
     * 监听增加测试数据
     *  目的是为了增加测试客户表中的测试数据
     *
     * @access public
     * @param $product_id integer 产品ID
     * @param $ps_customer_id string 客户ID
     *
     * @return void
     **/
    public function listenAddTestProduct($product_id, $ps_customer_id)
    {
        $data = DataRepository::getPreSalesCustomerData(compact('ps_customer_id'), 'test_product');
        $test_product = $data[0]['test_product'];
        $test_product_arr = explode(',', $test_product);
        if (empty($test_product)) {
            $test_product = [$product_id];
            $test_product = implode(',', $test_product);
            $this->model->where(compact('ps_customer_id'))->save([
                'test_product'  => $test_product
            ]);
        } elseif (!in_array($product_id, $test_product_arr)) {
            $test_product_arr[] = $product_id;
            $test_product = implode(',', $test_product_arr);
            $this->model->where(compact('ps_customer_id'))->save([
                'test_product'  => $test_product
            ]);
        }
    }
    /**
     * 获取所有未注册的测试用户的SELECT选择项
     *
     * @access public
     *
     * @return array
     **/
    public function getNotSignPreCustomer()
    {
        $sign_time = 0;
        $data = $this->model->field('ps_customer_id, ps_customer_name, company')->where(compact('sign_time'))->select();
        return array_column($data, null, 'ps_customer_id');
    }
    /**
     * 获取注册客户的售前测试客户数据
     *
     * @access public
     * @param $customer_id string 注册的客户ID
     * @param $field string 字段名
     *
     * @return array
     **/
    public function getPreCustomerBySignCustomerId($customer_id, $field = '*')
    {
        return $this->model->where(compact('customer_id'))->field($field)->find();
    }
    /**
     * 开通某个售前测试客户
     *
     * @access public
     * @param $customer_id string 开通的客户ID
     * @param $ps_customer_id string 测试售前客户ID
     *
     * @return void
     **/
    public function setSignCustomerId($customer_id, $ps_customer_id)
    {
        if (empty($ps_customer_id)) {
            return;
        }
        $sign_time = 0;
        $data = [
            'customer_id'   => $customer_id,
            'sign_time'     => time()
        ];
        $this->model->where(compact('sign_time', 'ps_customer_id'))->save($data);
    }
}