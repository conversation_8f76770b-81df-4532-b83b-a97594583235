<?php

namespace PreSales\Repositories;


//测试结果页需要实现的方法
use Think\Model;

interface PreSalesResultRepository
{
    /**
     * 获取测试数据的html
     *
     * @access public
     *
     * @return void
     **/
    public function add_html();
    /**
     * 获取测试数据的html（编辑）
     *
     * @access public
     * @param $data array 测试结果数据
     *
     * @return string
     **/
    public function edit_html($data);
    /**
     * 获取增加的数据
     *
     * @access public
     *
     * @return array
     **/
    public function getAddData();
    /**
     * 获取当前的产品的Model
     *
     * @access public
     *
     * @return Model
     **/
    public function getModel();
    /**
     * 获取首页的数据
     *
     * @access public
     *
     * @return array
     **/
    public function index();
    /**
     * 获取excel导出的所需数据
     *
     * @access public
     *
     * @return array
     **/
    public function file_data();
}