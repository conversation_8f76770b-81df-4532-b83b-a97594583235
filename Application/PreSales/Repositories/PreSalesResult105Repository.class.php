<?php

namespace PreSales\Repositories;

use Common\ORG\Page;
use PreSales\Model\PreSalesCustomerModel;
use PreSales\Model\PreSalesPubResultModel;
use PreSales\Model\PreSalesResult105Model;

class PreSalesResult105Repository implements PreSalesResultRepository
{
    protected $list_rows = 15;
    protected $model;
    protected $product_id = 105;
    protected $table_name = 'pre_sales_result105';
    public function __construct()
    {
        $this->model = new PreSalesResult105Model();
    }
    public function getModel()
    {
        return $this->model;
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        列表页及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function index()
    {
        //获取查询参数
        $params = $this->getSearchParams();
        //获取页面的input查询域
        $input = $this->getInput($params);
        //获取查询条件
        $where = $this->getWhere($params);
        //排序方式
        $order = $this->getOrder();
        //获取数据并进行分页
        $data = $this->getData($where, $order);
        $data['input'] = $input;
        //获取Table的html
        $table = new TableRepository();
        $data['table'] = $table->setData($data['data'])->setConfig([
            ['field' => 'ps_customer_name', 'title' => '客户名称', 'order' => false, 'callback' => null],
            ['field' => 'company', 'title' => '公司名称', 'order' => false, 'callback' => null],
            ['field' => 'region', 'title' => '区域', 'order' => false, 'callback' => null],
            ['field' => 'company_type', 'title' => '公司类型', 'order' => false, 'callback' => null],
            ['field' => 'submit_time', 'title' => '提交测试日期', 'order' => 'pre_sales_pub_result.submit_time', 'callback' => null],
            ['field' => 'return_time', 'title' => '返回结果日期', 'order' => 'pre_sales_pub_result.return_time', 'callback' => null],
            ['field' => 'test_phase', 'title' => '测试期数', 'order' => 'pre_sales_pub_result.test_phase', 'callback' => null],
            ['field' => 'test_days', 'title' => '测试天数', 'order' => 'pre_sales_pub_result.test_days', 'callback' => null],
            ['field' => 'test_num', 'title' => '测试总数量', 'order' => 'pre_sales_pub_result.test_num', 'callback' => null],
            ['field' => 'rate_good', 'title' => '好样本数据占比', 'order' => $this->table_name . '.rate_good', 'callback' => null],
            ['field' => 'rate_sure', 'title' => '催收命中占比', 'order' => $this->table_name . '.rate_sure', 'callback' => null],
            ['field' => 'rate_notsure', 'title' => '疑似催收命中占比', 'order' => $this->table_name . '.rate_notsure', 'callback' => null],
            ['field' => 'rate_accord', 'title' => '只有主叫', 'order' => $this->table_name . '.rate_accord', 'callback' => null],
            ['field' => 'rate_passivity', 'title' => '只有被叫', 'order' => $this->table_name . '.rate_passivity', 'callback' => null],
            ['field' => 'rate_sure_five', 'title' => '确认催收5个以上', 'order' => $this->table_name . '.rate_sure_five', 'callback' => null],
            ['field' => 'rate_sure_ten', 'title' => '确认催收10个以上', 'order' => $this->table_name . '.rate_sure_ten', 'callback' => null],
            ['field' => 'test_progress', 'title' => '测试进度', 'order' => false, 'callback' => null],
            ['field' => 'test_result', 'title' => '客户反馈', 'order' => false, 'callback' => null],
            ['field' => 'remark', 'title' => '备注', 'order' => false, 'callback' => null],
            ['field' => 'operator', 'title' => '操作', 'order' => false, 'callback' => null],
        ])->show();
        //获取JS
        $data['script'] = $table->setDefaultOrder('pre_sales_pub_result.submit_time')->getScript();
        //获取统计内容
        $data['statistics'] = $this->getStatistics($where);
        return $data;
    }
    /**
     * 获取统计内容
     *
     * @access protected
     * @param $where array 查询条件
     *
     * @return string
     **/
    protected function getStatistics($where)
    {
        $total = $this->model
            ->join('pre_sales_pub_result ON pre_sales_pub_result.id = ' . $this->table_name . '.pub_result_id')
            ->join('pre_sales_customer ON ' . $this->table_name . '.ps_customer_id = pre_sales_customer.ps_customer_id', 'left')
            ->where($where)->where('rate_good is not null')->count();
        $count = $this->model
            ->join('pre_sales_pub_result ON pre_sales_pub_result.id = ' . $this->table_name . '.pub_result_id')
            ->join('pre_sales_customer ON ' . $this->table_name . '.ps_customer_id = pre_sales_customer.ps_customer_id', 'left')
            ->where($where)->where([
            'rate_good' => ['egt', 80]
        ])->count();
        $rate = $total?bcmul(bcdiv($count, $total, 4), 100, 2) . '%':'NA';
        return "当前筛选出{$total}条数据，好样样本占比80%以上客户占比{$rate}<b>（注意：此处的数据条数不等于列表中展示的总条数）</b>";
    }
    /**
     * 分页获取当前页面的数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function getData($where, $order)
    {
        //获取总数
        $total = $this->model
            ->join('pre_sales_pub_result ON pre_sales_pub_result.id = ' . $this->table_name . '.pub_result_id')
            ->join('pre_sales_customer ON ' . $this->table_name . '.ps_customer_id = pre_sales_customer.ps_customer_id', 'left')
            ->where($where)->count();
        $page = new Page($total, $this->list_rows);
        $data = DataRepository::getFullPreSalesProductResultData($this->product_id, $where, '*,' . $this->table_name . '.id AS product_result_id', $order, [$page->firstRow, $page->listRows]);
        $render = $page->show();
        if (empty($data)) {
            return compact('data', 'render', 'total');
        }
        //获取必要的数据
        #企业类型
        $company_type = DataRepository::getCompanyTypeData([], 'id, name');
        $company_type = array_column($company_type, 'name', 'id');
        #区域
        $region_arr = PreSalesCustomerModel::$region;
        #测试进度
        $progress_arr = PreSalesPubResultModel::$progress;
        $data = array_map(function ($item) use($company_type, $region_arr, $progress_arr) {
            $result = [];
            $result['ps_customer_name'] = $item['ps_customer_name'];
            $result['company'] = $item['company'];
            $result['region'] = $region_arr[$item['region']];
            $result['company_type'] = $company_type[$item['first_type']] . '--' . $company_type[$item['twice_type']];
            $result['submit_time'] = date('Y-m-d', $item['submit_time']);
            $result['return_time'] = $item['return_time']?date('Y-m-d', $item['return_time']):'--';
            $result['test_phase'] = $item['test_phase'];
            $result['test_days'] = $item['test_days']?$item['test_days']:'--';
            $result['test_num'] = $item['test_num']?$item['test_num']:'--';
            $result['rate_good'] = $item['rate_good']?$item['rate_good'] . '%':'--';
            $result['rate_sure'] = $item['rate_sure']?$item['rate_sure'] . '%':'--';
            $result['rate_notsure'] = $item['rate_notsure']?$item['rate_notsure'] . '%':'--';
            $result['rate_accord'] = $item['rate_accord']?$item['rate_accord'] . '%':'--';
            $result['rate_passivity'] = $item['rate_passivity']?$item['rate_passivity'] . '%':'--';
            $result['rate_sure_five'] = $item['rate_sure_five']?$item['rate_sure_five'] . '%':'--';
            $result['rate_sure_ten'] = $item['rate_sure_ten']?$item['rate_sure_ten'] . '%':'--';
            $result['test_progress'] = $progress_arr[$item['test_progress']];
            if (mb_strlen($item['test_result'], 'utf8')>5) {
                $result['test_result'] = mb_substr($item['test_result'], 0, 5, 'utf8') . <<<A
<a tabindex="0" role="button" data-toggle="popover" data-placement="left" data-trigger="focus" title="客户反馈详情" data-content="{$item['test_result']}">...</a>
A;
            } else {
                $result['test_result'] = $item['test_result'];
            }
            if (mb_strlen($item['remark'],'utf8')>5) {
                $result['remark'] = mb_substr($item['remark'], 0, 5, 'utf8') . <<<A
<a tabindex="0" role="button" data-toggle="popover" data-placement="left" data-trigger="focus" title="备注详情" data-content="{$item['remark']}">...</a>
A;
            } else {
                $result['remark'] = $item['remark'];
            }
            $result['operator'] = '<a href="' . U('edit', [
                    'product_id'    => $this->product_id,
                    'id'            => $item['product_result_id']
                ]) . '?callback=' . urlencode($_SERVER['REQUEST_URI']) . '"><nobr>编辑</nobr></a>';
            return $result;
        }, $data);
        return compact('data', 'render', 'total');
    }
    /**
     * 获取查询的排序字段
     *
     * @access protected
     *
     * @return string
     **/
    protected function getOrder()
    {
        $order = 'pre_sales_pub_result.submit_time desc';
        $order_field = I('get.order_field', '', 'trim');
        $order_type = I('get.order_type', '', 'trim');
        if ($order_field && $order_type) {
            $order = $order_field . ' ' . $order_type;
        } elseif ($order_field) {
            $order = $order_field;
        }
        return $order;
    }
    /**
     * 获取查询参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getSearchParams()
    {
        //客户ID
        $ps_customer_id = I('get.ps_customer_id', '', 'trim');
        //客户名称
        $ps_customer_name = I('get.ps_customer_name', '', 'trim');
        //公司名称
        $company = I('get.company', '', 'trim');
        //区域
        $region = I('get.region', '', 'trim');
        //二级类型
        $twice_type = I('get.twice_type', '', 'trim');
        //提交测试日期
        $start_submit_time = I('get.start_submit_time', null, 'trim');
        $end_submit_time = I('get.end_submit_time', null, 'trim');
        if ($start_submit_time && $end_submit_time) {
            $submit_time = [strtotime($start_submit_time), strtotime($end_submit_time)];
            $start_submit_time = date('Y-m-d', min($submit_time));
            $end_submit_time = date('Y-m-d', max($submit_time));
        }
        //返回结果日期
        $start_return_time = I('get.start_return_time', null, 'trim');
        $end_return_time = I('get.end_return_time', null, 'trim');
        if ($start_return_time && $end_return_time) {
            $return_time = [strtotime($start_return_time), strtotime($end_return_time)];
            $start_return_time = date('Y-m-d', min($return_time));
            $end_return_time = date('Y-m-d', max($return_time));
        }
        //测试进度
        $test_progress = I('get.test_progress', '', 'trim');
        return compact('ps_customer_id', 'ps_customer_name', 'company', 'region', 'business_admin', 'start_return_time',
            'start_submit_time', 'end_return_time', 'end_submit_time', 'twice_type', 'test_progress');
    }
    /**
     * 获取查询条件
     *
     * @access protected
     * @param $params array 查询参数
     *
     * @return array
     **/
    protected function getWhere($params)
    {
        $where = [];
        //客户ID
        if (!empty($params['ps_customer_id'])) {
            $where['pre_sales_customer.ps_customer_id'] = $params['ps_customer_id'];
        }
        //客户名称
        if (!empty($params['ps_customer_name'])) {
            $where['pre_sales_customer.ps_customer_name'] = ['like', "%{$params['ps_customer_name']}%"];
        }
        //公司名称
        if (!empty($params['company'])) {
            $where['pre_sales_customer.company'] = ['like', "%{$params['company']}%"];
        }
        //区域
        if (!empty($params['region'])) {
            $where['pre_sales_customer.region'] = $params['region'];
        }
        //公司类型
        if (!empty($params['twice_type'])) {
            $where['pre_sales_customer.twice_type'] = $params['twice_type'];
        }
        //提交测试日期
        if ($params['start_submit_time'] && $params['end_submit_time']) {
            $where['pre_sales_pub_result.submit_time'] = ['between', [strtotime($params['start_submit_time']), strtotime($params['end_submit_time'] . ' 23:59:59')]];
        } elseif ($params['start_submit_time']) {
            $where['pre_sales_pub_result.submit_time'] = ['egt', strtotime($params['start_submit_time'])];
        } elseif ($params['end_submit_time']) {
            $where['pre_sales_pub_result.submit_time'] = ['elt', strtotime($params['end_submit_time'])];
        }
        //返回结果日期
        if ($params['start_return_time'] && $params['end_return_time']) {
            $where['pre_sales_pub_result.return_time'] = ['between', [strtotime($params['start_return_time']), strtotime($params['end_return_time'] . ' 23:59:59')]];
        } elseif ($params['start_return_time']) {
            $where['pre_sales_pub_result.return_time'] = ['egt', strtotime($params['start_return_time'])];
        } elseif ($params['end_return_time']) {
            $where['pre_sales_pub_result.return_time'] = ['elt', strtotime($params['end_return_time'])];
        }
        //测试进度
        if (!empty($params['test_progress'])) {
            $where['pre_sales_pub_result.test_progress'] = $params['test_progress'];
        }
        return $where;
    }
    /**
     * 获取页面查询的文本域
     *
     * @access protected
     * @param $params array 参数
     *
     * @return string
     **/
    protected function getInput($params)
    {
        $display = '';
        //客户ID
        $display .= <<<HTML
<div class="form-group">
    <label for="ps_customer_id">客户ID</label>
    <input type="text" value="{$params['ps_customer_id']}" class="form-control" id="ps_customer_id" name="ps_customer_id" placeholder="请填写客户ID" maxlength="13" />
</div>
HTML;
        //客户名称
        $display .= <<<HTML
<div class="form-group">
    <label for="ps_customer_name">客户名称</label>
    <input type="text" value="{$params['ps_customer_name']}" class="form-control" id="ps_customer_name" name="ps_customer_name" placeholder="请填写客户名称" maxlength="30" />
</div>
HTML;
        //公司名称
        $display .= <<<HTML
<div class="form-group">
    <label for="company">公司名称</label>
    <input type="text" value="{$params['company']}" class="form-control" id="company" name="company" placeholder="请填写公司名称" maxlength="50" />
</div>
HTML;
        //区域
        $regionOption = DataRepository::getOption(PreSalesCustomerModel::$region, $params['region'], ['' => '--选择区域--']);
        $display .= <<<HTML
<div class="form-group">
    <label for="region">区域</label>
    <select class="form-control" id="region" name="region">
        {$regionOption}
    </select>
</div>
HTML;
        //公司类型
        $companyTypeOption = DataRepository::getCompanyTypeOption($params['twice_type']);
        $display .= <<<HTML
<div class="form-group">
    <label for="twice_type">公司类型</label>
    <select class="form-control" id="twice_type" name="twice_type">
        {$companyTypeOption}
    </select>
</div>
HTML;
        //提交测试日期
        $display .= <<<HTML
<div class="form-group">
    <label for="start_trace_time">提交测试日期</label>
    <input class="form-control" value="{$params['start_submit_time']}" type="date" id="start_submit_time" name="start_submit_time" />
    -
    <input class="form-control" value="{$params['end_submit_time']}" type="date" id="end_submit_time" name="end_submit_time" />
</div>
HTML;
        //返回结果日期
        $display .= <<<HTML
<div class="form-group">
    <label for="start_return_time">返回结果日期</label>
    <input class="form-control" value="{$params['start_return_time']}" type="date" id="start_return_time" name="start_return_time" />
    -
    <input class="form-control" value="{$params['end_return_time']}" type="date" id="end_return_time" name="end_return_time" />
</div>
HTML;
        //测试进度
        $progressOption = DataRepository::getOption(PreSalesPubResultModel::$progress, $params['test_progress'], ['' => '--选择测试进度--']);
        $display .= <<<HTML
<div class="form-group">
    <label for="test_progress">测试进度</label>
    <select class="form-control" id="test_progress" name="test_progress">
        {$progressOption}
    </select>
</div>
HTML;
        return $display;
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        增加功能及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function add_html()
    {
        $input = <<<INPUT
<div class="form-group">
    <label for="rate_good" class="col-sm-2 control-label">
        好样本占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入好样本占比" id="rate_good" name="rate_good" aria-describedby="basic-addon3" maxlength="5">
            <span class="input-group-addon" id="basic-addon3">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_sure" class="col-sm-2 control-label">
        催收命中占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入催收命中占比" id="rate_sure" name="rate_sure" aria-describedby="basic-addon4" maxlength="5">
            <span class="input-group-addon" id="basic-addon4">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_notsure" class="col-sm-2 control-label">
        疑似催收命中占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入疑似催收命中占比" id="rate_notsure" name="rate_notsure" aria-describedby="basic-addon5" maxlength="5">
            <span class="input-group-addon" id="basic-addon5">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_accord" class="col-sm-2 control-label">
        只有主叫：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入只有主叫占比" id="rate_accord" name="rate_accord" aria-describedby="basic-addon6" maxlength="5">
            <span class="input-group-addon" id="basic-addon6">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_passivity" class="col-sm-2 control-label">
        只有被叫：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入只有被叫占比" id="rate_passivity" name="rate_passivity" aria-describedby="basic-addon7" maxlength="5">
            <span class="input-group-addon" id="basic-addon7">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_sure_five" class="col-sm-2 control-label">
        确认催收5个以上：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入确认催收5个以上占比" id="rate_sure_five" name="rate_sure_five" aria-describedby="basic-addon8" maxlength="5">
            <span class="input-group-addon" id="basic-addon8">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_sure_ten" class="col-sm-2 control-label">
        确认催收10个以上：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="请输入确认催收10个以上占比" id="rate_sure_ten" name="rate_sure_ten" aria-describedby="basic-addon10" maxlength="5">
            <span class="input-group-addon" id="basic-addon10">%</span>
        </div>
    </div>
</div>
INPUT;
        $validate = new ValidateRepository();
        $validate->successResponse('', 0, compact('input'));
    }
    public function getAddData()
    {
        $validate = new ValidateRepository();
        //好样本占比
        $rate_good = I('post.rate_good', '', 'trim');
        $rate_good = $validate->valid_rate($rate_good, '好样本占比格式不正确');
        //催收命中占比
        $rate_sure = I('post.rate_sure', '', 'trim');
        $rate_sure = $validate->valid_rate($rate_sure, '催收命中占比格式不正确');
        //疑似催收命中占比
        $rate_notsure = I('post.rate_notsure', '', 'trim');
        $rate_notsure = $validate->valid_rate($rate_notsure, '疑似催收命中占比格式不正确');
        //只有主叫占比
        $rate_accord = I('post.rate_accord', '', 'trim');
        $rate_accord = $validate->valid_rate($rate_accord, '只有主叫占比格式不正确');
        //只有被叫占比
        $rate_passivity = I('post.rate_passivity', '', 'trim');
        $rate_passivity = $validate->valid_rate($rate_passivity, '只有被叫占比格式不正确');
        //确认催收5个以上占比
        $rate_sure_five = I('post.rate_sure_five', '', 'trim');
        $rate_sure_five = $validate->valid_rate($rate_sure_five, '确认催收5个以上占比格式不正确');
        //确认催收10个以上占比
        $rate_sure_ten = I('post.rate_sure_ten', '', 'trim');
        $rate_sure_ten = $validate->valid_rate($rate_sure_ten, '确认催收10个以上占比格式不正确');
        return compact('rate_good', 'rate_sure', 'rate_notsure', 'rate_accord', 'rate_passivity', 'rate_sure_five', 'rate_sure_ten');
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        编辑功能及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function edit_html($data)
    {
        $input = <<<INPUT
<div class="form-group">
    <label for="rate_good" class="col-sm-2 control-label">
        好样本占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_good']}" class="form-control" placeholder="请输入好样本占比" id="rate_good" name="rate_good" aria-describedby="basic-addon3" maxlength="5">
            <span class="input-group-addon" id="basic-addon3">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_sure" class="col-sm-2 control-label">
        催收命中占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_sure']}" class="form-control" placeholder="请输入催收命中占比" id="rate_sure" name="rate_sure" aria-describedby="basic-addon4" maxlength="5">
            <span class="input-group-addon" id="basic-addon4">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_notsure" class="col-sm-2 control-label">
        疑似催收命中占比：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_notsure']}" class="form-control" placeholder="请输入疑似催收命中占比" id="rate_notsure" name="rate_notsure" aria-describedby="basic-addon5" maxlength="5">
            <span class="input-group-addon" id="basic-addon5">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_accord" class="col-sm-2 control-label">
        只有主叫：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_accord']}" class="form-control" placeholder="请输入只有主叫占比" id="rate_accord" name="rate_accord" aria-describedby="basic-addon6" maxlength="5">
            <span class="input-group-addon" id="basic-addon6">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_passivity" class="col-sm-2 control-label">
        只有被叫：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_passivity']}" class="form-control" placeholder="请输入只有被叫占比" id="rate_passivity" name="rate_passivity" aria-describedby="basic-addon7" maxlength="5">
            <span class="input-group-addon" id="basic-addon7">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_sure_five" class="col-sm-2 control-label">
        确认催收5个以上：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_sure_five']}" class="form-control" placeholder="请输入确认催收5个以上占比" id="rate_sure_five" name="rate_sure_five" aria-describedby="basic-addon8" maxlength="5">
            <span class="input-group-addon" id="basic-addon8">%</span>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="rate_sure_ten" class="col-sm-2 control-label">
        确认催收10个以上：
    </label>
    <div class="col-sm-10">
        <div class="input-group">
            <input type="text" value="{$data['rate_sure_ten']}" class="form-control" placeholder="请输入确认催收10个以上占比" id="rate_sure_ten" name="rate_sure_ten" aria-describedby="basic-addon10" maxlength="5">
            <span class="input-group-addon" id="basic-addon10">%</span>
        </div>
    </div>
</div>
INPUT;
        return $input;
    }
    /**
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     * |        导出功能及其相关方法
     * +------------------------------------------------------------------------------
     * +------------------------------------------------------------------------------
     **/
    public function file_data()
    {
        //获取查询参数
        $params = $this->getSearchParams();
        //获取查询条件
        $where = $this->getWhere($params);
        //排序方式
        $order = $this->getOrder();
        //获取数据
        return $this->getFileData($where, $order);
    }
    /**
     * 获取fileData
     *
     * @access protected
     * @param $where array 查询条件
     * @param $order string 排序方式
     *
     * @return array
     **/
    protected function getFileData($where, $order)
    {
        $title = [
            '客户名称', '公司名称', '区域', '公司类型', '提交测试日期', '返回结果日期', '测试期数',
            '测试天数', '测试总数量', '好样本数据占比', '催收命中占比', '疑似催收命中占比', '只有主叫',
            '只有被叫', '确认催收5个以上', '确认催收10个以上', '测试进度', '客户反馈', '备注'
        ];
        $width = [
            26, 26, 18, 26, 20, 20, 18, 18, 18, 18, 18, 20, 18, 18, 20, 20, 18, 26, 40
        ];
        $filename = '邦信分（详单版V2）产品测试信息';
        $data = DataRepository::getFullPreSalesProductResultData($this->product_id, $where, '*,' . $this->table_name . '.id AS product_result_id', $order);
        if (empty($data)) {
            return compact('title', 'data', 'filename', 'width');
        }
        //获取必要的数据
        #企业类型
        $company_type = DataRepository::getCompanyTypeData([], 'id, name');
        $company_type = array_column($company_type, 'name', 'id');
        #区域
        $region_arr = PreSalesCustomerModel::$region;
        #测试进度
        $progress_arr = PreSalesPubResultModel::$progress;
        $data = array_map(function ($item) use($company_type, $region_arr, $progress_arr) {
            $result = [];
            $result[] = $item['ps_customer_name'];
            $result[] = $item['company'];
            $result[] = $region_arr[$item['region']];
            $result[] = $company_type[$item['first_type']] . '--' . $company_type[$item['twice_type']];
            $result[] = date('Y-m-d', $item['submit_time']);
            $result[] = $item['return_time']?date('Y-m-d', $item['return_time']):'--';
            $result[] = $item['test_phase'];
            $result[] = $item['test_days']?$item['test_days']:'--';
            $result[] = $item['test_num']?$item['test_num']:'--';
            $result[] = $item['rate_good']?$item['rate_good'] . '%':'--';
            $result[] = $item['rate_sure']?$item['rate_sure'] . '%':'--';
            $result[] = $item['rate_notsure']?$item['rate_notsure'] . '%':'--';
            $result[] = $item['rate_accord']?$item['rate_accord'] . '%':'--';
            $result[] = $item['rate_passivity']?$item['rate_passivity'] . '%':'--';
            $result[] = $item['rate_sure_five']?$item['rate_sure_five'] . '%':'--';
            $result[] = $item['rate_sure_ten']?$item['rate_sure_ten'] . '%':'--';
            $result[] = $progress_arr[$item['test_progress']];
            $result[] = $item['test_result']?$item['test_result']:'--';
            $result[] = $item['remark']?$item['remark']:'--';
            return $result;
        }, $data);
        return compact('title', 'data', 'filename', 'width');
    }
}