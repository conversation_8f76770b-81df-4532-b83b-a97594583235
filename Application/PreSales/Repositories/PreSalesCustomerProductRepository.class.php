<?php

namespace PreSales\Repositories;


use Account\Model\AccountModel;
use PreSales\Model\PreSalesCustomerModel;
use PreSales\Model\PreSalesCustomerProductMiddleModel;

class PreSalesCustomerProductRepository
{
    protected $pre_sales_customer_model;
    protected $pre_sales_customer_product_model;
    protected $account_model;
    public function __construct()
    {
        $this->account_model = new AccountModel();
        $this->pre_sales_customer_product_model = new PreSalesCustomerProductMiddleModel();
        $this->pre_sales_customer_model = new PreSalesCustomerModel();
    }
    /**
     * 客户开通产品时监听事件（如果客户为绑定的线下客户，则需要在开通产品时在测试客户产品表中增加关联数据）
     *
     * @access public
     *
     * @return void
     **/
    public function listenStoreProduct()
    {
        $account_id = I('post.account_id', '', 'trim');
        $customer_id = $this->account_model->where(compact('account_id'))->getField('customer_id');
        $sign_time = ['neq', 0];
        $ps_customer_id = $this->pre_sales_customer_model->where(compact('customer_id', 'sign_time'))->getField('ps_customer_id');
        if (empty($ps_customer_id)) {
            return;
        }
        $product_id = I('post.product_id', '', 'trim');
        $create_time = time();
        $data = compact('customer_id', 'account_id', 'ps_customer_id', 'product_id', 'create_time');
        $this->pre_sales_customer_product_model->add($data);
    }
}