<?php

namespace PreSales\Repositories;

use Common\Common\PhpExcelTrait;
use Common\Controller\DataAuthController;
use PreSales\Model\PreSalesCustomerModel;
use PreSales\Model\PreSalesPubResultModel;

//测试结果
class PreSalesProductRepository
{
    use PhpExcelTrait;
    protected $product_id;
    protected $model;
    public function __construct()
    {
        $this->model = new PreSalesPubResultModel();
    }
    /**
     * 列表页
     *
     * @access protected
     *
     * @return array
     **/
    public function index()
    {
        //测试产品
        $product_id = I('get.product_id', '', 'trim');
        if (empty($product_id)) {
            redirect(U('PreSalesCustomer/index'));
        }
        if (!in_array($product_id, PreSalesCustomerModel::$product_id)) {
            throw new \Exception('不存在此产品的测试配置');
        }
        //获取页面内容
        $data = $this->getRepository($product_id)->index();
        //生成tab的字符串
        $data['tab'] = DataRepository::getTabHtml();
        //获取当前的测试产品ID
        $data['product_id'] = $product_id;
        return $data;
    }
    /**
     * 获取测试产品的Repository
     *
     * @access protected
     * @param $product_id string 产品ID
     *
     * @return PreSalesResultRepository
     **/
    protected function getRepository($product_id = '')
    {
        $product_id = $product_id?:$this->product_id;
        switch($product_id) {
            case 104:
                return new PreSalesResult104Repository();
                break;
            case 105:
                return new PreSalesResult105Repository();
                break;
            case 200:
                return new PreSalesResult200Repository();
                break;
            case 210:
                return new PreSalesResult210Repository();
                break;
            case 401:
                return new PreSalesResult401Repository();
                break;
        }
        return new PreSalesResult104Repository();
    }
    /**
     * 获取增加页面
     *
     * @access protected
     *
     * @return array
     **/
    public function add_html()
    {
        //默认产品
        $product_id = I('get.product_id', '', 'trim');
        //默认客户
        $ps_customer_id = I('get.ps_customer_id', '', 'trim');
        //产品的option
        $productData = DataRepository::getProductData([
            'product_id'    => ['in', PreSalesCustomerModel::$product_id]
        ], 'product_id,product_name');
        if (empty($product_id)) {
            $productOption = DataRepository::getOption(array_column($productData, 'product_name', 'product_id'), '', ['' => '--选择测试产品--']);
        } else {
            $productOption = DataRepository::getOption(array_column($productData, 'product_name', 'product_id'), $product_id);
        }
        //所有客户
        $psCustomerData = DataRepository::getPreSalesCustomerData([], 'ps_customer_id,ps_customer_name');
        if (empty($ps_customer_id)) {
            $psCustomerOption = DataRepository::getOption(array_column($psCustomerData, 'ps_customer_name', 'ps_customer_id'), '', [], true);
        } else {
            //校验数据权限
            DataAuthController::instance()->validAllowDoPreSalesCustomer($ps_customer_id);
            $psCustomerOption = DataRepository::getOption(array_column($psCustomerData, 'ps_customer_name', 'ps_customer_id'), $ps_customer_id, [], true);
        }
        //测试进度
        $progressOption = DataRepository::getOption(PreSalesPubResultModel::$progress);
        return compact('productOption', 'psCustomerOption', 'progressOption', 'product_id');
    }
    /**
     * 增加测试数据
     *
     * @access public
     *
     * @return void
     **/
    public function add()
    {
        //监听切换测试产品的增加页面
        $this->listenCutAdd();
        //数据权限校验
        DataAuthController::instance()->validAllowDoPreSalesCustomer(I('post.ps_customer_id', '', 'trim'));
        //获取基础的数据
        $data = $this->getAddData();
        //测试产品
        $product_id = $data['product_id'];
        $repository = $this->getRepository($product_id);
        //获取附加的基础数据
        $data = array_merge($data, $this->getAddExtraData($repository));
        //每个测试产品的测试数据
        $everyData = $repository->getAddData();
        //增加基础数据
        $res = $this->model->add($data);
        //增加附加数据
        $everyData['pub_result_id'] = $res;
        $everyData['ps_customer_id'] = $data['ps_customer_id'];
        $res = $repository->getModel()->add($everyData);
        $validate = new ValidateRepository();
        if ($res) {
            $preSalesCustomer = new PreSalesCustomerRepository();
            $preSalesCustomer->listenAddTestProduct($product_id, $data['ps_customer_id']);
            $validate->successResponse();
        } else {
            $validate->errorResponse('增加失败');
        }
    }
    /**
     * 获取基础数据（并校验）
     *
     * @access protected
     *
     * @return array
     **/
    protected function getAddData()
    {
        $validate = new ValidateRepository();
        //产品
        $product_id = I('post.product_id', '', 'trim');
        $product_id = $validate->valid_in_data($product_id, PreSalesCustomerModel::$product_id, '暂不支持的该测试产品', false);
        //测试客户
        $ps_customer_id = I('post.ps_customer_id', '', 'trim');
        $ps_customer_id = $validate->valid_empty($ps_customer_id, '请选择测试客户');
        $PsCustomerData = DataRepository::getPreSalesCustomerData(compact('ps_customer_id'));
        if (empty($PsCustomerData)) {
            $validate->errorResponse('该测试客户不存在或已被删除');
        }
        //提交测试日期
        $submit_time = I('post.submit_time', '', 'trim');
        $submit_time = $validate->valid_date($submit_time, '提交测试日期格式不正确', false);
        $submit_time = strtotime($submit_time);
        //返回结果日期
        $return_time = I('post.return_time', '', 'trim');
        $return_time = $validate->valid_date($return_time, '返回结果日期格式不正确');
        $return_time = $return_time?strtotime($return_time):$return_time;
        //测试天数
        $test_days = I('post.test_days', null, 'trim');
        $test_days = $validate->valid_number($test_days, '测试天数格式不正确');
        //测试数量
        $test_num = I('post.test_num', null, 'trim');
        $test_num = $validate->valid_number($test_num, '测试数量格式不正确');
        //测试进度
        $test_progress = I('post.test_progress', '', 'trim');
        $test_progress = $validate->valid_in_data($test_progress, array_keys(PreSalesPubResultModel::$progress), '暂不支持此测试进度', false);
        //客户反馈
        $test_result = I('post.test_result', '', 'trim');
        //备注
        $remark = I('post.remark', '', 'trim');
        return compact('product_id', 'ps_customer_id', 'submit_time', 'return_time', 'test_days', 'test_num', 'test_progress', 'test_result', 'remark');
    }
    /**
     * 获取增加的额外的数据
     *
     * @access protected
     * @param $repository PreSalesResultRepository
     *
     * @return array
     **/
    protected function getAddExtraData($repository)
    {
        //创建时间
        $create_time = $update_time = time();
        $create_admin = $update_admin = session('site_login_name');
        //测试期数
        $ps_customer_id = I('post.ps_customer_id', '', 'trim');
        $test_phase = $repository->getModel()->where(compact('ps_customer_id'))->count();
        $test_phase++;
        return compact('create_admin', 'create_time', 'update_admin', 'update_time', 'test_phase');
    }
    /**
     * 监听切换测试产品的增加页面
     *
     * @access protected
     *
     * @return void
     **/
    protected function listenCutAdd()
    {
        $type = I('post.type', '', 'trim');
        if ($type=='cut_product') {
            $product_id = I('post.product_id', '', 'trim');
            $repository = $this->getRepository($product_id);
            $repository->add_html();
        }
    }
    /**
     * 编辑页面
     *
     * @access public
     *
     * @return array
     **/
    public function edit_html()
    {
        //获取编辑数据
        $data = $this->getDataForEdit();
        //格式化编辑数据
        $data = $this->formatEditData($data);
        //测试进度Option
        $progressOption = DataRepository::getOption(PreSalesPubResultModel::$progress, $data['test_progress']);
        //测试结果Input
        $product_id = $data['product_id'];
        $repository = $this->getRepository($product_id);
        $input = $repository->edit_html($data);
        return compact('data', 'progressOption', 'input');
    }
    /**
     * 获取编辑的数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getDataForEdit()
    {
        $product_id = I('get.product_id', '', 'trim');
        if (!in_array($product_id, PreSalesCustomerModel::$product_id)) {
            throw new \Exception('暂不支持该测试产品');
        }
        $data = DataRepository::getPreSalesEditData($product_id, I('get.id', '', 'trim'));
        if (empty($data)) {
            throw new \Exception('编辑数据不存在');
        }
        $data['product_id'] = $product_id;
        //获取当前测试的名称
        $data['product_name'] = DataRepository::getProductData(compact('product_id'), 'product_name')[0]['product_name'];
        //获取测试客户名称
        $ps_customer_id = $data['ps_customer_id'];
        $data['ps_customer_name'] = DataRepository::getPreSalesCustomerData(compact('ps_customer_id'), 'ps_customer_name')[0]['ps_customer_name'];
        return $data;
    }
    /**
     * 格式化编辑数据
     *
     * @access protected
     * @param $data array 数据
     *
     * @return array
     **/
    protected function formatEditData($data)
    {
        //提交测试日期
        $data['submit_time'] = $data['submit_time']?date('Y-m-d', $data['submit_time']):'';
        //返回结果日期
        $data['return_time'] = $data['return_time']?date('Y-m-d', $data['return_time']):'';
        return $data;
    }
    /**
     * 编辑数据
     *
     * @access public
     *
     * @return void
     **/
    public function edit()
    {
        //获取基础的数据
        $data = $this->getEditData();
        //需要用到的数据
        $useData = $data['use_data'];
        $editData = $data['edit_data'];
        //获取子产品的测试结果ID
        $product_result_id = $useData['data']['product_result_id'];
        //获取公用测试结果ID
        $public_result_id = $useData['data']['public_result_id'];
        //测试产品
        $product_id = $useData['product_id'];
        $repository = $this->getRepository($product_id);
        //获取附加的基础数据
        $editData = array_merge($editData, $this->getEditExtraData());
        //每个测试产品的测试数据
        $everyData = $repository->getAddData();
        //保存基础数据
        $res1 = $this->model->where([
            'id'    => $public_result_id
        ])->save($editData);
        //增加附加数据
        $res2 = $repository->getModel()->where([
            'id'    => $product_result_id
        ])->save($everyData);
        $validate = new ValidateRepository();
        if ($res1 || $res2) {
            $validate->successResponse();
        } else {
            $validate->errorResponse('数据未做任何变化');
        }
    }
    /**
     * 获取编辑的数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getEditData()
    {
        $validate = new ValidateRepository();
        $edit_data = [];
        $use_data = [];
        //产品
        $product_id = I('post.product_id', '', 'trim');
        $use_data['product_id'] = $validate->valid_in_data($product_id, PreSalesCustomerModel::$product_id, '暂不支持的该测试产品', false);
        //修改的数据ID
        $id = I('post.product_result_id', '', 'trim');
        $data = DataRepository::getPreSalesEditData($product_id, $id);
        if (empty($data)) {
            $validate->errorResponse('缺少必要参数');
        }
        $use_data['id'] = $id;
        $use_data['data'] = $data;
        //提交测试日期
        $submit_time = I('post.submit_time', '', 'trim');
        $submit_time = $validate->valid_date($submit_time, '提交测试日期格式不正确', false);
        $edit_data['submit_time'] = strtotime($submit_time);
        //返回结果日期
        $return_time = I('post.return_time', '', 'trim');
        $return_time = $validate->valid_date($return_time, '返回结果日期格式不正确');
        $edit_data['return_time'] = $return_time?strtotime($return_time):$return_time;
        //测试天数
        $test_days = I('post.test_days', null, 'trim');
        $edit_data['test_days'] = $validate->valid_number($test_days, '测试天数格式不正确');
        //测试数量
        $test_num = I('post.test_num', null, 'trim');
        $edit_data['test_num'] = $validate->valid_number($test_num, '测试数量格式不正确');
        //测试进度
        $test_progress = I('post.test_progress', '', 'trim');
        $edit_data['test_progress'] = $validate->valid_in_data($test_progress, array_keys(PreSalesPubResultModel::$progress), '暂不支持此测试进度', false);
        //客户反馈
        $edit_data['test_result'] = I('post.test_result', '', 'trim');
        //备注
        $edit_data['remark'] = I('post.remark', '', 'trim');
        return compact('edit_data', 'use_data');
    }
    /**
     * 获取编辑的额外数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getEditExtraData()
    {
        //修改时间、修改人
        $update_time = time();
        $update_admin = session('site_login_name');
        return compact('update_time', 'update_admin');
    }
    /**
     * excel导出
     *
     * @access public
     *
     * @return void
     **/
    public function file()
    {
        $product_id = I('get.product_id', '', 'trim');
        if (!in_array($product_id, PreSalesCustomerModel::$product_id)) {
            throw new \Exception('暂不支持该测试产品');
        }
        $repository = $this->getRepository($product_id);
        $data = $repository->file_data();
        //设置excel内容
        $this->file_out_init()->setWidth($data['width'])->addRowContent($data['title'], 18, true);
        foreach ($data['data'] as $item) {
            $this->addRowContent($item, 16);
        }
        $this->setSheetTitle($data['filename'])->download($data['filename']);
    }

}