<?php

namespace PreSales\Repositories;

use Account\Model\AccountModel;
use Account\Model\AccountProductModel;
use Account\Model\CompanytypeModel;
use Account\Model\CustomerModel;
use Account\Model\ProductModel;
use Common\Controller\DataAuthController;
use Common\Model\SystemUserModel;
use PreSales\Model\PreSalesCustomerModel;
use PreSales\Model\PreSalesCustomerProductMiddleModel;
use Think\Model;

class DataRepository
{
    protected static $customer_model;
    protected static $product_model;
    protected static $account_model;
    protected static $account_product_model;
    protected static $pre_sales_customer_model;
    protected static $pre_sales_customer_product_model;
    protected static $company_type_model;
    protected static $manager_model;
    /**
     * 获取客户数据
     *
     * @access public
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getCustomerData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$customer_model) {
            self::$customer_model = new CustomerModel();
        }
        return self::getData(self::$customer_model, $where, $field, $order, $limit);
    }
    /**
     * 获取账号数据
     *
     * @access public
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getAccountData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$account_model) {
            self::$account_model = new AccountModel();
        }
        return self::getData(self::$account_model, $where, $field, $order, $limit);
    }
    /**
     * 获取产品数据
     *
     * @access public
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getProductData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$product_model) {
            self::$product_model = new ProductModel();
        }
        return self::getData(self::$product_model, $where, $field, $order, $limit);
    }
    /**
     * 获取账号_产品数据
     *
     * @access public
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getAccountProductData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$account_product_model) {
            self::$account_product_model = new AccountProductModel();
        }
        return self::getData(self::$account_product_model, $where, $field, $order, $limit);
    }
    /**
     * 获取售前测试客户数据
     *
     * @access public
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getPreSalesCustomerData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$pre_sales_customer_model) {
            self::$pre_sales_customer_model = new PreSalesCustomerModel();
        }
        $model = self::$pre_sales_customer_model->field($field)->where($where)->where(DataAuthController::instance()->getPreSalesCustomerWhere());
        if ($order) {
            $model = $model->order($order);
        }
        if ($limit) {
            list($first_rows, $list_rows) = $limit;
            $model = $model->limit($first_rows, $list_rows);
        }
        return $model->select();
    }
    /**
     * 获取售前测试客户_开通产品数据
     *
     * @access protected
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getPreSalesCustomerProductData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$pre_sales_customer_product_model) {
            self::$pre_sales_customer_product_model = new PreSalesCustomerProductMiddleModel();
        }
        return self::getData(self::$pre_sales_customer_product_model, $where, $field, $order, $limit);
    }
    /**
     * 获取售前测试数据
     *
     * @access public
     * @param $product_id integer 产品ID
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getPreSalesProductResultData($product_id, $where = [], $field = '*', $order = null, $limit = null)
    {
        $className = '\\PreSales\\Model\\PreSalesResult' . $product_id . 'Model';
        $model = new $className;
        return self::getData($model, $where, $field, $order, $limit);
    }
    /**
     * 获取售前测试数据(携带公用数据)
     *
     * @access public
     * @param $product_id integer 产品ID
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getFullPreSalesProductResultData($product_id, $where = [], $field = '*', $order = null, $limit = null)
    {
        $className = '\\PreSales\\Model\\PreSalesResult' . $product_id . 'Model';
        $tableName = 'pre_sales_result' . $product_id;
        $model = (new $className)->field($field)->where($where);
        if ($order){
            $model = $model->order($order);
        }
        if ($limit) {
            list($first_rows, $list_rows) = $limit;
            $model = $model->limit($first_rows, $list_rows);
        }
        return $model
            ->join('pre_sales_pub_result ON pre_sales_pub_result.id = ' . $tableName . '.pub_result_id')
            ->join('pre_sales_customer ON ' . $tableName . '.ps_customer_id = pre_sales_customer.ps_customer_id', 'left')
            ->select();
    }
    /**
     * 获取企业类型数据
     *
     * @access public
     * @param $product_id integer 产品ID
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getCompanyTypeData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$company_type_model) {
            self::$company_type_model = new CompanytypeModel();
        }
        return self::getData(self::$company_type_model, $where, $field, $order, $limit);
    }
    /**
     * 获取管理员的数据
     *
     * @access public
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    public static function getManagerData($where = [], $field = '*', $order = null, $limit = null)
    {
        if (!self::$manager_model) {
            self::$manager_model = new SystemUserModel();
        }
        $model = self::$manager_model->field($field)->where($where)->where(DataAuthController::instance()->getUserWhere());
        if ($order) {
            $model = $model->order($order);
        }
        if ($limit) {
            list($first_rows, $list_rows) = $limit;
            $model = $model->limit($first_rows, $list_rows);
        }
        return $model->select();
    }
    /**
     * 获取测试结果编辑数据
     *
     * @access public
     * @param $product_id integer 产品ID
     * @param $id integer 数据ID
     *
     * @return array
     **/
    public static function getPreSalesEditData($product_id, $id)
    {
        $className = '\\PreSales\\Model\\PreSalesResult' . $product_id . 'Model';
        $tableName = 'pre_sales_result' . $product_id;
        $model = (new $className)->field('*,' . $tableName . '.id as product_result_id,pre_sales_pub_result.id as public_result_id')->where($tableName . '.id='.$id);
        return $model
            ->join('pre_sales_pub_result ON pre_sales_pub_result.id = ' . $tableName . '.pub_result_id')
            ->find();
    }
    /**
     * 获取指定的数据
     *
     * @access protected
     * @param $model Model 数据模型
     * @param $where array 查询条件
     * @param $field string 查询字段
     * @param $order string 排序方式
     * @param $limit array 用于分页获取数据
     *
     * @return array
     **/
    protected static function getData(Model $model, $where, $field = '*', $order = null, $limit = null)
    {
        $model = $model->field($field)->where($where);
        if ($order) {
            $model = $model->order($order);
        }
        if ($limit) {
            list($first_rows, $list_rows) = $limit;
            $model = $model->limit($first_rows, $list_rows);
        }
        return $model->select();
    }
    /**
     * 为一个数据生成页面展示的option
     *
     * @access protected
     * @param $data array 数据 [value => show]
     * @param $default string 默认值
     * @param $extra array 附加展示的数据
     * @param $isSelect2 boolean 是否为select2
     *
     * @return string
     **/
    public static function getOption($data, $default = null, $extra = [], $isSelect2 = false)
    {
        $display = '';
        if ($isSelect2) {
            $display .= '<option></option>';
        }
        if (!empty($extra)) {
            array_walk($extra, function ($show, $value) use (&$display) {
                $display .= "<option value='{$value}'>{$show}</option>";
            });
        }
        array_walk($data, function ($show, $value) use (&$display, $default) {
            if ($default==$value) {
                $display .= "<option value='{$value}' selected>{$show}</option>";
            } else {
                $display .= "<option value='{$value}'>{$show}</option>";
            }
        });
        return $display;
    }
    /**
     * 设置页面展示的TAB
     *
     * @access public
     *
     * @return string
     **/
    public static function getTabHtml()
    {
        $tab = [
            0 => [
                'href'      => U('PreSalesCustomer/index'),
                'name'      => '客户测试产品一览表',
                'checked'   => false
            ],
            105 => [
                'href'      => U('PreSalesProduct/index', ['product_id' => 105]),
                'name'      => '邦信分详单版V2',
                'checked'   => false
            ],
            210 => [
                'href'      => U('PreSalesProduct/index', ['product_id' => 210]),
                'name'      => '邦信分快捷版',
                'checked'   => false
            ],
            401 => [
                'href'      => U('PreSalesProduct/index', ['product_id' => 401]),
                'name'      => '邦企查',
                'checked'   => false
            ],
            104 => [
                'href'      => U('PreSalesProduct/index', ['product_id' => 104]),
                'name'      => '邦秒配',
                'checked'   => false
            ],
            200 => [
                'href'      => U('PreSalesProduct/index', ['product_id' => 200]),
                'name'      => '邦秒验',
                'checked'   => false
            ]
//            101 => [
//                'href'      => U('PreSalesProduct/index', ['product_id' => 101]),
//                'name'      => '邦信分详单版V1',
//                'checked'   => false
//            ],
//            801 => [
//                'href'      => U('PreSalesProduct/index', ['product_id' => 801]),
//                'name'      => '号码状态查询',
//                'checked'   => false
//            ]
        ];
        $product_id = I('get.product_id', '', 'trim');
        if (empty($product_id)) {
            $tab[0]['checked'] = true;
        } else {
            $tab[$product_id]['checked'] = true;
        }
        $display = '';
        array_walk($tab, function ($item) use (&$display) {
            if ($item['checked']) {
                $display .= "<div class=\"navbar-header checked\"><a class=\"navbar-brand\" href=\"{$item['href']}\">{$item['name']}</a></div>";
            } else {
                $display .= "<div class=\"navbar-header\"><a class=\"navbar-brand\" href=\"{$item['href']}\">{$item['name']}</a></div>";
            }
        });
        return $display;
    }
    /**
     * 将公司类型进行组合，格式为（一级类型--二级类型）
     *
     * @access public
     * @param $default string 默认值
     * @param $extra string 附加值
     *
     * @return string
     **/
    public static function getCompanyTypeOption($default = '', $extra = ['' => '--选择公司类型--'])
    {
        $data = self::getCompanyTypeData([], 'id,name,parent_id');
        $result = [];
        $data_hash = array_column($data, 'name', 'id');
        array_walk($data, function ($item) use ($data_hash, &$result) {
            $parent_id = $item['parent_id'];
            if ($parent_id!=0) {
                $id = $item['id'];
                $result[$id] = $data_hash[$parent_id] . '--' . $item['name'];
            }
        });
        return self::getOption($result, $default, $extra);
    }

}