<?php

namespace PreSales\Controller;

use Common\Controller\AdminController;
use PreSales\Repositories\PreSalesProductRepository;

class PreSalesProductController extends AdminController
{
    public function index()
    {
        $repository = new PreSalesProductRepository();
        $this->assign($repository->index());
        $this->display();
    }
    public function add()
    {
        $repository = new PreSalesProductRepository();
        if (IS_POST) {
            try {
                $repository->add();
            } catch (\Exception $exception) {
                $data = [
                    'msg'   => $exception->getMessage(),
                    'code'  => 100
                ];
                $this->ajaxReturn($data);
            }
        } else {
            try {
                $this->assign($repository->add_html());
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), I('get.callback', U('PreSalesCustomer/index'), 'urldecode'));
            }
        }
    }
    public function edit()
    {
        $repository = new PreSalesProductRepository();
        if (IS_POST) {
            $repository->edit();
        } else {
            $this->assign($repository->edit_html());
            $this->display();
        }
    }
    public function file()
    {
        $repository = new PreSalesProductRepository();
        $repository->file();
    }
}