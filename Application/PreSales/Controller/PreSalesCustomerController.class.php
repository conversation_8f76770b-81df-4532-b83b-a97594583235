<?php

namespace PreSales\Controller;

use Common\Controller\AdminController;
use PreSales\Repositories\PreSalesCustomerRepository;

//测试售前客户管理
class PreSalesCustomerController extends AdminController
{
    public function index()
    {
        $repository = new PreSalesCustomerRepository();
        $data = $repository->index();
        $this->assign($data);
        $this->display();
    }
    public function add()
    {
        $repository = new PreSalesCustomerRepository();
        if (IS_POST) {
            try {
                $repository->add();
            } catch (\Exception $exception) {
                $data['status'] = 1;
                $data['msg'] = '数据增加失败：' . $exception->getMessage();
                $this->ajaxReturn($data);
            }
        } else {
            try {
                $this->assign($repository->add_html());
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }
        }
    }
    public function edit()
    {
        $repository = new PreSalesCustomerRepository();
        if (IS_POST) {
            try {
                $repository->edit();
            } catch (\Exception $exception) {
                $data['status'] = 1;
                $data['msg'] = '数据修改失败：' . $exception->getMessage();
                $this->ajaxReturn($data);
            }
        } else {
            try {
                $this->assign($repository->edit_html());
                $this->display();
            } catch (\Exception $exception) {
                $this->error($exception->getMessage(), U('index'));
            }

        }
    }
    public function file()
    {
        $repository = new PreSalesCustomerRepository();
        $repository->file();
    }
    public function del()
    {
        try {
            $repository = new PreSalesCustomerRepository();
            $repository->del();
            $this->success('删除成功', U('index'));
        } catch (\Exception $exception) {
            $this->error($exception->getMessage(), U('index'));
        }
    }
}