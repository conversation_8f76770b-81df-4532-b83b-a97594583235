<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container add_form">
    <form class="form-horizontal" id="form" action="{:U('add')}" method="post">
        <div class="form-group">
            <label for="ps_customer_name" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                客户名称：
            </label>
            <div class="col-sm-10">
                <input type="text" name="ps_customer_name" class="form-control" id="ps_customer_name" placeholder="请输入客户名称" maxlength="40">
            </div>
        </div>
        <div class="form-group">
            <label for="company" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                公司名称：
            </label>
            <div class="col-sm-10">
                <input type="text" name="company" class="form-control" id="company" placeholder="请输入公司名称" maxlength="50">
            </div>
        </div>
        <div class="form-group">
            <label for="region" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                区域分布：
            </label>
            <div class="col-sm-10">
                <select class="form-control" name="region" id="region">
                    {$regionOption}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="business_admin" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                商务跟进人：
            </label>
            <div class="col-sm-10">
                <select class="form-control" name="business_admin" id="business_admin">
                    {$userOption}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="first_type" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                公司类型（一级）：
            </label>
            <div class="col-sm-10">
                <select class="form-control" name="first_type" id="first_type">
                    {$firstTypeOption}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="twice_type" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                公司类型（二级）：
            </label>
            <div class="col-sm-10">
                <select class="form-control" name="twice_type" id="twice_type">
                    {$twiceTypeOption}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="trace_time" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                首次跟进日期：
            </label>
            <div class="col-sm-10">
                <input type="date" name="trace_time" class="form-control" id="trace_time" placeholder="请输入首次跟进日期">
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-warning">保存</button>
                <button type="button" class="btn btn-primary" id="back">返回</button>
            </div>
        </div>
    </form>
</div>
<div class="modal fade" tabindex="-1" role="dialog" id="myModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">增加成功</h4>
            </div>
            <div class="modal-body">
                <p>去添加测试产品信息？</p>
            </div>
            <div class="modal-footer">
                <a href="{:U('index')}" class="btn btn-primary">返回客户列表</a>
                <a id="add_pre_sales_result" class="btn btn-primary">立刻添加</a>
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__ajaxform.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript">
    window.new_ps_customer_id = null;
    //点击模态框中的立即添加的事件
    $("#add_pre_sales_result").click(function () {
        if (window.new_ps_customer_id) {
            location.href = "{:U('PreSalesProduct/add')}?ps_customer_id="+window.new_ps_customer_id+"&callback={:urlencode(U('PreSalesCustomer/index'))}";
        }
    });
    $("#business_admin").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择商务跟进人'
    });
    $('#form').ajaxForm({
        onSuccess: function(res){
            if (res.status==0) {
                $('#myModal').modal();
                window.new_ps_customer_id = res.data.ps_customer_id;
            } else {
                alert(res.msg);
            }
        },
        onErrors: function(){
            alert('增加失败');
        },
        onError: function(){
            alert('增加失败');
        }
    });
    $("#first_type").change(function () {
        $("#twice_type").html('');
        $.ajax({
            url : "{:U('add')}?request_type=company_type",
            type : "post",
            data : {
                'first_type' : $(this).val()
            },
            success : function (res) {
                if (res.status==0) {
                    $("#twice_type").html(res.data);
                } else {
                    alert(res.msg);
                }
            },
            error : function () {
                alert('请求失败');
            }
        });
    });
</script>
