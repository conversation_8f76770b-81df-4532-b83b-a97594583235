<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<include file="PreSales@Public/tab" />
<div class="container index_search">
    <form class="form-inline" action="{:U('index')}">
        <div class="form-group">
            <label for="ps_customer_id">客户ID</label>
            <input type="text" value="{$input.ps_customer_id}" class="form-control" id="ps_customer_id" name="ps_customer_id" placeholder="请填写客户ID" maxlength="13" />
        </div>
        <div class="form-group">
            <label for="ps_customer_name">客户名称</label>
            <input type="text" value="{$input.ps_customer_name}" class="form-control" id="ps_customer_name" name="ps_customer_name" placeholder="请填写客户名称" maxlength="40" />
        </div>
        <div class="form-group">
            <label for="company">公司名称</label>
            <input type="text" value="{$input.company}" class="form-control" id="company" name="company" placeholder="请填写公司名称" maxlength="50" />
        </div>
        <div class="form-group">
            <label for="test_product">测试产品</label>
            <select class="form-control" id="test_product" name="test_product">
                {$input.testProductOption}
            </select>
        </div>
        <div class="form-group">
            <label for="sign_product">签约产品</label>
            <select class="form-control" id="sign_product" name="sign_product">
                {$input.signProductOption}
            </select>
        </div>
        <div class="form-group">
            <label for="region">区域</label>
            <select class="form-control" id="region" name="region">
                {$input.regionOption}
            </select>
        </div>
        <div class="form-group">
            <label for="business_admin">商务跟进人</label>
            <select name="business_admin" id="business_admin" class="form-control">
                {$input.businessAdminOption}
            </select>
        </div>
        <div class="form-group">
            <label for="start_trace_time">首次跟进日期</label>
            <input class="form-control" value="{$input.start_trace_time}" type="date" id="start_trace_time" name="start_trace_time" />
            -
            <input class="form-control" value="{$input.end_trace_time}" type="date" id="end_trace_time" name="end_trace_time" />
        </div>
        <div class="form-group">
            <label for="start_trace_time">签约日期</label>
            <input class="form-control" value="{$input.start_sign_time}" type="date" id="start_sign_time" name="start_sign_time" />
            -
            <input class="form-control" value="{$input.end_sign_time}" type="date" id="end_sign_time" name="end_sign_time" />
        </div>
        <button type="submit" class="btn btn-primary">确定</button>
        <a href="{:U('add')}" class="btn btn-success">添加客户</a>
        <a href="javascript:file_out('{:U(\'file\')}');" class="btn btn-warning">Excel导出</a>
    </form>
</div>
<div class="container index_statistics">
    <div class="alert alert-danger" role="alert">{$statistics}</div>
</div>
<div class="container index_table">
    {$table}
</div>
<div class="container index_page">
    <ul class="pagination">{$render}</ul>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript">
    $("#sign_product").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择产品',
        width : '200px'
    });
    $("#business_admin").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择商务跟进人',
        width : '150px'
    });
    //列表JS特效
    {$script}
</script>
