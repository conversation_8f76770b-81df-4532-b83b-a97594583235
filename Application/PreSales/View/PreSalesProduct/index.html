<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<include file="PreSales@Public/tab" />
<div class="container index_search">
    <form class="form-inline" action="{:U('index', ['product_id' => $product_id])}">
        {$input}
        <button type="submit" class="btn btn-primary">确定</button>
        <a href="{:U('add', ['product_id' => $product_id])}?callback={$Think.server.REQUEST_URI|urlencode}" class="btn btn-success">添加测试结果</a>
        <a href="javascript:file_out('{:U(\'file\', [\'product_id\' => $product_id])}');" class="btn btn-warning">Excel导出</a>
    </form>
</div>
<notempty name="statistics">
<div class="container index_statistics">
    <div class="alert alert-danger" role="alert">{$statistics}</div>
</div>
</notempty>
<div class="container index_table">
    {$table}
</div>
<div class="container index_page">
    <ul class="pagination">{$render}</ul>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript">
    $("#sign_product").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择产品'
    });
    $("#business_admin").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择商务跟进人'
    });
    $('[data-toggle="popover"]').popover();
    //列表JS特效
    {$script}
</script>
