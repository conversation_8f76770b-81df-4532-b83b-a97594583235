<!DOCTYPE html>
<html>
<head>
    <include file="Common@Public/head" />
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .form_title{
            background: #cccccc;
            padding: 10px 10px;
            box-sizing: border-box;
            font-size: 16px;
            border-radius: 4px;
        }
        .title_anchor{
            width: 100px;
            height: 80px;
            position: fixed;
            left:0;
            top:50%;
            margin-top:-40px;
            z-index:100;
            border-radius: 0 6px 6px 0;
            border:1px solid #ddd;
            background:#ffffff;
            overflow: hidden;
        }
        .title_anchor>a {
            width: 100%;
            height:40px;
            text-align: center;
            color:#666;
            display: block;
            font-size: 14px;
            line-height: 40px;
        }
        .title_anchor>a:last-child{
            background: #323232;
            color: #ffffff;
        }
        .title_anchor>a:first-child{
            background: #d20a0a;
            color: #ffffff;
        }
    </style>
</head>
<body>
<include file="Common@Public/header" />
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="title_anchor">
    <a href="#base">基本信息</a>
    <a href="#test">测试信息</a>
</div>
<div class="container add_form">
    <form class="form-horizontal" id="form" action="{:U('edit')}" method="post">
        <h4 class="form_title" id="base">基本信息</h4>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                <span class="require_sign"></span>
                测试产品：
            </label>
            <div class="col-sm-10">
                <input class="form-control" type="text" value="{$data.product_name}" disabled />
                <input type="hidden" value="{$data.product_id}" name="product_id" />
                <input type="hidden" value="{$data.product_result_id}" name="product_result_id">
                <input type="hidden" value="{$data.public_result_id}" name="public_result_id">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">
                <span class="require_sign"></span>
                测试客户：
            </label>
            <div class="col-sm-10">
                <input class="form-control" type="text" value="{$data.ps_customer_name}" disabled />
            </div>
        </div>
        <div class="form-group">
            <label for="submit_time" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                提交测试日期：
            </label>
            <div class="col-sm-10">
                <input type="date" value="{$data.submit_time}" name="submit_time" class="form-control" id="submit_time" placeholder="请选择提交测试日期">
            </div>
        </div>
        <div class="form-group">
            <label for="return_time" class="col-sm-2 control-label">
                返回结果日期：
            </label>
            <div class="col-sm-10">
                <input type="date" value="{$data.return_time}" name="return_time" class="form-control" id="return_time" placeholder="请选择返回结果日期">
            </div>
        </div>
        <div class="form-group">
            <label for="test_days" class="col-sm-2 control-label">
                测试天数：
            </label>
            <div class="col-sm-10">
                <div class="input-group">
                    <input type="number" value="{$data.test_days}" class="form-control" placeholder="请输入测试天数" id="test_days" name="test_days" aria-describedby="basic-addon1" maxlength="4">
                    <span class="input-group-addon" id="basic-addon1">天</span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="test_num" class="col-sm-2 control-label">
                测试数量：
            </label>
            <div class="col-sm-10">
                <div class="input-group">
                    <input type="number" value="{$data.test_num}" class="form-control" placeholder="请输入测试数量" id="test_num" name="test_num" aria-describedby="basic-addon2" maxlength="9">
                    <span class="input-group-addon" id="basic-addon2">条</span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="test_progress" class="col-sm-2 control-label">
                <span class="require_sign"></span>
                测试进度：
            </label>
            <div class="col-sm-10">
                <select name="test_progress" id="test_progress" class="form-control">
                    {$progressOption}
                </select>
            </div>
        </div>
        <div class="form-group">
            <label for="test_result" class="col-sm-2 control-label">
                客户反馈：
            </label>
            <div class="col-sm-10">
                <input type="text" value="{$data.test_result}" name="test_result" class="form-control" id="test_result" placeholder="请输入客户反馈" maxlength="20">
            </div>
        </div>
        <div class="form-group">
            <label for="remark" class="col-sm-2 control-label">
                备注：
            </label>
            <div class="col-sm-10">
                <textarea class="form-control" name="remark" id="remark" cols="30" rows="10" placeholder="请输入备注信息" maxlength="50">{$data.remark}</textarea>
            </div>
        </div>
        <h4 class="form_title" id="test">测试结果信息</h4>
        <div id="test_data">{$input}</div>
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-warning">保存</button>
                <a href="{$Think.get.callback}"><button type="button" class="btn btn-primary">返回</button></a>
            </div>
        </div>
    </form>
</div>
</body>
</html>
<script type="text/javascript" src="//cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="__JS__select2.full.min.js"></script>
<script src="__JS__jquery.fileDownload.js"></script>
<script src="__JS__jquery.dataTables.js"></script>
<script src="__JS__jquery.dataTables.bootstrap.js"></script>
<script type="application/javascript" src="__JS__ajaxform.js"></script>
<script type="application/javascript" src="__JS__public.js"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>
<script type="application/javascript">
    $("#ps_customer_id").select2({
        allowClear: true,
        theme: "bootstrap",
        placeholder: '选择测试客户'
    });
    $('#form').ajaxForm({
        onSuccess: function(res){
            if (res.status==0) {
                alert('保存成功');
                location.href = "{$Think.get.callback}";
            } else {
                alert(res.msg);
            }
        },
        onErrors: function(){
            alert('保存失败');
        },
        onError: function(){
            alert('保存失败');
        }
    });
</script>
