<?php

namespace Config\Model;

use Think\Model;

class ChannelStatisModel extends Model
{
    protected $connection = 'DB_ALARM';
    protected $tableName = 'config_channel_statis';
    protected $tablePrefix = '';
    public static $typeName = [
        '1' => '超时',
        '2' => '未查得',
        '3' => '其它异常',
        '4' => '总异常',
    ];

    public static function getChannelStatisList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }
}