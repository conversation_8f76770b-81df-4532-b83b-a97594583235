<?php

namespace Config\Model;

use Think\Model;

class UpstreamChannelModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'upstream_channel';
    protected $tablePrefix = '';

    public static function getChannelByProductId($pid)
    {
        $model = new static();
        $result = $model->query("select * from upstream_channel where `product_id`={$pid} ");

        return $result;
    }

    public static function getByApikeys($apikeyArray)
    {
        $apikeys = implode("','", $apikeyArray);

        $model = new static();
        $result = $model->query("select account_name,apikey from account where `apikey` in ('{$apikeys}')");

        return $result;
    }

    public static function getAccountList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }
}