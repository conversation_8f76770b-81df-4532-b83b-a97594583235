<?php

namespace Config\Model;

use Think\Model;

class ChannelPriceModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'upstream_channel_price';
    protected $tablePrefix = '';

    public static function getPriceByPid($pid)
    {
        $model = new static();
        $result = $model->query("select * from upstream_channel_price where product_id = {$pid} and id in (select max(id) from upstream_channel_price group by upstream_channel_id)");

        return $result;
    }

    public static function getByApikeys($apikeyArray)
    {
        $apikeys = implode("','", $apikeyArray);

        $model = new static();
        $result = $model->query("select account_name,apikey from account where `apikey` in ('{$apikeys}')");

        return $result;
    }

    public static function getAccountList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }
}