<?php

namespace Config\Model;

use Think\Model;

class ProductValueModel extends Model
{
    protected $connection = 'DB_ALARM';
    protected $tableName = 'config_product_value';
    protected $tablePrefix = '';
    public static $typeName = [
        '1' => '等于',
        '2' => '大于',
        '3' => '小于',
        '4' => '区间',
    ];

    public static function getProductValueList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }
}