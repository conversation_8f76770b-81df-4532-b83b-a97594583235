<?php

namespace Config\Model;

use Think\Model;

class BxfProductChannelModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'bxf_product_channel';
    protected $tablePrefix = '';

    public static function getAll()
    {
        $model = new static();
        $result = $model->query("select b.*,p.product_name from bxf_product_channel b left join product p on b.product_id = p.product_id");

        return $result;
    }

    public static function getByPid($pid)
    {
        $model = new static();
        $result = $model->query("select * from bxf_product_channel where product_id ={$pid}");

        return $result;
    }

    public static function getAccountList(array $where, $field = '*')
    {
        return (new static())->where($where)
            ->field($field)
            ->select();
    }

    public static function upDownChannelById($id, $status)
    {
        $model = new static();
        $result = $model->where(['id'=>$id])->save($status);

        return $result;
    }
}