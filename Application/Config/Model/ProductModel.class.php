<?php

namespace Config\Model;

use Think\Model;

class ProductModel extends Model
{
    protected $connection = 'DB_FINANCE';
    protected $tableName = 'product';
    protected $tablePrefix = '';

    public static function getProductByFatherId($fathId)
    {
        $model = new static();
        $result = $model->query("select p.product_id,pt.type from product p left join product_type pt on p.product_id = pt.product_id where `father_id`={$fathId} ");

        return $result;
    }
}