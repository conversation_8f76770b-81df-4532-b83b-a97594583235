<?php

namespace Config\Controller;

use Common\Common\CurlTrait;
use Common\Controller\AdminController;
use Monitor\Repositories\UpstreamRepository;


/**
 * 配置产品值分布相关
 * Class ProductSpreadController
 * @package Monitor\Controller
 */
class ProductSpreadController extends AdminController
{
    use CurlTrait;

    public function __construct()
    {
        parent::__construct();

    }

    public function spreadList()
    {
        addBreakPoint('[spreadList]进入');
        if (IS_POST) {
            $this->getSpreadMonitor();
        }

        $repository = new UpstreamRepository();
        addBreakPoint('[spreadList]实例化');
        $data = $repository->getSpreadListHtmlInfo();

        $this->assign('data', $data);

        $this->display();
        addBreakPoint('[spreadList]display');
        printBreakPoint();
    }

    protected function getSpreadMonitor()
    {
        $repository = new UpstreamRepository();
        addBreakPoint('[spreadListPost]实例化');
        try {
            $data   = $repository->getConfigProductSpreadList();
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }

    public function spreadInfo()
    {
        $repository = new UpstreamRepository();
        $data   = $repository->getConfigProductInfo();

        $this->assign('data', $data);
        $this->display();
    }

    public function spreadEdit()
    {
        $id = intval(I('post.id', 0));
        $field = trim(I('post.field', ''));
        $value = floatval(I('post.value', 0));
        if (!$id || !$value || !$field ) {
            $status = -1;
            $msg    = '参数错误';
            $this->ajaxReturn(compact('status', 'msg'));
        }
        $repository = new UpstreamRepository();
        try {
            $data   = $repository->configProductEdit(compact('id', 'field', 'value'));
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }
    public function spreadAddView()
    {
        $repository = new UpstreamRepository();
        $data = $repository->getSpreadListHtmlInfo();
        $values = $repository->getProductValueHtmlInfo();

        $this->assign('data', $data);
        $this->assign('values', json_encode($values));
        $this->display();
    }

    public function spreadAdd()
    {
        $apikey = I('post.apikey', 0);
        $pid = intval(I('post.pid', 0));
        $cid = intval(I('post.cid', 0));
        $value_id = intval(I('post.value_id', 0));
        $th_one_max = floatval(I('post.th_one_max', 0));
        $th_one_min = floatval(I('post.th_one_min', 0));
        $th_two_max = floatval(I('post.th_two_max', 0));
        $th_two_min = floatval(I('post.th_two_min', 0));
        if (!$apikey || !$pid || !$cid || !$value_id || ($th_one_min >= $th_one_max) || ($th_two_min >= $th_two_max)) {
            $status = -1;
            $msg    = '非法参数';
            $this->ajaxReturn(compact('status', 'msg'));
        }
        $repository = new UpstreamRepository();
        try {
            $data   = $repository->SpreadAdd(compact('apikey', 'pid', 'cid', 'value_id', 'th_one_max', 'th_one_min', 'th_two_max', 'th_two_min'));
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }
}