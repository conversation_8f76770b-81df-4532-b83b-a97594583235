<?php

namespace Config\Controller;

use Common\Common\CurlTrait;
use Common\Controller\AdminController;
use Monitor\Repositories\UpstreamRepository;


/**
 * 配置产品值分布相关
 * Class ChannelStatisController
 * @package Monitor\Controller
 */
class ChannelStatisController extends AdminController
{
    use CurlTrait;

    public function __construct()
    {
        parent::__construct();

    }

    public function statisList()
    {
        if (IS_POST) {
            $this->getStatisMonitor();
        }

        $repository = new UpstreamRepository();
        $data = $repository->getStatisListHtml();

        $this->assign('data', $data);

        $this->display();
    }

    protected function getStatisMonitor()
    {
        $repository = new UpstreamRepository();
        try {
            $data   = $repository->getConfigChannelStatisList();
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }

    public function statisInfo()
    {
        $repository = new UpstreamRepository();
        $data   = $repository->getConfigChannelInfo();

        $this->assign('data', $data);
        $this->display();
    }

    public function statisEdit()
    {
        $id = intval(I('post.id', 0));
        $field = trim(I('post.field', ''));
        $value = floatval(I('post.value', 0));
        if (!$id || !$value || !$field ) {
            $status = -1;
            $msg    = '参数错误';
            $this->ajaxReturn(compact('status', 'msg'));
        }

        $repository = new UpstreamRepository();
        try {
            $data   = $repository->configStatisEdit(compact('id', 'field', 'value'));
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }
    public function statisAddView()
    {
        $repository = new UpstreamRepository();
        $data = $repository->getStatisListHtml();

        $this->assign('data', $data);
        $this->display();
    }

    public function statisAdd()
    {
        $cid = intval(I('post.cid', 0));
        $type = intval(I('post.type', 0));
        $th_one = floatval(I('post.th_one', 0));
        $th_two = floatval(I('post.th_two', 0));
        $custom_key = trim(I('post.custom_key', 0));

        if (!$cid || !$type || !$custom_key || ($th_one <= $th_two)) {
            $status = -1;
            $msg    = '非法参数';
            $this->ajaxReturn(compact('status', 'msg'));
        }
        $repository = new UpstreamRepository();
        try {
            $data   = $repository->StatisAdd(compact('cid', 'type', 'th_one', 'th_two', 'custom_key'));
            $status = 0;
            $msg    = '';
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        } catch (\Exception $exception) {
            $data   = [];
            $msg    = $exception->getMessage();
            $status = 1;
            $this->ajaxReturn(compact('status', 'msg', 'data'));
        }
    }
}