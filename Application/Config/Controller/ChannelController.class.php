<?php

namespace Config\Controller;

use Common\Controller\AdminController;

//渠道管理
class ChannelController extends AdminController
{
    //列表页
    public function index()
    {
        $this->display();
    }

    //邦信分渠道列表页
    public function index_bxf()
    {
        $this->display();
    }

	//增加渠道
	public function add()
	{
		$this->display();
	}
	
	//编辑页面
	public function edit()
	{
		$this->display();
	}
	
	//接口列表
	public function interface_index()
	{
		$this->display();
	}

    //产品渠道列表
    public function product_channel()
    {
        $this->display();
    }

    //编辑产品渠道
    public function edit_product_channel()
    {
        $this->display();
    }

    //增加产品渠道
    public function add_product_channel()
    {
        $this->display();
    }
	
	//增加接口
	public function add_interface()
	{
		$this->display();
	}
	
	//编辑接口
	public function edit_interface()
	{
		$this->display();
	}
	
	//计费配置列表页
	public function price_index()
	{
		$this->display();
	}
	
	//删除计费配置
	public function delete_price()
	{
		$this->display();
	}

	//
    public function complement(){
        $this->display();
    }

    public function fl_inner(){
        $this->display();
    }



}