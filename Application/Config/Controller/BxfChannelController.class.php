<?php

namespace Config\Controller;

use Common\Controller\AdminController;
use Config\Model\BxfProductChannelModel;
use Config\Model\ChannelPriceModel;
use Config\Model\ProductModel;

class BxfChannelController extends AdminController
{
    private $bxfProductId = 210;
    private $channelPrice;

    private $showPriceAdmin = [
        'yong.liu',
        'yanming.li',
//        'chang.liu',
    ];

    private $operatorName =[
            "CUCC" => "联通",
            "ALLCMCC" => "全国移动",
            "BJCMCC" => "北京移动",
            "JSCMCC" => "江苏移动",
            "SCCMCC" => "四川移动",
            "SDCMCC" => "山东移动",
            "HBCMCC" => "河北移动",
        ];

    public function index()
    {
        $fieldType = I("fieldType",  0);
        $admin = $this->loginuser['username'];

        $bxfProductType = $this->getSubProduct();
        $bxfProductChannelRow = BxfProductChannelModel::getAll();
        $this->channelPrice = $this->getChannelPrice();

        //处理产品和渠道数据
        $bxfProductChannel = [];
        foreach ( $bxfProductChannelRow as $v){
            $price = $this->getPrice($admin, $v);

            //字段类型
            $type = $bxfProductType[$v['product_id']];
            if( $type == 2 ){
                if( strpos($v['product_name'], "催收") !== false ){
                    $type = 3;
                }
            }

            $bxfProductChannel[ $v['product_id'].'_'.$v['product_name'] ]['channels'][$v['channel']]['price'] = $price;
            $bxfProductChannel[ $v['product_id'].'_'.$v['product_name'] ]['channels'][$v['channel']]['interface_id'] = $v['interface_id'];
            $bxfProductChannel[ $v['product_id'].'_'.$v['product_name'] ]['type'] = $type;
            $bxfProductChannel[ $v['product_id'].'_'.$v['product_name'] ]['pid'] = $v['product_id'];
        }

        //根据 字段排序
        array_multisort(array_column($bxfProductChannel, 'type'),SORT_ASC,$bxfProductChannel);

        //是否展示提示
        if( in_array($admin, $this->showPriceAdmin) ){
            $priceNote = '<label class="radio-inline">a:查询计费</label><label class="radio-inline">s:查得计费</label><label class="radio-inline">--:未对接</label>';
        }else{
            $priceNote = "";
        }

        $trs = $this->drowTrs($bxfProductChannel);
        $ths = $this->drowTh();
        $this->assign('thInfo', $ths);
        $this->assign('channelInfo', $trs);
        $this->assign('fieldType', $fieldType);
        $this->assign('priceNote', $priceNote);

        $this->display();
    }

    public function getChannel()
    {
        $pid = I("pid",  0);

        if( !$pid ){
            $result = ['status'=>1];
            $this->ajaxReturn($result);
        }

        $data = BxfProductChannelModel::getByPid($pid);

        if( !$data ){
            $result = ['status'=>1];
            $this->ajaxReturn($result);
        }

        foreach ( $data as $k=>$v ){
            if( $v['channel'] == 'CTCC' ){
                unset($data[$k]);
                continue;
            }
            $data[$k]['channelName'] = $this->operatorName[$v['channel']];
        }

        $result = ['status'=>0, "data"=>$data];
        $this->ajaxReturn($result);
    }

    public function setChannel()
    {
        $pid = I("pid",  0);
        $channels = I("channels",  "");
        $channels = explode(',', $channels);

        if( !$pid ){
            $result = ['status'=>1, "msg"=>"未指定产品码"];
            $this->ajaxReturn($result);
        }

        $data = BxfProductChannelModel::getByPid($pid);

        if( !$data ){
            $result = ['status'=>1, "该产品下没有渠道"];
            $this->ajaxReturn($result);
        }

        foreach ( $data as $k=>$v ){
            if( !in_array($v['channel'], $channels) ){
                BxfProductChannelModel::upDownChannelById($v['id'], ['status'=>0]);
            }else{
                BxfProductChannelModel::upDownChannelById($v['id'], ['status'=>1]);
            }
        }

        $result = ['status'=>0];
        $this->ajaxReturn($result);
    }

    private function getPrice($admin, $v)
    {
        //获取价格
        if( isset( $this->channelPrice[$v['interface_id']]['all']) || isset( $this->channelPrice[$v['interface_id']]['succ'] ) ){
            if( in_array($admin, $this->showPriceAdmin) ){
                if( isset( $this->channelPrice[$v['interface_id']]['all'] )  ){
                    $price = "a-".$this->channelPrice[$v['interface_id']]['all'];
                }else{
                    $price = "s-".$this->channelPrice[$v['interface_id']]['succ'];
                }
            }else{
                if( isset( $this->channelPrice[$v['interface_id']]['all'] )  ){
                    $price = "查询计费";
                }else{
                    $price = "查得计费";
                }
            }

        }else{
            if( in_array($admin, $this->showPriceAdmin) ){
                $price = "0";
            }else {
                $price = "查得计费";
            }
        }

        //标注 上下架
        if( $v['status'] == 0 ){
            $price = "<span style='color: red;text-decoration:underline'>".$price.'(下架)'."</span>";
        }else{
            $price = "<span style='text-decoration:underline'>".$price."</span>";
        }

        return $price;
    }

    private function getSubProduct()
    {
        $products = ProductModel::getProductByFatherId($this->bxfProductId);

        $result = [];
        foreach ( $products as $v ){
            $result[ $v['product_id']] = $v['type'];
        }

        return $result;
    }

    private function getChannelPrice()
    {
        $channelPrice = ChannelPriceModel::getPriceByPid($this->bxfProductId);

        $result = [];
        foreach ( $channelPrice as $v ){
            $result[ $v['upstream_channel_id'] ] = json_decode($v['price'], true);
        }

        return $result;
    }

    private function drowTh()
    {
        $ths = "<th>产品</th>";
        foreach ( $this->operatorName as $v ){
            $ths .= "<th>$v</th>";
        }

        $ths .= "<th>操作</th>";

        return $ths;
    }

    private function drowTrs($data)
    {
        $trs = "";

        //回溯情况
//        $trs .= $this->getHuiSuTr();

        foreach ( $data as $k=>$v ){
            $trs .= $this->getTrStr($k, $v);
        }

        return $trs;
    }

    private function getHuiSuTr()
    {
        $tr = "<tr>";
        $tr .= "<td>回溯情况</td>";

        foreach ( $this->operatorName as $k=>$v ){
            $tr .= "<td></td>";
        }

        $tr .= "<td>";
        $tr .= "<button class='btn index-btn btn-info btn-sm'>编辑</button>";
        $tr .= "</td>";

        return $tr;
    }

    private function getTrStr($pname, $data)
    {
        $channels = $data['channels'];
        $type = $data['type'];
        $pid = $data['pid'];

        $tr = "<tr class='fieldType{$type} fieldType'>";
        $tr .= "<td width='300'>$pname</td>";
        foreach ( $this->operatorName as $k=>$v ){
            $tr .= "<td>";
            if( array_key_exists($k, $channels) ){
                $money = "<a href='/Home/Upstream/bxfShortConfig.html?channel_name_search={$channels[$k]['interface_id']}'>{$channels[$k]['price']}</a>";
            }else{
                $money = "--";
            }

            $tr .= $money."</td>";
        }

        $tr .= "<td>";
        $tr .= "<button data-pid='{$pid}' data-pname='{$pname}' class='btn index-btn btn-danger btn-sm channelUpDown'>渠道上下架</button>";
        $tr .= "</td>";

        $tr .= "</tr>";

        return $tr;
    }
}
