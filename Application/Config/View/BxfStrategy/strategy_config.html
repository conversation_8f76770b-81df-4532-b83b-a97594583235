<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
    <script type="application/javascript" src="__JS__big.js"></script>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>

    [v-cloak] {
        display: none;
    }
</style>

<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <el-form :inline="true" :model="search_form" label-width="100px" class="demo-form-inline" size="mini">
            <el-form-item label="帐号:">
                <el-select v-model="search_form.account_id" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in account_map" :key="index" :label="item.account_name" :value="item.account_id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="产品:">
                <el-select v-model="search_form.product_id" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in product_map" :key="index" :label="item.product_name" :value="item.product_id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="策略名称:">
                <el-select v-model="search_form.strategy_id" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in strategy_name_list" :key="index" :label="item.strategy_name" :value="item.strategy_id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="状态:">
                <el-select v-model="search_form.status" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in status_map" :key="index" :label="item.name" :value="item.status"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="get_data()">查询</el-button>
                <el-button type="success" @click="add_strategy_dialog_visible = true">添加配置</el-button>
            </el-form-item>
        </el-form>
    </div>
    <div class="container">
        <el-table :data="strategy_config_list" border fit>
            <el-table-column label="客户" prop="customer_name" width="100"></el-table-column>
            <el-table-column label="帐号" prop="account_name" width="100"></el-table-column>
            <el-table-column label="产品" prop="product_name" width="150"></el-table-column>
            <el-table-column label="策略" width="400">
                <template slot-scope="scope">
                    <el-button size="mini" v-for="(item,index) in scope.row.strategy_infos" :key="index" :label="item.strategy_name" :value="item.strategy_id" @click="show_strategy_info(item)">{{item.strategy_name}}</el-button>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="80">
                <template slot-scope="scope">
                    <template v-if="scope.row.status === 1">
                        <el-tag type="success">可用</el-tag>
                    </template>
                    <template v-else>
                        <el-tag type="danger">禁用</el-tag>
                    </template>
                </template>
            </el-table-column>
            <el-table-column label="操作人" prop="admin" width="150"></el-table-column>
            <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
            <el-table-column label="添加时间" prop="created_at" width="160"></el-table-column>
            <el-table-column label="修改时间" prop="updated_at" width="160"></el-table-column>
            <el-table-column fixed="right" fit label="操作" width="150px">
                <template slot-scope="scope">
                    <el-button size="mini" type="warning" @click="edit_strategy_config_dialog(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" v-if="scope.row.status === 1" @click="del_strategy_config(scope.row.id)">禁用</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block" style="margin-bottom: 16px;">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="page_size"
                           layout="total, sizes, prev, pager, next, jumper" :total="total_num"></el-pagination>
        </div>
    </div>


    <!--弹窗-->
    <!--添加策略配置-->
    <el-dialog width="50%" title="添加策略配置" :visible="add_strategy_dialog_visible" v-loading="add_config_loading" :close-on-click-modal="true" :close-on-press-escape="true" :show-close="false">
        <el-form ref="form" :model="add_strategy_config_data" label-width="200px" label-position="right" size="mini">
            <el-form-item label="帐号:" required>
                <el-select v-model="add_strategy_config_data.account_id" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in account_map" :key="index" :label="item.account_name" :value="item.account_id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="产品:" required>
                <el-radio-group v-model="add_strategy_config_data.product_id">
                  <el-radio-button v-for="(item,index) in product_map" :label="item.product_id">{{item.product_name}}</el-radio-button>
                </el-radio-group>
            </el-form-item>

            <template>
                <el-form-item v-for="(item,index) in add_strategy_config_data.strategy_id" :label="index === 0 ? '策略' : ''" required>
                    <el-select v-model="add_strategy_config_data.strategy_id[index]" filterable clearable placeholder="请选择">
                        <el-option v-for="(item,index) in strategy_name_list" :key="index" :label="item.strategy_name" :value="item.strategy_id"></el-option>
                    </el-select>
                    <el-button icon="el-icon-plus" type="primary" circle v-if="index === 0" @click="add_strategy_selecter()"></el-button>
                    <el-button icon="el-icon-minus" type="danger" circle v-if="index > 0" @click="remove_strategy_selecter(index)"></el-button>
                </el-form-item>
            </template>
            <el-form-item label="备注:">
                <el-input v-model="add_strategy_config_data.remark" placeholder="策略名称"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel_add_strategy_config_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="add_strategy_config()" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--添加策略配置-->



    <!--编辑策略配置-->
    <el-dialog width="50%" title="编辑策略配置" :visible="edit_strategy_dialog_visible" v-loading="edit_config_loading" :close-on-click-modal="true" :close-on-press-escape="true" :show-close="false">
        <el-form ref="form" :model="edit_strategy_config_data" label-width="200px" label-position="right" size="mini">
            <el-form-item label="帐号:" required>
                <el-select v-model="edit_strategy_config_data.account_id" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in account_map" :key="index" :label="item.account_name" :value="item.account_id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="产品:" required>
                <el-radio-group v-model="edit_strategy_config_data.product_id">
                  <el-radio-button v-for="(item,index) in product_map" :label="item.product_id">{{item.product_name}}</el-radio-button>
                </el-radio-group>
            </el-form-item>

            <template>
                <el-form-item v-for="(item,index) in edit_strategy_config_data.strategy_id" :label="index === 0 ? '策略' : ''" required>
                    <el-select v-model="edit_strategy_config_data.strategy_id[index]" filterable clearable placeholder="请选择">
                        <el-option v-for="(item,index) in strategy_name_list" :key="index" :label="item.strategy_name" :value="item.strategy_id"></el-option>
                    </el-select>
                    <el-button icon="el-icon-plus" type="primary" circle v-if="index === 0" @click="edit_add_strategy_selecter()"></el-button>
                    <el-button icon="el-icon-minus" type="danger" circle v-if="index > 0" @click="edit_remove_strategy_selecter(index)"></el-button>
                </el-form-item>
            </template>
            <el-form-item label="备注">
                <el-input v-model="edit_strategy_config_data.remark" placeholder="策略名称"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel_edit_strategy_config_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="edit_strategy_config()" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--编辑策略配置-->



    <!--策略详情-->
    <el-dialog width="50%" title="策略详情" :visible="show_strategy_info_visible" v-loading="show_strategy_info_loading" close-on-click-modal close-on-press-escape @close="show_strategy_info_visible = false">
        <el-row :gutter="20">
          <el-col :span="4"><span>策略名称</span></el-col>
          <el-col :span="20">{{strategy_info.strategy_name}}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4"><span>策略产品id</span></el-col>
          <el-col :span="20">{{strategy_info.score_id}}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4"><span>pv_dict</span></el-col>
          <el-col :span="20"><pre>{{strategy_info.pv_dict}}</pre></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4"><span>map_dict</span></el-col>
          <el-col :span="20"><pre>{{strategy_info.map_dict}}</pre></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4"><span>pv_seg_innet_dict</span></el-col>
          <el-col :span="20"><pre>{{strategy_info.pv_seg_innet_dict}}</pre></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4"><span>pv_seg_noinnet_dict</span></el-col>
          <el-col :span="20"><pre>{{strategy_info.pv_seg_noinnet_dict}}</pre></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4"><span>filter_dict</span></el-col>
          <el-col :span="20"><pre>{{strategy_info.filter_dict}}</pre></el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
            <el-button @click="show_strategy_info_visible = false" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--策略详情-->




    <!--弹窗-->
</div>

<script type="application/javascript">
    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,
            user_cookie: user_cookie,
            //接口地址
            urls: {
                options_map: url_prefix + '/options/getMap?account=true&user_cookie=' + user_cookie,
                strategy_score_options: url_prefix + '/bxf_strategy/score_options', //策略产品id
                strategy_name_list: url_prefix + '/bxf_strategy/name_list', //策略名称列表
                strategy_config_list: url_prefix + '/bxf_strategy/config/list', //策略配置列表
                strategy_config_add: url_prefix + '/bxf_strategy/config/add', //添加策略配置
                strategy_config_edit: url_prefix + '/bxf_strategy/config/edit', //编辑策略配置
                strategy_config_del: url_prefix + '/bxf_strategy/config/del', //编辑策略配置
            },

            //策略列表
            strategy_config_list: [],

            //策略名称
            strategy_name_list: [],

            //策略产品id
            strategy_score_options: [],

            //状态
            status_map: [
                {status: 1, name: '可用'},
                {status: 2, name: '禁用'}
            ],

            //分页
            total_num: 0,
            page_size: 20,
            current_page: 1,

            account_map : [{'account_id':'default','account_name':'默认'}],
            product_map : [
                {'product_id':256,'product_name':'通话指数(中期逾期) - 256'},
                {'product_id':281,'product_name':'通话指数(短期逾期) - 281'},
            ],

            //搜索条件
            search_form: {
                account_id: '',
                product_id: '',
                strategy_id: '',
                status: 1,//默认可用
            },


            //添加策略数据
            add_strategy_dialog_visible:false,
            add_config_loading:false,
            add_strategy_config_data:{
                account_id:'',
                product_id:'',
                strategy_id:[''],
                remark:''
            },


            //编辑策略数据
            edit_strategy_dialog_visible:false,
            edit_config_loading:false,
            edit_strategy_config_data:{
                account_id:'',
                product_id:'',
                strategy_id:[''],
                remark:''
            },


            //策略详情弹窗
            show_strategy_info_visible:false,
            show_strategy_info_loading:false,
            strategy_info:{},
        },
        computed: {},
        mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);
        },
        created: function () {
            this.get_data();
            this.get_name_list();
            this.get_options();
            this.get_score_options();
        },
        methods: {
            get_data: async function () { //获取表格数据
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    page: this.current_page,
                    limit: this.page_size,
                    account_id: this.search_form.account_id,
                    product_id: this.search_form.product_id,
                    strategy_id: this.search_form.strategy_id,
                    status: this.search_form.status,
                };

                self.loading = true;

                await axios.post(self.urls.strategy_config_list, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.strategy_config_list = response.data.data.list;
                        self.total_num = response.data.data.count;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            get_options: async function () { //获取策略名称列表
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                };
                self.loading = true;
                await axios.get(self.urls.options_map, para).then(function (response) {
                    if (response.data.status === 0) {
                        for (const account_id in response.data.data.account) {
                            self.account_map.push({'account_id':account_id,'account_name':response.data.data.account[account_id]})
                        }
                        // for (const product_id in response.data.data.product) {
                        //     self.product_map.push({'product_id':product_id,'product_name':response.data.data.product[product_id]})
                        // }
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            get_name_list: async function () { //获取策略名称列表
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                };
                self.loading = true;
                await axios.post(self.urls.strategy_name_list, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.strategy_name_list = response.data.data;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            get_score_options: async function () { //获取策略
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                };
                self.loading = true;
                await axios.post(self.urls.strategy_score_options, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.strategy_score_options = response.data.data;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            show_strategy_info:function(strategy_info){ //展示策略详情
                this.strategy_info = strategy_info;
                this.show_strategy_info_visible = true;
            },
            cancel_add_strategy_config_dialog:function(){
                this.add_strategy_dialog_visible = false;
                this.add_strategy_config_data = {
                    account_id:'',
                    product_id:'',
                    strategy_id:[''],
                    remark:''
                };
            },
            add_strategy_config:async function(){
                let self = this;
                let para = self.add_strategy_config_data;

                para.user_cookie = user_cookie

                self.add_config_loading = true;
                await axios.post(self.urls.strategy_config_add, para).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("添加策略配置成功,等待审批!");
                        self.get_data();
                        self.cancel_add_strategy_config_dialog();
                        self.add_strategy_dialog_visible = false;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.add_config_loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.add_config_loading = false;
                });
            },
            add_strategy_selecter:function(){
                this.add_strategy_config_data.strategy_id.push('');
            },
            remove_strategy_selecter:function(index){
                this.add_strategy_config_data.strategy_id.splice(index,1);
            },
            edit_strategy_config_dialog:function(strategy_config_data){
                this.edit_strategy_dialog_visible = true;
                this.edit_config_loading = false;
                this.edit_strategy_config_data = {...strategy_config_data};
            },
            cancel_edit_strategy_config_dialog:function(){
                console.log("before empty",this.edit_strategy_config_data);
                this.edit_strategy_config_data = {
                    account_id:'',
                    product_id:'',
                    strategy_id:[''],
                    remark:''
                };
                console.log("empty",this.edit_strategy_config_data);
                this.edit_strategy_dialog_visible = false;
                this.edit_config_loading = false;
            },
            edit_strategy_config:async function(){
                let self = this;
                let para = self.edit_strategy_config_data;

                para.user_cookie = user_cookie

                self.edit_config_loading = true;
                await axios.post(self.urls.strategy_config_edit, para).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("编辑策略配置成功,等待审批!");
                        self.edit_strategy_dialog_visible = false;
                        self.cancel_edit_strategy_config_dialog();
                        self.get_data();
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.edit_config_loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.edit_config_loading = false;
                });
            },
            edit_add_strategy_selecter:function(){
                this.edit_strategy_config_data.strategy_id.push('');
            },
            edit_remove_strategy_selecter:function(index){
                this.edit_strategy_config_data.strategy_id.splice(index,1);
            },
            del_strategy_config:async function(id){ //编辑策略接口
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    id: id,
                };

                self.edit_loading = true;
                this.$confirm('确认要禁用吗？', '', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'danger'
                }).then(() => {
                    axios.post(self.urls.strategy_config_del, para).then(function (response) {
                        if (response.data.status === 0) {
                            successMsg("禁用策略成功,等待审批!");
                        } else {
                            errorMsg(response.data.msg);
                        }
                        self.edit_loading = false;
                        self.edit_strategy_dialog_visible = false;
                        self.get_data();
                    }).catch(function (error) {
                        errorMsg(error);
                        self.edit_loading = false;
                    });
                }).catch(() => {
                    //console.log()
                });
            },


            //修改每页条数
            handleSizeChange(val) {
                this.page_size = val;
                this.current_page = 1;
                this.get_data();
            },
            //修改页数,获取数据
            handleCurrentChange(val) {
                this.current_page = val;
                this.get_data();
            },
            successFunc: function (e) {
                // console.info("Action:", e.action);
                // console.info("Text:", e.text);
                // console.info("Trigger:", e.trigger);
                // 可以取到目标元素上的自定义属性（可以据此再做一些处理）
                // e.trigger.dataset.test && console.log(e.trigger.dataset.test)
                // 清除选中状态
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc: function (e) {
                // console.error("Action:", e.action);
                // console.error("Trigger:", e.trigger);

                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>