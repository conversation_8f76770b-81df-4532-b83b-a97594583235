<!DOCTYPE html>
<html lang="en">

<head>
    <include file="Common@Public/head"/>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__/clipboard/clipboard.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
    <script type="application/javascript" src="__JS__big.js"></script>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>

    [v-cloak] {
        display: none;
    }
    .el-textarea__inner{
        white-space: pre-wrap;
    }
</style>

<div id="app" v-loading="loading" v-cloak>
    <div class="container">
        <el-form :inline="true" :model="search_form" label-width="100px" class="demo-form-inline" size="mini">
            <el-form-item label="策略名称:">
                <el-select v-model="search_form.strategy_id" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in strategy_name_list" :key="index" :label="item.strategy_name" :value="item.strategy_id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="状态:">
                <el-select v-model="search_form.status" filterable clearable placeholder="请选择">
                    <el-option v-for="(item,index) in status_map" :key="index" :label="item.name" :value="item.status"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="get_data()">查询</el-button>


                <el-button type="success" @click="add_strategy_dialog()">添加策略</el-button>
            </el-form-item>
        </el-form>
    </div>
    <div class="container">
        <el-table :data="strategy_list" border fit>
            <el-table-column label="策略名" prop="strategy_name" width="300" show-overflow-tooltip></el-table-column>
            <el-table-column label="策略产品id" width="220" show-overflow-tooltip>
                <template slot-scope="scope">
                    {{strategy_score_map[scope.row.score_id]}} - {{scope.row.score_id}}
                </template>
            </el-table-column>
            <el-table-column label="pv_dict" prop="pv_dict" show-overflow-tooltip></el-table-column>
            <el-table-column label="map_dict" prop="map_dict" show-overflow-tooltip></el-table-column>
            <el-table-column label="pv_seg_innet_dict" prop="pv_seg_innet_dict" show-overflow-tooltip></el-table-column>
            <el-table-column label="pv_seg_noinnet_dict" prop="pv_seg_noinnet_dict" show-overflow-tooltip></el-table-column>
            <el-table-column label="filter_dict" prop="filter_dict" show-overflow-tooltip></el-table-column>
            <el-table-column label="添加时间" prop="created_at" width="160"></el-table-column>
            <el-table-column label="修改时间" prop="updated_at" width="160"></el-table-column>
            <el-table-column label="操作人" prop="admin" width="150"></el-table-column>
            <el-table-column label="状态" align="center" width="80">
                <template slot-scope="scope">
                    <template v-if="scope.row.status === 1">
                        <el-tag type="success">可用</el-tag>
                    </template>
                    <template v-else>
                        <el-tag type="danger">禁用</el-tag>
                    </template>
                </template>
            </el-table-column>
            <el-table-column fixed="right" fit label="操作" width="240px">
                <template slot-scope="scope">
                    <el-button size="mini" type="warning" @click="edit_strategy_dialog(scope.row.strategy_id)">编辑</el-button>
                    <el-button size="mini" type="danger" v-if="scope.row.status === 1" @click="del_strategy(scope.row.strategy_id)">禁用</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block" style="margin-bottom: 16px;">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current_page" :page-sizes="[10, 20, 30, 40, 50]" :page-size="page_size"
                           layout="total, sizes, prev, pager, next, jumper" :total="total_num"></el-pagination>
        </div>
    </div>


    <!--弹窗-->
    <!--添加策略-->
    <el-dialog width="50%" title="添加策略" :visible="add_strategy_dialog_visible" v-loading="add_loading" :close-on-click-modal="true" :close-on-press-escape="true" :show-close="false">
        <el-form ref="form" :model="add_strategy_data" label-width="200px" label-position="right" size="mini">
            <el-form-item label="策略名称" required>
                <el-input v-model="add_strategy_data.strategy_name" placeholder="策略名称"></el-input>
            </el-form-item>
            <el-form-item label="策略产品id" required>
                <el-radio-group v-model="add_strategy_data.score_id">
                  <el-radio-button v-for="(item,index) in strategy_score_options" :label="item.score_id">{{item.score_product_name}} - {{item.score_id}}</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="pv_dict" required>
                <el-input type="textarea" rows="5" v-model="add_strategy_data.pv_dict" placeholder="pv_dict"></el-input>
            </el-form-item>
            <el-form-item label="map_dict" required>
                <el-input type="textarea" rows="5" v-model="add_strategy_data.map_dict" placeholder="map_dict"></el-input>
            </el-form-item>
            <el-form-item label="pv_seg_innet_dict" required>
                <el-input type="textarea" rows="5" v-model="add_strategy_data.pv_seg_innet_dict" placeholder="pv_seg_innet_dict"></el-input>
            </el-form-item>
            <el-form-item label="pv_seg_noinnet_dict" required>
                <el-input type="textarea" rows="5" v-model="add_strategy_data.pv_seg_noinnet_dict" placeholder="pv_seg_noinnet_dict"></el-input>
            </el-form-item>
            <el-form-item label="filter_dict" required>
                <el-input type="textarea" rows="5" v-model="add_strategy_data.filter_dict" placeholder="filter_dict"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel_add_strategy_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="add_strategy()" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--添加策略-->



    <!--编辑策略-->
    <el-dialog width="50%" title="编辑策略" :visible="edit_strategy_dialog_visible" v-loading="edit_loading" :close-on-click-modal="true" :close-on-press-escape="true" :show-close="false">
        <el-form ref="form" :model="edit_strategy_info" label-width="200px" label-position="right" size="mini">
            <el-form-item label="策略名称" required>
                <el-input v-model="edit_strategy_info.strategy_name" placeholder="策略名称"></el-input>
            </el-form-item>
            <el-form-item label="策略产品id" required>
                <el-radio-group v-model="edit_strategy_info.score_id">
                  <el-radio-button v-for="(item,index) in strategy_score_options" :label="item.score_id">{{item.score_product_name}} - {{item.score_id}}</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="pv_dict" required>
                <el-input type="textarea" rows="5" v-model="edit_strategy_info.pv_dict" placeholder="pv_dict"></el-input>
            </el-form-item>
            <el-form-item label="map_dict" required>
                <el-input type="textarea" rows="5" v-model="edit_strategy_info.map_dict" placeholder="map_dict"></el-input>
            </el-form-item>
            <el-form-item label="pv_seg_innet_dict" required>
                <el-input type="textarea" rows="5" v-model="edit_strategy_info.pv_seg_innet_dict" placeholder="pv_seg_innet_dict"></el-input>
            </el-form-item>
            <el-form-item label="pv_seg_noinnet_dict" required>
                <el-input type="textarea" rows="5" v-model="edit_strategy_info.pv_seg_noinnet_dict" placeholder="pv_seg_noinnet_dict"></el-input>
            </el-form-item>
            <el-form-item label="filter_dict" required>
                <el-input type="textarea" rows="5" v-model="edit_strategy_info.filter_dict" placeholder="filter_dict"></el-input>
            </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel_edit_strategy_dialog()" size="mini">取 消</el-button>
            <el-button type="primary" @click="edit_strategy()" size="mini">确 定</el-button>
        </div>
    </el-dialog>
    <!--编辑策略-->

    <!--弹窗-->
</div>

<script type="application/javascript">
    let url_prefix = '{$Think.config.FINANCE_MANAGE_API_DOMAIN}';
    let user_cookie = getCookie('PHPSESSID');
    let vm = new Vue({
        el: '#app',
        data: {
            //加载遮罩
            loading: false,
            user_cookie: user_cookie,
            //接口地址
            urls: {
                user_auth: url_prefix + '/options/getMap?user_auth=true&user_cookie=' + user_cookie,
                strategy_list: url_prefix + '/bxf_strategy/list', //策略列表
                strategy_name_list: url_prefix + '/bxf_strategy/name_list', //策略名称列表
                strategy_score_options: url_prefix + '/bxf_strategy/score_options', //策略产品id
                strategy_add: url_prefix + '/bxf_strategy/add', //添加策略
                strategy_info: url_prefix + '/bxf_strategy/info', //策略详情
                strategy_edit: url_prefix + '/bxf_strategy/edit', //策略编辑
                strategy_del: url_prefix + '/bxf_strategy/del', //策略编辑
            },

            //策略列表
            strategy_list: [],

            //策略名称列表
            strategy_name_list: [],

            //策略产品id
            strategy_score_options: [],
            strategy_score_map:{},

            //状态
            status_map: [
                {status: 1, name: '可用'},
                {status: 2, name: '禁用'}
            ],

            //分页
            total_num: 0,
            page_size: 20,
            current_page: 1,

            //搜索条件
            search_form: {
                strategy_id: '',
                status: 1,//默认可用
            },


            //添加策略数据
            add_strategy_dialog_visible:false,
            add_loading:false,

            add_strategy_data:{
                strategy_name:'',
                score_id:'',
                pv_dict:'',
                map_dict:'',
                pv_seg_innet_dict:'',
                pv_seg_noinnet_dict:'',
                filter_dict:'',
            },


            //编辑策略数据
            edit_strategy_dialog_visible:false,
            edit_loading:false,

            edit_strategy_id:'',
            edit_strategy_info:{},


        },
        computed: {},
        mounted() {
            this.clipboard = new ClipboardJS(".copy_btn");
            this.clipboard.on("success", this.successFunc);
            this.clipboard.on("error", this.errorFunc);
        },
        created: function () {
            this.get_name_list();
            this.get_score_options();
            this.get_data();
        },
        methods: {
            get_data: async function () { //获取表格数据
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    page: this.current_page,
                    limit: this.page_size,
                    strategy_id: this.search_form.strategy_id,
                    status: this.search_form.status,
                };

                self.loading = true;

                await axios.post(self.urls.strategy_list, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.strategy_list = response.data.data.list;
                        self.total_num = response.data.data.count;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            get_name_list: async function () { //获取策略名称列表
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                };
                self.loading = true;
                await axios.post(self.urls.strategy_name_list, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.strategy_name_list = response.data.data;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },
            get_score_options: async function () { //获取策略
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                };
                self.loading = true;
                await axios.post(self.urls.strategy_score_options, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.strategy_score_options = response.data.data;
                        for (let i = 0; i < self.strategy_score_options.length; i++) {
                            self.strategy_score_map[self.strategy_score_options[i]['score_id']] = self.strategy_score_options[i]['score_product_name'];
                        }
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.loading = false;
                });
            },

            add_strategy_dialog: function () { //展示添加策略弹窗
                this.add_strategy_dialog_visible = true;
            },
            cancel_add_strategy_dialog: function () { //隐藏添加策略弹窗
                this.add_strategy_dialog_visible = false;
                this.add_strategy_data = {
                    strategy_name:'',
                    score_id:'',
                    pv_dict:'',
                    map_dict:'',
                    pv_seg_innet_dict:'',
                    pv_seg_noinnet_dict:'',
                    filter_dict:'',
                };
                this.add_loading = false;
            },
            add_strategy:async function(){ //调用添加策略接口
                let self = this;
                let para = self.add_strategy_data;

                para.user_cookie = user_cookie

                self.add_loading = true;
                await axios.post(self.urls.strategy_add, para).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("添加策略成功,等待审批!");
                        self.cancel_add_strategy_dialog();
                        self.get_data();
                    } else {
                        errorMsg(response.data.msg);
                        self.add_loading = false;
                    }
                }).catch(function (error) {
                    errorMsg(error);
                    // self.cancel_add_strategy_dialog();
                });
            },

            edit_strategy_dialog: function (edit_strategy_id) { //展示编辑策略弹窗
                this.edit_strategy_id = edit_strategy_id;
                this.edit_strategy_dialog_visible = true;
                this.get_strategy_info();
            },
            cancel_edit_strategy_dialog: function () { //隐藏编辑策略弹窗
                this.edit_strategy_dialog_visible = false;
            },
            get_strategy_info: async function () { //获取策略详情
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    strategy_id: self.edit_strategy_id,
                };
                self.loading = true;
                await axios.post(self.urls.strategy_info, para).then(function (response) {
                    if (response.data.status === 0) {
                        self.edit_strategy_info = response.data.data;
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.edit_loading = false;
                    self.get_data();
                }).catch(function (error) {
                    errorMsg(error);
                    self.edit_loading = false;
                });
            },
            edit_strategy:async function(){ //编辑策略接口
                let self = this;
                let para = self.edit_strategy_info;

                para.user_cookie = user_cookie

                self.edit_loading = true;
                await axios.post(self.urls.strategy_edit, para).then(function (response) {
                    if (response.data.status === 0) {
                        successMsg("编辑策略成功,等待审批!");
                        self.edit_strategy_dialog_visible = false;
                        self.get_data();
                    } else {
                        errorMsg(response.data.msg);
                    }
                    self.edit_loading = false;
                }).catch(function (error) {
                    errorMsg(error);
                    self.edit_loading = false;
                });
            },
            del_strategy:async function(strategy_id){ //编辑策略接口
                let self = this;
                let para = {
                    user_cookie: user_cookie,
                    strategy_id: strategy_id,
                };

                self.edit_loading = true;
                this.$confirm('确认要禁用吗？', '', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'danger'
                }).then(() => {
                    axios.post(self.urls.strategy_del, para).then(function (response) {
                        if (response.data.status === 0) {
                            successMsg("删除策略成功,等待审批!");
                            self.edit_strategy_dialog_visible = false;
                            self.get_data();
                        } else {
                            errorMsg(response.data.msg);
                        }
                        self.edit_loading = false;
                    }).catch(function (error) {
                        errorMsg(error);
                        self.edit_loading = false;
                    });
                }).catch(() => {
                    //console.log()
                });
            },


            //修改每页条数
            handleSizeChange(val) {
                this.page_size = val;
                this.current_page = 1;
                this.get_data();
            },
            //修改页数,获取数据
            handleCurrentChange(val) {
                this.current_page = val;
                this.get_data();
            },
            successFunc: function (e) {
                // console.info("Action:", e.action);
                // console.info("Text:", e.text);
                // console.info("Trigger:", e.trigger);
                // 可以取到目标元素上的自定义属性（可以据此再做一些处理）
                // e.trigger.dataset.test && console.log(e.trigger.dataset.test)
                // 清除选中状态
                e.clearSelection();

                this.$notify({
                    title: '成功',
                    message: '复制成功',
                    type: 'success',
                    showClose: false
                });
            },
            errorFunc: function (e) {
                // console.error("Action:", e.action);
                // console.error("Trigger:", e.trigger);

                this.$notify.error({
                    title: '失败',
                    message: '操作失败，请重试！',
                    showClose: false
                });
            },
            //格式化时间
            formatDate: function (date) {
                if (date !== '' && date !== undefined && date !== null && date !== 0) {
                    date = new Date(parseInt(date) * 1000)
                    let y = date.getFullYear()
                    let m = date.getMonth() + 1
                    m = m < 10 ? ('0' + m) : m
                    let d = date.getDate()
                    d = d < 10 ? ('0' + d) : d

                    let h = date.getHours();
                    h = h < 10 ? ('0' + h) : h

                    let mm = date.getMinutes();
                    mm = mm < 10 ? ('0' + mm) : mm
                    let s = date.getSeconds();
                    s = s < 10 ? ('0' + s) : s
                    return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
                } else {
                    return ''
                }
            },
        },
    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

    function getCookie(name) {
        let reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        let arr = document.cookie.match(reg);
        if (arr)
            return (arr[2]);
        else
            return null;
    }
</script>
</body>
</html>