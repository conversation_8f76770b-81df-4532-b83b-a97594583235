<head>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
</head>
<form class="layui-form" style="padding-top: 10px;" action="">
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">渠道:</label>
            <label class="layui-form-label" style="text-align: left;">{$data.channel}</label>
        </div>
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">接口:</label>
            <label class="layui-form-label" style="text-align: left;width: 130px;">{$data.custom_key}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">类型:</label>
            <label class="layui-form-label" style="text-align: left;">{$data.type_name}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">一级阈值报警</label>
            <div class="layui-input-block">
                <input type="number" style="width: 65px; display: inline;" class="layui-input" name="th_one" id="th_one" value="{$data.th_one}" placeholder="非空"/>%
            </div>
        </div>
        <div class="layui-inline" style="width: 250px;">
            <label class="layui-form-label" style="width: 100px;">二级阈值报警</label>
            <div class="layui-input-block">
                <input type="number" style="width: 65px; display: inline;" class="layui-input" name="th_two" id="th_two" value="{$data.th_two}" placeholder="非空"/>%
            </div>
        </div>
    </div>
<!--    <div class="layui-form-item">-->
<!--        <div class="layui-inline" style="width: 290px;">-->
<!--            <label class="layui-form-label" style="width: 100px;">一级响应时间报警</label>-->
<!--            <div class="layui-input-block">-->
<!--                <input type="number"  style="width: 65px; display: inline;" class="layui-input" name="runtime_th_one" id="runtime_th_one" value="{$data.runtime_th_one}" placeholder="非空"/>%-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="layui-inline" style="width: 250px;">-->
<!--            <label class="layui-form-label" style="width: 100px;">二级响应时间报警</label>-->
<!--            <div class="layui-input-block">-->
<!--                <input type="number"  style="width: 65px; display: inline;" class="layui-input" name="runtime_th_two" id="runtime_th_two" value="{$data.runtime_th_two}" placeholder="非空"/>%-->
<!--            </div>-->
<!--        </div>-->

<!--    </div>-->
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 260px; margin-top: 37px;">
            <button type="button" id="edit_submit" class="layui-btn" lay-filter="demo1">立即提交</button>
        </div>
    </div>

    <input type="hidden" id="id" name="id" value="{$data.id}">

</form>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js"></script>
<script type="application/javascript"></script>
<script type="text/javascript">
    $("#edit_submit").on('click', function(){
        let editIndex = layer.load(0, {shade : [0.3, '#393D49']});
        let id = $('#id').val();
        let th_one = parseFloat($('#th_one').val());
        let th_two = parseFloat($('#th_two').val());
        if (th_two >= th_one) {
            layer.close(editIndex);
            layer.alert("最大值不能小于最小值");
            return false;
        }
        $.ajax({
            type: "POST",
            url:"/Config/ChannelStatis/statisEdit",
            data:{
                "id": id,
                "th_one": th_one,
                "th_two": th_two,
            },
            error: function(request) {
                layer.close(editIndex);
                layer.alert("接口异常");
                return false;
            },
            dataType: "json",
            success: function(data) {
                if(data.status == 0){
                    layer.alert("操作成功", function(index){
                        layer.close(index);
                        window.parent.layer.closeAll();
                        window.parent.loadTable();
                    });
                }else{
                    layer.close(editIndex);
                    layer.alert(data.msg);
                }
            }
        });
    });
</script>