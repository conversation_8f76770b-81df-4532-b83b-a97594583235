<!DOCTYPE html>
<html lang="en">
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__xm-select.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }

        .index-btn {
            margin : 5px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container">
    <form class="layui-form layui-row list_form">
        <div style="display: flex">
            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
                <label class="layui-form-label" >选择渠道</label>
                <div class="layui-input-block">
                    <div name='channel[]' id="channel" class="xm-select-demo"></div>
                </div>
            </div>


            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md3 layui-col-lg2" style="margin-left:10px;">
                <div class="layui-btn layui-btn-normal" id="query">
                    <i class="layui-icon">&#xe615;</i> 查询
                </div>
                <div class="layui-btn layui-btn-normal" id="add">
                    <i class="layui-icon">&#xe615;</i> 新增
                </div>
            </div>
        </div>

    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="table"></div>
</div>
</body>
</html>
<script type="text/html" id="barEdit">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</script>

<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js"></script>
<script type="application/javascript">
    layui.laydate.render({
        elem  : '#date_section',
        type  : 'date',
        range : true
    });
    channel = xmSelect.render({
        el: '#channel',
        repeat: true,
        data: JSON.parse('{$data.channelOption}')
    });

    //加载表格
    function loadTable(initWhere = {}) {
        //获取查询的where条件
        let url = "/Config/ChannelStatis/statisList";
        let where = getWhere();
        where     = Object.assign(where, initWhere);

        //异步请求数据
        let index = layer.load(0, {shade : [0.3, '#393D49']});
        let fields = [[
            {
                field    : 'channel_label',
                title    : '渠道',
                align    : 'center',
                width    : "20%",
            },
            {
                field    : 'custom_key',
                title    : '接口',
                align    : 'center',
                width    : "20%",
            },
            {
                field    : 'type_name',
                title    : '类型',
                align    : 'center',
            },
            {
                field    : 'th_one',
                title    : '严重报警阈值',
                align    : 'right',
                edit     : 'text',
            },
            {
                field    : 'th_two',
                title    : '关注报警阈值',
                align    : 'right',
                edit     : 'text',
            },
            // {
            //     field    : 'runtime_th_one',
            //     title    : '一级响应时间报警',
            //     align    : 'left',
            //     width    : '10%',
            // },
            // {
            //     field    : 'runtime_th_two',
            //     title    : '二级响应时间报警',
            //     align    : 'left',
            //     width    : '10%',
            // },
            // {title: '操作', fixed: 'right', width:'10%', align:'left', toolbar: '#barEdit'}
        ]];

        layui.table.render({
            elem           : '#list_table',
            title          : '渠道查得率阈值',
            url            : url,
            method         : 'post',
            where          : where,
            page           : true,
            limits         : [15, 30, 60, 100, 1000],
            limit          : 15,
            cols           : fields,
            parseData      : function (res) {   //渲染表格之前数据校验和数据格式整理
                if (0 !== Number(res.status)) {
                    layer.close(index);
                    layer.alert("接口请求失败，请联系开发人员", {icon : 2, shade : [0.3, '#393D49']});
                    return;
                }
                json = {
                    code : 0,
                    count : res.data.count,
                    data : res.data.data
                };
                return json;
            },
            //禁用前端排序
            autoSort       : false,
            // cellMinWidth   : 200,
            done            : function(res, curr, count) {  //渲染完表格之后事件
                layer.close(index);
            }
        });
    }

    //获取查询where条件
    function getWhere() {
        let where         = {};
        let apikey = $("#apikey").val();
        if ('' !== apikey) {
            where.apikey = apikey;
        }

        let product_id = $("#product_id").val();
        if ('' !== product_id) {
            where.product_id = product_id;
        }

        where.channel = channel.getValue('value');
        return where;
    }

    //条件查询
    $("#query").click(function () {
        loadTable();
    });

    //条件查询
    $("#add").click(function () {
        layer.open({
            type : 2,
            title : '新增产品值分布阈值',
            content : '/Config/ChannelStatis/statisAddView',
            area: ['650px', '400px'],
        })
    });

    layui.table.on('tool(table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        let data = obj.data; //获得当前行数据
        var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
        // var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）

        if(layEvent === 'edit'){
            layer.open({
                type : 2,
                title : '产品值分布阈值配置',
                content : '/Config/ChannelStatis/statisInfo.html?id=' + data.id,
                area: ['650px', '300px'],
            })

        } else if(layEvent === 'del'){ //删除
            // layer.confirm('真的删除行么', function(index){
            //     obj.del(); //删除对应行（tr）的DOM结构，并更新缓存
            //     layer.close(index);
            //     //向服务端发送删除指令
            // });
        }
    });

    //编辑
    layui.table.on('edit(table)', function (obj) {
        let value = parseFloat(obj.value);
        if (value <= 0) {
            layer.alert('输入有误');
            return false;
        }
        if ((obj.field === "th_one") && (value <= obj.data['th_two'])) {
            let old=$(this).prev().text();//旧值
            $(this).val(old);//重新掰回来
            layer.alert('请先修改最小值');
            return false;
        }
        let data = {
            'id': obj.data['id'],
            'value': value,
            'field': obj.field,
        };
        $.ajax({
            type: "POST",
            url: "/Config/ChannelStatis/statisEdit",
            data: data,
            error: function(request) {
                layer.alert("接口异常");
                return false;
            },
            dataType: "json",
            success: function(data) {
                if(data.status == 0){
                    layer.alert("操作成功");
                }else{
                    layer.alert(data.msg);
                }
            }
        });
    });
    $("#edit_submit").on('click', function(){

    });

    //排序
    layui.table.on('sort(table)', function (obj) {
        loadTable({
            sort_field : obj.field,
            sort_type  : obj.type
        }, obj);
    });
    loadTable();
</script>