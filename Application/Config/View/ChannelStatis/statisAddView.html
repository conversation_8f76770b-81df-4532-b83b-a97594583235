<head>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
</head>
<form class="layui-form" style="padding-top: 10px;" action="">
    <div class="layui-form-item">
        <label class="layui-form-label">渠道</label>
        <div class="layui-input-inline">
            <select name="channel_id" lay-search id="channel_id" >
                <option value="">--请选择--</option>
                {$data.channelSelect}
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-inline" style="width: 50%;">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-inline">
                <select name="type" lay-search id="type">
                    <option value="">--请选择--</option>
                    {$data.typeOption}
                </select>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-inline" style="width: 50%;">
            <label class="layui-form-label">接口</label>
            <div class="layui-input-inline">
                <input type="text" name="custom_key" id="custom_key" placeholder="请输入" autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">一级阈值报警</label>
            <div class="layui-input-block">
                <input type="number" style="width: 57px; display: inline;" class="layui-input" id="th_one" name="th_one" value="{$data.data.th_one}" placeholder="非空"/>%
            </div>
        </div>
        <div class="layui-inline" style="width: 250px;">
            <label class="layui-form-label" style="width: 100px;">二级阈值报警</label>
            <div class="layui-input-block">
                <input type="number" style="width: 57px; display: inline;" class="layui-input" id="th_two" name="th_two" value="{$data.data.th_two}" placeholder="非空"/>%
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 260px;">
            <button type="button" id="add_submit" class="layui-btn" lay-submit="" lay-filter="demo1">立即提交</button>
<!--            <button type="reset" class="layui-btn layui-btn-primary">重置</button>-->
        </div>
    </div>

    <input type="hidden" name="id" value="{$list['id']}">

</form>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js"></script>
<script type="application/javascript"></script>
<script type="text/javascript">

    $("#add_submit").on('click', function() {
        let addIndex = layer.load(0, {shade : [0.3, '#393D49']});
        let cid = parseInt($('#channel_id').val());
        let custom_key = $('#custom_key').val();
        let type = $('#type').val();
        let th_one = parseFloat($('#th_one').val());
        let th_two = parseFloat($('#th_two').val());
        if(!cid){
            layer.close(addIndex);
            layer.alert('请选择渠道');
            return false;
        }
        if(!type){
            layer.close(addIndex);
            layer.alert('请选择类型');
            return false;
        }
        if(!custom_key){
            layer.close(addIndex);
            layer.alert('接口不能为空');
            return false;
        }
        if (!th_one || !th_two) {
            layer.close(addIndex);
            layer.alert("预警值不能为0");
            return false;
        }
        if ((th_one <= th_two)) {
            layer.close(addIndex);
            layer.alert("最大值不能小于最小值");
            return false;
        }

        $.ajax({
            type: "POST",
            url:"/Config/ChannelStatis/statisAdd",
            data:{
                "cid": cid,
                "type": type,
                "custom_key": custom_key,
                "th_one": th_one,
                "th_two": th_two,
            },
            error: function(request) {
                layer.close(addIndex);
                layer.alert("接口异常");
                return false;
            },
            dataType: "json",
            success: function(data) {
                if(data.status == 0){
                    layer.alert("操作成功", function(index){
                        layer.close(index);
                        window.parent.layer.closeAll();
                        window.parent.loadTable();
                    });
                }else{
                    layer.close(addIndex);
                    layer.alert(data.msg);
                }
            }
        });
    });
</script>