<!DOCTYPE html>
<html lang="en">
    <head>
        <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__/vue/vue.js"></script>
        <script type="application/javascript" src="__JS__/vue/index.js"></script>
        <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <style>
        .el-dialog__body .el-form .el-input__inner{
            width: 256px;
        }
        .el-dialog{
            height: 550px;
            overflow-y: scroll;
        }
    </style>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body">

                    <el-form :inline="true" :model="searchForm"  label-width="100px" class="demo-form-inline">

                        <el-form-item label="账号">
                            <el-select v-model="searchForm.apikey"  filterable clearable placeholder="请选择账号">
                                <el-option
                                        v-for="item in accountList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>


                        <el-form-item label="产品">
                            <el-select v-model="searchForm.product_id" filterable clearable placeholder="请选择">
                                <el-option
                                        v-for="item in productList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" @click="searchTableData()">查询</el-button>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="success"   @click="addTableData()">添加数据</el-button>
                        </el-form-item>
                    </el-form>

                </div>
            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%">
                        <el-table-column
                                prop="apikey"
                                label="apikey"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="account_name"
                                label="账号名称"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="product_id"
                                label="产品ID"
                               >
                        </el-table-column>
                        <el-table-column
                                prop="product_name"
                                label="产品名称"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="min_org"
                                label="MinOrg"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="max_org"
                                label="MaxOrg"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="min_trans"
                                label="MinTrans"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="max_trans"
                                label="MaxTrans"
                        >
                        </el-table-column>

                        <el-table-column
                                prop="create_at"
                                label="创建时间"
                        >
                        </el-table-column>

                        <el-table-column label="操作" width="180">
                            <template slot-scope="scope">
                                <el-button
                                        size="mini"
                                        @click="handleEdit(scope.$index, scope.row.id)">编辑</el-button>

                                <el-button
                                        size="mini"
                                        type="danger"
                                        @click="handleDelete(scope.$index, scope.row.id)">删除</el-button>

                            </template>
                        </el-table-column>

                    </el-table>
                </template>

                <el-dialog :title="dialog_title" :visible.sync="dialogFormVisible" @close="closeDialogCallBack('form')" v-if="dialogFormVisible">
                    <el-form :model="form" :rules="rules" ref="form">

                        <el-form-item label="账号" prop="apikey" :label-width="formLabelWidth">
                            <el-select v-model="form.apikey"  filterable clearable placeholder="请选择账号">
                                <el-option
                                        v-for="item in accountList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="产品" prop="product_id" :label-width="formLabelWidth">
                            <el-select v-model="form.product_id" filterable clearable placeholder="请选择产品">
                                <el-option
                                        v-for="item in productList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="MinOrg" prop="min_org" :label-width="formLabelWidth">
                            <el-input v-model.number="form.min_org" autocomplete="off" ></el-input>
                        </el-form-item>

                        <el-form-item label="MaxOrg" prop="max_org" :label-width="formLabelWidth">
                            <el-input v-model.number="form.max_org" autocomplete="off" ></el-input>
                        </el-form-item>

                        <el-form-item label="MinTrans" prop="min_trans" :label-width="formLabelWidth">
                            <el-input v-model.number="form.min_trans" autocomplete="off" ></el-input>
                        </el-form-item>

                        <el-form-item label="MaxTrans" prop="max_trans" :label-width="formLabelWidth">
                            <el-input v-model.number="form.max_trans" autocomplete="off" ></el-input>
                        </el-form-item>


                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="dialogFormVisible = false">取 消</el-button>
                        <el-button type="primary" @click="onSubmit('form')">确 定</el-button>
                    </div>
                </el-dialog>

            </div>

            <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40, 50]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalNum">
                </el-pagination>
            </div>

        </div>

    </div>

    <script type="application/javascript">

        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getCMCCScoreList";

        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                totalNum: 0,
                pageSize: 10,
                currentPage:1,
                //fatherProductList: [],
                productList: [],
                accountList:[],
                //customerList:[],
                //operatorList:[],
                pickerOptions: {},
                getUrl: url,
                dialogFormVisible: false,
                dialog_title:'添加数据',
                operate:false,
                form: {
                    id:'',
                    apikey: '',
                    product_id: '',
                    min_org: '',
                    max_org: '',
                    min_trans: '',
                    max_trans: ''
                },
                formLabelWidth: '120px',
                searchForm: {
                    apikey: '',
                    product_id:''
                },
                rules:{
                    apikey: [
                        { required: true, message: '请选择账号', trigger: 'change' }
                    ],
                    product_id: [
                        { required: true, message: '请选择产品', trigger: 'change' }
                    ],
                    min_org: [
                        { required: true, message: 'min_org不能为空' },
                        { type: 'number', message: 'min_org必须为数字值'}
                    ],
                    max_org: [
                        { required: true, message: 'max_org不能为空' },
                        { type: 'number', message: 'max_org必须为数字值'}
                    ],
                    min_trans: [
                        { required: true, message: 'min_trans不能为空' },
                        { type: 'number', message: 'min_trans必须为数字值'}
                    ],
                    max_trans: [
                        { required: true, message: 'max_trans不能为空' },
                        { type: 'number', message: 'max_trans必须为数字值'}
                    ]
                }
            },
            created: function(){
                this.getTableData();
                this.getAccountSelectData();
                this.getAllProduct();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    var apikey = this.searchForm.apikey;

                    var product_id = this.searchForm.product_id;
                    var where = {limit:this.pageSize, page:this.currentPage};
                    if(apikey){
                        where.apikey = apikey;
                    }
                    if(product_id){
                        where.product_id = product_id;
                    }
                    axios.post(url, where).then(function (response) {
                        //console.log(response);
                        self.tableData = response.data.data.list;
                        self.totalNum = response.data.data.count;
                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                getAllProduct:function(){
                    //this.productList = [];
                    //var father_id = this.searchForm.father_id;
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getMap";
                    axios.get(url, {
                        params:{product:true}
                    }).then(function (response) {
                        //console.log(response.data.data.product);
                        Object.getOwnPropertyNames(response.data.data.product).forEach(function(key){
                            var name = key + ':' + response.data.data.product[key];
                            self.productList.push({value:key,label:name});
                        });

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                getAccountSelectData:function(){
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/account";
                    axios.get(url, {
                        params:{options:false}
                    }).then(function (response) {

                        Object.getOwnPropertyNames(response.data.data).forEach(function(key){
                            var name = response.data.data[key];
                            self.accountList.push({value:key,label:name});
                        });
                        self.accountList.push({value:'default',label:'default'});

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                onSubmit:function(formName){

                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            var user_cookie = getCookie('PHPSESSID');
                            this.form.user_cookie = user_cookie;
                            var request_params = this.form;
                            var self = this;
                            var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/saveCMCCScore";

                            axios.post(url, request_params).then(function (response) {
                                if(response.data.code == 0){
                                    successMsg(response.data.msg);

                                }else{
                                    errorMsg(response.data.msg);
                                }
                            }).catch(function (error) {
                                console.log(error);
                                errorMsg(error);
                            });

                        } else {
                            console.log('error submit!!');
                            return false;
                        }
                    });

                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.getTableData();
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                    this.currentPage = val;
                    this.getTableData();
                },
                searchTableData:function (){
                    this.currentPage = 1;
                    this.getTableData();
                },
                addTableData:function(){
                    this.dialog_title = '添加数据';
                    this.operate = false;
                    this.resetForm();

                    this.dialogFormVisible = true;

                },
                closeDialogCallBack:function(formName){
                    this.$refs[formName].resetFields();
                },
                handleEdit(index, id) {
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getCMCCScore";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.form.id = response.data.data.id;
                            self.form.apikey = response.data.data.apikey;
                            self.form.product_id = response.data.data.product_id;

                            self.form.min_org = response.data.data.min_org;
                            self.form.max_org = response.data.data.max_org;
                            self.form.min_trans = response.data.data.min_trans;
                            self.form.max_trans = response.data.data.max_trans;

                            self.dialog_title = '编辑数据';
                            self.operate = true;
                            self.dialogFormVisible = true;

                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                handleDelete(index, id) {
                    var self = this;
                    var user_cookie = getCookie('PHPSESSID');
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/delCMCCScore";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });
                },
                resetForm:function(){
                    this.form.id = '';
                    this.form.apikey = '';
                    this.form.product_id = '';
                    this.form.min_org = '';
                    this.form.max_org = '';
                    this.form.min_trans = '';
                    this.form.max_trans = '';
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            vm.resetForm();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }
        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return (arr[2]);
            else
                return null;
        }

    </script>
    </body>
</html>
