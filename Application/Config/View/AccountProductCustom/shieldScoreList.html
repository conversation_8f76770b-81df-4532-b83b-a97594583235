<!DOCTYPE html>
<html lang="en">
<head>
    <!--<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <script src="__JS__xm-select.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
    <style>
        /*表格条纹*/
        .strip_1 {
            background-color: #f2f2f2;
        }
        .layui-table-tool-temp{
            Display:none;
        }
        .layui-table-tool{
            display: none;
        }
    </style>
    <style>
        .table_title{
            width : 100%;
            min-height: 40px;
            line-height:40px;
            text-indent:10px;
            font-size:14px;
            color:red;
        }
        .table_title b{
            margin:0 10px;
            font-size:16px;
        }
        .row-first {
            margin-bottom: 10px;
        }
        label {
            margin-left: 10px;
        }
        #loading{
            width:100%;
            height:100%;
            position:fixed;
            background:rgba(200, 200, 200, 0.2);
            z-index:100;
            top:0;
            left:0;
            display:none;
        }
        .not_null{
            color:red;
            margin-right:10px;
        }
        @keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        @-webkit-keyframes lds-spinner {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
        .lds-spinner {
            position: fixed;
        }
        .lds-spinner div {
            left: 50%;
            top: 50%;
            margin-top:-20px;
            margin-left:-6px;
            position: fixed;
            -webkit-animation: lds-spinner linear 1s infinite;
            animation: lds-spinner linear 1s infinite;
            background: #286090;
            width: 12px;
            height: 40px;
            border-radius: 20%;
            -webkit-transform-origin: 6px 80px;
            transform-origin: 6px 80px;
        }
        .lds-spinner div:nth-child(1) {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-animation-delay: -0.916666666666667s;
            animation-delay: -0.916666666666667s;
        }
        .lds-spinner div:nth-child(2) {
            -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
            -webkit-animation-delay: -0.833333333333333s;
            animation-delay: -0.833333333333333s;
        }
        .lds-spinner div:nth-child(3) {
            -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
            -webkit-animation-delay: -0.75s;
            animation-delay: -0.75s;
        }
        .lds-spinner div:nth-child(4) {
            -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
            -webkit-animation-delay: -0.666666666666667s;
            animation-delay: -0.666666666666667s;
        }
        .lds-spinner div:nth-child(5) {
            -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
            -webkit-animation-delay: -0.583333333333333s;
            animation-delay: -0.583333333333333s;
        }
        .lds-spinner div:nth-child(6) {
            -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
            -webkit-animation-delay: -0.5s;
            animation-delay: -0.5s;
        }
        .lds-spinner div:nth-child(7) {
            -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
            -webkit-animation-delay: -0.416666666666667s;
            animation-delay: -0.416666666666667s;
        }
        .lds-spinner div:nth-child(8) {
            -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
            -webkit-animation-delay: -0.333333333333333s;
            animation-delay: -0.333333333333333s;
        }
        .lds-spinner div:nth-child(9) {
            -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
            -webkit-animation-delay: -0.25s;
            animation-delay: -0.25s;
        }
        .lds-spinner div:nth-child(10) {
            -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
            -webkit-animation-delay: -0.166666666666667s;
            animation-delay: -0.166666666666667s;
        }
        .lds-spinner div:nth-child(11) {
            -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
            -webkit-animation-delay: -0.083333333333333s;
            animation-delay: -0.083333333333333s;
        }
        .lds-spinner div:nth-child(12) {
            -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
            -webkit-animation-delay: 0s;
            animation-delay: 0s;
        }
        .lds-spinner {
            width: 200px !important;
            height: 200px !important;
            -webkit-transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
            transform: translate(-100px, -100px) scale(1) translate(100px, 100px);
        }
        .add_image,.edit_image{
            width: auto;
            height: 150px;
            border: 1px solid #ccc;
            display: inline-block;
            cursor: pointer;
            overflow:hidden;
        }
        .add_image::after,.edit_image::after{
            display:block;
            width: 150px;
            height: 150px;
            content: '+';
            font-size: 100px;
            line-height: 150px;
            text-align: center;
        }
        .proof{
            width:100px;
            height:100px;
            border:1px solid #ccc;
            cursor:pointer;
        }
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container">
    <form class="layui-form layui-row list_form">
        <div style="display: flex">


            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width:326px">
                <label class="layui-form-label" style="width: 136px;">账号名或apikey：</label>
                <div class="layui-input-block">
                    <input type="text" name="account_apikey" id="account_apikey" class="form-control" value="{$input.max_money}" placeholder="请输入账号名或apikey" autocomplete="off"/>

                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3" style="width:260px">
                <label class="layui-form-label">Pid：</label>
                <div class="layui-input-block">
                    <input type="text" name="p_id" id="p_id" class="form-control" value="{$input.max_money}" placeholder="请输入Pid" maxlength="16" autocomplete="off"/>

                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
                <div class="layui-btn layui-btn-normal"  id="query" >
                    <i class="layui-icon">&#xe615;</i> 查询
                    <!--<input id="list_submit" type="button" class="btn btn-primary btn-sm" value="查询">-->

                </div>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
                <button type="button" id="file_in" class="layui-btn" >
                    批量上传
                </button>
            </div>

            <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">

                <button type="button" id="export" class="layui-btn layui-btn-warm">导出</button>
            </div>

        </div>

    </form>
</div>

<div class="container">
    <div id="table_and_page_div_id">
        <div class="panel panel-default table-responsive" id="tableform">

        </div>
    </div>
</div>

<div class="modal fade" id="file_in_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >请上传Excel文件</h4>
            </div>
            <div class="modal-body">
                <form action="{:U('add')}" id="file_in_form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <input type="hidden" name="type" value="file_in">
                        <label for="add_receipt_serial">选择Excel文件</label>
                        <input type="file" name="file" id="excel_file" />
                    </div>
                    <div class="form-group" style="color:red;line-height:24px;">
                        * Excel文件的大小需要小于500KB，如果大于500KB，需要分多次上传<br/>
                        * Excel文件只支持xlsx或xls两种文件格式<br/>
                        * Excel文件的内容须严格按照以下规则填写，数据与数据之间不可存在空行，字段的位置不可随意更改。<br/>
                        * Excel文件第一行不可填写数据，系统将从第二行开始批量导入<br/>
                        * 点击下载 <a href="/statics/template/shieldScoreRule.xlsx">Excel模板</a><br/>
                        * 批量导入所需时间可能较长，请耐心等待<br/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file_in_submit">保存</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="file_in_alert" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" style="position:absolute;overflow-y:auto;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" >提示</h4>
            </div>
            <div class="modal-body" id="file_in_alert_body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="know">我知道了</button>
            </div>
        </div>
    </div>
</div>

<div id="loading">
    <div class="modal-dialog" role="document">
        <div class="lds-css ng-scope">
            <div class="lds-spinner" style="top:200px;left:50%;margin-left:-100px;"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
        </div>
    </div>
</div>


<!--
<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue" lay-filter="table">
            <thead style="background:#009688; color:white">
            <tr>
                <th style="width:6%;">客户 </th>
                <th style="width:6%;">子产品 </th>
                <th style="width:6%;">状态 </th>
                <th style="width:6%;">返回值 </th>
                <th style="width:6%;">响应时间 </th>
                <th style="width:6%;">调用时间 </th>
            </tr>
            </thead>

            <tbody id="table_data">

                <?php echo $detail_str; ?>
            </tbody>

        </table>
    </div>
</div>
-->
<!--<script src="__JS__opdata.js"></script>-->
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>

<!--
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__JS__bootstrap-select.min.js"></script>

<script type="application/javascript" src="__STATICS__bignumber.js-master/bignumber.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript" src="__JS__eleFixed.min.js"></script>
<script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/dateFormat.js"></script>
<script type="application/javascript" src="__STATICS__/jquery-dateFormat-master/src/jquery.dateFormat.js"></script>
-->
<script type="application/javascript">

    $(document).ready(function () {
        //批量导入收款单（模态框）
        $("#file_in").click(function () {
            $("#file_in_modal").modal({});
        });

        //批量导入收款单
        $("#file_in_submit").click(function () {
            if (valid_empty($("#excel_file"))) {
                alert('请选择需要上传的Excel');
                return false;
            }
            //验证文件的大小
            let file = $("#excel_file")[0].files[0];
            let size = file.size;
            if (size>=500*1024) {
                alert('文件过大，请将文件分割后导入');
                return false;
            }
            //验证文件后缀
            let ext = file.name.split('.')[1];
            if (ext!='xls' && ext!='xlsx') {
                alert('文件格式不正确，请核对后导入');
                return false;
            }
            $('#file_in_modal').modal('hide');
            $("#loading").show();
            //解决执行ajax后上述的动作不执行的BUG
            setTimeout(() => {
                let formData = new FormData();
                formData.append('type', 'file_in');
                formData.append('file', file);
                let url ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/setAccountProductCustom?ruleType=615_score";

                $.ajax({
                    url : url,
                    data : formData,
                    type : 'post',
                    async : false,
                    cache: false,
                    processData: false,
                    contentType: false,
                    success : function (res) {
                        $("#loading").hide();
                        let tips = '';
                        $.each(res.data, function(index,value){
                            tips += value+"<br/>";
                        });
                        $("#file_in_alert_body").html(tips);
                        $("#file_in_alert").modal();
                    },
                    error : function ()
                    {
                        $("#loading").hide();
                        alert('导入失败');
                    }
                });
            }, 10);
        });

        $("#know").click(function () {
            location.href = location.href;
        });

        $("#query").click(function () {
            let account_apikey = $('#account_apikey').val();
            let pid = $('#p_id').val();
            let url ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getAccountProductCustomList?account_apikey="+
                account_apikey+'&product_id='+pid+'&ruleType=615_score';
            getData(url);

        });


        $("#export").click(function () {
            let account_apikey = $('#account_apikey').val();
            let pid = $('#p_id').val();
            let url ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getAccountProductCustomList?account_apikey="+
                account_apikey+'&product_id='+pid+'&ruleType=615_score';

            Request.get(url, {export:true}).then(function (data) {
                //console.log(data.data);
                if(data.code != 0){
                    layer.msg(data.message);
                    return false;
                }
                let sheetHeader = ['account_name','apikey','pids','p10_out','p20_out','p30_out','p40_out','p50_out','p60_out','p70_out','p80_out','p90_out','p10_mod','p20_mod','p30_mod','p40_mod','p50_mod','p60_mod','p70_mod','p80_mod','p90_mod'];
                let sheetData = [];

                $.each(data.data, function (key, val){
                    if (!val['product_id']) {
                        return true;
                    }

                    let tmpData = [];
                    tmpData['account_name']=val['account_name'];
                    tmpData['apikey']=val['apikey'];
                    tmpData['pids']=val['product_id'];
                    //浏览器后端传过来的是json对象，但是我们前端是需要Javascript的对象，所以需要做个转换
                    $.each(JSON.parse(val['data']), function(index, value){
                        tmpData[index]=value;
                    });
                    let tmpSheetData = [];
                    $.each(sheetHeader, function (headerKey, headerVal){
                        tmpSheetData[headerVal] = tmpData[headerVal];
                    })
                    sheetData.push(tmpSheetData)
                });
                let now = new Date();
                let year = now.getFullYear(); //得到年份
                let month = now.getMonth(); //得到月份
                let date = now.getDate(); //得到日期
                month = month + 1;
                if (month < 10) month = "0" + month;
                if (date < 10) date = "0" + date;
                let time = year + "-" + month + "-" + date; //（格式化"yyyy-MM-dd"）
                let option = {};
                option.fileName = '号码风险等级分点位配置导出'+time;
                option.datas = [{sheetData:sheetData, sheetHeader:sheetHeader}];
                (new ExportJsonExcel(option)).saveExcel();

            });


        });


    });


    //验证制定的数据是否为空
    function valid_empty(ele) {
        let val = ele.val();
        if (val=='') {
            return true;
        }
        return false;
    }


    /**
     * 获取主数据
     */
    (function () {

        var string ="{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/getAccountProductCustomList?ruleType=615_score";
        getData(string);

    })();



    //获取数据并展示
    function getData(url){

        layui.use(['table'],function () {
            var table = layui.table;
            table.render({
                elem: '#tableform'
                ,height: 420
                ,url: url  //数据接口
                ,page: true //开启分页
                ,toolbar: 'default' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
                ,cols: [[ //表头
                    {field: 'account_name', title: '账号名', fixed: 'left'}
                    ,{field: 'apikey', title: '账号apikey'}
                    ,{field: 'product_id', title: 'Pid'}
                    ,{field: 'data', title: '规则'}
                    ,{field: 'update_at', title: '更新时间'}
                ]]
                ,limit: 20
                ,limits:[20]
                ,defaultToolbar: false
                ,autoSort: false
            });

            AutoTableHeight();

        });

    }

    function AutoTableHeight()
    {
        var dev_obj = document.getElementById('table_and_page_div_id'); //table的父div

        var layuitable_main = dev_obj.getElementsByClassName("layui-table-main"); //在父div中找 layui-table-main 属性所在标签
        if (layuitable_main != null && layuitable_main.length > 0) {
            layuitable_main[0].style.height = '100%';
        }

        var layuitable = dev_obj.getElementsByClassName("layui-form"); //在父div中找 layui-form 属性所在标签
        if (layuitable != null && layuitable.length > 0) {
            layuitable[0].style.height = '100%';
        }
    }





</script>
</body>
</html>
