<!DOCTYPE html>
<html lang="en">
    <head>
        <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__/vue/vue.js"></script>
        <script type="application/javascript" src="__JS__/vue/index.js"></script>
        <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <style>
        .el-dialog__body .el-form .el-input__inner{
            width: 256px;
        }
    </style>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body">

                    <el-form :inline="true" :model="searchForm"  label-width="100px" class="demo-form-inline">

                        <el-form-item label="账号或apikey" label-width="108px">
                            <el-input v-model="searchForm.apikey" placeholder="账号或apikey"></el-input>
                        </el-form-item>

                        <el-form-item label="渠道名称">
                            <el-select v-model="searchForm.channel_id" filterable clearable placeholder="请选择">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" @click="searchTableData()">查询</el-button>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="success"   @click="addTableData()">添加数据</el-button>
                        </el-form-item>
                    </el-form>

                </div>
            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%">
                        <el-table-column
                                prop="father_id"
                                label="父产品ID"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="account_name"
                                label="账号名称"
                               >
                        </el-table-column>
                        <el-table-column
                                prop="channel_name"
                                label="渠道名称"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="channel_id"
                                label="渠道ID"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="data"
                                label="补数配置"
                                width="280">
                        </el-table-column>
                        <el-table-column
                                prop="create_at"
                                label="创建时间"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="update_at"
                                label="修改时间"
                                >
                        </el-table-column>
                        <el-table-column label="是否补数" >
                            <template slot-scope="scope">
                                <el-switch v-model="scope.row.complement" :value="scope.row.complement" :active-value="1" :inactive-value="0" @change="handleChangeStatus(scope.row.id, scope.row.complement)"></el-switch>
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="200">
                            <template slot-scope="scope">
                                <el-button
                                        size="mini"
                                        @click="handleEdit(scope.$index, scope.row.id)">编辑</el-button>

                                <el-button
                                        size="mini"
                                        type="danger"
                                        @click="handleDelete(scope.$index, scope.row.id)">删除</el-button>
                            </template>
                        </el-table-column>

                    </el-table>
                </template>

                <el-dialog :title="dialog_title" :visible.sync="dialogFormVisible" @close="closeDialogCallBack('form')" v-if="dialogFormVisible">
                    <el-form :model="form" :rules="rules" ref="form">

                        <el-form-item label="父产品ID" prop="father_id" :label-width="formLabelWidth">
                            <el-input v-model.number="form.father_id" :readonly="operate" autocomplete="off"></el-input>
                        </el-form-item>

                        <el-form-item label="账号名称" prop="apikey" :label-width="formLabelWidth">
                            <el-select v-model="form.apikey" :disabled="operate"  filterable clearable placeholder="请选择账号">
                                <el-option
                                        v-for="item in accountList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="渠道名称" prop="channel_id" :label-width="formLabelWidth">
                            <el-select v-model="form.channel_id" :disabled="operate" multiple filterable clearable  placeholder="请选择渠道">
                                <el-option
                                        v-for="item in options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="超时时间" prop="timeout" :label-width="formLabelWidth">
                            <el-input v-model.number="form.timeout" autocomplete="off" ></el-input>
                        </el-form-item>

                        <el-form-item label="是否补数" prop="complement" :label-width="formLabelWidth">
                            <el-input v-model.number="form.complement" autocomplete="off"></el-input>
                        </el-form-item>

                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="dialogFormVisible = false">取 消</el-button>
                        <el-button type="primary" @click="onSubmit('form')">确 定</el-button>
                    </div>
                </el-dialog>

            </div>

            <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40, 50]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalNum">
                </el-pagination>
            </div>

        </div>

    </div>

    <script type="application/javascript">
        var user_cookie = getCookie('PHPSESSID');
        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getComplementList";

        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                totalNum: 0,
                pageSize: 10,
                currentPage:1,
                options: [],
                accountList:[],
                pickerOptions: {},
                getUrl: url,
                dialogFormVisible: false,
                dialog_title:'添加数据',
                operate:false,
                form: {
                    id:'',
                    channel_id: [],
                    apikey: '',
                    father_id: '',
                    timeout: '',
                    complement: ''
                },
                formLabelWidth: '120px',
                searchForm: {
                    channel_id: '',
                    apikey:''
                },
                rules:{
                    channel_id: [
                        { required: true, message: '请选择渠道', trigger: 'change' }
                    ],
                    apikey: [
                        { required: true, message: '请选择账号', trigger: 'change' }
                    ],
                    father_id: [
                        { required: true, message: '父产品ID不能为空' },
                        { type: 'number', message: '父产品ID必须为数字值'}
                    ],
                    timeout: [
                        { required: true, message: '超时时间不能为空' },
                        { type: 'number', message: '超时时间必须为数字值'}
                    ],
                    complement: [
                        { required: true, message: '是否补数不能为空' },
                        { type: 'number', message: '是否补数必须为数字值'},
                        { pattern: /(^[0-1]$)/, message: '只能输入0或1' }
                    ]
                }
            },
            created: function(){
                this.getTableData();
                this.getChannelSelectData();
                this.getAccountSelectData();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    var channel_id = this.searchForm.channel_id;
                    var apikey = this.searchForm.apikey;
                    var where = {limit:this.pageSize, page:this.currentPage};
                    if(channel_id){
                        where.channel_id = channel_id;
                    }
                    if(apikey){
                        where.apikey = apikey;
                    }
                    axios.post(url, where).then(function (response) {
                        //console.log(response);
                        self.tableData = response.data.data.list;
                        self.totalNum = response.data.data.count;
                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                getChannelSelectData:function(){
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/channel";
                    axios.get(url, {
                        params:{bxf:true}
                    }).then(function (response) {
                        //console.log(response.data.data);
                        self.options.push({value:'default',label:'default'});
                        response.data.data.forEach((item, index) => {
                            //console.log(item.channel_id + item.label);
                            self.options.push({value:item.channel_id,label:item.label});
                        });

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                getAccountSelectData:function(){
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/account";
                    axios.get(url, {
                        params:{options:false}
                    }).then(function (response) {

                        Object.getOwnPropertyNames(response.data.data).forEach(function(key){
                            var name = response.data.data[key];
                            self.accountList.push({value:key,label:name});
                        });
                        self.accountList.push({value:'default',label:'default'});

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                onSubmit:function(formName){

                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            var request_params = this.form;
                            var self = this;
                            var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/saveChannelComplement";

                            axios.post(url, request_params).then(function (response) {
                                if(response.data.code == 0){
                                    self.$refs['form'].resetFields();
                                    successMsg(response.data.msg);

                                }else{
                                    errorMsg(response.data.msg);
                                }
                            }).catch(function (error) {
                                console.log(error);
                                errorMsg(error);
                            });

                        } else {
                            console.log('error submit!!');
                            return false;
                        }
                    });

                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.getTableData();
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                    this.currentPage = val;
                    this.getTableData();
                },
                searchTableData:function (){
                    this.currentPage = 1;
                    this.getTableData();
                },
                addTableData:function(){
                    this.dialog_title = '添加数据';
                    this.operate = false;
                    this.form.id = '';
                    this.form.channel_id = [];
                    this.form.apikey = '';
                    this.form.father_id = '';
                    this.form.timeout = '';
                    this.form.complement = '';

                    //this.$refs['form'].resetFields();
                    this.dialogFormVisible = true;

                },
                closeDialogCallBack:function(formName){
                    this.$refs[formName].resetFields();
                },
                handleEdit(index, id) {
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelComplement";
                    axios.get(url, {
                        params:{id:id}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.form.id = response.data.data.id;
                            self.form.channel_id = response.data.data.channel_id;
                            self.form.apikey = response.data.data.apikey;
                            self.form.timeout = response.data.data.timeout;
                            self.form.complement = response.data.data.complement;
                            self.form.father_id = response.data.data.father_id;

                            self.dialog_title = '编辑数据';
                            self.operate = true;
                            self.dialogFormVisible = true;

                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                handleDelete(index, id) {
                    var self = this;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/delChannelComplement";
                    axios.get(url, {
                        params:{id:id,user_cookie:user_cookie}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });
                },
                handleChangeStatus(id, complement) {
                    var self = this;

                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/setChannelComplement";
                    axios.get(url, {
                        params:{id:id,complement:complement}
                    }).then(function (response) {

                        if(response.data.code == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.data.msg);
                        }

                    }).catch(function (error) {
                        console.log(error);
                    });
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            vm.$refs['form'].resetFields();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }

        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return (arr[2]);
            else
                return null;
        }

    </script>
    </body>
</html>
