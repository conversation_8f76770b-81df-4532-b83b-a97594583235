<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>渠道管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body style="overflow-x: hidden;height:auto;">
<div class="container" id="add" style="margin-top:20px;height: auto;">
    <form class="layui-form layui-row" method="post">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="channel_id">渠道ID</label>
            <div class="layui-input-block">
                <input type="text" maxlength="5" name="channel_id" id="channel_id" required lay-verify="required|number" placeholder="请输入渠道ID" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="channel_name">渠道名称</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="channel_name" id="channel_name" required lay-verify="required" placeholder="请输入渠道名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">渠道标识</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="name" id="name" required lay-verify="required" placeholder="请输入渠道标识" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
            <label class="layui-form-label" for="name">余额</label>
            <div class="layui-input-inline">
                <input type="text" maxlength="32" name="balance" id="balance"   placeholder="请输入余额" autocomplete="off" class="layui-input">
            </div>

            <label style="width: auto" class="layui-form-label" for="name">余额开始计算时间</label>
            <div class="layui-input-inline" >
                <input  style="width: 170%" type="date" name="balance_start_date" id="balance_start_date" class="form-control"/>
            </div>
        </div>
<!--        <div class="layui-form-item layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">-->

<!--        </div>-->
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">
            <label class="layui-form-label" for="name">剩余调用量</label>
            <div class="layui-input-inline">
                <input type="text" maxlength="32" name="rest_num" id="rest_num"   placeholder="请输入剩余调用量" autocomplete="off" class="layui-input">
            </div>
            <label style="width: auto" class="layui-form-label" for="name">剩余调用计算时间</label>
            <div class="layui-input-inline">
                <input  style="width: 170%" type="date" name="rest_num_start_date" id="rest_num_start_date" class="form-control"/>
            </div>
        </div>
<!--        <div class="layui-form-item layui-col-xs6 layui-col-sm6 layui-col-md8 layui-col-lg8">-->

<!--        </div>-->

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">每天最大调用次数</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="max_call_num" id="max_call_num"  placeholder="请输入每天最大调用次数" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">日均调用次数</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="daily_call_num" id="daily_call_num"  placeholder="请输入日均调用次数" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">合同到期时间</label>
            <div class="layui-input-block">
                <input type="date" name="contract_end_date" id="contract_end_date" class="form-control"/>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">
                    增加渠道
                </button>
            </div>
        </div>
    </form>
</div>

</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    //表单提交事件
    layui.form.on('submit(submit)', function (data) {
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/addChannel", data.field).then(function (response) {
            Popup.success("增加成功", function () {
                Table.refreshTopTable();
                Popup.closeTopIframe();
            });

        });
        return;
    });
</script>