<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>接口管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
            <label class="layui-form-label" for="channel_id">选择渠道</label>
            <div class="layui-input-block">
                <select name="channel_id" lay-search id="channel_id">
                    <option value="0">全部</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label" for="status">状态</label>
            <div class="layui-input-block">
                <select name="status" id="status">
                    <option value="">--全部--</option>
                    <option value="1">可用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <button type="button" class="layui-btn" onclick="Popup.iframe('./add_interface.html?channel_id='+Common.getRequestParam('channel_id'), '增加接口')">
                增加接口
            </button>
        </div>
    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    window.Popup = Popup;
    window.Table = Table;
    //渲染产品下拉选项框
    (function () {
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelOptions", {
            is_with_id : true,
            "default"  : Common.getRequestParam('channel_id')
        }).then(function (data) {
            $("#channel_id").html(`<option value="0">--全部--</option>${data.data}`);
            layui.form.render('select');
        });
    })();

    window.cache = {
        list_url         : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getInterfaceList",
        list_order_field : "sort",
        list_order_type  : "desc",
        list_where       : {
            channel_id : Common.getRequestParam('channel_id')
        },
        list_form        : true,
        list_fields      : [[{
            field : 'id',
            title : '接口ID',
            align : 'center',
        }, {
            field : 'channel_name',
            title : '所属渠道',
            align : 'center',
        }, {
            field : 'label',
            title : "接口名称",
            align : "center"
        }, {
            field : 'name',
            title : "接口标识",
            align : "center",
        }, {
            field   : 'status',
            title   : "状态",
            align   : "center",
            templet : function (data) {
                if (0 === Number(data.status)) {
                    return `<span class="color_red">禁用</span>`;
                } else {
                    return `<span class="color_green">可用</span>`;
                }
            }
        }, {
            field   : 'operation',
            title   : "操作",
            align   : "left",
            width   : 400,
            templet : function (data) {
                return `<button class="layui-btn layui-btn-xs" onclick="Popup.iframe('./edit_interface.html?interface_id=${data.id}', '编辑')">
编辑
</button>
<a class="layui-btn layui-btn-danger layui-btn-xs" href="./price_index.html?channel_id=${data.channel_id}&interface_id=${data.id}" target="_blank">
计费配置
</a>`;
            }
        }]]
    };

    //加载表格
    Table.reloadTable({
        where     : window.cache.list_where,
        method    : 'get',
        page      : false,
        limit     : 10000,
        parseData : function (response) {
            return {
                "code" : response.status,
                "msg"  : response.msg,
                "data" : response.data
            };
        }
    });

</script>