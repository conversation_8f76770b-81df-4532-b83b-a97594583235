<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>添加产品渠道-渠道管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        * {
            box-sizing : inherit;
        }
    </style>
</head>
<body style="overflow-x: hidden;height:auto;">
<div class="container" id="add" style="margin-top:20px;height: auto;">
    <form class="layui-form layui-row" method="post">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="label">父产品id</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="product_id" value="" id="product_id" required lay-verify="required" placeholder="请输入父产品id" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="label">回朔日期</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="back_date" value="{:date('Ymd')}" id="back_date"  placeholder="请输入回朔日期 格式如:20210101" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="label">渠道账期</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="period_date" value="{:date('Ymd')}" id="period_date"  placeholder="请输入账期日期 格式如:20210101" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="label">缓存日期</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="cache_date" value="" id="cache_date"  placeholder="请输入缓存日期 格式如:20210101" autocomplete="off" class="layui-input">
                <span class="" >注:全国移动</span>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">支持产品</label>
            <div class="layui-input-block">
                <input type="text"  name="product_ids" id="product_ids" placeholder="请输入支持产品id,以英文逗号分割" autocomplete="off" class="layui-input">
                <span class="product_ids_count" style="display: none;"></span>
            </div>

        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">检测产品</label>
            <div class="layui-input-block">
                <input type="text" name="check_product_ids" id="check_product_ids"  placeholder="请输入检测产品id" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="check_tel">检测电话</label>
            <div class="layui-input-block">
                <input type="text"  name="check_tel" id="check_tel" required lay-verify="required" placeholder="请输入检测电话" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="status">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="0" title="停用">
                <input type="radio" name="status" value="1" title="启用">
                <input type="radio" name="status" value="2" title="暂停">
            </div>
        </div>

        <input type="hidden" name="channel_id" value="" id="channel_id"/>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">
                    保存数据
                </button>
            </div>
        </div>
    </form>
</div>

</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    /*
    layui.laydate.render({
        elem  : '#back_date',
        type  : 'date',
    });
     */

    $("#channel_id").val(Common.getRequestParam('channel_id'));

    //表单提交事件
    layui.form.on('submit(submit)', function (data) {
        data        = data.field;
        //data.status = data.status ? 1 : 0;
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/saveProductChannel", data).then(function (response) {
            if(response.code == 0){
                Popup.success("添加成功", function () {
                    Table.refreshTopTable();
                    Popup.closeTopIframe();
                });
            }else{
                Popup.error(response.message, function () {
                    Table.refreshTopTable();
                    Popup.closeTopIframe();
                });
            }


        });
        return;
    });
</script>