<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>渠道管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
            <label class="layui-form-label" for="channel_id">选择渠道</label>
            <div class="layui-input-block">
                <select name="channel_id" lay-search id="channel_id">
                    <option value="0">全部</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md3 layui-col-lg2">
            <label class="layui-form-label" for="status">状态</label>
            <div class="layui-input-block">
                <select name="status" id="status">
                    <option value="">--全部--</option>
                    <option value="1">可用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <button type="button" class="layui-btn" onclick="Popup.iframe('./add.html', '增加渠道')">
                增加渠道
            </button>
        </div>
    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="list_table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.2"></script>
<script type="application/javascript">
    window.Popup = Popup;
    window.Table = Table;
    //渲染产品下拉选项框
    (function () {
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelOptions", {
            is_with_id : true
        }).then(function (data) {
            $("#channel_id").html(`<option value="0">--全部--</option>${data.data}`);
            layui.form.render('select');
        });
    })();

    window.cache = {
        list_url         : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelList",
        list_order_field : "contract_end_date",
        list_order_type  : "desc",
        list_where       : {
            channel_id : 0
        },
        list_form        : true,
        list_fields      : [[{
            field : 'channel_id',
            title : '渠道ID',
            align : 'center',
        }, {
            field : 'label',
            title : "渠道名称",
            align : "center"
        }, {
            field : 'name',
            title : "渠道标识",
            align : "center",
        }, {
            field   : 'status',
            title   : "状态",
            align   : "center",
            templet : function (data) {
                if (0 === Number(data.status)) {
                    return `<span class="color_red">禁用</span>`;
                } else {
                    return `<span class="color_green">可用</span>`;
                }
            }
        }, {
            field : 'interface_count',
            title : "接口数量",
            align : "center",
        }, {
            field : 'contract_end_date',
            title : "合同到期时间",
            align : "center",
            templet:function(data){
                let timestamp = Date.parse(new Date())/1000
                if(data.contract_end_times<0){
                    // 到期时间默认值1970-01-01 正常显示
                    return data.contract_end_date
                }else if(data.contract_end_times<timestamp){
                    // 已经到期，标红显示
                    return `<span style="color:red">${data.contract_end_date}</span>`
                }
                else if(data.contract_end_times<timestamp+30*24*3600){
                    // 到期时间小于30天，标黄显示
                    return `<span style="color:#dfae3a">${data.contract_end_date}</span>`
                }
                else{
                    // 未到期，绿色显示
                    return `<span style="color:green">${data.contract_end_date}</span>`
                }
            },
            sort:true
        }, {
            field   : 'operation',
            title   : "操作",
            align   : "left",
            width   : 400,
            templet : function (data) {
                return `<button class="layui-btn layui-btn-xs" onclick="Popup.iframe('./edit.html?channel_id=${data.channel_id}', '编辑')">
编辑
</button>
<a class="layui-btn layui-btn-xs" href="./product_channel.html?channel_id=${data.channel_id}" target="_blank">
渠道配置
</a>
<a class="layui-btn layui-btn-normal layui-btn-xs" href="./interface_index.html?channel_id=${data.channel_id}" target="_blank">
查看接口
</a>
<button class="layui-btn layui-btn-xs" onclick="Popup.iframe('./add_interface.html?channel_id=${data.channel_id}', '增加接口')">
增加接口
</button>
<a class="layui-btn layui-btn-danger layui-btn-xs" href="./price_index.html?channel_id=${data.channel_id}" target="_blank">
计费配置
</a>`;
            }
        }]]
    };

    //加载表格
    Table.reloadTable({
        where     : window.cache.list_where,
        method    : 'get',
        page      : false,
        limit     : 10000,
        parseData : function (response) {
            return {
                "code" : response.status,
                "msg"  : response.msg,
                "data" : response.data
            };
        }
    });

</script>