<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>渠道管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body style="overflow-x: hidden;height:auto;">
<div class="container" id="add" style="margin-top:20px;height: auto;">
    <form class="layui-form layui-row" method="post">

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="channel_id">选择渠道</label>
            <div class="layui-input-block">
                <select name="channel_id" id="channel_id" lay-search></select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="label">接口名称</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="label" id="label" required lay-verify="required" placeholder="请输入接口名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">接口标识</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="name" id="name" required lay-verify="required" placeholder="请输入接口标识" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">
                    增加接口
                </button>
            </div>
        </div>
    </form>
</div>

</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    //渲染产品下拉选项框
    (function () {
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelOptions", {
            is_with_id : true,
            "default"  : Common.getRequestParam('channel_id')
        }).then(function (data) {
            $("#channel_id").html(`${data.data}`);
            layui.form.render('select');
        });

        //判断是否确定了渠道
        if (Common.getRequestParam('channel_id')) {
            $("#channel_id").prop('disabled', true);
        }
    })();
    //表单提交事件
    layui.form.on('submit(submit)', function (data) {
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/addInterface", data.field).then(function (response) {
            Popup.success("增加成功", function () {
                Table.refreshTopTable();
                Popup.closeTopIframe();
            });

        });
        return;
    });
</script>