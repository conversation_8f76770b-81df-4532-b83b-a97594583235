<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>渠道管理(邦信分)</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item">
            <label class="layui-form-label"></label>
            <div class="layui-input-block day_type_div">
                <input type="radio" lay-filter="product_id" name="product_id" value="210" title="邦信分-通信字段" checked="">
                <input type="radio" lay-filter="product_id" name="product_id" value="1000" title="邦信分-通信评分">
            </div>

        </div>

    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
<script type="application/javascript">
    window.Popup = Popup;
    window.Table = Table;

    window.cache = {
        list_url         : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelProductList",
        list_order_field : "sort",
        list_order_type  : "desc",
        list_where       : {
            product_id : 210
        },
        list_form        : true,
        list_fields      : [[{
            field : 'channel_id',
            title : '渠道ID',
            align : 'center',
            width   : 200,
        }, {
            field : 'label',
            title : "渠道名称",
            align : "center"
        }, {
            field : 'name',
            title : "渠道标识",
            align : "center",
        }, {
            field   : 'status',
            title   : "状态",
            align   : "center",
            templet : function (data) {
                if (1 === Number(data.status)) {
                    return `<span class="color_green">已上线</span>`;
                } else {
                    return `<span class="color_red">已下线</span>`;
                }
            }
        }, {
            field   : 'operation',
            title   : "操作",
            align   : "left",
            width   : 200,
            templet : function (data) {
                if (1 === Number(data.status)) {
                    return `<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="editChannelStatus(2, ${data.id})">下线</button>`;
                } else {
                    return `<button class="layui-btn layui-btn-xs" onclick="editChannelStatus(1, ${data.id})">上线</button>`;
                }
            }
        }]]
    };

    reloadTable();

    layui.use(['form'], function () {
        let form = layui.form;
        form.on('radio(product_id)', function(){
            window.cache.list_where.product_id = $('input:radio[name=product_id]:checked').val();
            reloadTable();
        });
    });

    function reloadTable()
    {
        //加载表格
        Table.reloadTable({
            where     : window.cache.list_where,
            method    : 'get',
            page      : false,
            limit     : 100,
            parseData : function (response) {
                return {
                    "code" : response.status,
                    "msg"  : response.msg,
                    "data" : response.data
                };
            }
        });
    }

    function editChannelStatus(status, id)
    {
        let wenan = status === 2 ? "确定下线吗？" : "确定上线吗？";

        layer.confirm(wenan, function(index){
            layer.close(index);
            $.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/editChannelStatus", {
                id:id,
                status:status,
                user_cookie: $.cookie('PHPSESSID'),
            }, function (res) {
                if (res.code == 0) {
                    alert("更新成功");
                    reloadTable();
                } else {
                    alert("更新失败");
                }
            })
        });
    }

</script>