<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>编辑渠道-渠道管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        * {
            box-sizing : inherit;
        }
    </style>
</head>
<body style="overflow-x: hidden;height:auto;">
<div class="container" id="add" style="margin-top:20px;height: auto;">
    <form class="layui-form layui-row" method="post">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="back_date">回朔日期</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="back_date" value="{:date('Ymd')}" id="back_date"  placeholder="请输入回朔日期 格式如:20210101" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="period_date">渠道账期</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="period_date" value="{:date('Ymd')}" id="period_date"  placeholder="请输入账期日期 格式如:20210101" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="cache_date">缓存日期</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="cache_date" value="" id="cache_date"  placeholder="请输入缓存日期 格式如:20210101" autocomplete="off" class="layui-input">
                <span class="" >注:全国移动</span>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="product_ids">支持产品</label>
            <div class="layui-input-block">
                <input type="text"  name="product_ids" id="product_ids"  placeholder="请输入支持产品id,以英文逗号分割" autocomplete="off" class="layui-input">
                <span class="product_ids_count" style="display: none;"></span>
            </div>

        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="beijing_proxy">北京代理</label>
            <div class="layui-input-block">
                <input type="text"  name="beijing_proxy" id="beijing_proxy"  placeholder="请输入北京代理" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="shenzhen_proxy">深圳代理</label>
            <div class="layui-input-block">
                <input type="text"  name="shenzhen_proxy" id="shenzhen_proxy"  placeholder="请输入深圳代理" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="check_product_ids">检测产品</label>
            <div class="layui-input-block">
                <input type="text" name="check_product_ids" id="check_product_ids"  placeholder="请输入检测产品id" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="check_tel">检测电话</label>
            <div class="layui-input-block">
                <input type="text"  name="check_tel" id="check_tel" required lay-verify="required" placeholder="请输入检测电话" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="status">状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="0" title="停用">
                <input type="radio" name="status" value="1" title="启用">
                <input type="radio" name="status" value="2" title="暂停">
            </div>
        </div>

        <input type="hidden" name="id" value="" id="id"/>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">
                    保存数据
                </button>
            </div>
        </div>
    </form>
</div>

</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    (function () {
        Request.get(`{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getProductChannelInfo?id=${Common.getRequestParam('id')}`).then(function (response) {
            $("#id").val(response.data.id);
            $("#back_date").val(response.data.back_date);
            $("#cache_date").val(response.data.cache_date);
            $("#period_date").val(response.data.period_date);
            $("#product_ids").val(response.data.product_ids);
            $("#beijing_proxy").val(response.data.beijing_proxy);
            $("#shenzhen_proxy").val(response.data.shenzhen_proxy);
            $("#check_product_ids").val(response.data.check_product_ids);
            $("#check_tel").val(response.data.check_tel);
            if(response.data.product_ids_count > 0){
                $('.product_ids_count').text('共' + response.data.product_ids_count + '个');
                $('.product_ids_count').css('display', 'block');
            }
            let status = Number(response.data.status);
            $('[name=status]').each(function(i, item){
                if($(item).val() == status){
                    $(item).prop('checked', true);
                    layui.use('form', function(){
                        var form = layui.form;
                        form.render();
                    });
                }

            });

        });
    })();

    /*
    layui.laydate.render({
        elem  : '#back_date',
        type  : 'date',
    });
     */


    //表单提交事件
    layui.form.on('submit(submit)', function (data) {
        data        = data.field;
        //data.status = data.status ? 1 : 0;
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/saveProductChannel", data).then(function (response) {
            if(response.code == 0){
                Popup.success("修改成功", function () {
                    Table.refreshTopTable();
                    Popup.closeTopIframe();
                });
            }else{
                Popup.error(response.message, function () {
                    Table.refreshTopTable();
                    Popup.closeTopIframe();
                });
            }


        });
        return;
    });
</script>