<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>接口计费配置</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        .layui-table-cell {
            height : auto !important;
        }
    </style>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
            <label class="layui-form-label" for="channel_id">选择渠道</label>
            <div class="layui-input-block">
                <select name="channel_id" lay-search id="channel_id" lay-filter="channel_id">
                    <option value="0">全部</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
            <label class="layui-form-label" for="interface_id">选择接口</label>
            <div class="layui-input-block">
                <select name="interface_id" lay-search id="interface_id" lay-filter="interface_id">
                    <option value="0">全部</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <button type="button" class="layui-btn" onclick="Popup.iframe('/Home/PriceInterface/index.html', '增加计费配置')">
                增加计费配置
            </button>
        </div>
    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    window.Popup = Popup;
    window.Table = Table;
    window.cache = {
        channel_id       : Common.getRequestParam('channel_id'),
        list_url         : "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getPriceList",
        list_order_field : "sort",
        list_order_type  : "desc",
        list_where       : {
            channel_id   : Common.getRequestParam('channel_id'),
            interface_id : Common.getRequestParam('interface_id')
        },
        list_form        : true,
        list_fields      : [[
            {
                field : 'channel_name',
                title : '渠道',
                align : 'center',
            }, {
                field : 'interface_name',
                title : '接口',
                align : 'center',
            }, {
                field : 'start_date',
                title : "计费开始时间",
                align : "center",
            }, {
                field : 'price',
                title : "价格",
                align : "left",
            }, {
                field   : 'operation',
                title   : "操作",
                align   : "left",
                templet : function (data) {
                    return `
<button class="layui-btn layui-btn-danger layui-btn-xs" onclick="deletePrice(${data.id}, ${data.start_date})">
删除
</button>`;
                }
            }]]
    };

    //加载接口下拉选项
    function reloadInterfaceOptions() {
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getInterfaceOptions", {
            is_with_id : true,
            channel_id : window.cache.channel_id,
            "default"  : Common.getRequestParam('interface_id')
        }).then(function (data) {
            $("#interface_id").html(`<option value="0">--全部--</option>${data.data}`);
            layui.form.render('select');
        });
    }

    //加载渠道下拉选项
    function reloadChannelOptions() {
        Request.get("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getChannelOptions", {
            is_with_id : true,
            "default"  : Common.getRequestParam('channel_id')
        }).then(function (data) {
            $("#channel_id").html(`<option value="0">--全部--</option>${data.data}`);
            layui.form.render('select');
        });
    }

    //加载表格数据
    function reloadTable() {
        Table.reloadTable({
            where     : window.cache.list_where,
            method    : 'get',
            page      : false,
            limit     : 10000,
            parseData : function (response) {
                return {
                    "code" : response.status,
                    "msg"  : response.msg,
                    "data" : response.data
                };
            }
        });
    }

    //删除计费配置
    function deletePrice(id, start_date) {
        Popup.confirm("您正在删除一条正在使用的计费配置，是否继续？", function () {
            Popup.iframe(`./delete_price.html?id=${id}`, "删除数据");
        });
    }

    //渲染产品下拉选项框
    (function () {
        reloadChannelOptions();

        reloadInterfaceOptions();

        //切换
        layui.form.on('select(channel_id)', function (data) {
            window.cache.channel_id              = data.value;
            window.cache.list_where.channel_id   = data.value;
            window.cache.list_where.interface_id = 0;
            reloadInterfaceOptions();
            reloadTable();
        });

        layui.form.on('select(interface_id)', function (data) {
            window.cache.list_where.interface_id = data.value;
            reloadTable();
        });

        //加载表格数据
        reloadTable();
    })();
</script>