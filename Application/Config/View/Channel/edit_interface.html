<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>编辑接口-接口管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
    <style>
        * {
            box-sizing : inherit;
        }
    </style>
</head>
<body style="overflow-x: hidden;height:auto;">
<div class="container" id="add" style="margin-top:20px;height: auto;">
    <form class="layui-form layui-row" method="post">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="label">接口名称</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="label" id="label" required lay-verify="required" placeholder="请输入接口名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="name">接口标识</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="name" id="name" required lay-verify="required" placeholder="请输入接口标识" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="status">状态</label>
            <div class="layui-input-block">
                <input type="checkbox" id="status" name="status" lay-skin="switch" lay-text="可用|禁用" value="1">
            </div>
        </div>

        <input type="hidden" name="id" value="" id="id"/>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <label class="layui-form-label"></label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">
                    保存数据
                </button>
            </div>
        </div>
    </form>
</div>

</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    (function () {
        Request.get(`{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getInterfaceInfo?interface_id=${Common.getRequestParam('interface_id')}`).then(function (response) {
            $("#id").val(response.data.id);
            $("#label").val(response.data.label);
            $("#name").val(response.data.name);
            if (0 !== Number(response.data.status)) {
                $("#status").prop('checked', true);
                layui.form.render('checkbox');
            }
        });
    })();
    //表单提交事件
    layui.form.on('submit(submit)', function (data) {
        data        = data.field;
        data.status = data.status ? 1 : 0;
        Request.post("{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/saveInterface", data).then(function (response) {
            Popup.success("修改成功", function () {
                Table.refreshTopTable();
                Popup.closeTopIframe();
            });

        });
        return;
    });
</script>