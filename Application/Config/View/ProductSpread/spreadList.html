<html>
<include file="Common@Public/head"/>
<!--<script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>-->
<!--<script src="https://unpkg.com/element-ui@2.15.6/lib/index.js"></script>-->
<!--<script src="https://unpkg.com/axios/dist/axios.min.js"> </script>-->
<!--<link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.6/lib/theme-chalk/index.css">-->
<script src="__JS__/vue/vue.js"> </script>
<script src="__JS__/vue/index.js"> </script>
<script src="__JS__/vue/axios.min.js"> </script>
<link rel="stylesheet" href="__JS__/vue/index.css">

<style>
    ::-webkit-scrollbar {
        width: 1px;
        height: 1px;
    }
    /* // 滚动条的滑块 */
    ::-webkit-scrollbar-thumb {
        background-color: #a1a3a9;
        border-radius: 0px;
    }

    element.style {
    }
    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
        font-size: 10px;
    }

</style>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="app">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="选择账号">
            <el-select v-model="formInline.account" clearable filterable placeholder="选择产品">
                <el-option v-for="x in accountOption" :key="x.value" :label="x.label" :value="x.value">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="选择产品">
            <el-select v-model="formInline.product" clearable filterable placeholder="选择产品">
                <el-option v-for="x in productOption" :key="x.value" :label="x.label + '(' + x.value + ')'" :value="x.value">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="选择渠道">
            <el-select v-model="formInline.channel" multiple  placeholder="选择渠道">
                <el-option v-for="x in channelOption" :key="x.value" :label="x.label" :value="x.value">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button type="primary" @click="dislogVisibel=true">新增</el-button>
        </el-form-item>
    </el-form>
    <!--
    <template>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="产品" name="first"></el-tab-pane>
            <el-tab-pane label="技术" name="second"></el-tab-pane>
        </el-tabs>
    </template>
    -->
    <el-table :data="tableData" height="500"  border style="width: 100%">
        <el-table-column prop="account_name" label="客户名称" width="180"></el-table-column>
        <el-table-column prop="product_name" label="产品" width="180"></el-table-column>
        <el-table-column prop="channel_name" label="渠道"></el-table-column>
        <el-table-column prop="value" label="值"></el-table-column>
        <el-table-column prop="th_one_max" label="严重报警级别Max/%" width="180">
            <template slot-scope="{row,$index}">
                <div @click="{{ChangeNum(row,$index)}}">
                    <el-input v-if="editableB[$index]"
                              @blur="updateProductConfig(row,$index)"
                              v-model='row.th_one_max'>
                    </el-input>
                    <span v-else>{{row.th_one_max}}</span>
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="th_one_min" label="严重报警级别Min/%" width="180">
            <template slot-scope="{row,$index}">
                <div @click="{{ChangeNum(row,$index)}}">
                    <el-input v-if="editableB[$index]"
                              @blur="updateProductConfig(row,$index)"
                              v-model='row.th_one_min'>
                    </el-input>
                    <span v-else>{{row.th_one_min}}</span>
                </div>
            </template>
            </template>
        </el-table-column>
        <el-table-column prop="th_two_max" label="关注报警级别Max/%" width="180">
            <template slot-scope="{row,$index}">
                <div @click="{{ChangeNum(row,$index)}}">
                    <el-input v-if="editableB[$index]"
                              @blur="updateProductConfig(row,$index)"
                              v-model='row.th_two_max'>
                    </el-input>
                    <span v-else>{{row.th_two_max}}</span>
                </div>
            </template>
        </el-table-column>
        <el-table-column prop="th_two_min" label="关注报警级别Min/%" width="180">
            <template slot-scope="{row,$index}">
                <div @click="{{ChangeNum(row,$index)}}">
                    <el-input v-if="editableB[$index]"
                              @blur="updateProductConfig(row,$index)"
                              v-model='row.th_two_min'>
                    </el-input>
                    <span v-else>{{row.th_two_min}}</span>
                </div>
            </template>
        </el-table-column>
    </el-table>
    <el-dialog
     title="添加数据"
     :visible.sync="dislogVisibel">
        <span>
<!--             <el-form :inline="true" :model="addForm" class="demo-form-inline">-->
             <el-form :label-position="labelPosition" label-width="120px">
                    <el-form-item label="选择账号" prop="account">
                        <el-select  v-model="account" clearable filterable placeholder="选择产品">
                            <el-option  v-for="x in accountOption" :key="x.value" :label="x.label" :value="x.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="选择产品">
                        <el-select v-model="product" clearable filterable placeholder="选择产品">
                            <el-option v-for="x in productOption" :key="x.value" :label="x.label" :value="x.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="选择渠道">
                        <el-select v-model="channel" clearable filterable  placeholder="选择渠道">
                            <el-option v-for="x in channelOption" :key="x.value" :label="x.label" :value="x.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="值类型">
                          <el-tooltip class="item" effect="dark" content="* 若无选项，请先添加产品对应的值类型后再操作" placement="right">
                            <el-select  v-model="value" clearable filterable  placeholder="请选择">
                                <el-option  v-for="x in valueOption" :key="x.value" :label="x.label" :value="x.value">
                                </el-option>
                            </el-select>
                          </el-tooltip>
                    </el-form-item>
                     <el-form-item label="一级报警Max">
                            <el-input style="width: 30%" v-model="one_max" placeholder="请输入内容"></el-input>%
                     </el-form-item>
                     <el-form-item label="一级报警Min">
                            <el-input style="width: 30%" v-model="one_min" placeholder="请输入内容"></el-input>%
                     </el-form-item>
                     <el-form-item label="二级报警Max">
                            <el-input style="width: 30%" v-model="two_max" placeholder="请输入内容"></el-input>%
                     </el-form-item>
                     <el-form-item label="二级报警Min">
                            <el-input style="width: 30%" v-model="two_min" placeholder="请输入内容"></el-input>%
                     </el-form-item>
             </el-form>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dislogVisibel=false">取消</el-button>
            <el-button type="primary" @click="addConfigProductValue">确定</el-button>
        </span>

    </el-dialog>
    <div class="block">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="1"
                :page-sizes="[20, 50, 100]"
                :page-size="20"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
        </el-pagination>
    </div>
</div>
</body>
</html>
<script>
    var Main = {
        data() {
            return {
                optiondataurl:'{$Think.config.FINANCE_MANAGE_API_DOMAIN}/config/product/spread/getOptions',
                tabledataurl:'{$Think.config.FINANCE_MANAGE_API_DOMAIN}/config/product/spread/getConfigProductValueSpread',
                getproductvalueurl:'{$Think.config.FINANCE_MANAGE_API_DOMAIN}/config/product/spread/getProductValueSpreadByPid',
                addproductvalueurl:'{$Think.config.FINANCE_MANAGE_API_DOMAIN}/config/product/spread/addConfigProductValueSpread',
                updateproductvalueurl:'{$Think.config.FINANCE_MANAGE_API_DOMAIN}/config/product/spread/updateConfigProductValueSpread',
                accountOption:[], //客户选择
                productOption:[], //产品选择
                channelOption:[], //渠道选择
                valueOption:[], // 值选择
                tableData:[], //表数据
                editableB: [], //编辑表数据
                formInline: {
                    account:'',
                    product: '',
                    channel:'',
                },
                account:'',
                product:'',
                channel:'',
                value:'',
                one_max:'',
                one_min:'',
                two_max:'',
                two_min:'',
                select:true,
                activeName:'first',
                total:0,
                SizeChange:20,  //默认每页20
                CurrentChange:1, //默认页码 0
                dislogVisibel:false,
                labelPosition: 'right',
            }
        },
        watch:{
            product(val){
                this.product = val;
                this.getProductValue();
            },
        },
        created(){
            this.getOptionData();
            this.getTableData();
        },

        methods: {
            handleClick(tab, event) {
                if (tab.index == 0){
                    this.select = true;
                }else{
                    this.select = false;
                }
                console.log(this.select);
                this.getTableData();
            },
            ChangeNum(row,$index) {
                this.editableB[$index] = true;
                this.$set(this.editableB, $index, true)
            },
            updateProductConfig(row,$index){
                row.select = this.select;
                axios({
                    url:_this.updateproductvalueurl,
                    headers:{'content-type':'applicationjson','select':_this.select},
                    method:'POST',
                    data: row,
                }).then(function (response) {
                    if(response.data.status == 0){
                        _this.$message({ message:response.data.msg,type: 'success'});
                    }else{
                        _this.$message.error(response.data.msg);
                    }
                })
                this.editableB[$index] = false;
                this.$set(this.editableB, $index, false);
            },
            onSubmit() {
                this.getTableData();
            },
            getOptionData:function () {
                _this = this;
                axios.get(this.optiondataurl).then(function (response) {
                    _this.accountOption = response.data.data.accountData;
                    _this.productOption = response.data.data.productData;
                    _this.channelOption = response.data.data.channelData;
                });

            },
            getTableData:function () {
                _this = this;
                this.gettabledataurl = this.tabledataurl + '?sizechange=' + this.SizeChange + '&currentchange=' + this.CurrentChange + '&account=' + this.formInline.account + '&product=' + this.formInline.product + '&channel=' +this.formInline.channel;
                axios.get(this.gettabledataurl,{headers:{'select':_this.select}}).then(function (response) {
                    _this.tableData = response.data.data.configproductvaluespread;
                    _this.total = response.data.data.count;

                    //重置当前页码
                    _this.CurrentChange = 1;
                })
            },
            handleSizeChange(val) {
                this.SizeChange = `${val}`;
                this.getTableData();
            },
            handleCurrentChange(val) {
                this.CurrentChange = `${val}`;
                this.getTableData();
            },
            getProductValue:function () {
                  this.value = '';
                  _this = this;
                  var getproductvalueurls = this.getproductvalueurl+'?product='+this.product;
                  axios.get(getproductvalueurls).then(function (response) {
                      _this.valueOption = response.data.data;
                  });

            },
            addConfigProductValue:function () {
                      _this = this;
                    //form 表单验证
                     if (this.account == ''){
                        this.account = 'other';
                     }
                     if(this.product == ''){
                         this.$message.error('产品不能为空');
                         return;
                     }
                     if (this.channel == ''){
                         this.channel = 0;
                     }
                    if (this.value == ''){
                        this.$message.error('值不能为空');
                        return;
                    }
                    if (this.one_max == ''){
                        this.$message.error('一级max不能为空');
                        return;
                    }
                    if (this.one_min == ''){
                        this.$message.error('一级min不能为空');
                        return;
                    }
                    if (this.two_max == ''){
                        this.$message.error('二级max不能为空');
                        return;
                    }
                    if (this.two_min == ''){
                        this.$message.error('二级min不能为空');
                        return;
                    }
                axios({
                    url:_this.addproductvalueurl,
                    headers:{'content-type':'applicationjson','select':_this.select},
                    method:'POST',
                    data:{"account":this.account,"product":this.product,"channel":this.channel,"value":this.value,"one_max":this.one_max,"one_min":this.one_min,"two_max":this.two_max,"two_min":this.two_min,'select':this.select}
                }).then(function (response) {
                    if(response.data.status == 0){
                         _this.getTableData();
                         _this.$message({ message:response.data.msg,type: 'success'});
                         _this.dislogVisibel = false;
                         _this.account = '';
                         _this.product = '';
                         _this.channel='';
                         _this.value='';
                         _this.one_max='';
                         _this.one_min='';
                         _this.two_max='';
                         _this.two_min='';
                    }else{
                        _this.$message.error(response.data.msg);
                    }
                })
            },
        },

    }
    var Ctor = Vue.extend(Main)
    new Ctor().$mount('#app')
</script>