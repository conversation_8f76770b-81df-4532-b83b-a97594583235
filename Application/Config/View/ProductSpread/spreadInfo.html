<head>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
</head>
<form class="layui-form" style="padding-top: 10px;" action="">
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">客户名称:</label>
            <label class="layui-form-label" style="text-align: left;">{$data.account}</label>
        </div>
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">产品名称:</label>
            <label class="layui-form-label" style="text-align: left;width: 130px;">{$data.product}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">渠道:</label>
            <label class="layui-form-label" style="text-align: left;">{$data.channel}</label>
        </div>
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">值类型:</label>
            <label class="layui-form-label" style="text-align: left;">{$data.value.html}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">一级报警Max</label>
            <div class="layui-input-block">
                <input type="number" style="width: 65px; display: inline;" class="layui-input" name="th_one_max" id="th_one_max" value="{$data.th_one_max}" placeholder="非空"/>%
            </div>
        </div>
        <div class="layui-inline" style="width: 250px;">
            <label class="layui-form-label" style="width: 100px;">一级报警Min</label>
            <div class="layui-input-block">
                <input type="number" style="width: 65px; display: inline;" class="layui-input" name="th_one_min" id="th_one_min" value="{$data.th_one_min}" placeholder="非空"/>%
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">二级报警Max</label>
            <div class="layui-input-block">
                <input type="number"  style="width: 65px; display: inline;" class="layui-input" name="th_two_max" id="th_two_max" value="{$data.th_two_max}" placeholder="非空"/>%
            </div>
        </div>
        <div class="layui-inline" style="width: 250px;">
            <label class="layui-form-label" style="width: 100px;">二级报警Min</label>
            <div class="layui-input-block">
                <input type="number"  style="width: 65px; display: inline;" class="layui-input" name="th_two_min" id="th_two_min" value="{$data.th_two_min}" placeholder="非空"/>%
            </div>
        </div>

    </div>
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 260px; margin-top: 37px;">
            <button type="button" id="edit_submit" class="layui-btn" lay-filter="demo1">立即提交</button>
        </div>
    </div>

    <input type="hidden" id="id" name="id" value="{$data.id}">

</form>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js"></script>
<script type="application/javascript"></script>
<script type="text/javascript">
    $("#edit_submit").on('click', function(){
        let editIndex = layer.load(0, {shade : [0.3, '#393D49']});
        let id = $('#id').val();
        let th_one_max = parseFloat($('#th_one_max').val());
        let th_one_min = parseFloat($('#th_one_min').val());
        let th_two_max = parseFloat($('#th_two_max').val());
        let th_two_min = parseFloat($('#th_two_min').val());
        if ((th_one_max <= th_one_min) || (th_two_max <= th_two_min)) {
            layer.close(editIndex);
            layer.alert("最大值不能小于最小值");
            return false;
        }
        $.ajax({
            type: "POST",
            url:"/Config/ProductSpread/spreadEdit",
            data:{
                "id": id,
                "th_one_max": th_one_max,
                "th_one_min": th_one_min,
                "th_two_max": th_two_max,
                "th_two_min": th_two_min,
            },
            error: function(request) {
                layer.close(editIndex);
                layer.alert("接口异常");
                return false;
            },
            dataType: "json",
            success: function(data) {
                if(data.status == 0){
                    layer.alert("操作成功", function(index){
                        layer.close(index);
                        window.parent.layer.closeAll();
                        window.parent.loadTable();
                    });
                }else{
                    layer.close(editIndex);
                    layer.alert(data.msg);
                }
            }
        });
    });
</script>