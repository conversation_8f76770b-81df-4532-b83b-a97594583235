<head>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.4/layui/css/layui.css">
</head>
<form class="layui-form" style="padding-top: 10px;" action="">
    <div class="layui-form-item">
        <label class="layui-form-label">客户名称</label>
        <div class="layui-input-inline">
            <select name="apikey" lay-search id="apikey" >
                <option value="">--请选择--</option>
                {$data.accountOption}
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">产品名称</label>
        <div class="layui-input-inline">
            <select name="product_id" lay-search id="product_id" lay-filter="product_id">
                <option value="">--请选择--</option>
                {$data.productOption}
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">渠道</label>
        <div class="layui-input-inline">
            <select name="channel_id" lay-search id="channel_id" >
                <option value="">--请选择--</option>
                {$data.channelSelect}
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-inline" style="width: 50%;">
            <label class="layui-form-label">值类型</label>
            <div class="layui-input-inline">
                <select name="value_txt" lay-search id="value_txt" lay-filter="value_txt">
                </select>
            </div>
        </div>
        <div class="layui-input-inline">
            <label class="layui-form-label" style="width: 160px;text-align: left;">* 若无选项，请先添加产品对应的值类型后再操作</label>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">一级报警Max</label>
            <div class="layui-input-block">
                <input type="number" style="width: 57px; display: inline;" class="layui-input" id="th_one_max" name="th_one_max" value="{$data.data.th_one_max}" placeholder="非空"/>%
            </div>
        </div>
        <div class="layui-inline" style="width: 250px;">
            <label class="layui-form-label" style="width: 100px;">一级报警Min</label>
            <div class="layui-input-block">
                <input type="number" style="width: 57px; display: inline;" class="layui-input" id="th_one_min" name="th_one_min" value="{$data.data.th_one_min}" placeholder="非空"/>%
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline" style="width: 290px;">
            <label class="layui-form-label" style="width: 100px;">二级报警Max</label>
            <div class="layui-input-block">
                <input type="number"  style="width: 57px; display: inline;" class="layui-input" id="th_two_max" name="th_two_max" value="{$data.data.th_two_max}" placeholder="非空"/>%
            </div>
        </div>
        <div class="layui-inline" style="width: 250px;">
            <label class="layui-form-label" style="width: 100px;">二级报警Min</label>
            <div class="layui-input-block">
                <input type="number"  style="width: 57px; display: inline;" class="layui-input" id="th_two_min" name="th_two_min" value="{$data.data.th_two_min}" placeholder="非空"/>%
            </div>
        </div>

    </div>
    <div class="layui-form-item">
        <div class="layui-input-block" style="margin-left: 260px;">
            <button type="button" id="add_submit" class="layui-btn" lay-submit="" lay-filter="demo1">立即提交</button>
<!--            <button type="reset" class="layui-btn layui-btn-primary">重置</button>-->
        </div>
    </div>

    <input type="hidden" name="id" value="{$list['id']}">

</form>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js"></script>
<script type="application/javascript"></script>
<script type="text/javascript">
    let values = JSON.parse('{$values}');
    let form = layui.form, layer = layui.layer;
    // 监听
    $(document).ready(function() {
        form.on('select(product_id)', function(data){
            $("#value_txt").empty();
            let pid = data.value;
            if (values[pid]) {
                $("#value_txt").append("<option value=''>--请选择--</option>");
                $(values[pid]).each(function (k, v){
                    console.log(v);
                    $("#value_txt").append("<option value="+v.value+">"+v.name+"</option>");
                });
            }
            form.render('select');
        });
    });

    $("#add_submit").on('click', function() {
        let addIndex = layer.load(0, {shade : [0.3, '#393D49']});

        let apikey = $('#apikey').val();
        let product_id = parseInt($('#product_id').val());
        let cid = parseInt($('#channel_id').val());
        let value_id = parseInt($('#value_txt').val());
        let th_one_max = parseFloat($('#th_one_max').val());
        let th_one_min = parseFloat($('#th_one_min').val());
        let th_two_max = parseFloat($('#th_two_max').val());
        let th_two_min = parseFloat($('#th_two_min').val());
        if (!apikey){
            layer.close(addIndex);
            layer.alert('请选择客户');
            return false;
        }
        if(!product_id){
            layer.close(addIndex);
            layer.alert('请选择产品');
            return false;
        }
        if(!cid){
            layer.close(addIndex);
            layer.alert('请选择渠道');
            return false;
        }
        if(!value_id){
            layer.close(addIndex);
            layer.alert('请选择值类型');
            return false;
        }
        if (!th_one_max || !th_one_min || !th_two_max || !th_two_min) {
            layer.close(addIndex);
            layer.alert("预警值不能为0");
            return false;
        }
        if ((th_one_max <= th_one_min) || (th_two_max <= th_two_min)) {
            layer.close(addIndex);
            layer.alert("最大值不能小于最小值");
            return false;
        }

        $.ajax({
            type: "POST",
            url:"/Config/ProductSpread/spreadAdd",
            data:{
                "apikey": apikey,
                "pid": product_id,
                "cid": cid,
                "value_id": value_id,
                "th_one_max": th_one_max,
                "th_one_min": th_one_min,
                "th_two_max": th_two_max,
                "th_two_min": th_two_min,
            },
            error: function(request) {
                layer.close(addIndex);
                layer.alert("接口异常");
                return false;
            },
            dataType: "json",
            success: function(data) {
                if(data.status == 0){
                    layer.alert("操作成功", function(index){
                        layer.close(index);
                        window.parent.layer.closeAll();
                        window.parent.loadTable();
                    });
                }else{
                    layer.close(addIndex);
                    layer.alert(data.msg);
                }
            }
        });
    });
</script>