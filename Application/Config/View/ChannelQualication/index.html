<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>
    <script type="application/javascript" src="__JS__JsonExportExcel.min.js"></script>
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<style scoped>
    .showText {
        white-space: pre-wrap; /*这是重点。文本换行*/
        padding-left: 4px;
        padding-right: 4px;
    }

    /* 隐藏默认的提示文本 */
    /* 深度选择器隐藏原生input */
    input[type=file] {
        display: none;
    }
</style>

<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>

<div id="app">
    <div class="container" id="cuishou_list_app">
        <div class="panel panel-default">
            <div class="panel-body">
                <form action="" class="form-inline" method="get" id="list_form">
                    <template>
                        <div class="form-group" style="margin-right: 30px;">
                            <label class="control-label">渠道名称</label>
                            <el-input v-model="searchForm.channel_name" placeholder="" clearable
                                      style="width: 158px;"></el-input>
                        </div>

                        <div class="form-group" style="margin-right: 30px;">
                            <label class="control-label">开始日期</label>
                            <el-date-picker v-model="searchForm.start_date" value-format="yyyy-MM-dd"
                                            style="width: 140px;" type="date" placeholder="选择日期"></el-date-picker>
                        </div>
                        <div class="form-group" style="margin-right: 30px;">
                            <label class="control-label">结束日期</label>
                            <el-date-picker v-model="searchForm.end_date" value-format="yyyy-MM-dd"
                                            style="width: 140px;" type="date" placeholder="选择日期"></el-date-picker>

                        </div>
                        <div class="form-group">
                            <el-button type="primary" @click="getTableData()">查询</el-button>
                        </div>
                        <div class="form-group">
                            <el-button type="primary" @click="addTableData()">新增</el-button>
                        </div>

                    </template>
                </form>
            </div>
        </div>
    </div>

    <div class="container">

        <div id="app_body">

            <template>
                <el-table
                        :data="tableData"
                        tooltip-effect="dark"
                        border
                        style="width: 100%"
                        ref="expandTable"
                        row-key="id"
                        @expand-change="handleExpandChange"
                >

                    <el-table-column type="expand">
                        <template #default="{row}">
                            <div style="background-color: #e6f7ff; padding: 15px;">
                                <el-table
                                        :data="row.qualication_detail"
                                        tooltip-effect="dark"
                                        border
                                        style="width: 100%"
                                >
                                    <el-table-column
                                            prop="qualication_name"
                                            label="资质名称"
                                    >
                                    </el-table-column>
                                    <el-table-column prop="detail_file_list" label="资质文件" width="300">
                                        <template #default="{ row }">
                                            <div v-if="row.detail_file_list && row.detail_file_list.length">
                                                <div
                                                        v-for="(file, index) in row.detail_file_list"
                                                        :key="file.id"
                                                        style="display: flex; align-items: center; margin-bottom: 8px;"
                                                >
                                                    <!-- 文件名 -->
                                                    <span style="flex: 1; margin-right: 10px; overflow: hidden; text-overflow: ellipsis;">
                                                      {{ file.original_name }}
                                                     </span>

                                                    <!-- 预览按钮（支持图片/PDF/Office） -->
                                                    <el-tooltip content="预览" placement="top"
                                                                v-if="isPreviewable(file.show_url)">
                                                        <el-button
                                                                size="mini"
                                                                type="text"
                                                                icon="el-icon-view"
                                                                @click="handlePreview(file)"
                                                        />
                                                    </el-tooltip>

                                                    <!-- 下载按钮 -->
                                                    <el-tooltip content="下载" placement="top">
                                                        <el-button
                                                                size="mini"
                                                                type="text"
                                                                icon="el-icon-download"
                                                                @click="downloadFile(file.id)"
                                                        />
                                                    </el-tooltip>
                                                    <!-- 删除 -->
                                                    <el-tooltip content="删除" placement="top">
                                                        <el-button
                                                                size="mini"
                                                                type="text"
                                                                icon="el-icon-delete"
                                                                style="color: #F56C6C;"
                                                                @click.stop="handleRemoveExistFile(file.id)"
                                                        />
                                                    </el-tooltip>
                                                </div>
                                            </div>
                                            <span v-else>暂无文件</span>
                                        </template>
                                    </el-table-column>

                                    <el-table-column
                                            prop="end_date"
                                            label="到期时间"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="product_description"
                                            label="产品描述"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="is_alarm"
                                            label="到期是否报警"
                                            :formatter="formatIsAlarm"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="remark"
                                            label="备注"
                                    >
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                            prop="id"
                            label="ID"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="channel_name"
                            label="渠道名称"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="channel_fullname"
                            label="渠道全称"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="salesman"
                            label="商务"
                            :formatter="formatSalesman"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="is_qualication_passed"
                            label="准入资格是否合格"
                            :formatter="formatQualicationStatus"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="channel_type"
                            label="渠道类型"
                            :formatter="formatChannelType"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="cooperation_status"
                            label="合作状态"
                            :formatter="formatCooperationStatus"
                    >
                    </el-table-column>
                    <el-table-column label="操作" width="170">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    type="primary"
                                    @click="handleEdit(scope.$index, scope.row)">编辑
                            </el-button>

                        </template>
                    </el-table-column>
                </el-table>
            </template>

            <el-dialog :title="dialog_title" :visible.sync="dialogFormVisible" @close="closeDialogCallBack('form')"
                       class="customwidth">
                <el-form :model="form" :rules="rules" ref="form">
                    <el-form-item label="渠道名称" prop="channel_name" :label-width="formLabelWidth">
                        <el-input
                                placeholder="请输入内容"
                                v-model="form.channel_name">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="渠道全称" prop="channel_fullname" :label-width="formLabelWidth">
                        <el-input
                                placeholder="请输入内容"
                                v-model="form.channel_fullname">
                        </el-input>
                    </el-form-item>
                    <el-form-item label=商务 prop="salesman" :label-width="formLabelWidth">

                        <el-select v-model="form.salesman" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="optionsItem in salesmanOptions"
                                    :key="optionsItem.value"
                                    :label="optionsItem.label"
                                    :value="optionsItem.value">
                            </el-option>
                        </el-select>

                    </el-form-item>

                    <el-form-item label=资质审核状态 prop="is_qualication_passed" :label-width="formLabelWidth">
                        <el-select v-model="form.is_qualication_passed" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="optionsItem in isQualicationPassedOptions"
                                    :key="optionsItem.value"
                                    :label="optionsItem.label"
                                    :value="optionsItem.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label=渠道类型 prop="channel_type" :label-width="formLabelWidth">
                        <el-select v-model="form.channel_type" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="optionsItem in channelTypeOptions"
                                    :key="optionsItem.value"
                                    :label="optionsItem.label"
                                    :value="optionsItem.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label=合作状态 prop="channel_type" :label-width="formLabelWidth">
                        <el-select v-model="form.cooperation_status" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="optionsItem in cooperationStatusOptions"
                                    :key="optionsItem.value"
                                    :label="optionsItem.label"
                                    :value="optionsItem.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-divider></el-divider>
                    <div v-for="(item, index) in form.qualication_detail" :key="index" class="qualification-item">
                        <el-divider v-if="index > 0"></el-divider>

                        <el-form-item
                                :label="'资质名称 ' + (index + 1)"
                                :label-width="formLabelWidth"
                        >
                            <el-input v-model="item.qualication_name" placeholder="请输入资质名称"></el-input>
                        </el-form-item>

                        <el-form-item
                                :label="'资质文件 ' + (index + 1)"
                                :label-width="formLabelWidth"
                        >
                            <el-upload
                                    ref="fileUpload"
                                    class="upload-demo"
                                    :auto-upload="false"
                                    :before-upload="() => false"
                                    :action="''"
                                    :on-change="(file, fileList) => handleFileChange(index, file, fileList)"
                                    :on-remove="(file, fileList) => handleFileRemove(index, file, fileList)"
                                    v-model="item.qualication_file_list"
                                    :multiple="true"
                                    :limit="10"
                            >
                                <el-button size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip">可上传多个文件，且单个文件不超过50MB</div>
                                <div slot="tip" class="el-upload__tip">
                                    <div v-if="item.detail_file_list && item.detail_file_list.length">
                                        <div
                                                v-for="(file, index) in item.detail_file_list"
                                                :key="file.id"
                                                style="display: flex; align-items: center; margin-bottom: 8px;"
                                        >
                                            <!-- 文件名 -->
                                            <span style="flex: 1; margin-right: 10px; overflow: hidden; text-overflow: ellipsis;">
                                                      {{ file.original_name }}
                                                     </span>
                                        </div>
                                    </div>
                                </div>
                            </el-upload>
                        </el-form-item>
                        <el-form-item
                                :label="'到期日期 ' + (index + 1)"
                                :label-width="formLabelWidth"
                        >
                            <el-date-picker
                                    v-model="item.end_date"
                                    type="date"
                                    placeholder="选择日期"
                                    value-format="yyyy-MM-dd"
                            ></el-date-picker>
                        </el-form-item>
                        <el-form-item :label="'产品描述 ' + (index + 1)" :label-width="formLabelWidth">
                            <el-input
                                    v-model="item.product_description"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入产品描述信息"
                            ></el-input>
                        </el-form-item>
                        <el-form-item :label="'备注 ' + (index + 1)" :label-width="formLabelWidth">
                            <el-input
                                    v-model="item.remark"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入备注信息"
                            ></el-input>
                        </el-form-item>
                        <el-form-item :label="'到期是否报警 ' + (index + 1)" :label-width="formLabelWidth">
                            <el-radio-group v-model="item.is_alarm">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="2">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-button
                                @click="removeQualification(index)"
                                type="danger"
                                icon="el-icon-delete"
                                size="mini"
                                class="remove-btn"
                        >
                            删除此项
                        </el-button>

                        <el-button
                                @click="addQualification"
                                type="primary"
                                icon="el-icon-plus"
                                size="small"
                                class="add-btn"
                        >
                            添加资质
                        </el-button>
                    </div>

                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogFormVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onSubmit('form')">确 定</el-button>
                </div>
            </el-dialog>


            <!-- 文件预览弹窗 -->
            <el-dialog
                    :visible.sync="previewVisible"
                    :title="previewTitle"
                    width="80%"
                    top="5vh"
            >
                <!-- 图片预览 -->
                <el-image
                        v-if="previewType === 'image'"
                        :src="previewUrl"
                        style="max-width: 100%; max-height: 70vh;"
                        fit="contain"
                />

            </el-dialog>

        </div>

        <div class="block" style="margin-bottom: 16px;">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40, 50]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalNum">
            </el-pagination>
        </div>

    </div>

</div>

<script type="application/javascript" src="__JS__jquery.cookie.min.js"></script>
<script type="application/javascript">
    var user_cookie = $.cookie('PHPSESSID');

    var vm = new Vue({
        el: '#app',
        data: {
            iframeKey: 0,
            iframeLoading: true,
            previewVisible: false,
            previewType: '', // image|pdf|office
            previewUrl: '',
            previewTitle: '',
            tableData: [],
            expandedRowKeys: [],
            onSelectId: '',
            channelQualicationType: 1,
            totalNum: 0,
            pageSize: 10,
            currentPage: 1,
            salesmanOptions: [],
            qualication_file_list: [],
            fileList: [],
            isAlarmOptions: [
                {
                    "label": "默认",
                    "value": 0,
                },
                {
                    "label": "是",
                    "value": 1,
                },
                {
                    "label": "否",
                    "value": 2,
                },
            ],//资质审核状态
            isQualicationPassedOptions: [
                {
                    "label": "待审核",
                    "value": 0,
                },
                {
                    "label": "通过",
                    "value": 1,
                },
                {
                    "label": "不通过",
                    "value": 2,
                },
            ],//资质审核状态
            channelTypeOptions: [
                {
                    "label": "代运营类",
                    "value": 1,
                },
                {
                    "label": "数据供应类-直连",
                    "value": 2,
                },
                {
                    "label": "数据供应类-征信",
                    "value": 3,
                },
                {
                    "label": "运营商类",
                    "value": 4,
                },
                {
                    "label": "代理类",
                    "value": 5,
                },
                {
                    "label": "供应商-其他",
                    "value": 6,
                },
            ],//渠道类型
            cooperationStatusOptions: [
                {
                    "label": "未合作",
                    "value": 1,
                },
                {
                    "label": "合作中",
                    "value": 2,
                },
                {
                    "label": "暂停合作",
                    "value": 3,
                },
                {
                    "label": "已终止",
                    "value": 4,
                },
                {
                    "label": "黑名单",
                    "value": 5,
                }
            ],//合作状态
            dialogFormVisible: false,
            dialog_title: '新增操作',
            form: {
                id: '',
                channel_name: '',
                channel_fullname: '',
                salesman: '',
                is_qualication_passed: '',
                channel_type: '',
                cooperation_status: '',
                qualication_detail: [
                    {
                        qualication_name: '',
                        is_alarm: 1,
                        qualication_file_list: [],
                        end_date: '',
                        product_description: '',
                        remark: '',
                        image_url: '',
                    }
                ]
            },
            dialogChannelQualicationVisible: false,
            formLabelWidth: '120px',
            searchForm: {},
            rules: {
                channel_name: [
                    {required: true, message: '渠道名称'}
                ]
            }
        },
        created: function () {
            this.getTableData();
            this.getOptions()
        },
        methods: {
            getTableData: function () {
                var self = this;
                var channel_name = this.searchForm.channel_name;
                var start_date = this.searchForm.start_date;
                var end_date = this.searchForm.end_date;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getQualicationList";

                var where = {limit: this.pageSize, page: this.currentPage, user_cookie: user_cookie};
                if (channel_name) {
                    where.channel_name = channel_name;
                }
                if (start_date) {
                    where.start_date = start_date;
                }
                if (end_date) {
                    where.end_date = end_date;
                }

                axios.get(url, {params: where}).then(function (response) {
                    self.tableData = response.data.data.list;
                    self.totalNum = response.data.data.total;

                }).catch(function (error) {
                    console.log(error);
                });

                // 2. 在下一次DOM更新后恢复展开状态
                this.$nextTick(() => {
                    this.restoreExpandedRows()
                })

            },
            getOptions: function () {
                url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/getQualicationOptions"
                var self = this;
                axios.get(url).then(function (response) {
                    if (response.data.code == 0) {
                        self.salesmanOptions = response.data.data.salesmanOptions
                    }
                });
            },

            onSubmit: function (formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        var self = this;
                        var url = "";
                        if (this.channelQualicationType == 1) {
                            url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/addQualication"
                        } else {
                            url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/editQualication"
                        }
                        const request_params = new FormData();
                        // 1. 添加普通表单数据
                        request_params.append('id', this.form.id);
                        request_params.append('channel_name', this.form.channel_name);
                        request_params.append('channel_fullname', this.form.channel_fullname);
                        request_params.append('salesman', this.form.salesman);
                        request_params.append('is_qualication_passed', this.form.is_qualication_passed);
                        request_params.append('channel_type', this.form.channel_type);
                        request_params.append('cooperation_status', this.form.cooperation_status);
                        // 2. 添加用户cookie
                        request_params.append('user_cookie', user_cookie);

                        // 3. 添加资质文件数据
                        this.form.qualication_detail.forEach((item, index) => {
                            request_params.append(`qualication_detail[${index}][id]`, item.id);
                            request_params.append(`qualication_detail[${index}][qualication_name]`, item.qualication_name || '');
                            request_params.append(`qualication_detail[${index}][end_date]`, item.end_date || '');
                            request_params.append(`qualication_detail[${index}][is_alarm]`, item.is_alarm || 0);
                            request_params.append(`qualication_detail[${index}][remark]`, item.remark || '');
                            request_params.append(`qualication_detail[${index}][product_description]`, item.product_description || '');

                            // 添加文件
                            if (item.qualication_file_list && item.qualication_file_list.length > 0) {
                                item.qualication_file_list.forEach((fileObj, fileIndex) => {
                                    const file = fileObj.raw || fileObj;
                                    request_params.append(`qualication_detail[${index}][qualication_file_list][]`, file);
                                });
                            }
                        });

                        axios.post(url, request_params, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        }).then(function (response) {
                            if (response.data.code == 0) {
                                successMsg(response.data.msg);
                            } else {
                                errorMsg(response.data.msg);
                            }
                        }).catch(function (error) {
                            console.log(error);
                            errorMsg(error);
                        });
                    } else {
                        console.log('no submit!!');
                        return false;
                    }
                });

                this.getTableData();
            },


            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.pageSize = val;
                this.currentPage = 1;
                this.getTableData();
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.currentPage = val;
                this.getTableData();
            },
            closeDialogCallBack: function (formName) {
                this.$refs[formName].resetFields();
            },
            handleEdit(index, row) {
                this.form.qualication_detail.forEach((_, index) => {
                    this.resetUpload(index);
                });
                this.dialog_title = '编辑渠道资质';
                this.form.id = row.id;
                this.form.channel_name = row.channel_name;
                this.form.channel_fullname = row.channel_fullname;
                this.form.salesman = row.salesman;
                this.form.is_qualication_passed = row.is_qualication_passed;
                this.form.channel_type = row.channel_type;
                this.form.cooperation_status = row.cooperation_status;
                if (row.qualication_detail.length > 0) {
                    this.form.qualication_detail = row.qualication_detail;
                } else {
                    this.form.qualication_detail = [
                        {
                            id: '',
                            qualication_name: '',
                            is_alarm: 1,
                            qualcation_file_list: [],
                            end_date: '',
                            remark: '',
                            image_url: '',
                        }
                    ];
                }

                this.channelQualicationType = 2; //编辑
                this.dialogFormVisible = true;
            },
            addTableData() {
                this.initForm();
                this.channelQualicationType = 1; //新增
                this.dialogFormVisible = true;
            },
            initForm() {
                this.form.qualication_detail.forEach((_, index) => {
                    this.resetUpload(index);
                });
                this.form.id = '';
                this.form.channel_name = '';
                this.form.channel_fullname = '';
                this.form.salesman = '';
                this.form.is_qualication_passed = '';
                this.form.channel_type = '';
                this.form.cooperation_status = '';
                this.form.qualication_detail = [
                    {
                        id: '',
                        qualication_name: '',
                        is_alarm: 1,
                        qualication_file_list: [],
                        end_date: '',
                        remark: '',
                        image_url: '',
                    }
                ];
            },
            // 添加资质项
            addQualification() {
                this.form.qualication_detail.push({
                    id: '',
                    qualication_name: '',
                    is_alarm: 1,
                    qualication_file_list: [],
                    end_date: '',
                    remark: '',
                    image_url: '',
                })
            },
            // 删除资质项
            removeQualification(index) {
                this.form.qualication_detail.splice(index, 1)
            },
            // 处理文件选择
            async handleFileChange(index, file, fileList) {
                // 检查文件大小
                if (file.size > 50 * 1024 * 1024) {
                    this.$message.error('文件大小不能超过50MB')
                    return false
                }

                // 使用 Vue.set 或 this.$set 确保响应式更新
                this.$set(this.form.qualication_detail[index], 'qualication_file_list', [...fileList]);
                return false; // 必须返回 false 阻止自动上传
            },
            // 处理文件移除
            handleFileRemove(index, file, fileList) {
                const newFileList = fileList.filter(f => f.uid !== file.uid);
                this.$set(this.form.qualication_detail[index], 'qualication_file_list', [...fileList]);
            },

            formatQualicationStatus(row) {
                const option = this.isQualicationPassedOptions.find(
                    item => item.value === row.is_qualication_passed
                );
                return option ? option.label : '未知状态';
            },
            formatChannelType(row) {
                const option = this.channelTypeOptions.find(
                    item => item.value === row.channel_type
                );
                return option ? option.label : '未知状态';
            },
            formatCooperationStatus(row) {
                const option = this.cooperationStatusOptions.find(
                    item => item.value === row.cooperation_status
                );
                return option ? option.label : '未知状态';
            },
            formatSalesman(row) {
                const option = this.salesmanOptions.find(
                    item => item.value === row.salesman
                );
                return option ? option.label : '未知状态';
            },
            formatIsAlarm(row) {
                const option = this.isAlarmOptions.find(
                    item => item.value === row.is_alarm
                );
                return option ? option.label : '未知状态';
            },
            getFileUrl(file) {
                return "{$Think.config.FINANCE_MANAGE_API_DOMAIN}" + file;
            },
            // 文件类型判断
            isImage(filename) {
                return /\.(jpe?g|png|gif|webp|bmp)$/i.test(filename);
            },
            isPdf(filename) {
                return /\.pdf$/i.test(filename);
            },
            isOfficeFile(filename) {
                return /\.(docx?|xlsx?|pptx?)$/i.test(filename);
            },
            // 判断文件是否可预览
            isPreviewable(filename) {
                return this.isImage(filename) || this.isPdf(filename) || this.isOfficeFile(filename);
            },

            // 处理预览
            handlePreview(file) {
                const url = this.getFileUrl(file.show_url);
                const filename = file.qualication_file;
                this.previewUrl = url;

                if (this.isImage(filename)) {
                    this.previewType = 'image';
                    this.previewTitle = '图片预览';
                    this.previewVisible = true;
                } else if (this.isPdf(filename)) {
                    window.open(url, '_blank');
                } else if (this.isOfficeFile(filename)) {
                    // this.previewType = 'office';
                    // this.previewTitle = '文档预览';
                    // window.open(
                    //     `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`,
                    //     '_blank'
                    // );
                    this.$message({
                        message: '暂不支持预览',
                        type: 'warning'
                    });
                } else {
                    this.$message({
                        message: '暂不支持预览',
                        type: 'warning'
                    });
                }
            },

            // 文件下载
            downloadFile(id) {
                const url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/downQualicationDetailFile?id=" + id;
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = url;
                document.body.appendChild(iframe);
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 1000);

            },
            // 文件删除
            handleRemoveExistFile(id) {
                if (confirm('确定要删除此项吗？')) {
                    const url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/channel/delQualicationDetailFile?id=" + id;
                    const iframe = document.createElement('iframe');
                    iframe.style.display = 'none';
                    iframe.src = url;
                    document.body.appendChild(iframe);
                    setTimeout(() => {
                        document.body.removeChild(iframe);
                    }, 1000);
                }
                this.getTableData()
            },
            // 处理展开/折叠变化
            handleExpandChange(row, expandedRows) {
                this.expandedRowKeys = expandedRows.map(item => item.id)
            },
            // 恢复之前展开的行
            restoreExpandedRows() {
                this.tableData.forEach(row => {
                    if (this.expandedRowKeys.includes(row.id)) {
                        this.$refs.expandTable.toggleRowExpansion(row, true)
                    }
                })
            },
            resetUpload(index) {
                // 清空数据
                this.$set(this.form.qualication_detail[index], 'qualication_file_list', []);
                this.$set(this.form.qualication_detail[index], 'detail_file_list', []);

                // 清除组件状态
                this.$nextTick(() => {
                    this.$refs.fileUpload[index]?.clearFiles();
                });
            }
        }

    })

    function successMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
        vm.getTableData();
        vm.dialogFormVisible = false;
    }

    function errorMsg(msg) {
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }

</script>
</body>
</html>
