<!DOCTYPE html>
<html>
<head>
<!--    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/bootstrap/3/css/bootstrap.css"/>-->
    <include file="Common@Public/head"/>
    <script src="__JS__select2.full.min.js"></script>
    <link rel="stylesheet" href="__CSS__select2.min.css">
    <link rel="stylesheet" type="text/css" href="__CSS__select2-bootstrap.min.css">
    <link rel="stylesheet" href="__CSS__bootstrap-select.min.css">
    <style>
        .panel-body .form-inline .form-group {
            margin-bottom : 15px;
        }
        .index-btn {
            margin:5px;
        }

        .haveRowSpan{
            display: flex;justify-content: space-between;align-items: center;
        }

    </style>
</head>
<body>
<include file="Common@Public/header" />
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav" />
    </div>
</div>
<div class="container">
    <div class="panel panel-default">
        <div class="panel-body">

            <div class="form-group">
                <label >字段类型:</label>
                <label class="radio-inline"><input type="radio" class="method_input" name="fieldType" value="0" checked>全部</label>
                <label class="radio-inline"><input type="radio" class="method_input" name="fieldType" value="1">评分字段</label>
                <label class="radio-inline"><input type="radio" class="method_input" name="fieldType" value="2">基础字段</label>
                <label class="radio-inline"><input type="radio" class="method_input" name="fieldType" value="3">催收字段</label>
            </div>

            <div class="form-group">
                <?php echo $priceNote ?>
            </div>

        </div>
    </div>
</div>

<div class="container">
    <div class="panel panel-default table-responsive">
        <table class="table table-hover table-bordered" id="target_vue">
            <thead>
            <tr>
                <?php echo $thInfo ?>
            </tr>
            </thead>

            <tbody>

                <?php echo $channelInfo ?>

            </tbody>

        </table>
    </div>
</div>

<div class="modal fade" id="channelUpDownwindow">
    <div class="modal-dialog modal-lg">

        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">渠道上下架</h4>
            </div>

            <div class="modal-body" id="Dialogue_form_add">

                <form class="form-horizontal" action="#" method="post" id="DialogueFormAdd">
                    <div class="form-group" style="margin-left: 10px">
                        <label class="control-label">字段名称：</label>
                        <div>
                            <span id="fieldSpan"></span>
                        </div>
                    </div>

                    <div class="form-group" style="margin-left: 10px">
                        <label class="control-label">渠道上下架：</label>
                        <div id="channelsDiv">
                        </div>
                    </div>

                    <input type="hidden" id="pidInput" value="">

                </form>

            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">返回</button>
                <button type="button" class="btn btn-primary btn-sm btn-submit setChannelButton">确定</button>
            </div>

        </div>

    </div>
</div>

<script src="__JS__opdata.js"></script>
<script type="text/javascript">

    $(".channelUpDown").click(function () {

        var pid = $(this).attr("data-pid");
        var pname = $(this).attr("data-pname");
        var url = "/Config/BxfChannel/getChannel?pid="+pid;
        $("#channelsDiv").html("");
        $.ajax({
            url: url,
            success: function(data) {
                var checkBoxStr = "";
                var channels = data.data;
                var checked = "";
                for( var i in channels ){

                    checked = channels[i].status == 1 ? "checked" : "";

                    checkBoxStr += '<label class="checkbox-inline">';
                    checkBoxStr += '<input type="checkbox" name="channels" value="'+ channels[i].channel +'" '+ checked +'>' + channels[i].channelName;
                    checkBoxStr += '</label>';
                }

                $("#channelsDiv").html(checkBoxStr);
            }
        });

        $("#channelUpDownwindow").modal('show');
        $("#pidInput").val(pid);
        $("#fieldSpan").html(pname);
    });

    $('.setChannelButton').on('click', function(){

        var channelsValue = [] ;
        $("input[name='channels']:checked").each(function () {
            channelsValue.push($(this).val());
        });

        var pid = $("#pidInput").val();
        var url = "/Config/BxfChannel/setChannel?pid="+pid+"&channels="+channelsValue.join(",");

        $.ajax({
            url: url,
            success: function(data) {
                if(data.status == 0){
                    $('#channelUpDownwindow').modal('hide');
                    window.location.reload();
                }else{
                    alert(data.msg);
                }
            }
        });
    });

    $('input[type=radio][name=fieldType]').change(function () {
        type = $(this).val();
        showFieldByType(type);
        changeURLParam('fieldType', type);
    });

    var type = getQueryVariable('fieldType') ? getQueryVariable('fieldType') : 0;
    $('input[type=radio][name=fieldType][value='+ type +']').click();

    function showFieldByType(type)
    {
        if( type == 0 ){
            $(".fieldType").show();
        }else{
            $(".fieldType").hide();
            $(".fieldType"+type).show();
        }
    }

    function getQueryVariable(variable)
    {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
        }
        return(false);
    }

    function changeURLParam(name, value) {
        var url = document.URL, resultUrl = '';
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        var replaceText = name + '=' + value;
        if (r != null) {
            var tmp = url.replace(unescape(name + '=' + r[2]), replaceText);
            resultUrl = (tmp);
        } else {
            if (url.match('[\?]')) {
                resultUrl = url + '&' + replaceText;
            }
            else {
                resultUrl = url + '?' + replaceText;
            }
        }
        history.replaceState(null, null, resultUrl)
    }

</script>

</body>
</html>
