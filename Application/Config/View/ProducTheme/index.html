<html>
<include file="Common@Public/head"/>
<script src="__JS__/vue/vue.js"> </script>
<script src="__JS__/vue/index.js"> </script>
<script src="__JS__/vue/axios.min.js"> </script>
<link rel="stylesheet" href="__JS__/vue/index.css">
<style>
    #app{
        margin-left: 20px;
    }
    .el-input{
        width: 75%;
        float: left;
        margin-left: 5px;
    }
    .el-input-search{
        width: 100%;
    }
    .el-input-inline{
        width: 55%;
        margin-left: 10px;
    }
    .el-select-level{
        width: 100;
    }
    .el-button{
        margin-left: 10px;
    }
    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
        font-size: 10px;
    }
    .el-select{
        float: left;
    }
    .el-button-div-delete{
        float: left;
    }
    .el-button-delete{
        height: 40;
    }
</style>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div id="app">
    <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="选择主题">
            <el-input class="el-input-search" v-model="formInline.theme"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button type="primary" @click="add">新增</el-button>
        </el-form-item>
    </el-form>
    <template>
        <el-table
                :data="tableData"
                style="width: 100%">
            <el-table-column
                    label="产品监控主题"
                    prop="topic">
            </el-table-column>
            <el-table-column
                    label="SQL"
                    prop="sql"
                    show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
                    label="报警展示字段"
                    prop="contains_column">
            </el-table-column>
            <el-table-column
                    label="执行间隔（分钟）"
                    prop="time_interval">
            </el-table-column>
            <el-table-column
                    label="状态"
                    prop="status">
                <template scope="scope">
                    {{ scope.row.status === 1 ?  '开启': '' }}
                    {{ scope.row.status === 0 ?  '关闭': '' }}
                </template>
            </el-table-column>
            <el-table-column
                    label="操作"
                    align="left">
                <template slot-scope="scope">
                    <el-button
                            v-if="scope.row.status == 1"
                            size="mini"
                            type="danger"
                            @click="handleEdit(scope.$index, scope.row)">关闭
                    </el-button>
                    <el-button
                            v-if="scope.row.status == 0"
                            size="mini"
                            type="success"
                            @click="handleEdit(scope.$index, scope.row)">开启
                    </el-button>
                    <el-button type="primary" icon="el-icon-edit" size="mini"  @click="handleDelete(scope.$index, scope.row)"></el-button>
                </template>
            </el-table-column>
        </el-table>
    </template>

    <el-dialog
            title="添加数据"
            :visible.sync="dislogVisibel">
        <span>
             <el-form :model="addForm" ref="form" label-width="100px" class="demo-dynamic">
                <el-form-item
                        prop="主题"
                        label="主题:"
                >
                    <el-input  v-model="addForm.topic" placeholder="请输入主题"></el-input>
                </el-form-item>
                <el-form-item
                        prop="sql"
                        label="sql:"

                >
                    <el-input v-model="addForm.sql" placeholder="请输入sql"></el-input> <el-button type="success" @click.prevent="testSql()">测试sql</el-button>
                </el-form-item>
                <el-form-item
                        prop="字段"
                        label="字段:"
                >
                    <el-input  v-model="addForm.contains_column" placeholder="报警字段 ,分割"></el-input>
                </el-form-item>
                <el-form-item
                        prop="执行间隔"
                        label="执行间隔:"
                >
                    <el-input  v-model="addForm.time_interval" placeholder="sql执行间隔(分钟)"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="success" @click="addDomain">新增条件</el-button>
                </el-form-item>
                <el-form-item
                        v-for="(condition, index) in addForm.conditions"
                        :label="'条件' + index"
                        :key="condition.key"
                        :prop="'conditions.' + index + '.value'"
                >

                  <el-input class="el-input-inline"  v-model="condition.condition" placeholder="sql"></el-input>
                  <el-select class="el-select-level" v-model="condition.level" placeholder="等级">
                      <el-option  label="严重" :value="2"></el-option>
                      <el-option  label="关注" :value="3"></el-option>
                  </el-select>

                      <div class="el-button-div-delete"><el-button class="el-button-delete" type="success" size="mini"  @click.prevent="testSql(condition)">测试</el-button></div>
                    <div class="el-button-div-delete"><el-button class="el-button-delete" icon="el-icon-delete" type="danger" size="mini"  @click.prevent="removeDomain(condition)"></el-button></div>

                </el-form-item>

            </el-form>

        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dislogVisibel=false">取消</el-button>
            <el-button type="primary" @click="submitForm('addForm')">确定</el-button>
        </span>

    </el-dialog>

</div>
</body>
</html>
<script>
    var Main = {
        data() {
            return {
                formInline:{
                    theme:''
                },
                addForm: {
                    conditions: [{
                        condition:'',
                        level:''
                    }],
                    sql: '',
                    topic:'',
                    contains_column:'',
                    time_interval:10,
                    ptm_id: '',
                },
                tableData:[], //表数据
                dislogVisibel:false,
                sqlstatus:false,
                testsqlurl:"{$Think.config.FINANCE_MANAGE_API_DOMAIN}/productheme/testsql", //测试sql
                addurl:"{$Think.config.FINANCE_MANAGE_API_DOMAIN}/productheme/addOrEdit",  //新增产品主题
                getlisturl:"{$Think.config.FINANCE_MANAGE_API_DOMAIN}/productheme/getlist",  //获取产品主题列表
                updatestatusurl:"{$Think.config.FINANCE_MANAGE_API_DOMAIN}/productheme/updatestatus",  //更新状态
                getediturl:"{$Think.config.FINANCE_MANAGE_API_DOMAIN}/productheme/getedit",  //获取编辑信息
            };
        },
        created(){
           this.getproducthemelist(this.getlisturl);
        },
        methods: {
            onSubmit(){
                let url = this.getlisturl+"?topic="+this.formInline.theme
                this.getproducthemelist(url);
            },
            add(){
                this.dislogVisibel=true;
                this.updateAddForm();
            },
            submitForm() {
                if (this.sqlstatus == false){
                    this.$message('请先验证sql');
                    return false;
                }
                if (this.addForm.topic == ''){
                    this.$message('请输入主题');
                    return false;
                }
                if (this.addForm.contains_column == ''){
                    this.$message('请输入字段');
                    return false;
                }
                if (this.addForm.time_interval == ''){
                    this.$message('请输入时间间隔');
                    return false;
                }

                this.sqlstatus = false
                axios({
                    url:this.addurl,
                    method:'POST',
                    data:{"sql":this.addForm.sql,"topic":this.addForm.topic,"contains_column":this.addForm.contains_column,"time_interval":this.addForm.time_interval,"conditions":this.addForm.conditions,"ptm_id":this.addForm.ptm_id},
                }).then(function (response) {
                    if(response.data.status ==0){
                        _this.$message('添加成功');
                        _this.dislogVisibel = false;
                        _this.getproducthemelist(_this.getlisturl);

                    }
                });
            },
            resetForm(formName) {
                this.$refs[formName].resetFields();
            },
            removeDomain(item) {
                var index = this.addForm.conditions.indexOf(item)
                if (index !== -1) {
                    this.addForm.conditions.splice(index, 1)
                }
            },
            addDomain() {
                this.addForm.conditions.push({});
            },
            testSql(condition = ''){

                //执行sql前 sql状态为false
                this.sqlstatus = false;
                _this = this;
                if (condition == ''){
                    var onlysql = 1;
                }else{
                    var onlysql = 0;
                }
                axios({
                    url:this.testsqlurl,
                    method:'POST',
                    data:{"sql":this.addForm.sql,"condition":condition.condition,'onlysql':onlysql}
                }).then(function (response) {
                    if(response.data.status ==0){
                        _this.sqlstatus = true;
                    }
                    _this.$message(response.data.data.msg);
                });
            },
            handleEdit(index, row) {
                //console.log(row);
                _this = this;
                let id = row.id;
                let status = row.status;
                if (status == 1){
                    update_status = 0;
                }else{
                    update_status = 1;
                }
                let  queryurl = this.updatestatusurl+'?id='+id+"&status="+update_status;
                axios({
                    url:queryurl,
                    method:'GET',
                }).then(function (response) {
                    if (response.status == 200){
                       _this.getproducthemelist(_this.getlisturl);
                    }
                });

            },
            handleDelete(index, row) {
                _this = this;
                let id = row.id;
                let  queryurl = this.getediturl+'?id='+id;
                axios({
                    url:queryurl,
                    method:'GET',
                }).then(function (response) {
                    ptm = response.data.data.data;
                    _this.addForm.sql = ptm.sql;
                    _this.addForm.topic = ptm.topic;
                    _this.addForm.contains_column = ptm.contains_column;
                    _this.addForm.time_interval = ptm.time_interval;
                    _this.addForm.conditions = ptm.conditions;
                    _this.addForm.ptm_id = ptm.ptm_id;
                    _this.dislogVisibel = true;

                });
            },
            getproducthemelist(url){
                _this = this;
                axios({
                    url:url,
                    method:'GET',
                    data:{},
                }).then(function (response) {
                    if(response.data.status ==0){
                       _this.tableData =  response.data.data.data;
                    }
                });
            },
            updateAddForm(){
                this.addForm.sql = '';
                this.addForm.topic = '';
                this.addForm.contains_column = '';
                this.addForm.time_interval = 10;
                this.addForm.ptm_id = '';
                this.addForm.conditions =[{
                    condition:'',
                    level:''
                }];
            }
        },

    }
    var Ctor = Vue.extend(Main)
    new Ctor().$mount('#app')
</script>


