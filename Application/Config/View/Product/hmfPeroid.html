<!DOCTYPE html>
<html lang="en">
    <head>
        <!--
        <link rel="stylesheet" type="text/css" href="//unpkg.com/element-ui@2.15.6/lib/theme-chalk/index.css"/>
        -->
        <link rel="stylesheet" type="text/css" href="/statics/js/vue/index.css"/>
        <script type="application/javascript" src="__JS__vue-common.js?version=v1.1"></script>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__/vue/vue.js"></script>
        <script type="application/javascript" src="__JS__/vue/index.js"></script>
        <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

    </head>
    <body>
    <include file="Common@Public/header"/>
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <style>
        .el-dialog__body .el-form .el-input__inner{
            width: 256px;
        }
        .el-dialog{
            height: 240px;
            overflow-y: scroll;
        }
    </style>
    <SCRIPT type="text/javascript">

    </SCRIPT>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">
                <div class="panel-body" style="height:50px;text-align:center;font-size: medium;color:slateblue;">
                    <span id="timer">{{content}}</span><span>秒后自动刷新</span>
                    <!--
                    <el-form :inline="true" :model="searchForm"  label-width="100px" class="demo-form-inline">

                        <el-form-item>
                            <el-button type="primary" @click="searchTableData()">查询</el-button>
                        </el-form-item>

                    </el-form>
                    -->
                </div>
            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>

                    <el-table
                            v-loading="loading"
                            element-loading-text="拼命加载中"
                            element-loading-spinner="el-icon-loading"
                            empty-text=""
                            :data="tableData"
                            border
                            style="width: 100%">
                        <!--增加内容-->
                        <template slot="empty">
                            <p>{{tableDataText}}</p>
                        </template>

                        <el-table-column
                                prop="use_date"
                                label="使用账期"
                                >
                        </el-table-column>
                        <!--
                        <el-table-column
                                prop="date"
                                label="日期"
                        >
                        -->
                        </el-table-column>

                        <el-table-column label="操作" width="300">
                            <template slot-scope="scope">
                                <el-button type="primary" v-if="scope.row.status == 1" @click="confirmChangeStatus(scope)" icon="el-icon-upload2">未用</el-button>
                                <el-button type="info" v-if="scope.row.status == 2" disabled icon="el-icon-loading">处理中</el-button>
                                <el-button type="success" v-if="scope.row.status == 3" disabled icon="el-icon-check">启用</el-button>
                            </template>
                        </el-table-column>


                    </el-table>
                </template>

            </div>


            <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">

            </div>


            <el-dialog
                    title="提示"
                    :visible.sync="dialogTableVisible"
                    width="30%"
                    @close="closeDialogCallBack('form')">
                <span>{{tips_text}}</span>
                <el-input v-model="modify_id"  v-show="false"></el-input>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogTableVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleChangeStatus()">确 定</el-button>
                </span>
            </el-dialog>

        </div>

    </div>


    <script type="application/javascript">

        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/hmfPeriod";
        //var max_time = 10;//倒计时默认值按秒计算,自己调整!
        //var timer = null;
        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                tableDataText: '',
                historyData:[],
                historyTotalNum: 0,
                historyPageSize: 10,
                historyCurrentPage:1,
                max_time: 60,
                content: 60,
                history_father_id: '',
                history_father_name: '',
                history_node: '',
                history_type: '',
                history_type_name: '',
                history_node_name: '',
                history_use_date: '',
                history_date: '',
                //dialogFormVisible: false,
                dialogTableVisible: false,
                //dialog_title:'添加数据',
                history_title:'历史账期',
                tips_text:'',
                modify_id:'',
                scope:'',
                status:0,
                //operate:false,
                form: {
                    id:''
                },
                formLabelWidth: '120px',
                searchForm: {
                    limit_month:'1',
                    month: getMonth()
                },
                loading: false
            },
            created: function(){
                this.getTableData();
                //this.getFatherProductSelectData();
                //this.getCustomerSelectData();
                //this.getOperatorSelectData();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    self.loading = true;
                    var where = {};

                    axios.post(url, where).then(function (response) {
                        self.loading = false;
                        self.countDown();
                        //console.log(response);
                        self.tableData = response.data.data.list;
                        self.totalNum = response.data.data.count;
                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                objectSpanMethod :function({ row, column, rowIndex, columnIndex }) {
                    //console.log(row.father_name_col);

                },
                countDown :function() {

                    this.content = this.max_time;
                    let clock = setInterval(() => {
                        this.max_time --;
                        this.content = this.max_time;
                        if (this.max_time <= 1) {
                            window.clearInterval(clock);
                            this.max_time = 60;
                            this.getTableData();
                        }
                    }, 1000)
                },
                onSubmit:function(formName){

                },
                getWhere:function(){
                    var self = this;
                    return {
                        father_id:self.history_father_id,
                        father_name:self.history_father_name,
                        node:self.history_node,
                        node_name:self.history_node_name,
                        type:self.history_type,
                        type_name:self.history_type_name,
                        use_date:self.history_use_date,
                        date:self.history_date
                    };

                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                    this.historyPageSize = val;
                    this.historyCurrentPage = 1;
                    var where = this.getWhere();
                    this.handleHistory(0, where);
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                    this.historyCurrentPage = val;
                    var where = this.getWhere();
                    console.log(where);
                    this.handleHistory(0, where);
                },
                searchTableData:function (){
                    this.historyCurrentPage = 1;
                    this.handleHistory();
                },
                addTableData:function(){

                },
                closeDialogCallBack:function(formName){
                    var self = this;
                    self.id = '';
                    self.modify_id = '';
                },
                handleClose(done) {
                    this.$confirm('确认关闭？')
                        .then(_ => {
                            done();
                        })
                        .catch(_ => {});
                },
                handleHistory(index, row) {

                },
                confirmChangeStatus(scope = ''){
                    var self = this;
                    self.scope = scope;
                    self.tips_text = '您确认要切换使用'+scope.row.use_date+'账期? 请谨慎操作!';
                    self.modify_id = scope.row.id;

                    self.dialogTableVisible = true;
                },
                handleChangeStatus(){
                    var self = this;
                    var id = self.modify_id;


                    self.dialogTableVisible = false;
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/setHmfPeriod";
                    var where = {id:id};
                    Request.post(url, where).then(function (response) {
                        console.log('res:'+response.data);
                        if(response.status == 0){
                            //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                            self.$message({
                                showClose: true,
                                message: response.msg,
                                type: 'success'
                            });
                            self.getTableData();
                        }else{
                            errorMsg(response.msg);
                        }

                    });
                    self.scope.row.status = 2;
                    self.modify_id = '';
                    self.id = '';

                },
                resetForm:function(){
                    this.form.id = '';
                    this.form.modify_id = '';
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            vm.resetForm();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }
        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return (arr[2]);
            else
                return null;
        }

        function getMonth(){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth();
            if(month == 0){
                month = 12;
                year = year - 1;
            }
            return year + '-' + month;
        }

    </script>
    </body>
</html>
