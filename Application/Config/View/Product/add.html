<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>产品管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<div class="container" id="add">
    <form class="layui-form layui-row" method="post">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="type">产品类型</label>
            <div class="layui-input-block">
                <select name="type" id="type" lay-filter="type"></select>
            </div>
        </div>

        <div id="fatherIdPerch"></div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="product_id">产品ID</label>
            <div class="layui-input-block">
                <input type="text" maxlength="5" name="product_id" id="product_id" required lay-verify="required|number" placeholder="请输入产品ID" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="product_name">产品名称</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="product_name" id="product_name" required lay-verify="required" placeholder="请输入产品名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="product_enname">英文名称</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="product_enname" id="product_enname" placeholder="请输入产品英文名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="product_key">产品KEY</label>
            <div class="layui-input-block">
                <input type="text" maxlength="32" name="product_key" id="product_key" required lay-verify="required" placeholder="请输入产品KEY" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="sort">排序号</label>
            <div class="layui-input-block">
                <input type="text" maxlength="6" name="sort" id="sort" value="0" required lay-verify="required|number" placeholder="请输入排序号" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item layui-form-text layui-form-text layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="mark">备注</label>
            <div class="layui-input-block">
                <textarea name="mark" id="mark" placeholder="备注" class="layui-textarea"></textarea>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <label class="layui-form-label" for="mark"></label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" lay-submit lay-filter="submit">
                    增加产品
                </button>
            </div>
        </div>
    </form>
</div>

</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    /**
     * 获取父产品ID的选择框HTML内容
     * @param defaultFatherId 默认选择的父产品ID
     */
    let getFatherIdSelectHtml = async function (defaultFatherId = '') {
        //获取数据需要同步请求
        let response = await Request.get("{$Think.config.BACK_API_DOMAIN}/options/product", {
            default : defaultFatherId,
            type    : 2
        });

        return `<div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md8 layui-col-lg8">
            <label class="layui-form-label" for="type">产品类型</label>
            <div class="layui-input-block">
                <select name="type" id="type" lay-filter="type">${response}</select>
            </div>
        </div>`;
    };
    /**
     * 渲染父产品ID选择框
     * @param type
     * @param defaultFatherId
     */
    let drawFatherIdSelect    = async function (type, defaultFatherId = '') {
        let html;
        switch (Number(type)) {
            case 1:
                html = '';
                break;
            case 2:
                html = '';
                break;
            case 3:
                html = await getFatherIdSelectHtml(defaultFatherId);
                break;
            case 4:
                html = await getFatherIdSelectHtml(defaultFatherId);
                break;
            case 5:
                html = await getFatherIdSelectHtml(defaultFatherId);
                break;
            default:
                html = '';
                break;
        }
        $("#fatherIdPerch").html(html);
        layui.form.render('select');
    };


    //产品类型
    (function () {
        let defaultFatherId = Common.getRequestParam('father_id');
        let defaultType     = defaultFatherId ? 3 : 1;
        Request.get("{$Think.config.BACK_API_DOMAIN}/options/productType", {
            "default" : defaultType
        }).then(function (data) {
            $("#type").html(data.data);
            layui.form.render('select');
        });

        //如果产品类型是3/4/5,则需要设置父产品选择框
        if (defaultFatherId) {
            drawFatherIdSelect(defaultType, defaultFatherId);
        }

        //绑定切换产品类型的事件
        layui.form.on('select(type)', function (data) {
            drawFatherIdSelect(data.value, defaultFatherId);
        });
    })();

    //表单提交事件
    layui.form.on('submit(submit)', function (data) {
        console.log(data);
        return;
    });
</script>