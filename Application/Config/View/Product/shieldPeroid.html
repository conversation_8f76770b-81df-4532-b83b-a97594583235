<!DOCTYPE html>
<html lang="en">
    <head>
        <!--
        <link rel="stylesheet" type="text/css" href="//unpkg.com/element-ui@2.15.6/lib/theme-chalk/index.css"/>
        -->
        <link rel="stylesheet" type="text/css" href="/statics/js/vue/index.css"/>
        <script type="application/javascript" src="__JS__vue-common.js?version=v1.1"></script>
        <include file="Common@Public/head"/>
        <script type="application/javascript" src="__JS__/vue/vue.js"></script>
        <script type="application/javascript" src="__JS__/vue/index.js"></script>
        <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

    </head>
    <body>
    <!--<include file="Common@Public/header"/>-->
    <include file="Common@Public/dhb_info"/>
    <div class="container">
        <div id="breadcrumb_box">
            <include file="Common@Public/nav"/>
        </div>
    </div>
    <style>
        .el-dialog__body .el-form .el-input__inner{
            width: 256px;
        }
        .el-dialog{
            height: 240px;
            overflow-y: scroll;
        }
    </style>
    <SCRIPT type="text/javascript">

    </SCRIPT>

    <div id="app">
        <div class="container" id="cuishou_list_app">
            <div class="panel panel-default">

            </div>
        </div>

        <div class="container">

            <div id="app_body">

                <template>
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane label="新模型分布" name="first">
                            <template>
                                <el-table
                                        :data="firstTableData"
                                        border
                                        style="width: 100%">
                                    <el-table-column
                                            prop="type"
                                            label="类型"
                                            >
                                    </el-table-column>
                                    <el-table-column
                                            prop="new_666"
                                            label="666new"
                                            >
                                    </el-table-column>
                                    <el-table-column
                                            prop="old_666"
                                            label="666old"
                                            >
                                    </el-table-column>
                                    <el-table-column
                                            prop="new_667"
                                            label="667new"
                                            >
                                    </el-table-column>
                                    <el-table-column
                                            prop="old_667"
                                            label="667old"
                                            >
                                    </el-table-column>
                                </el-table>
                                <br/>
                                <el-row style="text-align:center;">
                                    <el-button type="primary" @click="cutOver">切换{{use_period}}账期</el-button>
                                    <el-button type="danger" @click="fallBack">回退{{old_period}}账期</el-button>
                                </el-row>
                                <br/>
                                <br>
                            </template>
                        </el-tab-pane>
                        <el-tab-pane label="老模型分布" name="second">
                            <template>
                                <el-table
                                        :data="secondTableData"
                                        border
                                        style="width: 100%">
                                    <el-table-column
                                            prop="type"
                                            label="类型"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="new_666"
                                            label="666new"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="old_666"
                                            label="666old"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="new_667"
                                            label="667new"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="old_667"
                                            label="667old"
                                    >
                                    </el-table-column>
                                </el-table>
                                <br/>
                                <el-row style="text-align:center;">
                                    <el-button type="primary" @click="cutOver">切换{{use_period}}账期</el-button>
                                    <el-button type="danger" @click="fallBack">回退{{old_period}}账期</el-button>
                                </el-row>
                                <br/>
                                <br>
                            </template>
                        </el-tab-pane>
                        <!--
                        <el-tab-pane label="操作" name="third">
                            <el-row style="text-align:center;">
                                <el-button type="primary" @click="cutOver">切换{{use_period}}账期</el-button>
                            </el-row>
                            <br/>
                            <br/>
                            <el-row style="text-align:center;">
                                <el-button type="danger" @click="fallBack">回退{{old_period}}账期</el-button>
                            </el-row>
                            <br/>
                            <br/>
                            <br/>
                            <br/>
                        </el-tab-pane>
                        -->
                    </el-tabs>
                </template>

            </div>

        </div>

    </div>


    <script type="application/javascript">

        var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/getShieldPeriod";
        //var max_time = 10;//倒计时默认值按秒计算,自己调整!
        //var timer = null;
        var vm = new Vue({
            el:'#app',
            data:{
                tableData: [],
                tableDataText: '',
                firstTableData:[],
                secondTableData:[],
                activeName: 'first',
                tabType:'new',
                use_period:'',
                old_period:'',
                form: {
                    id:''
                },
                formLabelWidth: '120px',
                searchForm: {
                    limit_month:'1',
                    month: getMonth()
                }
            },
            created: function(){
                this.getTableData();
            },
            methods:{
                getTableData:function(){
                    var self = this;
                    var where = {};
                    var period = getUrlParam('period');
                    var old_period = getUrlParam('old_period');
                    where.period = period;
                    where.old_period = old_period;
                    where.type = self.tabType;
                    self.use_period = period;
                    self.old_period = old_period;

                    Request.post(url, where).then(function (response) {
                        //console.log('res:'+response.data);
                        if(response.status == 0){
                            if(self.tabType == 'new'){
                                self.firstTableData = response.data;
                            }else{
                                self.secondTableData = response.data;
                            }

                        }else{
                            errorMsg(response.msg);
                        }

                    });

                },
                handleClick(tab, event) {
                    var self = this;

                    //新模型
                    if(tab.index == 0){
                        self.tabType = 'new';
                        self.getTableData();
                    }
                    //老模型
                    if(tab.index == 1){
                        self.tabType = 'old';
                        self.getTableData();
                    }

                    if(tab.index == 2){
                        var period = getUrlParam('period');
                        var old_period = getUrlParam('old_period');
                        self.use_period = period;
                        self.old_period = old_period;
                        self.tabType = '';
                    }

                },
                onSubmit:function(formName){

                },
                getWhere:function(){
                    var self = this;
                    return {
                        father_id:self.history_father_id,
                        father_name:self.history_father_name,
                        node:self.history_node,
                        node_name:self.history_node_name,
                        type:self.history_type,
                        type_name:self.history_type_name,
                        use_date:self.history_use_date,
                        date:self.history_date
                    };

                },
                cutOver:function(){
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/setShieldPeriod";
                    var period = getUrlParam('period');
                    if(!period){
                        errorMsg('切换账期日期有误');
                        return;
                    }
                    this.$confirm('你确定要切换账期到'+period+'吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        closeOnClickModal:false,
                        center:true,
                        type: 'warning'
                    }).then(() => {
                        var where = {};
                        where.period = period;
                        where.type = 'cutover';

                        Request.post(url, where).then(function (response) {
                            //console.log('res:'+response.data);
                            if(response.status == 0){
                                successMsg(response.msg);
                            }else{
                                errorMsg(response.msg);
                            }

                        });

                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消切换操作'
                        });
                    });

                },
                fallBack:function(){
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/setShieldPeriod";
                    var old_period = getUrlParam('old_period');
                    if(!old_period){
                        errorMsg('回退账期日期有误');
                        return;
                    }
                    this.$confirm('你确定要回退账期到'+old_period+'吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        closeOnClickModal:false,
                        center:true,
                        type: 'warning'
                    }).then(() => {
                        var where = {};
                        where.period = old_period;
                        where.type = 'fallback';
                        Request.post(url, where).then(function (response) {
                            //console.log('res:'+response.data);
                            if(response.status == 0){
                                successMsg(response.msg);
                            }else{
                                errorMsg(response.msg);
                            }

                        });

                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消回退操作'
                        });
                    });

                },
                closeDialogCallBack:function(formName){
                    var self = this;
                    self.id = '';
                    self.modify_id = '';
                },

                resetForm:function(){
                    this.form.id = '';
                    this.form.modify_id = '';
                }

            }

        })

        function successMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'success'
            });
            vm.getTableData();
            vm.resetForm();
            vm.dialogFormVisible = false;
        }
        function errorMsg(msg){
            vm.$message({
                showClose: true,
                message: msg,
                type: 'error'
            });
        }
        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return (arr[2]);
            else
                return null;
        }

        function getMonth(){
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth();
            if(month == 0){
                month = 12;
                year = year - 1;
            }
            return year + '-' + month;
        }

        function getUrlParam(name)
        {
            var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg);  //匹配目标参数
            if (r!=null)
                return unescape(r[2]);

            return null; //返回参数值
        }

    </script>
    </body>
</html>
