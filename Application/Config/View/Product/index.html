<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>产品管理</title>
    <include file="Common@Public/head"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATICS__layui-v2.5.6/layui/css/layui.css">
</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<div class="container" id="search">
    <form class="layui-form layui-row list_form">
        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md4 layui-col-lg3">
            <label class="layui-form-label" for="product_id">选择产品</label>
            <div class="layui-input-block">
                <select name="product_id" lay-search id="product_id"></select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm12 layui-col-md2 layui-col-lg2">
            <label class="layui-form-label" for="status">选择产品</label>
            <div class="layui-input-block">
                <select name="status" id="status">
                    <option value="">全部</option>
                    <option value="0">禁用</option>
                    <option value="1" selected>可用</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <div class="layui-btn layui-btn-normal" lay-submit id="query" lay-filter="list_form">
                <i class="layui-icon">&#xe615;</i> 查询
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12 layui-col-sm6 layui-col-md2 layui-col-lg1">
            <button type="button" class="layui-btn" onclick="Popup.iframe('./add.html', '增加产品')">
                增加产品
            </button>
        </div>
    </form>
</div>

<div class="container">
    <div id="list_table" class="list_table" lay-filter="table"></div>
</div>
</body>
</html>
<script src="__JS__jquery.min.js" type="text/javascript"></script>
<script type="application/javascript" src="__STATICS__layui-v2.5.6/layui/layui.all.js"></script>
<script type="application/javascript" src="__JS__common.js?version=v1.1"></script>
<script type="application/javascript">
    //渲染产品下拉选项框
    (function () {
        Request.get("{$Think.config.BACK_API_DOMAIN}/options/product", {
            is_with_id : true
        }).then(function (data) {
            $("#product_id").html(`<option value="">--全部--</option>${data.data}`);
            layui.form.render('select');
        });
    })();

    window.cache = {
        list_url         : "{$Think.config.BACK_API_DOMAIN}/config/getProductInfo",
        list_order_field : "sort",
        list_order_type  : "desc",
        list_where       : {
            type   : '1,2',
            status : 1
        },
        list_form        : true,
        list_fields      : [[{
            field : 'product_id',
            title : '产品ID',
            width : 100,
            align : 'center',
        }, {
            field   : 'product_name',
            title   : '产品名称',
            align   : 'left',
            templet : function (data) {
                if (2 === Number(data.type)) {
                    return `<a onclick="Popup.iframe('./children_product.html?father_id=${data.product_id}')" class="link">${data.product_name}</a>`;
                }
                return data.product_name;
            }
        }, {
            field : 'product_key',
            title : "产品KEY",
            align : "left"
        }, {
            field   : 'status',
            title   : "可用状态",
            align   : "center",
            width   : 100,
            templet : function (data) {
                return 1 === Number(data.status) ? `<span class="color_green">可用</span>` : `<span class="color_red">禁用</span>`;
            }
        }, {
            field : 'mark',
            title : "备注",
            align : "left"
        }, {
            field : 'update_at',
            title : "修改时间",
            align : "left"
        }, {
            field : 'admin',
            title : "操作人",
            align : "left"
        }, {
            field   : 'operation',
            title   : "操作",
            align   : "left",
            width   : 300,
            templet : function (data) {
                $operation = `
<button class="layui-btn layui-btn-xs" onclick="Popup.iframe('./index.html?product_id=${data.product_id}', '编辑产品')">
编辑
</button>
<button class="layui-btn layui-btn-normal layui-btn-xs" onclick="Popup.iframe('./config_bill.html?product_id=${data.product_id}', '计费配置')">
计费配置
</button>
<button class="layui-btn layui-btn-danger layui-btn-xs" onclick="Popup.iframe('./index.html', '产品配置')">
产品配置
</button>
                `;

                if (2 === Number(data.type)) {
                    $operation += `
<button class="layui-btn layui-btn-xs" onclick="Popup.iframe('./add.html?father_id=${data.product_id}', '增加产品')">
增加子产品
</button>
`;
                }
                return $operation;
            }
        }]]
    };

    //加载表格
    Table.reloadTable({
        where     : window.cache.list_where,
        method    : 'get',
        page      : false,
        limit     : 10000,
        parseData : function (response) {
            return {
                "code" : response.status,
                "msg"  : response.msg,
                "data" : response.data
            };
        }
    });

</script>