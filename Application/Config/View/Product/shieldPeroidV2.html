<!DOCTYPE html>
<html lang="en">
<head>
    <!--
    <link rel="stylesheet" type="text/css" href="//unpkg.com/element-ui@2.15.6/lib/theme-chalk/index.css"/>
    -->
    <link rel="stylesheet" type="text/css" href="/statics/js/vue/index.css"/>
    <script type="application/javascript" src="__JS__vue-common.js?version=v1.1"></script>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner{
        width: 256px;
    }
    .el-dialog{
        height: 240px;
        overflow-y: scroll;
    }
</style>
<SCRIPT type="text/javascript">

</SCRIPT>

<div id="app">
    <div class="container" id="cuishou_list_app">
        <div class="panel panel-default">
            <div class="panel-body" style="height:50px;text-align:center;font-size: medium;color:slateblue;">
                <span>号码风险等级帐期切换</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div id="app_body">
            <template>
                <el-table
                        v-loading="loading"
                        element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading"
                        empty-text=""
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column prop="periodTypeTxt" label="类型"></el-table-column>
                    <el-table-column prop="period" label="使用账期"></el-table-column>
                    <el-table-column label="操作" width="300">
                        <template slot-scope="scope">
                            <el-button type="primary" v-if="scope.row.status == 1" @click="confirmChangeStatus(scope)" icon="el-icon-upload2">未用</el-button>
                            <el-button type="info" v-if="scope.row.status == 2" disabled icon="el-icon-loading">处理中</el-button>
                            <el-button type="success" v-if="scope.row.status == 3" disabled icon="el-icon-check">启用</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </div>
        <div class="block" style="margin-bottom: 18px;margin-top: 10px;text-align:right;">
        </div>
        <el-dialog
                title="提示"
                :visible.sync="dialogTableVisible"
                width="30%"
                @close="closeDialogCallBack('form')">
            <span>{{tips_text}}</span>
            <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogTableVisible = false">取 消</el-button>
                    <el-button type="primary" @click="handleChangeStatus()">确 定</el-button>
                </span>
        </el-dialog>
    </div>
</div>
<script type="application/javascript">

    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/getShieldPeriodV2";
    //var max_time = 10;//倒计时默认值按秒计算,自己调整!
    //var timer = null;
    var vm = new Vue({
        el:'#app',
        data:{
            tableData: [],
            max_time: 60,
            content: 60,
            dialogTableVisible: false,
            tips_text:'',
            period:'',
            periodType:'',
            scope:'',
            status:0,
            form: {
                period:'',
                periodType:'',
            },
            formLabelWidth: '120px',
            loading: false
        },
        created: function(){
            this.getTableData();
        },
        methods:{
            getTableData:function(){
                var self = this;
                self.loading = true;
                var where = {};

                axios.post(url, where).then(function (response) {
                    self.loading = false;
                    self.tableData = response.data.data;
                }).catch(function (error) {
                    console.log(error);
                });

            },
            objectSpanMethod :function({ row, column, rowIndex, columnIndex }) {
                //console.log(row.father_name_col);

            },
            onSubmit:function(formName){

            },
            searchTableData:function (){
                this.historyCurrentPage = 1;
                this.handleHistory();
            },
            addTableData:function(){

            },
            closeDialogCallBack:function(formName){
                var self = this;
                self.period = '';
                self.periodType = '';
            },
            handleClose(done) {
                this.$confirm('确认关闭？')
                    .then(_ => {
                        done();
                    })
                    .catch(_ => {});
            },
            handleHistory(index, row) {

            },
            confirmChangeStatus(scope = ''){
                var self = this;
                self.scope = scope;
                console.log(self.scope);
                self.tips_text = '您确认要切换使用'+scope.row.period+'账期? 请谨慎操作!';
                self.period = scope.row.period;
                self.periodType = scope.row.periodType;

                self.dialogTableVisible = true;
            },
            handleChangeStatus(){
                var self = this;
                var period = self.period;
                var periodType = self.periodType;

                self.dialogTableVisible = false;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/period/setShieldPeriodV2";
                var where = {period:period,periodType:periodType};
                axios.post(url, where).then(function (response) {
                    console.log(response);
                    if(response.data.status == 0){
                        //在这个ajax请求里 如果不使用self中间转换一下，直接使用this操作不行，已验证
                        self.$message({
                            showClose: true,
                            message: '切换成功!',
                            type: 'success'
                        });
                    }else{
                        self.$message({
                            showClose: true,
                            message: response.data.msg,
                            type: 'error'
                        });
                    }
                    self.getTableData();
                });
                self.period = '';
                self.periodType = '';

            },
            resetForm:function(){
                this.form.period = '';
                this.form.periodType = '';
            }

        }

    })

    function successMsg(msg){
        vm.$message({
            showClose: true,
            message: msg,
            type: 'success'
        });
        vm.getTableData();
        vm.resetForm();
        vm.dialogFormVisible = false;
    }
    function errorMsg(msg){
        vm.$message({
            showClose: true,
            message: msg,
            type: 'error'
        });
    }
    function getCookie(name) {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg))
            return (arr[2]);
        else
            return null;
    }

    function getMonth(){
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth();
        if(month == 0){
            month = 12;
            year = year - 1;
        }
        return year + '-' + month;
    }

</script>
</body>
</html>
