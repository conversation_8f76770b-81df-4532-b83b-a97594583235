<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner {
        width: 256px;
    }

    /*.el-table .cell {*/
    /*    white-space: pre-line;*/
    /*}*/
</style>

<div id="app">
    <div class="container" id="cuishou_list_app">
        <div class="panel panel-default">
            <div class="panel-body">

                <el-form id="form_0" :inline="true" :model="searchForm" label-width="100px" class="demo-form-inline">

                    <el-form-item label="产品">
                        <el-select v-model="searchForm.interface_name" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in interfaceInfo"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="渠道">
                        <el-select v-model="searchForm.channel_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in channelList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="searchTableData(0)">查询</el-button>
                    </el-form-item>
                </el-form>

                <el-form id="form_1" :inline="true" :model="form" label-width="100px" class="demo-form-inline">

                    <el-form-item label="账号名称">
                        <el-select v-model="form.account_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in accountList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="产品">
                        <el-select v-model="form.product_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in productList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="渠道">
                        <el-select v-model="form.channel_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in channelAll"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="searchTableData(1)">查询</el-button>
                        <el-button type="success" @click="addConfig()">添加配置</el-button>
                    </el-form-item>
                </el-form>

            </div>
        </div>
    </div>

    <div class="container">

        <div id="app_body">

            <template>
                <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                    <el-tab-pane label="渠道分流配置" name="first"></el-tab-pane>
                    <el-tab-pane label="客户渠道配置" name="second"></el-tab-pane>
                </el-tabs>
            </template>
            <template>
                <el-table id="first"
                          :data="firstTableData"
                          :key="randomKey"
                          @cell-dblclick="editData"
                          :span-method="objectSpanMethod"
                          border
                          style="width: 100%">
                    <el-table-column
                            prop="interface_label"
                            label="产品"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="operator"
                            label="运营商"
                            width="65"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="encrypt"
                            label="加密方式"
                            width="80"
                    >
                    </el-table-column>
                    <el-table-column
                            v-for="(item,index) in tableHead"
                            :prop="item.name"
                            :label="item.label" :key="item.channel_id"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row[item.name]['price'] }}</span>
                            <br>
                            <el-input type="text"
                                      v-if="scope.row[scope.column.property + 'isShow'] && scope.row[item.name]['price'] !== ''"
                                      :ref="item.name"
                                      v-model="scope.row[item.name]['ratio']"
                                      @blur="alterData(scope.row, scope.column, item.name)"
                            ></el-input>
                            <span v-else>{{ scope.row[item.name]['ratio'] }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-table id="second"
                          :data="secondTableData"
                          border
                          style="width: 100%;">
                    <el-table-column
                            prop="account_name"
                            label="账号名称"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="product_name"
                            label="产品"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="operator_name"
                            label="运营商"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="channel_name_need"
                            label="要走的渠道"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="channel_name_no"
                            label="不走的渠道"
                    >
                    </el-table-column>
                    <el-table-column
                            label="操作"
                    >

                        <template slot-scope="scope">
                            <el-button @click="handleEdit(scope.$index, scope.row)" type="text" size="small">编辑
                            </el-button>
                            <el-button @click="handleDelete(scope.$index, scope.row)" type="text" size="small">删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>

            <div class="block" id="page">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="1"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="20"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="totalNum">
                </el-pagination>
            </div>

            <el-dialog title="添加配置" :visible.sync="dialogFormVisible" @close="resetForm('addForm')">
                <el-form :model="addForm" :rules="rules" ref="addForm" method="post">
                    <el-form-item label="账号名称" prop="account_id" :label-width="formLabelWidth">
                        <el-select v-model="addForm.account_id" filterable clearable placeholder="请选择"
                                   @change="selectAccount(addForm.account_id)" v-bind:disabled="addForm.id > 0">
                            <el-option
                                    v-for="item in accountList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品" prop="product_id" :label-width="formLabelWidth">
                        <el-select v-model="addForm.product_id" filterable clearable placeholder="请选择"
                                   @change="selectProduct(addForm.product_id)" v-bind:disabled="addForm.id > 0">
                            <el-option
                                    v-for="item in accountProduct"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value+'_'+item.label">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="运营商" prop="operator" :label-width="formLabelWidth" >
                        <el-select v-model="addForm.operator" filterable clearable placeholder="请选择"
                                   v-bind:disabled="addForm.id > 0 && addForm.operator != 'ALL'">
                            <el-option label="不区分" value="ALL"></el-option>
                            <el-option label="移动" value="CMCC"></el-option>
                            <el-option label="联通" value="CUCC"></el-option>
                            <el-option label="电信" value="CTCC"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="要走的渠道" prop="channel_need" :label-width="formLabelWidth">
                        <el-checkbox-group v-model="addForm.channel_need" v-if="channelNeedList.length != 0" @change="selectChannel(addForm.channel_need)">
                            <el-checkbox type="array" v-for="item in channelNeedList"
                                         :key="item.value"
                                         :label="item.value"
                                         name="channel_need">
                                {{item.label}}
                            </el-checkbox>
                        </el-checkbox-group>
                        <span v-else>该产品无渠道</span>
                    </el-form-item>
                    <el-form-item label="不走的渠道" prop="channel_no" :label-width="formLabelWidth">
                        <el-checkbox-group v-model="addForm.channel_no" v-if="channelNoList.length != 0">
                            <el-checkbox type="array" label="all" name="channel_no">
                                全部
                            </el-checkbox>
                            <el-checkbox type="array" v-for="item in channelNoList"
                                         :key="item.value"
                                         :label="item.value"
                                         name="channel_no">
                                {{item.label}}
                            </el-checkbox>
                        </el-checkbox-group>
                        <span v-else>该产品无渠道</span>
                    </el-form-item>

                    <el-form-item label="备注信息" prop="remark" :label-width="formLabelWidth" style="width:420px;">
                        <el-input
                                type="textarea"
                                :rows="5"
                                placeholder="备注信息"
                                v-model="addForm.remark"
                                maxlength="1000"
                                show-word-limit
                        >
                        </el-input>
                    </el-form-item>


                    <el-input type="hidden" v-model="addForm.id"></el-input>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="resetForm('addForm')">取 消</el-button>
                    <el-button type="primary" @click="submitForm('addForm')">确 定</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</div>

<script type="application/javascript">
    $(function () {
        var active = '{$active}';
        if (active == 'first') {
            $('#first').show();
            $('#form_0').show();
            $('#form_1').hide();
            $('#second').hide();
            $('#page').hide();
        } else {
            $('#second').show();
            $('#form_1').show();
            $('#first').hide();
            $('#form_0').hide();
            $('#page').show();
        }
    })

    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getShuntList";

    var vm = new Vue({
        el: '#app',
        data: {
            tableHead: [],
            firstTableData: [],
            secondTableData: [],
            totalNum: 0,
            pageSize: 20,
            currentPage: 1,
            interfaceInfo: [],
            channelList: [],
            accountList: [],
            productList: [],
            channelAll: [],
            channelNeedList: [],
            channelNoList: [],
            accountProduct: [],
            getUrl: url,
            formLabelWidth: '120px',
            searchForm: {
                interface_name: '',
                channel_id: ''
            },
            form: {
                account_id: '',
                product_id: '',
                channel_id: ''
            },
            addForm: {
                id: '',
                account_id: '',
                product_id: '',
                operator: '',
                channel_need: [],
                channel_no: [],
                remark: '',
            },
            rules: {
                account_id: [
                    {required: true, message: '请选择账号', trigger: 'change'}
                ],
                product_id: [
                    {required: true, message: '请选择产品', trigger: 'change'}
                ],
                operator: [
                    {required: true, message: '请选择运营商', trigger: 'change'}
                ],
            },
            randomKey: Math.random(),
            activeName: "{$active}",
            dialogFormVisible: false,
        },
        created: function () {
            if (this.activeName == 'first') {
                this.getTableData();
            } else {
                this.getSecondTableData();
            }
            this.getInterfaceSelectData();
            this.getChannelSelectData();
            this.getAccountSelectData();
            this.getProductSelectData();
            this.getChannelAllSelectData();
        },
        methods: {
            handleClick: function (tab, event) {
                if (tab.index == 0) {
                    $('#first').show();
                    $('#second').hide();
                    $('#form_0').show();
                    $('#form_1').hide();
                    $('#page').hide();
                    this.getTableData();
                } else {
                    $('#first').hide();
                    $('#second').show();
                    $('#form_0').hide();
                    $('#form_1').show();
                    $('#page').show();
                    this.getSecondTableData();
                }
            },
            getTableData: function () {
                var self = this;
                var interface_name = this.searchForm.interface_name;
                var channel_id = this.searchForm.channel_id;
                var where = {};
                if (interface_name) {
                    where.interface_name = interface_name;
                }
                if (channel_id) {
                    where.channel_id = channel_id;
                }

                axios.post(url, where).then(function (response) {
                    // console.log(response.data.data);
                    self.tableHead = response.data.data.channel_info;
                    self.firstTableData = response.data.data.list;
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getSecondTableData: function () {
                var self = this;
                var account_id = this.form.account_id;
                var product_id = this.form.product_id;
                var channel_id = this.form.channel_id;
                var where = {limit: this.pageSize, page: this.currentPage};
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getConfigChannelAccount";
                if (account_id) {
                    where.account_id = account_id;
                }
                if (product_id) {
                    where.product_id = product_id;
                }
                if (channel_id) {
                    where.channel_id = channel_id;
                }

                axios.post(url, where).then(function (response) {
                    //console.log(response.data.data);
                    self.secondTableData = response.data.data.list;
                    self.totalNum = response.data.data.count;
                    self.currentPage = 1;
                }).catch(function (error) {
                    console.log(error);
                });
            },
            objectSpanMethod: function ({row, column, rowIndex, columnIndex}) {
                if (columnIndex === 0) {
                    if (rowIndex % 9 === 0) {
                        return {
                            rowspan: 9,
                            colspan: 1
                        };
                    } else {
                        return {
                            rowspan: 0,
                            colspan: 0
                        };
                    }
                }
                if (columnIndex === 1) {
                    if (rowIndex % 3 === 0) {
                        return {
                            rowspan: 3,
                            colspan: 1
                        };
                    } else {
                        return {
                            rowspan: 0,
                            colspan: 0
                        };
                    }
                }
            },
            getInterfaceSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getInterfaceList";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.interfaceInfo.push({value: key, label: name});
                        //console.log(key)
                    });
                    //self.interfaceInfo.push({value: '0', label: '全部'});
                }).catch(function (error) {
                    console.log(error);
                });

            },
            getChannelSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getChannelList";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.channelList.push({value: key, label: name});
                    });
                    //self.channelList.push({value: '0', label: '全部'});
                }).catch(function (error) {
                    console.log(error);
                });

            },
            getAccountSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/apikey?father_id=200&options=false&is_with_id=true";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.accountList.push({value: key, label: name});
                    });
                    //self.accountList.push({value: '0', label: '全部'});
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getProductSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/options/childrenProductList?father_id=200";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.productList.push({value: key, label: name});
                        //console.log(key)
                    });
                    //self.productList.push({value: '0', label: '全部'});
                }).catch(function (error) {
                    console.log(error);
                });
            },
            getChannelAllSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getChannelList?type=all";
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.channelAll.push({value: key, label: name});
                    });
                    //self.channelAll.push({value: '0', label: '全部'});

                }).catch(function (error) {
                    console.log(error);
                });
            },
            searchTableData: function (index) {
                // console.log(index)
                if (index == 0) {
                    this.getTableData();
                } else {
                    this.getSecondTableData();
                }
            },
            selectAccount: function (account_id) {
                var self = this;
                self.accountProduct = [];
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getAccountProductList?account_id=" + account_id;
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.accountProduct.push({value: key, label: name});
                    });
                }).catch(function (error) {
                    console.log(error);
                });
            },
            selectProduct: function (product_id) {
                // console.log(product_id)
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getChannelList?type=all&product_name=" + product_id;
                axios.get(url, {}).then(function (response) {
                    // console.log(response.data.data)
                    self.channelNeedList = [];
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        if (key != 'length') {
                            var name = response.data.data[key];
                            self.channelNeedList.push({value: key, label: name});
                        }
                    });
                    self.channelNoList = self.channelNeedList;
                }).catch(function (error) {
                    console.log(error);
                });
            },
            selectChannel: function (channel_need) {
                //console.log(channel_need);
            },
            addConfig: function () {
                this.dialogFormVisible = true;
            },
            submitForm: function (formName) {
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/doConfigChannelAccount";
                var id = this.addForm.id;
                var product_id = this.addForm.product_id;
                var account_id = this.addForm.account_id;
                var operator = this.addForm.operator;
                var channel_need = this.addForm.channel_need;
                var channel_no = this.addForm.channel_no;
                var remark = this.addForm.remark;
                var is_flag = false;
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        is_flag = true;
                        return true;
                    } else {
                        is_flag = false
                        console.log('error submit!!');
                        return false;
                    }
                });

                if (!channel_no && !channel_need) {
                    this.$message({
                        showClose: true,
                        message: '要走的渠道或不走的渠道，请至少选择一项',
                        type: 'error',
                        offset: 300
                    });
                    is_flag = false;
                    return false;
                }
                if (is_flag) {
                    var data = {
                        id: id,
                        product_id: product_id,
                        account_id: account_id,
                        operator: operator,
                        channel_need: channel_need,
                        channel_no: channel_no,
                        remark: remark,
                    }
                    var self = this;
                    axios.post(url, data).then(function (response) {
                        // console.log(response.data);
                        if (response.data.status == 0) {
                            this.dialogFormVisible = false;
                            window.location.href = window.location.pathname + '?active=second';
                        } else {
                            self.$message({
                                showClose: true,
                                message: response.data.msg,
                                type: 'error',
                                offset: 300
                            });
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });

                }
            },
            resetForm: function (formName) {
                this.$nextTick(() => {
                    this.addForm = {
                        id: '',
                        account_id: '',
                        product_id: '',
                        operator: '',
                        channel_need: [],
                        channel_no: [],
                        remark: '',
                    };
                    this.$refs[formName].resetFields();
                })
                // console.log(this.addForm);
                this.dialogFormVisible = false;
                this.accountProduct = [];
                this.channelNeedList = [];
                this.channelNoList = [];
            },
            handleEdit: function (index, row) {
                // console.log(index, row);
                var self = this;
                this.dialogFormVisible = true;
                this.addForm.id = row.id;
                this.addForm.account_id = row.account_id;
                this.addForm.product_id = row.product_id + '_' + row.product_name;
                this.addForm.operator = row.operator;
                this.addForm.remark = row.remark;
                // this.addForm.operator = [];
                // Object.getOwnPropertyNames(row.operator_list).forEach(function (key) {
                //     if (key != '__ob__') {
                //         self.addForm.operator.push(key);
                //     }
                // })

                this.addForm.channel_need = [];
                var numReg = /^[0-9]*$/
                var numRe = new RegExp(numReg)
                Object.getOwnPropertyNames(row.channel_name_need_list).forEach(function (key) {
                    if (numRe.test(key)) {
                        self.addForm.channel_need.push(key);
                    }
                })
                this.addForm.channel_no = [];
                Object.getOwnPropertyNames(row.channel_name_no_list).forEach(function (key) {
                    if (numRe.test(key) || key == 'all') {
                        self.addForm.channel_no.push(key);
                    }
                })

                this.selectAccount(row.account_id);
                this.selectProduct(this.addForm.product_id);
            },
            handleDelete: function (index, row) {
                // console.log(index, row);
                var self = this;
                this.$confirm('确认要删除吗？', '', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/delConfigChannelAccount";
                    axios.post(url, {id: row.id}).then(function (response) {
                        //console.log(response.data.data);
                        if (response.data.status == 0) {
                            self.secondTableData.splice(index, 1);
                        }
                    }).catch(function (error) {
                        console.log(error);
                    });
                }).catch(() => {
                    //console.log()
                });
            },
            editData: function (row, column, cell, event) {
                row[column.property + "isShow"] = true
                //refreshTable是table数据改动时，刷新table的
                this.refreshTable()
                this.$nextTick(() => {
                    this.$refs[column.property][0] && this.$refs[column.property][0].focus()
                })
            },
            alterData: function (row, column, channel) {
                var self = this;
                var ratio = this.$refs[column.property][0].value;
                const r = /^\+?[0-9]*%$/; // 正整数（可以以0打头）
                if (!r.test(ratio) ) {
                    self.$message({
                        showClose: true,
                        message: '分流占比不能为小数',
                        type: 'error',
                        offset: 300
                    });
                    return false;
                }
                ratio = parseInt(ratio);
                if (ratio > 100) {
                    self.$message({
                        showClose: true,
                        message: '超过100%占比，不可设置',
                        type: 'error',
                        offset: 300
                    });
                    return false;
                }

                var total_ratio = 0;
                $.each(row, function (index, content) {
                    // console.log($.isPlainObject(content))
                    if ($.isPlainObject(content)) {
                        if (content['ratio'] != '') {
                            total_ratio = parseInt(total_ratio) + parseInt(content['ratio']);
                        }
                    }
                })

                if (total_ratio > 100) {
                    self.$message({
                        showClose: true,
                        message: '超过100%占比，不可设置',
                        type: 'error',
                        offset: 300
                    });
                    return false;
                }

                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/doShunt";
                var params = {
                    operator: row.operator_en,
                    channel_id: row[channel]['channel_id'],
                    interface_id: row[channel]['interface_id'],
                    encrypt: row.encrypt_en,
                    ratio: ratio
                }
                axios.post(url, params).then(function (response) {
                    if (response.data.status == 0) {
                        self.$message({
                            showClose: true,
                            message: '设置成功',
                            type: 'success',
                            offset: 300
                        });
                    } else {
                        self.$message({
                            showClose: true,
                            message: response.data.msg,
                            type: 'error',
                            offset: 300
                        });
                        //self.$message.error(response.data.msg);
                    }

                    row[column.property + "isShow"] = false
                    self.refreshTable()
                }).catch(function (error) {
                    self.$message({
                        showClose: true,
                        message: error,
                        type: 'error',
                        offset: 300
                    });
                    console.log(error);
                });
            },
            refreshTable() {
                this.randomKey = Math.random()
            },
            handleSizeChange(val) {
                this.pageSize = `${val}`;
                this.getSecondTableData();
            },
            handleCurrentChange(val) {
                this.currentPage = `${val}`;
                this.getSecondTableData();
            },
        }
    })

</script>
</body>
</html>
