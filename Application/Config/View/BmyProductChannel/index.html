<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" type="text/css" href="__JS__vue/index.css"/>
    <include file="Common@Public/head"/>
    <script type="application/javascript" src="__JS__/vue/vue.js"></script>
    <script type="application/javascript" src="__JS__/vue/index.js"></script>
    <script type="application/javascript" src="__JS__/vue/axios.min.js"></script>

</head>
<body>
<include file="Common@Public/header"/>
<include file="Common@Public/dhb_info"/>
<div class="container">
    <div id="breadcrumb_box">
        <include file="Common@Public/nav"/>
    </div>
</div>
<style>
    .el-dialog__body .el-form .el-input__inner {
        width: 256px;
    }
    .el-table .cell {
        white-space: pre-line;
    }
</style>

<div id="app">
    <div class="container" id="cuishou_list_app">
        <div class="panel panel-default">
            <div class="panel-body">

                <el-form :inline="true" :model="searchForm" label-width="100px" class="demo-form-inline">

                    <el-form-item label="产品">
                        <el-select v-model="searchForm.interface_name" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in interfaceInfo"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="渠道">
                        <el-select v-model="searchForm.channel_id" filterable clearable placeholder="请选择">
                            <el-option
                                    v-for="item in channelList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="searchTableData()">查询</el-button>
                    </el-form-item>
                </el-form>

            </div>
        </div>
    </div>

    <div class="container">

        <div id="app_body">

            <template>
                <el-table
                        :data="tableData"
                        :span-method="objectSpanMethod"
                        border
                        style="width: 100%">
                    <el-table-column
                            prop="interface_label"
                            label="产品"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="operator"
                            label="运营商"
                            width="65"
                    >
                    </el-table-column>
                    <el-table-column
                            v-for="(item,index) in tableHead"
                            :prop="item.name"
                            :label="item.label" :key="item.channel_id"
                    >

                    </el-table-column>
                </el-table>
            </template>
        </div>
    </div>

</div>

<script type="application/javascript">

    var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getProductChannel";

    var vm = new Vue({
        el: '#app',
        data: {
            tableHead: [],
            tableData: [],
            totalNum: 0,
            pageSize: 10,
            currentPage: 1,
            interfaceInfo: [],
            channelList: [],
            pickerOptions: {},
            getUrl: url,
            formLabelWidth: '120px',
            searchForm: {
                interface_name: '',
                channel_id: ''
            },
            rules: {
                interface_name: [
                    {required: true, message: '请选择产品', trigger: 'change'}
                ],
                channel_id: [
                    {required: true, message: '请选择渠道', trigger: 'change'}
                ],
            }
        },
        created: function () {
            this.getTableData();
            this.getInterfaceSelectData();
            this.getChannelSelectData();
        },
        methods: {
            getTableData: function () {
                var self = this;
                var interface_name = this.searchForm.interface_name;
                var channel_id = this.searchForm.channel_id;
                var where = {limit:this.pageSize, page:this.currentPage};
                if(interface_name){
                    where.interface_name = interface_name;
                }
                if(channel_id){
                    where.channel_id = channel_id;
                }
                axios.post(url, where).then(function (response) {
                    //console.log(response.data.data);
                    self.tableHead = response.data.data.channel_info;
                    self.tableData = response.data.data.list;
                }).catch(function (error) {
                    console.log(error);
                });

            },
            objectSpanMethod: function ({row, column, rowIndex, columnIndex}) {
                if (columnIndex === 0) {

                    if (rowIndex % 4 === 0) {
                        return {
                            rowspan: 4,
                            colspan: 1
                        };
                    } else {
                        return {
                            rowspan: 0,
                            colspan: 0
                        };
                    }
                }
            },
            getInterfaceSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getInterfaceList";
                axios.get(url, {
                }).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.interfaceInfo.push({value: key, label: name});
                        //console.log(key)
                    });
                    self.interfaceInfo.push({value:'0',label:'全部'});
                }).catch(function (error) {
                    console.log(error);
                });

            },
            getChannelSelectData: function () {
                var self = this;
                var url = "{$Think.config.FINANCE_MANAGE_API_DOMAIN}/bmy/getChannelList";
                axios.get(url, {
                }).then(function (response) {
                    // console.log(response.data.data)
                    Object.getOwnPropertyNames(response.data.data).forEach(function (key) {
                        var name = response.data.data[key];
                        self.channelList.push({value: key, label: name});
                    });
                    self.channelList.push({value:'0',label:'全部'});

                }).catch(function (error) {
                    console.log(error);
                });

            },
            searchTableData: function () {
                this.currentPage = 1;
                this.getTableData();
            },
        }

    })

</script>
</body>
</html>
