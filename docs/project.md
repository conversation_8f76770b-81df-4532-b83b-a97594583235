# 金融科技业务部后台管理系统项目文档

## 1. 项目概述

### 1.1 项目基本信息
- **项目名称**: 金融科技业务部后台管理系统 (bmp-mgmt.dianhua.cn)
- **项目描述**: 面向金融科技业务的综合性后台管理平台，提供数据产品统计、客户管理、API服务等功能
- **开发团队**: 金融科技业务部
- **项目状态**: 生产环境运行中

### 1.2 主要功能模块
- **数据产品管理**: 邦秒爬、邦秒配、邦企查、邦信分、邦秒验等核心产品
- **统计分析**: 产品调用量统计、成功率分析、日报生成
- **客户管理**: 客户信息维护、账号管理、权限控制
- **售前管理**: 测试客户管理、产品测试结果跟踪
- **系统管理**: 用户权限、角色管理、系统配置
- **API服务**: 对外提供标准化API接口

### 1.3 技术架构概览
```
前端层: Bootstrap + Vue.js + jQuery + ECharts
应用层: ThinkPHP 3.2.3 MVC架构
数据层: MySQL (主库) + MongoDB (辅助)
缓存层: Redis + 文件缓存
部署层: 多环境部署 (dev/test/prod)
```

### 1.4 业务背景
系统服务于金融科技领域的数据产品业务，主要为银行、金融机构提供风险评估、数据验证、企业查询等服务。通过统一的后台管理平台，实现产品运营、客户服务、数据统计的一体化管理。

## 2. 技术栈详情

### 2.1 后端框架
- **框架**: ThinkPHP 3.2.3
- **PHP版本**: >= 5.3.0
- **架构模式**: MVC (Model-View-Controller)
- **命名空间**: 支持PSR-4自动加载
- **扩展要求**: ext-json, ext-curl

### 2.2 前端技术栈
- **CSS框架**: Bootstrap 3.x
- **JavaScript库**: 
  - jQuery (核心库)
  - Vue.js (组件化开发)
  - ECharts (图表展示)
  - LayUI (UI组件)
- **编辑器**: CKEditor 4, WangEditor
- **表格组件**: Bootstrap Table, DataTables
- **其他工具**: Select2, DatePicker, FileUploader

### 2.3 数据库系统
- **主数据库**: MySQL
  - 表前缀: `crs_`
  - 字符集: UTF-8
  - 存储引擎: InnoDB
- **辅助数据库**: MongoDB
  - 用途: 大数据存储、日志记录
  - 集合: backend, risk_list等

### 2.4 第三方依赖
- **Composer**: PHP依赖管理
- **PHPMailer**: 邮件发送
- **PhpExcel**: Excel文件处理
- **Redis**: 缓存和会话存储

## 3. 项目结构分析

### 3.1 目录结构
```
bmp-mgmt.dianhua.cn/
├── Application/           # 应用目录
│   ├── Account/          # 账号管理模块
│   ├── Api/              # API接口模块
│   ├── Bill/             # 账单管理模块
│   ├── Common/           # 公共模块
│   ├── Config/           # 配置管理模块
│   ├── Home/             # 主页模块
│   ├── Monitor/          # 监控模块
│   ├── Opdata/           # 运营数据模块
│   ├── Payment/          # 支付模块
│   ├── PreSales/         # 售前管理模块
│   ├── ReportTask/       # 报表任务模块
│   ├── Stat/             # 统计模块
│   ├── System/           # 系统管理模块
│   └── Task/             # 定时任务模块
├── ThinkPHP/             # 框架核心
├── statics/              # 静态资源
├── doc/                  # 项目文档
├── cache/                # 缓存目录
└── index.php             # 入口文件
```

### 3.2 核心模块功能

#### 3.2.1 Home模块 - 主页和核心功能
- **统计概览**: 产品调用量、成功率统计
- **数据展示**: 图表、报表展示
- **产品管理**: 邦秒爬、邦秒配等产品配置
- **费用统计**: 产品费用统计和分析

#### 3.2.2 Api模块 - 对外接口服务
- **催收分API**: 信用评分接口
- **数据验证API**: 号码验证、企业查询
- **统计API**: 调用量统计接口
- **权限验证**: API访问权限控制

#### 3.2.3 Stat模块 - 统计分析
- **产品统计**: 各产品调用量、成功率统计
- **日报生成**: 自动生成日报数据
- **数据分析**: 趋势分析、对比分析
- **支持产品**: 邦秒爬、邦秒配、邦企查、邦信分、邦秒验等

#### 3.2.4 System模块 - 系统管理
- **用户管理**: 用户增删改查、权限分配
- **角色管理**: 角色定义、权限配置
- **部门管理**: 组织架构管理
- **系统配置**: 参数配置、缓存清理

#### 3.2.5 Account模块 - 账号管理
- **客户管理**: 客户信息维护
- **账号配置**: 产品账号配置
- **权限控制**: 数据权限管理
- **销售管理**: 销售人员分配

#### 3.2.6 PreSales模块 - 售前管理
- **测试客户**: 售前测试客户管理
- **产品测试**: 各产品测试结果记录
- **签约跟踪**: 客户签约状态跟踪
- **转化分析**: 签约率统计分析

### 3.3 核心配置文件
- **index.php**: 应用入口，环境配置
- **Application/Common/Conf/config.php**: 公共配置
- **Application/Common/Conf/{env}.php**: 环境特定配置
- **composer.json**: PHP依赖配置

## 4. 开发环境配置

### 4.1 环境要求
- **PHP**: >= 5.3.0
- **MySQL**: >= 5.5
- **MongoDB**: >= 3.0 (可选)
- **Redis**: >= 3.0 (可选)
- **Web服务器**: Apache/Nginx

### 4.2 本地开发环境搭建

#### 4.2.1 代码获取
```bash
git clone [repository-url] bmp-mgmt
cd bmp-mgmt
```

#### 4.2.2 依赖安装
```bash
composer install
```

#### 4.2.3 配置文件设置
1. 复制配置模板
```bash
cp Application/Common/Conf/dev.php.example Application/Common/Conf/local.php
```

2. 修改数据库配置
```php
'DB_HOST' => 'localhost',
'DB_NAME' => 'your_database',
'DB_USER' => 'your_username',
'DB_PWD' => 'your_password',
```

#### 4.2.4 数据库初始化
```bash
mysql -u username -p database_name < system_db.sql
```

#### 4.2.5 目录权限设置
```bash
chmod -R 755 cache/
chmod -R 755 Runtime/
```

### 4.3 环境变量说明
- **APP_DEBUG**: 调试模式开关
- **APP_STATUS**: 应用状态 (dev/test/prod)
- **RUNTIME_PATH**: 运行时缓存目录
- **DB_PREFIX**: 数据库表前缀

## 5. 部署和运维

### 5.1 多环境部署配置

#### 5.1.1 开发环境 (dev)
- **配置文件**: `Application/Common/Conf/dev.php`
- **数据库**: 开发库
- **调试模式**: 开启
- **日志级别**: DEBUG

#### 5.1.2 测试环境 (test)
- **配置文件**: `Application/Common/Conf/test.php`
- **数据库**: 测试库
- **调试模式**: 开启
- **日志级别**: INFO

#### 5.1.3 生产环境 (prod)
- **配置文件**: `Application/Common/Conf/prod.php`
- **数据库**: 生产库
- **调试模式**: 关闭
- **日志级别**: ERROR

### 5.2 缓存管理
- **文件缓存**: `cache/` 目录
- **数据库缓存**: 查询结果缓存
- **Redis缓存**: 会话、临时数据
- **清理命令**: System/Index/clear

### 5.3 日志管理
- **日志目录**: `cache/{date}/`
- **日志类型**: 错误日志、API调用日志、业务日志
- **日志轮转**: 按日期自动分割
- **日志清理**: 定期清理过期日志

### 5.4 性能监控
- **断点监控**: `addBreakPoint()` 函数
- **SQL监控**: 开启DB_DEBUG模式
- **内存监控**: 记录内存使用情况
- **响应时间**: 记录页面响应时间

## 6. API接口文档

### 6.1 接口规范
- **协议**: HTTP/HTTPS
- **格式**: JSON
- **认证**: Token/Session认证
- **版本**: 通过URL路径区分

### 6.2 主要接口列表

#### 6.2.1 催收分API
- **接口**: `/Api/BmStatCuishou/index`
- **方法**: POST
- **功能**: 催收分统计数据查询
- **参数**: biz['list_cuishou'], time_begin, time_end

#### 6.2.2 数据验证API
- **接口**: `/Api/DataValidate/*`
- **方法**: POST
- **功能**: 号码验证、数据校验
- **版本**: v1.2

#### 6.2.3 邦企查API
- **接口**: `/Api/BangProduct/*`
- **方法**: POST
- **功能**: 企业信息查询
- **认证**: API Key认证

### 6.3 接口调用示例
```javascript
// 催收分统计查询
$.ajax({
    url: '/Api/BmStatCuishou/index',
    method: 'POST',
    data: {
        'biz[list_cuishou]': [1,2,3],
        'time_begin': '2023-01-01',
        'time_end': '2023-01-31'
    },
    success: function(response) {
        console.log(response.data);
    }
});
```

## 7. 开发规范

### 7.1 代码风格
- **PHP标准**: 遵循PSR-1、PSR-2规范
- **命名约定**: 
  - 类名: PascalCase (如 `UserController`)
  - 方法名: camelCase (如 `getUserInfo`)
  - 变量名: snake_case (如 `$user_info`)
  - 常量: UPPER_CASE (如 `APP_DEBUG`)

### 7.2 文件组织
- **控制器**: `{Module}/Controller/{Name}Controller.class.php`
- **模型**: `{Module}/Model/{Name}Model.class.php`
- **视图**: `{Module}/View/{Controller}/{action}.html`
- **仓库**: `{Module}/Repositories/{Name}Repository.class.php`

### 7.3 安全规范
- **输入验证**: 所有用户输入必须验证
- **SQL注入**: 使用参数化查询
- **XSS防护**: 输出时进行HTML转义
- **CSRF防护**: 使用Token验证
- **权限控制**: 基于角色的访问控制

### 7.4 性能优化
- **数据库优化**: 合理使用索引、避免N+1查询
- **缓存策略**: 合理使用文件缓存和Redis缓存
- **静态资源**: 压缩CSS/JS文件
- **分页处理**: 大数据量查询使用分页

### 7.5 测试规范
- **单元测试**: 核心业务逻辑编写单元测试
- **集成测试**: API接口编写集成测试
- **功能测试**: 关键功能编写功能测试
- **性能测试**: 定期进行性能测试

## 8. 数据库设计

### 8.1 主要数据表

#### 8.1.1 用户权限相关
- **crs_system_user**: 系统用户表
- **crs_system_role**: 角色表
- **crs_system_node**: 权限节点表
- **crs_system_session**: 会话存储表

#### 8.1.2 客户管理相关
- **crs_customer**: 客户信息表
- **crs_account**: 账号配置表
- **crs_customer_consume**: 客户消费记录表
- **crs_receipt**: 收据管理表

#### 8.1.3 产品统计相关
- **crs_crawler_stat**: 邦秒爬统计表
- **crs_matching_stat**: 邦秒配统计表
- **crs_cuishou_stat**: 邦信分统计表
- **crs_bang_product_stat**: 邦企查统计表

#### 8.1.4 售前管理相关
- **crs_pre_sales_customer**: 售前客户表
- **crs_pre_sales_result_***: 各产品测试结果表

### 8.2 数据库连接配置
```php
// MySQL主库配置
'DB_TYPE' => 'mysql',
'DB_HOST' => '数据库主机',
'DB_PORT' => '3306',
'DB_NAME' => '数据库名',
'DB_USER' => '用户名',
'DB_PWD' => '密码',
'DB_PREFIX' => 'crs_',

// MongoDB配置
'MONGO' => [
    'DB_TYPE' => 'mongo',
    'DB_HOST' => 'MongoDB主机',
    'DB_PORT' => '27017',
    'DB_NAME' => 'backend',
]
```

## 9. 故障排除

### 9.1 常见问题

#### 9.1.1 数据库连接失败
**症状**: 页面显示数据库连接错误
**解决方案**:
1. 检查数据库配置文件
2. 确认数据库服务状态
3. 验证用户名密码正确性
4. 检查网络连接

#### 9.1.2 缓存问题
**症状**: 页面显示异常或数据不更新
**解决方案**:
1. 清理缓存: 访问 `/System/Index/clear`
2. 手动删除cache目录
3. 重启Web服务器

#### 9.1.3 权限问题
**症状**: 用户无法访问某些功能
**解决方案**:
1. 检查用户角色配置
2. 验证数据权限设置
3. 确认模块权限分配

### 9.2 日志分析
- **错误日志位置**: `cache/{date}/error.log`
- **SQL日志位置**: `cache/{date}/sql.log`
- **API调用日志**: `cache/{date}/api.log`

### 9.3 性能优化建议
1. **数据库优化**: 定期分析慢查询
2. **缓存优化**: 合理设置缓存过期时间
3. **代码优化**: 避免重复查询
4. **服务器优化**: 调整PHP配置参数

## 10. 更新日志

### v1.0.0 (2024年)
- 完成项目文档初版
- 整理技术栈和架构信息
- 补充开发和部署规范

### 历史版本
- 系统持续迭代更新中
- 各模块功能不断完善
- API接口版本持续升级

## 11. 联系方式

### 11.1 开发团队
- **项目负责人**: 金融科技业务部
- **技术支持**: 开发团队
- **运维支持**: 运维团队

### 11.2 相关链接
- **生产环境**: https://finance-manage.dianhua.cn/
- **测试环境**: http://bmp-mgmt-dev.dianhua.cn
- **API文档**: 详见 `doc/` 目录
- **统计公式**: 详见 `doc/statistics_formula.md`

---

**文档版本**: v1.0
**最后更新**: 2024年
**维护人员**: 金融科技业务部开发团队
