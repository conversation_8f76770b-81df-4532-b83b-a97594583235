# Augment 忽略文件配置
# 金融科技业务部后台管理系统 (bmp-mgmt.dianhua.cn)

# 版本控制系统
.git/
.gitignore
.gitattributes
.gitmodules

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 依赖管理
vendor/
node_modules/
composer.lock
package-lock.json
yarn.lock

# ThinkPHP 框架相关
ThinkPHP/
cache/
Runtime/
Logs/

# 缓存和临时文件
*.cache
*.tmp
*.temp
*.log
*.pid

# 构建和编译输出
dist/
build/
out/
target/

# 配置文件（包含敏感信息）
Application/Common/Conf/prod.php
Application/Common/Conf/dev.php
Application/Common/Conf/test.php
Application/Common/Conf/local.php
Application/Common/Conf/*local*.php
*.env
.env.*

# 数据库文件
*.sql
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
logs/
log/

# 上传文件目录
upload/
uploads/
files/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 系统文件
.htaccess
robots.txt
sitemap.xml

# 第三方库和插件（已在 statics 目录中）
statics/bootstrap-table-master/
statics/ckeditor-v4/
statics/layui-v2.5.4/
statics/layui-v2.5.6/
statics/bignumber.js-master/
statics/jquery-dateFormat-master/

# 文档和说明文件（保留主要文档）
doc/
*.md
!README.md

# 测试文件
test/
tests/
phpunit.xml
phpunit.xml.dist

# 性能分析文件
*.prof
*.profile

# 错误报告
error_log
php_errors.log

# Session 文件
sess_*

# 临时上传文件
tmp/
temp/

# 编译后的资源文件
*.min.js
*.min.css
*.map

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 安全相关
*.key
*.pem
*.crt
*.p12
*.pfx

# 项目特定忽略
favicon.ico
upload_demo.html
system_db.sql

# 开发工具配置
.editorconfig
.eslintrc*
.prettierrc*
.stylelintrc*

# 包管理器缓存
.npm/
.yarn/
.pnpm/

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output/

# 调试文件
*.debug
debug.log
