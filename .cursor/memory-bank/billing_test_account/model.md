### 二、核心数据结构

1. **账号表(account)**

```sql
CREATE TABLE `account` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `account_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '账户id',
  `cid` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '根据account_id生成(先md5，在截取前8位)',
  `ctag` char(32) DEFAULT '' COMMENT '根据account_id + "ctag" 生成(先md5，再截取前8位)',
  `user_agent_number` tinyint(2) DEFAULT '0' COMMENT '代调用户编号, 默认为0没有编号则不可代调，1朴道,2百行',
  `account_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账号名称',
  `customer_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '所属客户id',
  `group_id` char(32) CHARACTER SET utf8 NOT NULL COMMENT '主体id',
  `father_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '父级为0，子级别指向父级account_id',
  `email` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '登录邮箱',
  `password` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '登录密码',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号状态,1可用，0不可用',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '账号类型：1正式，0测试',
  `apikey` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'apikey',
  `appsecret` char(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'appsecret',
  `end_time` bigint(11) NOT NULL COMMENT '账号截止时间',
  `concurrency` int(10) NOT NULL DEFAULT '-1' COMMENT '账号秒并发',
  `access_ip` text CHARACTER SET utf8 COMMENT 'ip白名单',
  `mark` text CHARACTER SET utf8 COMMENT '备注',
  `admin` varchar(30) CHARACTER SET utf8 DEFAULT '' COMMENT '操作人',
  `appsecret_bak` char(100) NOT NULL DEFAULT '',
  `close_zhilian` tinyint(2) NOT NULL DEFAULT '0' COMMENT '直连关闭 0不关闭，1关闭',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `is_delete` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `cache_days` int(10) NOT NULL DEFAULT '1' COMMENT '缓存时长 天',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_name` (`account_name`) USING BTREE,
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `account_father_id` (`father_id`) USING BTREE,
  KEY `account_type` (`type`) USING BTREE,
  KEY `account_end_time` (`end_time`) USING BTREE,
  KEY `account_email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3760 DEFAULT CHARSET=utf8mb4 COMMENT='账号表'
```

2. **账号产品关联表(account_product)**

```sql
CREATE TABLE `account_product` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `account_id` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户id',
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号-产品状态：1可用，2禁用',
  `use_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品使用类型:1外部调用,2内部调用',
  `contract_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '签约状态:1已签约已付款,2已签约未付费,3未签约,4其他,5特殊客户',
  `end_time` int(11) DEFAULT NULL,
  `daily_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '日限额用量',
  `month_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '月限额用量',
  `year_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '年限额量',
  `total_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '总限额量',
  `concurrency` int(10) NOT NULL DEFAULT '1' COMMENT '秒并发',
  `data` text CHARACTER SET utf8 COMMENT '产品配置参数',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `limit_start_date` date DEFAULT NULL COMMENT '总限量预警开始时间',
  `source_tags` varchar(6) DEFAULT '' COMMENT '切换渠道标记,仅标记子产品 填写已换签的渠道id 例如 ,1,2,99,100,',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ca_acc_pro_unique_id` (`account_id`,`product_id`) USING BTREE,
  KEY `ca_account_id` (`account_id`) USING BTREE,
  KEY `ca_product_id` (`product_id`) USING BTREE,
  KEY `ca_status` (`status`) USING BTREE,
  KEY `ca_cntract_status` (`contract_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16680 DEFAULT CHARSET=utf8mb4 COMMENT='账号-产品-关联表'
```
