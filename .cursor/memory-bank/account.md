## 账号管理功能架构分析

### 1. 系统模块定位

账号管理是系统中连接客户与产品服务的核心桥梁模块，负责：

- 账号生命周期管理（创建、编辑、删除、状态控制）
- 账号与产品的关联管理
- API 访问凭证管理（apikey/appsecret）
- 访问权限控制（IP 白名单、并发限制）

### 2. 核心架构特点

#### 2.1 分层架构设计

- **控制器层**：`AccountController`（正式账号）、`TestAccountController`（测试账号）
- **模型层**：`AccountModel`（账号）、`AccountProductModel`（账号产品关联）
- **仓库层**：`AccountRepository`（复杂业务逻辑封装）
- **视图层**：完整的 CRUD 操作界面

#### 2.2 账号类型体系

```php
// 账号类型分类
type = 1: 正式账号 (FA前缀)
type = 0: 测试账号 (TA前缀)
type = 2: 售前测试账号
```

#### 2.3 账号 ID 生成规则

```php
// 正式账号：****************
// 测试账号：****************
// 格式：前缀 + YYYYMMDD + 6位随机字符
```

### 3. 核心业务流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant AC as AccountController
    participant AR as AccountRepository
    participant AM as AccountModel
    participant APM as AccountProductModel
    participant CM as CustomerModel
    participant DAC as DataAuthController

    Note over U,DAC: 账号创建流程
    U->>AC: 1. 访问添加账号页面
    AC->>CM: 2. 获取客户信息
    CM-->>AC: 3. 返回客户数据
    AC-->>U: 4. 显示添加表单

    U->>AC: 5. 提交账号数据
    AC->>DAC: 6. 数据权限校验
    DAC-->>AC: 7. 权限验证通过
    AC->>AM: 8. 创建账号
    AM->>AM: 9. 生成account_id
    AM->>AM: 10. 生成密码/CID等
    AM-->>AC: 11. 返回账号ID

    Note over U,DAC: 产品开通流程
    U->>AC: 12. 请求开通产品
    AC->>AR: 13. 调用产品开通
    AR->>AR: 14. 验证产品参数
    AR->>APM: 15. 创建账号产品关联
    APM-->>AR: 16. 关联创建成功
    AR->>AR: 17. 监听状态变化
    AR-->>AC: 18. 开通完成
    AC-->>U: 19. 返回成功结果
```

### 4. 核心数据结构

#### 4.1 账号表结构（account）

```php
- id: 主键
- account_id: 业务账号ID（FA/TA前缀）
- account_name: 账号名称
- customer_id: 所属客户ID
- type: 账号类型（1正式/0测试/2售前测试）
- status: 状态（1可用/0禁用）
- apikey/appsecret: API访问凭证
- access_ip: IP白名单（序列化存储）
- end_time: 截止时间
- concurrency: 并发限制
- cache_days: 缓存天数
- group_id: 主体ID
- cid/ctag: 系统生成标识
- close_zhilian: 直连关闭标志
```

#### 4.2 账号产品关联表（account_product）

```php
- id: 主键
- account_id: 账号ID
- product_id: 产品ID
- status: 产品状态
- contract_status: 签约状态
- end_time: 产品截止时间
- daily_limit/month_limit/year_limit/total_limit: 各类限额
- concurrency: 产品并发数
- data: 产品配置（JSON格式）
```

### 5. 关键业务特性

#### 5.1 数据权限控制

- 继承客户级数据权限控制
- 通过`DataAuthController`实现统一权限管理
- 支持基于部门、角色的数据隔离

#### 5.2 产品开通管理

- 支持多产品并行开通
- 产品状态与客户状态联动
- 复杂产品配置的 JSON 存储

#### 5.3 访问控制机制

- IP 白名单控制
- 并发数限制
- 直连开关控制
- 缓存策略配置

#### 5.4 售前测试账号特殊处理

- 独立的管理入口`TestAccountController`
- 与售前客户系统集成
- 支持测试到正式的转换

### 6. 系统集成特点

#### 6.1 与客户管理集成

- 账号必须归属于特定客户
- 继承客户的主体、权限等属性
- 客户状态变化影响账号状态

#### 6.2 与产品配置集成

- 动态产品配置加载
- 产品参数验证
- 特殊产品（如邦信分、催收等）的差异化处理

#### 6.3 与计费系统集成

- 账号变更触发计费配置更新
- 产品开通触发计费监听事件

### 8. 潜在优化方向

1. **状态机模式**：账号状态变化可考虑引入状态机
2. **事件驱动**：账号变更可通过事件系统解耦
3. **配置中心化**：产品配置可考虑独立配置中心
4. **缓存优化**：热点账号数据可引入缓存机制
