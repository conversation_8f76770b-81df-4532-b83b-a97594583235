### 核心数据表

#### 1. 售前客户相关表

- **pre_sales_customer**
  - 售前客户主表
  - 存储客户基本信息、跟进状态等

```sql
CREATE TABLE `pre_sales_customer` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `customer_id` char(32) NOT NULL DEFAULT '' COMMENT '签约客户ID（关联的customer_id）',
  `sign_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '签约时间',
  `ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户ID',
  `ps_customer_name` varchar(255) NOT NULL DEFAULT '' COMMENT '售前客户名称',
  `company` varchar(255) NOT NULL DEFAULT '' COMMENT '公司名称',
  `region` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '区域分布（1华东、2华北、3华南、4西南、5华北银行、6华东银行）',
  `business_admin` varchar(100) NOT NULL DEFAULT '' COMMENT '商务跟进人',
  `first_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '一级公司类型',
  `twice_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '二级公司类型',
  `trace_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '首次跟进时间',
  `test_product` varchar(255) NOT NULL DEFAULT '' COMMENT '测试的产品（product_id,product_id）',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '增加时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  `create_admin` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `update_admin` varchar(100) NOT NULL DEFAULT '' COMMENT '最后一次修改人',
  `delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
  UNIQUE KEY `ps_customer_name_delete_time` (`delete_time`,`ps_customer_name`) USING BTREE,
  UNIQUE KEY `company_delete_time` (`delete_time`,`company`) USING BTREE,
  KEY `region` (`region`) USING BTREE,
  KEY `customer_id` (`customer_id`) USING BTREE,
  KEY `business_admin` (`business_admin`) USING BTREE,
  KEY `sign_time` (`sign_time`) USING BTREE,
  KEY `trace_time` (`trace_time`) USING BTREE,
  KEY `twice_type` (`twice_type`)
) ENGINE=InnoDB AUTO_INCREMENT=311 DEFAULT CHARSET=utf8 COMMENT='售前客户表'
```

- **pre_sales_customer_product_middle**
  - 客户产品关联中间表
  - 记录客户开通的产品信息

```sql
CREATE TABLE `pre_sales_customer_product_middle` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户ID',
  `customer_id` char(32) NOT NULL DEFAULT '' COMMENT '签约客户ID（关联的customer_id）',
  `account_id` char(32) NOT NULL DEFAULT '' COMMENT '开通产品的账号ID（关联的account_id）',
  `product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开通的产品ID',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '增加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=414 DEFAULT CHARSET=utf8 COMMENT='售前客户开通产品中间表'
```

#### 2. 测试结果相关表

- **pre_sales_pub_result**

  - 测试结果公共表
  - 存储通用测试信息（提交时间、返回时间等）

```sql
CREATE TABLE `pre_sales_pub_result` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `return_time` int(10) unsigned DEFAULT NULL COMMENT '测试返回结果日期',
  `submit_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '提交测试日期',
  `test_phase` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '测试期数',
  `test_days` int(10) unsigned DEFAULT NULL COMMENT '测试天数',
  `test_num` int(10) unsigned DEFAULT NULL COMMENT '测试数量',
  `test_progress` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '测试进度（1测试中，2枚举值：3测试中，4测试完成、5测试结果已返回、6测试结果暂不返回）',
  `test_result` varchar(60) NOT NULL DEFAULT '' COMMENT '客户反馈（测试结果）',
  `remark` varchar(150) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '增加时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  `create_admin` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `update_admin` varchar(100) NOT NULL DEFAULT '' COMMENT '最后一次修改人',
  PRIMARY KEY (`id`),
  KEY `return_time` (`return_time`) USING BTREE,
  KEY `submit_time` (`submit_time`) USING BTREE,
  KEY `test_progress` (`test_progress`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='售前客户表（共用部分）'
```

- **pre_sales_result{产品 ID}**

  - 各产品测试结果表
  - 包含以下具体表： - `pre_sales_result104` (邦秒配)
    `` sql
CREATE TABLE `pre_sales_result104`(
`id`int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id`varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户ID',
`pub_result_id`int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据ID',
`rate_score`float(5,2) unsigned DEFAULT NULL COMMENT '测试命中占比（0-100）',
`result` varchar(100) NOT NULL DEFAULT '' COMMENT '测试结果',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦秒配产品测试结果'
 `` - `pre_sales_result105` (邦信分详单版 V2)

                                    ```sql

                                CREATE TABLE `pre_sales_result105` (
                                `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                `ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
                                `pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
                                `rate_good` float(5,2) unsigned DEFAULT NULL COMMENT '好样本数据占比（0-100）',
                                `rate_sure` float(5,2) unsigned DEFAULT NULL COMMENT '催收命中占比（0-100）',
                                `rate_notsure` float(5,2) unsigned DEFAULT NULL COMMENT '疑似催收命中占比（0-100）',
                                `rate_accord` float(5,2) unsigned DEFAULT NULL COMMENT '只有主叫（0-100）',
                                `rate_passivity` float(5,2) unsigned DEFAULT NULL COMMENT '只有被叫（0-100）',
                                `rate_sure_five` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收 5 个以上（0-100）',
                                `rate_sure_ten` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收 10 个以上（0-100）',
                                PRIMARY KEY (`id`),
                                KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
                                KEY `pub_result_id` (`pub_result_id`) USING BTREE
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦信分（详单版 V2）产品测试结果'

                                    - `pre_sales_result200` (邦秒验)

                                    ```sql

CREATE TABLE `pre_sales_result200` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`product_children_id` varchar(255) NOT NULL DEFAULT '' COMMENT '子产品 ID',
`rate_result` float(5,2) unsigned DEFAULT NULL COMMENT '查得率（0-100）',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='邦秒验产品测试结果'

````


    - `pre_sales_result210` (邦信分快捷版)
    ```sql
CREATE TABLE `pre_sales_result210` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户ID',
  `pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据ID',
  `apply_time` varchar(50) DEFAULT NULL COMMENT '申请时间范围',
  `detail_time` varchar(50) DEFAULT NULL COMMENT '详单时间范围',
  `rate_sure` float(5,2) unsigned DEFAULT NULL COMMENT '催收命中占比（0-100）',
  `rate_notsure` float(5,2) unsigned DEFAULT NULL COMMENT '疑似催收命中占比（0-100）',
  `rate_accord` float(5,2) unsigned DEFAULT NULL COMMENT '只有主叫（0-100）',
  `rate_passivity` float(5,2) unsigned DEFAULT NULL COMMENT '只有被叫（0-100）',
  `rate_sure_five` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收5个以上（0-100）',
  `rate_sure_ten` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收10个以上（0-100）',
  `operator` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '运营商（1联通 2电信 3移动）',
  `real_num` int(10) unsigned DEFAULT NULL COMMENT '实际测试数量',
  `is_important` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否重要客户（0,1）',
  `is_pay` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否付费测试（0,1）',
  `cost` float(10,2) unsigned DEFAULT NULL COMMENT '测试成本',
  PRIMARY KEY (`id`),
  KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
  KEY `pub_result_id` (`pub_result_id`) USING BTREE,
  KEY `is_important` (`is_important`) USING BTREE,
  KEY `is_pay` (`is_pay`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦信分（快捷版）产品测试结果'
````

    - `pre_sales_result401` (邦企查)
    ```sql

CREATE TABLE `pre_sales_result401` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`start_time` int(10) unsigned DEFAULT NULL COMMENT '开始外呼时间',
`end_time` int(10) unsigned DEFAULT NULL COMMENT '结束外呼时间',
`rate_sure` float(5,2) unsigned DEFAULT NULL COMMENT '精准一致（0-100）',
`rate_notsure` float(5,2) unsigned DEFAULT NULL COMMENT '模糊一致（0-100）',
`rate_not` float(5,2) unsigned DEFAULT NULL COMMENT '不一致（0-100）',
`rate_undefined` float(5,2) unsigned DEFAULT NULL COMMENT '无法匹配（0-100）',
`result` varchar(100) NOT NULL DEFAULT '' COMMENT '测试结果',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦企查产品测试结果'

````



### 关联业务表

#### 1. 客户账号相关表

- **customer**

  - 正式客户信息表
  - 用于关联已签约客户

```sql
CREATE TABLE `customer` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `customer_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '客户id',
  `name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '客户名',
  `company` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '公司名称',
  `agent_company` varchar(255) NOT NULL DEFAULT '' COMMENT '代理机构对应的签约公司名称,格式为json',
  `c_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '公司类别 1数据公司,2风控系统,3综合类,4p2p,5现金分期,6消费金融（3C),7农商行,8汽车金融,9消费金融,10其他',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '客户类型',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户状态 1 可用 2 禁用',
  `operator` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '运营跟进人',
  `salesman` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '销售跟进人',
  `admin` varchar(30) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作人',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `is_delete` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `bill_email` varchar(500) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账单收件邮箱',
  `balance` float(11,2) DEFAULT NULL COMMENT '余额预警值（null代表未设置）',
  `balance_percent` float(5,4) DEFAULT NULL COMMENT '余额百分比预警（null代表未设置）',
  `available_days` int(10) unsigned DEFAULT NULL COMMENT '预警剩余消耗天数',
  `bill_cc_email` varchar(500) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账单抄送人',
  `contract_status` tinyint(3) unsigned NOT NULL DEFAULT '4' COMMENT '客户签约状态（1--已签约已付款 2--未签约未付款 3--未签约 4--其他 5--特殊客户）',
  `frequency` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '账单发送频率（0-人工发送、1-每月发送、2-每季度发送、3-每年发送）',
  `payment_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '付款类型(1-预付款客户 2-后付款客户)',
  `credit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户授信额度',
  `email_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '邮件类型 1 标准 2 非标准',
  `customer_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '用户类型：1-金融用户，2-企服用户',
  `channel_mode` tinyint(3) NOT NULL DEFAULT '2' COMMENT '客户类型 是否为渠道客户: 1-渠道客户,2-非渠道客户(直客)',
  `reconciliation_cycle` tinyint(3) NOT NULL DEFAULT '1' COMMENT '对账方式 对账周期: 1-月度,2-季度,3-年度',
  `dhb_sign_corp` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT 'yulore' COMMENT '客户跟电话邦签约的公司简称，全称见common_enum表value值',
  `channel_follower` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '渠道跟进人',
  `source_id` varchar(100) NOT NULL DEFAULT '-1' COMMENT '征信机构id -1不限,0电话邦, 1朴道。多个用英文逗号分隔。',
  `sign_type` varchar(100) NOT NULL DEFAULT '0' COMMENT '征信客户分类 0: 电话邦签约, 10: 朴道签约 逗号分隔',
  `group_id` char(32) DEFAULT '' COMMENT '主体id',
  `introduce_salesman` varchar(100) NOT NULL DEFAULT '' COMMENT '推荐商务',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '100' COMMENT '客户分类 客户级别 头部客户:10, 重要客户:50, 一般客户:100',
  `level_scale` varchar(16) NOT NULL DEFAULT '' COMMENT '客户级别1(规模)',
  `level_income` tinyint(3) NOT NULL DEFAULT '0' COMMENT '客户级别2(收入)',
  `level_scale_income` varchar(16) NOT NULL DEFAULT '' COMMENT '客户级别3(规模+收入)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `customer_name` (`name`) USING BTREE,
  KEY `customer_status` (`status`) USING BTREE,
  KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=972 DEFAULT CHARSET=utf8mb4 COMMENT='客户表'
````

- **account**
  - 账号信息表
  - 存储客户账号信息

```sql
CREATE TABLE `account` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `account_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '账户id',
  `cid` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '根据account_id生成(先md5，在截取前8位)',
  `ctag` char(32) DEFAULT '' COMMENT '根据account_id + "ctag" 生成(先md5，再截取前8位)',
  `user_agent_number` tinyint(2) DEFAULT '0' COMMENT '代调用户编号, 默认为0没有编号则不可代调，1朴道,2百行',
  `account_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账号名称',
  `customer_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '所属客户id',
  `group_id` char(32) CHARACTER SET utf8 NOT NULL COMMENT '主体id',
  `father_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '父级为0，子级别指向父级account_id',
  `email` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '登录邮箱',
  `password` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '登录密码',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号状态,1可用，0不可用',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '账号类型：1正式，0测试',
  `apikey` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'apikey',
  `appsecret` char(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'appsecret',
  `end_time` bigint(11) NOT NULL COMMENT '账号截止时间',
  `concurrency` int(10) NOT NULL DEFAULT '-1' COMMENT '账号秒并发',
  `access_ip` text CHARACTER SET utf8 COMMENT 'ip白名单',
  `mark` text CHARACTER SET utf8 COMMENT '备注',
  `admin` varchar(30) CHARACTER SET utf8 DEFAULT '' COMMENT '操作人',
  `appsecret_bak` char(100) NOT NULL DEFAULT '',
  `close_zhilian` tinyint(2) NOT NULL DEFAULT '0' COMMENT '直连关闭 0不关闭，1关闭',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `is_delete` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `cache_days` int(10) NOT NULL DEFAULT '1' COMMENT '缓存时长 天',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_name` (`account_name`) USING BTREE,
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `account_father_id` (`father_id`) USING BTREE,
  KEY `account_type` (`type`) USING BTREE,
  KEY `account_end_time` (`end_time`) USING BTREE,
  KEY `account_email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3760 DEFAULT CHARSET=utf8mb4 COMMENT='账号表'
```

#### 2. 产品配置相关表

- **product**

  - 产品信息表
  - 存储产品基础信息

```sql
CREATE TABLE `product` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品编号',
  `product_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '产品名称',
  `product_enname` varchar(100) NOT NULL DEFAULT '',
  `product_key` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '产品业务key',
  `father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父级产品ID',
  `password` varchar(255) DEFAULT '$2y$10$k1LpRx5OOh/ysWOj/XAlkOIxdqzBqcI89YX.xIsk7sK9d9lRgsQi6' COMMENT 'api password grant',
  `data` text CHARACTER SET utf8 COMMENT '配置参数[type：1单行文本框、2多行文本框、3单选框、4多选框、5时间控件，option表示单选多选框的枚举值]',
  `stat_config` longtext CHARACTER SET utf8 COMMENT '客户统计配置',
  `fee_config` text CHARACTER SET utf8 COMMENT '计费配置参数',
  `product_param` varchar(300) NOT NULL DEFAULT '',
  `back_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品后台展示状态（1展示，0不展示）',
  `search_show` tinyint(2) NOT NULL DEFAULT '0' COMMENT '主产品在统计等表单查询中是否展示（1展示，-1不展示）',
  `channel_stat` text COMMENT '渠道统计配置',
  `channel_fee` text COMMENT '渠道计费配置',
  `admin` varchar(30) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作人',
  `mark` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '备注',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '产品可用状态（0-禁用 1-可用）',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号（倒序）',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '产品类型（1-普通产品 2-父产品 3-子产品 4-金盾分流产品 5-虚拟产品）',
  `bill_config` text COMMENT '产品类型（1-普通产品 2-父产品 3-子产品 4-金盾分流产品 5-虚拟产品）',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=748 DEFAULT CHARSET=utf8mb4 COMMENT='产品表'
```

- **account_product**
  - 账号产品关联表
  - 记录账号开通的产品

```sql
CREATE TABLE `account_product` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `account_id` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户id',
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号-产品状态：1可用，2禁用',
  `use_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品使用类型:1外部调用,2内部调用',
  `contract_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '签约状态:1已签约已付款,2已签约未付费,3未签约,4其他,5特殊客户',
  `end_time` int(11) DEFAULT NULL,
  `daily_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '日限额用量',
  `month_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '月限额用量',
  `year_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '年限额量',
  `total_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '总限额量',
  `concurrency` int(10) NOT NULL DEFAULT '1' COMMENT '秒并发',
  `data` text CHARACTER SET utf8 COMMENT '产品配置参数',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `limit_start_date` date DEFAULT NULL COMMENT '总限量预警开始时间',
  `source_tags` varchar(6) DEFAULT '' COMMENT '切换渠道标记,仅标记子产品 填写已换签的渠道id 例如 ,1,2,99,100,',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ca_acc_pro_unique_id` (`account_id`,`product_id`) USING BTREE,
  KEY `ca_account_id` (`account_id`) USING BTREE,
  KEY `ca_product_id` (`product_id`) USING BTREE,
  KEY `ca_status` (`status`) USING BTREE,
  KEY `ca_cntract_status` (`contract_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16680 DEFAULT CHARSET=utf8mb4 COMMENT='账号-产品-关联表'
```

#### 3. 基础配置表

- **companytype**

  - 公司类型配置表
  - 存储企业类型分类信息

```sql
CREATE TABLE `companytype` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父级分类ID',
  `name` varchar(40) NOT NULL DEFAULT '' COMMENT '分类名称',
  `create_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 COMMENT='公司类型表'
```

### 表关系说明

1. **客户关系**

```

pre_sales_customer → customer
(售前客户 → 正式客户)

```

2. **产品关系**

```

pre_sales_customer ← pre_sales_customer_product_middle → product
(售前客户 ← 中间表 → 产品)

```

3. **测试结果关系**

```

pre_sales_pub_result ← pre_sales_result{产品 ID}
(公共结果 ← 具体产品结果)

```
