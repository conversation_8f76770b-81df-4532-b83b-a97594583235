## 用户管理模块业务架构梳理

### 一、模块定位与职责

用户管理模块是系统权限管理的核心基础模块，负责：

1. **用户账户管理** - 用户的基本信息维护
2. **权限控制** - 通过角色和数据权限控制用户访问
3. **组织架构** - 部门管理和用户归属
4. **会话管理** - 用户登录状态和安全控制

### 二、实际系统架构

```mermaid
graph TB
    subgraph 用户管理模块架构
        UC[UserController<br/>用户控制器]
        SUM[SystemUserModel<br/>用户模型]
        SURM[SystemUserRoleModel<br/>用户角色模型]
        DR[DeptRepository<br/>部门仓库]

        subgraph 数据库表
            ST[(system_user<br/>用户表)]
            SUR[(system_user_role<br/>用户角色表)]
            SR[(system_role<br/>角色表)]
            SD[(system_dept<br/>部门表)]
        end

        subgraph 权限控制
            AAC[AdminController<br/>基础权限控制]
            DAC[DataAuthController<br/>数据权限控制]
            SN[SystemNode<br/>节点权限]
        end

        UC --> SUM
        UC --> SURM
        UC --> DR
        SUM --> ST
        SURM --> SUR
        SURM --> SR
        DR --> SD

        AAC --> SUM
        AAC --> SURM
        DAC --> SUM
        SN --> SURM
    end
```

### 三、核心数据结构

```sql
-- system_user表(用户表)
- id: 主键
- username: 用户名(唯一)
- password: 密码(MD5加密)
- realname: 真实姓名
- email: 电子邮箱
- phone: 手机号码
- dept_id: 所属部门ID
- profession: 职位
- data_auth: 数据权限(逗号分隔)
- source_auth: 征信机构权限
- balance_auth/surplus_auth/expire_auth/profit_auth: 邮件权限
- disabled: 状态(1启用/2禁用)
- lastloginip: 最后登录IP
- lastlogintime: 最后登录时间

-- system_user_role表(用户角色关联表)
- username: 用户名
- roleid: 角色ID
- is_default: 是否默认角色(1是/2否)
```

### 四、核心业务流程

```mermaid
graph TD
    subgraph 用户管理流程
        A[用户列表页面] --> B{操作类型}
        B -->|编辑用户| C[用户设置]
        B -->|角色管理| D[角色设置]

        C --> E[获取用户信息]
        E --> F[加载部门/职位数据]
        F --> G[表单编辑]
        G --> H[数据验证]
        H --> I{验证通过?}
        I -->|否| J[返回错误信息]
        I -->|是| K[保存用户信息]

        D --> L[获取用户角色]
        L --> M[加载所有角色列表]
        M --> N[角色分配界面]
        N --> O[角色选择与默认角色设置]
        O --> P[保存角色关系]
    end
```

### 五、核心功能特性

#### 1. 基础 CRUD 功能

```php
- index(): 用户列表查询(支持用户名/邮箱搜索)
- set(): 用户信息编辑(GET显示表单/POST保存数据)
- role(): 用户角色管理(角色分配和默认角色设置)
```

#### 2. 数据验证机制

```php
- 手机号格式验证(/^0?1[34578]\d{9}$/)
- 部门必选验证
- 数据权限必选验证
- 用户名唯一性约束(数据库级别)
```

#### 3. 权限体系设计

```php
- 数据权限级别: 0全部权限/1个人权限/2本部门权限/3下级部门权限
- 角色权限: 通过用户角色关联表实现多角色支持
- 默认角色: 支持设置用户的默认角色
- 邮件权限: 细粒度的邮件发送权限控制
```

#### 4. 安全机制

```php
- Session管理: 登录状态保存到数据库
- 密码加密: MD5加密存储
- IP记录: 记录最后登录IP和时间
- 状态控制: 支持启用/禁用用户
```

### 六、系统集成特点

#### 1. 部门管理集成

- 通过 DeptRepository 实现部门数据获取
- 支持无限层级的部门结构
- 部门与用户的归属关系管理

#### 2. 权限系统集成

- 与 DataAuthController 集成实现数据权限控制
- 通过 AdminController 实现基础权限验证
- 与 SystemNode 节点权限系统关联

#### 3. 业务模块集成

- 为各业务模块提供用户数据支撑
- 支持用户选择组件(getUserOption 方法)
- 与客户管理、账号管理等模块深度集成

### 七、架构特点分析

#### 1. 简化的分层设计

- 采用 Controller -> Model 的两层架构
- 部分功能通过 Repository 层增强(如 DeptRepository)
- 直接使用 ThinkPHP 的 Model 层进行数据操作

#### 2. 会话管理机制

- 自定义 Session 存储(存储到数据库)
- 支持审核流程的用户模拟登录
- Session 变量名可配置

#### 3. 灵活的权限设计

- 支持多角色分配
- 数据权限与功能权限分离
- 细粒度的邮件权限控制

#### 4. 安全性考虑

- 密码加盐处理(USER_PWD_SALT 配置)
- IP 访问记录
- 用户状态控制
- 数据验证完整

### 八、扩展性设计

#### 1. 权限扩展

- 支持新增权限类型(如 source_auth 征信权限)
- 数据权限可灵活配置
- 角色系统可独立扩展

#### 2. 组织架构扩展

- 部门支持无限层级
- 用户归属关系清晰
- 便于组织架构调整

#### 3. 业务集成扩展

- 提供标准的用户数据接口
- 支持各种业务场景的权限控制
- 便于新模块集成用户体系
