## 部门管理模块业务架构梳理

### 一、模块定位与职责

部门管理模块是企业组织架构管理的核心模块，主要负责：

1. **组织架构管理** - 支持无限层级的部门结构
2. **部门与用户关联** - 管理用户与部门的归属关系
3. **权限体系支撑** - 为数据权限控制提供组织基础
4. **业务流程集成** - 与其他业务模块的部门数据集成

### 二、实际系统架构

```mermaid
graph TB
    subgraph 部门管理模块架构
        DC[DeptController<br/>部门控制器]
        DR[DeptRepository<br/>部门仓库]

        subgraph 模型层
            SDM[SystemDeptModel<br/>部门模型]
            SDGM[SystemDeptGradeModel<br/>部门等级模型]
            SUM[SystemUserModel<br/>用户模型]
        end

        subgraph 数据库表
            SDT[(system_dept<br/>部门表)]
            SDGT[(system_dept_grade<br/>部门等级关系表)]
            SUT[(system_user<br/>用户表)]
        end

        subgraph 业务集成
            DAC[DataAuthController<br/>数据权限控制]
            DSR[DeptSalesmanRepository<br/>部门商务管理]
        end

        DC --> DR
        DR --> SDM
        DR --> SDGM
        DR --> SUM

        SDM --> SDT
        SDGM --> SDGT
        SUM --> SUT

        DAC --> SDGM
        DSR --> SDGM
        DSR --> SDM
    end
```

### 三、核心数据结构

```sql
-- system_dept表(部门表)
- dept_id: 部门ID(主键,格式:DEPT+时间戳+随机数)
- dept_name: 部门名称
- status: 状态(1启用/0禁用)
- create_time: 创建时间
- delete_time: 删除时间(软删除)

-- system_dept_grade表(部门等级关系表)
- grade_dept_id: 部门ID
- grade_father_id: 直接父级部门ID
- grade_grand_father_id: 祖父级部门ID
(支持多层级关系存储)

-- system_user表(用户表扩展字段)
- dept_id: 所属部门ID
- is_leader: 是否部门负责人(1是/0否)
```

### 四、核心业务流程

```mermaid
graph TD
    subgraph 部门管理流程
        A[部门管理首页] --> B{操作类型}
        B -->|新增部门| C[新增部门流程]
        B -->|编辑部门| D[编辑部门流程]
        B -->|删除部门| E[删除部门流程]

        C --> F[生成部门ID]
        F --> G[验证部门信息]
        G --> H[创建部门记录]
        H --> I[建立等级关系]
        I --> J[分配部门成员]

        D --> K[获取部门信息]
        K --> L[状态变更检查]
        L --> M[级联状态处理]
        M --> N[更新部门信息]
        N --> O[重新分配成员]

        E --> P[获取子部门列表]
        P --> Q[清理用户关联]
        Q --> R[软删除部门]
        R --> S[删除等级关系]
    end
```

### 五、核心功能特性

#### 1. 基础 CRUD 功能

```php
- index(): 部门列表显示(树形结构)
- add(): 新增部门(GET显示表单/POST保存数据)
- edit(): 编辑部门(GET显示编辑表单/POST更新数据)
- del(): 删除部门(级联删除子部门)
```

#### 2. 无限层级支持

```php
- 通过system_dept_grade表实现多层级关系存储
- 支持父子部门、祖父部门的快速查询
- 递归算法实现树形结构生成
- 前端展示支持展开/收缩效果
```

#### 3. 用户与部门关联

```php
- 支持部门成员和负责人的分别管理
- 用户可以分配到部门并设置是否为负责人
- 部门状态变更时自动处理用户归属
- 提供未分配部门的用户列表
```

#### 4. 级联操作机制

```php
- 部门禁用时自动禁用所有子部门
- 部门删除时自动删除所有子部门
- 状态变更时自动清理或重新分配用户
- 维护部门等级关系的完整性
```

### 六、特色设计亮点

#### 1. 智能 ID 生成策略

```php
protected function getDeptId()
{
    return 'DEPT' . date('YmdHis') . rand(0,9) . rand(0, 9);
}
```

- 使用时间戳+随机数确保 ID 唯一性
- 固定前缀便于识别和管理

#### 2. 复杂等级关系管理

```php
// 支持祖父级、父级、子级的多维度关系存储
protected function addDeptGrade($dept_id, $father_id)
{
    // 根据父部门获取所有上级关系
    // 为新部门建立完整的等级关系链
}
```

#### 3. 递归树形结构处理

```php
protected function getEndlessDeptData($data, $father_id = '')
{
    // 递归构建无限层级的部门树
    // 支持任意深度的组织架构
}
```

#### 4. 智能状态级联处理

```php
protected function listenEditStatus($dept_id, $data)
{
    // 监听部门状态变更
    // 自动处理子部门和用户的状态同步
}
```

### 七、系统集成特点

#### 1. 数据权限集成

- 与`DataAuthController`深度集成
- 支持基于部门的数据访问控制
- 实现"个人权限/本部门权限/下级部门权限"的分级管理

#### 2. 商务部门特殊处理

- 通过`DeptSalesmanRepository`实现商务部门的特殊管理
- 支持商务人员的层级关系和权限控制
- 为业务模块提供商务数据支撑

#### 3. 业务模块数据支撑

- 为客户管理、账号管理等提供部门信息
- 支持 Excel 导出时的部门信息关联
- 提供统一的部门选择组件

### 九、扩展性设计

#### 1. 层级扩展性

- 理论上支持无限层级的部门结构
- 等级关系表设计支持快速查询任意层级
- 便于复杂组织架构的管理

#### 2. 功能扩展性

- Repository 模式便于新增业务功能
- 清晰的接口设计便于其他模块集成
- 支持个性化的部门管理需求

#### 3. 性能扩展性

- 递归算法优化减少数据库查询
- 等级关系表设计优化查询性能
- 支持大规模组织架构的管理
