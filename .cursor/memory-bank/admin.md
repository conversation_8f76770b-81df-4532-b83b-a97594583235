## 系统权限管理架构分析

### 1. 权限管理核心架构

根据代码分析，系统采用了**两层权限控制架构**：

#### 1.1 功能权限层（RBAC）

- **用户-角色-节点** 三级权限模型
- **核心组件**：
  - `SystemUser` - 用户管理
  - `SystemRole` - 角色管理
  - `SystemNode` - 功能节点管理
  - `SystemUserRole` - 用户角色关联
  - `SystemRoleNode` - 角色节点权限关联

#### 1.2 数据权限层

- **基于部门组织架构的数据范围控制**
- **四级数据权限**：
  - `0` - 全部权限
  - `1` - 个人权限
  - `2` - 本部门权限
  - `3` - 下级部门权限

### 2. 权限管理核心流程

#### 2.1 用户权限验证流程

```
用户登录 → 获取用户角色 → 获取角色权限节点 → 检查访问权限 → 应用数据权限过滤
```

从 `AdminController.class.php` 中的初始化流程可以看出：

```73:75:Application/Common/Controller/AdminController.class.php
//检查登录
$this->checkLogin();
//获取用户角色
$this->getUserRole();
//检查权限
$this->checkPer();
```

#### 2.2 角色权限管理流程

- **角色创建**：`SystemRoleModel` 负责角色的增删改查
- **权限分配**：通过 `SystemRoleNodeModel` 管理角色与功能节点的关联
- **用户授权**：通过 `SystemUserRoleModel` 管理用户与角色的关联

### 3. 数据权限控制机制

#### 3.1 数据权限控制器

`DataAuthController` 是核心的数据权限控制组件，采用**单例模式**：

```49:50:Application/Common/Controller/DataAuthController.class.php
// 0--全部权限, 1--个人权限, 2--本部门权限, 3--下级部门权限
$this->data_auth = explode(',', $user_data['data_auth']);
```

#### 3.2 数据权限应用范围

根据代码分析，数据权限控制覆盖了以下业务模块：

- 客户管理（Customer）
- 账号管理（Account）
- 售前测试客户（PreSalesCustomer）
- 打款单管理（Remit）
- 合同管理（Bargain）

### 4. 部门管理架构

#### 4.1 部门组织结构

- **无限级部门层次**：支持多级部门嵌套
- **部门等级关系**：通过 `SystemDeptGradeModel` 管理部门层级关系
- **部门用户角色**：区分普通成员和部门管理员

#### 4.2 部门权限继承

```127:131:Application/Common/Controller/DataAuthController.class.php
if (in_array(3, $this->data_auth)) {
    //获取当前部门的下级部门
    $children_dept_id = $this->getChildrenDeptId($this->dept_id);
    // 下级部门权限处理逻辑
}
```

### 5. 业务权限扩展

#### 5.1 邮件权限控制

系统还包含细粒度的邮件权限控制：

- `balance_auth` - 余额预警邮件权限
- `surplus_auth` - 余量预警邮件权限
- `expire_auth` - 账号到期预警邮件权限
- `profit_auth` - 营收邮件权限

#### 5.2 征信机构权限

```55:56:Application/System/Controller/UserController.class.php
// 征信机构权限
$this->assign('source_auth',$lists['source_auth']);
```

### 6. 前端权限控制

#### 6.1 界面元素控制

前端通过用户权限信息动态控制界面元素：

```452:462:Application/Stat/View/Product/customerCost.html
//判断是销售人员且是领导 可查看本区域销售
if(user_auth.is_sale === true && user_auth.is_leader === 1 && user_auth.data_auth !== 0){
    self.disableDept = true;
    self.disableUser = false;
}
//判断是销售人员且不是领导 只能查看自己
if(user_auth.is_sale === true && user_auth.is_leader === 0 && user_auth.data_auth !== 0){
    self.disableDept = true;
    self.disableUser = true;
}
```

### 7. 权限管理特色功能

#### 7.1 内置角色保护

系统对核心角色提供保护机制：

```29:32:Application/Common/Model/SystemRoleModel.class.php
//游客和超级管理员 不允许 被禁用
if(in_array($id,array(1,2)) && $this->disabled != 1){
    throw new \Exception('该角色 不允许 被禁用');
}
```

#### 7.2 权限缓存机制

```88:98:Application/Common/Model/SystemNodeModel.class.php
$cachename = $this->role_node_cache_prefix.'_'.$roleid;
if(APP_DEBUG === true || !S($cachename)){
    //超级管理员 默认取得全部权限
    if($roleid == 1){
        $rolenode = $this->getField('id',true);
    } else {
        $rolenode = D('SystemRoleNode')->getRoleNode($roleid);
    }
    S($cachename,$rolenode);
}
```

### 8. 权限管理最佳实践

#### 8.1 权限验证中间件

系统通过 `AdminController` 基类统一处理权限验证，确保所有需要权限的控制器都经过统一的权限检查流程。

#### 8.2 数据权限透明化

通过 `DataAuthController` 单例模式，在各个业务模块中透明地应用数据权限过滤，避免权限逻辑散落在各个业务代码中。

#### 8.3 审核机制集成

系统集成了审核机制，在权限验证中包含了审核流程：

```56:57:Application/Common/Controller/AdminController.class.php
//校验操作是否需要审核
(new Approval())->checkAddApprove($this->loginuser['username']);
```

### 总结

这是一个相对完整的企业级权限管理系统，采用了**功能权限+数据权限**的双重控制机制，支持复杂的组织架构和业务场景。系统在架构设计上体现了以下特点：

1. **分层清晰**：功能权限与数据权限分离
2. **扩展性强**：支持无限级部门和灵活的权限配置
3. **安全性高**：多重权限验证和核心角色保护
4. **性能优化**：权限缓存机制
5. **业务集成**：与具体业务模块深度集成
