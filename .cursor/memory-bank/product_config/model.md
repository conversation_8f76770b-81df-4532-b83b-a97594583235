## 核心数据结构

```sql
CREATE TABLE `product` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品编号',
  `product_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '产品名称',
  `product_enname` varchar(100) NOT NULL DEFAULT '',
  `product_key` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '产品业务key',
  `father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父级产品ID',
  `password` varchar(255) DEFAULT '$2y$10$k1LpRx5OOh/ysWOj/XAlkOIxdqzBqcI89YX.xIsk7sK9d9lRgsQi6' COMMENT 'api password grant',
  `data` text CHARACTER SET utf8 COMMENT '配置参数[type：1单行文本框、2多行文本框、3单选框、4多选框、5时间控件，option表示单选多选框的枚举值]',
  `stat_config` longtext CHARACTER SET utf8 COMMENT '客户统计配置',
  `fee_config` text CHARACTER SET utf8 COMMENT '计费配置参数',
  `product_param` varchar(300) NOT NULL DEFAULT '',
  `back_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品后台展示状态（1展示，0不展示）',
  `search_show` tinyint(2) NOT NULL DEFAULT '0' COMMENT '主产品在统计等表单查询中是否展示（1展示，-1不展示）',
  `channel_stat` text COMMENT '渠道统计配置',
  `channel_fee` text COMMENT '渠道计费配置',
  `admin` varchar(30) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作人',
  `mark` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '备注',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '产品可用状态（0-禁用 1-可用）',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号（倒序）',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '产品类型（1-普通产品 2-父产品 3-子产品 4-金盾分流产品 5-虚拟产品）',
  `bill_config` text COMMENT '产品类型（1-普通产品 2-父产品 3-子产品 4-金盾分流产品 5-虚拟产品）',
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=748 DEFAULT CHARSET=utf8mb4 COMMENT='产品表'
```
