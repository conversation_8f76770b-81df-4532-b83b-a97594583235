## 核心业务流程

```mermaid
graph TD
    subgraph 产品配置管理流程
        A[产品列表] --> B{操作类型}
        B -->|新增| C[新增产品]
        B -->|编辑| D[编辑产品]
        B -->|查看| E[查看详情]

        C --> F[填写基础信息]
        F --> G[配置JSON参数]
        G --> H[数据验证]
        H --> I{验证通过?}
        I -->|否| J[返回错误信息]
        I -->|是| K[保存产品]
        K --> L[清理鉴权缓存]

        D --> M[获取产品信息]
        M --> N[修改配置]
        N --> O[数据验证]
        O --> P{验证通过?}
        P -->|否| Q[返回错误信息]
        P -->|是| R[更新产品]
        R --> S[清理鉴权缓存]
    end
```
