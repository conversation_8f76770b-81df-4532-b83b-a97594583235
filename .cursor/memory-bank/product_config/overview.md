## 产品配置模块业务架构梳理

### 一、模块定位与职责

产品配置模块是系统的核心基础模块，主要负责：

1. **产品基础信息管理** - 产品的创建、编辑、查询
2. **产品配置管理** - 产品的功能配置、统计配置、计费配置
3. **产品层级管理** - 父子产品关系管理
4. **产品状态管理** - 产品的上下架、搜索展示控制

### 四、系统架构设计

```mermaid
graph TB
    subgraph 产品配置模块架构
        Controller[ProductController]
        Repository[ProductRepository]
        Model[ProductModel]
        DB[(product表)]

        subgraph 业务层功能
            Validate[数据验证]
            Cache[缓存清理]
            Config[配置管理]
        end

        subgraph 外部集成
            Auth[鉴权系统]
            Account[账号模块]
            Fee[计费模块]
        end

        Controller --> Repository
        Repository --> Model
        Repository --> Validate
        Repository --> Cache
        Repository --> Config
        Model --> DB

        Cache --> Auth
        Repository --> Account
        Repository --> Fee
    end
```

### 五、核心功能特性

1. **数据验证机制**

```php
- 产品编号唯一性校验
- 产品名称唯一性校验
- JSON配置格式验证
- 必填字段验证
```

2. **配置管理功能**

```php
- 产品基础配置(data字段)
- 统计配置(stat_config字段)
- 计费配置(fee_config字段)
- 渠道配置(channel_stat/channel_fee字段)
```

3. **产品层级管理**

```php
- 支持父子产品关系
- 子产品数量统计
- 层级查询支持
```

4. **状态控制机制**

```php
- back_status: 后台显示控制
- search_show: 搜索展示控制
- 禁用状态常量定义
```
