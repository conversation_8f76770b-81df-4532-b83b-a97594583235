## 客户管理模块业务架构梳理

### 一、模块定位与职责

客户管理模块是系统的核心业务模块，负责：

1. **客户生命周期管理** - 客户的创建、编辑、删除和状态管理
2. **多主体签约管理** - 支持多个公司主体的客户签约关系
3. **计费配置管理** - 管理客户的产品计费配置
4. **数据权限控制** - 基于角色和部门的客户数据访问权限
5. **业务流程集成** - 与账号、产品、统计等模块深度集成

### 二、实际系统架构

```mermaid
graph TB
    subgraph 客户管理模块架构
        CC[CustomerController<br/>客户控制器]

        subgraph 模型层
            CM[CustomerModel<br/>客户模型]
            CEM[CustomerExpendModel<br/>客户消费模型]
            CGM[CustomerGroupModel<br/>客户主体模型]
            CSHM[CustomerSalesmanHistoryModel<br/>商务历史模型]
        end

        subgraph 仓库层
            CFR[CustomerFeeRepository<br/>客户计费仓库]
            CTR[CompanyTypeRepository<br/>公司类型仓库]
            PCR[PreSalesCustomerRepository<br/>售前客户仓库]
        end

        subgraph 数据库表
            CT[(customer<br/>客户表)]
            CET[(customer_expend<br/>客户消费表)]
            CGT[(customer_group<br/>客户主体表)]
            CSHT[(customer_salesman_history<br/>商务历史表)]
            CTT[(company_type<br/>公司类型表)]
        end

        subgraph 权限控制
            DAC[DataAuthController<br/>数据权限控制]
            SUM[SystemUserModel<br/>系统用户]
            SDM[SystemDeptModel<br/>系统部门]
        end

        CC --> CM
        CC --> CFR
        CC --> CTR
        CC --> PCR

        CM --> CT
        CM --> CEM
        CEM --> CET
        CGM --> CGT
        CSHM --> CSHT
        CTR --> CTT

        CFR --> CM

        DAC --> SUM
        DAC --> SDM
        CC --> DAC
    end
```

### 三、核心数据结构

```sql
-- customer表(客户表)
- customer_id: 客户编号(C+日期+随机码)
- name: 客户名称
- company: 公司名称(与羽乐科技签约)
- agent_company: 代理公司信息(JSON格式,多主体签约)
- type: 公司类型(二级分类)
- customer_type: 客户类型(1金融/2企服)
- payment_type: 付款方式(1预付/2后付)
- status: 状态(1启用/0禁用)
- operator: 运营跟进人
- salesman: 商务跟进人
- introduce_salesman: 商务推荐人
- channel_follower: 渠道跟进人
- channel_mode: 是否渠道客户
- source_id: 可见征信机构
- sign_type: 签约分类(逗号分隔多主体)
- group_id: 所属主体ID
- reconciliation_cycle: 对账周期
- level_scale/level_income: 客户级别
- is_delete: 软删除标记

-- customer_expend表(客户特殊消费表)
- customer_id: 客户ID
- product_id: 产品ID
- start_date: 计费开始日期
- money: 费用金额
- fee_number: 费用数量
- type: 费用类型
- name: 费用名称
- remark: 备注
```

### 五、核心功能特性

#### 1. 基础 CRUD 功能

```php
- index(): 客户列表查询(支持多维度筛选和分页)
- add(): 新增客户(GET显示表单/POST创建客户)
- edit(): 编辑客户(GET显示编辑表单/POST更新客户)
- delete(): 删除客户(软删除,级联处理关联数据)
- export(): 客户数据导出(Excel格式)
```

#### 2. 多主体签约管理

```php
- 支持多个公司主体的签约关系管理
- agent_company字段存储JSON格式的多主体公司信息
- sign_type字段支持多个签约分类(羽乐/朴道/浙数交/郑数交等)
- source_id字段控制渠道跟进人可见的征信机构
```

#### 3. 智能 ID 生成策略

```php
public function createUUid($type, $random_len = 6)
{
    // 客户ID: C + 日期(Ymd) + 6位随机码(A-Z+0-9)
    // 格式示例: C20240101A1B2C3
}
```

#### 4. 数据权限控制

```php
- 通过DataAuthController实现基于用户和部门的数据权限
- getCustomerWhere(): 获取用户可访问的客户范围
- validAllowDoCustomer(): 验证用户是否有操作特定客户的权限
```

#### 5. 特殊消费管理

```php
- 支持客户特殊费用的录入和管理
- 按产品、时间维度记录特殊消费
- 支持费用的增删改操作
```

### 六、业务集成特点

#### 1. 售前业务集成

- 与 PreSalesCustomerRepository 集成
- 支持售前测试客户转正式客户
- 关联售前客户的测试数据

#### 2. 计费系统集成

- 通过 CustomerFeeRepository 管理客户计费配置
- 支持复杂的计费规则配置
- 与 FeeConfigRepository 深度集成

#### 3. 公司类型管理

- 通过 CompanyTypeRepository 实现二级联动
- 支持一级、二级公司类型的动态选择
- listenCutFirstType()方法实现前端联动

#### 4. 商务跟进历史

- CustomerSalesmanHistoryModel 记录商务人员变更历史
- 支持按月份查询商务归属关系
- 为统计分析提供数据支撑
